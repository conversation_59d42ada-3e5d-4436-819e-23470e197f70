using System;

namespace Redarbor.Repo.Trackings.Library.Entities
{
    public class CvVisualizationControlEntity
    {
        public int IdCompany { get; set; }
        public int IdCandidate { get; set; }
        public short IdPortal { get; set; }
        public short IdType { get; set; }
        public int IdCv { get; set; }
        public int DateInt { get; set; }
        public short StatusId { get; set; }
        public int IdOffer { get; set; }
        public short IsCompanyVIsible { get; set; }
        public DateTime CreationDate { get; set; } = new DateTime();
        public string IP { get; set; } = string.Empty;
        public short IdApp { get; set; }
        public int IdCompanyUser { get; set; }
    }
}
