using MySql.Data.MySqlClient;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Core.Resolver.Contracts.ServiceLibrary;
using Redarbor.Core.Resolver.Contracts.ServiceLibrary.Enums;
using Redarbor.Extensions.Library.Extensions;
using Redarbor.Master.Entities.Company;
using Redarbor.Master.Entities.Match;
using Redarbor.Repo.Offer.Library.Configuration;
using Redarbor.TrackingActions.Library.DomainServiceContracts;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;

namespace Redarbor.TrackingActions.Library.DomainServicesImplementations
{
    public class TrackingActionsRecoverAndPersist : ITrackingActionsRecoverAndPersist
    {
        private readonly IConnectionStringResolverService _connectionStringResolverService;
        private readonly IExceptionPublisherService _exceptionPublisherService;

        private readonly IRepoOfferConfiguration _repoOfferConfiguration;

        public TrackingActionsRecoverAndPersist(
            IRepoOfferConfiguration repoOfferConfiguration,
            IConnectionStringResolverService connectionStringResolverService,
            IExceptionPublisherService exceptionPublisherService)
        {
            _connectionStringResolverService = connectionStringResolverService;
            _exceptionPublisherService = exceptionPublisherService;
            //TODO eliminar cuando se mueva a: TrackingRecoverAndPersist : ITrackingRecoverAndPersist
            _repoOfferConfiguration = repoOfferConfiguration;
        }

        public void AddActionTracking(TrackingActionEntity trackingActions)
        {
            try
            {
                using (var conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, trackingActions.IdPortal)))
                {
                    using (var cmd = new MySqlCommand("actionTrackingCompanys", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("_type_feature", trackingActions.Type);
                        cmd.Parameters.AddWithValue("_idobject", trackingActions.IdObject);
                        cmd.Parameters.AddWithValue("_idportal", trackingActions.IdPortal);
                        cmd.Parameters.AddWithValue("_idstatus", trackingActions.IdStatus);
                        cmd.Parameters.AddWithValue("_idcompany", trackingActions.IdCompany);
                        cmd.Parameters.AddWithValue("_iduser", trackingActions.IdUser);
                        cmd.Parameters.AddWithValue("_user_name", trackingActions.UserName);

                        conn.Open();

                        cmd.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"TrackingActionsRecoverAndPersist - AddActionTracking ex: {ex.ToString()}");
                _exceptionPublisherService.Publish(ex, "TrackingActionsRecoverAndPersist", "AddActionTracking", false, null, trackingActions.IdPortal);
            }
        }

        public int GetTotalTrackingActions(TrackingActionFilterEntity trackingActionsFilters)
        {
            int result = 0;

            try
            {
                using (var conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
                {
                    using (var cmd = new MySqlCommand("get_total_tracking_actions_V2", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("_idcompany", trackingActionsFilters.CompanyId);
                        cmd.Parameters.AddWithValue("_idportal", trackingActionsFilters.PortalId);
                        cmd.Parameters.AddWithValue("_iduser", trackingActionsFilters.UserId);
                        cmd.Parameters.AddWithValue("_action", trackingActionsFilters.Action);
                        cmd.Parameters.AddWithValue("_status", trackingActionsFilters.AvoidStatusId);

                        conn.Open();

                        var resultExecute = cmd.ExecuteScalar();

                        if (resultExecute != null)
                            int.TryParse(resultExecute.ToString(), out result);

                    }
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"TrackingActionsRecoverAndPersist - GetTotalTrackingActions ex: {ex.ToString()}");
                _exceptionPublisherService.Publish(ex, "TrackingActionsRecoverAndPersist", "GetTotalTrackingActions");
            }


            return result;
        }

        public List<TrackingActionEntity> GetTrackingActionsPending(TrackingActionFilterEntity trackingActionsFilters)
        {
            var result = new List<TrackingActionEntity>();

            try
            {
                using (var conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
                {
                    using (var cmd = new MySqlCommand("get_tracking_actions_pending_v3", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("_idcompany", trackingActionsFilters.CompanyId);
                        cmd.Parameters.AddWithValue("_idportal", trackingActionsFilters.PortalId);
                        cmd.Parameters.AddWithValue("_iduser", trackingActionsFilters.UserId);
                        cmd.Parameters.AddWithValue("_rowIndex", trackingActionsFilters.RowsByPage * (trackingActionsFilters.Page == 0 ? 0 : trackingActionsFilters.Page - 1));
                        cmd.Parameters.AddWithValue("_numberRows", trackingActionsFilters.RowsByPage);
                        cmd.Parameters.AddWithValue("_filterIds", !string.IsNullOrEmpty(trackingActionsFilters.DynamicFilter) ? trackingActionsFilters.DynamicFilter : string.Empty);

                        conn.Open();
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                result.Add(new TrackingActionEntity()
                                {
                                    DateAdd = !reader.IsDBNull(reader.GetOrdinal("date_add")) ? reader.GetDateTime("date_add") : DateTime.MinValue,
                                    DateUpdate = !reader.IsDBNull(reader.GetOrdinal("date_updated")) ? reader.GetDateTime("date_updated") : DateTime.MinValue,
                                    Type = reader.GetInt16("type_feature"),
                                    Id = reader.GetInt32("id"),
                                    IdCompany = reader.GetInt32("idcompany"),
                                    IdObject = reader.GetInt32("idobject"),
                                    IdUser = reader.GetInt32("iduser"),
                                    IdPortal = reader.GetInt16("idportal"),
                                    IdStatus = reader.GetInt16("idstatus"),
                                    UserName = !reader.IsDBNull(reader.GetOrdinal("user_name")) ? reader.GetString("user_name") : string.Empty
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"TrackingActionsRecoverAndPersist - GetTrackingActionsPending ex: {ex.ToString()}");
                _exceptionPublisherService.Publish(ex, "TrackingActionsRecoverAndPersist", "GetTrackingActionsPending");
            }

            return result;
        }

        public List<TrackingActionEntity> GetTrackingActions(TrackingActionFilterEntity filters)
        {
            var result = new List<TrackingActionEntity>();

            try
            {
                using (var conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
                {
                    using (var cmd = new MySqlCommand("get_tracking_actions_v4", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("_idcompany", filters.CompanyId);
                        cmd.Parameters.AddWithValue("_idportal", filters.PortalId);
                        cmd.Parameters.AddWithValue("_iduser", filters.UserId);
                        cmd.Parameters.AddWithValue("_rowIndex", filters.RowsByPage * (filters.Page == 0 ? 0 : filters.Page - 1));
                        cmd.Parameters.AddWithValue("_numberRows", filters.RowsByPage);
                        cmd.Parameters.AddWithValue("_action", filters.Action);
                        cmd.Parameters.AddWithValue("_status", filters.AvoidStatusId);

                        conn.Open();
                        using (var reader = cmd.ExecuteReader())
                        {

                            if (filters.RowsByPage != 0)
                            {
                                while (reader.Read())
                                {
                                    result.Add(new TrackingActionEntity()
                                    {
                                        DateAdd = !reader.IsDBNull(reader.GetOrdinal("date_add")) ? reader.GetDateTime("date_add") : DateTime.MinValue,
                                        DateUpdate = !reader.IsDBNull(reader.GetOrdinal("date_updated")) ? reader.GetDateTime("date_updated") : DateTime.MinValue,
                                        Type = reader.GetInt16("type_feature"),
                                        Id = reader.GetInt32("id"),
                                        IdCompany = reader.GetInt32("idcompany"),
                                        IdObject = reader.GetInt32("idobject"),
                                        IdUser = reader.GetInt32("iduser"),
                                        IdPortal = reader.GetInt16("idportal"),
                                        IdStatus = reader.GetInt16("idstatus"),
                                        UserName = !reader.IsDBNull(reader.GetOrdinal("user_name")) ? reader.GetString("user_name") : string.Empty
                                    });
                                }
                            }
                            else
                            {
                                while (reader.Read())
                                {
                                    result.Add(new TrackingActionEntity()
                                    {
                                        Type = reader.GetInt16("type_feature")
                                    });
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"TrackingActionsRecoverAndPersist - GetTrackingActions ex: {ex.ToString()}");
                _exceptionPublisherService.Publish(ex, "TrackingActionsRecoverAndPersist", "GetTrackingActions");
            }

            return result;
        }      

        public Dictionary<int, MatchDownloaderEntity> GetCvDownloadByPks(string ids, short idPortal)
        {
            var result = new Dictionary<int, MatchDownloaderEntity>();

            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
            {
                using (MySqlCommand command = new MySqlCommand("get_cvdownloader_by_pks", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.AddWithValue("_ids", ids);
                    command.Parameters.AddWithValue("_idportal", idPortal);

                    try
                    {
                        connection.Open();

                        using (var reader = command.ExecuteReader())
                        {

                            while (reader != null && reader.Read())
                            {
                                if (!result.ContainsKey(reader.GetInt32("id")))
                                {
                                    result.Add(reader.GetInt32("id"), new MatchDownloaderEntity()
                                    {
                                        Id = reader.GetInt32("id"),
                                        CountCvs = reader.GetInt32("count_cvs"),
                                        DateAdd = !reader.IsDBNull(reader.GetOrdinal("date_add")) ? reader.GetDateTime("date_add") : DateTime.MinValue,
                                        DateDeleted = !reader.IsDBNull(reader.GetOrdinal("date_deleted")) ? reader.GetDateTime("date_deleted") : DateTime.MinValue,
                                        DownloadAllCvs = reader.GetInt16("download_all_cvs"),
                                        FileName = !reader.IsDBNull(reader.GetOrdinal("file_name")) ? reader.GetString("file_name") : string.Empty,
                                        Filter = !reader.IsDBNull(reader.GetOrdinal("filter")) ? reader.GetString("filter") : string.Empty,
                                        IdCompany = reader.GetInt32("id_company"),
                                        IdExtension = reader.GetInt16("id_extension"),
                                        IdLocation = reader.GetInt32("id_location"),
                                        IdOffer = reader.GetInt32("id_offer"),
                                        IdPortal = reader.GetInt16("id_portal"),
                                        IdStatus = reader.GetInt16("id_status"),
                                        IdUser = reader.GetInt32("user_id"),
                                        OfferDate = !reader.IsDBNull(reader.GetOrdinal("offer_date")) ? reader.GetDateTime("offer_date") : DateTime.MinValue,
                                        OfferTitle = !reader.IsDBNull(reader.GetOrdinal("offer_title")) ? reader.GetString("offer_title") : string.Empty,
                                        Url = !reader.IsDBNull(reader.GetOrdinal("url")) ? reader.GetString("url") : string.Empty
                                    });
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Trace.TraceError($"TrackingActionsRecoverAndPersist - GetCvDownloadByPks ex: {ex.ToString()}");
                        _exceptionPublisherService.Publish(ex, "TrackingActionsRecoverAndPersist", "GetCvDownloadByPks");
                    }
                }
            }

            return result;
        }

        public Dictionary<int, string> GetFileNameByIdsFile(string ids, short idPortal)
        {
            var result = new Dictionary<int, string>();

            try
            {
                using (var conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
                {
                    using (var cmd = new MySqlCommand("get_filename_by_ids", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("_ids", ids);
                        cmd.Parameters.AddWithValue("_idportal", idPortal);

                        conn.Open();
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var id = reader.GetInt32("id");

                                if (!result.ContainsKey(id))
                                    result.Add(id, reader.GetAsString("filename"));
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"TrackingActionsRecoverAndPersist - GetFileNameByIdsFile ex: {ex.ToString()}");
                _exceptionPublisherService.Publish(ex, "TrackingActionsRecoverAndPersist", "GetFileNameByIdsFile");
            }

            return result;
        }

        public Dictionary<int, string> GetTitleByOffers(string idoffers, int portalId)
        {
            var result = new Dictionary<int, string>();

            try
            {
                using (var conn = new MySqlConnection(_repoOfferConfiguration.RepoOfferConnectionString))
                {
                    using (var cmd = new MySqlCommand("get_titles_by_array_offers_ct", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("_ids", idoffers);
                        cmd.Parameters.AddWithValue("_idportal", portalId);

                        conn.Open();
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var idOfferCt = reader.GetInt32("IdCT");

                                if (!result.ContainsKey(idOfferCt))
                                {
                                    result.Add(idOfferCt, reader.GetString("Title"));
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"TrackingActionsRecoverAndPersist - GetTitleByOffers ex: {ex.ToString()}");
                _exceptionPublisherService.Publish(ex, "TrackingActionsRecoverAndPersist", "GetTitleByOffers");
            }

            return result;
        }

        public void ActionTrackingCompanyUpdateStatus(TrackingActionEntity trackingActions)
        {
            try
            {
                using (var conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
                {
                    using (var cmd = new MySqlCommand("actionTrackingCompanysUpdateStatus", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("_idobject", trackingActions.IdObject);
                        cmd.Parameters.AddWithValue("_idportal", trackingActions.IdPortal);
                        cmd.Parameters.AddWithValue("_idstatus", trackingActions.IdStatus);
                        cmd.Parameters.AddWithValue("_type_feature", trackingActions.Type);

                        conn.Open();

                        cmd.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyRecoverAndPersist - ActionTrackingCompanyUpdateStatus: ex: {ex.ToString()}");
                _exceptionPublisherService.Publish(ex, "CompanyRecoverAndPersist", "ActionTrackingCompanyUpdateStatus");
            }
        }
    }
}
