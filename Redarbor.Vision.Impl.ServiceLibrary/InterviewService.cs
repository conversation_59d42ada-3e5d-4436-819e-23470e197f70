using Redarbor.Core.Cache.Contracts.ServiceLibrary;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Core.Counter.Contracts.ServiceLibrary;
using Redarbor.Core.Kpi.Contracts.ServiceLibrary;
using Redarbor.Core.Kpi.Contracts.ServiceLibrary.Enums;
using Redarbor.Core.Shared.Contracts.ServiceLibrary;
using Redarbor.Extensions.Library.Extensions;
using Redarbor.Master.Entities.Enums;
using Redarbor.Master.Entities.Vision;
using Redarbor.Vision.Contracts.ServiceLibrary;
using Redarbor.Vision.Impl.ServiceLibrary.Configuration;
using System;
using System.IO;
using System.Net;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web.Script.Serialization;

namespace Redarbor.Vision.Impl.ServiceLibrary
{
    public class InterviewService : IInterviewsService
    {
        private readonly ITempCache _tempCacheService;
        private readonly IExceptionPublisherService _exceptionPublisherService;
        private readonly IKpiService _kpiService;
        private readonly ICompanyCountersService _companyCountersService;
        private readonly IEncryptionService _encryptionService;
        private readonly IVisionConfiguration _visionConfiguration;

        private const string _KEY_INTERVIEW_LIST_COMPANY_DATA_CACHE = "INTERVIEW_LIST_COMPANY_DATA";

        public InterviewService(ITempCache tempCacheService,
            IExceptionPublisherService exceptionPublisherService, IKpiService kpiService, ICompanyCountersService companyCountersService,
            IEncryptionService encryptionService,
            IVisionConfiguration visionConfiguration)
        {
            _tempCacheService = tempCacheService;
            _exceptionPublisherService = exceptionPublisherService;
            _kpiService = kpiService;
            _companyCountersService = companyCountersService;
            _encryptionService = encryptionService;
            _visionConfiguration = visionConfiguration;
        }


        public ElasticResult<InterviewEntity> FindAllInterviews(InterviewFilter interviewFilter)
        {
            try
            {
                if (interviewFilter.PortalId <= 0 || interviewFilter.CompanyId <= 0)
                    return new ElasticResult<InterviewEntity>();

                var cacheKey = GetCacheKeyByCompanyAndPortal(_KEY_INTERVIEW_LIST_COMPANY_DATA_CACHE, interviewFilter.CompanyId,
                                            interviewFilter.PortalId, interviewFilter.PageNumber);

                var interviews = _tempCacheService.Get<ElasticResult<InterviewEntity>>(cacheKey);

                if (interviews != null && interviews.TotalDocuments != 0)
                    return interviews;
                else
                {
                    var serializer = new JavaScriptSerializer();
                    interviews = serializer.Deserialize<ElasticResult<InterviewEntity>>(CallAPI(interviewFilter, "/interview/list", interviewFilter.PortalId, "post"));

                    if (interviews != null && interviews.TotalDocuments != 0)
                    {
                        interviews.Documents.ForEach(FillExtraInfoInterviewsEntity);
                        _tempCacheService.Add(cacheKey, interviews, TimeSpan.FromMinutes(60));
                    }


                    return interviews;
                }
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "InterviewsService", "FindAllInterviews");
            }

            return new ElasticResult<InterviewEntity>();
        }

        public ElasticResult<InterviewEntity> FindInterviewsByInterviewFilterComment(InterviewFilter interviewFilter)
        {
            if (interviewFilter.InterviewFilterComment <= 0 || interviewFilter.PortalId <= 0 || interviewFilter.CompanyId <= 0)
                return new ElasticResult<InterviewEntity>();

            var serializer = new JavaScriptSerializer();
            var interviews = serializer.Deserialize<ElasticResult<InterviewEntity>>(CallAPI(interviewFilter, "/interview/list", interviewFilter.PortalId, "post"));

            if (interviews != null && interviews.TotalDocuments != 0)
                interviews.Documents.ForEach(FillExtraInfoInterviewsEntity);

            return interviews;
        }


        public bool InsertAnswer(int idInterview, string text, int companyId, short portalId, long userId, bool giveThanks = false, bool update = false)
        {
            if (idInterview <= 0 || string.IsNullOrWhiteSpace(text) || portalId < 0 || companyId <= 0)
                return false;

            var interview = new InterviewEntity
            {
                Id = idInterview,
                CompanyComment = text,
                PortalId = portalId
            };

            var result = CallAPI(interview, "interview/savecomment", interview.PortalId, "post");

            var isOk = result.ToBoolean();

            if (isOk)
            {
                if (update)
                {
                    ActionKpi(companyId, portalId, (short)KpiEnum.VALUATIONS_UPDATE_PERSONAL_REPLY_INTERVIEW, userId);
                }
                else
                {
                    if (giveThanks)
                        ActionKpi(companyId, portalId, (short)KpiEnum.VALUATIONS_AUTO_REPLY_INTERVIEW, userId);
                    else
                        ActionKpi(companyId, portalId, (short)KpiEnum.VALUATIONS_PERSONAL_REPLY_INTERVIEW, userId);
                }

                WaitElastic();
            }

            return isOk;
        }

        public bool DeleteAnswer(int idInterview, int companyId, short portalId, long userId)
        {
            if (idInterview <= 0 || portalId <= 0 || companyId <= 0)
                return false;

            var interview = new InterviewEntity
            {
                Id = idInterview,
                PortalId = portalId,
                CompanyComment = string.Empty
            };

            var result = CallAPI(interview, "interview/savecomment", interview.PortalId, "post");

            var isOK = result.ToBoolean();

            if (isOK)
            {
                ActionKpi(companyId, portalId, (short)KpiEnum.VALUATIONS_DELETE_PERSONAL_REPLY_INTERVIEW, userId);
                WaitElastic();
            }

            return isOK;
        }

        public bool DenouncedInterview(InterviewModerationEntity interviewModeration, int companyId)
        {
            if (interviewModeration.Id <= 0 || interviewModeration.PortalId <= 0
                || interviewModeration.ModerationStatusId != (short)InterviewModerationStatusEnum.Denounced)
                return false;

            var result = CallAPI(interviewModeration, "interview/moderate", interviewModeration.PortalId, "post");

            var isOk = result.ToBoolean();

            if (isOk)
            {
                _companyCountersService.AddCounterCompany((short)KpiEnum.INTERVIEW_REPORTED_BY_COMPANY, interviewModeration.PortalId, companyId, 1, true);

                WaitElastic();
            }

            return isOk;
        }

        private string CallAPI(object objectToSend, string apiAction, short idPortal, string method)
        {
            string callResult = "";

            try
            {
                JavaScriptSerializer serializer = new JavaScriptSerializer();
                var request = (HttpWebRequest)WebRequest.Create($"{_visionConfiguration.ApiValuationsEndPoint}{apiAction}");
                request.Method = method.ToUpper();
                request.ContentType = "application/json";

                request.KeepAlive = false;

                if (method.ToUpper().Equals("POST") && objectToSend != null)
                {
                    using (var sWriter = new StreamWriter(request.GetRequestStream()))
                    {
                        sWriter.Write(serializer.Serialize(objectToSend));
                        sWriter.Flush();
                        sWriter.Close();
                    }
                }

                using (var httpResponse = request.GetResponse().GetResponseStream())
                {
                    using (var sReader = new StreamReader(httpResponse))
                    {
                        callResult = sReader.ReadToEnd();

                    }
                }

            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "InterviewsService", "CallAPI");
            }

            return callResult;
        }

        private string GetCacheKeyByCompanyAndPortal(string baseCache, int companyId, short portalId, int page)
        {
            var cacheKey = new StringBuilder();

            cacheKey.Append(baseCache);
            cacheKey.Append("_CompanyId_");
            cacheKey.Append(companyId);
            cacheKey.Append("_PortalId_");
            cacheKey.Append(portalId);
            cacheKey.Append("_Page_");
            cacheKey.Append(page);

            return cacheKey.ToString();
        }

        private void ActionKpi(int companyId, short portalId, short _kpiType, long userId)
        {
            Task.Factory.StartNew(() =>
            {
                _companyCountersService.AddCounterCompany(_kpiType, portalId, companyId, 1, false);

                _kpiService.AddSumBlock(_kpiType, portalId, 1);

            });
        }

        private void WaitElastic()
        {
            Thread.Sleep(1500);
        }

        private void FillExtraInfoInterviewsEntity(InterviewEntity interview)
        {
            interview.IdEncrypt = _encryptionService.Encrypt(interview.Id.ToString());
        }

    }
}
