{"profiles": {"Redarbor.OfferWatcher DEV": {"commandName": "Project", "dotnetRunMessages": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "Redarbor.OfferWatcher STA": {"commandName": "Project", "dotnetRunMessages": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Staging"}}, "Redarbor.OfferWatcher PRO": {"commandName": "Project", "dotnetRunMessages": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Production"}}}}