using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Redarbor.OfferWatcher.Application.Abstractions.Repositories;
using Redarbor.OfferWatcher.Application.Abstractions.Services;
using Redarbor.OfferWatcher.Application.Entities;
using Redarbor.OfferWatcher.Infrastructure.Repositories;
using Redarbor.OfferWatcher.Infrastructure.Services;
using Redarbor.Tools.Exceptions.Consumer.Enums;
using Serilog;
using System;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace Redarbor.OfferWatcher
{
    public class Program
    {
        public static async Task Main(string[] args)
        {
            Directory.SetCurrentDirectory(AppDomain.CurrentDomain.BaseDirectory);

            var builder = Host.CreateDefaultBuilder(args)
            .ConfigureAppConfiguration(conf =>
            {
                conf.SetBasePath(AppDomain.CurrentDomain.BaseDirectory);
            })
            .UseEnvironment(Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production")
            .ConfigureServices((hostContext, services) =>
            {

                services.AddHostedService<Worker>();
                services.AddScoped<IOfferRepository, OfferRepository>();
                services.AddScoped<IOfferWatcherService, OfferWatcherService>();
                services.AddScoped<IOfferGroupRepository, OfferGroupRepository>();

                var applicationData = GetApplicationData(hostContext.Configuration);
                services.AddSingleton(applicationData);

                var connectionStrings = GetConnectionStrings(hostContext.Configuration);
                services.AddSingleton(connectionStrings);

                services.AddRedarborToolsExceptionsConsumer((c) =>
                {
                    var envEnum = Enum.TryParse<EnvironmentEnum>(Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT"), true, out var env) ? env : EnvironmentEnum.Production;

                    c.SetEnvironment(envEnum);
                    c.SetArea(ExceptionAreaEnum.Payments);
                    c.SetAppId(applicationData.AppId);
                    c.SetAppType(ExceptionAppTypeEnum.BatchProcess);
                    c.SetBusiness(ExceptionBusinessEnum.CompuTrabajo);
                    c.SetApiEndpoint(applicationData.ApiExceptionsUrl.ToString());
                    c.SetThrowExceptions(applicationData.ThrowExceptions);
                }, addRabbitMqDependences: false);
            }).UseSerilog((ctx, lc) =>
            {
                lc.ReadFrom.Configuration(ctx.Configuration);
            });

            var host = Debugger.IsAttached || args.Contains("--console")
                ? builder.UseConsoleLifetime()
                : builder.UseWindowsService();

            var builtHost = builder.Build();

            await builtHost.RunAsync();
        }

        private static ApplicationData GetApplicationData(IConfiguration configuration)
        {
            var applicationData = new ApplicationData();
            configuration.GetSection(nameof(ApplicationData)).Bind(applicationData);
            return applicationData;
        }

        private static ConnectionStrings GetConnectionStrings(IConfiguration configuration)
        {
            var connectionStrings = new ConnectionStrings();
            configuration.GetSection(nameof(ConnectionStrings)).Bind(connectionStrings);
            return connectionStrings;
        }
    }
}