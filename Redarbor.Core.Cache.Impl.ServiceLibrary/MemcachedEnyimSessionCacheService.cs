using Redarbor.Common.Entities.Configuration;
using Redarbor.Core.Cache.Contracts.ServiceLibrary;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Core.Resolver.Contracts.ServiceLibrary;
using Redarbor.Extensions.Library.DI;

namespace Redarbor.Core.Cache.ServiceLibrary
{
    [RegisterStatefulService]
    public class MemcachedEnyimSessionCacheService : MemcachedEnyimBaseCacheService, IMemcachedEnyimSessionCacheService
    {

        public MemcachedEnyimSessionCacheService(IPortalConfigurationService portalConfigurationService,
            IPortalResolverService portalResolverService,
            IApplicationIdResolverService applicationIdResolverService,
            IExceptionPublisherService exceptionPublisherService)
            : base(portalConfigurationService,
                    portalResolverService,
                    applicationIdResolverService,
                    exceptionPublisherService)
        {
        }
        protected override MemcachedConfig GetMemcachedConfig(short portalId, short applicationId)
        {
            PortalConfig portalConfig = _portalConfigurationService.GetPortalConfiguration(portalId, applicationId);
            return portalConfig.SessionMemcached;
        }
    }
}
