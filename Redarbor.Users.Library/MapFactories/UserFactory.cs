using System;
using System.Diagnostics;
using MySql.Data.MySqlClient;
using Redarbor.Extensions.Library;
using Redarbor.Extensions.Library.Extensions;
using Redarbor.Master.Entities.Users;

namespace Redarbor.Users.Library.MapFactories
{
    public class UserFactory : FactoryBase
    {
        public override TOutput Map<TInput, TOutput>(TInput input)
        {
            if (input == null) { return default(TOutput); }

            var dataReader = input as MySqlDataReader;
            if (dataReader == null) { throw new InvalidCastException(string.Format("Exception casting object MySqlDataReader in {0}", this.GetType().Name)); }

            var partialMsg = Convert.IsDBNull(dataReader["username"]) ? "Null nombre de usuario" : dataReader["username"].ToString();

            try
            {
                var dto = new UserEntity
                {
                    Id = dataReader.GetAsInt("iduser"),
                    PortalId = dataReader.GetAsShort("idportal"),
                    Username = dataReader.GetAsString("username"),
                    Password = dataReader.GetAsString("password"),
                    CompanyId = dataReader.GetAsInt("idcompany"),
                    ParentCompanyId = dataReader.GetAsInt("idparentcompany"),
                    OriginId = dataReader.GetAsInt("idorigin"),
                    TypeId = dataReader.GetAsInt("idusertype"),
                    ContactName = dataReader.GetAsString("contactname"),
                    Email = dataReader.GetAsString("email"),
                    PhoneNumbers = dataReader.GetAsString("telephone"),
                    Fax = dataReader.GetAsString("fax"),
                    CreatedOn = dataReader.GetAsDateTime("createdon"),                    
                    CreatedBy = dataReader.GetAsInt("createdby"),
                    UpdatedOn = dataReader.GetAsDateTime("updatedon"),
                    DeletedOn = dataReader.GetAsDateTime("deletedon"),
                    LastLoginOn = dataReader.IsDBNull(dataReader.GetOrdinal("lastlogin")) ?
                                !dataReader.IsDBNull(dataReader.GetOrdinal("updatedon"))
                                    ? dataReader.GetAsDateTime("updatedon")
                                    : DateTime.MinValue
                                        : dataReader.GetAsDateTime("lastlogin"),
                    Principal = dataReader.GetAsShort("principal"),
                    NdrStatusId = dataReader.GetAsInt("ndrstatus"),
                    StatusId = dataReader.GetAsShort("idstatus"),
                    RoleId = dataReader.GetAsShort("user_role"),
                    AvatarPath = dataReader.GetAsString("avatar_path"),
                    VerifiedMail = dataReader.GetAsShort("verifiedmail")
                };

                return dto as TOutput;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"Exception parsing User {partialMsg} from database.", ex);
                throw;
            }
        }
    }
}
