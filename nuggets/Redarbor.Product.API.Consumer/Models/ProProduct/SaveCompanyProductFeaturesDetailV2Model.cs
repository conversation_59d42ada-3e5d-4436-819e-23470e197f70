using Redarbor.Product.API.Consumer.Models.CompanyProducts;
using System.Collections.Generic;

namespace Redarbor.Product.API.Consumer.Models.ProProduct
{
    public class SaveCompanyProductFeaturesDetailV2Model
    {
        public short PortalId { get; set; }
        public int CompanyProductId { get; set; }
        public int CompanyId { get; set; }
        public int CustomerId { get; set; }
        public short GroupId { get; set; }
        public CompanyProductHeaderWithComercialNameModel CompanyProductModified { get; set; }
        public List<ProProductPortalFeatureModel> ProductsToAdd { get; set; }
        public List<ProProductPortalFeatureModel> ProductsToUpdate { get; set; }
        public List<ProProductPortalFeatureModel> ProductsToDelete { get; set; }

        public SaveCompanyProductFeaturesDetailV2Model(short portalId, int companyProductId, int companyId, int customerId, short groupId, CompanyProductHeaderWithComercialNameModel companyProductModified, List<ProProductPortalFeatureModel> productsToAdd, List<ProProductPortalFeatureModel> productsToUpdate, List<ProProductPortalFeatureModel> productsToDelete)
        {
            PortalId = portalId;
            CompanyProductId = companyProductId;
            CompanyId = companyId;
            CustomerId = customerId;
            GroupId = groupId;
            CompanyProductModified = companyProductModified;
            ProductsToAdd = productsToAdd;
            ProductsToUpdate = productsToUpdate;
            ProductsToDelete = productsToDelete;
        }
    }
}
