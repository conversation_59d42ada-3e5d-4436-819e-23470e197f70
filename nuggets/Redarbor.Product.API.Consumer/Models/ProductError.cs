using Redarbor.Product.API.Consumer.Enums;

namespace Redarbor.Product.API.Consumer.Models
{
    public class ProductError
    {
        public ProductErrorEnum Error { get; set; }
        public string Description { get; set; }
        public ProductError(ProductErrorEnum error, string description)
        {
            Error = error;
            Description = description;
        }

        public ProductError(ProductErrorEnum error)
        {
            Error = error;
            Description = error.ToString();
        }

        public ProductError(string description)
        {
            Description = description;
        }

        public ProductError()
        {
        }
    }
}
