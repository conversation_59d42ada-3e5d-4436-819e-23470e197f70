using Redarbor.Product.API.Consumer.Enums;
using System;

namespace Redarbor.Product.API.Consumer.Models.CompanyProducts
{
    public class ExtendCompanyProductExpirationDateResponseModel : Response
    {
        public DateTime NewExpirationDate { get; set; }

        public ExtendCompanyProductExpirationDateResultApiEnum ExtendCompanyProductExpirationDateResultEnum { get; set; }

        public ExtendCompanyProductExpirationDateResponseModel(DateTime newExpirationDateReturn, ExtendCompanyProductExpirationDateResultApiEnum extendCompanyProductExpirationDateResultEnum) : base(newExpirationDateReturn != DateTime.MinValue)
        {
            NewExpirationDate = newExpirationDateReturn;
            ExtendCompanyProductExpirationDateResultEnum = extendCompanyProductExpirationDateResultEnum;
        }

        public ExtendCompanyProductExpirationDateResponseModel()
        {
        }
    }
}
