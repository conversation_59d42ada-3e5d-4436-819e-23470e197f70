using Redarbor.Kinesis.Consumer;
using Redarbor.Kinesis.Consumer.Configuration;
using Redarbor.Kinesis.Consumer.Enums;
using Redarbor.Kinesis.Consumer.Models;
using Redarbor.Master.Impl.ServiceLibrary.KinesisEventHelper.Entities;
using Redarbor.RabbitMQ.Abstractions;
using Redarbor.RabbitMQ.Api;
using Redarbor.RabbitMQ.Core;
using Redarbor.RabbitMQ.Service;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Configuration;
using System.Threading;

namespace Redarbor.Master.Impl.ServiceLibrary.KinesisEventHelper
{
    public class KinesisEventHelper
    {
        const string EVENT_NAME = "Internal/Profiling";
        const string PRODUCTION = "production";
        const string STAGING = "staging";
        const string DEVELOPMENT = "development";
        private static ConcurrentDictionary<string, EventDataBuildDto> _buildEventsDictionary = new ConcurrentDictionary<string, EventDataBuildDto>();
        private static int _idApp = GetIdApp();
        private static IKinesisConsumerService _kinesisConsumerService = InitializeKinesisConsumer();

        private static int GetIdApp()
        {
            int.TryParse(ConfigurationManager.AppSettings["APP_ID"] ?? "", out var result);
            return result;
        }

        private static IKinesisConsumerService InitializeKinesisConsumer()
        {
            var environment = GetEnviromentForEnumKinesis(ConfigurationManager.AppSettings["Environment"] ?? "");
            var byRabbit = ConfigurationManager.AppSettings["KINESIS_BY_RABBIT"] != null
                    && ConfigurationManager.AppSettings["KINESIS_BY_RABBIT"].ToLower() == "true";

            var kinesisConsumerConfiguration = new KinesisConsumerConfigurator();
            kinesisConsumerConfiguration.SetKinesisByRabbit(byRabbit);
            kinesisConsumerConfiguration.SetAppId(_idApp);
            kinesisConsumerConfiguration.SetBusiness(KinesisBusinessEnum.Empleo);
            kinesisConsumerConfiguration.SetEnvironment(environment);

            if (!byRabbit)
            {
                kinesisConsumerConfiguration.SetApiEndpoint(ConfigurationManager.AppSettings["KINESIS_API_ENDPOINT"] ?? "");
            }

            IProducerRabbitMQ _producerRabbitMQ = InitRabbitConsumer();

            return new KinesisConsumerService(_producerRabbitMQ);
        }

        private static KinesisEnvironmentEnum GetEnviromentForEnumKinesis(string environment)
        {
            switch (environment.ToLower())
            {
                case DEVELOPMENT:
                    return KinesisEnvironmentEnum.Development;
                case STAGING:
                    return KinesisEnvironmentEnum.Staging;
                case PRODUCTION:
                    return KinesisEnvironmentEnum.Production;
                default:
                    return KinesisEnvironmentEnum.Development;
            }
        }

        private static IProducerRabbitMQ InitRabbitConsumer()
        {
            RabbitMQBuilder.AddRedarborRabbitMQ(x => x.SetApiEndpoint(ConfigurationManager.AppSettings["RABBITMQ_API_ENDPOINT"] ?? ""));

            return new ProducerRabbitMQ(new RabbitMQApi(), new DiskServiceCore());
        }


        public static void Add(EventDataBuildDto eventDataBuildDto)
        {
            if (eventDataBuildDto != null)
            {
                try
                {
                    var key = $"{eventDataBuildDto.IdPortal}_{eventDataBuildDto.Type}";

                    if (!_buildEventsDictionary.ContainsKey(key))
                    {
                        _buildEventsDictionary.TryAdd(key, eventDataBuildDto);
                    }
                    else if (_buildEventsDictionary.TryGetValue(key, out var eventDataBuildBase))
                    {
                        Interlocked.Add(ref eventDataBuildBase.NumActions, eventDataBuildDto.NumActions);
                        Interlocked.Add(ref eventDataBuildBase.TotalTimeOfActions, eventDataBuildDto.TotalTimeOfActions);

                        CheckAndSend(eventDataBuildBase, key);
                    }
                }
                catch
                {
                }
            }
        }

        private static void CheckAndSend(EventDataBuildDto eventDataBuildBase, string key)
        {
            if (eventDataBuildBase.ServerDateTime.AddMinutes(1) < DateTime.Now
                && _buildEventsDictionary.TryRemove(key, out var eventDataBuildBaseDeleted)
                && eventDataBuildBaseDeleted.NumActions > 0)
            {
                _kinesisConsumerService.Add(new List<AddModel>()
                    {
                        new AddModel()
                        {
                            Country = "",
                            EventData = new
                            {
                                IdPortal = eventDataBuildBaseDeleted.IdPortal,
                                IdApp = _idApp,
                                ServerTimestamp = eventDataBuildBaseDeleted.ServerDateTime,
                                NumActions = eventDataBuildBaseDeleted.NumActions,
                                AverageTime =  (float)eventDataBuildBaseDeleted.TotalTimeOfActions/eventDataBuildBaseDeleted.NumActions,
                                Type = eventDataBuildBaseDeleted.Type.ToString(),
                                BuildVersion = ConfigurationManager.AppSettings["BuildVersion"] ?? ""
                            },
                            EventName = EVENT_NAME,
                            IdPortal = eventDataBuildBaseDeleted.IdPortal
                        }
                    }, KinesisStreamEnum.Kinesis);
            }
        }

        public static void SendKinesisConvertToComplete(EventConvertToComplete eventConvertToComplete)
        {
            _kinesisConsumerService.Add(new List<AddModel>()
                {
                    new AddModel()
                    {
                        Country = "",
                        EventData = new
                        {
                            IdPortal = eventConvertToComplete.IdPortal,
                            IdApp = _idApp,
                            ServerTimestamp = eventConvertToComplete.DateTimeNowByPortal,
                            IdOffer = eventConvertToComplete.IdOffer,
                            IdOriginButton = eventConvertToComplete.IdOriginButton,
                            OriginButtonDescription = eventConvertToComplete.OriginButtonDescription,
                            IdPage = eventConvertToComplete.IdPage,
                            PageDescription = eventConvertToComplete.PageDescription,
                            OriginConvertMethod = eventConvertToComplete.OriginConvertMethod,
                            DeviceType = eventConvertToComplete.DeviceType,
                            BuildVersion = ConfigurationManager.AppSettings["BuildVersion"] ?? ""
                        },
                        EventName = "Company/ConvertToPaid",
                        IdPortal = eventConvertToComplete.IdPortal
                    }
                }, KinesisStreamEnum.Kinesis);
        }

        public static void SendKinesisSearchCV(EventSearchCV eventSearchCV)
        {
            _kinesisConsumerService.Add(new List<AddModel>()
            {
                new AddModel()
                {
                    Country = "",
                    EventData = new
                    {
                        IdPortal = eventSearchCV.PortalId,
                        IdApp = eventSearchCV.IdApp,
                        BuildVersion = ConfigurationManager.AppSettings["BuildVersion"] ?? "",
                        Exact = eventSearchCV.Exact,
                        UserToken = eventSearchCV.UserToken,
                        IdUser = eventSearchCV.IdUser,
                        Company = eventSearchCV.Company,
                        PortalId= eventSearchCV.PortalId,
                        ServerTimestamp = eventSearchCV.ServerTimestamp,
                        ClientTimestamp = eventSearchCV.ClientTimestamp,
                        Platform = eventSearchCV.Platform,
                        UserAgent = eventSearchCV.UserAgent,
                        Device = eventSearchCV.Device,
                        Source = eventSearchCV.Source,
                        IdPage = eventSearchCV.IdPage,
                        NumResults = eventSearchCV.NumResults,
                        IdSession = eventSearchCV.IdSession,
                        PageNumber = eventSearchCV.PageNumber,
                        SearchWord = eventSearchCV.SearchWord,
                        SearchCompany = eventSearchCV.SearchCompany,
                        Visualization = eventSearchCV.Visualization,
                        CategoryIds = eventSearchCV.CategoryIds,
                        MinYearsExperience = eventSearchCV.MinYearsExperience,
                        MaxYearsExperience = eventSearchCV.MaxYearsExperience,
                        IsWorking = eventSearchCV.IsWorking,
                        HasDisability  = eventSearchCV.HasDisability,
                        Department = eventSearchCV.Department,
                        Nationalities = eventSearchCV.Nationalities,
                        CityIds = eventSearchCV.CityIds,
                        LastUpdate = eventSearchCV.LastUpdate,
                        University = eventSearchCV.University,
                        LevelStudyIds = eventSearchCV.LevelStudyIds,
                        IsStudying = eventSearchCV.IsStudying,
                        LanguagesIds = eventSearchCV.LanguagesIds,
                        MinSalary = eventSearchCV.MinSalary,
                        MaxSalary = eventSearchCV.MaxSalary,
                        CommentedIds = eventSearchCV.CommentedIds,
                        Valuations = eventSearchCV.Valuations,
                        HasPhoto = eventSearchCV.HasPhoto,
                        NIT = eventSearchCV.NIT,
                        OrderSearch = eventSearchCV.OrderSearch,
                        ClientIP = eventSearchCV.ClientIP,
                        CandidateInfo = eventSearchCV.CandidateInfo
                    },
                    EventName = "Company/SearchCV",
                    IdPortal = eventSearchCV.PortalId
                }
            }, KinesisStreamEnum.Kinesis);
        }
    }
}
