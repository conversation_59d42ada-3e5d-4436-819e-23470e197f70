using Redarbor.Common.Entities.Configuration;
using Redarbor.Core.Cache.Contracts.ServiceLibrary;
using Redarbor.Extensions.Library.DI;
using Redarbor.Master.Contracts.ServiceLibrary;
using Redarbor.Master.Contracts.ServiceLibrary.Constants;
using Redarbor.Master.Contracts.ServiceLibrary.DTO;
using Redarbor.Web.UI.Library.Exceptions;
using System;
using System.Configuration;
using System.Web;
using System.Web.Configuration;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;

namespace Redarbor.Master.Impl.ServiceLibrary
{
    [RegisterStatefulService]
    public class LoginCompanyService : ILoginCompanyService
    {
        private readonly IMemcachedEnyimSessionCacheService _memcachedEnyimSessionCacheService;
        private readonly IExceptionPublisherService _exceptionPublisherService;
        private readonly ICookieService _cookieService;


        public LoginCompanyService(IMemcachedEnyimSessionCacheService memcachedEnyimSessionCacheService,
            IExceptionPublisherService exceptionPublisherService,
            ICookieService cookieService)
        {
            _memcachedEnyimSessionCacheService = memcachedEnyimSessionCacheService;
            _exceptionPublisherService = exceptionPublisherService;
            _cookieService = cookieService;
        }

        public void LogOff(PortalConfig portalConfig)
        {
            try
            {
                var cacheKey = $"{CacheKeys.CACHE_NAME_USERSESSION}{HttpContext.Current.Session.SessionID}";
                _memcachedEnyimSessionCacheService.Remove(cacheKey);
#if DEBUG
                _cookieService.Delete(CookieType.USER_COMPANY.ToString(), "localhost");
#else
    _cookieService.Delete(CookieType.USER_COMPANY.ToString(), portalConfig.cookie_domain);
#endif
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "LoginCompanyService", "LogOff");
            }
        }

        public bool LoginAndCheck(CompanyCredentials credentials, string login, string password, ref string message, int timeout = 0)
        {

            var cacheKey = $"{CacheKeys.CACHE_NAME_USERSESSION}{credentials.SessionId}";
            _memcachedEnyimSessionCacheService.Add(cacheKey, credentials, TimeSpan.FromMinutes(timeout));

            return _memcachedEnyimSessionCacheService.Get<CompanyCredentials>(cacheKey) != null;
        }

        public void Login(CompanyCredentials credentials, string login, string password, ref string message, int timeout = 0)
        {

            var cacheKey = $"{CacheKeys.CACHE_NAME_USERSESSION}{credentials.SessionId}";
            _memcachedEnyimSessionCacheService.Add(cacheKey, credentials, TimeSpan.FromMinutes(timeout));
        }

        public CompanyCredentials GetCredentialsFromSession(string sessionId = "")
        {
            try
            {
                var sessionIdKey = string.IsNullOrEmpty(sessionId) ? GetSesionId() : sessionId;
                var cacheKey = $"{CacheKeys.CACHE_NAME_USERSESSION}{sessionIdKey}";
                var userData = _memcachedEnyimSessionCacheService.Get<object>(cacheKey);

                if (userData is null || !(userData is CompanyCredentials))
                    return null;
                
                return (CompanyCredentials)userData;
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "LoginCompanyService", "GetCredentialsForSession");
                throw new FormsAuthenticationExpiredException();
            }            
        }

        public void UpdateUserData(CompanyCredentials newCredentials)
        {
            var timeout = 5;

            var cacheKey = $"{CacheKeys.CACHE_NAME_USERSESSION}{newCredentials.SessionId}";
            _memcachedEnyimSessionCacheService.Add(cacheKey, newCredentials, TimeSpan.FromMinutes(timeout));
        }

        public bool UpdateCredentials(CompanyCredentials credentials)
        {
            try
            {
                var cacheKey = $"{CacheKeys.CACHE_NAME_USERSESSION}{credentials.SessionId}";
                var timeout = ((SessionStateSection)(ConfigurationManager.GetSection("system.web/sessionState") as ConfigurationSection)).Timeout.TotalMinutes;

                _memcachedEnyimSessionCacheService.Add(cacheKey, credentials, TimeSpan.FromMinutes(timeout));

                return true;
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "LoginCompanyService", "UpdateCredentials");
                return false;
            }
        }

        private string GetSesionId()
        {
            return (HttpContext.Current != null
                && HttpContext.Current.Session != null 
                && !string.IsNullOrEmpty(HttpContext.Current.Session.SessionID)) ? HttpContext.Current.Session.SessionID : string.Empty;
        }
    }
}
