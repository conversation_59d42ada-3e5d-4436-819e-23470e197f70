using System.Collections.Specialized;

namespace Redarbor.Core.Shared.Contracts.ServiceLibrary
{
    public interface IHttpService
    {
        int GetSessionSourceButton();
        int GetSessionSourcePage();
        string GetSessionId();
        string GetServerName();
        string GetServerProtocol();
        string GetRequestQueryString();
        NameValueCollection GetRequestForm();
        string GetHttpMethod();
        string GetApplicationPath();
    }
}

