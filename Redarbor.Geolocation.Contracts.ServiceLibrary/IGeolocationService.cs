using Redarbor.Geolocation.Contracts.ServiceLibrary.DTO;

namespace Redarbor.Geolocation.Contracts.ServiceLibrary
{
    public interface IGeoLocationService
    {
        string GetIpCountry(string ip);    
        int GetCountryPrefix(int countryId);
        int GetDistance(GeoPointDTO geoPoint, GeoPointDTO matchLocation);
        GeoPointDTO GetGeoPoint(short portalId, int idPostalCode, int idCity);
    }
}
