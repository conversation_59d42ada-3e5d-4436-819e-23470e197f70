using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Redarbor.AE.Procs.ImportOffersSendMail.Application.Abstractions;
using Redarbor.AE.Procs.ImportOffersSendMail.Application.Models;

namespace Redarbor.AE.Procs.ImportOffersSendMail
{
    public class Worker : BackgroundService
    {
        private readonly ILogger<Worker> _logger;
        private readonly IImportOffersSendMailManagerService _importOffersSendMailManagerService;
        private readonly ApplicationSettingsData _appData;

        public Worker(ILogger<Worker> logger, IImportOffersSendMailManagerService importOffersSendMailManagerService, ApplicationSettingsData applicationSettingsData)
        {
            _logger = logger;
            _importOffersSendMailManagerService = importOffersSendMailManagerService;
            _appData = applicationSettingsData;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                _logger.LogInformation("Worker running at: {time}", DateTimeOffset.UtcNow);
                await _importOffersSendMailManagerService.RunAsync(stoppingToken);
                _logger.LogInformation("Sleeping at: {time}", DateTimeOffset.UtcNow);
                await Task.Delay(_appData.SecondsToSleepAfterEachExecution * 1000, stoppingToken);
            }
        }
    }
}
