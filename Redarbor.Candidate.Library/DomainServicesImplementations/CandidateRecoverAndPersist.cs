using MySql.Data.MySqlClient;
using Redarbor.Candidate.Library.DomainServiceContracts;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Core.Resolver.Contracts.ServiceLibrary;
using Redarbor.Core.Resolver.Contracts.ServiceLibrary.Enums;
using Redarbor.Extensions.Library.Extensions;
using Redarbor.Master.Entities.Candidate;
using Redarbor.Master.Entities.Enums;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;

namespace Redarbor.Candidate.Library.DomainServicesImplementations
{
    public class CandidateRecoverAndPersist : ICandidateRecoverAndPersist
    {
        private readonly IConnectionStringResolverService _connectionStringResolverService;
        private readonly IExceptionPublisherService _exceptionPublisherService;

        public CandidateRecoverAndPersist(IConnectionStringResolverService connectionStringResolverService,
            IExceptionPublisherService exceptionPublisherService)
        {
            _connectionStringResolverService = connectionStringResolverService;
            _exceptionPublisherService = exceptionPublisherService;
        }

        public string GetCandidatePhoto(int idCandidate)
        {
            string photoPath = string.Empty;

            using (var conex = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
            {
                using (var command = new MySqlCommand("sp_dtcandidate_GetPhoto", conex))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    try
                    {
                        command.Parameters.Add(new MySqlParameter("_idcandidate", idCandidate));
                        conex.Open();

                        var result = command.ExecuteScalar();

                        if (result != null)
                        {
                            photoPath = result.ToString();
                        }

                    }
                    catch (Exception ex)
                    {
                        Trace.TraceError($"CandidateRecoverAndPersist - GetPhoto: ex: {ex.ToString()}");
                        _exceptionPublisherService.Publish(ex, "CandidateRecoverAndPersist", "GetPhoto");
                    }
                }
            }

            return photoPath;
        }       

        public CandidateEntity GetCandidateById(int Id, short idPortal)
        {
            try
            {
                using (var conex = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, idPortal)))
                {
                    using (var command = new MySqlCommand("gl_candidate_selectbypk_v12", conex))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.Add(new MySqlParameter("_idcandidate", Id));

                        conex.Open();

                        var candidateEntity = new CandidateEntity();
                        using (var dtr = command.ExecuteReader())
                        {
                            if (dtr != null && dtr.Read())
                            {
                                candidateEntity.idcandidate = dtr.GetAsInt("idcandidate");
                                candidateEntity.iduser = dtr.GetAsInt("iduser");
                                candidateEntity.idportal = dtr.GetAsShort("idportal");
                                candidateEntity.name = dtr.GetAsString("name");
                                candidateEntity.surname = dtr.GetAsString("surname");
                                candidateEntity.birthdate = dtr.GetAsDateTime("birthdate");
                                candidateEntity.age = GetAgeFromDateBirth(candidateEntity.birthdate);
                                candidateEntity.idgender = dtr.GetAsShort("idgender");
                                candidateEntity.idcity = dtr.GetAsShort("idcity");
                                candidateEntity.idlocalization = dtr.GetAsShort("idlocalization");
                                candidateEntity.idcountry = dtr.GetAsShort("idcountry");
                                candidateEntity.idotherlocalizations = dtr.GetAsString("idotherlocalizations");
                                candidateEntity.address = dtr.GetAsString("address");
                                candidateEntity.postcode = dtr.GetAsString("postcode");
                                candidateEntity.countrypermission = dtr.GetAsShort("countrypermission");
                                candidateEntity.phone1 = dtr.GetAsString("phone1");
                                candidateEntity.idphonetype1 = dtr.GetAsShort("idphonetype1");
                                candidateEntity.phone2 = dtr.GetAsString("phone2");
                                candidateEntity.idphonetype2 = dtr.GetAsShort("idphonetype2");
                                candidateEntity.idmaritalstatus = dtr.GetAsShort("idmaritalstatus");
                                candidateEntity.nationality = dtr.GetAsShort("nationality");
                                candidateEntity.idlegalsituation = dtr.GetAsShort("idlegalsituation");
                                candidateEntity.idmilitaryservice = dtr.GetAsShort("idmilitaryservice");
                                candidateEntity.drivelicence = dtr.GetAsShort("drivelicence");
                                candidateEntity.nit = dtr.GetAsString("nit");
                                candidateEntity.ididentificationtype = dtr.GetAsShort("ididentificationtype");
                                candidateEntity.disability = dtr.GetAsShort("disability");
                                candidateEntity.experienceyears = dtr.GetAsShort("experienceyears");
                                candidateEntity.photo = dtr.GetAsString("photo");
                                candidateEntity.car = dtr.GetAsShort("car");
                                candidateEntity.idprivacylevel = dtr.GetAsShort("idprivacylevel");
                                candidateEntity.idemploymentstatus = dtr.GetAsShort("idemploymentstatus");
                                candidateEntity.minimumsalary = dtr.GetAsInt("minimumsalary");
                                candidateEntity.wishedsalary = dtr.GetAsInt("wishedsalary");
                                candidateEntity.salarydefault = dtr.GetAsString("salarydefault");
                                candidateEntity.residencechange = dtr.GetAsShort("residencechange");
                                candidateEntity.travel = dtr.GetAsShort("travel");
                                candidateEntity.wishedjob = dtr.GetAsString("wishedjob");
                                candidateEntity.createdon = dtr.GetAsDateTime("createdon");
                                candidateEntity.createdby = dtr.GetAsInt("createdby");
                                candidateEntity.updatedon = dtr.GetAsDateTime("updatedon");
                                candidateEntity.deletedon = dtr.GetAsDateTime("deletedon");
                                candidateEntity.idstatus = dtr.GetAsShort("idstatus");
                                candidateEntity.client_ip_add = dtr.GetAsString("client_ip_add");
                                candidateEntity.client_ip_mod = dtr.GetAsString("client_ip_mod");
                                candidateEntity.idCargo = dtr.GetAsInt("idcargo");
                                candidateEntity.idrace = dtr.GetAsShort("idrace");
                                candidateEntity.skypeName = dtr.GetAsString("skype_name");
                                candidateEntity.ProductId = dtr.GetAsInt("product_id");
                                candidateEntity.CodePresentationVideo = dtr.GetAsString("code_presentation_video");
                                candidateEntity.Phone1VerificationStatus = (dtr.GetAsInt("phone1_verification_status") == (int)PhoneVerificationStatusEnum.Verified);
                                candidateEntity.Phone2VerificationStatus = (dtr.GetAsInt("phone2_verification_status") == (int)PhoneVerificationStatusEnum.Verified);
                                candidateEntity.TestCompetencesTalentViewVisible = dtr.GetAsInt("test_competences_talent_view_visible") == 1;
                                candidateEntity.TestCompetences = dtr.GetAsInt("competence_test") == (int)CompetenceTestStatusEnum.Realized;
                                candidateEntity.TalentView3D = dtr.GetAsInt("talent_view") == (int)CompetenceTestStatusEnum.Realized;
                            }
                        }
                        return candidateEntity;
                    }
                }
            }
            catch (Exception ex)
            {
                Trace.TraceInformation("CandidateRecoverAndPersist - GetCandidateById", ex);
                _exceptionPublisherService.Publish(ex, "CandidateRecoverAndPersist", "GetCandidateById", false, new Dictionary<string, string>() { { "CandidateId", Id.ToString() } }, idPortal);
                return new CandidateEntity();
            }
        }

        public List<CandidateCategoryEntity> GetCategoriesByIdCandidate(int idCandidate, short idPortal)
        {
            List<CandidateCategoryEntity> list = new List<CandidateCategoryEntity>();

            try
            {
                using (var conex = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, idPortal)))
                {
                    using (var command = new MySqlCommand("gl_candidatebycategory_select_by_candidate", conex))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.Add(new MySqlParameter("_idcandidate", idCandidate));

                        conex.Open();

                        using (var dtr = command.ExecuteReader())
                        {
                            while (dtr != null && dtr.Read())
                            {
                                list.Add(
                                 new CandidateCategoryEntity()
                                 {
                                     IdCategory = dtr.GetAsInt("idcategory"),
                                     IdCandidate = dtr.GetAsInt("idcandidate")
                                 });
                            }
                            return list;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Trace.TraceInformation("CandidateRecoverAndPersist - GetCategoriesByIdCandidate", ex);
                _exceptionPublisherService.Publish(ex, "CandidateRecoverAndPersist", "GetCategoriesByIdCandidate", false, null, idPortal);
                return list;
            }
        }

        public List<CandidateLocalizationEntity> GetLocalizationsByIdCandidate(int idCandidate, short idPortal)
        {
            List<CandidateLocalizationEntity> list = new List<CandidateLocalizationEntity>();

            try
            {
                using (var conex = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, idPortal)))
                {
                    using (var command = new MySqlCommand("sp_dtcandidatebylocalization_SelectByCandidate", conex))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.Add(new MySqlParameter("_idcandidate", idCandidate));

                        conex.Open();

                        using (var dtr = command.ExecuteReader())
                        {
                            while (dtr != null && dtr.Read())
                            {
                                list.Add(
                                 new CandidateLocalizationEntity()
                                 {
                                     IdLocalization = dtr.GetAsInt("idlocalization"),
                                     IdCandidate = dtr.GetAsInt("idcandidate")
                                 });
                            }
                            return list;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Trace.TraceInformation("CandidateRecoverAndPersist - GetLocalizationsByIdCandidate", ex);
                _exceptionPublisherService.Publish(ex, "CandidateRecoverAndPersist", "GetLocalizationsByIdCandidate", false, null, idPortal);
                return list;
            }
        }

        public short GetTestCompetencesVisible(int idCandidate, short portalId)
        {
            try
            {
                using (MySqlConnection conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
                {
                    using (MySqlCommand command = new MySqlCommand("dtcandidate_get_test_competences_visible", conn))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.Add(new MySqlParameter("_candidateId", idCandidate));
                        conn.Open();
                        return command.ExecuteScalar().ToShort();
                    }
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CandidateRecoverAndPersist - GetTestCompetencesVisible: ex: {ex.ToString()}");
                _exceptionPublisherService.Publish(ex, "CandidateRecoverAndPersist", "GetTestCompetencesVisible");
            }
            return (short)CompetenceTestVisibilityEnum.NoRealize;
        }

        public CandidateFileEntity GetPrincipalCandidateFile(int candidateId, short portalId)
        {
            var candidateFile = new CandidateFileEntity();
            try
            {
                using (var conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
                {
                    using (var command = new MySqlCommand("GetPrincipalCandidateFile", conn))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.AddWithValue("_idCandidate", candidateId);
                        command.Parameters.AddWithValue("_idPortal", portalId);

                        conn.Open();
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader != null && reader.Read())
                            {
                                candidateFile.Id = reader.GetAsInt("id");
                                candidateFile.UserId = reader.GetAsInt("user_id");
                                candidateFile.CandidateId = reader.GetAsInt("candidate_id");
                                candidateFile.FileName = reader.GetAsString("filename");
                                candidateFile.DateAdd = reader.GetAsDateTime("date_add");
                                candidateFile.DateUpd = reader.GetAsDateTime("date_upd");
                                candidateFile.DateDel = reader.GetAsDateTime("date_del");
                                candidateFile.StatusId = reader.GetAsShort("status_id");
                                candidateFile.IsPrincipal = reader.GetAsShort("isprincipal");
                                candidateFile.DocTypeId = reader.GetAsInt("doc_type_id");
                                candidateFile.ExtensionId = reader.GetAsShort("extension_id");
                                candidateFile.PortalId = reader.GetAsShort("portal_id");
                                candidateFile.Path = reader.GetAsString("path");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {

                Trace.TraceError($"CandidateRecoverAndPersist - GetPrincipalCandidateFile: ex: {ex.ToString()}");
                _exceptionPublisherService.Publish(ex, "CandidateRecoverAndPersist", "GetPrincipalCandidateFile");
            }

            return candidateFile;
        }


        public CandidateFileEntity GetCandidateFileById(int idFile, short portalId)
        {
            var candidateFile = new CandidateFileEntity();
            try
            {
                using (var conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
                {
                    using (var command = new MySqlCommand("GetCandidateFileById", conn))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.AddWithValue("_id", idFile);
                        command.Parameters.AddWithValue("_idPortal", portalId);

                        conn.Open();
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader != null && reader.Read())
                            {
                                candidateFile.Id = reader.GetAsInt("id");
                                candidateFile.UserId = reader.GetAsInt("user_id");
                                candidateFile.CandidateId = reader.GetAsInt("candidate_id");
                                candidateFile.FileName = reader.GetAsString("filename");
                                candidateFile.DateAdd = reader.GetAsDateTime("date_add");
                                candidateFile.DateUpd = reader.GetAsDateTime("date_upd");
                                candidateFile.DateDel = reader.GetAsDateTime("date_del");
                                candidateFile.StatusId = reader.GetAsShort("status_id");
                                candidateFile.IsPrincipal = reader.GetAsShort("isprincipal");
                                candidateFile.DocTypeId = reader.GetAsInt("doc_type_id");
                                candidateFile.ExtensionId = reader.GetAsShort("extension_id");
                                candidateFile.PortalId = reader.GetAsShort("portal_id");
                                candidateFile.Path = reader.GetAsString("path");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {

                Trace.TraceError($"CandidateRecoverAndPersist - GetCandidateFileById: ex: {ex.ToString()}");
                _exceptionPublisherService.Publish(ex, "CandidateRecoverAndPersist", "GetCandidateFileById");
            }

            return candidateFile;
        }

        private short GetAgeFromDateBirth(DateTime dateBirth)
        {
            if (dateBirth == DateTime.MinValue) return 0;
            var year = DateTime.Now.Year - dateBirth.Year;
            var month = DateTime.Now.Month - dateBirth.Month;
            if (month < 0) year--;
            else if (month == 0)
            {
                var day = DateTime.Now.Day - dateBirth.Day;
                if (day < 0) year--;
            }
            return Convert.ToInt16(year);
        }

        public long GetIdUserCandidateById(int candidateId, short portalId)
        {
            long idUser = 0;
            try
            {             
                using (MySqlConnection conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
                {
                    using (MySqlCommand command = new MySqlCommand("dtcandidate_get_iduser_by_idcandidate", conn))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.Add(new MySqlParameter("_idcandidate", candidateId));
                        command.Parameters.Add(new MySqlParameter("_idportal", portalId));
                        conn.Open();
                        var result = command.ExecuteScalar();

                        if(result != null) { 

                            long.TryParse(result.ToString(), out idUser);                         
                        }                      
                    }
                }

                return idUser;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CandidateRecoverAndPersist - GetTestCompetencesVisible: ex: {ex.ToString()}");
                _exceptionPublisherService.Publish(ex, "CandidateRecoverAndPersist", "GetTestCompetencesVisible");
                return 0;
            }          
        }
    }
}
