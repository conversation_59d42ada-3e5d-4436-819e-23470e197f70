using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Redarbor.AE.Procs.BrokenOperationsNotifier.Application.Abstractions;
using Redarbor.AE.Procs.BrokenOperationsNotifier.Application.Models;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Redarbor.AE.Procs.BrokenOperationsNotifier
{
    public class Worker : BackgroundService
    {
        private readonly ILogger<Worker> _logger;

        private readonly ApplicationSettingsData _appData;

        private readonly IOperationsService _operationsService;

        public Worker(ILogger<Worker> logger, IOperationsService operationsService, ApplicationSettingsData appData)
        {
            _logger = logger;
            _appData = appData;
            _operationsService = operationsService;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                _logger.LogInformation("Worker running at: {time}", DateTimeOffset.Now);
                await _operationsService.ProcessOperationsAsync(stoppingToken);
                _logger.LogInformation("Sleeping at: {time}", DateTimeOffset.Now);
                await Task.Delay(_appData.SecondsToSleepAfterEachExecution * 1000, stoppingToken);
            }
        }
    }
}