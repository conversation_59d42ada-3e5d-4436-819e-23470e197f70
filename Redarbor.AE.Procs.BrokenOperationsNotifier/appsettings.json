{"Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": "Information", "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "File", "Args": {"path": "logs/logfile.log"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}, "ConnectionStrings": {"CtComun": "server=common-srv.ct.local;Persist Security Info=True;database=ct_comun;Uid=usr_ae;Pwd=************;port=3306;SslMode=none;", "CtAnalytics": "server=analytics-srv.ct.local;UserId=usr_prc;PersistSecurityInfo=False;database=ct_analytics;Pwd=5FingerDeathPunch!;port=3306;ConnectionTimeout=15;SSLMode=none"}, "AppSettings": {"AppId": 530, "ApiExceptionsUrl": "http://api-tools-audit.ct.local", "ThrowExceptions": true, "Title": "Proc_BrokenOperationNotifier", "SecondsToSleepAfterEachExecution": 1800, "RabbitMQEndpoint": "http://api-queues.ct.local", "RabbitQueueId": 106, "PaymentsApi": "http://api-payments.ct.local/"}, "PortalProviderIntervals": {"Default": {"WorldPay": {"From": {"Amount": -48, "Unit": "Hours"}, "To": {"Amount": -24, "Unit": "Hours"}}, "PayU": {"From": {"Amount": -5, "Unit": "Days"}, "To": {"Amount": -4, "Unit": "Days"}}}, "Portals": {"ComputrabajoArgentina": {"WorldPay": {"From": {"Amount": -48, "Unit": "Hours"}, "To": {"Amount": -24, "Unit": "Hours"}}, "PayU": {"From": {"Amount": -48, "Unit": "Hours"}, "To": {"Amount": -24, "Unit": "Hours"}}}}}}