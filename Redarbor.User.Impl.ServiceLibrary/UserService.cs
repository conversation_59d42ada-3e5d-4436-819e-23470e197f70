using Redarbor.AppSettings.Contracts.ServiceLibrary;
using Redarbor.Common.Entities.Configuration;
using Redarbor.Core.Cache.Contracts.ServiceLibrary;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Core.Contracts.ServiceLibrary;
using Redarbor.Core.Resolver.Contracts.ServiceLibrary;
using Redarbor.Core.Shared.Contracts.ServiceLibrary;
using Redarbor.Core.Stack.Contracts.ServiceLibrary;
using Redarbor.Dictionaries.Consumer.Enums;
using Redarbor.Extensions.Library.DI;
using Redarbor.Jobads.User.Api.Consumer.Model;
using Redarbor.Master.Contracts.ServiceLibrary;
using Redarbor.Master.Contracts.ServiceLibrary.Constants;
using Redarbor.Master.Entities.Enums;
using Redarbor.Master.Entities.Stack;
using Redarbor.Master.Entities.Users;
using Redarbor.Products.Contracts.ServiceLibrary;
using Redarbor.User.Contracts.ServiceLibrary;
using Redarbor.User.Contracts.ServiceLibrary.DTO;
using Redarbor.User.Impl.ServiceLibrary.Mappers;
using Redarbor.User.Impl.ServiceLibrary.Model;
using Redarbor.Users.Library.DomainServiceContracts;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;

namespace Redarbor.User.Impl.ServiceLibrary
{
    [RegisterStatefulService]
    public class UserService : IUserService
    {
        readonly TimeSpan _timeInCache = new TimeSpan(0, 60, 0);
        readonly TimeSpan _timeInCacheQuarter = new TimeSpan(0, 15, 0);

        private readonly IUserRecoverAndPersist _userRecoverAndPersist;
        private readonly IDeviceTokenRecoverAndPersist _deviceTokenRecoverAndPersist;
        private readonly IEncryptionService _encryptionService;
        private readonly ITempCache _tempCache;
        private readonly IHashService _hashService;
        private readonly IPortalResolverService _portalResolverService;
        private readonly ICompanyProductService _companyProductService;
        private readonly IStackService _stackService;
        private readonly ICookieService _cookieService;
        private readonly IExceptionPublisherService _exceptionPublisherService;
        private readonly IClientIpAddressResolverService _clientIpAddressResolverService;
        private readonly IDictionaryService _dictionaryService;
        private readonly IEnumToolsService _enumToolsService;
        private readonly IPortalConfigurationService _portalConfigurationService;
        private readonly IAppSettingsService _appSettingsService;
        private readonly IApiService _apiService;

        public UserService(IUserRecoverAndPersist userRecoverAndPersist,
            IDeviceTokenRecoverAndPersist deviceTokenRecoverAndPersist,
            IEncryptionService encryptionService,
            IHashService hashService,
            ITempCache tempCache,
            IStackService stackService,
            IPortalResolverService portalResolverService,
            ICompanyProductService companyProductService,
            ICookieService cookieService,
            IClientIpAddressResolverService clientIpAddressResolverService,
            IDictionaryService dictionaryService,
            IExceptionPublisherService exceptionPublisherService,
            IEnumToolsService enumToolsService,
            IPortalConfigurationService portalConfigurationService,
            IAppSettingsService appSettingsService,
            IApiService apiService)
        {
            _userRecoverAndPersist = userRecoverAndPersist;
            _deviceTokenRecoverAndPersist = deviceTokenRecoverAndPersist;
            _encryptionService = encryptionService;
            _tempCache = tempCache;
            _hashService = hashService;
            _stackService = stackService;
            _portalResolverService = portalResolverService;
            _companyProductService = companyProductService;
            _cookieService = cookieService;
            _clientIpAddressResolverService = clientIpAddressResolverService;
            _dictionaryService = dictionaryService;
            _exceptionPublisherService = exceptionPublisherService;

            _enumToolsService = enumToolsService;
            _portalConfigurationService = portalConfigurationService;
            _appSettingsService = appSettingsService;
            _apiService = apiService;
        }

        public UserEntity Get(long userId, bool fillExtraInfo, short idPortal)
        {
            if (idPortal <= 0 || userId <= 0)
                return new UserEntity();

            var userEntity = GetUserEntity(userId, idPortal);

            if (fillExtraInfo)
                FillExtraInfoUserEntity(userEntity, idPortal);

            return userEntity;
        }

        private UserEntity GetUserEntity(long userId, short idPortal)
        {
            var userEntity = new UserEntity();

            var portalConfig = _portalConfigurationService.GetPortalConfiguration(idPortal);
            var useUserWebAPI = _appSettingsService.Get(portalConfig?.EnvironmentUserApiKeys?.GetKey ?? "", portalConfig.idapp, portalConfig.PortalId, false);

            if (useUserWebAPI)
            {
                var resultApi = _apiService.Get<UserApiModel>($"{portalConfig.UrlWebApiUserBBDD}{Jobads.User.Api.Consumer.Model.ApiEndpoints.GetUserByIdGet.Replace("{version}", "1").TrimEnd('/')}?IdPortal={idPortal}&Id={userId}&idOrigin={(short)ApplicationEnum.AreaEmpresa}", null);

                if (resultApi != null && resultApi.user != null)
                {
                    userEntity = resultApi.user.ToUserEntity();
                }
            }
            else
            {
                userEntity = _userRecoverAndPersist.Get(userId, idPortal);
            }

            return userEntity;
        }

        public UserEntity GetByUserName(string userName, short portalId, short userTypeId)
        {
            var userEntity = _userRecoverAndPersist.GetByUserName(userName, portalId, userTypeId);
            FillExtraInfoUserEntity(userEntity, portalId);
            return userEntity;
        }

        public UserEntity GetByEmail(string email, short portalId)
        {
            var userEntity = _userRecoverAndPersist.GetByEmail(email, portalId);
            FillExtraInfoUserEntity(userEntity, portalId);
            return userEntity;
        }

        public List<UserEntity> GetManagersByIdCompany(int idCompany, short idPortal)
        {
            return GetManagersByIdCompany(idCompany, idPortal, true);
        }

        public List<UserEntity> GetManagersByIdCompany(int idCompany, short idPortal, bool useCache)
        {
            var listGestores = new List<UserEntity>();
            string cacheKey = this.GetCacheKeyByGestoresList(idCompany);

            if (useCache)
            {
                listGestores = _tempCache.Get<List<UserEntity>>(cacheKey);
                if (listGestores != null)
                {
                    return listGestores;
                }
            }

            var portalConfig = _portalConfigurationService.GetPortalConfiguration(idPortal);
            var useUserWebAPI = _appSettingsService.Get(portalConfig?.EnvironmentUserApiKeys?.GetManagersByIdCompanyKey ?? "", portalConfig.idapp, portalConfig.PortalId, false);

            listGestores = GetManagers(idCompany, idPortal, listGestores, portalConfig, useUserWebAPI);

            if (listGestores != null)
            {
                Dictionary<string, string> dicUserRoles = _dictionaryService.GetDictionary(DictionaryEnum.USER_ROLES, _portalResolverService.ResolvePortalId());
                listGestores.ForEach(u => FillExtraInfoUserEntity(u, dicUserRoles));
                SaveCacheListManagers(useCache, listGestores, cacheKey);
                return listGestores;
            }

            return new List<UserEntity>();
        }

        private void SaveCacheListManagers(bool useCache, List<UserEntity> listGestores, string cacheKey)
        {
            if (useCache)
            {
                _tempCache.Add(cacheKey, listGestores, _timeInCache);
            }
        }

        private List<UserEntity> GetManagers(int idCompany, short idPortal, List<UserEntity> listGestores, PortalConfig portalConfig, bool useUserWebAPI)
        {
            if (useUserWebAPI)
            {
                var resultApi = _apiService.Get<UsersApiModel>($"{portalConfig.UrlWebApiUserBBDD}{ApiEndpoints.GetUsersByCompanyExcludingStatus.Replace("{version}", "1").TrimEnd('/')}?IdPortal={idPortal}&IdStatus=4&IdCompany={idCompany}&idOrigin={(short)ApplicationEnum.AreaEmpresa}", null);

                if (resultApi == null || !resultApi.isSuccess)
                    return null;

                if (resultApi.users != null && resultApi.users.Any())
                {
                    return resultApi.users.ToUserEntityList();
                }
            }
            else
            {
                return _userRecoverAndPersist.GetManagersByIdCompany(idCompany);
            }

            return new List<UserEntity>();
        }

        public long GetUserIdByEmail(string email, short portalId, UserTypeEnum userTypeEnum)
        {
            long idUser = 0;

            if (!string.IsNullOrWhiteSpace(email) && portalId > 0)
            {
                var portalConfig = _portalConfigurationService.GetPortalConfiguration(portalId);
                var useUserWebAPI = _appSettingsService.Get(portalConfig?.EnvironmentUserApiKeys?.GetUserIdByEmailKey ?? "", portalConfig.idapp, portalConfig.PortalId, false);

                if (useUserWebAPI)
                {
                    var resultApi = _apiService.Get<GetUserIdActiveByEmailResponse>($"{portalConfig.UrlWebApiUserBBDD}{ApiEndpoints.GetUserIdActiveByEmail.Replace("{version}", "1").TrimEnd('/')}?IdPortal={portalId}&Email={email}&IdUserType={(short)userTypeEnum}&idOrigin={(short)ApplicationEnum.AreaEmpresa}", null);

                    if (resultApi != null && resultApi.IsSuccess)
                        idUser = resultApi.UserId;
                }
                else
                {
                    idUser = _userRecoverAndPersist.GetUserIdByEmail(email, portalId, userTypeEnum);
                }
            }

            return idUser;
        }

        public string GetContactNameByUserId(long idUser, short portalId)
        {
            return _userRecoverAndPersist.GetContactNameByUserId(idUser, portalId);
        }

        public bool UpdateUserName(int userId, string userName, UserTypeEnum userType, PortalConfig portalConfig)
        {
            if (_userRecoverAndPersist.UpdateUserName(userId, userName))
            {
                var cookieName = CookieType.USER_COMPANY;

                try
                {
                    _cookieService.Delete(cookieName, portalConfig.cookie_domain);
                }
                catch (Exception ex)
                {
                    Trace.TraceError($"UserService - UpdateUserName: ex: {ex.ToString()}");
                    _exceptionPublisherService.PublishInCommon(ex, "UserService", "UpdateUserName");
                    return false;
                }

            }
            return false;
        }

        public DateTime SetLastLogin(long userId, short portalId)
        {
            return _userRecoverAndPersist.SetLastLogin(userId, portalId);
        }

        public bool ExistsMail(string mail, short portalId)
        {
            return _userRecoverAndPersist.ExistsEmail(mail, portalId);
        }

        public int CountUsersByEmail(string email, short portalId)
        {
            if (email == string.Empty)
                return 1;

            return _userRecoverAndPersist.CountByEmail(email, portalId);
        }

        public bool IsLogged(short portalId, long userId, string sessionId)
        {
            return _userRecoverAndPersist.IsLogged(portalId, userId, sessionId);
        }

        public bool SetLogged(short portalId, long userId, string sessionId)
        {
            return _userRecoverAndPersist.SetLogged(portalId, userId, sessionId);
        }

        public bool RemoveLogged(short portalId, long userId, string sessionId)
        {
            return _userRecoverAndPersist.RemoveLogged(portalId, userId, sessionId);
        }

        public int Add(UserEntity userEntity)
        {
            if (!IsUserValidToAdd(userEntity))
                return 0;

            RemoveCacheManagerByCompanyId(userEntity.CompanyId);
            RemoveCacheAllByCompanyId(userEntity.CompanyId);

            userEntity.Password = _hashService.Encrypt(userEntity.Password);

            var addId = _userRecoverAndPersist.Add(userEntity);

            if (addId > 0)
                RemoveCacheManagerByCompanyId(userEntity.CompanyId);

            return addId;
        }

        public int AddUserGdpr(int userId, string email, short portalId, AcceptationTypeLegalEnum acceptationTypeLegalEnum, RegisterTypeLegalEnum registerTypeLegalEnum)
        {
            var userGdprEntity = new UserGdprEntity()
            {
                AcceptationType = (short)acceptationTypeLegalEnum,
                ActionType = (short)ActionTypeLegalEnum.Concession,
                PortalId = portalId,
                RegisterType = (short)registerTypeLegalEnum,
                ClientIp = _clientIpAddressResolverService.GetIpAddress(),
                UserId = userId,
                Email = email
            };

            return _userRecoverAndPersist.AddUserGdpr(userGdprEntity);
        }

        public bool Update(UserEntity userEntity, bool actualizePassword = true)
        {
            if (!IsUserValidToUpdate(userEntity))
                return false;

            if (actualizePassword) userEntity.Password = _hashService.Encrypt(userEntity.Password);

            var updateOK = _userRecoverAndPersist.Update(userEntity);

            if (updateOK)
            {
                RemoveCacheManagerByCompanyId(userEntity.CompanyId);
                RemoveCacheAllByCompanyId(userEntity.CompanyId);
            }
            return updateOK;
        }

        public bool DeleteUser(int idUserDelete, int idCompany)
        {
            if (idUserDelete <= 0) return false;
            var idPortal = _portalResolverService.ResolvePortalId();

            var deleteOk = !CheckContentUser(idUserDelete, idCompany, idPortal)
                           && _userRecoverAndPersist.DeleteUser(idUserDelete, idPortal, idCompany);

            if (deleteOk)
            {
                RemoveCacheManagerByCompanyId(idCompany);
                RemoveCacheAllByCompanyId(idCompany);
                _userRecoverAndPersist.DeleteCompanyProductsFeaturesUsers(idUserDelete, idCompany, idPortal);
            }

            return deleteOk;
        }

        public bool DeleteUser(int idUserDelete, int idUserToAssingContent, int idCompany)
        {
            MoveFunctionsToOtherUser(idUserDelete, idUserToAssingContent, idCompany);

            var idPortal = _portalResolverService.ResolvePortalId();
            var deleteOk = _userRecoverAndPersist.DeleteUser(idUserDelete, idPortal, idCompany);

            if (deleteOk)
            {
                RemoveCacheManagerByCompanyId(idCompany);
                RemoveCacheAllByCompanyId(idCompany);
                _userRecoverAndPersist.DeleteCompanyProductsFeaturesUsers(idUserDelete, idCompany, idPortal);
            }

            return deleteOk;
        }

        public void DeleteCompanyProductsFeaturesUsers(int idUserDelete, int idCompany, short idPortal)
        {
            _userRecoverAndPersist.DeleteCompanyProductsFeaturesUsers(idUserDelete, idCompany, idPortal);
        }

        public void MoveFunctionsToOtherUser(int idUser, int idUserToAssingContent, int idCompany)
        {
            if (idUser <= 0 || idUserToAssingContent <= 0 || idUser == idUserToAssingContent) return;
            var idPortal = _portalResolverService.ResolvePortalId();

            if (CheckContentUser(idUser, idCompany, idPortal))
            {
                if (_userRecoverAndPersist.CheckMatchDownloaderUser(idUser))
                    _userRecoverAndPersist.AssignMatchDownloaderUser(idUser, idUserToAssingContent);

                if (_userRecoverAndPersist.CheckMessagesUser(idUser))
                    _userRecoverAndPersist.AssignMessagesToUser(idUser, idUserToAssingContent, idPortal);

                if (_userRecoverAndPersist.CheckOffersUser(idUser, idPortal))
                {
                    AssignOffersToUserAndAddStackPublishOffers(idUser, idUserToAssingContent, idPortal);
                }

                if (_userRecoverAndPersist.CheckFiltersUser(idUser))
                    _userRecoverAndPersist.AssignFiltersToUser(idUser, idUserToAssingContent, idPortal);

                if (_companyProductService.HasUserCredits(idUser, idCompany, idPortal))
                    _companyProductService.ReAssignCredits(idUser, idCompany, idPortal, idUserToAssingContent);
            }
        }

        public bool CheckContentUser(int idUser, int idCompany, int idPortal)
        {
            if (idUser <= 0 || idCompany <= 0 || idPortal <= 0) return false;
            //TODO unique query, if posible
            return _userRecoverAndPersist.CheckOffersUser(idUser, (short)idPortal) || _userRecoverAndPersist.CheckMatchDownloaderUser(idUser)
                                                                  || _userRecoverAndPersist.CheckMessagesUser(idUser) || _userRecoverAndPersist.CheckFiltersUser(idUser)
                                                                  || _companyProductService.HasUserCredits(idUser, idCompany, idPortal);
        }

        public int GetTotalManagersByIdCompany(int idCompany)
        {
            if (idCompany == 0)
                return 0;

            return _userRecoverAndPersist.GetTotalManagersByIdCompany(idCompany);
        }

        public short GetRoleIdByUserId(long idUser, short idPortal)
        {
            if (idUser <= 0)
                return 0;

            return _userRecoverAndPersist.GetRoleIdByUserId(idUser, idPortal);
        }

        public IList<UserDeviceTokenEntity> GetDeviceTokens(int userId, int portalId)
        {
            if (userId <= 0 || portalId <= 0)
                return new List<UserDeviceTokenEntity>();

            return _deviceTokenRecoverAndPersist.GetDeviceTokens(userId, portalId);
        }

        public string GetEmailByUserId(long idUser, short idPortal)
        {
            string result = "";
            var portalConfig = _portalConfigurationService.GetPortalConfiguration(idPortal);
            var useUserWebAPI = _appSettingsService.Get(portalConfig?.EnvironmentUserApiKeys?.GetEmailByUserIdKey ?? "", portalConfig.idapp, portalConfig.PortalId, false);

            if (useUserWebAPI)
            {
                var resultApi = _apiService.Get<GetUserEmailByIdResponse>($"{portalConfig.UrlWebApiUserBBDD}{ApiEndpoints.GetUserEmailById.Replace("{version}", "1").TrimEnd('/')}?IdPortal={idPortal}&Id={idUser}&idOrigin={(short)ApplicationEnum.AreaEmpresa}", null);

                if (resultApi != null && resultApi.IsSuccess)
                    result = resultApi.Email ?? "";
            }
            else
            {
                result = _userRecoverAndPersist.GetEmailByUserId(idUser, idPortal);
            }

            return result;
        }

        public List<UserEntity> GetManagersByDescendentRol(int companyId, UserRoleEnum userRole, short portalId)
        {
            if (companyId < 1)
            {
                return new List<UserEntity>();
            }

            var userRoles = GetDescentAndMeUserRolesByUserRole(userRole);
            var users = GetManagersByIdCompany(companyId, portalId);

            return users.Where(g => userRoles.Contains((UserRoleEnum)g.RoleId)).ToList();
        }

        public List<UserRoleEnum> GetDescentAndMeUserRolesByUserRole(UserRoleEnum userRole)
        {
            List<UserRoleEnum> listRoles = new List<UserRoleEnum>();

            switch (userRole)
            {
                case UserRoleEnum.ADMINISTRADOR_PRINCIPAL:
                    listRoles.Add(UserRoleEnum.ADMINISTRADOR);
                    listRoles.Add(UserRoleEnum.COMPUADVISOR);
                    listRoles.Add(UserRoleEnum.SUPERVISOR);
                    listRoles.Add(UserRoleEnum.GESTOR_SENIOR);
                    listRoles.Add(UserRoleEnum.GESTOR);
                    listRoles.Add(UserRoleEnum.ENTREVISTADOR);
                    break;
                case UserRoleEnum.ADMINISTRADOR:
                    listRoles.Add(UserRoleEnum.COMPUADVISOR);
                    listRoles.Add(UserRoleEnum.SUPERVISOR);
                    listRoles.Add(UserRoleEnum.GESTOR_SENIOR);
                    listRoles.Add(UserRoleEnum.GESTOR);
                    listRoles.Add(UserRoleEnum.ENTREVISTADOR);
                    break;
                case UserRoleEnum.SUPERVISOR:
                    listRoles.Add(UserRoleEnum.SUPERVISOR);
                    listRoles.Add(UserRoleEnum.GESTOR_SENIOR);
                    listRoles.Add(UserRoleEnum.GESTOR);
                    listRoles.Add(UserRoleEnum.ENTREVISTADOR);
                    break;
                case UserRoleEnum.GESTOR_SENIOR:
                    listRoles.Add(UserRoleEnum.GESTOR_SENIOR);
                    listRoles.Add(UserRoleEnum.GESTOR);
                    listRoles.Add(UserRoleEnum.ENTREVISTADOR);
                    break;
                case UserRoleEnum.SELECCIONADOR:
                    listRoles.Add(UserRoleEnum.GESTOR_SENIOR);
                    listRoles.Add(UserRoleEnum.GESTOR);
                    break;
            }

            return listRoles;
        }

        public void RemoveCacheManagerByCompanyId(int companyId)
        {
            var cacheKey = GetCacheKeyByGestoresList(companyId);

            _tempCache.Remove(cacheKey);
        }

        public void RemoveCacheAllByCompanyId(int companyId)
        {
            var cacheKey = GetCacheKeyByAllList(companyId);

            _tempCache.Remove(cacheKey);
        }

        public bool IsUserValidToUpdate(UserEntity userEntity)
        {
            return string.IsNullOrWhiteSpace(userEntity.ContactName) || string.IsNullOrWhiteSpace(userEntity.Email) || string.IsNullOrWhiteSpace(userEntity.Password)
                   || userEntity.RoleId == 0 || userEntity.PortalId == 0 || userEntity.Id == 0 || userEntity.CompanyId == 0 ? false : true;
        }

        public bool NeedAddToStack(UserEntity userEntity)
        {
            return GetRoleIdByUserId(userEntity.Id, userEntity.PortalId) != userEntity.RoleId;
        }

        public void PublicOffersToStack(List<int> idOffers, short idPortal)
        {
            var stackEntities = new List<StackEntity>();
            if (idOffers.Any() && idPortal > 0)
            {
                idOffers.ForEach(oi =>
                {
                    if (oi > 0)
                    {
                        stackEntities.Add(new StackEntity(idPortal)
                        {
                            ObjectId = oi,
                            TypeId = (int)StackObjectTypeEnum.Offer
                        });
                    }
                });
                if (stackEntities.Any())
                    _stackService.Save(stackEntities);
            }
        }

        public List<int> FindAllIdOffersByUser(int idUser, short idPortal)
        {
            return _userRecoverAndPersist.FindAllIdOffersByUser(idUser, idPortal);
        }

        public List<int> FindAllIdOffersByUserBulk(int idUser, short idPortal, int index, int blocksize)
        {
            return _userRecoverAndPersist.FindAllIdOffersByUserBulk(idUser, idPortal, index, blocksize);
        }

        public bool MarkMailAsVerified(MarkMailDTO markMail)
        {
            return _userRecoverAndPersist.MarkMailAsVerified(markMail.userId, markMail.portalId, markMail.verificationEnum);
        }

        private bool IsUserValidToAdd(UserEntity userEntity)
        {
            return (string.IsNullOrWhiteSpace(userEntity.ContactName) || string.IsNullOrWhiteSpace(userEntity.Email) || string.IsNullOrWhiteSpace(userEntity.Password)
               || userEntity.RoleId == 0 || userEntity.PortalId == 0 || string.IsNullOrWhiteSpace(userEntity.Username) || userEntity.CompanyId == 0)
               ? false : true;
        }

        private void FillExtraInfoUserEntity(UserEntity userEntity, short portalId = 0)
        {
            if (userEntity != null && userEntity.Id > 0)
            {
                userEntity.IdEncrypt = _encryptionService.Encrypt(userEntity.Id.ToString());
                userEntity.RoleText = _dictionaryService.GetDictionaryValue(DictionaryEnum.USER_ROLES, userEntity.RoleId.ToString(), userEntity.PortalId);
            }
        }

        private void FillExtraInfoUserEntity(UserEntity userEntity, Dictionary<string, string> dicUserRoles)
        {
            if (userEntity != null)
            {
                userEntity.IdEncrypt = _encryptionService.Encrypt(userEntity.Id.ToString());
                userEntity.RoleText = dicUserRoles.TryGetValue(userEntity.RoleId.ToString(), out string value) ? value : string.Empty;
            }
        }

        public void AssignOffersToUserAndAddStackPublishOffers(int idUser, int idUserToAssingContent, short idPortal)
        {
            var userRole = GetRoleIdByUserId(idUserToAssingContent, idPortal);
            if (userRole != 0)
            {
                var offersByUser = FindAllIdOffersByUser(idUser, idPortal);
                if (offersByUser.Any())
                {
                    if (_userRecoverAndPersist.AssignOffersToUser(idUser, idUserToAssingContent, userRole, idPortal))
                        PublicOffersToStack(offersByUser, idPortal);
                }
            }
        }

        private string GetCacheKeyByGestoresList(int companyId)
        {
            return $"SUPER_LOCAL_CACHE_GESTORES_{companyId}";
        }

        private string GetCacheKeyByAllList(int companyId)
        {
            return $"SUPER_LOCAL_CACHE_ALL_{companyId}";
        }

        public List<long> GetUserMisuseList(int IdCompany, short PortalId)
        {
            string cacheKey = $"SUPER_LOCAL_CACHE_USER_MISUSE_LIST_{IdCompany}_{PortalId}";

            List<long> misuseList = _tempCache.Get<List<long>>(cacheKey);
            if (misuseList != null && misuseList.Any())
            {
                return misuseList;
            }

            misuseList = _userRecoverAndPersist.GetUserMisuseList(IdCompany, PortalId);

            _tempCache.Add(cacheKey, misuseList, _timeInCacheQuarter);

            return misuseList;
        }
    }
}