using System.Reflection;
using System.Runtime.InteropServices;

[assembly: AssemblyTitle("Redarbor.Products.UnitTest.ServiceLibrary")]
[assembly: AssemblyDescription("")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("")]
[assembly: AssemblyProduct("Redarbor.Products.UnitTest.ServiceLibrary")]
[assembly: AssemblyCopyright("Copyright ©  2018")]
[assembly: AssemblyTrademark("")]
[assembly: AssemblyCulture("")]

[assembly: ComVisible(false)]

[assembly: Guid("356be14a-d0ca-4764-ad4f-088989ba6528")]

// [assembly: AssemblyVersion("1.0.*")]
[assembly: AssemblyVersion("*******")]
[assembly: AssemblyFileVersion("*******")]
