using Microsoft.VisualStudio.TestTools.UnitTesting;
using Redarbor.Master.Entities.Enums;
using Redarbor.Products.UnitTest.ServiceLibrary.Enums;
using Redarbor.Products.UnitTest.ServiceLibrary.Factories;
using Redarbor.Products.UnitTest.ServiceLibrary.Factories.CompanyProductFactory.Context.Scenarios;

namespace Redarbor.Products.UnitTest.ServiceLibrary.CompanyProductServiceTests
{
    [TestClass]
    public class CompanyProductService_IsPaymentTests
    {
        static CompanyProductServiceScenario _mainContext;

        [TestInitialize]
        public void SetUp()
        {
            _mainContext = CompanyProductServiceContextFactory.Create(CompanyProductServiceContextEnum.Simple);
        }

        [TestMethod]
        public void When_Product_Id_Is_Zero_Returns_False()
        {
            var result = _mainContext.CompanyProductService.IsPayment(_mainContext.MainContext.CompanyProductExpected);

            Assert.IsFalse(result);
        }

        [TestMethod]
        [DataRow((short)ProductGroupsEnum.Freemium)]
        [DataRow((short)ProductGroupsEnum.Advertising)]
        [DataRow((short)ProductGroupsEnum.CompuAdvisor)]
        public void When_ProductGroup_Is_NOT_Payment_Returns_False(short groupId)
        {
            _mainContext.MainContext.CompanyProductExpected.GroupId = groupId;
            _mainContext.MainContext.CompanyProductExpected.Id = 123;

            var result = _mainContext.CompanyProductService.IsPayment(_mainContext.MainContext.CompanyProductExpected);

            Assert.IsFalse(result);
        }        

        [TestMethod]
        [DataRow((short)ProductGroupsEnum.Membership)]
        [DataRow((short)ProductGroupsEnum.Packs)]
        [DataRow((short)ProductGroupsEnum.Packs)]
        public void When_ProductGroup_Is_Payment_Returns_True(short groupId)
        {
            _mainContext.MainContext.CompanyProductExpected.GroupId = groupId;
            _mainContext.MainContext.CompanyProductExpected.Id = 123;

            var result = _mainContext.CompanyProductService.IsPayment(_mainContext.MainContext.CompanyProductExpected);

            Assert.IsTrue(result);
        }       

        [TestMethod]
        public void When_Product_Is_Welcome_Pack_Returns_False()
        {
            _mainContext.MainContext.CompanyProductExpected.GroupId = (short)ProductGroupsEnum.Packs;
            _mainContext.MainContext.CompanyProductExpected.Id = 123;
            _mainContext.MainContext.CompanyProductExpected.ProductId = (short)ProductEnum.WelcomePack5;

            var result = _mainContext.CompanyProductService.IsPayment(_mainContext.MainContext.CompanyProductExpected);

            Assert.IsFalse(result);
        }
    }
}
