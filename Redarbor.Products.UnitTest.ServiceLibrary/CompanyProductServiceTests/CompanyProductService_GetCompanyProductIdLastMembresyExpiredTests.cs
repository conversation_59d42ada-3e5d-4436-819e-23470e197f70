using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using Redarbor.Products.UnitTest.ServiceLibrary.Enums;
using Redarbor.Products.UnitTest.ServiceLibrary.Factories;
using Redarbor.Products.UnitTest.ServiceLibrary.Factories.CompanyProductFactory.Context.Scenarios;

namespace Redarbor.Products.UnitTest.ServiceLibrary.CompanyProductServiceTests
{
    [TestClass]
    public class CompanyProductService_GetCompanyProductIdLastMembresyExpiredTests
    {
        static CompanyProductServiceScenario _mainContext;

        [TestInitialize]
        public void SetUp()
        {
            _mainContext = CompanyProductServiceContextFactory.Create(CompanyProductServiceContextEnum.Simple);
        }

        [TestMethod]
        [DataRow(0,0)]
        [DataRow(-10,10)]
        public void When_companyId_is_0_or_lower_Return_0(int companyId, int portalId)
        {
            var result = _mainContext.CompanyProductService.GetCompanyProductIdLastMembresyExpired(companyId, (short)portalId);

            Assert.IsTrue(result == 0);
        }

        [TestMethod]
        [DataRow(0,0)]
        [DataRow(10,-10)]
        public void When_portalId_is_0_or_lower_Return_0(int companyId, int portalId)
        {
            var result = _mainContext.CompanyProductService.GetCompanyProductIdLastMembresyExpired(companyId, (short)portalId);

            Assert.IsTrue(result == 0);
        }

        [TestMethod]
        public void When_portalId_And_companyId_is_bigger_than_0_Return_A_Bigger_Number_Than_0()
        {
            _mainContext.MainContext.CompanyProductRecoverAndPersistMock.Setup(s => s.GetCompanyProductIdLastMembresyExpired(It.IsAny<int>(), It.IsAny<short>())).Returns(100);
            var result = _mainContext.CompanyProductService.GetCompanyProductIdLastMembresyExpired(10, 1);

            Assert.IsTrue(result > 0);
        }
    }
}
