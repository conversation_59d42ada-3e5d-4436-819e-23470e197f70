<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\MSTest.TestAdapter.2.2.10\build\net46\MSTest.TestAdapter.props" Condition="Exists('..\packages\MSTest.TestAdapter.2.2.10\build\net46\MSTest.TestAdapter.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{356BE14A-D0CA-4764-AD4F-088989BA6528}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Redarbor.Products.UnitTest.ServiceLibrary</RootNamespace>
    <AssemblyName>Redarbor.Products.UnitTest.ServiceLibrary</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <ProjectTypeGuids>{3AC096D0-A1C2-E12C-1390-A8335801FDAB};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">15.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
    <ReferencePath>$(ProgramFiles)\Common Files\microsoft shared\VSTT\$(VisualStudioVersion)\UITestExtensionPackages</ReferencePath>
    <IsCodedUITest>False</IsCodedUITest>
    <TestProjectType>UnitTest</TestProjectType>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x64\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\x64\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Castle.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=407dd0808d44fbdc, processorArchitecture=MSIL">
      <HintPath>..\packages\Castle.Core.4.4.1\lib\net45\Castle.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.VisualStudio.TestPlatform.TestFramework, Version=14.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\MSTest.TestFramework.2.2.10\lib\net45\Microsoft.VisualStudio.TestPlatform.TestFramework.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions, Version=14.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\MSTest.TestFramework.2.2.10\lib\net45\Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="Moq, Version=4.16.0.0, Culture=neutral, PublicKeyToken=69f491c39445e920, processorArchitecture=MSIL">
      <HintPath>..\packages\Moq.4.16.0\lib\net45\Moq.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=5.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.5.0.0\lib\net45\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net47\System.ValueTuple.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="CompanyProductFeatureServiceTests\CompanyProductFeatureService_GetAvaliableUnitsByFeatureTests.cs" />
    <Compile Include="CompanyProductFeatureServiceTests\CompanyProductFeatureService_GetInitialUnitsByFeatureTests.cs" />
    <Compile Include="CompanyProductFeatureServiceTests\CompanyProductFeatureService_HasFeatureTests.cs" />
    <Compile Include="CompanyProductFeatureServiceTests\CompanyProductFeatureService_IsSimultaneousTests.cs" />
    <Compile Include="CompanyProductFeatureServiceTests\CompanyProductFeatureService_IsUnlimitedTests.cs" />
    <Compile Include="CompanyProductFeatureServiceTests\CompanyProductFeatureService_SelectFeaturesByIdCompanyProductTests.cs" />
    <Compile Include="CompanyProductFeatureUserServiceTests\CompanyProductFeatureUserService_HasFeatureTests.cs" />
    <Compile Include="CompanyProductFeatureUserServiceTests\CompanyProductFeatureUserService_GetInitialUnitsByFeatureTests.cs" />
    <Compile Include="CompanyProductServiceTests\CompanyProductService_CanEditTests.cs" />
    <Compile Include="CompanyProductServiceTests\CompanyProductService_CanPublishTests.cs" />
    <Compile Include="CompanyProductConsumUnitServiceTests\CompanyProductConsumUnitService_CheckAndConsumeTypeGlobalTests.cs" />
    <Compile Include="CompanyProductBuilderServiceTests\CompanyProductBuilderService_GetByIdCompanyTests.cs" />
    <Compile Include="CompanyProductBuilderServiceTests\CompanyProductBuilderService_GetByCompanyProductIdTests.cs" />
    <Compile Include="CompanyProductServiceTests\CompanyProductService_GetCompanyProductIdLastMembresyExpiredTests.cs" />
    <Compile Include="CompanyProductServiceTests\CompanyProductService_Has2NTests.cs" />
    <Compile Include="CompanyProductConsumUnitServiceTests\CompanyProductConsumUnitService_FeatureGlobalHasAvailableUnitsTests.cs" />
    <Compile Include="CompanyProductServiceTests\CompanyProductService_HasUserCreditsTests.cs" />
    <Compile Include="CompanyProductServiceTests\CompanyProductService_IsFreemiumTests.cs" />
    <Compile Include="CompanyProductServiceTests\CompanyProductService_IsPaymentTests.cs" />
    <Compile Include="CompanyProductServiceTests\CompanyProductService_SimultaneousLimitReachedTests.cs" />
    <Compile Include="CompanyProductServiceTests\CompanyProductService_UpdateDateModTests.cs" />
    <Compile Include="DTOs\CompanyProductDto.cs" />
    <Compile Include="DTOs\CompanyProductFeatureDto.cs" />
    <Compile Include="Enums\CompanyProductServiceContextEnum.cs" />
    <Compile Include="Factories\CompanyProductFactory\CompanyProductBuilderServiceContextFactory.cs" />
    <Compile Include="Factories\CompanyProductFactory\CompanyProductConsumUnitServiceContextFactory.cs" />
    <Compile Include="Factories\CompanyProductFactory\CompanyProductFeatureServiceContextFactory.cs" />
    <Compile Include="Factories\CompanyProductFactory\CompanyProductServiceContextFactory.cs" />
    <Compile Include="Factories\CompanyProductFactory\Scenarios\CompanyProductBuilderServiceScenario.cs" />
    <Compile Include="Factories\CompanyProductFactory\Scenarios\CompanyProductConsumUnitServiceScenario.cs" />
    <Compile Include="Factories\CompanyProductFactory\Scenarios\CompanyProductFeatureServiceScenario.cs" />
    <Compile Include="Factories\CompanyProductFactory\Context\SimpleContext.cs" />
    <Compile Include="Factories\CompanyProductFactory\Builders\FeatureBuilder.cs" />
    <Compile Include="Factories\CompanyProductFactory\Context\CompanyProductExpectedContext.cs" />
    <Compile Include="Factories\CompanyProductFactory\Context\ByService\CompanyProductBuilderServiceContext\CompanyProductExtraSourceContext.cs" />
    <Compile Include="Factories\CompanyProductFactory\Context\ByService\CompanyProductBuilderServiceContext\CompanyProductsAndFeaturesSourceContext.cs" />
    <Compile Include="Factories\CompanyProductFactory\Scenarios\CompanyProductServiceScenario.cs" />
    <Compile Include="Factories\CompanyProductFactory\Context\MainContext.cs" />
    <Compile Include="Factories\CompanyProductFactory\Builders\CompanyProductBuilder.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Readarbor.Master.Entities\Redarbor.Master.Entities.csproj">
      <Project>{72b93ba2-c177-4ddf-9f27-e08151323719}</Project>
      <Name>Redarbor.Master.Entities</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Common.Entities\Redarbor.Common.Entities.csproj">
      <Project>{3da7e832-19ae-4c35-85f8-5faf5a4dabeb}</Project>
      <Name>Redarbor.Common.Entities</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Cache.Contracts.ServiceLibrary\Redarbor.Core.Cache.Contracts.ServiceLibrary.csproj">
      <Project>{e683967c-b674-4450-b9a6-6030fd65e0f4}</Project>
      <Name>Redarbor.Core.Cache.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Computrabajo.Contracts.ServiceLibrary\Redarbor.Core.Computrabajo.Contracts.ServiceLibrary.csproj">
      <Project>{9BE71737-B615-4227-BB44-C5ECEC3DCED3}</Project>
      <Name>Redarbor.Core.Computrabajo.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Counter.Contracts.ServiceLibrary\Redarbor.Core.Counters.Contracts.ServiceLibrary.csproj">
      <Project>{eec65db1-974d-4172-ba9a-735d63153264}</Project>
      <Name>Redarbor.Core.Counters.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Kpi.Contracts.ServiceLibrary\Redarbor.Core.Kpi.Contracts.ServiceLibrary.csproj">
      <Project>{69FA56CC-33A8-445A-AA62-6BB70ED1BC70}</Project>
      <Name>Redarbor.Core.Kpi.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Resolver.Contracts.ServiceLibrary\Redarbor.Core.Resolver.Contracts.ServiceLibrary.csproj">
      <Project>{27f82506-248d-4117-9dc1-2198a2187fbb}</Project>
      <Name>Redarbor.Core.Resolver.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Shared.Contracts.ServiceLibrary\Redarbor.Core.Shared.Contracts.ServiceLibrary.csproj">
      <Project>{21E3ABEA-6637-49C2-8684-E0F842DE8506}</Project>
      <Name>Redarbor.Core.Shared.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Stack.Contracts.ServiceLibrary\Redarbor.Core.Stack.Contracts.ServiceLibrary.csproj">
      <Project>{89AACF3B-AC86-48CC-97E9-15BC5C6543E7}</Project>
      <Name>Redarbor.Core.Stack.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Extensions.Library\Redarbor.Extensions.Library.csproj">
      <Project>{a30c078b-2f28-42b0-84e7-e02f9c30e27e}</Project>
      <Name>Redarbor.Extensions.Library</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Offer.Contracts.ServiceLibrary\Redarbor.Offer.Contracts.ServiceLibrary.csproj">
      <Project>{5df02fd7-adc3-4aa2-b8ad-f5f2d42c506f}</Project>
      <Name>Redarbor.Offer.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Products.Contracts.ServiceLibrary\Redarbor.Products.Contracts.ServiceLibrary.csproj">
      <Project>{7e7dfd45-2a07-4347-aad6-836b215f129b}</Project>
      <Name>Redarbor.Products.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Products.Impl.ServiceLibrary\Redarbor.Products.Impl.ServiceLibrary.csproj">
      <Project>{e53e025f-0bca-4e70-8efd-6c50ca9c246d}</Project>
      <Name>Redarbor.Products.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Products.Library\Redarbor.Products.Library.csproj">
      <Project>{b0546e25-69e2-4b31-9f4c-1f8c74c00586}</Project>
      <Name>Redarbor.Products.Library</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(VSToolsPath)\TeamTest\Microsoft.TestTools.targets" Condition="Exists('$(VSToolsPath)\TeamTest\Microsoft.TestTools.targets')" />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>