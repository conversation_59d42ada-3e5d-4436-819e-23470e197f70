using Redarbor.Products.UnitTest.ServiceLibrary.Enums;
using Redarbor.Products.UnitTest.ServiceLibrary.Factories.CompanyProductFactory.Context;
using Redarbor.Products.UnitTest.ServiceLibrary.Factories.CompanyProductFactory.Context.Scenarios;

namespace Redarbor.Products.UnitTest.ServiceLibrary.Factories
{
    public class CompanyProductServiceContextFactory
    {
        public static CompanyProductServiceScenario Create(CompanyProductServiceContextEnum context)
        {
            MainContext mainContext = null;

            switch (context)
            {
                case CompanyProductServiceContextEnum.Simple:
                    mainContext = new SimpleContext();
                    break;                                               
            }

            return new CompanyProductServiceScenario(mainContext ?? new SimpleContext());
        }
    }
}
