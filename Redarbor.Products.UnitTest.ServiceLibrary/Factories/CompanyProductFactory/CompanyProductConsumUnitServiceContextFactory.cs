using Redarbor.Products.UnitTest.ServiceLibrary.Enums;
using Redarbor.Products.UnitTest.ServiceLibrary.Factories.CompanyProductFactory.Context;
using Redarbor.Products.UnitTest.ServiceLibrary.Factories.CompanyProductFactory.Scenarios;

namespace Redarbor.Products.UnitTest.ServiceLibrary.Factories.CompanyProductFactory
{
    public class CompanyProductConsumUnitServiceContextFactory
    {
        public static CompanyProductConsumUnitServiceScenario Create(CompanyProductServiceContextEnum context)
        {
            MainContext mainContext = null;

            switch (context)
            {
                case CompanyProductServiceContextEnum.Simple:
                    mainContext = new SimpleContext();
                    break;
                case CompanyProductServiceContextEnum.CompanyProductOfferAmbitRecurrentNotAvailables:
                case CompanyProductServiceContextEnum.CompanyProductOfferHiddenAmbitRecurrentNotAvailables:
                case CompanyProductServiceContextEnum.CompanyProductOfferHighlightedAmbitRecurrentNotAvailables:
                case CompanyProductServiceContextEnum.CompanyProductOfferUrgentAmbitRecurrentNotAvailables:
                case CompanyProductServiceContextEnum.CompanyProductOfferAmbitUnlimited:
                case CompanyProductServiceContextEnum.CompanyProductOfferHiddenAmbitTypeUnlimited:
                case CompanyProductServiceContextEnum.CompanyProductOfferHighlightedAmbitTypeUnlimited:
                case CompanyProductServiceContextEnum.CompanyProductOfferUrgentAmbitTypeUnlimited:
                    mainContext = new CompanyProductExpectedContext(context);
                    break;
            }

            return new CompanyProductConsumUnitServiceScenario(mainContext ?? new SimpleContext());
        }
    }
}
