using Moq;
using Redarbor.Core.Cache.Contracts.ServiceLibrary;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Core.Counter.Contracts.ServiceLibrary;
using Redarbor.Core.Shared.Contracts.ServiceLibrary;
using Redarbor.Products.Contracts.ServiceLibrary;
using Redarbor.Products.Impl.ServiceLibrary;

namespace Redarbor.Products.UnitTest.ServiceLibrary.Factories.CompanyProductFactory.Context.Scenarios
{
    public class CompanyProductServiceScenario
    {
        public MainContext MainContext;
        public Mock<IExceptionPublisherService> ExceptionPublisherService;
        public Mock<ICompanyProductBuilderService> CompanyProductBuilderService;
        public Mock<ICompanyProductFeatureUserService> CompanyProductFeatureUserServiceMock;
        public Mock<ICompanyProductAddService> CompanyProductAddServiceMock;
        public Mock<ICompanyProductConsumUnitService> CompanyProductConsumUnitServiceMock;
        public Mock<ICompanyProductAssignUnitService> CompanyProductAssignUnitServiceMock;
        public Mock<ITempCache> TempCacheServiceMock;
        public Mock<IEncryptionService> EncryptionServiceMock;
        public Mock<IProductService> ProductServiceMock;
        public Mock<ITempCache> TempCacheMock;
        private ICompanyProductService _companyProductService;
        private Mock<IOfferCvCountersService> OfferCvCountersService;


        public ICompanyProductService CompanyProductService { get { return _companyProductService; } }      

        public CompanyProductServiceScenario(MainContext mainContext)
        {
            MainContext = mainContext;
            CompanyProductBuilderService = new Mock<ICompanyProductBuilderService>();
            CompanyProductFeatureUserServiceMock = new Mock<ICompanyProductFeatureUserService>();
            CompanyProductAddServiceMock = new Mock<ICompanyProductAddService>();
            CompanyProductConsumUnitServiceMock = new Mock<ICompanyProductConsumUnitService>();
            CompanyProductAssignUnitServiceMock = new Mock<ICompanyProductAssignUnitService>();
            OfferCvCountersService = new Mock<IOfferCvCountersService>();
            TempCacheMock = new Mock<ITempCache>();

            _companyProductService = new CompanyProductService(
               mainContext.CompanyProductRecoverAndPersistMock.Object,
               mainContext.CompanyProductFeatureServiceMock.Object,
               CompanyProductBuilderService.Object,
               CompanyProductFeatureUserServiceMock.Object,
               CompanyProductAddServiceMock.Object,
               CompanyProductConsumUnitServiceMock.Object,
               CompanyProductAssignUnitServiceMock.Object,
               TempCacheMock.Object,
               OfferCvCountersService.Object);
        }
    }
}

