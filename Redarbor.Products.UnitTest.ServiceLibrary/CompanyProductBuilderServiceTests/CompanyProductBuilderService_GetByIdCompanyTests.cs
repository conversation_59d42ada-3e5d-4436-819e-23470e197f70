using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using Redarbor.Common.Entities.Configuration;
using Redarbor.Master.Entities.Enums;
using Redarbor.Master.Entities.Product;
using Redarbor.Products.UnitTest.ServiceLibrary.Enums;
using Redarbor.Products.UnitTest.ServiceLibrary.Factories.CompanyProductFactory;
using Redarbor.Products.UnitTest.ServiceLibrary.Factories.CompanyProductFactory.Context.Scenarios;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Redarbor.Products.UnitTest.ServiceLibrary.CompanyProductBuilderServiceTests
{
    [TestClass]
    public class CompanyProductBuilderService_GetByIdCompanyTests
    {
        static CompanyProductBuilderServiceScenario _mainContext;

        [TestInitialize]
        public void SetUp()
        {
            _mainContext = CompanyProductBuilderServiceContextFactory.Create(CompanyProductServiceContextEnum.Simple);
        }

        [TestMethod]
        public void When_Called_With_IdCompany_0_Returns_Empty_Object()
        {
            var result = _mainContext.CompanyProductBuilderService.GetByIdCompany(0, 1);

            Assert.AreEqual(result.Id, 0);
        }

        [TestMethod]
        public void When_Called_With_IdPortal_0_Returns_Empty_Object()
        {
            var result = _mainContext.CompanyProductBuilderService.GetByIdCompany(1, 0);

            Assert.AreEqual(result.Id, 0);
        }

        [TestMethod]
        public void When_Product_Not_In_DB_Returns_Empty_Object_And_verify_SelectByCompany()
        {
            _mainContext.TempCacheMock.Setup(m => m.Get<CompanyProductEntity>(It.IsNotNull<string>(), 0)).Returns((CompanyProductEntity)null);
            _mainContext.MainContext.CompanyProductRecoverAndPersistMock.Setup(m => m.SelectByCompany(1, 1, (int)ProductType.Normal)).Returns(new List<CompanyProductEntity>());
            var result = _mainContext.CompanyProductBuilderService.GetByIdCompany(1, 1);

            Assert.AreEqual(result.Id, 0);
            _mainContext.MainContext.CompanyProductRecoverAndPersistMock.Verify(m => m.SelectByCompany(1, 1, (int)ProductType.Normal), Times.Once);
        }

        [TestMethod]
        [DataRow(CompanyProductServiceContextEnum.CompanyProductV2FreemiumGroup)]
        [DataRow(CompanyProductServiceContextEnum.CompanyProductV2PackGroup)]
        [DataRow(CompanyProductServiceContextEnum.CompanyProductV2MembershipGroup)]
        public void When_Product_In_Safety_Cache_Returns_Object_And_verify_SelectByCompany(CompanyProductServiceContextEnum contextEnum)
        {
            var context = CompanyProductBuilderServiceContextFactory.Create(contextEnum);
            context.MainContext.CompanyProductExpected.DateMod = DateTime.UtcNow;
            context.TempCacheMock.Setup(m => m.Get<CompanyProductEntity>(It.IsNotNull<string>(), context.MainContext.CompanyProductExpected.PortalId)).Returns(context.MainContext.CompanyProductExpected);
            context.MainContext.CompanyProductRecoverAndPersistMock.Setup(m => m.SelectCompanyProductDateMod(context.MainContext.CompanyProductExpected.IdCompany, (int)ProductType.Normal, context.MainContext.CompanyProductExpected.PortalId)).Returns(context.MainContext.CompanyProductExpected.DateMod);

            var result = context.CompanyProductBuilderService.GetByIdCompany(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId);

            Assert.AreEqual(result.Id, context.MainContext.CompanyProductExpected.Id);
            context.MainContext.CompanyProductRecoverAndPersistMock.Verify(m => m.SelectByCompany(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId, (int)ProductType.Normal), Times.Never);
        }

        [TestMethod]
        [DataRow(CompanyProductServiceContextEnum.CompanyProductV2FreemiumGroup)]
        [DataRow(CompanyProductServiceContextEnum.CompanyProductV2PackGroup)]
        [DataRow(CompanyProductServiceContextEnum.CompanyProductV2MembershipGroup)]
        public void When_Product_In_No_Safety_Cache_Returns_DB_Object_And_verify_SelectByCompany(CompanyProductServiceContextEnum contextEnum)
        {
            var context = CompanyProductBuilderServiceContextFactory.Create(contextEnum);
            context.TempCacheMock.Setup(m => m.Get<CompanyProductEntity>(It.IsNotNull<string>(), context.MainContext.CompanyProductExpected.PortalId)).Returns(context.MainContext.CompanyProductExpected);
            context.MainContext.CompanyProductRecoverAndPersistMock.Setup(m => m.SelectCompanyProductDateMod(context.MainContext.CompanyProductExpected.IdCompany, (int)ProductType.Normal, context.MainContext.CompanyProductExpected.PortalId)).Returns(DateTime.UtcNow);
            context.MainContext.CompanyProductRecoverAndPersistMock.Setup(m => m.SelectByCompany(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId, (int)ProductType.Normal)).Returns(new List<CompanyProductEntity>() { context.MainContext.CompanyProductExpected });
            context.MainContext.CompanyProductRecoverAndPersistMock.Setup(m => m.SelectByCompany(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId, (int)ProductType.Extra)).Returns(new List<CompanyProductEntity>());
            context.MainContext.CompanyProductFeatureServiceMock.Setup(m => m.SelectFeaturesByIdCompanyProduct(context.MainContext.CompanyProductExpected.Id, context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId)).Returns(new List<CompanyProductFeatureEntity>());

            var result = context.CompanyProductBuilderService.GetByIdCompany(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId);

            Assert.AreEqual(result.Id, context.MainContext.CompanyProductExpected.Id);
            context.MainContext.CompanyProductRecoverAndPersistMock.Verify(m => m.SelectByCompany(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId, (int)ProductType.Normal), Times.Once);
        }

        [TestMethod]
        public void When_Product_Is_A_List_Packswith_Availables_Select_Older_Pack()
        {
            var context = CompanyProductBuilderServiceContextFactory.Create(CompanyProductServiceContextEnum.CompanyProductListByPacksWithAvailables);

            var result = context.CompanyProductBuilderService.GetByIdCompany(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId);

            Assert.AreEqual(result.Id, context.MainContext.CompanyProductExpected.Id);
            Assert.AreEqual(result.GroupId, context.MainContext.CompanyProductExpected.GroupId);
            Assert.AreEqual(result.GroupId, (short)ProductGroupsEnum.Packs);
        }

        [TestMethod]
        public void When_Product_Is_A_List_Packs_without_Availables_Select_BasicService()
        {
            var context = CompanyProductBuilderServiceContextFactory.Create(CompanyProductServiceContextEnum.CompanyProductListByPacksWithoutAvailables);

            var result = context.CompanyProductBuilderService.GetByIdCompany(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId);

            Assert.AreEqual(result.Id, context.MainContext.CompanyProductExpected.Id);
            Assert.AreEqual(result.GroupId, context.MainContext.CompanyProductExpected.GroupId);
            Assert.AreEqual(result.GroupId, (short)ProductGroupsEnum.Freemium);
        }

        [TestMethod]
        public void When_Product_Is_Membership_With_CompuAdvisor()
        {
            var context = CompanyProductBuilderServiceContextFactory.Create(CompanyProductServiceContextEnum.CompanyProductListMembershipWithCompuAdvisor);
            var visionFeatureSource = context.MainContext.CompanyProductFeaturesSource.FirstOrDefault().FirstOrDefault(f => f.AmbitId == (short)ProductAmbitEnum.Vision);

            var result = context.CompanyProductBuilderService.GetByIdCompany(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId);
            var visionFeatureResult = result.Features.FirstOrDefault(f => f.AmbitId == (short)ProductAmbitEnum.Vision);

            Assert.AreEqual(result.Id, context.MainContext.CompanyProductExpected.Id);
            Assert.AreEqual(result.GroupId, context.MainContext.CompanyProductExpected.GroupId);
            Assert.AreEqual(result.GroupId, (short)ProductGroupsEnum.Membership);
            Assert.AreEqual(visionFeatureResult.AmbitId, visionFeatureSource.AmbitId);
            Assert.AreEqual(visionFeatureResult.AmbitId, (short)ProductAmbitEnum.Vision);
        }

        [TestMethod]
        [DataRow(CompanyProductServiceContextEnum.CompanyProductV2FreemiumGroup)]
        [DataRow(CompanyProductServiceContextEnum.CompanyProductV2PackGroup)]
        [DataRow(CompanyProductServiceContextEnum.CompanyProductV2MembershipGroup)]
        public void When_Product_No_Has_Extra_Product_then_Return_same_Product(CompanyProductServiceContextEnum contextEnum)
        {
            var context = CompanyProductBuilderServiceContextFactory.Create(contextEnum);

            context.MainContext.CompanyProductRecoverAndPersistMock.Setup(m => m.SelectByCompany(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId, (int)ProductType.Normal)).Returns(new List<CompanyProductEntity>() { context.MainContext.CompanyProductExpected });
            context.MainContext.CompanyProductRecoverAndPersistMock.Setup(m => m.SelectByCompany(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId, (int)ProductType.Extra)).Returns(new List<CompanyProductEntity>());
            context.MainContext.CompanyProductFeatureServiceMock.Setup(m => m.SelectFeaturesByIdCompanyProduct(context.MainContext.CompanyProductExpected.Id, context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId)).Returns(new List<CompanyProductFeatureEntity>());

            var result = context.CompanyProductBuilderService.GetByIdCompany(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId);

            Assert.AreEqual(result.Id, context.MainContext.CompanyProductExpected.Id);
            Assert.AreEqual(result.GroupId, context.MainContext.CompanyProductExpected.GroupId);
            Assert.IsTrue(!result.Features.Any());
        }

        [TestMethod]
        [DataRow(CompanyProductServiceContextEnum.CompanyProductV2FreemiumGroup)]
        [DataRow(CompanyProductServiceContextEnum.CompanyProductV2PackGroup)]
        [DataRow(CompanyProductServiceContextEnum.CompanyProductV2MembershipGroup)]
        public void When_Product_Has_Extra_Product_But_Product_No_Has_Features_Then_Return_same_Product(CompanyProductServiceContextEnum contextEnum)
        {
            var context = CompanyProductBuilderServiceContextFactory.Create(contextEnum);
            context.MainContext.CompanyProductRecoverAndPersistMock.Setup(m => m.SelectByCompany(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId, (int)ProductType.Normal)).Returns(new List<CompanyProductEntity>() { context.MainContext.CompanyProductExpected });
            context.MainContext.CompanyProductRecoverAndPersistMock.Setup(m => m.SelectByCompany(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId, (int)ProductType.Extra)).Returns(new List<CompanyProductEntity>() {
                new CompanyProductEntity(){
                    GroupId = (short) ProductGroupsEnum.Packs,
                    Features = new List<CompanyProductFeatureEntity>(){
                            new CompanyProductFeatureEntity() {
                                AmbitId = (short)ProductAmbitEnum.Offer
                        }
                    }
                }
            });

            context.MainContext.CompanyProductFeatureServiceMock.Setup(m => m.SelectFeaturesByIdCompanyProduct(context.MainContext.CompanyProductExpected.Id, context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId)).Returns(new List<CompanyProductFeatureEntity>());

            var result = context.CompanyProductBuilderService.GetByIdCompany(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId);

            Assert.AreEqual(result.Id, context.MainContext.CompanyProductExpected.Id);
            Assert.AreEqual(result.GroupId, context.MainContext.CompanyProductExpected.GroupId);
            Assert.IsTrue(!result.Features.Any());
        }

        [TestMethod]
        [DataRow(CompanyProductServiceContextEnum.CompanyProductMembershipWithExtraGroupDifferent)]
        [DataRow(CompanyProductServiceContextEnum.CompanyProductMembershipWithExtraFeatureDifferent)]
        [DataRow(CompanyProductServiceContextEnum.CompanyProductPacksWithExtraTypeProductSameFeatureUnitsNegative)]
        public void When_MembershipProduct_Has_Extra_Product_But_Not_Sum_Units(CompanyProductServiceContextEnum contextEnum)
        {
            var context = CompanyProductBuilderServiceContextFactory.Create(contextEnum);
            var featureExpected = context.MainContext.CompanyProductExpected.Features.FirstOrDefault(f => f.AmbitId == (short)ProductAmbitEnum.Offer);

            var result = context.CompanyProductBuilderService.GetByIdCompany(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId);

            var featureResult = result.Features.FirstOrDefault(f => f.AmbitId == (short)ProductAmbitEnum.Offer);
            Assert.AreNotEqual(featureExpected.AvailableUnits, featureResult.AvailableUnits);
            Assert.AreNotEqual(featureExpected.InitialUnits, featureResult.InitialUnits);
        }

        [TestMethod]
        [DataRow(CompanyProductServiceContextEnum.CompanyProductMembershipWithCorrectExtra)]
        [DataRow(CompanyProductServiceContextEnum.CompanyProductMembershipWithTwoCorrectExtra)]
        [DataRow(CompanyProductServiceContextEnum.CompanyProductPacksWithExtraTypeProductSameFeature)]
        public void When_Product_Has_Extra_Product_Then_Sum_Units(CompanyProductServiceContextEnum contextEnum)
        {
            var context = CompanyProductBuilderServiceContextFactory.Create(contextEnum);
            var featureExpected = context.MainContext.CompanyProductExpected.Features.FirstOrDefault(f => f.AmbitId == (short)ProductAmbitEnum.Offer);

            var result = context.CompanyProductBuilderService.GetByIdCompany(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId);

            var featureResult = result.Features.FirstOrDefault(f => f.AmbitId == (short)ProductAmbitEnum.Offer);
            Assert.AreEqual(featureExpected.AvailableUnits, featureResult.AvailableUnits);
            Assert.AreEqual(featureExpected.InitialUnits, featureResult.InitialUnits);
        }

        [TestMethod]
        [DataRow(CompanyProductServiceContextEnum.CompanyProductMembershipWithCorrectExtraAndTwoFeatures)]
        [DataRow(CompanyProductServiceContextEnum.CompanyProductPacksWithCorrectExtraNewFeature)]
        [DataRow(CompanyProductServiceContextEnum.CompanyProductPacksExtraNewServiceTypeFeature)]
        public void When_Product_Has_Extra_Product_Two_Features(CompanyProductServiceContextEnum contextEnum)
        {
            var context = CompanyProductBuilderServiceContextFactory.Create(contextEnum);
            var featureOfferExpected = context.MainContext.CompanyProductExpected.Features.FirstOrDefault(f => f.AmbitId == (short)ProductAmbitEnum.Offer);
            var featureHighlightedExpected = context.MainContext.CompanyProductExpected.Features.FirstOrDefault(f => f.AmbitId == (short)ProductAmbitEnum.OfferHighlighted);

            var result = context.CompanyProductBuilderService.GetByIdCompany(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId);

            var featureOfferResult = result.Features.FirstOrDefault(f => f.AmbitId == (short)ProductAmbitEnum.Offer);
            var featureHighlightedResult = result.Features.FirstOrDefault(f => f.AmbitId == (short)ProductAmbitEnum.OfferHighlighted);

            Assert.AreEqual(featureOfferExpected.AvailableUnits, featureOfferResult.AvailableUnits);
            Assert.AreEqual(featureOfferExpected.InitialUnits, featureOfferResult.InitialUnits);
            Assert.AreEqual(featureHighlightedExpected.AvailableUnits, featureHighlightedResult.AvailableUnits);
            Assert.AreEqual(featureHighlightedExpected.InitialUnits, featureHighlightedResult.InitialUnits);
        }

        [TestMethod]
        public void When_Product_Has_Extra_Product_With_Service_Type_Feature_Already_Exist()
        {
            var context = CompanyProductBuilderServiceContextFactory.Create(CompanyProductServiceContextEnum.CompanyProductPacksExtraServiceTypeFeatureAlreadyExist);
            var featureHighlightedExpected = context.MainContext.CompanyProductExpected.Features.FirstOrDefault(f => f.AmbitId == (short)ProductAmbitEnum.OfferHighlighted);
            var featureHighlightedExtraSource = context.MainContext.CompanyProductFeaturesExtraSource.FirstOrDefault().FirstOrDefault(f => f.AmbitId == (short)ProductAmbitEnum.OfferHighlighted);

            var result = context.CompanyProductBuilderService.GetByIdCompany(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId);

            var featureHighlightedResult = result.Features.FirstOrDefault(f => f.AmbitId == (short)ProductAmbitEnum.OfferHighlighted);

            Assert.AreEqual(featureHighlightedExpected.TypeId, featureHighlightedResult.TypeId);
            Assert.AreNotEqual(featureHighlightedResult.TypeId, featureHighlightedExtraSource.TypeId);
            Assert.AreNotEqual(featureHighlightedResult.AvailableUnits, featureHighlightedExtraSource.AvailableUnits);
        }

        [TestMethod]
        [DataRow(CompanyProductServiceContextEnum.CompanyProductV2PackGroup)]
        [DataRow(CompanyProductServiceContextEnum.CompanyProductV2MembershipGroup)]
        public void When_Product_Verify_Not_Call_SelectOldProducts(CompanyProductServiceContextEnum contextEnum)
        {
            var context = CompanyProductBuilderServiceContextFactory.Create(contextEnum);
            context.MainContext.CompanyProductRecoverAndPersistMock.Setup(m => m.SelectByCompany(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId, (int)ProductType.Normal)).Returns(new List<CompanyProductEntity>() { context.MainContext.CompanyProductExpected });
            context.MainContext.CompanyProductRecoverAndPersistMock.Setup(m => m.SelectByCompany(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId, (int)ProductType.Extra)).Returns(new List<CompanyProductEntity>());
            context.MainContext.CompanyProductFeatureServiceMock.Setup(m => m.SelectFeaturesByIdCompanyProduct(context.MainContext.CompanyProductExpected.Id, context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId)).Returns(new List<CompanyProductFeatureEntity>());

            var result = context.CompanyProductBuilderService.GetByIdCompany(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId);

            context.MainContext.CompanyProductRecoverAndPersistMock.Verify(m => m.SelectOldProducts(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId), Times.Never);
        }

        [TestMethod]
        public void When_FreemiumProduct_And_PortalConfig_BlockOldMembership_Is_False_Verify_Not_Call_SelectOldProducts()
        {
            var context = CompanyProductBuilderServiceContextFactory.Create(CompanyProductServiceContextEnum.CompanyProductV2FreemiumGroup);
            context.MainContext.CompanyProductRecoverAndPersistMock.Setup(m => m.SelectByCompany(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId, (int)ProductType.Normal)).Returns(new List<CompanyProductEntity>() { context.MainContext.CompanyProductExpected });
            context.MainContext.CompanyProductRecoverAndPersistMock.Setup(m => m.SelectByCompany(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId, (int)ProductType.Extra)).Returns(new List<CompanyProductEntity>());
            context.MainContext.CompanyProductFeatureServiceMock.Setup(m => m.SelectFeaturesByIdCompanyProduct(context.MainContext.CompanyProductExpected.Id, context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId)).Returns(new List<CompanyProductFeatureEntity>());

            var result = context.CompanyProductBuilderService.GetByIdCompany(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId);

            context.MainContext.CompanyProductRecoverAndPersistMock.Verify(m => m.SelectOldProducts(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId), Times.Never);
        }

        [TestMethod]
        public void When_FreemiumProduct_And_PortalConfig_BlockOldMembership_Is_True_Verify_Call_SelectOldProducts()
        {
            var context = CompanyProductBuilderServiceContextFactory.Create(CompanyProductServiceContextEnum.CompanyProductV2FreemiumGroup);
            context.MainContext.PortalConfigurationServiceMock.Setup(m => m.GetPortalConfiguration(It.IsNotNull<short>(), It.IsNotNull<short>())).Returns(new PortalConfig() { blockOldMembership = true });
            context.MainContext.CompanyProductRecoverAndPersistMock.Setup(m => m.SelectByCompany(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId, (int)ProductType.Normal)).Returns(new List<CompanyProductEntity>() { context.MainContext.CompanyProductExpected });
            context.MainContext.CompanyProductRecoverAndPersistMock.Setup(m => m.SelectByCompany(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId, (int)ProductType.Extra)).Returns(new List<CompanyProductEntity>());
            context.MainContext.CompanyProductFeatureServiceMock.Setup(m => m.SelectFeaturesByIdCompanyProduct(context.MainContext.CompanyProductExpected.Id, context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId)).Returns(new List<CompanyProductFeatureEntity>());
            context.MainContext.CompanyProductRecoverAndPersistMock.Setup(m => m.SelectOldProducts(It.IsNotNull<int>(), It.IsNotNull<int>())).Returns(new List<CompanyProductEntity>());

            var result = context.CompanyProductBuilderService.GetByIdCompany(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId);

            context.MainContext.CompanyProductRecoverAndPersistMock.Verify(m => m.SelectOldProducts(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId), Times.Once);
        }

        [TestMethod]
        public void When_FreemiumProduct_And_PortalConfig_BlockOldMembership_Is_True_And_There_Are_Old_Products_Then_Merge_Has_OldMembership_Properties_Setted()
        {
            var context = CompanyProductBuilderServiceContextFactory.Create(CompanyProductServiceContextEnum.CompanyProductV2FreemiumGroup);
            context.MainContext.CompanyProductExpected.IsBlockedByOldMembership = true;
            context.MainContext.CompanyProductExpected.OldMembershipId = 12;
            context.MainContext.CompanyProductExpected.OldMembershipCompanyProductId = 100;
            context.MainContext.CompanyProductExpected.OldMembershipDateExpiration = DateTime.Now.AddDays(600);

            context.MainContext.PortalConfigurationServiceMock.Setup(m => m.GetPortalConfiguration(It.IsNotNull<short>(), It.IsNotNull<short>())).Returns(new PortalConfig() { blockOldMembership = true });
            context.MainContext.CompanyProductRecoverAndPersistMock.Setup(m => m.SelectByCompany(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId, (int)ProductType.Normal)).Returns(new List<CompanyProductEntity>() { context.MainContext.CompanyProductExpected });
            context.MainContext.CompanyProductRecoverAndPersistMock.Setup(m => m.SelectByCompany(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId, (int)ProductType.Extra)).Returns(new List<CompanyProductEntity>());
            context.MainContext.CompanyProductFeatureServiceMock.Setup(m => m.SelectFeaturesByIdCompanyProduct(context.MainContext.CompanyProductExpected.Id, context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId)).Returns(new List<CompanyProductFeatureEntity>());
            context.MainContext.CompanyProductRecoverAndPersistMock.Setup(m => m.SelectOldProducts(It.IsNotNull<int>(), It.IsNotNull<int>())).Returns(new List<CompanyProductEntity>() {
                new CompanyProductEntity(){
                    IsBlockedByOldMembership = context.MainContext.CompanyProductExpected.IsBlockedByOldMembership,
                    OldMembershipId = context.MainContext.CompanyProductExpected.OldMembershipId,
                    OldMembershipCompanyProductId = context.MainContext.CompanyProductExpected.OldMembershipCompanyProductId,
                    OldMembershipDateExpiration = context.MainContext.CompanyProductExpected.OldMembershipDateExpiration
                }
            });

            var result = context.CompanyProductBuilderService.GetByIdCompany(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId);

            Assert.AreEqual(result.IsBlockedByOldMembership, context.MainContext.CompanyProductExpected.IsBlockedByOldMembership);
            Assert.AreEqual(result.OldMembershipId, context.MainContext.CompanyProductExpected.OldMembershipId);
            Assert.AreEqual(result.OldMembershipCompanyProductId, context.MainContext.CompanyProductExpected.OldMembershipCompanyProductId);
            Assert.AreEqual(result.OldMembershipDateExpiration, context.MainContext.CompanyProductExpected.OldMembershipDateExpiration);
        }

        [TestMethod]
        [DataRow(CompanyProductServiceContextEnum.CompanyProductV2FreemiumGroup)]
        [DataRow(CompanyProductServiceContextEnum.CompanyProductV2PackGroup)]
        [DataRow(CompanyProductServiceContextEnum.CompanyProductV2MembershipGroup)]
        public void When_No_Inform_About_UserId_Then_No_Load_User_Features(CompanyProductServiceContextEnum contextEnum)
        {
            var context = CompanyProductBuilderServiceContextFactory.Create(contextEnum);
            context.MainContext.CompanyProductRecoverAndPersistMock.Setup(m => m.SelectByCompany(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId, (int)ProductType.Normal)).Returns(new List<CompanyProductEntity>() { context.MainContext.CompanyProductExpected });
            context.MainContext.CompanyProductFeatureServiceMock.Setup(m => m.SelectFeaturesByIdCompanyProduct(context.MainContext.CompanyProductExpected.Id, context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId)).Returns(new List<CompanyProductFeatureEntity>());
            context.MainContext.CompanyProductRecoverAndPersistMock.Setup(m => m.SelectByCompany(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId, (int)ProductType.Extra)).Returns(new List<CompanyProductEntity>());

            var result = context.CompanyProductBuilderService.GetByIdCompany(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId);

            context.CompanyProductFeatureUserServiceMock.Verify(m => m.GetAssignCreditsByUser(It.IsNotNull<long>(), It.IsNotNull<int>(), It.IsNotNull<short>()), Times.Never);
        }

        [TestMethod]
        [DataRow(CompanyProductServiceContextEnum.CompanyProductV2FreemiumGroup)]
        [DataRow(CompanyProductServiceContextEnum.CompanyProductV2PackGroup)]
        [DataRow(CompanyProductServiceContextEnum.CompanyProductV2MembershipGroup)]
        public void When_No_Have_AssignCredits_Feature_Then_No_Load_User_Features(CompanyProductServiceContextEnum contextEnum)
        {
            var iduser = 1130;
            var context = CompanyProductBuilderServiceContextFactory.Create(contextEnum);
            context.MainContext.CompanyProductRecoverAndPersistMock.Setup(m => m.SelectByCompany(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId, (int)ProductType.Normal)).Returns(new List<CompanyProductEntity>() { context.MainContext.CompanyProductExpected });
            context.MainContext.CompanyProductFeatureServiceMock.Setup(m => m.SelectFeaturesByIdCompanyProduct(context.MainContext.CompanyProductExpected.Id, context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId)).Returns(new List<CompanyProductFeatureEntity>());
            context.MainContext.CompanyProductRecoverAndPersistMock.Setup(m => m.SelectByCompany(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId, (int)ProductType.Extra)).Returns(new List<CompanyProductEntity>());

            var result = context.CompanyProductBuilderService.GetByIdCompany(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId, iduser);

            context.CompanyProductFeatureUserServiceMock.Verify(m => m.GetAssignCreditsByUser(It.IsNotNull<long>(), It.IsNotNull<int>(), It.IsNotNull<short>()), Times.Never);
        }

        [TestMethod]
        public void When_Product_With_AssignFeature_And_UserId_Then_Load_User_Features()
        {
            var iduser = 1130;
            var context = CompanyProductBuilderServiceContextFactory.Create(CompanyProductServiceContextEnum.CompanyProductV2MembershipGroup);
            context.MainContext.CompanyProductRecoverAndPersistMock.Setup(m => m.SelectByCompany(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId, (int)ProductType.Normal)).Returns(new List<CompanyProductEntity>() { context.MainContext.CompanyProductExpected });
            context.MainContext.CompanyProductFeatureServiceMock.Setup(m => m.SelectFeaturesByIdCompanyProduct(context.MainContext.CompanyProductExpected.Id, context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId))
                                                            .Returns(new List<CompanyProductFeatureEntity>()
                                                                    {
                                                                        new CompanyProductFeatureEntity(){ AmbitId = (short)ProductAmbitEnum.AssignCredits }
                                                            });

            context.MainContext.CompanyProductRecoverAndPersistMock.Setup(m => m.SelectByCompany(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId, (int)ProductType.Extra)).Returns(new List<CompanyProductEntity>());

            var result = context.CompanyProductBuilderService.GetByIdCompany(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId, iduser);

            context.CompanyProductFeatureUserServiceMock.Verify(m => m.GetAssignCreditsByUser(It.IsNotNull<long>(), It.IsNotNull<int>(), It.IsNotNull<short>()), Times.Once);
            
        }

        [TestMethod]
        [DataRow(CompanyProductServiceContextEnum.CompanyProductV2FreemiumGroup)]
        [DataRow(CompanyProductServiceContextEnum.CompanyProductV2PackGroup)]
        [DataRow(CompanyProductServiceContextEnum.CompanyProductV2MembershipGroup)]
        public void When_Build_Empty_Product_No_Add_Cache(CompanyProductServiceContextEnum contextEnum)
        {
            var context = CompanyProductBuilderServiceContextFactory.Create(contextEnum);
            context.MainContext.CompanyProductRecoverAndPersistMock.Setup(m => m.SelectByCompany(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId, (int)ProductType.Normal)).Returns(new List<CompanyProductEntity>() { context.MainContext.CompanyProductExpected });
            context.MainContext.CompanyProductFeatureServiceMock.Setup(m => m.SelectFeaturesByIdCompanyProduct(context.MainContext.CompanyProductExpected.Id, context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId)).Returns(new List<CompanyProductFeatureEntity>());
            context.MainContext.CompanyProductRecoverAndPersistMock.Setup(m => m.SelectByCompany(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId, (int)ProductType.Extra)).Returns(new List<CompanyProductEntity>());

            var result = context.CompanyProductBuilderService.GetByIdCompany(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId);

            context.TempCacheMock.Verify(m => m.Add(It.IsNotNull<string>(), It.IsNotNull<Object>(), It.IsNotNull<short>()), Times.Never);
        }

        [TestMethod]
        [DataRow(CompanyProductServiceContextEnum.CompanyProductV2FreemiumGroup)]
        [DataRow(CompanyProductServiceContextEnum.CompanyProductV2PackGroup)]
        [DataRow(CompanyProductServiceContextEnum.CompanyProductV2MembershipGroup)]
        public void When_Build_Correct_Product_Add_Cache(CompanyProductServiceContextEnum contextEnum)
        {
            var context = CompanyProductBuilderServiceContextFactory.Create(contextEnum);
            context.MainContext.CompanyProductRecoverAndPersistMock.Setup(m => m.SelectByCompany(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId, (int)ProductType.Normal)).Returns(new List<CompanyProductEntity>() { context.MainContext.CompanyProductExpected });
            context.MainContext.CompanyProductFeatureServiceMock.Setup(m => m.SelectFeaturesByIdCompanyProduct(context.MainContext.CompanyProductExpected.Id, context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId))
                                                            .Returns(new List<CompanyProductFeatureEntity>()
                                                                    {
                                                                        new CompanyProductFeatureEntity(){ AmbitId = (short)ProductAmbitEnum.Offer, AvailableUnits=10, InitialUnits=10 }
                                                            });
            context.MainContext.CompanyProductRecoverAndPersistMock.Setup(m => m.SelectByCompany(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId, (int)ProductType.Extra)).Returns(new List<CompanyProductEntity>());

            var result = context.CompanyProductBuilderService.GetByIdCompany(context.MainContext.CompanyProductExpected.IdCompany, context.MainContext.CompanyProductExpected.PortalId);

            context.TempCacheMock.Verify(m => m.Add(It.IsNotNull<string>(), It.IsNotNull<Object>(), It.IsNotNull<TimeSpan>(), It.IsNotNull<short>()), Times.Once);
        }
    }
}
