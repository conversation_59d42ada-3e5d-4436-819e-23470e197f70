using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using Redarbor.Master.Entities.Enums;
using Redarbor.Master.Entities.Product;
using Redarbor.Products.UnitTest.ServiceLibrary.Enums;
using Redarbor.Products.UnitTest.ServiceLibrary.Factories.CompanyProductFactory;
using Redarbor.Products.UnitTest.ServiceLibrary.Factories.CompanyProductFactory.Context.Scenarios;
using System;
using System.Collections.Generic;

namespace Redarbor.Products.UnitTest.ServiceLibrary.CompanyProductFeatureServiceTests
{
    [TestClass]
    public class CompanyProductFeatureService_SelectFeaturesByIdCompanyProductTests
    {
        static CompanyProductFeatureServiceScenario _mainContext;

        [TestInitialize]
        public void SetUp()
        {
            _mainContext = CompanyProductFeatureServiceContextFactory.Create(CompanyProductServiceContextEnum.Simple);
        }

        [TestMethod]
        public void When_IdCompanyProduct_Is_0_Return_Empty_Result()
        {
            var result = _mainContext.CompanyProductFeatureService.SelectFeaturesByIdCompanyProduct(0, 1, 1);
            Assert.IsTrue(result != null);
            Assert.IsTrue(result.Count == 0);
        }

        [TestMethod]
        public void When_IdCompany_Is_0_Return_Empty_Result()
        {
            var result = _mainContext.CompanyProductFeatureService.SelectFeaturesByIdCompanyProduct(1, 0, 1);
            Assert.IsTrue(result != null);
            Assert.IsTrue(result.Count == 0);
        }

        [TestMethod]
        public void When_IdPortal_Is_0_Return_Empty_Result()
        {
            var result = _mainContext.CompanyProductFeatureService.SelectFeaturesByIdCompanyProduct(1, 1, 0);
            Assert.IsTrue(result != null);
            Assert.IsTrue(result.Count == 0);
        }

        [TestMethod]
        public void When_TempCache_Is_Null_Then_Call_SelectFeaturesByIdCompanyProduct_Of_BBDD()
        {
            var feature = new List<CompanyProductFeatureEntity>() {
                new CompanyProductFeatureEntity(){ AmbitId = (short)ProductAmbitEnum.Offer, AvailableUnits= 10}
            };

            _mainContext.CompanyProductFeatureRecoverAndPersist.Setup(s => s.SelectFeaturesByIdCompanyProduct(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<int>())).Returns(feature);
            _mainContext.CompanyProductFeatureRecoverAndPersist.Setup(s => s.GetFeaturesByCompanyProductId(It.IsAny<int>(), It.IsAny<short>(), It.IsAny<IEnumerable<int>>())).Returns(new List<CompanyProductFeatureEntity>());

            var result = _mainContext.CompanyProductFeatureService.SelectFeaturesByIdCompanyProduct(1, 1, 1);

            _mainContext.CompanyProductFeatureRecoverAndPersist.Verify(v => v.SelectFeaturesByIdCompanyProduct(1, 1, 1), Times.Once);
        }

        [TestMethod]
        public void When_TempCache_Is_Empty_Then_Call_SelectFeaturesByIdCompanyProduct_Of_BBDD()
        {
            var feature = new List<CompanyProductFeatureEntity>() {
                new CompanyProductFeatureEntity(){ AmbitId = (short)ProductAmbitEnum.Offer, AvailableUnits= 10}
            };

            _mainContext.CompanyProductFeatureRecoverAndPersist.Setup(s => s.SelectFeaturesByIdCompanyProduct(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<int>())).Returns(feature);
            _mainContext.TempCacheMock.Setup(s => s.Get<List<CompanyProductFeatureEntity>>(It.IsAny<string>(), It.IsAny<short>())).Returns(new List<CompanyProductFeatureEntity>());
            _mainContext.CompanyProductFeatureRecoverAndPersist.Setup(s => s.GetFeaturesByCompanyProductId(It.IsAny<int>(), It.IsAny<short>(), It.IsAny<IEnumerable<int>>())).Returns(new List<CompanyProductFeatureEntity>());

            var result = _mainContext.CompanyProductFeatureService.SelectFeaturesByIdCompanyProduct(1, 1, 1);

            _mainContext.CompanyProductFeatureRecoverAndPersist.Verify(v => v.SelectFeaturesByIdCompanyProduct(1, 1, 1), Times.Once);
        }

        [TestMethod]
        public void When_TempCache_With_Values_But_Old_Date_Then_Call_SelectFeaturesByIdCompanyProduct_Of_BBDD()
        {
            var feature = new List<CompanyProductFeatureEntity>() {
                new CompanyProductFeatureEntity(){ AmbitId = (short)ProductAmbitEnum.Offer, AvailableUnits= 10}
            };

            _mainContext.CompanyProductFeatureRecoverAndPersist.Setup(s => s.SelectFeaturesByIdCompanyProduct(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<int>())).Returns(feature);
            _mainContext.TempCacheMock.Setup(s => s.Get<List<CompanyProductFeatureEntity>>(It.IsAny<string>(), It.IsAny<short>())).Returns(new List<CompanyProductFeatureEntity>() {
                new CompanyProductFeatureEntity(){ AmbitId = (short)ProductAmbitEnum.Offer, DateMod = DateTime.MinValue}
            });

            _mainContext.CompanyProductFeatureRecoverAndPersist.Setup(s => s.SelectLastDateModFeatureByIdcompanyProduct(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<int>())).Returns(DateTime.Now);
            _mainContext.CompanyProductFeatureRecoverAndPersist.Setup(s => s.GetFeaturesByCompanyProductId(It.IsAny<int>(), It.IsAny<short>(), It.IsAny<IEnumerable<int>>())).Returns(new List<CompanyProductFeatureEntity>());
            var result = _mainContext.CompanyProductFeatureService.SelectFeaturesByIdCompanyProduct(1, 1, 1);

            _mainContext.CompanyProductFeatureRecoverAndPersist.Verify(v => v.SelectFeaturesByIdCompanyProduct(1, 1, 1), Times.Once);
        }

        [TestMethod]
        public void When_TempCache_With_Values_And_Date_Is_Correct_Then_Not_Call_SelectFeaturesByIdCompanyProduct_Of_BBDD_And_Return_TempCache_Value()
        {
            var date = DateTime.Now;
            var feature = new List<CompanyProductFeatureEntity>() {
                new CompanyProductFeatureEntity(){ AmbitId = (short)ProductAmbitEnum.Offer, DateMod = date}
            };

            _mainContext.TempCacheMock.Setup(s => s.Get<List<CompanyProductFeatureEntity>>(It.IsAny<string>(), It.IsAny<short>())).Returns(feature);

            _mainContext.CompanyProductFeatureRecoverAndPersist.Setup(s => s.SelectLastDateModFeatureByIdcompanyProduct(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<int>())).Returns(date);

            var result = _mainContext.CompanyProductFeatureService.SelectFeaturesByIdCompanyProduct(1, 1, 1);

            Assert.AreEqual(result, feature);
            _mainContext.CompanyProductFeatureRecoverAndPersist.Verify(v => v.SelectFeaturesByIdCompanyProduct(1, 1, 1), Times.Never);
        }

        [TestMethod]
        public void When_Call_SelectFeaturesByIdCompanyProduct_Of_BBDD_With_Values_Call_AddTempCache()
        {
            var key = $"COMPANY_PRODUCT_FEATURES_BY_COMPANY_PRODUCT_ID_1_COMPANY_ID_1_PORTAL_ID_1";
            var feature = new List<CompanyProductFeatureEntity>() {
                new CompanyProductFeatureEntity(){ AmbitId = (short)ProductAmbitEnum.Offer, AvailableUnits= 10}
            };

            _mainContext.CompanyProductFeatureRecoverAndPersist.Setup(s => s.SelectFeaturesByIdCompanyProduct(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<int>())).Returns(feature);
            _mainContext.CompanyProductFeatureRecoverAndPersist.Setup(s => s.SelectFeaturesByIdCompanyProduct(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<int>())).Returns(feature);
            _mainContext.CompanyProductFeatureRecoverAndPersist.Setup(s => s.GetFeaturesByCompanyProductId(It.IsAny<int>(), It.IsAny<short>(), It.IsAny<IEnumerable<int>>())).Returns(new List<CompanyProductFeatureEntity>());
       
            var result = _mainContext.CompanyProductFeatureService.SelectFeaturesByIdCompanyProduct(1, 1, 1);

            Assert.AreEqual(result, feature);
            _mainContext.TempCacheMock.Verify(v => v.Add(key, feature, new TimeSpan(0, 15, 0), 1), Times.Once);
        }
    }
}
