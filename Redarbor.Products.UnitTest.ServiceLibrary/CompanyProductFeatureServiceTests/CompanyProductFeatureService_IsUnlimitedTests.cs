using Microsoft.VisualStudio.TestTools.UnitTesting;
using Redarbor.Master.Entities.Enums;
using Redarbor.Master.Entities.Product;
using Redarbor.Products.UnitTest.ServiceLibrary.Enums;
using Redarbor.Products.UnitTest.ServiceLibrary.Factories.CompanyProductFactory;
using Redarbor.Products.UnitTest.ServiceLibrary.Factories.CompanyProductFactory.Context.Scenarios;
using System.Collections.Generic;

namespace Redarbor.Products.UnitTest.ServiceLibrary.CompanyProductFeatureServiceTests
{
    [TestClass]
    public class CompanyProductFeatureService_IsUnlimitedTests
    {
        static CompanyProductFeatureServiceScenario _mainContext;

        [TestInitialize]
        public void SetUp()
        {
            _mainContext = CompanyProductFeatureServiceContextFactory.Create(CompanyProductServiceContextEnum.Simple);
        }

        [TestMethod]
        public void When_Called_With_Null_Company_Product_Returns_False()
        {
            var result = _mainContext.CompanyProductFeatureService.IsUnlimited(null, (short)ProductAmbitEnum.Offer);
            Assert.IsFalse(result);
        }

        [TestMethod]
        public void When_Called_With_Null_Company_Product_Features_Returns_False()
        {
            _mainContext.MainContext.CompanyProductExpected.Features = null;
            var result = _mainContext.CompanyProductFeatureService.IsUnlimited(_mainContext.MainContext.CompanyProductExpected, (short)ProductAmbitEnum.Offer);
            Assert.IsFalse(result);
        }

        [TestMethod]
        public void When_Called_Feature_No_Exist_Returns_False()
        {
            _mainContext.MainContext.CompanyProductExpected.Features = new List<CompanyProductFeatureEntity>();
            var result = _mainContext.CompanyProductFeatureService.IsUnlimited(_mainContext.MainContext.CompanyProductExpected, (short)ProductAmbitEnum.Offer);
            Assert.IsFalse(result);
        }

        [TestMethod]
        [DataRow(ProductAmbitEnum.Offer)]
        [DataRow(ProductAmbitEnum.OfferHiddenName)]
        [DataRow(ProductAmbitEnum.OfferHighlighted)]
        [DataRow(ProductAmbitEnum.OfferUrgent)]
        public void When_Called_Feature_Is_Not_Simultaneous_Returns_False(ProductAmbitEnum ambit)
        {
            _mainContext.MainContext.CompanyProductExpected.Features = new List<CompanyProductFeatureEntity>()
            {
                new CompanyProductFeatureEntity() { AmbitId = (short)ambit }
            };

            var result = _mainContext.CompanyProductFeatureService.IsUnlimited(_mainContext.MainContext.CompanyProductExpected, (short)ambit);

            Assert.IsFalse(result);
        }

        [TestMethod]
        [DataRow(ProductAmbitEnum.Offer)]
        [DataRow(ProductAmbitEnum.OfferHiddenName)]
        [DataRow(ProductAmbitEnum.OfferHighlighted)]
        [DataRow(ProductAmbitEnum.OfferUrgent)]
        public void When_Called_Feature_Is_Simultaneous_Returns_False(ProductAmbitEnum ambit)
        {
            _mainContext.MainContext.CompanyProductExpected.Features = new List<CompanyProductFeatureEntity>()
            {
                new CompanyProductFeatureEntity() { AmbitId = (short)ambit, IsUnlimited = true }
            };

            var result = _mainContext.CompanyProductFeatureService.IsUnlimited(_mainContext.MainContext.CompanyProductExpected, (short)ambit);

            Assert.IsTrue(result);
        }
    }
}
