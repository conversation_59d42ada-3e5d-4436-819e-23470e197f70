using Redarbor.Core.Stack.Contracts.ServiceLibrary;
using Redarbor.Core.Stack.Library.DomainServiceContracts;

namespace Redarbor.Core.Stack.Impl.ServiceLibrary
{
    public class StackInvoiceService : IStackInvoiceService
    {
        private readonly IStackInvoiceRecoverAndPersits _stackInvoiceRecoverAndPersist;

        public StackInvoiceService(IStackInvoiceRecoverAndPersits stackInvoiceRecoverAndPersist)
        {
            _stackInvoiceRecoverAndPersist = stackInvoiceRecoverAndPersist;
        }
        public void Add(int invoiceId, short portalId)
        {
            _stackInvoiceRecoverAndPersist.Add(invoiceId, portalId);
        }
    }
}
