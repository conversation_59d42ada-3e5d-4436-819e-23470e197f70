using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Core.Shared.Contracts.ServiceLibrary;
using Redarbor.Core.Stack.Contracts.ServiceLibrary;
using Redarbor.Core.Stack.Contracts.ServiceLibrary.DTO;
using Redarbor.Master.Entities.Enums;
using Redarbor.RabbitMQ.Abstractions;
using Redarbor.RabbitMQ.Core;
using System;

namespace Redarbor.Core.Stack.Impl.ServiceLibrary
{
    public class OfferModerationService : IOfferModerationService
    {
        private readonly IProducerRabbitMQ _producerRabbitMQ;
        private readonly IPortalConfigurationService _portalConfigurationService;
        private readonly IConfigurationService _configurationService;
        private readonly IExceptionPublisherService _exceptionPublisherService;

        public OfferModerationService(
            IProducerRabbitMQ producerRabbitMQ,
            IPortalConfigurationService portalConfigurationService,
            IConfigurationService configurationService,
            IExceptionPublisherService exceptionPublisherService
            )
        {
            _producerRabbitMQ = producerRabbitMQ;
            _portalConfigurationService = portalConfigurationService;
            _configurationService = configurationService;
            _exceptionPublisherService = exceptionPublisherService;

            RabbitMQBuilder.AddRedarborRabbitMQ(x => { x.SetApiEndpoint(_configurationService.AppSettings["ApiQueueRabbit"]); });
        }

        public bool Publish(PublishOfferDTO publishOfferDTO)
        {
            short idApp = 0;
            try
            {
                if (_configurationService.AppSettings["APP_ID"] != null)
                {
                    short.TryParse(_configurationService.AppSettings["APP_ID"], out idApp);
                }

                _producerRabbitMQ.Publish(new Redarbor.Master.Entities.Moderation.QueuePublishConfigEntity()
                {
                    IdOffer = publishOfferDTO.IdOffer,
                    IdPortal = publishOfferDTO.IdPortal,
                    IdCompany = publishOfferDTO.IdCompany,
                    IdApplication = idApp,
                    IsPostModeration = true,
                    IdOfferActionEnum = publishOfferDTO.OfferActionId,
                    ExperienceYears = publishOfferDTO.ExperienceYears,
                    UrlRewrite = publishOfferDTO.UrlReWrite

                }, new RabbitMQ.Model.ProducerDeliverEventArgs()
                {
                    AppId = idApp,
                    QueueId = _portalConfigurationService.GetPortalConfiguration(publishOfferDTO.IdPortal, (short) ApplicationEnum.AreaEmpresa).AEPortalConfig.IdQueueRabbit,
                    Persistent = true,
                    RoutingKey= "comercial"
                    
                });

                return true;
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.PublishWarning(ex, "OfferModerationService - Publish",null, publishOfferDTO.IdPortal);
                return false;
            }
        }
    }
}
