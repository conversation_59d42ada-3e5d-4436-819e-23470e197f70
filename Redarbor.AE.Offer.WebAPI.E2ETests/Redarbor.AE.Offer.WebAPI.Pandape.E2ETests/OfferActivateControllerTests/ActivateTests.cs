using Redarbor.AE.Offer.WebAPI.Pandape.E2ETests.Enums;
using Redarbor.AE.Offer.WebAPI.Pandape.E2ETests.TestFixtures;
using Redarbor.Offer.API.Consumer;
using Redarbor.Offer.API.Consumer.Models.ActivateOffer;
using System.Net;

namespace Redarbor.AE.Offer.WebAPI.Pandape.E2ETests.OfferActivateControllerTests
{
    [Collection("Activate")]
    public class ActivateTests
    {
        private readonly TestsFixture _fixture;
        const int TIMEOUT = 5;
        const int ID_ORIGIN = 1;

        public ActivateTests(TestsFixture fixture)
        {
            _fixture = fixture;
            _fixture.SetParamsEndpoint(new Models.ParamEndpointModel(API.RedarborAEOfferApi.Activate(_fixture.BaseAddress, EnvironmentManager.OfferResult?.Id ?? 0, API.ApiVersion.v1, -1), TIMEOUT, Enums.HttpMethodsEnum.Post));
        }

        [Fact(DisplayName = "ActivateTests_Without_IdOrigin_ResponseBadRequest")]
        [Trait("Category", "E2E")]
        public async void ActivateTests_Without_IdOrigin_ResponseBadRequest()
        {
            if (!CheckShouldRunNextTests() || !await CheckEndPoint())
                return;

            try
            {
                var response = await _fixture.ApiClient.PostAsync(API.RedarborAEOfferApi.Activate(_fixture.BaseAddress, EnvironmentManager.OfferResult?.Id ?? 0, API.ApiVersion.v1, -1), new StringContent(""));
                Assert.Equal(HttpStatusCode.BadRequest, response.StatusCode);
            }
            catch (Exception)
            {
                EnvironmentManager.ShouldRunNextTests = false;
                throw;
            }
        }

        [Fact(DisplayName = "ActivateTests_Without_IdOffer_ResponseBadRequest")]
        [Trait("Category", "E2E")]
        public async void ActivateTests_Without_IdOffer_ResponseBadRequest()
        {
            if (!CheckShouldRunNextTests() || !await CheckEndPoint())
                return;

            try
            {
                var response = await _fixture.ApiClient.PostAsync(API.RedarborAEOfferApi.Activate(_fixture.BaseAddress, 0, API.ApiVersion.v1, ID_ORIGIN),
                    new StringContent(System.Text.Json.JsonSerializer.Serialize(new ActivateOfferRequest() { PortalId = EnvironmentManager.OfferResult?.IdPortal ?? 0 })));
                Assert.Equal(HttpStatusCode.UnsupportedMediaType, response.StatusCode);
            }
            catch (Exception)
            {
                EnvironmentManager.ShouldRunNextTests = false;
                throw;
            }
        }

        [Fact(DisplayName = "ActivateTests_Without_Object_ResponseBadRequest")]
        [Trait("Category", "E2E")]
        public async void ActivateTests_Without_Object_ResponseBadRequest()
        {
            if (!CheckShouldRunNextTests() || !await CheckEndPoint())
                return;

            try
            {
                var response = await _fixture.ApiClient.PostAsync(API.RedarborAEOfferApi.Activate(_fixture.BaseAddress, EnvironmentManager.OfferResult?.Id ?? 0, API.ApiVersion.v1, ID_ORIGIN), new StringContent(""));
                Assert.Equal(HttpStatusCode.BadRequest, response.StatusCode);
            }
            catch (Exception)
            {
                EnvironmentManager.ShouldRunNextTests = false;
                throw;
            }
        }

        [Fact(DisplayName = "ActivateTests_ResponseOk")]
        [Trait("Category", "E2E")]
        public async void ActivateTests_ResponseOk()
        {
            //falta por hacer

            //if (!CheckShouldRunNextTests() || !await CheckEndPoint())
            //    return;

            //try
            //{
            //    var response = await _fixture.ApiClient.PostAsync(API.RedarborAEOfferApi.SetUrgent(_fixture.BaseAddress, EnvironmentManager.OfferResult?.Id ?? 0, API.ApiVersion.v1, ID_ORIGIN),
            //        new StringContent(System.Text.Json.JsonSerializer.Serialize(new SetUrgentOfferRequest(true, EnvironmentManager.OfferResult?.CreatedBy ?? 0, EnvironmentManager.OfferResult?.IdPortal ?? 0))));
            //    Assert.Equal(HttpStatusCode.BadRequest, response.StatusCode);
            //}
            //catch (Exception)
            //{
            //    EnvironmentManager.ShouldRunNextTests = false;
            //    throw;
            //}

            Assert.True(true);
        }
        private bool CheckShouldRunNextTests()
        {
            if (!EnvironmentManager.ShouldRunNextTests)
            {
                Assert.Fail("No se deben ejecutar los siguientes tests");
            }

            if (EnvironmentManager.OfferResult == null && EnvironmentManager.OfferResult?.Id <= 0)
            {
                Assert.Fail("No se creo correctamente la oferta y por lo tanto no existe ningún elemento al que aplicar modificaciones");
            }

            return true;
        }

        private async Task<bool> CheckEndPoint()
        {
            await _fixture.InizializateCheckEndPoint();

            if (_fixture.EndpointStatus != EndpointStatusEnum.IsAvailable)
            {
                if (_fixture.EndpointStatus == EndpointStatusEnum.Timeout)
                    Assert.Fail("El endPoint no se ejecuta debido a timeOut");
                else if (_fixture.EndpointStatus == EndpointStatusEnum.NotFound)
                    Assert.Fail("El endPoint no se ejecuta debido a NotFound");

                Assert.Fail("El endPoint no se ejecuta correctamente");
            }

            return true;
        }
    }
}
