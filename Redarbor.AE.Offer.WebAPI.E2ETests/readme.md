# 📄 **README - Pruebas E2E para Redarbor.AE.Offer.WebAPI (TESTS secuenciales en orden)**

---
## **Sin terminar**, solo se han añadido badrequests y la estructura pero falta hacer los response OK.
## 🌟 **Objetivo**

Estos proyectos de pruebas E2E validan el correcto funcionamiento de la Web API en diferentes entornos (**Staging**, **Beta** y **Production**), actuando como **contrato** para los consumidores de la API.
Cada consumidor tiene su proyecto de tests que sirve como contrato por lo que cada uno:
1. Debería usar la versión correspondiente de nuget consumidor Redarbor.Offer.API.Consumer (https://redarbor.visualstudio.com/Company_project/_git/Redarbor.Offer?path=/Redarbor.Offer/src/Redarbor.Offer.API.Consumer). 
2. Debe utilizar empresas de pruebas para poder llevar a cabo el CRUD y que el resultado no ofrezca una gran alteración en los datos publicados. Por ejemplo: los datos para crear la oferta deben ser creibles y la oferta debe ser archivada al final del procedimiento.
3. **El orden de las pruebas es importante**. Siguen un orden por colección (cada clase de test pertenece a una colección) y por orden de test dentro de la colección usando el TestCaseOrder.
4. Cada endpoint tiene un check del endpoint antes de ejecutar los test de manera que se pueda evitar estar esperando largos timeouts para cada test. (Si el check del endpoint detecta un timeout definido por el test por ejemplo de 5 segunds o devuelve un status NotFound no se llamará de nuevo a ese mismo endpoint) 
5. También tiene un CheckShouldRunNextTests que indica si los siguientes test se pueden ejecutar. 
6. Cada colección de tests puede hacer uso de un fixture base (TestsFixture) que se encarga de inicializar el cliente http y de realizar las llamadas a los endpoints o uno específico que herede de TestsFixture. Este fixture 
específico se puede usar para inicializar datos de prueba o para realizar acciones específicas antes de ejecutar los tests.


## 🌟 **Para añadir una nueva colección de tests**

Para poder añadir una nueva colección de test se debe seguir los siguientes pasos:
1. Crear una nueva clase de test en la carpeta Collections:
 
    ``` [CollectionDefinition("Highlighted", DisableParallelization = true)]
        public class Highlighted : ICollectionFixture<TestsFixture> { } 
    ```
2. Añadir en el orden de la clase CustomTestCollectionOrderer:
``` 
 public class CustomTestCollectionOrderer : ITestCollectionOrderer
    {
        public IEnumerable<ITestCollection> OrderTestCollections(IEnumerable<ITestCollection> testCollections)
        {
            var orderedCollections = new List<string>
            {
                "Publish",
                "Edit",
                "Highlighted"
            };

            return testCollections.OrderBy(tc => orderedCollections.IndexOf(tc.DisplayName) >=0 ? orderedCollections.IndexOf(tc.DisplayName) : int.MaxValue);            
        }
    }
```

3. Añadir atributo Collection en la propia clase de tests.

``` [Collection("Highlighted")]
	public class HighlightedTests
	{
		private readonly TestsFixture _fixture;

		public HighlightedTests(TestsFixture fixture)
		{
			_fixture = fixture;
		}
	}
```

## 🌟 **Para añadir un orden concreto para los tests de una clase**
Para poder añadir un orden de test por clase se debe seguir los siguientes pasos:
1. Añadir atributo: ``` [TestCaseOrderer("Redarbor.AE.Offer.WebAPI.Pandape.E2ETests.TestCaseOrder.SequentialPublishTestCaseOrderer", "Redarbor.AE.Offer.WebAPI.Pandape.E2ETests")] ```
2. Crear nueva clase que implemente ITestCaseOrderer con el orden de tests:
 
``` public class SequentialPublishTestCaseOrderer : ITestCaseOrderer
    {
        public IEnumerable<TTestCase> OrderTestCases<TTestCase>(IEnumerable<TTestCase> testCases) where TTestCase : ITestCase
        {
            var testCasesOrder = new List<string>
            {
                "Test1_c",
                "Test1_A",
                "Test1_b"
            };

            return testCases.OrderBy(tc => testCasesOrder.IndexOf(tc.DisplayName) >= 0 ? testCasesOrder.IndexOf(tc.DisplayName) : int.MaxValue);
        }
    } 
```

## 🌟 **Versión de Nuget por consumidor**

En los test se añade directament la librería hasta que el consumidor la incorpore, en ese momento se deberá instalar la misma versión que tenga el consumidor en el proyecto de tests específico.

| Consumidor | Versión Nuget |
|------------|---------------|
| Pandapé    | 4.3.2         | 


---

## 🚀 **Ejecución de pruebas en local**

### 1️⃣ **Configuración del entorno y lanzar manualmente**

Las pruebas permiten especificar el entorno deseado (**Staging**, **Beta** o **Production**) mediante el archivo `launchSettings.json`. Solo es necesario cambiar la variable `TestEnvironment` en el perfil deseado.
**Recordar usar la vpn correspondiente**
#### 🔧 **Modificación de `launchSettings.json`**

Ubica o crea el archivo `Properties/launchSettings.json` en el proyecto de pruebas E2E y ajusta el perfil correspondiente:

```json
{
  "profiles": {
    "Redarbor.AE.Product.WebAPI.Hub.E2ETests": {
      "commandName": "Project",
      "environmentVariables": {
        "TestEnvironment": "Production"
      }
    }
  }
}
```

botón derecho sobre el proyecto de pruebas > ejecutar pruebas

### 2️⃣ **Opcional:Lanzar desde terminal**

Se puede lanzar abriendo una terminal del proyecto específico y poniendo el siguiente comando:
$env:TestEnvironment = "Staging"
dotnet test --configuration Release

### 3️⃣ **Ejecutar todas las pruebas unitarias o E2E**

## 🚀 **Archivos `.runsettings` disponibles**

### 🔧 **1️⃣ E2E.runsettings**
Ejecuta **solo las pruebas E2E**:

```xml
<?xml version="1.0" encoding="utf-8"?>
<RunSettings>
  <RunConfiguration>
    <TestCaseFilter>Category=E2E</TestCaseFilter>
  </RunConfiguration>
</RunSettings>
```

---

### 🔧 **2️⃣ UnitOnly.runsettings**
Ejecuta **todas las pruebas excepto las E2E**:

```xml
<?xml version="1.0" encoding="utf-8"?>
<RunSettings>
  <RunConfiguration>
    <TestCaseFilter>Category!=E2E</TestCaseFilter>
  </RunConfiguration>
</RunSettings>
```

---

## 🏃 **Cómo alternar entre archivos `.runsettings`**

### 🔄 **Paso 1: Seleccionar el archivo `.runsettings` deseado**

1. En Visual Studio, ve al menú superior:  
   **🧪 Pruebas > Configuración de prueba > Seleccionar archivo de configuración**.

2. En el explorador que se abre, selecciona uno de los archivos:
   - `E2E.runsettings` para ejecutar **solo E2E**.
   - `UnitOnly.runsettings` para ejecutar **solo pruebas unitarias**.
   - Si quieres lanzar solo los tests de un proyecto es tan fácil como darle botón derecho al proyecto y seleccionar ejecutar pruebas.

3. Visual Studio recordará el archivo seleccionado hasta que lo cambies.

---

### 🔍 **Paso 2: Verificar el archivo activo**

- Revisa el menú: **Pruebas > Configuración de prueba**.
- El archivo actualmente seleccionado tendrá una marca de verificación (`✔`).

---

### ⚡ **Paso 3: Limpiar y compilar el proyecto para luego ejecutar las pruebas**

Una vez seleccionado el archivo `.runsettings` deseado, limpiado la solución y recompilado:
- **En el Explorador de pruebas solo deben aparecer las pruebas deseadas**
- **Botón derecho sobre el proyecto de pruebas** > **Ejecutar pruebas**.





