using Dapper;
using Redarbor.AE.Procs.ObsoleteCompaniesManager.Application.Abstractions;
using Redarbor.AE.Procs.ObsoleteCompaniesManager.Application.Models;
using Redarbor.ObsoleteCompaniesManager.Application.Abstractions;
using Redarbor.Procs.Domain.Enums;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;

namespace Redarbor.AE.Procs.ObsoleteCompaniesManager.Infrastructure.Repositories
{
    public class InvoiceRepository : IInvoiceRepository
    {
        private readonly IMasterConnectionsRepository _masterConnectionsRepository;

        public InvoiceRepository(IMasterConnectionsRepository masterConnectionsRepository)
        {
            _masterConnectionsRepository = masterConnectionsRepository;
        }

        public async Task<IEnumerable<InvoiceDTO>> GetInvoicesBetweenDateAsync(PortalEnum idPortal, IEnumerable<int> companyIds, DateTime dateIni)
        {            
            using var connection = await _masterConnectionsRepository.GetMasterConnectionAsync(idPortal);
            if (connection.State != ConnectionState.Open) connection.Open();

            var companyList = string.Join(",", companyIds);

            var sql = $@"SELECT inv.idcompany as IdCompany, 
                                inv.idPortal as IdPortal, 
                                COALESCE(COUNT(inv.idcompany), 0) AS CountInvoices
                        FROM invoices inv
                        LEFT JOIN invoices i
                            ON inv.idcompany = i.idcompany
                            AND inv.idportal = i.idportal
                            AND i.statusId = 2
                            AND i.date_add > fn_portal_date(@DateIni, @IdPortal) 
                        WHERE inv.idcompany IN ({companyList})
                            AND inv.idportal = @IdPortal
                            AND inv.statusId = 2
                            AND inv.date_add < fn_portal_date(@DateIni, @IdPortal)
                            AND i.idcompany IS NULL      
                        GROUP BY 1, 2";

            return (await connection.QueryAsync<InvoiceDTO>(sql, new { IdPortal = idPortal, DateIni = dateIni })).ToList();
        }

        public async Task<IEnumerable<InvoiceDTO>> GetInvoicesAsync(PortalEnum idPortal, IEnumerable<int> companyIds)
        {
            using var connection = await _masterConnectionsRepository.GetMasterConnectionAsync(idPortal);
            if (connection.State != ConnectionState.Open) connection.Open();
            var sql = $@"SELECT `idcompany` AS IdCompany, `idportal` AS IdPortal, COUNT(idcompany) AS CountInvoices
                    FROM `invoices`
                    WHERE `idportal` = @IdPortal
                    AND `idcompany` IN @CompanyList
                    AND `statusId` = 2
                    GROUP by 1,2";
            return (await connection.QueryAsync<InvoiceDTO>(sql, new { IdPortal = idPortal, CompanyList = companyIds })).ToList();
        }
    }
}
