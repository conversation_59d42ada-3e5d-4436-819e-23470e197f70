using System;
using System.Collections.Generic;
using Redarbor.Procs.Domain.Enums;
using System.Threading.Tasks;
using Redarbor.AE.Procs.ObsoleteCompaniesManager.Application.Models;

namespace Redarbor.AE.Procs.ObsoleteCompaniesManager.Application.Abstractions
{
    public interface IInvoiceRepository
    {
        Task<IEnumerable<InvoiceDTO>> GetInvoicesAsync(PortalEnum idPortal, IEnumerable<int> companyIds);
        Task<IEnumerable<InvoiceDTO>> GetInvoicesBetweenDateAsync(PortalEnum idPortal, IEnumerable<int> companyIds, DateTime dateIni);
    }
}
