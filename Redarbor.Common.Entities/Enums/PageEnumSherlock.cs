
namespace Redarbor.Common.Entities.Enums
{
    public enum PageEnumSherlock
    {
        LayoutPublic = 1,
        LayoutPrivate = 2,
        Home = 3,
        Login = 4,
        AccountBlocked = 5,
        AccountUsedCompany = 6,
        PublishOffers = 7,
        EditOffers = 8,
        Vision = 9,
        Dashboard = 10,
        DataCompany = 11,
        Master = 12,
        SearchCandidates = 13,
        ExclusionMatches = 14,
        ShareOfferInSocialNetwork = 15,
        ConfigurationCompany = 16,
        ConfigurationCompanyMultiposting = 17,
        GridOffers = 18,
        MultiPosting = 19,
        ConfigurationCompanyUsers = 20,
        CVDetailCT = 21,
        OfferDashboard = 22,
        OfferLeads = 23,
        OfferSourcing = 24,
        CreditsUsers = 25,
        UserRoleEntity = 26,
        UserStatusEnum = 27,
        OfferTeam = 28,
        RecoverPassword = 29,
        Reports = 30,
        ConfigurationCompanyAgencies = 31,
        CvLeadsGrid = 32,
        CVDetail = 33,
        ConfigurationCompanyCandidateSourceReferenced = 34,
        PrintOffer = 35,
        AddCV = 36,
        ConfigurationCompanyCandidateSourceOther = 37,
        Messages = 38,
        Calendar = 39,
        AnalyticsRecruitmentSources = 40,        
        Interview = 41,        
        InterviewKit = 42,
        OfferStages = 43,
        ConfigurationCompanyStages = 44,
        ConfigurationTest = 45,
        AnalyticsReportsTeam = 46,
        GoogleCalendarConfig = 47,
        ConfigurationCustomEmailTemplates = 48,
        ConfigurationCompanyPersonalizedOptions = 49,
        ConfigureCompanyConfigurationCvDuplicate = 50,
        NotificationsConfig = 51,
        NotificationsGrid = 52,
        CVMerge = 53,
        LayoutPublicAccess = 54,
        ConfigurationTrackingActions = 55,
        ConfigurationReports = 56,
        Invoice = 57,
        ModifyAccount = 58,
        ListDownloadCvs = 59,
        ExtractionCvConfig = 60,
        DownloadCvs = 61,
        Support = 62,
        OptionsDiscard = 63,
        LegalWarning = 64,
        PrivacyPolicy = 65
    }
}