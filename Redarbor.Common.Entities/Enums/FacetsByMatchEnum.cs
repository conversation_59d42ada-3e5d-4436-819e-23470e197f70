

namespace Redarbor.Common.Entities.Enums
{
        public enum FacetsByMatchEnum
        {
            //deprecated
            idioms_search,
            candidate_localization,
            candidate_city,
            candidate_study_level,
            categories_search,
            candidate_age,
            candidate_gender,
            idcompanyfolder,
            showed,
            idsalary,
            kq_excluding,
            //NEW ENTITY
            languageIds,
            regionId,
            cityId,
            educationLevelId,
            categoryIds,
            ageRangeId,
            genderId,
            folderId,
            viewed,
            salaryId,
            excluding,
            commented,
            rating,
            hasphoto,
            ageMin,
            ageMax,
            nationality,
            offerId
        }

    }
