namespace Redarbor.Common.Entities.Enums
{
    public enum PortalConfigFeaturesEnum
    {
        ShowSalariesSection = 2,
        ShowBannerSherlock = 4,
        BannerSherlockCode = 5,
        ShowSkypeContact = 6,
        ShowPublicHomeCVSearch = 7,
        ShowComplaintsBook = 8,
        HasFreeFeaturePhone1Verification = 9,
        HasFreeFeatureVideoPresentation = 10,
        HasFreeFeatureCardPresentation = 11,
        IsPremiumActive = 12,
        LimitationPurchase = 13,
        AmazonBucketCompanyFiles = 14,
        AmazonAccesKeyCompanyFiles = 15,
        AmazonSecretKeyCompanyFiles = 16,
        AmazonBucketInvoiceFiles = 17,
        AmazonAccesKeyInvoiceFiles = 18,
        AmazonSecretKeyInvoiceFiles = 19,
        AmazonBucketExportedCvs = 20,
        AmazonAccesKeyExportedCvs = 21,
        AmazonSecretKeyExportedCvs = 22,
        AmazonBucketUserFiles = 23,
        AmazonAccesKeyUserFiles = 24,
        AmazonSecretKeyUserFiles = 25,
        UrlZendeskCT = 26,
        ACCMailZendeskTicket = 27,
        ACCTokenZendeskTicket = 28,
        RecipientZendeskTicket = 29,
        StatusZendeskTicket = 30,
        URLZendeskTicketSherlock = 31,
        ACCMailZendeskTicketSherlock = 32,
        ACCTokenZendeskTicketSherlock = 33,
        RecipientZendeskTicketSherlock = 34,
        StatusZendeskTicketSherlock = 35,
        AccId1Rbs = 36,
        PaypalUrlReturn = 37,
        ServerGeolocationUrl = 38,
        AmazonProfileName = 39,
        McReturnPageTxt = 40,
        IsNewSupportWithSF = 41,
        IsPaymentsTPV = 42,
        ShowPaymentsTPVWorldPay = 43,
        ShowPaymentsTPVPayU = 44,
        ShowPaymentsTPVPayPal = 45,
        CaptchaInRegisterCompany = 46,
        ShowNitWithValueInPayU = 47,
        LandingPacksWithExtraInfo = 49,
        ShowCompanyCardToken = 51,
        ActiveRecurrentPayment = 52,
        PostPublish = 53,
        AutoPopupConvertToCompleteByCompany = 54,
        AutoPopupConvertToCompleteByOffer = 55,
        AutoPopupConvertToCompleteByCv = 56,
        AutoPopupConvertToCompleteMinutes = 57,
        IndexOldMatchElastic = 58,
        IsNewMatchElasticSearch = 59,
        IndexNewMatchElastic = 60,
        DaysPromotion = 61,
        ShowCookiesConsent = 62,
        IsPortalWithAssignedSalesTeam = 64,
        ExternalUrl = 65,
        RangeDaysReport = 66,
        UseOldMatchElasticByConsumer = 67,
        PostPublishWithConsumables = 68,
        ApiQueueRabbit = 69,
        IdQueueRabbit = 70,
        IsNewDesignPublicHome = 71,
        //MultiPurchase = 72,
        UseOnlineNotificationStack = 73,
        ShowBasicOffersProduct = 74,
        IdQueueRabbitMailCompanyProductProduced = 75,
        RemoveScriptAnalytics = 76,
        IsActiveLogJsWithMuscula = 77,
        ShowNewMatchesFilterDesign = 78,
        MaxCVsSuggested = 79,
        MinAdequacyCVsSuggested = 80,
        IdQueueRabbitMailCompanyAuthorizationWelcome = 81,
        ShowCodeVerificationPopup = 82,
        HasHubSpot = 83,
        HubSpotContact = 84,
        MatchesPageSize = 86,
        ShowPaymentReasonsKO = 87,
        HasOnePlusOnePack = 88,
        PromoOffers = 89,
        ViewMatchesByNumberBasicOffer = 90,
        ValidateUserByCodeDate = 92,
        IdQueueRabbitMailCompanyAuthorizationRemember = 93,
        MinimumTimeShowRememberActiveUser = 94,
        NumHoursToShowCaptchaInCompanyEdition = 95,
        AllowPayWithTokenReusableAutologinBackweb = 96,
        RegisterVerificationWithNit = 97,
        ReportCandidatePhoto = 98,
        EnableComercialContactPopup = 99,
        HasPurchaseActivity = 100,
        ActivateCompanyCvsConsumible = 101,
        EnablePostalCode = 103,
        DisableFreemiumAfterPurchaseActivity = 104,
        SimplifiedRegisterWithCP = 105,
        EnableTestABRegister = 106,
        ActivateInboxMessengerJobadsChatSystem = 107,
        DescriptionInputInRegister = 108,
        ShowDistanceMatchesFilter = 109,
        AutoConvertToComplete = 110,
        PublishFromWelcomeSimplified = 112,
        ShowAllProdsToConvToPay = 113,
        EnableZendeskNewAccountTicket = 114,
        ZendeskCustomerIdCustomFieldId = 115,
        ZendeskPortalCompanyNameFieldId = 116,
        ZendeskPortalIdCustomFieldId = 117,
        ZendeskPortalPhoneFieldId = 118,
        ZendeskAccountTypeCustomFieldId = 119,
        ZendeskCompanyLoggedCustomFieldId = 120,
        ZendeskCountryIdCustomFieldId = 121,
        ZendeskCTBrandId = 122,
        ShowImproveOfferButton = 123,
        ShowFreemiumPublishedLanding = 124,
        ShowOfferViews = 126,
        ShowNewLandingRegister = 127,
        ProgressiveChatFreemium = 128,
        HideBasicOffersNum = 131,
        DictionaryExcludeEmploymentTypesId = 132,
        ShowBBDDCvPromoTag = 133,
        ShowVideoPresentationPopup = 134,
        TestABPrivateHomePrices = 135,
        TraceDifferentTotalMatchesExceptions = 136,
        TalentView3DFilter = 137,
        AccessToAllCvsOnPostPublishOption = 138,
        ActivatePrimeProduct = 139,
        ShowFreemiumHomeV2 = 140,
        IdQueueRabbitTotalNewDecreaser = 141
    }
}
