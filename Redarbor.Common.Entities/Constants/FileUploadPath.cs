namespace Redarbor.Common.Entities.Constants
{
    public static class FileUploadPath
    {
        public const string CompanyUrlRewritePrefix = "/empresas/ofertas-de-trabajo-de-";
        public const string CompanyUrlRewritePrefixBJ = "/companies/jobs-of-";
        public const string CvUrlRewritePrefixColombia = "/hojas-de-vida/hoja-de-vida-de-";
        public const string CvUrlRewritePrefixMexico = "/curriculums/curriculum-de-";
        public const string CvUrlRewritePrefixBJ = "/resumes/cv-of-";
        public const string OfferUrlRewritePrefix = "/ofertas-de-trabajo/oferta-de-trabajo-de-";
        public const string OfferUrlRewritePrefixBJ = "/jobs/job-of-";
        public const string CompanyPathAws = "logos/empresas/";
        public const string CandidatePathAws = "logos/candidates/";
        public const string DocumentIdentifyPathAws = "documentos/empresas/";

    }
}
