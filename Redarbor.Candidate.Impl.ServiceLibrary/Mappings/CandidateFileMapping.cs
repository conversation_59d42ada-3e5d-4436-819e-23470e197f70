using Redarbor.Candidates.API.Consumer.Models.Dtos;
using Redarbor.Master.Entities.Candidate;

namespace Redarbor.Candidate.Impl.ServiceLibrary.Mappings
{
    public static class CandidateFileMapping
    {
        public static CandidateFileEntity ToCandidateFileEntity(this CandidateFileApiDto model)
        {
            return new CandidateFileEntity
            {
                Id = model.Id,
                CandidateId = model.CandidateId,
                FileName = model.FileName,
                DateAdd = model.DateAdd,
                DateUpd = model.DateUpd,
                DateDel = model.DateDel,
                StatusId = model.StatusId,
                IsPrincipal = model.IsPrincipal,
                DocTypeId = model.DocTypeId,
                ExtensionId = model.ExtensionId,
                PortalId = model.PortalId,
                Path = model.Path
            };
        }       
    }
}
