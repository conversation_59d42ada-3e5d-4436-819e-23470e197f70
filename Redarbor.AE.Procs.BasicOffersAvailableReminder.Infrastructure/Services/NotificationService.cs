using Redarbor.AE.Procs.BasicOffersAvailableReminder.Application.Abstractions;
using Redarbor.AE.Procs.BasicOffersAvailableReminder.Application.Models;
using Redarbor.RabbitMQ.Abstractions;
using Redarbor.RabbitMQ.Core;
using Redarbor.RabbitMQ.Model.Contracts;
using System;
using System.Threading.Tasks;

namespace Redarbor.AE.Procs.BasicOffersAvailableReminder.Infrastructure.Services
{
    public class NotificationService : INotificationService
    {
        private readonly ApplicationConfigData _applicationConfigData;

        private readonly IProducerRabbitMQ _rabbit;

        private readonly IExceptionsService _exceptionService;

        private readonly IRabbitMQExceptionLogger _rabbitMQExceptionLogger;

        public NotificationService(
            IExceptionsService exceptionService,
            ApplicationConfigData applicationConfigData,
            IRabbitMQExceptionLogger rabbitMQExceptionLogger)
        {
            _exceptionService = exceptionService;
            _applicationConfigData = applicationConfigData;
            _rabbitMQExceptionLogger = rabbitMQExceptionLogger;

            RabbitMQBuilder.AddRedarborRabbitMQ(x => x.SetApiEndpoint(applicationConfigData.RabbitMqEndpoint));
            _rabbit = new ProducerRabbitMQBuilder().Create(_rabbitMQExceptionLogger);
        }

        public async Task SendNotificationAsync(NotificationDto notification)
        {
            var tcs = new TaskCompletionSource<bool>();
            var task = tcs.Task;

            await Task.Factory.StartNew(async () =>
            {
                try
                {
                    _rabbit.Publish(
                    notification,
                    new RabbitMQ.Model.ProducerDeliverEventArgs()
                    {
                        AppId = _applicationConfigData.AppId,
                        QueueId = _applicationConfigData.RabbitQueueId,
                    });
                    tcs.SetResult(true);
                }
                catch (Exception exception)
                {
                    await _exceptionService.ProcessExceptionASync(exception);
                    tcs.SetResult(false);
                }
            });

            await task;
        }
    }
}
