using Dapper;
using MySqlConnector;
using Redarbor.AE.Procs.BasicOffersAvailableReminder.Application.Abstractions;
using Redarbor.AE.Procs.BasicOffersAvailableReminder.Application.Models;
using Redarbor.AE.Procs.BasicOffersAvailableReminder.Domain.Entities;
using Redarbor.AE.Procs.BasicOffersAvailableReminder.Domain.Enums;
using Redarbor.AE.Procs.BasicOffersAvailableReminder.Infrastructure.Extensions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Redarbor.AE.Procs.BasicOffersAvailableReminder.Infrastructure.Repositories
{
    public class PortalSetRepository : IPortalSetRepository
    {
        private readonly string _connectionString;

        private readonly ApplicationConfigData _configData;

        public PortalSetRepository(
            string connectionString,
            ApplicationConfigData configData)
        {
            _configData = configData;
            _connectionString = connectionString;
        }

        public async Task<ICollection<PortalSet>> GetPortalSetsAsync()
        {
            var sql = @"SELECT
                        `p`.`app_set` AS `SetId`,
                        `e`.`db_connection_master` as `MasterConnectionString`,
                        `p`.`id` AS `Id`,
                        `p`.`portal` AS `Name`,
                        `p`.`url_company` AS `CompanyUrl`,
                        `p`.`url_web` AS `CandidateUrl`,
                        `p`.`currency` AS `Currency`,
                        `p`.`language_code` AS `LanguageCode`
                        FROM `portal` `p`
                        INNER JOIN `portal_database_engine` `e`
                        ON `e`.`idportal` = `p`.`id` WHERE `p`.`app_set` IN @SetIds
                        ";

            if (_configData.PortalsOfInterest.Any())
            {
                sql += "AND `e`.`idportal` IN @PortalsOfInterest";
            }

            var connection = new MySqlConnection(_connectionString);
            connection.Open();

            var setsDictionary = new Dictionary<DatabaseSet, PortalSet>();
            return (await connection.QueryAsync<PortalSet, Portal, PortalSet>(sql, (set, portal) =>
            {
                if (!setsDictionary.ContainsKey(set.SetId))
                {
                    set.MasterConnectionString = set.MasterConnectionString.ModifyConnectionStringOptions();
                    setsDictionary.Add(set.SetId, set);
                }

                var portalSet = setsDictionary[set.SetId];

                if (portalSet.Portals is null)
                {
                    portalSet.Portals = new List<Portal>();
                }

                if (!portalSet.Portals.Any(x => x.Id == portal.Id))
                {
                    portalSet.Portals.Add(portal);
                }

                return portalSet;
            }, new { SetIds = Enum.GetValues<DatabaseSet>(), _configData.PortalsOfInterest }, splitOn: "Id")).Distinct().ToList();

        }
    }
}
