using Microsoft.VisualStudio.TestTools.UnitTesting;
using System.Collections.Generic;
using System.Text.RegularExpressions;

namespace Redarbor.UnitTest.Common.Entities
{
    [TestClass]
    public class ValidationRegexIsValidFormatTextTest
    {
        const string SPECIAL = @"/([<>\?\*\\\""\-{}\[\]%&~¬;_|])+/";
        const string REGEX = @"/^[A-Z0-9\s]*$/";
        const string REGEX_CAPITAL_LETTERS_TOGETHER = @"/([A-Z]){5,}/";

        [TestMethod]
        public void VerifyRegex_ContainsSimbols_True()
        {     
            Regex regexSpecial = new Regex(SPECIAL);
            foreach (var stringItem in GetStringsWithSpecialChars())
            {
                Assert.IsFalse(regexSpecial.Match(stringItem).Success, $"'{stringItem}' should be matched by contains special regex");
            }
        }

        private List<string> GetStringsWithSpecialChars()
        {
            return new List<string>()
            {
                "Casa?",
                "Coche&",
                "Tren_ Avion"
            };
        }

        [TestMethod]
        public void VerifyRegex_ContainsSimbols_False()
        {
            Regex regexSpecial = new Regex(SPECIAL);
            foreach (var stringItem in GetStringsWithOutSpecialChars())
            {
                Assert.IsFalse(regexSpecial.Match(stringItem).Success, $"'{stringItem}' should be matched by contains special regex");
            }
        }

        private List<string> GetStringsWithOutSpecialChars()
        {
            return new List<string>()
            {
                "Casa",
                "Coche",
                "Tren Avion"
            };
        }

        [TestMethod]
        public void VerifyRegex_ContainsSpaceAndLowerCase_True()
        {
            Regex regex = new Regex(REGEX);
            foreach (var stringItem in GetStringsWithSpaceAndLowerCase())
            {
                Assert.IsFalse(regex.Match(stringItem).Success, $"'{stringItem}' should be matched by contains special regex");
            }
        }

        private List<string> GetStringsWithSpaceAndLowerCase()
        {
            return new List<string>()
            {
                "Casa01",
                "casaavion",
                "Te0pre0",
                "Te0pre0\\r"
            };
        }

        [TestMethod]
        public void VerifyRegex_ContainsSpaceAndLowerCase_False()
        {
            Regex regex = new Regex(REGEX);
            foreach (var stringItem in GetStringsWithOutLSpaceAndLowerCase())
            {
                Assert.IsFalse(regex.Match(stringItem).Success, $"'{stringItem}' should be matched by contains special regex");
            }
        }

        private List<string> GetStringsWithOutLSpaceAndLowerCase()
        {
            return new List<string>()
            {
                "CASA01",
                "CASAAVION",
                "TE0PRE0"
            };
        }

        [TestMethod]
        public void VerifyRegex_ContainsCapitalLettersTogether_True()
        {
            Regex regexCapitalLettersTogether = new Regex(REGEX_CAPITAL_LETTERS_TOGETHER);
            foreach (var stringItem in GetStringsWithCapitalLettersTogether())
            {
                Assert.IsFalse(regexCapitalLettersTogether.Match(stringItem).Success, $"'{stringItem}' should be matched by contains special regex");
            }
        }

        private List<string> GetStringsWithCapitalLettersTogether()
        {
            return new List<string>()
            {
                "ABCDE",
                "HELLO"
            };
        }

        [TestMethod]
        public void VerifyRegex_ContainsCapitalLettersTogether_False()
        {
            Regex regexCapitalLettersTogether = new Regex(REGEX_CAPITAL_LETTERS_TOGETHER);
            foreach (var stringItem in GetStringsWithOutCapitalLettersTogether())
            {
                Assert.IsFalse(regexCapitalLettersTogether.Match(stringItem).Success, $"'{stringItem}' should be matched by contains special regex");
            }
        }

        private List<string> GetStringsWithOutCapitalLettersTogether()
        {
            return new List<string>()
            {
                "abcde",
                "hello",
                "HEL LO"
            };
        }
    }
}
