using Redarbor.Common.Entities.Configuration;
using Redarbor.Master.Contracts.ServiceLibrary.DTO;

namespace Redarbor.Master.Contracts.ServiceLibrary
{
    public interface ILoginCompanyService
    {
        void LogOff(PortalConfig portalConfig);
        void UpdateUserData(CompanyCredentials newCredentials);
        bool LoginAndCheck(CompanyCredentials credentials, string login, string password, ref string message, int timeout = 0);
        void Login(CompanyCredentials credentials, string login, string password, ref string message, int timeout = 0);
        bool UpdateCredentials(CompanyCredentials credentials);
        CompanyCredentials GetCredentialsFromSession(string sessionId = "");
    }
}
