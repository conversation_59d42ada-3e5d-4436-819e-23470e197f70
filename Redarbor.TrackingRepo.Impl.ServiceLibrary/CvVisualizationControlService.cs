using Redarbor.Repo.Trackings.Library.DomainServiceContracts;
using Redarbor.Repo.Trackings.Library.Entities;
using Redarbor.TrackingRepo.Contracts.ServiceLibrary;

namespace Redarbor.TrackingRepo.Impl.ServiceLibrary
{
    public class CvVisualizationControlService : ICvVisualizationControlService
    {
        private readonly ICvVisualizationControlRecoverAndPersist _cvVisualizationControlRecoverAndPersist;

        public CvVisualizationControlService(ICvVisualizationControlRecoverAndPersist cvVisualizationControlRecoverAndPersist)
        {
            _cvVisualizationControlRecoverAndPersist = cvVisualizationControlRecoverAndPersist;
        }

        public bool Exists(int idCompany, int idCandidate, short idPortal, short idType, int idCv, int dateInt)
        {
            return _cvVisualizationControlRecoverAndPersist.Exists(idCompany, idCandidate, idPortal, idType, idCv, dateInt);
        }

        public int Insert(CvVisualizationControlEntity cvVisualizationControl)
        {
            return _cvVisualizationControlRecoverAndPersist.Insert(cvVisualizationControl);
        }
    }
}
