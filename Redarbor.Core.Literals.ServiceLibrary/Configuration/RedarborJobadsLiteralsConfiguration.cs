using Redarbor.Extensions.Library.Extensions;
using Redarbor.Jobads.Literals.Consumer.Configuration;
using Redarbor.Jobads.Literals.Consumer.Enums;
using System;
using System.Configuration;

namespace Redarbor.Core.Literals.ServiceLibrary
{
    public class RedarborJobadsLiteralsConfiguration : IRedarborJobadsLiteralsConfiguration
    {
        public RedarborJobadsLiteralsConfiguration()
        {
            AppId = ConfigurationManager.AppSettings["APP_ID"].ToInt();
            UrlJobadsLiterals = new Uri(ConfigurationManager.AppSettings["URL_API_LITERALS"]);
            Business = JobadsLiteralsBusiness.Jobads;
        }

        public int AppId { get; }

        public Uri UrlJobadsLiterals { get; }

        public JobadsLiteralsBusiness Business { get; }

        public int CheckUpdatesMinutesInterval { get; }

        public bool ReturnLiteralKeyIfNotFound { get; }

        public bool LogErrorIfLiteralOrPageNotFound { get; }

        public void LogError(Exception ex)
        {
            throw ex;
        }
    }
}
