<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{9BE71737-B615-4227-BB44-C5ECEC3DCED3}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Redarbor.Core.Computrabajo.Contracts.ServiceLibrary</RootNamespace>
    <AssemblyName>Redarbor.Core.Computrabajo.Contracts.ServiceLibrary</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x64\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\x64\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Constants\CacheConstants.cs" />
    <Compile Include="Constants\ConnectionStringKeys.cs" />
    <Compile Include="Constants\Constants.cs" />
    <Compile Include="Constants\ExtraDataConstants.cs" />
    <Compile Include="Constants\QueryStringParams.cs" />
    <Compile Include="Entity\AmazonAccessConfiguration.cs" />
    <Compile Include="Entity\ExceptionsConsumer\ConfigurationExceptionModel.cs" />
    <Compile Include="Enums\AmazonFileExtensionEnum.cs" />
    <Compile Include="Enums\AmazonMimeTypeEnum.cs" />
    <Compile Include="Enums\ApiUrls.cs" />
    <Compile Include="Enums\ExceptionsConsumer\AppEnum.cs" />
    <Compile Include="Enums\ExceptionsConsumer\EnvironmentEnum.cs" />
    <Compile Include="Enums\ExceptionsConsumer\ExceptionAppTypeEnum.cs" />
    <Compile Include="Enums\ExceptionsConsumer\ExceptionAreaEnum.cs" />
    <Compile Include="Enums\ExceptionsConsumer\ExceptionBusinessEnum.cs" />
    <Compile Include="Enums\TelephoneTypeEnum.cs" />
    <Compile Include="IAmazonFilesService.cs" />
    <Compile Include="IExceptionPublisherNoPortalDependencyService.cs" />
    <Compile Include="IExceptionPublisherService.cs" />
    <Compile Include="IPortalByHostService.cs" />
    <Compile Include="IPortalConfigurationService.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Redarbor.Common.Entities\Redarbor.Common.Entities.csproj">
      <Project>{3da7e832-19ae-4c35-85f8-5faf5a4dabeb}</Project>
      <Name>Redarbor.Common.Entities</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>