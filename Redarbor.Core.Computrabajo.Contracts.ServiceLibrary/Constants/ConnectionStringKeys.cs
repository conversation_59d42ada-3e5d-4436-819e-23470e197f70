
namespace Redarbor.Core.Computrabajo.Contracts.ServiceLibrary.Constants
{
    public static class ConnectionStringKeys
    {
        public const string MasterConnectionstringKey = "Masterdbconnectionstring";
        public const string ReadMasterConnectionstringKey = "ReadMasterdbconnectionstring";
        public const string MasterTraceConnectionstringKey = "MasterTracedbconnectionstring";

        public const string DataConnectionstringKey = "Datadbconnectionstring";

        public const string CommonConnectionstringKey = "Commondbconnectionstring";
        public const string CommonCrmConnectionstringKey = "CommonCrmdbconnectionstring"; 
        public const string CommonTrackingConnectionstringKey = "CTTracking";
        public const string CommonValuationsConnectionstringKey = "CommonValuationsconnectionstring";

        public const string WhiteLabelConnectionstringKey = "WhiteLabel_ConnectionString";
        public const string CompanyCountersConnectionstringKey = "CompanyCountersConnectionString";
        public const string MatchesManagerConnectionstringKey = "MatchesManagerConnectionString";

        //TODO => Connection string to be removed. Use Core AppSettings Nugget.
        public const string RepoAppSettingConnection = "RepoAppSettingConnection";

        public const string RepoAuthenticate = "RepoAuthenticatedbconnectionstring";

        public const string BadDomainDbConnectionStringKey = "BadDomainDbConnectionString";
        public const string RepoCompaniesConnectionStringKey = "RepoCompaniesConnectionString";
        public const string RepoProduct = "RepoProductConnectionString";
    }
}
