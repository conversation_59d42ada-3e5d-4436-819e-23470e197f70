<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Antlr" version="3.5.0.2" targetFramework="net472" />
  <package id="Autofac" version="4.8.0" targetFramework="net472" />
  <package id="bootstrap" version="3.4.1" targetFramework="net472" />
  <package id="EnyimMemcached" version="2.16.0" targetFramework="net472" />
  <package id="jQuery" version="3.4.1" targetFramework="net472" />
  <package id="log4net" version="2.0.8" targetFramework="net472" />
  <package id="Microsoft.AspNet.Mvc" version="5.2.9" targetFramework="net472" />
  <package id="Microsoft.AspNet.Razor" version="3.2.9" targetFramework="net472" />
  <package id="Microsoft.AspNet.Web.Optimization" version="1.1.3" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi" version="5.2.5" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.Client" version="5.2.5" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.Core" version="5.2.5" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.HelpPage" version="5.2.5" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.WebHost" version="5.2.5" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebPages" version="3.2.9" targetFramework="net472" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="7.0.0" targetFramework="net472" />
  <package id="Microsoft.CodeDom.Providers.DotNetCompilerPlatform" version="2.0.1" targetFramework="net472" />
  <package id="Microsoft.CSharp" version="4.6.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Configuration" version="3.1.6" targetFramework="net472" />
  <package id="Microsoft.Extensions.Configuration.Abstractions" version="5.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Configuration.Binder" version="3.1.6" targetFramework="net472" />
  <package id="Microsoft.Extensions.DependencyInjection" version="6.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="6.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.FileProviders.Abstractions" version="5.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Hosting.Abstractions" version="5.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Logging.Abstractions" version="3.1.6" targetFramework="net472" />
  <package id="Microsoft.Extensions.ObjectPool" version="5.0.11" targetFramework="net472" />
  <package id="Microsoft.Extensions.Primitives" version="5.0.0" targetFramework="net472" />
  <package id="Microsoft.Web.Infrastructure" version="2.0.1" targetFramework="net472" />
  <package id="Modernizr" version="2.8.3" targetFramework="net472" />
  <package id="Newtonsoft.Json" version="12.0.3" targetFramework="net472" />
  <package id="RabbitMQ.Client" version="6.2.2" targetFramework="net472" />
  <package id="Redarbor.Api.Contracts" version="3.0.4" targetFramework="net472" />
  <package id="Redarbor.Api.Impl" version="3.1.4" targetFramework="net472" />
  <package id="Redarbor.AuditApiRequests.Common.Library" version="3.0.0" targetFramework="net472" />
  <package id="Redarbor.AuditApiRequests.NetFramework.Library" version="3.0.0" targetFramework="net472" />
  <package id="Redarbor.Extensions.NetStandard" version="1.0.7.1" targetFramework="net472" />
  <package id="Redarbor.GlobalConfiguration.Library" version="1.1.3" targetFramework="net472" />
  <package id="Redarbor.Kpi.Consumer" version="3.2.0" targetFramework="net472" />
  <package id="Redarbor.Kpi.Consumer.Abstractions" version="3.2.1" targetFramework="net472" />
  <package id="Redarbor.RabbitMQ" version="1.0.0.17" targetFramework="net472" />
  <package id="Redarbor.RabbitMQ.Abstractions" version="1.0.0.17" targetFramework="net472" />
  <package id="Redarbor.RabbitMQ.Extensions" version="1.0.0.17" targetFramework="net472" />
  <package id="Redarbor.RabbitMQ.Model" version="1.0.0.17" targetFramework="net472" />
  <package id="Redarbor.Tools.Exceptions.Consumer" version="2.1.0" targetFramework="net472" />
  <package id="Swashbuckle" version="5.6.0" targetFramework="net472" />
  <package id="Swashbuckle.Core" version="5.6.0" targetFramework="net472" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net472" />
  <package id="System.ComponentModel.Annotations" version="4.7.0" targetFramework="net472" />
  <package id="System.Configuration.ConfigurationManager" version="4.7.0" targetFramework="net472" />
  <package id="System.Diagnostics.DiagnosticSource" version="4.5.1" targetFramework="net472" />
  <package id="System.Memory" version="4.5.5" targetFramework="net472" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net472" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net472" />
  <package id="System.Security.AccessControl" version="4.7.0" targetFramework="net472" />
  <package id="System.Security.Permissions" version="4.7.0" targetFramework="net472" />
  <package id="System.Security.Principal.Windows" version="4.7.0" targetFramework="net472" />
  <package id="System.Text.Encodings.Web" version="7.0.0" targetFramework="net472" />
  <package id="System.Text.Json" version="7.0.3" targetFramework="net472" />
  <package id="System.Threading.Channels" version="4.7.1" targetFramework="net472" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net472" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net472" />
  <package id="WebActivatorEx" version="2.0" targetFramework="net472" />
  <package id="WebGrease" version="1.6.0" targetFramework="net472" />
</packages>