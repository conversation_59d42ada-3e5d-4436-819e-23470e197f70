<?xml version="1.0" encoding="utf-8"?>
<!--
  For more information on how to configure your ASP.NET application, please visit
  https://go.microsoft.com/fwlink/?LinkId=301879
  -->
<configuration>
	<connectionStrings>
		<add name="Commondbconnectionstring" connectionString="server=common-srv.ct.local;Uid=ats;PersistSecurityInfo=False;database=ct_comun;Pwd=$herl0cK;port=3306;ConvertZeroDatetime=True;ProcedureCacheSize=100;ConnectionTimeout=15;DefaultCommandTimeout=600;CacheServerProperties=True;SslMode=none;logging=true;" providerName="MySql.Data.MySqlClient" />
		<add name="RepoAppSettingConnection" connectionString="server=repo-settings.ct.local;port=3306;database=repo_settings;Uid=usr_ae;Pwd=************;PersistSecurityInfo=False;ConnectionTimeout=15;ConvertZeroDateTime=True;ProcedureCacheSize=10;max pool size=100;logging=true;DefaultCommandTimeout=600;SslMode=none;CacheServerProperties=True;" />
	</connectionStrings>
	<appSettings>
		<add key="Environment" value="Beta" />
		<add key="BuildVersion" value="1.0.0" />
		<add key="APPLICATION_ID" value="Redarbor.AE.Invoice.WebAPI" />
		<add key="ENVIRONMENT_FOLDER_PATH" value="C:\Redarbor" />
		<add key="APP_ID" value="2" />
		<add key="APP_ID_API" value="157" />
		<add key="ExceptionApiEndPoint" value="https://api-tools-audit.computrabajo.com/" />
		
		<add key="AUDIT_API_REQUESTS_ENABLED" value="false" />
		<add key="AUDIT_API_REQUESTS_THROW_EXCEPTION_ORIGIN_NOT_FOUND" value="true" />
		<!-- SEPARAR POR ; -->
		<add key="AUDIT_API_REQUESTS_PATHS_TO_AUDIT" value="v1/invoices" />
		<!--TIMELINE-->
		<add key="TIMELINE_URL_API" value="http://api-timeline.ct.local/"/>	
		<!-- Add Redarbor.AppSettings.Tool-->
		<add key="APPSETTINGS_FOLDER" value ="_resources/_appsettings"/>
		<add key="APPSETTINGS_URL_API" value="http://api-settings.ct.local"/>
		<add key="APPSETTINGS_RECYCLE_TIME_IN_MINUTES" value ="10"/>
		<add key="APPSETTINGS_AMBITID" value ="1"/>	
		<!-- End Redarbor.AppSettings.Tool-->
		<add key="KINESIS_API_ENDPOINT" value="http://api-kinesis.ct.local/" />
		<add key="RABBITMQ_API_ENDPOINT" value="http://api-queues.ct.local/" />
		<add key="KINESIS_BY_RABBIT" value="true" />
	</appSettings>
	<system.web>
		<compilation targetFramework="4.7.2" />
		<httpRuntime targetFramework="4.7.2" />
	</system.web>

	<runtime>
		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
			<dependentAssembly>
				<assemblyIdentity name="Antlr3.Runtime" publicKeyToken="eb42632606e9261f" />
				<bindingRedirect oldVersion="0.0.0.0-3.5.0.2" newVersion="3.5.0.2" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Newtonsoft.Json" culture="neutral" publicKeyToken="30ad4fe6b2a6aeed" />
				<bindingRedirect oldVersion="0.0.0.0-12.0.0.0" newVersion="12.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Web.Optimization" publicKeyToken="31bf3856ad364e35" />
				<bindingRedirect oldVersion="*******-1.1.0.0" newVersion="1.1.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Web.Infrastructure" publicKeyToken="31bf3856ad364e35" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35" />
				<bindingRedirect oldVersion="0.0.0.0-1.6.5135.21930" newVersion="1.6.5135.21930" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Web.Helpers" publicKeyToken="31bf3856ad364e35" />
				<bindingRedirect oldVersion="*******-3.0.0.0" newVersion="3.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35" />
				<bindingRedirect oldVersion="*******-3.0.0.0" newVersion="3.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35" />
				<bindingRedirect oldVersion="*******-5.2.5.0" newVersion="5.2.5.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Web.Http" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-5.2.5.0" newVersion="5.2.5.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Net.Http.Formatting" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-5.2.5.0" newVersion="5.2.5.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Text.Json" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-7.0.0.3" newVersion="7.0.0.3" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Threading.Tasks.Extensions" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.2.0.1" newVersion="4.2.0.1" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.ObjectPool" publicKeyToken="adb9793829ddae60" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-5.0.11.0" newVersion="5.0.11.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Bcl.AsyncInterfaces" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-7.0.0.0" newVersion="7.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.DependencyInjection.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.Configuration.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-5.0.0.0" newVersion="5.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.Primitives" publicKeyToken="adb9793829ddae60" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-5.0.0.0" newVersion="5.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Configuration.ConfigurationManager" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Autofac" publicKeyToken="17863af14b0044da" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.8.0.0" newVersion="4.8.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Numerics.Vectors" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.1.4.0" newVersion="4.1.4.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.0.1.2" newVersion="4.0.1.2" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Threading.Channels" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.0.2.0" newVersion="4.0.2.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Text.Encodings.Web" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-7.0.0.0" newVersion="7.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.DependencyInjection" publicKeyToken="adb9793829ddae60" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Enyim.Caching" publicKeyToken="cec98615db04012e" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
			</dependentAssembly>
		</assemblyBinding>
	</runtime>
	<system.codedom>
		<compilers>
			<compiler language="c#;cs;csharp" extension=".cs" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.CSharpCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" warningLevel="4" compilerOptions="/langversion:default /nowarn:1659;1699;1701" />
			<compiler language="vb;vbs;visualbasic;vbscript" extension=".vb" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.VBCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" warningLevel="4" compilerOptions="/langversion:default /nowarn:41008 /define:_MYTYPE=\&quot;Web\&quot; /optionInfer+" />
		</compilers>
	</system.codedom>
	<system.webServer>
		<handlers>
			<remove name="ExtensionlessUrlHandler-Integrated-4.0" />
			<remove name="OPTIONSVerbHandler" />
			<remove name="TRACEVerbHandler" />
			<add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="*" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" />
		</handlers>
	</system.webServer>
	<system.diagnostics>
		<trace autoflush="true">
			<listeners>
				<add name="Log4netTraceListener" type="Redarbor.AE.Invoice.WebAPI.Helpers.Log4netTraceListener, Redarbor.AE.Invoice.WebAPI, Version=*******, Culture=neutral" />
			</listeners>
		</trace>
	</system.diagnostics>
</configuration>