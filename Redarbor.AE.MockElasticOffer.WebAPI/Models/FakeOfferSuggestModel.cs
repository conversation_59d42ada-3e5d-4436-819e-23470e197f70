namespace Redarbor.AE.MockElasticOffer.WebAPI.Models
{
    public class FakeOfferSuggestModel
    {
        public string Title { get; set; }
        public int IdCompany { get; set; }
        public int IdPortal { get; set; }
        public int MaxSuggesterResults { get; set; } = 10;
        public List<long> AssignedUser { get; set; } = new List<long>();
        public string IdUser { get; set; }
        public string IdRole { get; set; }
    }
}
