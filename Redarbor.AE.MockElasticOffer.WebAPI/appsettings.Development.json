{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"RepoOfferConnectionString": "server=redarbor.ae.mysql;Uid=root;PersistSecurityInfo=False;database=repo_offers;Pwd=test;port=3306;ConnectionTimeout=15;ConvertZeroDateTime=True;DefaultCommandTimeout=300;ProcedureCacheSize=100;CacheServerProperties=True;SslMode=none", "RepoOfferAuxConnectionString": "server=redarbor.ae.mysql;Uid=root;PersistSecurityInfo=False;database=repo_offers_aux;Pwd=testport=3306;ConnectionTimeout=15;ConvertZeroDateTime=True;DefaultCommandTimeout=300;ProcedureCacheSize=100;CacheServerProperties=True;SslMode=none"}}