using System;
using System.Diagnostics;
using Redarbor.Core.Resolver.Contracts.ServiceLibrary;
using Redarbor.Core.Shared.Contracts.ServiceLibrary;
using Redarbor.Extensions.Library.DI;

namespace Redarbor.Core.Resolver.ServiceLibrary
{
    [RegisterStatefulService]
    public class ApplicationIdResolverService : IApplicationIdResolverService
    {
        protected const string APP_ID_KEYNAME = "APP_ID";
        protected static short _appId = 0;

        readonly IConfigurationService _configurationService;

        public ApplicationIdResolverService(IConfigurationService configurationService)
        {
            _configurationService = configurationService;
        }

        public virtual short ResolveApplicationId()
        {
            if (_appId <= 0) {
                TryGetAppId();
            }

            return _appId;
        }

        private void TryGetAppId()
        {
            try
            {
                if (_appId <= 0) {
                    _appId = GetIdFromConfigurationManager();
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"Fail to get related AppId ex: {ex.ToString()}");
                throw;
            }
        }

        private short GetIdFromConfigurationManager()
        {
            short appId = 0;

            if (_configurationService.AppSettings[APP_ID_KEYNAME] != null)
            {
                short.TryParse(_configurationService.AppSettings[APP_ID_KEYNAME], out appId);
            }

            return appId;
        }
    }
}
