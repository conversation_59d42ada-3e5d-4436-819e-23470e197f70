<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{76DF1704-A73E-40ED-BBE7-69473D962CB1}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Redarbor.Company.Contracts.ServiceLibrary</RootNamespace>
    <AssemblyName>Redarbor.Company.Contracts.ServiceLibrary</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x64\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\x64\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Redarbor.Quiz.Contract.ServiceLibrary, Version=1.0.48.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Quiz.Contract.ServiceLibrary.1.0.48\lib\net452\Redarbor.Quiz.Contract.ServiceLibrary.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Configuration\CompanyConfiguration.cs" />
    <Compile Include="Configuration\ICompanyConfiguration.cs" />
    <Compile Include="DTO\CompanyAbuseResultDTO.cs" />
    <Compile Include="DTO\CompanyComercialEmailDTO.cs" />
    <Compile Include="DTO\CompanyFileDTO.cs" />
    <Compile Include="DTO\CompanyHasAtsDTO.cs" />
    <Compile Include="DTO\CompanyPreRegisterDTO.cs" />
    <Compile Include="DTO\CompanySaveDTO.cs" />
    <Compile Include="DTO\CompanySaveTrackingDTO.cs" />
    <Compile Include="DTO\CrmCompanyComercialDTO.cs" />
    <Compile Include="DTO\CrmConsumerCompanyComercial.cs" />
    <Compile Include="DTO\CrmConsumerCustomerResponseDTO.cs" />
    <Compile Include="DTO\CrmConsumerStackDTO.cs" />
    <Compile Include="DTO\MatchesOfferMailSettingsDTO.cs" />
    <Compile Include="Enums\CompanyFileEnum.cs" />
    <Compile Include="Enums\CompanyVisibilityEnum.cs" />
    <Compile Include="Enums\CvVisualizationPrivacity.cs" />
    <Compile Include="Enums\CvVisualizationTypeEnum.cs" />
    <Compile Include="Enums\CrmCompanyComercialProductEnum.cs" />
    <Compile Include="Enums\MatchesOfferMailSettingsStatusEnum.cs" />
    <Compile Include="Enums\MatchOfferMailStatusEnum.cs" />
    <Compile Include="Enums\OriginRequest.cs" />
    <Compile Include="Enums\QuizDefinitionTypeEnum.cs" />
    <Compile Include="Enums\QuizObjectTypeEnum.cs" />
    <Compile Include="ICompanyAbuseService.cs" />
    <Compile Include="ICompanyCheckIdentifierService.cs" />
    <Compile Include="Enums\CompanyFilterTypeEnum.cs" />
    <Compile Include="Enums\CompanyVisibilitySectionEnum.cs" />
    <Compile Include="ICompanyCheckLocalizationService.cs" />
    <Compile Include="ICompanyDocumentIdentifyService.cs" />
    <Compile Include="ICompanyFiltersService.cs" />
    <Compile Include="Enums\CountryEnum.cs" />
    <Compile Include="Enums\NitResultEnum.cs" />
    <Compile Include="ICompanyFileService.cs" />
    <Compile Include="ICompanyService.cs" />
    <Compile Include="ICompanyPreRegisterService.cs" />
    <Compile Include="ICompanyTrackingService.cs" />
    <Compile Include="ICrmConsumerService.cs" />
    <Compile Include="IQuizConsumerService.cs" />
    <Compile Include="IRegisterLoginService.cs" />
    <Compile Include="Mappers\CompanyMappers.cs" />
    <Compile Include="Models\RegisterActionUpdateModel.cs" />
    <Compile Include="Models\RegisterLoginModel.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Readarbor.Master.Entities\Redarbor.Master.Entities.csproj">
      <Project>{72B93BA2-C177-4DDF-9F27-E08151323719}</Project>
      <Name>Redarbor.Master.Entities</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Common.Entities\Redarbor.Common.Entities.csproj">
      <Project>{3DA7E832-19AE-4C35-85F8-5FAF5A4DABEB}</Project>
      <Name>Redarbor.Common.Entities</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Company.Library\Redarbor.Company.Library.csproj">
      <Project>{8738fc41-64c2-4664-923c-9486f8d4b607}</Project>
      <Name>Redarbor.Company.Library</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Configuration.Library\Redarbor.Configuration.Library.csproj">
      <Project>{BD90AD61-E12D-4CBD-BE3A-81A1A31FDDB2}</Project>
      <Name>Redarbor.Configuration.Library</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Kpi.Contracts.ServiceLibrary\Redarbor.Core.Kpi.Contracts.ServiceLibrary.csproj">
      <Project>{69fa56cc-33a8-445a-aa62-6bb70ed1bc70}</Project>
      <Name>Redarbor.Core.Kpi.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Extensions.Library\Redarbor.Extensions.Library.csproj">
      <Project>{A30C078B-2F28-42B0-84E7-E02F9C30E27E}</Project>
      <Name>Redarbor.Extensions.Library</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Connected Services\" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>