using Redarbor.Company.Contracts.ServiceLibrary.Enums;
using Redarbor.Quiz.Contract.ServiceLibrary.DTO;

namespace Redarbor.Company.Contracts.ServiceLibrary
{
    public interface IQuizConsumerService
    {
        QuizRelationDTO GetCompetenceTest(long idCompany, long idCv, short idTypeRelation, short idTypeDefinition, short idBusiness, short portalId, short originRequest);

        string GetApiKeyForVideoInterviews(short idBusiness, short idPortal);
    }
}
