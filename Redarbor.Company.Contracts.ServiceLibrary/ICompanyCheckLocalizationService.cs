using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Redarbor.Company.Contracts.ServiceLibrary
{
    public interface ICompanyCheckLocalizationService
    {
        bool CheckLocalizationExists(short idCountry, short idLocalization, short idPortal);
        bool CheckCityExists(short idCountry, short idLocalization, short idCity, short idPortal);
    }
}
