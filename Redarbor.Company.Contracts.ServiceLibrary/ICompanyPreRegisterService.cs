using Redarbor.Company.Contracts.ServiceLibrary.DTO;
using Redarbor.Master.Entities.Company;
using Redarbor.Master.Entities.Enums;

namespace Redarbor.Company.Contracts.ServiceLibrary
{
    public interface ICompanyPreRegisterService
    {
        CompanyPreRegisterDTO GetByPk(int id, short idPortal);
        int GetIdCompanyPreRegisterByEmail(string email, short idPortal);
        int GetIdCompanyPreRegisterByNit(string nit, short idPortal);
        int Insert(CompanyPreRegisterDTO companyTemp);
        bool UpdateStatus(int id, short idPortal, short idStatus);
        bool UpdateEmailSent(int id, short idPortal, bool emailSent);
        bool UpdateEmailNdrStatus(int id, short idPortal, short emailNdrStatus);
        int UpdateCompanyPreRegister(CompanyPreRegisterDTO companyTemp);
        bool UpdateIdCompanyFromCompanyPreRegister(int idCompany, int idCompanyPreRegister, short idPortal);
        bool UpdateStatusImporOfferFromCompanyPreRegister(int idCompany, short idPortal, CompanyImportOfferStatusEnum status);
        bool UpdateSendMailPublishLastOfferForCompanyPreRegister(int idCompany, short idPortal);
        bool UpdateSendMailReminderImportOfferForCompanyPreRegister(int idCompany, short idPortal);
    }
}
