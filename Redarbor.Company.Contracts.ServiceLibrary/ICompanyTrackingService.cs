using Redarbor.Common.Entities.Configuration;
using Redarbor.Common.Entities.Enums;
using Redarbor.Company.Contracts.ServiceLibrary.DTO;
using Redarbor.Master.Entities.Enums;
using Redarbor.Master.Entities.Offers;
using Redarbor.Master.Entities.Tracking;
using System;
using System.Collections.Generic;
using System.Web;

namespace Redarbor.Company.Contracts.ServiceLibrary
{
    public interface ICompanyTrackingService
    {
        bool Add(TrackingCompany tracking, int companyId, short portalId);
        bool UpdateLastModerationAccept(int companyId, int portalId, DateTime lastModerationAcceptDate);
        bool UpdateLastModerationRefuse(int companyId, int portalId, DateTime refuseDate);
        bool UpdateLastFreemiumOffer(int companyId, int portalId, DateTime freemiumOfferDate);
        bool UpdateLastCWBuy(int companyId, int portalId, DateTime lastCWBuy, decimal price, int units);
        bool UpdateLastLogin(int companyId, int portalId);
        bool AddTrace(TrackingTraceCompany tracking, short portalId);
        TrackingCompany GetCookieTracking();
        TrackingTraceCompany GetCookieTrackingTrace();
        string GetCookiePublicExternalReferal();
        void SetCookieTracking(TrackingCompany trackingData, string cookieDomain);
        void SetCookieTrackingTrace(TrackingTraceCompany trackingData, string cookieDomain);
        void SetTrackingTrace(HttpRequestBase request, PortalConfig portalConfig, PageEnum page, int companyId = 0, long userId = 0);
        void SetTrackingTraceNitValidate(HttpRequestBase request, PortalConfig portalConfig, PageEnum page, string nit, short checkIdentifierId);
        void CaptureCampaign(HttpRequestBase request, PortalConfig portalConfig, PageEnum page, int companyId = 0, long userId = 0);
        string GetABTestingUser(PortalConfig portalConfig, int qaTest = 0);
        bool RecordExists(int companyId, short portalId);
        bool Update(TrackingCompany tracking, int companyId, short portalId);

        bool AeUpsertMatchesSummarySubscription(int oi, int idCompany, short portalId, long userId, MatchSummaryNotificationType type, bool isDisabled);
        List<OfferMatchesSummarySubscriptionEntity> GetMatchSummaryNotificationsFromUserAndCompany(long userId, int idCompany, short portalId, bool getFromCache = true);
        bool AeInvalidateMatchesForNotification(int offerId, int idCompany, short portalId);
        bool AddMatchesOfferMailSettings(MatchesOfferMailSettingsDTO matchesOfferMailSettingsDTO);
        bool UpdateStatusByUserMatchesOfferMailSettings(MatchesOfferMailSettingsDTO matchesOfferMailSettingsDTO);
        MatchesOfferMailSettingsDTO GetMatchesOfferMailSettings(int idOffer, int idCompany, short idPortal);
        bool SetLostMatchesOfferMailSettings(MatchesOfferMailSettingsDTO matchesOfferMailSettingsDTO);
        bool UnsuscribeMatchesOffer(OfferEntity offer, long userId);
    }
}
