using Dapper;
using Redarbor.OfferWatcher.Application.Abstractions.Repositories;
using Redarbor.OfferWatcher.Application.Entities;
using Redarbor.OfferWatcher.Domain.Entities;
using Redarbor.OfferWatcher.Domain.Enums;
using Redarbor.Procs.Domain.Enums;
using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;

namespace Redarbor.OfferWatcher.Infrastructure.Repositories
{
    public class OfferGroupRepository : RepositoryBase, IOfferGroupRepository
    {

        public OfferGroupRepository(ConnectionStrings connectionStrings) : base(connectionStrings.RepoModeration)
        {
        }


        private IDbTransaction? _transaction;


        private IDbTransaction Transaction
        {
            get
            {
                if (_transaction == null)
                {
                    _transaction = DbConnection.BeginTransaction();
                }
                return _transaction;
            }
        }

        public void DiscardChanges()
        {
            _transaction?.Rollback();
            _transaction?.Dispose();
            _transaction = null;
        }

        public void CommitChanges()
        {
            _transaction?.Commit();
            _transaction?.Dispose();
            _transaction = null;
        }

        public async Task<IEnumerable<OfferGroupCompanies>> GetCompaniesToExclude(PortalEnum portal)
        {
            var sql = "SELECT IdCompany as CompanyId, IdPortal as Portal FROM StackSettingsForOffersGroupedByContent WHERE IdStatus <> @Processed AND IdPortal = @Portal;";
            return await DbConnection.QueryAsync<OfferGroupCompanies>(sql, new { Processed = StatusStackSettingsOfferGroupedByContentEnum.Finished, Portal = portal });
        }

        public async Task<bool> InsertAsync(SettingsForOfferGroupedByContent settings)
        {
            var sql = @"INSERT IGNORE INTO SettingsForOffersGroupedByContent
                    (IdCompany, IdPortal, Threshold, NumOffersToShow, IsSimulation, IsPenalized)
                    VALUES (@CompanyId, @Portal, @Threshold, @NumOffersToShow,@IsSimulation, @IsPenalized);";

            var modifiedRows = await DbConnection.ExecuteAsync(sql, settings, Transaction);
            return modifiedRows > 0;
        }

        public async Task AddToStack(SettingsForOfferGroupedByContent settings)
        {
            var sql = @"INSERT IGNORE INTO StackSettingsForOffersGroupedByContent
                    (IdPortal, IdCompany, CreatedOn, UpdatedOn, IdStatus)
                    VALUES
                    (@Portal, @CompanyId, @CreatedOn, @UpdatedOn, @Status);";

            await DbConnection.ExecuteAsync(sql, new
            {
                settings.Portal,
                settings.CompanyId,
                CreatedOn = DateTime.UtcNow,
                UpdatedOn = DateTime.UtcNow,
                Status = StatusStackSettingsOfferGroupedByContentEnum.Pending
            }, Transaction);
        }
    }
}
