using Redarbor.Master.Entities.Enums;
using System;
using System.Collections.Specialized;

namespace Redarbor.Master.Entities.Tracking
{
    [Serializable]
    public class TrackingCompany
    {
        private Int16 m_source = 0;
        public Int16 source
        {
            get { return m_source; }
            set { m_source = value; }
        }

        private string m_campaign = string.Empty;
        public string campaign
        {
            get { return m_campaign; }
            set { m_campaign = value; }
        }

        private string m_group = string.Empty;
        public string group
        {
            get { return m_group; }
            set { m_group = value; }
        }

        private string m_keyword = string.Empty;
        public string keyword
        {
            get { return m_keyword; }
            set { m_keyword = value; }
        }

        private string m_ip = string.Empty;
        public string ip
        {
            get { return m_ip; }
            set { m_ip = value; }
        }

        private string m_url_referral = string.Empty;
        public string url_referral
        {
            get { return m_url_referral; }
            set { m_url_referral = value; }
        }

        private string m_utm_source = string.Empty;
        public string utm_source
        {
            get { return m_utm_source; }
            set { m_utm_source = value; }
        }
      
        public bool ParseQueryString(NameValueCollection queryParams)
        {

            string pep_s = queryParams.Get("pep_s");
            if (!string.IsNullOrEmpty(pep_s))
            {
                return ParsePEPQueryString(pep_s, queryParams);
            }
            string utm_source = queryParams.Get("utm_source");
            if (!string.IsNullOrEmpty(utm_source))
            {
                return ParseUTMQueryString(utm_source, queryParams);
            }
            return false;
        }
        private bool ParsePEPQueryString(string pep_s, NameValueCollection queryParams)
        {
            if (!short.TryParse(pep_s, out short source)) return false;
            if (source != (short)CompanyTrackingEnum.Google) return false;
            if (!short.TryParse(queryParams.Get("cpc"), out short cpc)) return false;
            if (cpc != 1) return false;
            m_source = (short)CompanyTrackingEnum.Google;
            m_group = queryParams.Get("pep_g") ?? string.Empty;
            m_campaign = queryParams.Get("pep_c") ?? string.Empty;
            m_keyword = queryParams.Get("pep_k") ?? string.Empty;

            return true;
        }
        private bool ParseUTMQueryString(string utm_source, NameValueCollection queryParams)
        {
            if (string.IsNullOrEmpty(utm_source)) return false;
            m_utm_source = utm_source;
            m_source = GetSourceByUtmSource(utm_source);
            m_group = queryParams.Get("utm_medium") ?? string.Empty; ;
            m_campaign = queryParams.Get("utm_campaign") ?? string.Empty; ;
            m_keyword = queryParams.Get("utm_term") ?? string.Empty; ;
            return true;
        }
        private short GetSourceByUtmSource(string sourceParam)
        {
            var sourceParamLowerCase = sourceParam.ToLower();
            if (sourceParamLowerCase == "facebook" ||
                sourceParamLowerCase == "facebook.com") return (short)CompanyTrackingEnum.FacebookUtm;
            else if (sourceParamLowerCase == "bing") return (short)CompanyTrackingEnum.BingUtm;
            else if (sourceParamLowerCase == "linkedin") return (short)CompanyTrackingEnum.LinkedinUtm;
            else if (sourceParamLowerCase == "google") return (short)CompanyTrackingEnum.GoogleUtm;
            else return (short)CompanyTrackingEnum.UnknownUtm;
        }

        public void SetUtmCodes(string utmSource, string utmMedium, string utmCampaign)
        {
            m_utm_source = utmSource;
            m_source = GetSourceByUtmSource(utmSource);
            m_group = utmMedium;
            m_campaign = utmCampaign;
        }

        public string TestABRegister { get; set; }
        public short IsFromMobileView { get; set; }
        public short? RegisterOriginId { get; set; }
        public string IdAccountBySalesForce { get; set; } = string.Empty;
        public string IdSalesForceUser { get; set; } = string.Empty;
    }
}
