using System;
using System.Collections.Generic;

namespace Redarbor.Master.Entities.Invoice
{
    [Serializable]
    public class DtInvoiceEntity
    {
        public int IdInvoice { get; set; } = 0;
        public int IdPurchaseOperation { get; set; } = 0;
        public string PdfPath { get; set; } = string.Empty;
        public DateTime CreationDate { get; set; } = DateTime.MinValue;
        public int IdCompany { get; set; } = 0;
        public int IdInvoiceentity { get; set; } = 0;
        public string SerialNumber { get; set; } = string.Empty;
        public DateTime TaxDate { get; set; } = DateTime.MinValue;
        public float NetGbp { get; set; } = 0.0F;
        public float VatGbp { get; set; } = 0.0F;
        public DateTime CreateDon { get; set; } = DateTime.MinValue;
        public int CreatedBy { get; set; } = 0;
        public int UpdatedBy { get; set; } = 0;
        public DateTime UpdateDon { get; set; } = DateTime.MinValue;
        public DateTime DeleteDon { get; set; } = DateTime.MinValue;
        public int DeletedBy { get; set; } = 0;
        public int IdStatus { get; set; } = 0;
        public string GeneratedInvoice { get; set; } = string.Empty;
        public string ProductsId { get; set; } = string.Empty;
        public string PacksId { get; set; } = string.Empty;
        public short IdPortal { get; set; } = 0;        
        public InvoiceUnifiedEntity InvoiceUnified { get; set; } = new InvoiceUnifiedEntity();
        public List<DtInvoiceLinesEntity> Lines { get; set; } = new List<DtInvoiceLinesEntity> ();
    }
}
