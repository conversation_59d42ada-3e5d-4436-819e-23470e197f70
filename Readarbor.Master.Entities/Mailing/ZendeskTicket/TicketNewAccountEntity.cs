using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Redarbor.Master.Entities.Mailing.ZendeskTicket
{
    public class TicketNewAccountEntity
    {
        public string subject { get; set; }
        public string recipient { get; set; }
        public string status { get; set; }
        public long brand_id { get; set; }
        public List<string> tags { get; set; } = new List<string>();
        public CommentEntity comment { get; set; }
        public RequesterEntity requester { get; set; } = new RequesterEntity();

        public List<CustomField> custom_fields = new List<CustomField>();

    }
}
