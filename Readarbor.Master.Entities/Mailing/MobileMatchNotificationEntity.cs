namespace Redarbor.Master.Entities.Mailing
{
    public class MobileMatchNotificationEntity
    {
        public MobileMatchNotificationEntity()
        {
            DeviceToken = string.Empty;
            OfferTitle = string.Empty;
        }

        public string DeviceToken { get; set; }

        public long UserId { get; set; }

        public long CandidateId { get; set; }

        public long MatchId { get; set; }

        public int OfferId { get; set; }

        public string OfferTitle { get; set; }

        public int ProcessStatusId { get; set; }

        public int StatusId { get; set; }

        public int PortalId { get; set; }

        public int AppId { get; set; }

        public int DeviceTypeId { get; set; }

        public bool IsValid
        {
            get
            {
                return !string.IsNullOrWhiteSpace(DeviceToken)
                    && UserId > 0
                    && CandidateId > 0
                    && MatchId > 0
                    && OfferId > 0
                    && !string.IsNullOrWhiteSpace(OfferTitle)
                    && ProcessStatusId > 0
                    && PortalId > 0
                    && StatusId == 0
                    && AppId > 0;
            }
        }
    }
}
