using System;

namespace Redarbor.Master.Entities.Vision
{
    [Serializable]
    public class AttractivenessChartHistoryByCompanyResult
    {
        private DateTime dateIndex;
        public DateTime DateIndex
        {
            get { return this.dateIndex; }
        }

        private int index;
        public int Index
        {
            get { return this.index; }
        }

        public AttractivenessChartHistoryByCompanyResult(DateTime dateIndex, int index)
        {
            this.dateIndex = dateIndex;
            this.index = index;
        }
    }
}
