using System;

namespace Redarbor.Master.Entities.Reports
{
    public class ReportConfigurationEntity
    {
        public int Id { get; set; }
        public short PortalId { get; set; }
        public int CompanyId { get; set; }
        public long UserId { get; set; }
        public DateTime? DateFrom { get; set; } 
        public DateTime? DateTo { get; set; } 
        public string UserName { get; set; } = string.Empty;
        public short FileExtension { get; set; }
        public short Status { get; set; }
        public short Type { get; set; }
        public string NameFileAmazon { get; set; } = string.Empty;
        public string Path { get; set; }
        public string Name { get; set; } = string.Empty;
        public int CompanyFileId { get; set; }
    }
}
