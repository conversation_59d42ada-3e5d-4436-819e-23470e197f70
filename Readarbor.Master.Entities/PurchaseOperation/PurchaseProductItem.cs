namespace Redarbor.Master.Entities.PurchaseOperation
{
    public class PurchaseProductItem
    {
        public int Id { get; set; } = 0;
        public int IdProduct { get; set; } = 0;
        public int IdTemporality { get; set; } = 0;
        public decimal Price { get; set; } = 0;
        public decimal PriceBase { get; set; } = 0;
        public int PromotionId { get; set; } = 0;
        public float Tax { get; set; } = 0.0F;
        public string ComercialName { get; set; } = string.Empty;
        public int CompanyProductId { get; set; } = 0;
        public int Unit { get; set; } = 1; //TODO MQC 2021-12-09 pensar si meter este valor en la entidad
        public int PurchaseOperationId { get; set; } = 0; //FK
        public decimal OriginalPriceBase { get; set; } //Price before discounts
    }
}
