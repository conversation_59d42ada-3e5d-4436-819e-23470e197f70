using Redarbor.Master.Entities.Enums;
using System;

namespace Redarbor.Master.Entities.Users
{
    [Serializable]
    public class UserEntity
    {
        public long Id { get; set; } = 0;
        public short PortalId { get; set; }
        public string Username { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        public int CompanyId { get; set; }
        public int ParentCompanyId { get; set; }
        public int OriginId { get; set; }
        public int TypeId { get; set; }
        public string ContactName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string PhoneNumbers { get; set; } = string.Empty;
        public string Fax { get; set; } = string.Empty;
        public DateTime CreatedOn { get; set; } = DateTime.UtcNow;
        public long CreatedBy { get; set; }
        public DateTime UpdatedOn { get; set; } = DateTime.UtcNow;
        public DateTime? DeletedOn { get; set; } = DateTime.UtcNow;
        public DateTime? LastLoginOn { get; set; } = DateTime.UtcNow;
        public Int16 Principal { get; set; }
        public int NdrStatusId { get; set; }
        public int StatusId { get; set; } = (int)UserStatusEnum.Activo;
        public Int16 RoleId { get; set; } = (int)UserRoleEnum.GESTOR;
        public string RoleText { get; set; } = string.Empty;
        public Int16 CurrentCvStep { get; set; }
        public long AppId { get; set; }
        public string ThumbnailUrl { get; set; }
        public string IdEncrypt { get; set; } = string.Empty;
        public string AvatarPath { get; set; }
        public short VerifiedMail { get; set; }
    }
}
