using System;

namespace Redarbor.Master.Entities.Product
{
    [Serializable]
    public class CompanyProductFeatureEntity : ProductFeatureEntity
    {
        public int CompanyId { get; set; }
        public int CustomerId { get; set; }
        public int CompanyProductId { get; set; }
        public int PortalFeatureId { get; set; }
        public short StatusId { get; set; }
        public DateTime DateAdd { get; set; } = DateTime.MinValue;
        public DateTime DateMod { get; set; } = DateTime.MinValue;
        public DateTime DateLastConsumed { get; set; } = DateTime.MinValue;
        public short GroupId { get; set; }
        public int ConsumedUnits { get; set; }
    }
}
