using System;
using System.Collections.Generic;

namespace Redarbor.Master.Entities.Company
{
    [Serializable]
    public class ContactInformationEntity
    {
        public string Username { get; set; }
        public string Name { get; set; }
        public string Surname { get; set; }
        public int PositionId { get; set; }
        public List<PhoneNumberEntity> PhoneNumbers { get; set; } = new List<PhoneNumberEntity>();
        public string Email { get; set; }
    }
}
