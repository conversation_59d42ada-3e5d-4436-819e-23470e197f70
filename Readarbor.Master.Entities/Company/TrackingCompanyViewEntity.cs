using System;

namespace Redarbor.Master.Entities.Company
{
    [Serializable]
    public class TrackingCompanyViewEntity
    {
        public int IdMaster { get; set; }
        public int IdCompany { get; set; }
        public short IdPortal { get; set; }
        public int DateInt { get; set; }
        public int AboutTab { get; set; }
        public int OffersTab { get; set; }
        public int ValuationsTab { get; set; }
        public int PhotosTab { get; set; }
        public int SalariesTab { get; set; }
        public int BenefitsTab { get; set; }
        public int InterviewsTab { get; set; }
    }
}
