using Redarbor.Payment.Contracts.ServiceLibrary.Models;
using Redarbor.Payments.Consumer.Enums;
using Redarbor.Payments.Consumer.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Redarbor.Payment.Impl.ServiceLibrary.Mappings
{
    public static class OperationPaymentCompleteMapping
    {
        public static OperationPaymentCompleteResponseModel Map(OperationActionReturn model)
        {
            if (model == null) return null;
            if (model.Operation == null) return null;

            return new OperationPaymentCompleteResponseModel()
            {
                IdOperation = model.IdOperation,
                UrlOperation = model.Url,
                Status = PurchaseOperationEnumMapping.Map(model.Operation.IdStatus),
                PortalId = model.Operation.IdPortal,
                CompanyId = (int)model.Operation.IdCustomer,
                AlertsCreateOperation = model.Alerts,
                UrlKO = model.Operation.UrlReturnKO,
                UrlOk = model.Operation.UrlReturnOk,        
                Error = (model.Error > 0),
                ErrorMessage = model.ErrorMessage

            };       
   
        }  
    }
}
