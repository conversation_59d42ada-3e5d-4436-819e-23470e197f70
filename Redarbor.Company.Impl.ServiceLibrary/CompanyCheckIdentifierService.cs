using Redarbor.Common.Entities.Configuration;
using Redarbor.Common.Entities.Enums;
using Redarbor.Company.Contracts.ServiceLibrary;
using Redarbor.Company.Contracts.ServiceLibrary.Enums;
using Redarbor.Company.Library.DomainServiceContracts;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Extensions.Library.DI;
using Redarbor.Master.Entities.Enums;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;

namespace Redarbor.Company.Impl.ServiceLibrary
{
    [RegisterStatefulService]
    public class CompanyCheckIdentifierService : ICompanyCheckIdentifierService
    {
        private const int MAX_LENGHT_NIT_CO = 11;
        private readonly ICompanyCheckIdentifierRecoverAndPersist _companyCheckIdentifierRecoverAndPersist;
        private readonly IExceptionPublisherService _exceptionPublisherService;

        public CompanyCheckIdentifierService(ICompanyCheckIdentifierRecoverAndPersist companyCheckIdentifierRecoverAndPersist, IExceptionPublisherService exceptionPublisherService)
        {
            _companyCheckIdentifierRecoverAndPersist = companyCheckIdentifierRecoverAndPersist;
            _exceptionPublisherService = exceptionPublisherService;
        }

        public string NitFormatCO(string nit, int portalId)
        {
            if (nit.Length < MAX_LENGHT_NIT_CO && portalId == (int)PortalEnum.ComputrabajoColombia)
            {
                for (int i = nit.Length; i < MAX_LENGHT_NIT_CO; i++)
                {
                    nit = $"0{nit}";
                }
            }
            return nit;
        }

        public int CheckIdentifier(string nit, int countryId, PortalConfig portalConfig, int idCompany, bool withStatus = true, bool validateNitWithCountry = false)
        {
            if (string.IsNullOrEmpty(nit) || countryId == 0 || portalConfig.PortalId == 0)
                return (short)NitResultEnum.IncorrectNit;

            if (portalConfig.ValidateIdentifier)
            {
                if (!ValidateIdentifier(nit, countryId, idCompany))
                {
                    if (!_companyCheckIdentifierRecoverAndPersist.IsValidNit(nit, portalConfig.PortalId, withStatus))
                        return (short)NitResultEnum.IncorrectNit;
                }
                else
                {
                    // si la validación busca el registerAction en la dtcompany y es mayor que 0 devolverá correctnit para que no salte la validación ya que no se busca eso
                    short registerAction = _companyCheckIdentifierRecoverAndPersist.GetCompanyRegisterAction(NitFormatCO(nit, portalConfig.PortalId), portalConfig.PortalId, countryId);
                    if (registerAction == (short)CompanyRegisterActionEnum.UpdateCompanyWithoutActivity || registerAction == (short)CompanyRegisterActionEnum.UpdateCompanyWithPurchases)
                    {
                        return (short)NitResultEnum.CorrectNit;
                    }
                    if (validateNitWithCountry && _companyCheckIdentifierRecoverAndPersist.IsOtherCountryNit(nit, portalConfig.PortalId, countryId))
                    {
                        return (short)NitResultEnum.NitOtherCountry;
                    }
                    if (_companyCheckIdentifierRecoverAndPersist.ExistsIdentifier(nit, portalConfig.PortalId))
                        return (short)NitResultEnum.DuplicateNit;
                }
            }

            return (short)NitResultEnum.CorrectNit;
        }

        public short CheckIdentifier(string nit, int countryId, short portalId)
        {
            if (string.IsNullOrWhiteSpace(nit) || countryId == 0 || portalId == 0)
                return (short)NitResultEnum.IncorrectNit;

            if (!ValidateIdentifier(nit, countryId, 0))
            {
                if (!_companyCheckIdentifierRecoverAndPersist.IsValidNit(nit, portalId, true))
                {
                    return (short)NitResultEnum.IncorrectNit;
                }
            }
            else
            {
                if (_companyCheckIdentifierRecoverAndPersist.ExistsIdentifier(nit, portalId))
                {
                    return (short)NitResultEnum.DuplicateNit;
                }
            }
            return (short)NitResultEnum.CorrectNit;
        }

        private bool ValidateIdentifier(string identifier, int countryId, int idCompany)
        {
            bool isValidIdentifier = false;

            try
            {
                switch (countryId)
                {
                    case (int)CountryEnum.Colombia:
                        isValidIdentifier = Validate_NIT(identifier);
                        break;
                    case (int)CountryEnum.Mexico:
                        isValidIdentifier = Validate_RFC(identifier);
                        break;
                    case (int)CountryEnum.Peru:
                        isValidIdentifier = Validate_RUC(identifier);
                        break;
                    case (int)CountryEnum.Guatemala:
                        isValidIdentifier = Validate_RUC_GT(identifier);
                        break;
                    case (int)CountryEnum.Chile:
                        isValidIdentifier = Validate_RUT(identifier);
                        break;
                    case (int)CountryEnum.Argentina:
                        isValidIdentifier = Validate_CUIT(identifier);
                        break;
                    case (int)CountryEnum.Costarica:
                        isValidIdentifier = Validate_NITE(identifier);
                        break;
                    case (int)CountryEnum.Ecuador:
                        isValidIdentifier = Validate_RUC_EC(identifier);
                        break;
                    case (int)CountryEnum.ElSalvador:
                        isValidIdentifier = Validate_NIT_ELSALVADOR(identifier);
                        break;
                    case (int)CountryEnum.Filipinas:
                        isValidIdentifier = Validate_NIT_Philipinnes(identifier);
                        break;
                    case (int)CountryEnum.Venezuela:
                        isValidIdentifier = Validate_NIT_VENEZUELA(identifier);
                        break;
                    case (int)CountryEnum.Puertorico:
                        isValidIdentifier = Validate_NIT_PUERTORICO(identifier);
                        break;
                    case (int)CountryEnum.Uruguay:
                        isValidIdentifier = Validate_NIT_URUGUAY(identifier);
                        break;
                    case (int)CountryEnum.Nicaragua:
                        isValidIdentifier = Validate_NIT_NICARAGUA(identifier);
                        break;
                    case (int)CountryEnum.RepublicaDominicana:
                        isValidIdentifier = Validate_NIT_REPUBLICADOMINICANA(identifier);
                        break;
                    case (int)CountryEnum.Australia:
                    case (int)CountryEnum.Canada:
                    case (int)CountryEnum.HongKong:
                    case (int)CountryEnum.India:
                    case (int)CountryEnum.Indonesia:
                    case (int)CountryEnum.Ireland:
                    case (int)CountryEnum.Kenya:
                    case (int)CountryEnum.Malaysia:
                    case (int)CountryEnum.Newzealand:
                    case (int)CountryEnum.Singapore:
                    case (int)CountryEnum.SouthAfrica:
                    case (int)CountryEnum.UnitedKingdom:
                    case (int)CountryEnum.UnitedStates:
                        isValidIdentifier = true;
                        break;
                    default:
                        isValidIdentifier = Validate_Default(identifier);
                        break;
                }
            }
            catch (Exception ex)
            {
                Dictionary<string, string> extradata = new Dictionary<string, string>();
                extradata.Add("NIT_CUIT_RUC", identifier);
                if (idCompany != 0)
                    extradata.Add("IdCompany", idCompany.ToString());
                Trace.TraceError($"CompanyCheckIdentifierService ValidateIdentifier {ex}");
                _exceptionPublisherService.Publish(ex, "CompanyCheckIdentifierService", "ValidateIdentifier", false, extradata);
                return false;
            }

            return isValidIdentifier;
        }

        private bool Validate_NIT(string identificator)
        {
            bool is_valid = false;

            int NumGuiones = identificator.ToArray().Where(lr => lr.Equals('-')).Count();
            if (NumGuiones != 1)
            {
                return false;
            }

            if (identificator.Length < 11)
            {
                for (int i = identificator.Length; i < 11; i++)
                {
                    identificator = $"0{identificator}";
                }
            }

            if (identificator.Length == 11 || identificator.Length == 12)
            {
                double ic = 0;
                if (double.TryParse(identificator.Replace("-", ""), out ic))
                {
                    if (identificator.Substring(9, 1) == "-" || identificator.Substring(10, 1) == "-")
                    {
                        is_valid = true;
                    }
                }
            }

            if (is_valid)
            {
                string digito_control = identificator.Substring(identificator.Length - 1, 1);
                string txt_nit = identificator.Split('-').GetValue(0).ToString();

                string ceros = "000000";
                if (identificator.Length == 12)
                {
                    ceros = "00000";
                }

                int[] li_peso = new int[15];
                li_peso[0] = 71;
                li_peso[1] = 67;
                li_peso[2] = 59;
                li_peso[3] = 53;
                li_peso[4] = 47;
                li_peso[5] = 43;
                li_peso[6] = 41;
                li_peso[7] = 37;
                li_peso[8] = 29;
                li_peso[9] = 23;
                li_peso[10] = 19;
                li_peso[11] = 17;
                li_peso[12] = 13;
                li_peso[13] = 7;
                li_peso[14] = 3;

                string ls_str_nit = $"{ceros}{txt_nit}";

                int li_suma = 0;
                int parsedNumber = 0;
                if (ls_str_nit.Length >= li_peso.Length)
                {
                    for (int i = 0; i < li_peso.Length; i++)
                    {
                        if (!string.IsNullOrWhiteSpace(ls_str_nit.Substring(i, 1)))
                        {
                            int.TryParse(ls_str_nit.Substring(i, 1), out parsedNumber);
                            li_suma += parsedNumber * li_peso[i];
                        }
                        else
                        {
                            return false;
                        }
                    }
                }

                int digito_chequeo = li_suma % 11;

                if (digito_chequeo >= 2)
                    digito_chequeo = 11 - digito_chequeo;
                if (digito_control == digito_chequeo.ToString())
                {
                    return true;
                }
            }

            return false;
        }

        public short ValidatePersonType(string identificator)
        {
            short result = (short)TaxEntityFilterEnum.NotApply;

            if (!string.IsNullOrWhiteSpace(identificator))
            {
                identificator = identificator.ToUpperInvariant();

                Regex regexFisica = new Regex(@"^[A-ZÑ&]{4}\d{6}[A-Z0-9]{3}$", RegexOptions.Compiled);
                Regex regexMoral = new Regex(@"^[A-ZÑ&]{3}\d{6}[A-Z0-9]{3}$", RegexOptions.Compiled);

                if (regexFisica.IsMatch(identificator))
                    result = (short)TaxEntityFilterEnum.Autonomous;
                else if (regexMoral.IsMatch(identificator))
                    result = (short)TaxEntityFilterEnum.Company;
            }

            return result;
        }

        private bool Validate_RFC(string identificator)
        {
            bool is_valid = false;
            identificator = identificator.ToUpper();

            Regex regex = new Regex("^[a-zA-Z&]+$");

            string ident_apellidos_nombres = string.Empty;
            string ident_fechas = string.Empty;
            string ident_homonimia = string.Empty;
            string ident_verificador = string.Empty;
            string digito_verificador = string.Empty;
            string cadena = string.Empty;

            int length_identificador = identificator.Length;
            switch (length_identificador)
            {
                case 12: //Empresa
                    ident_apellidos_nombres = identificator.Substring(0, 3);
                    ident_fechas = identificator.Substring(3, 6);
                    ident_homonimia = identificator.Substring(9, 2);
                    ident_verificador = identificator.Substring(11, 1);
                    break;
                case 13: //Autónomo
                    ident_apellidos_nombres = identificator.Substring(0, 4);
                    ident_fechas = identificator.Substring(4, 6);
                    ident_homonimia = identificator.Substring(10, 2);
                    ident_verificador = identificator.Substring(12, 1);
                    break;
                default:
                    return false;
            }

            if (regex.IsMatch(ident_apellidos_nombres))
            {
                int i = 0;
                is_valid = int.TryParse(ident_fechas, out i);
            }

            if (is_valid)
            {
                string caracter = null;

                string mi_suma_str = string.Empty;

                if (identificator.Length == 12)
                    cadena = $"-{identificator.Substring(0, identificator.Length - 1)}";
                else
                    cadena = identificator.Substring(0, identificator.Length - 1);

                for (int i = 0; i < cadena.Length; i++)
                {
                    caracter = cadena.Substring(i, 1);
                    switch (caracter)
                    {
                        case "-":
                            mi_suma_str = $"{mi_suma_str}37";
                            break;
                        case " ":
                            mi_suma_str = $"{mi_suma_str}37";
                            break;
                        case "&":
                            mi_suma_str = $"{mi_suma_str}24";
                            break;
                        case "Ñ":
                            mi_suma_str = $"{mi_suma_str}38";
                            break;
                        case "A":
                        case "B":
                        case "C":
                        case "D":
                        case "E":
                        case "F":
                        case "G":
                        case "H":
                        case "I":
                        case "J":
                        case "K":
                        case "L":
                        case "M":
                        case "N":
                            mi_suma_str = $"{mi_suma_str}{(Asc(caracter) - 55)}";
                            break;
                        case "O":
                        case "P":
                        case "Q":
                        case "R":
                        case "S":
                        case "T":
                        case "U":
                        case "V":
                        case "W":
                        case "X":
                        case "Y":
                        case "Z":
                            mi_suma_str = $"{mi_suma_str}{(Asc(caracter) - 54)}";
                            break;
                        default:
                            mi_suma_str = $"{mi_suma_str}0{caracter}";
                            break;
                    }
                }

                int numero = 0;
                int suma = 0;
                int cont = 0;
                for (int i = 0; i < mi_suma_str.Length; i += 2)
                {
                    int.TryParse(mi_suma_str.Substring(i, 2), out numero);
                    suma += numero * (13 - cont);
                    cont++;
                }
                int resto = suma % 11;


                switch (resto)
                {
                    case 0:
                        digito_verificador = "0";
                        break;
                    /* case 10:
                         digito_verificador = "A";
                         break;*/
                    default:
                        digito_verificador = (11 - resto).ToString();
                        if (digito_verificador == "10")
                            digito_verificador = "A";
                        break;
                }

                if (digito_verificador == ident_verificador)
                {
                    return true;
                }
            }

            return false;
        }

        private bool Validate_RUC(string identificator)
        {
            bool is_valid = false;

            if (identificator.Length == 11)
            {
                double ic = 0;
                if (double.TryParse(identificator, out ic))
                {
                    is_valid = true;
                }
            }

            if (is_valid)
            {
                int init_number = 0;

                int.TryParse(identificator.Substring(0, 2), out init_number);

                List<int> lst_valid_numbers = new List<int> { 10, 15, 17, 20 };

                is_valid = lst_valid_numbers.Contains(init_number);

                // otra opcion
                if (!lst_valid_numbers.Contains(init_number))
                    return false;
            }

            if (is_valid)
            {
                int li_suma = 0;

                int valor = 0;
                int.TryParse(identificator.Substring(0, 1), out valor);
                li_suma += valor * 5;
                int.TryParse(identificator.Substring(1, 1), out valor);
                li_suma += valor * 4;
                int.TryParse(identificator.Substring(2, 1), out valor);
                li_suma += valor * 3;
                int.TryParse(identificator.Substring(3, 1), out valor);
                li_suma += valor * 2;
                int.TryParse(identificator.Substring(4, 1), out valor);
                li_suma += valor * 7;
                int.TryParse(identificator.Substring(5, 1), out valor);
                li_suma += valor * 6;
                int.TryParse(identificator.Substring(6, 1), out valor);
                li_suma += valor * 5;
                int.TryParse(identificator.Substring(7, 1), out valor);
                li_suma += valor * 4;
                int.TryParse(identificator.Substring(8, 1), out valor);
                li_suma += valor * 3;
                int.TryParse(identificator.Substring(9, 1), out valor);
                li_suma += valor * 2;


                int digito_chequeo = li_suma % 11;
                digito_chequeo = 11 - digito_chequeo;

                if (digito_chequeo == 10)
                    digito_chequeo = 0;
                if (digito_chequeo == 11)
                    digito_chequeo = 1;

                int digito_calculado = 0;
                int.TryParse(identificator.Substring(identificator.Length - 1, 1), out digito_calculado);
                if (digito_chequeo == digito_calculado)
                {
                    return true;
                }
            }

            return false;
        }

        public bool Validate_RUC_GT(string identificator)
        {
            bool is_valid = false;

            if (identificator.Length < 9)
            {
                for (int i = identificator.Length; i < 9; i++)
                {
                    identificator = $"0{identificator}";
                }
            }

            if (identificator.Length == 9)
            {
                is_valid = true;
            }

            if (is_valid)
            {
                int suma = 0;
                int valor = 0;
                int Validator = 0;
                var ValidatorChar = identificator[8];
                int verificacion = 0;

                if (char.IsDigit(ValidatorChar))
                {
                    int.TryParse(ValidatorChar.ToString(), out Validator);
                }
                else if (char.ToUpper(ValidatorChar) == 'K')
                {
                    Validator = 10;
                }
                else
                {
                    return false;
                }

                int.TryParse(identificator.Substring(0, 1), out valor);
                suma += valor * 8;
                int.TryParse(identificator.Substring(1, 1), out valor);
                suma += valor * 7;
                int.TryParse(identificator.Substring(2, 1), out valor);
                suma += valor * 6;
                int.TryParse(identificator.Substring(3, 1), out valor);
                suma += valor * 5;
                int.TryParse(identificator.Substring(4, 1), out valor);
                suma += valor * 4;
                int.TryParse(identificator.Substring(5, 1), out valor);
                suma += valor * 3;
                int.TryParse(identificator.Substring(6, 1), out valor);
                suma += valor * 2;

                verificacion = (11 - (suma % 11)) % 11;

                if (verificacion == Validator)
                {
                    return true;
                }
                else
                {
                    return ValidateNITGtFirstNumbersOfCUI(identificator);
                }
            }

            return false;
        }

        private bool ValidateNITGtFirstNumbersOfCUI(string cui)
        {
            if (string.IsNullOrWhiteSpace(cui) || cui.Length != 9)
                return false;

            string cuiNumber = cui.Substring(0, 8);
            int.TryParse(cui.Substring(8, 1), out var checkerCode);
            int total = 0;

            for (int i = 0; i < cuiNumber.Length; i++)
            {
                var character = cuiNumber[i];

                if (!Char.IsNumber(character))
                    return false;

                total += character * (i + 2);
            }

            return checkerCode == (total % 11);
        }

        private bool Validate_RUT(string identificator)
        {
            bool is_valid = false;

            Regex regex = new Regex(@"^[a-zA-Z0-9\-]+$");

            if (regex.Match(identificator).Success)
            {
                switch (identificator.Length)
                {
                    case 9:
                        if (identificator.Substring(7, 1) == "-")
                        {
                            is_valid = true;
                        }
                        break;
                    case 10:
                        if (identificator.Substring(8, 1) == "-")
                        {
                            is_valid = true;
                        }
                        break;
                }
            }

            if (is_valid
                && identificator.Replace("-", "").All(a => a == '0'))
            {
                is_valid = false;
            }

            if (is_valid)
            {
                string rut_str = identificator.Replace("-", "");

                int rut = 0;
                int.TryParse(rut_str.Substring(0, rut_str.Length - 1), out rut);

                int Digito;
                int Contador;
                int Multiplo;
                int Acumulador;
                string RutDigito;

                Contador = 2;
                Acumulador = 0;

                while (rut != 0)
                {
                    Multiplo = (rut % 10) * Contador;
                    Acumulador = Acumulador + Multiplo;
                    rut = rut / 10;
                    Contador = Contador + 1;
                    if (Contador == 8)
                    {
                        Contador = 2;
                    }
                }

                Digito = 11 - (Acumulador % 11);
                RutDigito = Digito.ToString().Trim();
                if (Digito == 10)
                {
                    RutDigito = "K";
                }
                if (Digito == 11)
                {
                    RutDigito = "0";
                }
                string digito_verificador = identificator.Substring(identificator.Length - 1, 1);
                if (digito_verificador.ToUpper() == RutDigito)
                {
                    is_valid = true;
                }
                else
                {
                    is_valid = false;
                }
            }

            return is_valid;
        }

        private bool Validate_CUIT(string identificator)
        {
            bool is_valid = false;
            identificator = identificator.Replace(" ", "").Trim();

            is_valid = ValidateLengthString(identificator);

            if (is_valid)
                is_valid = ValidateTwoFirstNumbersAreValids(identificator);
            else
                return false;

            if (is_valid)
                is_valid = ValidateNumberCUIT(identificator);
            else
                return false;

            return is_valid;
        }

        private bool Validate_NITE(string identificator)
        {
            bool is_valid = false;
            Regex regex = new Regex(@"^3-1[0-9]{2}-[0-9]{6}$");

            if (string.IsNullOrEmpty(identificator))
                is_valid = false;
            else if (!regex.Match(identificator).Success)//formato incorrect
                is_valid = false;
            else
                is_valid = true;

            return is_valid;
        }

        private bool Validate_RUC_EC(string identificador)
        {
            bool is_valid = false;
            Regex regex = new Regex(@"^[0-9]{10}001$");

            if (string.IsNullOrEmpty(identificador))
            {
                is_valid = false;
            }
            else if (regex.Match(identificador).Success)
            {
                short twoFirstNumbers = 0;

                short.TryParse(identificador.Substring(0, 2), out twoFirstNumbers);

                is_valid = twoFirstNumbers >= 1 && twoFirstNumbers <= 24 ? true : false;
            }

            return is_valid;
        }

        private bool Validate_NIT_ELSALVADOR(string identificador)
        {
            bool is_valid = false;

            int NumGuiones = identificador.ToArray().Where(lr => lr.Equals('-')).Count();
            if (NumGuiones != 3)
            {
                return false;
            }

            Regex regex = new Regex(@"^[0-9]{4}-[0-9]{6}-[0-9]{3}-[0-9]{1}$");

            if (string.IsNullOrEmpty(identificador))
            {
                is_valid = false;
            }
            else if (regex.Match(identificador).Success)
            {
                string[] partsNit = identificador.Split('-');
                short firstPartNit = 0;
                short.TryParse(partsNit[0], out firstPartNit);
                string SecondPartNit = partsNit[1];

                if (firstPartNit < 1420 || firstPartNit >= 9000)
                {
                    short twoFirstNumbers = 0;
                    short twoSecondNumbers = 0;
                    short.TryParse(SecondPartNit.Substring(0, 2), out twoFirstNumbers);
                    short.TryParse(SecondPartNit.Substring(2, 2), out twoSecondNumbers);

                    is_valid = twoFirstNumbers < 32 && twoSecondNumbers < 13 ? true : false;
                }
                else
                {
                    is_valid = false;
                }

            }
            else
            {
                is_valid = false;
            }

            return is_valid;
        }

        private bool Validate_NIT_Philipinnes(string identificator)
        {
            return Regex.IsMatch(identificator, @"^([\d]{3}-[\d]{3}-[\d]{3}-[\d]{3})$");
        }

        public bool Validate_NIT_VENEZUELA(string identificator)
        {
            if (string.IsNullOrEmpty(identificator))
            {
                return false;
            }

            if (identificator.Length > 2 && identificator[1] == '-' && identificator.Length == 11)
            {
                identificator = identificator.Replace("-", "");
            }

            if (identificator.Contains("-"))
            {
                return false;
            }

            bool is_valid = false;
            int valor = 0;
            int suma = 0;
            int valorControl = 0;

            string id = identificator.Replace("-", "");
            string digito_control = identificator.Substring(identificator.Length - 1, 1).ToString();

            if (id.Length == 10 && (id.Substring(0, 1) == "J" || id.Substring(0, 1) == "G" || id.Substring(0, 1) == "V" || id.Substring(0, 1) == "E" || id.Substring(0, 1) == "P"))
            {
                switch (id.Substring(0, 1))
                {
                    case "V":
                        suma = 1 * 4;
                        break;
                    case "E":
                        suma = 2 * 4;
                        break;
                    case "J":
                        suma = 3 * 4;
                        break;
                    case "P":
                        suma = 4 * 4;
                        break;
                    case "G":
                        suma = 5 * 4;
                        break;
                }

                int.TryParse(id.Substring(1, 1), out valor);
                suma += valor * 3;
                int.TryParse(id.Substring(2, 1), out valor);
                suma += valor * 2;
                int.TryParse(id.Substring(3, 1), out valor);
                suma += valor * 7;
                int.TryParse(id.Substring(4, 1), out valor);
                suma += valor * 6;
                int.TryParse(id.Substring(5, 1), out valor);
                suma += valor * 5;
                int.TryParse(id.Substring(6, 1), out valor);
                suma += valor * 4;
                int.TryParse(id.Substring(7, 1), out valor);
                suma += valor * 3;
                int.TryParse(id.Substring(8, 1), out valor);
                suma += valor * 2;

                var doubleSuma = 0.0;
                if (double.TryParse((suma / 11).ToString(), out doubleSuma))
                {
                    var DG = 11 - (suma - (Math.Round(doubleSuma) * 11));

                    if (DG > 9)
                        DG = 0;

                    int.TryParse(digito_control, out valorControl);
                    if (valorControl == DG)
                    {
                        is_valid = true;
                    }
                    else
                    {
                        is_valid = false;
                    }
                }
                else
                    is_valid = false;
            }
            else
                is_valid = false;


            return is_valid;
        }

        private bool Validate_NIT_PUERTORICO(string identificator)
        {
            bool is_valid = false;
            Regex regex = new Regex(@"^[0-9]{2}-[0-9]{7}$");

            if (string.IsNullOrEmpty(identificator))
                is_valid = false;
            else if (!regex.Match(identificator).Success)//formato incorrect
                is_valid = false;
            else
                is_valid = true;



            return is_valid;
        }

        private bool Validate_NIT_URUGUAY(string identificator)
        {
            bool is_valid = false;
            int valorControl = 0;

            string id = identificator.Replace("-", "");
            string digito_control = identificator.Substring(identificator.Length - 1, 1).ToString();

            if (string.IsNullOrEmpty(identificator))
                is_valid = false;
            else if (id.Length != 12)
                is_valid = false;
            else
            {
                int suma = 0;
                int valor = 0;
                int resultVerification = 0;

                if (digito_control != "K")
                {
                    int.TryParse(digito_control, out valorControl);
                }

                int.TryParse(id.Substring(0, 1), out valor);
                suma += valor * 4;
                int.TryParse(id.Substring(1, 1), out valor);
                suma += valor * 3;
                int.TryParse(id.Substring(2, 1), out valor);
                suma += valor * 2;
                int.TryParse(id.Substring(3, 1), out valor);
                suma += valor * 9;
                int.TryParse(id.Substring(4, 1), out valor);
                suma += valor * 8;
                int.TryParse(id.Substring(5, 1), out valor);
                suma += valor * 7;
                int.TryParse(id.Substring(6, 1), out valor);
                suma += valor * 6;
                int.TryParse(id.Substring(7, 1), out valor);
                suma += valor * 5;
                int.TryParse(id.Substring(8, 1), out valor);
                suma += valor * 4;
                int.TryParse(id.Substring(9, 1), out valor);
                suma += valor * 3;
                int.TryParse(id.Substring(10, 1), out valor);
                suma += valor * 2;

                resultVerification = (11 - (suma % 11));

                switch (resultVerification)
                {
                    case 11:
                        if (digito_control == "0")
                            is_valid = true;
                        break;

                    case 10:
                        if (digito_control == "1")
                            is_valid = true;
                        break;
                    default:
                        if (valorControl == resultVerification)
                        {
                            is_valid = true;
                        }
                        else
                        {
                            is_valid = false;
                        }
                        break;
                }
            }
            return is_valid;
        }

        private bool Validate_NIT_NICARAGUA(string identificator)
        {
            bool is_valid = false;

            if ((identificator.Length == 13 || identificator.Length == 14) && identificator.Substring(0, 1) == "J")
            {
                is_valid = true;
            }
            return is_valid;
        }

        private bool Validate_NIT_REPUBLICADOMINICANA(string identificator)
        {
            bool is_valid = false;

            string id = identificator.Replace("-", "");
            string digito_control = identificator.Substring(identificator.Length - 1, 1).ToString();

            if (id.Length == 9 && id.Substring(0, 1) == "1")
            {
                int suma = 0;
                int valor = 0;
                int Validator = 0;
                int verificacion = 0;
                int.TryParse(digito_control, out Validator);

                int.TryParse(id.Substring(0, 1), out valor);
                suma += valor * 7;
                int.TryParse(id.Substring(1, 1), out valor);
                suma += valor * 9;
                int.TryParse(id.Substring(2, 1), out valor);
                suma += valor * 8;
                int.TryParse(id.Substring(3, 1), out valor);
                suma += valor * 6;
                int.TryParse(id.Substring(4, 1), out valor);
                suma += valor * 5;
                int.TryParse(id.Substring(5, 1), out valor);
                suma += valor * 4;
                int.TryParse(id.Substring(6, 1), out valor);
                suma += valor * 3;
                int.TryParse(id.Substring(7, 1), out valor);
                suma += valor * 2;

                verificacion = (suma % 11);

                switch (verificacion)
                {
                    case 0:
                        if (verificacion == Validator)
                        {
                            is_valid = true;
                        }
                        break;
                    case 1:
                        if (verificacion == Validator)
                        {
                            is_valid = true;
                        }
                        break;
                    case 99:
                        is_valid = false;
                        break;
                    default:
                        if ((11 - verificacion) == Validator)
                        {
                            is_valid = true;
                        }
                        break;
                }
            }
            return is_valid;
        }

        private bool Validate_Default(string identificator)
        {
            bool is_valid = true;
            //Regex regex = new Regex(@"^[a-zA-Z0-9\-]+$");

            if (string.IsNullOrEmpty(identificator))
                is_valid = false;
            //else if (!regex.Match(identificator).Success)//formato incorrect
            //    is_valid = false;
            //else if (identificator.Length < 6 || identificator.Length > 20)
            //    is_valid = false;
            //else
            //    is_valid = true;

            return is_valid;
        }

        private bool ValidateLengthString(string identificator)
        {
            Int64 ic = 0;

            if (identificator.Length == 11)
            {
                if (Int64.TryParse(identificator, out ic))
                {
                    return true;
                }
            }

            return false;
        }

        private bool ValidateTwoFirstNumbersAreValids(string identificator)
        {
            int init_number = 0;

            int.TryParse(identificator.Substring(0, 2), out init_number);

            if (init_number > 0)
            {
                List<int> validInitNumber = new List<int> { 20, 23, 24, 27, 28, 30, 31, 32, 33, 34 };

                if (validInitNumber.Contains(init_number))
                    return true;
                else
                    return false;
            }
            else
                return false;
        }

        private bool ValidateNumberCUIT(string p_indentificador)
        {
            int suma = 0;

            short[] arr_digits = p_indentificador.Select(c => Convert.ToInt16(c.ToString())).ToArray();
            int[] serie = new int[] { 5, 4, 3, 2, 7, 6, 5, 4, 3, 2 };

            //suma += arr_digits[9] * 2;
            //suma += arr_digits[8] * 3;
            //suma += arr_digits[7] * 4;
            //suma += arr_digits[6] * 5;
            //suma += arr_digits[5] * 6;
            //suma += arr_digits[4] * 7;
            //suma += arr_digits[3] * 2;
            //suma += arr_digits[2] * 3;
            //suma += arr_digits[1] * 4;
            //suma += arr_digits[0] * 5;

            for (int i = 0; i < 10; i++)
                suma += arr_digits[i] * serie[i];

            int digito_verificador = 11 - (suma % 11);

            if (digito_verificador == 11)
                digito_verificador = 0;
            else if (digito_verificador == 10)
                digito_verificador = 9;

            return (arr_digits[10] == digito_verificador);
        }

        private int Asc(string caracter)
        {
            return Encoding.ASCII.GetBytes(caracter)[0];
        }
    }
}
