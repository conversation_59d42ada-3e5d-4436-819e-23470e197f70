<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="AutoMapper" version="6.2.2" targetFramework="net472" />
  <package id="AWSSDK.Core" version="3.3.19" targetFramework="net452" />
  <package id="AWSSDK.S3" version="3.3.14" targetFramework="net452" />
  <package id="Microsoft.AspNetCore.Http.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Http.Features" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="8.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Configuration.Abstractions" version="5.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.DependencyInjection" version="8.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="8.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.FileProviders.Abstractions" version="5.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Hosting.Abstractions" version="5.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.ObjectPool" version="5.0.11" targetFramework="net472" />
  <package id="Microsoft.Extensions.Primitives" version="5.0.0" targetFramework="net472" />
  <package id="Newtonsoft.Json" version="13.0.1" targetFramework="net472" />
  <package id="RabbitMQ.Client" version="6.2.2" targetFramework="net472" />
  <package id="Redarbor.Api.Contracts" version="3.0.4" targetFramework="net472" />
  <package id="Redarbor.Api.Impl" version="3.1.4" targetFramework="net472" />
  <package id="Redarbor.Cache" version="2.0.2" targetFramework="net472" />
  <package id="Redarbor.Dictionaries.Consumer" version="4.6.2" targetFramework="net472" />
  <package id="Redarbor.Kpi.Consumer" version="3.2.0" targetFramework="net472" />
  <package id="Redarbor.Kpi.Consumer.Abstractions" version="3.2.1" targetFramework="net472" />
  <package id="Redarbor.Quiz.Contract.ServiceLibrary" version="1.0.48" targetFramework="net452" />
  <package id="Redarbor.RabbitMQ" version="1.0.0.17" targetFramework="net472" />
  <package id="Redarbor.RabbitMQ.Abstractions" version="1.0.0.17" targetFramework="net472" />
  <package id="Redarbor.RabbitMQ.Extensions" version="1.0.0.17" targetFramework="net472" />
  <package id="Redarbor.RabbitMQ.Model" version="1.0.0.17" targetFramework="net472" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net472" />
  <package id="System.Configuration.ConfigurationManager" version="4.7.0" targetFramework="net472" />
  <package id="System.Memory" version="4.5.4" targetFramework="net472" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net472" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net472" />
  <package id="System.Security.AccessControl" version="4.7.0" targetFramework="net472" />
  <package id="System.Security.Permissions" version="4.7.0" targetFramework="net472" />
  <package id="System.Security.Principal.Windows" version="4.7.0" targetFramework="net472" />
  <package id="System.Text.Encodings.Web" version="4.5.0" targetFramework="net472" />
  <package id="System.Threading.Channels" version="4.7.1" targetFramework="net472" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net472" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net472" />
</packages>