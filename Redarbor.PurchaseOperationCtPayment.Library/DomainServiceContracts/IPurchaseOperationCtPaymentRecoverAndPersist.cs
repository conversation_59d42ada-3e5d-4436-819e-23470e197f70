using Redarbor.PurchaseOperationCtPayment.Library.Entities;
using System;
using System.Collections.Generic;
using System.Text;

namespace Redarbor.PurchaseOperationCtPayment.Library.DomainServiceContracts
{
    public interface IPurchaseOperationCtPaymentRecoverAndPersist
    {
        bool Insert(PurchaseOperationCtPaymentEntity purchaseOperationCtPayment);
        int GetPurchaseOperationCT(long IdPurchaseOperationPayment, short IdPortal);
    }
}
