use repo_products;

DROP PROCEDURE IF EXISTS `CProductGetLastDateModByIdCompanyProductAndPortalId`;
DROP PROCEDURE IF EXISTS `CompanyProductGetByCompanyProdIdAndPortalId`;

DELIMITER $$

-- CProductGetLastDateModByIdCompanyProductAndPortalId
CREATE DEFINER=`root`@`localhost` PROCEDURE `CProductGetLastDateModByIdCompanyProductAndPortalId`(
    IN _IdPortal tinyint,
    IN _IdCompanyProduct BIGINT
)
BEGIN
    SELECT DateMod
    FROM CompanyProducts
    WHERE  Id = _IdCompanyProduct
      AND PortalId = _IdPortal;
END$$


-- 
CREATE DEFINER=`root`@`localhost` PROCEDURE `CompanyProductGetByCompanyProdIdAndPortalId`(
_CompanyProductId BIGINT, 
_IdPortal tinyint)
BEGIN

SELECT 
    Id,
    ProductId, 
    CompanyId, 
    CustomerId,
    PortalId, 
    DateAdd,
    DateMod,
    UserId,
    Price,
    ServiceTypeId,
    SourceId,
    PaymentOriginId,
    DateActivation,
    DateExpiration,
    ExpirationDays,
    StatusId,
    ClientIp,
    GroupId,
    SubgroupId,
    LimitationMultiplier
FROM CompanyProducts
WHERE PortalId = _IdPortal
AND Id = _CompanyProductId;
END$$