USE `repo_products`;

-- DROP FUNCTIONS AND PROCEDURES

DROP FUNCTION IF EXISTS `FnNowByPortal`;
DROP PROCEDURE IF EXISTS `CompanyProductAllFeaturesGetLastDateModByIdCompanyProduct`;
DROP PROCEDURE IF EXISTS `CompanyProductAllFeaturesGetLastDateModByIdCustomer`;
DROP PROCEDURE IF EXISTS `CompanyProductDefinitionSetLimitationMultiplier`;
DROP PROCEDURE IF EXISTS `CompanyProductsGetFeaturesByCompanyProductId`;
DROP PROCEDURE IF EXISTS `DeleteCompanyProductFeature`;
DROP PROCEDURE IF EXISTS `GetAllCompanyActiveProducts`;
DROP PROCEDURE IF EXISTS `GetAllCompanyActiveProductsMaxDateMod`;
DROP PROCEDURE IF EXISTS `GetAllCompanyProductFeatures`;
DROP PROCEDURE IF EXISTS `GetAllCompanyProductFeaturesByCustomer`;
DROP PROCEDURE IF EXISTS `GetAllCompanyProducts`;
DROP PROCEDURE IF EXISTS `GetAllCompanyProductsMaxDateMod`;
DROP PROCEDURE IF EXISTS `GetAllCompanyProductsSelectByCompany`;
DROP PROCEDURE IF EXISTS `GetAllCustomerActiveProducts`;
DROP PROCEDURE IF EXISTS `GetAllCustomerActiveProductsMaxDateMod`;
DROP PROCEDURE IF EXISTS `GetAllCustomerProducts`;
DROP PROCEDURE IF EXISTS `GetAllCustomerProductsMaxDateMod`;
DROP PROCEDURE IF EXISTS `GetCompanyProductSelect`;
DROP PROCEDURE IF EXISTS `InsertCompanyProductFeatureExtra`;
DROP PROCEDURE IF EXISTS `UpdateCompanyProduct`;
DROP PROCEDURE IF EXISTS `UpdateCompanyProductFeature`;


DELIMITER ;;
CREATE DEFINER=`root`@`localhost` FUNCTION `FnNowByPortal`(p_idportal tinyint) RETURNS datetime
    DETERMINISTIC
BEGIN 

DECLARE l_offset int; 

DECLARE l_now DATETIME;

DECLARE l_time_zone CHAR(64);



SET SESSION TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;



CASE p_idportal

WHEN 0 THEN SELECT 'etc/UTC' INTO l_time_zone;



  WHEN 1 THEN SELECT 'America/Bogota' INTO l_time_zone;

  WHEN 2 THEN SELECT 'America/Mexico_City' INTO l_time_zone;

  WHEN 3 THEN SELECT 'America/Lima' INTO l_time_zone;

  WHEN 4 THEN SELECT 'America/Santiago' INTO l_time_zone;

  WHEN 5 THEN SELECT 'America/Argentina/Buenos_Aires' INTO l_time_zone;

  WHEN 6 THEN SELECT 'America/Guayaquil' INTO l_time_zone;

  WHEN 7 THEN SELECT 'America/Caracas' INTO l_time_zone;

  WHEN 8 THEN SELECT 'America/Costa_Rica' INTO l_time_zone;

  WHEN 9 THEN SELECT 'America/Guatemala' INTO l_time_zone;

  WHEN 10 THEN SELECT 'America/El_Salvador' INTO l_time_zone;

  WHEN 11 THEN SELECT 'America/Montevideo' INTO l_time_zone;

  WHEN 12 THEN SELECT 'America/Asuncion' INTO l_time_zone;

  WHEN 13 THEN SELECT 'America/Panama' INTO l_time_zone;

  WHEN 14 THEN SELECT 'America/Tegucigalpa' INTO l_time_zone;

  WHEN 15 THEN SELECT 'America/Managua' INTO l_time_zone;

  WHEN 16 THEN SELECT 'America/Santo_Domingo' INTO l_time_zone;

  WHEN 17 THEN SELECT 'America/La_Paz' INTO l_time_zone;

  WHEN 18 THEN SELECT 'America/Havana' INTO l_time_zone;

  WHEN 19 THEN SELECT 'America/Puerto_Rico' INTO l_time_zone;



	WHEN 20 THEN SELECT 'Australia/Canberra' INTO l_time_zone;

	WHEN 21 THEN SELECT 'Canada/Eastern' INTO l_time_zone;

	WHEN 22 THEN SELECT 'Europe/Dublin' INTO l_time_zone;

	WHEN 23 THEN SELECT 'Antarctica/McMurdo' INTO l_time_zone;

	WHEN 24 THEN SELECT 'Africa/Johannesburg' INTO l_time_zone;

	WHEN 25 THEN SELECT 'Europe/London' INTO l_time_zone;

	WHEN 26 THEN SELECT 'EST' INTO l_time_zone;

	WHEN 27 THEN SELECT 'Asia/Singapore' INTO l_time_zone;

	WHEN 28 THEN SELECT 'Asia/Calcutta' INTO l_time_zone;

	WHEN 29 THEN SELECT 'Asia/Kuala_Lumpur' INTO l_time_zone;

	WHEN 30 THEN SELECT 'Asia/Manila' INTO l_time_zone;

	WHEN 31 THEN SELECT 'Africa/Nairobi' INTO l_time_zone;

	WHEN 32 THEN SELECT 'Asia/Jakarta' INTO l_time_zone;

	WHEN 33 THEN SELECT 'Asia/Hong_Kong' INTO l_time_zone;



	WHEN 34 THEN SELECT 'Europe/Madrid' INTO l_time_zone;

	WHEN 35 THEN SELECT 'Europe/Madrid' INTO l_time_zone;



  	WHEN 36 THEN SELECT 'Europe/Istanbul' INTO l_time_zone;

	WHEN 38 THEN SELECT 'Africa/Casablanca' INTO l_time_zone;

ELSE SELECT 'etc/UTC' INTO l_time_zone;

END CASE;



-- select time_zone into l_time_zone from portal_config_datetime_offset where idportal = p_idportal ; 

select CONVERT_TZ(now(),'SYSTEM',l_time_zone) INTO l_now;

SET SESSION TRANSACTION ISOLATION LEVEL REPEATABLE READ ;







RETURN l_now; 

END ;;
DELIMITER ;

DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `CompanyProductAllFeaturesGetLastDateModByIdCompanyProduct`(

    IN _idcompany INT,

    IN _idportal tinyint,

    IN _idcompanyproduct bigint

)
BEGIN



    SELECT MAX(DateMod)

    FROM CompanyProductsFeatures

    WHERE CompanyId =_idcompany 

      AND CompanyProductId = _idcompanyproduct

      AND PortalId = _idportal;

END ;;
DELIMITER ;

DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `CompanyProductAllFeaturesGetLastDateModByIdCustomer`(

    IN _idCustomer BIGINT,

    IN _idPortal tinyint,

    IN _idCompanyProduct BIGINT

)
BEGIN



    SELECT MAX(DateMod)

    FROM CompanyProductsFeatures

    WHERE CustomerId = _idCustomer 

      AND CompanyProductId = _idcompanyproduct

      AND PortalId = _idportal;

END ;;
DELIMITER ;

DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `CompanyProductDefinitionSetLimitationMultiplier`(_id INT,_limitationMultiplier tinyint)
BEGIN



UPDATE CompanyProducts

SET LimitationMultiplier = _limitationMultiplier

WHERE Id = _id;



END ;;
DELIMITER ;

DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `CompanyProductsGetFeaturesByCompanyProductId`(

			_portal_id tinyint,	

			_company_product_id BIGINT,

		  _company_id int

		)
Begin

			SELECT

			Id,

      CustomerId,

			CompanyId,

      ProductId,			

			CompanyProductId,

      PortalId,

      GroupId,

			AmbitId,

			

			PortalFeatureId,

			AvailableUnits,

			InitialUnits,

			IsUnlimited,

			IsSimultaneous,

			IsRecurrent,

			FrequencyRenewDays,

			FeatureTypeId,

			StatusId,

			DateAdd,

			DateMod,

			DateLastConsumed,

			GroupId

		  FROM

			CompanyProductsFeatures

		  WHERE

			CompanyId = _company_id AND

			CompanyProductId = _company_product_id AND

			PortalId = _portal_id;

		End ;;
DELIMITER ;

DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `DeleteCompanyProductFeature`(

_id   int

)
Begin

 

DELETE FROM CompanyProductsFeatures WHERE Id=_id;

                             

 End ;;
DELIMITER ;

DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `GetAllCompanyActiveProducts`(

    IN _companyId int,

    IN _portalId tinyint

  )
BEGIN

	SELECT 

    Id,

    ProductId,

    CompanyId,

    CustomerId,

    PortalId,

    `DateAdd`,

    DateMod,

    UserId,

    Price,

    ServiceTypeId,

    SourceId,

    PaymentOriginId,

    DateActivation,

    DateExpiration,

    ExpirationDays,

    StatusId,

    GroupId,

    ClientIp,

    SubgroupId,

    ProductType

	FROM CompanyProducts

    WHERE CompanyId = _companyId

    and PortalId = _portalId

    and StatusId =2

    and DateActivation <= FnNowByPortal(_portalId)

    and DateExpiration > FnNowByPortal(_portalId)

    order by DateActivation;    

END ;;
DELIMITER ;

DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `GetAllCompanyActiveProductsMaxDateMod`(

  IN _idcompany INT,

  IN _idportal INT

)
BEGIN



SELECT

  max(DateMod)

FROM

  CompanyProducts    

WHERE CompanyId = _idcompany

    and PortalId = _idportal

    and StatusId =2

    and DateActivation <= FnNowByPortal(_idportal)

    and DateExpiration > FnNowByPortal(_idportal);

END ;;
DELIMITER ;

DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `GetAllCompanyProductFeatures`(

	_portal_id tinyint,	

	_company_product_id BIGINT,

  _company_id int

)
Begin



	SELECT

    Id,

    CompanyId,

    CustomerId,

    ProductId,

    CompanyProductId,

    PortalId,

    AmbitId,

    PortalFeatureId,

    AvailableUnits,

    InitialUnits,

    IsUnlimited,

    IsSimultaneous,

    IsRecurrent,

    FrequencyRenewDays,

    FeatureTypeId,

    StatusId,

    DateAdd,

    DateMod,

    DateLastConsumed,

    GroupId,

    ConsumedUnits

  FROM

    CompanyProductsFeatures

  WHERE

    CompanyId = _company_id AND

    CompanyProductId = _company_product_id AND

    PortalId = _portal_id;

End ;;
DELIMITER ;

DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `GetAllCompanyProductFeaturesByCustomer`(

	_portalId tinyint,	

	_companyProductId BIGINT,

  _customerId BIGINT

)
Begin



	SELECT

    Id,

    CompanyId,

    CustomerId,

    ProductId,

    CompanyProductId,

    PortalId,

    AmbitId,

    PortalFeatureId,

    AvailableUnits,

    InitialUnits,

    IsUnlimited,

    IsSimultaneous,

    IsRecurrent,

    FrequencyRenewDays,

    FeatureTypeId,

    StatusId,

    DateAdd,

    DateMod,

    DateLastConsumed,

    GroupId,

    ConsumedUnits

  FROM

    CompanyProductsFeatures

  WHERE

    CompanyId = _customerId AND

    CompanyProductId = _companyProductId AND

    PortalId = _portalId;

End ;;
DELIMITER ;

DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `GetAllCompanyProducts`(

    IN _companyId int,

    IN _portalId tinyint

  )
BEGIN



	SELECT 

    Id,

    ProductId,

    CompanyId,

    CustomerId,

    PortalId,

    DateAdd,

    DateMod,

    UserId,

    Price,

    ServiceTypeId,

    ProductType,

    SourceId,

    PaymentOriginId,

    DateActivation,

    DateExpiration,

    ExpirationDays,

    StatusId,

    GroupId,

    ClientIp,

    SubgroupId

	FROM CompanyProducts

    WHERE CompanyId = _companyId

    and PortalId = _portalId

    order by DateActivation;    

END ;;
DELIMITER ;

DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `GetAllCompanyProductsMaxDateMod`(

  IN _idcompany INT,

  IN _idportal INT

)
BEGIN





SELECT

  max(DateMod)

FROM

  CompanyProducts    

WHERE CompanyId = _idcompany

    and PortalId = _idportal;

END ;;
DELIMITER ;

DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `GetAllCompanyProductsSelectByCompany`(_company_id int, _portal_id tinyint)
Begin

			SELECT Id, ProductId, DateAdd, GroupId, DateActivation, DateExpiration, StatusId

        FROM CompanyProducts 

		 WHERE CompanyId = _company_id

		 AND PortalId = _portal_id

		 ORDER BY DateActivation, DateAdd;

End ;;
DELIMITER ;

DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `GetAllCustomerActiveProducts`(

    IN _customerId BIGINT,

    IN _portalId tinyint

  )
BEGIN

	SELECT 

    Id,

    ProductId,

    CompanyId,

    CustomerId,

    PortalId,

    `DateAdd`,

    DateMod,

    UserId,

    Price,

    ServiceTypeId,

    SourceId,

    PaymentOriginId,

    DateActivation,

    DateExpiration,

    ExpirationDays,

    StatusId,

    GroupId,

    ClientIp,

    SubgroupId,

    ProductType

	FROM CompanyProducts

    WHERE CustomerId = _customerId

    and PortalId = _portalId

    and StatusId =2

    and DateActivation <= FnNowByPortal(_portalId)

    and DateExpiration > FnNowByPortal(_portalId)

    order by DateActivation;    

END ;;
DELIMITER ;

DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `GetAllCustomerActiveProductsMaxDateMod`(

  IN _idCustomer BIGINT,

  IN _idPortal INT

)
BEGIN



SELECT

  max(DateMod)

FROM

  CompanyProducts    

WHERE CustomerId = _idCustomer

    and PortalId = _idPortal

    and StatusId =2

    and DateActivation <= FnNowByPortal(_idportal)

    and DateExpiration > FnNowByPortal(_idportal);

END ;;
DELIMITER ;

DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `GetAllCustomerProducts`(

    IN _customerId int,

    IN _portalId tinyint

  )
BEGIN



	SELECT 

    Id,

    ProductId,

    CompanyId,

    CustomerId,

    PortalId,

    DateAdd,

    DateMod,

    UserId,

    Price,

    ServiceTypeId,

    ProductType,

    SourceId,

    PaymentOriginId,

    DateActivation,

    DateExpiration,

    ExpirationDays,

    StatusId,

    GroupId,

    ClientIp,

    SubgroupId

	FROM CompanyProducts

    WHERE CustomerId = _customerId

    and PortalId = _portalId

    order by DateActivation;    

END ;;
DELIMITER ;

DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `GetAllCustomerProductsMaxDateMod`(

  IN _idCustomer BIGINT,

  IN _idPortal INT

)
BEGIN





SELECT

  max(DateMod)

FROM

  CompanyProducts    

WHERE CustomerId = _idCustomer

    and PortalId = _idPortal;

END ;;
DELIMITER ;

DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `GetCompanyProductSelect`(_companyId int, _portalId tinyint,_companyProductId bigint)
Begin

SELECT

  cp.Id,

  cp.ProductId,

  cp.CompanyId,

  cp.PortalId,

  cp.DateAdd,

  cp.DateMod,

  cp.UserId,

  cp.Price,

  cp.ServiceTypeId,

  cp.SourceId,

  cp.PaymentOriginId,

  cp.DateActivation,

  cp.DateExpiration,

  cp.ExpirationDays,

  cp.StatusId,

  cp.ClientIp,

  cp.GroupId,

  cp.SubgroupId,

  cp.ProductType,

  cp.LimitationMultiplier,
  cp.SelfRenewing

FROM CompanyProducts cp  

WHERE cp.CompanyId = _companyId

AND cp.Id = _companyProductId

AND cp.PortalId = _portalId

AND cp.DateExpiration > FnNowByPortal(_portalId)

AND cp.StatusId = 2

ORDER BY cp.GroupId DESC, DateAdd DESC;

END ;;
DELIMITER ;

DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `InsertCompanyProductFeatureExtra`(

                                _company_id INT,

                                _customer_id INT,

                                _company_product_id BIGINT,

                                _portal_id TINYINT ,

                                _product_id INT,

                                _ambit_id INT,

                                _available_units INT ,

                                _initial_units INT ,

                                _is_unlimited TINYINT ,

                                _is_simultaneous TINYINT ,

                                _is_recurrent TINYINT ,

                                _frequency_renew_days INT ,

                                _feature_type_id TINYINT 

                              )
Begin



INSERT into

  CompanyProductsFeatures (

  CompanyId, CustomerId, ProductId, CompanyProductId, PortalId, AmbitId, PortalFeatureId, AvailableUnits,

  InitialUnits, IsUnlimited, IsSimultaneous, IsRecurrent, FrequencyRenewDays, FeatureTypeId,

  StatusId, DateAdd, DateMod)

VALUES

  (_company_id,_customer_id, _product_id, _company_product_id, _portal_id, _ambit_id, 0, _available_units, _initial_units, _is_unlimited,

  _is_simultaneous, _is_recurrent, _frequency_renew_days, _feature_type_id, 2, FnNowByPortal(_portal_id),

  FnNowByPortal(_portal_id));

 

End ;;
DELIMITER ;

DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `UpdateCompanyProduct`(_id INT,_date_activation datetime, _date_expiration datetime, _idportal tinyint)
BEGIN

  UPDATE CompanyProducts

  SET 

    DateActivation = _date_activation,

    DateExpiration = _date_expiration,

    DateMod = FnNowByPortal(_idportal)

  WHERE Id = _id;

END ;;
DELIMITER ;

DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `UpdateCompanyProductFeature`(

                                _id INT,                                    

                                _available_units INT ,

                                _initial_units INT ,

                                _is_unlimited TINYINT ,

                                _is_simultaneous TINYINT ,

                                _is_recurrent TINYINT ,

                                _frequency_renew_days INT ,

                                _portal_id TINYINT 

                              )
Begin

 

UPDATE CompanyProductsFeatures

 SET

        AvailableUnits  = _available_units,

        InitialUnits  = _initial_units,

        IsUnlimited = _is_unlimited,

        IsSimultaneous = _is_simultaneous,

        IsRecurrent = _is_recurrent,

        FrequencyRenewDays = _frequency_renew_days,

        DateMod= FnNowByPortal(_portal_id)

  WHERE

        Id = _id ;                       

End ;;
DELIMITER ;

