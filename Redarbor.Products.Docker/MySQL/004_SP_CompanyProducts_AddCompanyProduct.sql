use repo_products;
-- DROP PROCEDURES
DROP PROCEDURE IF EXISTS `AddCompanyProduct`;
DROP PROCEDURE IF EXISTS `AddCompanyProductFeature`;
DROP PROCEDURE IF EXISTS `GetProProductPortalForAddingCompanyProduct`;
DROP PROCEDURE IF EXISTS `GetProProductPortalForAddingCompanyProductFeature`;

-- PROCEDURES

DELIMITER $$

--  GET TEMPLATE PRODUCT

CREATE DEFINER=`root`@`localhost` PROCEDURE `GetProProductPortalForAddingCompanyProduct`(IN _portalId tinyint, IN _productId bigint, IN _idTemporality int)
BEGIN

-- se quitan campos que no podemos pasar aqui .. de donde salen ?? _price, _paymentOriginId,  (SOBRE TODO EL PRICE)

SELECT 
    ppp.ProductId,
    ppp.PortalId, 
    pp.ServiceTypeId, 
    ppp.GroupId,
    pt.ExpirationDays, 
    ppp.LimitationMultiplier, 
    ppp.SubgroupId,
    pt.TemporalityId,
    ppp.IsActiveOCC
FROM 
    ProProductPortal ppp
INNER JOIN
    ProProduct pp
ON
    pp.Id = ppp.ProductId
INNER JOIN
    ProProductPortalTemporality pt
ON
    pp.Id = pt.ProductId
    AND pt.PortalId = _portalId
WHERE 
    ppp.ProductId = _productId 
    AND ppp.PortalId = _portalId
    AND pt.TemporalityId = _idTemporality;

END$$

-- ADD PRODUCT

CREATE DEFINER=`root`@`localhost` PROCEDURE `AddCompanyProduct`( 
  IN _productId bigint,  
  IN _companyId int,
  IN _customerId bigint,
  IN _portalId tinyint,
  IN _userId int,
  IN _price decimal(19,2),
  IN _serviceTypeId tinyint,
  IN _groupId int,
  IN _sourceId int, 
  IN _paymentOriginId int,  
  IN _activationDate datetime,
  IN _expirationDate datetime,
  IN _expirationDays int, 
  IN _clientIp varchar(20),
  IN _productType int,
  IN _limitationMultiplier int,
  IN _subgroupId int,
  IN _idTemporality int,
  IN _isConfigProductInMaster bit(1))
BEGIN

    INSERT INTO CompanyProducts (
      ProductId, CompanyId, CustomerId, PortalId, DateAdd, DateMod, UserId, Price,
      ServiceTypeId, GroupId, SourceId, PaymentOriginId, DateActivation, DateExpiration, ExpirationDays, StatusId,
      ClientIp, ProductType, LimitationMultiplier, SubgroupId, TemporalityId,IsConfigProductInMaster
    ) VALUES (
      _productId, _companyId,_customerId, _portalId, FnNowByPortal(_portalId), FnNowByPortal(_portalId), _userId, _price,
      _serviceTypeId, _groupId, _sourceId, _paymentOriginId, _activationDate, _expirationDate, _expirationDays, 2,
      _clientIp, _productType, _limitationMultiplier, _subgroupId, _idTemporality,_isConfigProductInMaster
    );

    SELECT LAST_INSERT_ID();

END$$

-- GET TEMPLATE PRODUCT FEATURE

CREATE DEFINER=`root`@`localhost` PROCEDURE `GetProProductPortalForAddingCompanyProductFeature`(IN _portalId tinyint, IN _productId bigint)
BEGIN

SELECT 
  pppf.ProductId, 
  pppf.PortalId, 
  pppf.AmbitId, 
  pppf.Id, 
  pppf.AvailableUnits, 
  pppf.InitialUnits, 
  pppf.IsUnlimited,
  pppf.IsSimultaneous, 
  pppf.IsRecurrent, 
  pppf.FrequencyRenewDays, 
  pppf.FeatureTypeId,
  ppp.GroupId
FROM 
  ProProductPortalFeatures pppf
  INNER JOIN ProProductPortal ppp ON pppf.ProductId = ppp.ProductId AND pppf.PortalId = ppp.PortalId
WHERE 
  pppf.PortalId = _portalId 
  AND pppf.ProductId = _productId;

END$$


CREATE DEFINER=`root`@`localhost` PROCEDURE `AddCompanyProductFeature`( 
  IN _companyId int, 
  IN _customerId bigint,
  IN _productId bigint,
  IN _companyProductId bigint,
  IN _portalId tinyint,
  IN _ambitId int,
  IN _userId int,
  IN _portalFeatureId bigint,
  IN _availableUnits int,
  IN _initialUnits int,
  IN _isUnlimited tinyint,
  IN _isSimultaneous tinyint,
  IN _isRecurrent tinyint,
  IN _frequencyRenewDays int,
  IN _featureTypeId tinyint,
  IN _groupId int)
BEGIN

INSERT  
  CompanyProductsFeatures (
  CompanyId, CustomerId, ProductId, CompanyProductId, PortalId, AmbitId, PortalFeatureId, AvailableUnits,
  InitialUnits, IsUnlimited, IsSimultaneous, IsRecurrent, FrequencyRenewDays, FeatureTypeId,
  StatusId, DateAdd, DateMod, GroupId)
VALUES (_companyId, _customerId, _productId, _companyProductId, _portalId, _ambitId, _portalFeatureId,
_availableUnits, _initialUnits, _isUnlimited, _isSimultaneous, _isRecurrent, _frequencyRenewDays, _featureTypeId, 2,
FnNowByPortal(_portalId), FnNowByPortal(_portalId), _groupId);

  SELECT LAST_INSERT_ID();

END$$