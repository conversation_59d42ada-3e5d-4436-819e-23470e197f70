
use repo_products;

DROP PROCEDURE IF EXISTS UpdateStatusCompanyProduct;

DELIMITER $$

CREATE DEFINER=`root`@`localhost` PROCEDURE `UpdateStatusCompanyProduct`(IN _companyProductId BIGINT,
    IN _portalId SMALLINT,
    IN _statusId SMALLINT)

BEGIN
    -- Si es estado Expired, Deleted, Canceled (StatusEnum) solucion de AE
    IF _statusId IN (3,4,13) THEN       
        UPDATE CompanyProducts
            SET StatusId = _statusId,
                DateExpiration = FnNowByPortal(_portalId),
                DateMod = FnNowByPortal(_portalId)
        WHERE Id = _companyProductId;
    ELSE
        UPDATE CompanyProducts
            SET StatusId = _statusId,
                DateMod = FnNowByPortal(_portalId)
        WHERE Id = _companyProductId;
    END IF;

END$$