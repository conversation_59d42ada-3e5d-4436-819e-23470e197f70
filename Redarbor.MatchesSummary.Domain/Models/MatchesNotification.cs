using Redarbor.MatchesSummary.Domain.Enums;
using Redarbor.Procs.Domain.Extensions;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Redarbor.MatchesSummary.Domain.Models
{
    public class MatchesNotification
    {
        public MatchesNotificationContext Context { get; }

        public ICollection<BaseMatchesSummary> Summaries { get; } = new List<BaseMatchesSummary>();

        public MatchesNotification(ICollection<BaseMatchesSummary> summaries, MatchesNotificationContext context)
        {
            Summaries = summaries;
            Context = context;
        }

        public string Subject => Summaries.Count == 1
            ? $"Inscripciones más recientes en tu proceso de selección abierto"
            : "Inscripciones más recientes en tus procesos de selección abiertos";

        public bool IsSingular => Summaries.Select(x => x.NewMatches).Sum() == 1;

        public Uri PortalBaseUrl => Summaries.First().PortalBaseUrl;

        public string PortalName => Summaries.First().IdPortal.GetDescription();

        public Uri AvisoLegalUrl => Summaries.First().AvisoLegalUrl;

        public Uri PrivacidadUrl => Summaries.First().PrivacidadUrl;

        public Uri ContactoUrl => Summaries.First().ContactoUrl;

        public string UtmCampaign => Summaries.First().UtmCampaign;

        public int TotalCvs => Summaries.Select(x => x.NewMatches).Sum();

        public MatchesSummaryNotificationType NotificationType => Summaries.First().NotificationType;
    }
}
