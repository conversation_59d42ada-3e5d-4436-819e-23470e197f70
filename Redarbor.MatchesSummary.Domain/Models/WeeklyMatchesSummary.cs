using Redarbor.MatchesSummary.Domain.Enums;
using Redarbor.Procs.Domain.Enums;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Redarbor.MatchesSummary.Domain.Models
{
    public class WeeklyMatchesSummary : BaseMatchesSummary
    {
        public WeeklyMatchesSummary(
            int userId,
            int offerId,
            int companyId,
            string offerTitle,
            PortalEnum portal,
            int newsletterId,
            long subscriptionId,
            string utmCampaign,
            string domainPrefix = null)
            : base(userId, offerId, companyId, offerTitle, portal, newsletterId, subscriptionId, MatchesSummaryNotificationType.Weekly, domainPrefix, utmCampaign)
        {
        }

        public ICollection<WeeklySummaryMatch> Matches { get; } = new List<WeeklySummaryMatch>();

        public override int NewMatches => Matches.Count;

        public bool IsSingular => Matches.Count == 1;


        public void AddMatch(string matchName, long matchId)
        {
            var cvDetailUrl = GetQueryWithArgs(new Uri($"{GetPortalUrl(IdPortal)}Company/MatchCvDetail/MatchDetail"), GetQueryParams(new Dictionary<string, object>
            {
                ["oi"] = Encrypt(IdOffer),
                ["ims"] = Encrypt(matchId),
            }));

            Matches.Add(new WeeklySummaryMatch(matchName, cvDetailUrl));
        }

        public void AddMatches(ICollection<Match> matches, ICollection<Candidate> candidates)
        {
            var matchesCandidates = matches.Join(candidates, m => m.IdCandidate, c => c.IdCandidate, (m, c) => (Match: m, Candidate: c)).ToList();
            foreach (var matchCandidate in matchesCandidates)
            {
                var name = $"{matchCandidate.Candidate.Name} {matchCandidate.Candidate.SurName}";
                AddMatch(name, matchCandidate.Match.IdMatch);
            }
        }
    }
}
