using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using MySql.Data.MySqlClient;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Core.Resolver.Contracts.ServiceLibrary;
using Redarbor.Core.Resolver.Contracts.ServiceLibrary.Enums;
using Redarbor.Extensions.Library.Extensions;
using Redarbor.Master.Entities.Offers;
using Redarbor.Master.Entities.Product;
using Redarbor.Offer.Library.DomainServiceContracts;
using Redarbor.Repo.Offer.Library.Configuration;

namespace Redarbor.Offer.Library.DomainServicesImplementations
{
    public class OfferProductTraceRecoverAndPersist : IOfferProductTraceRecoverAndPersist
    {
        private readonly IConnectionStringResolverService _connectionStringResolverService;
        private readonly IExceptionPublisherService _exceptionPublisherService;

        public OfferProductTraceRecoverAndPersist(IConnectionStringResolverService connectionStringResolverService,
            IExceptionPublisherService exceptionPublisherService)
        {
            _connectionStringResolverService = connectionStringResolverService;
            _exceptionPublisherService = exceptionPublisherService;
        }

        public List<ProductTraceEntity> GetDeletedOfferProductTrace(int companyId, short idPortal)
        {
            var result = new List<ProductTraceEntity>();
            try
            {
                using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.MasterAux, idPortal)))
                {
                    using (var command = new MySqlCommand("AeGetDeletedOfferProductTrace", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.Add(new MySqlParameter("_companyId", companyId));
                        command.Parameters.Add(new MySqlParameter("_idPortal", idPortal));

                        connection.Open();

                        using (var datareader = command.ExecuteReader())
                        {
                            while (datareader.Read())
                            {
                                result.Add(new ProductTraceEntity()
                                {
                                    idoffer = datareader.GetAsInt("idoffer"),
                                    IdUser = datareader.GetAsInt("iduser"),
                                    date_add = datareader.GetAsDateTime("date_add"),
                                    action = datareader.GetAsString("action"),
                                    IpAddress = datareader.GetAsString("ipaddress")
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"OfferComputrabajoRecoverAndPersist - OfferProductTraceByCompanyId: {ex}");
                _exceptionPublisherService.Publish(ex, "OfferComputrabajoRecoverAndPersist", "OfferProductTraceByCompanyId");
            }

            return result;
        }
    }

}
