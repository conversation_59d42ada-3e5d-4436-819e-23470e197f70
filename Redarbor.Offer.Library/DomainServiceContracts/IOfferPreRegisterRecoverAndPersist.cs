using Redarbor.Master.Entities.Offers;
using System;
using System.Collections.Generic;

namespace Redarbor.Offer.Library.DomainServiceContracts
{
    public interface IOfferPreRegisterRecoverAndPersist
    {
        List<OfferPreRegister> GetOffersByCompanyNit(string companyNit, short idPortal, short idStatus);          
        bool UpdateCargoAndDescriptionById(int id,short idPortal,string cargo, string description);
        bool SetOfferMigratedSuccessById(int id, int idOffer, int idCompany, short status, short idPortal, DateTime dateMigration);
        bool SetOfferStatusById(int id, int idCompany, short status, short idPortal);

        //Obtenemos un elemento
        OfferPreRegister GetOfferById(int id);

        //marcamos el bool selectedToImport
        bool UpdateOfferSelectedToImportByIds(string companyNit, short idPortal, List<int> ids);     

        //Esto para recuperar cuando este el selectedToImport
        List<OfferPreRegister> GetOffersToImportByCompanyNit(string companyNit, short idPortal);

        bool ClearOfferSelectedToImport(string companyNit, short idPortal);

        bool UpdateOfferSelectedToImportById(int id, bool isChecked);

        int GetTotalOffersByCompanyNit(string companyNit, short idPortal, short status);
        int GetTotalOffersToImportByCompany(string companyNit, short idPortal);
    }
}
