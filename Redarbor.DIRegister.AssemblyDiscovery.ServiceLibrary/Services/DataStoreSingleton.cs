using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Redarbor.DIRegister.AssemblyDiscovery.ServiceLibrary.DTO;
using Redarbor.DIRegister.AssemblyDiscovery.ServiceLibrary.Models;

namespace Redarbor.DIRegister.AssemblyDiscovery.ServiceLibrary.Services
{
    public sealed class DataStoreSingleton
    {
        private static volatile DataStoreSingleton _Instance;
        private static object sync = new object();

        static DataStoreSingleton() { }

        public static DataStoreSingleton Instance
        { 
            get
            {
                if (_Instance == null)
                {
                    lock (sync)
                    {
                        if (_Instance == null)
                        {
                            _Instance = new DataStoreSingleton
                            {
                                InspectedDependencies = new InspectedDependencies(),
                                OnActivatedImplementations = new List<Type>(),                               
                            };
                        }
                    }
                }

                return _Instance;
            } 
        }

        public RegisterDefinition RegisterDefinition { get; set; }

        public InspectedDependencies InspectedDependencies { get; set; }

        public List<Type> OnActivatedImplementations { get; set; }

        public List<Type> RedarborImplementationsTypes { get; private set; }

        public List<Assembly> RedarborAssemblies { get; private set; }

        public void Initialize()
        {
            RedarborAssemblies = AssemblyLoadService.GetRedarborAssemblies().ToList();
            RedarborImplementationsTypes = AssemblyImplementationsService.GetRedarborImplementationsTypes();
        }

        public bool IsUsingMaestrosSettings { get; set; }

        public void AddOnActivatedImplementation(Type implementation)
        {
            OnActivatedImplementations.Add(implementation);
        }

        public void CleanUp()
        {
            _Instance = null;
        }
        
    }
}
