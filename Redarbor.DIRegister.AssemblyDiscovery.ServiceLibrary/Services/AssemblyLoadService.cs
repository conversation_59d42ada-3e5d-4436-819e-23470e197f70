using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Reflection;
using Redarbor.DIRegister.AssemblyDiscovery.ServiceLibrary.Helpers;

namespace Redarbor.DIRegister.AssemblyDiscovery.ServiceLibrary.Services
{
    internal class AssemblyLoadService
    {
        internal static IEnumerable<Assembly> GetRedarborAssemblies()
        {
            var domain = AppDomain.CurrentDomain;

            var binFolder = !String.IsNullOrEmpty(domain.RelativeSearchPath) ? Path.Combine(domain.BaseDirectory, domain.RelativeSearchPath) : domain.BaseDirectory;
            
            var asssemblyPathFiles = Directory.GetFiles(binFolder, "Redarbor*.*", SearchOption.AllDirectories).
                Where(path => path.EndsWith(".dll") || path.EndsWith(".exe")).Distinct();

            return asssemblyPathFiles.Select(
                path =>
                {
                    try
                    {
                        return Assembly.LoadFrom(path);
                    }
                    catch (Exception ex)
                    {
                        Trace.TraceError($"Error loading Redarbor assembly: {path}. Exception: {ex.GetDetails()}");
                    }
                    return null;
                }).Where(assembly => assembly != null).OrderBy(assembly => assembly.FullName).Distinct().ToList();
        }

    }
}
