

namespace Redarbor.Folders.Library.DomainServiceContracts
{
    public interface IFoldersRecoverAndPersist
    {
        bool MoveCvsFromFolder(int offerId, long match, int folderDestiny, int folderOrigin, long companyId);
        void DecreaseTotalCandidatesInFolder(long companyId, int offerId, int folderOrigin, int count);
        void IncreaseTotalCandidatesInFolder(long companyId, int offerId, int folderDestiny, int count);
        void RecalculateTotalAndFolders(int offerId, short portalId, int companyId, int total, int folder1, int folder2, int folder3, int folder4, int folder5, int folder6);
    }
}
