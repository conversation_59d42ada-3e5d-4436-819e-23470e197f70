using Redarbor.Product.API.Consumer;
using Redarbor.Product.API.Consumer.Models.CompanyProducts;

namespace Redarbor.AE.Product.WebAPI.IntegrationConsoleTests.ProductHubController
{
    public class CompanyProductDiscontinueTests : ExtensionTest
    {
        static List<Func<bool>> _tests = new List<Func<bool>>()
        {
            CompanyProductDiscontinueRepoProductTest,
            CompanyProductDiscontinueMasterTest,
            CompanyProductDiscontinueNotFoundTest
        };

        public static bool Execute()
        {
            return ExecuteTests(_tests, "CompanyProductDiscontinue");
        }

        private static bool CompanyProductDiscontinueRepoProductTest()
        {
            var result = false;
            Console.WriteLine("-> Test 1: Updateamos los campos DateMod y DateExpiration en el repo_products (CompanyProductDiscontinueRepoProductTest)");
            var objectRepoProduct = new
            {
                idOrigin = 2,
                objRequest = new CompanyProductDiscontinueRequestModel() { PortalId = 1, CompanyProductId = ********* }
            };

            Console.WriteLine("Parametros de entrada");
            ShowProperties(objectRepoProduct.objRequest);

            string url = ProductHubURLs.CompanyProductDiscontinue(ConfigurationValues.BaseUrl, objectRepoProduct.idOrigin);

            var response = ApiHelper.Post<CompanyProductDiscontinueResponseModel>(url, objectRepoProduct.objRequest);

            if (response != null && response.Errors.Any())
            {
                Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine($"Se han producido estos errores {response.Errors}");
                Console.ForegroundColor = ConsoleColor.White;
            }
            else if (response != null && !response.Errors.Any())
            {
                Console.ForegroundColor = ConsoleColor.Green;
                Console.WriteLine($"Se han modificado las fechas con el companyproductId {objectRepoProduct.objRequest.CompanyProductId} de esta cantidad de productos {response.RowsAffected}");                 
                Console.ForegroundColor = ConsoleColor.White;
                result = true;
            }

            return result;
        }

        private static bool CompanyProductDiscontinueMasterTest()
        {
            var result = false;
            Console.WriteLine("-> Test 2: Updateamos los campos DateMod y DateExpiration en la master (CompanyProductDiscontinueMasterTest)");
            var objectMaster = new
            {
                idOrigin = 2,
                objRequest = new CompanyProductDiscontinueRequestModel() { PortalId = 1, CompanyProductId = 1623745 }
            };
           
            Console.WriteLine("Parametros de entrada");
            ShowProperties(objectMaster.objRequest);

            string url = ProductHubURLs.CompanyProductDiscontinue(ConfigurationValues.BaseUrl, objectMaster.idOrigin);

            var response = ApiHelper.Post<CompanyProductDiscontinueResponseModel>(url, objectMaster.objRequest);

            if (response != null && response.Errors.Any())
            {
                Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine($"Se han producido estos errores {response.Errors}");
                Console.ForegroundColor = ConsoleColor.White;
            }
            else if (response != null && !response.Errors.Any())
            {
                Console.ForegroundColor = ConsoleColor.Green;
                Console.WriteLine($"Se han modificado las fechas con el companyproductId {objectMaster.objRequest.CompanyProductId} de esta cantidad de productos {response.RowsAffected}");
                Console.ForegroundColor = ConsoleColor.White;
                result = true;
            }
            return result;
        }

        private static bool CompanyProductDiscontinueNotFoundTest()
        {
            var result = false;
            Console.WriteLine("-> Test 3: Producto no existente (CompanyProductDiscontinueNotFoundTest)");
            var objectNotFound = new
            {
                idOrigin = 2,
                objRequest = new CompanyProductDiscontinueRequestModel() { PortalId = 1, CompanyProductId = 84544 }
            };
      
            Console.WriteLine("Parametros de entrada");
            ShowProperties(objectNotFound);

            string url = ProductHubURLs.CompanyProductDiscontinue(ConfigurationValues.BaseUrl, objectNotFound.idOrigin);

            var response = ApiHelper.Post<CompanyProductDiscontinueResponseModel>(url, objectNotFound.objRequest);

            if (response != null && response.Errors.Any())
            {
                Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine($"Se han producido estos errores {response.Errors}");
                Console.ForegroundColor = ConsoleColor.White;
            }
            else if (response != null && !response.Errors.Any())
            {
                Console.ForegroundColor = ConsoleColor.Green;
                Console.WriteLine($"No se ha encontrado este companyproductId {objectNotFound.objRequest.CompanyProductId}");
                Console.ForegroundColor = ConsoleColor.White;
                result = true;
            }
            return result;
        }
    }  
}
