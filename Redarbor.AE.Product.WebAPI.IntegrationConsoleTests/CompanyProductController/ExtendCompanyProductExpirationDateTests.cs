using Redarbor.AE.Product.WebAPI.IntegrationConsoleTests;
using Redarbor.Product.API.Consumer;
using Redarbor.Product.API.Consumer.Enums;
using Redarbor.Product.API.Consumer.Models.CompanyProducts;

namespace Redarbor.AE.Product.WebAPI.IntegrationConsole.CompanyProductController
{
    public class ExtendCompanyProductExpirationDateTests : ExtensionTest
    {
        static List<Func<bool>> _tests = new List<Func<bool>>()
        {
            ExtendCompanyProductExpirationDate
        };

        public static bool Execute()
        {
            return ExecuteTests(_tests, "ExtendCompanyProductExpirationDateTests");
        }

        private static bool ExtendCompanyProductExpirationDate()
        {
            var result = false;

            var objectToRequest = new { idOrigin = 1, obj = new ExtendCompanyProductExpirationDateModel { idCompany = 588826, portalId = 1, companyProductId = 1728892, additionalExpirationDays = 10 } };
            
            Console.WriteLine($"-> Test 1: Obtenemos la nueva Fecha de Expiración (ExtendCompanyProductExpirationDate)");
            ShowProperties(objectToRequest.obj);
            string url = URLs.Product.ExtendCompanyProductExpirationDateAsync(ConfigurationValues.BaseUrl, objectToRequest.obj, objectToRequest.idOrigin);

            var response = ApiHelper.Post<ExtendCompanyProductExpirationDateResponseModel>(url, objectToRequest.obj);

            if(response != null)
            {
                if (response.IsSuccessful)
                {
                    Console.ForegroundColor = ConsoleColor.Green;
                    Console.WriteLine($"Se ha extendido correctamente la fecha del producto a {response.NewExpirationDate}");
                    Console.ForegroundColor = ConsoleColor.White;
                    result = true;
                }
                if (!response.IsSuccessful)
                {
                    Console.ForegroundColor = ConsoleColor.Red;
                    Console.WriteLine($"Se ha producido el siguiente error {response.ExtendCompanyProductExpirationDateResultEnum.ToString()}");
                    Console.ForegroundColor = ConsoleColor.White;
                }
            }
            

            return result;
        }
    }
}
