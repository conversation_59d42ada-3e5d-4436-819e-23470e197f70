using System;

namespace Redarbor.Extensions.Library.DI
{
    [AttributeUsage(AttributeTargets.Method)]
    public class RegisterActionOnActivatedAttribute : System.Attribute
    {
        /// <summary>
        /// In case of decorate several methods, you can specify a priority for execution.
        /// Lower value means higher priority.
        /// </summary>
        public int ExecutionPriority { get; set; }
    }
}
