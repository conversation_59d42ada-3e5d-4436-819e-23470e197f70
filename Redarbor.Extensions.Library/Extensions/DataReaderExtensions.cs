using System;
using System.Data;

namespace Redarbor.Extensions.Library.Extensions
{
    public static class DataReaderExtensions
    {

        public static string GetAsString(this IDataReader dtr, string fieldName)
        {
            return IsNotNull(dtr, fieldName)
                ? dtr[fieldName].ToString()
                : string.Empty;
        }

        public static string GetAsString(this IDataReader dtr, int fieldOrder)
        {
            return IsNotNull(dtr, fieldOrder)
                ? dtr[fieldOrder].ToString()
                : string.Empty;
        }

        public static DateTime GetAsDateTime(this IDataReader dtr, string fieldName)
        {
            try
            {
                return IsNotNull(dtr, fieldName)
                    ? Convert.ToDateTime(dtr[fieldName])
                    : DateTime.MinValue;
            }
            catch (Exception)
            {
                return DateTime.MinValue;
            }
        }

        public static DateTime GetAsDateTime(this IDataReader dtr, int fieldOrder)
        {
            try
            {
                return IsNotNull(dtr, fieldOrder)
                    ? Convert.ToDateTime(dtr[fieldOrder])
                    : DateTime.MinValue;
            }
            catch (Exception)
            {
                return DateTime.MinValue;
            }
        }

        public static long GetAsLong(this IDataReader dtr, string fieldName)
        {
            return IsNotNull(dtr, fieldName)
                ? Convert.ToInt64(dtr[fieldName])
                : (long)0;
        }

        public static long GetAsLong(this IDataReader dtr, int fieldOrder)
        {
            return IsNotNull(dtr, fieldOrder)
                ? Convert.ToInt64(dtr[fieldOrder])
                : (long)0;
        }

        public static ulong GetAsULong(this IDataReader dtr, string fieldName)
        {
            return IsNotNull(dtr, fieldName)
                ? Convert.ToUInt64(dtr[fieldName])
                : (ulong)0;
        }

        public static ulong GetAsULong(this IDataReader dtr, int fieldOrder)
        {
            return IsNotNull(dtr, fieldOrder)
                ? Convert.ToUInt64(dtr[fieldOrder])
                : (ulong)0;
        }

        public static int GetAsInt(this IDataReader dtr, string fieldName)
        {
            return IsNotNull(dtr, fieldName)
                ? Convert.ToInt32(dtr[fieldName])
                : 0;
        }

        public static int GetAsInt(this IDataReader dtr, int fieldOrder)
        {
            return IsNotNull(dtr, fieldOrder)
                ? Convert.ToInt32(dtr[fieldOrder])
                : 0;
        }

        public static uint GetAsUInt(this IDataReader dtr, string fieldName)
        {
            return IsNotNull(dtr, fieldName)
                ? Convert.ToUInt32(dtr[fieldName])
                : (uint)0;
        }

        public static uint GetAsUInt(this IDataReader dtr, int fieldOrder)
        {
            return IsNotNull(dtr, fieldOrder)
                ? Convert.ToUInt32(dtr[fieldOrder])
                : (uint)0;
        }

        public static decimal GetAsDecimal(this IDataReader dtr, string fieldName)
        {
            return IsNotNull(dtr, fieldName)
                ? Convert.ToDecimal(dtr[fieldName])
                : (decimal)0;
        }

        public static decimal GetAsDecimal(this IDataReader dtr, int fieldOrder)
        {
            return IsNotNull(dtr, fieldOrder)
                ? Convert.ToDecimal(dtr[fieldOrder])
                : (decimal)0;
        }

        public static float GetAsFloat(this IDataReader dtr, string fieldName)
        {
            return IsNotNull(dtr, fieldName)
                ? Convert.ToSingle(dtr[fieldName])
                : (float)0;
        }

        public static float GetAsFloat(this IDataReader dtr, int fieldOrder)
        {
            return IsNotNull(dtr, fieldOrder)
                ? Convert.ToSingle(dtr[fieldOrder])
                : (float)0;
        }

        public static double GetAsDouble(this IDataReader dtr, string fieldName)
        {
            return IsNotNull(dtr, fieldName)
                ? Convert.ToDouble(dtr[fieldName])
                : (double)0;
        }

        public static double GetAsDouble(this IDataReader dtr, int fieldOrder)
        {
            return IsNotNull(dtr, fieldOrder)
                ? Convert.ToDouble(dtr[fieldOrder])
                : (double)0;
        }

        public static short GetAsShort(this IDataReader dtr, string fieldName)
        {
            return IsNotNull(dtr, fieldName)
                ? Convert.ToInt16(dtr[fieldName])
                : (short)0;
        }

        public static short GetAsShort(this IDataReader dtr, int fieldOrder)
        {
            return IsNotNull(dtr, fieldOrder)
                ? Convert.ToInt16(dtr[fieldOrder])
                : (short)0;
        }

        public static ushort GetAsUShort(this IDataReader dtr, string fieldName)
        {
            return IsNotNull(dtr, fieldName)
                ? Convert.ToUInt16(dtr[fieldName])
                : (ushort)0;
        }

        public static ushort GetAsUShort(this IDataReader dtr, int fieldOrder)
        {
            return IsNotNull(dtr, fieldOrder)
                ? Convert.ToUInt16(dtr[fieldOrder])
                : (ushort)0;
        }


        public static bool GetAsBoolean(this IDataReader dtr, string fieldName)
        {
            return IsNotNull(dtr, fieldName)
                ? Convert.ToBoolean(dtr[fieldName])
                : false;
        }

        public static bool GetAsBoolean(this IDataReader dtr, int fieldOrder)
        {
            return IsNotNull(dtr, fieldOrder)
                ? Convert.ToBoolean(dtr[fieldOrder])
                : false;
        }

        private static bool IsNotNull(IDataReader dtr, string fieldName)
        {
            try
            {
                return !string.IsNullOrWhiteSpace(fieldName)
                && !dtr.IsDBNull(dtr.GetOrdinal(fieldName))
                && dtr[fieldName] != null;
            }
            catch
            {
                return false;
            }
            
        }

        private static bool IsNotNull(IDataReader dtr, int fieldOrder)
        {
            try
            {
                return !dtr.IsDBNull(fieldOrder)
                && dtr[fieldOrder] != null;
            }
            catch
            {
                return false;
            }

        }



        public static int GetAsInt(this IDataRecord dtr, string fieldName)
        {
            return IsNotNull(dtr, fieldName)
                ? Convert.ToInt32(dtr[fieldName])
                : 0;
        }
        public static int GetAsInt(this IDataRecord dtr, int fieldOrder)
        {
            return IsNotNull(dtr, fieldOrder)
                ? Convert.ToInt32(dtr[fieldOrder])
                : 0;
        }

        public static short GetAsShort(this IDataRecord dtr, string fieldName)
        {
            return IsNotNull(dtr, fieldName)
                ? Convert.ToInt16(dtr[fieldName])
                : (short)0;
        }
        public static short GetAsShort(this IDataRecord dtr, int fieldOrder)
        {
            return IsNotNull(dtr, fieldOrder)
                ? Convert.ToInt16(dtr[fieldOrder])
                : (short)0;
        }

        public static DateTime GetAsDateTime(this IDataRecord dtr, int fieldOrder)
        {
            try
            {
                return IsNotNull(dtr, fieldOrder)
                    ? Convert.ToDateTime(dtr[fieldOrder])
                    : DateTime.MinValue;
            }
            catch (Exception)
            {
                return DateTime.MinValue;
            }
        }
        public static DateTime GetAsDateTime(this IDataRecord dtr, string fieldName)
        {
            try
            {
                return IsNotNull(dtr, fieldName)
                    ? Convert.ToDateTime(dtr[fieldName])
                    : DateTime.MinValue;
            }
            catch (Exception)
            {
                return DateTime.MinValue;
            }
        }


        private static bool IsNotNull(IDataRecord dtr, string fieldName)
        {
            try
            {
                return !string.IsNullOrWhiteSpace(fieldName)
                && !dtr.IsDBNull(dtr.GetOrdinal(fieldName))
                && dtr[fieldName] != null;
            }
            catch
            {
                return false;
            }

        }

        private static bool IsNotNull(IDataRecord dtr, int fieldOrder)
        {
            try
            {
                return !dtr.IsDBNull(fieldOrder)
                && dtr[fieldOrder] != null;
            }
            catch
            {
                return false;
            }

        }
    }
}
