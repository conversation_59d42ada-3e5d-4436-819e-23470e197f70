using System.Collections.Generic;
using System.Linq;

namespace Redarbor.Extensions.Library
{
    public enum LogLevelType
    {
        DEBUG = 0,
        INFO = 1,
        WARN = 2,
        ERROR = 3,
        FATAL = 4
    }

    public class LogAppenderLevelMatchFilters
    {
        private string _appenderName;
        private List<LogLevelType> _levelMatchFilters;

        public LogAppenderLevelMatchFilters(string appenderName, IEnumerable<LogLevelType> levelMatchFilters)
        {
            _appenderName = appenderName;
            _levelMatchFilters = levelMatchFilters.ToList();
        }

        public string AppenderName
        {
            get { return _appenderName; }
        }

        public IEnumerable<LogLevelType> LevelMatchFilters
        {
            get
            {
                if (_levelMatchFilters == null)
                    _levelMatchFilters = new List<LogLevelType>();

                return _levelMatchFilters;
            }
        }
    }
}
