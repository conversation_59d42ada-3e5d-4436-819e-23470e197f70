<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{79E50E19-D3C9-4D95-890C-EC9E9B1A5B1C}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Redarbor.User.Contracts.ServiceLibrary</RootNamespace>
    <AssemblyName>Redarbor.User.Contracts.ServiceLibrary</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x64\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\x64\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="DTO\MarkMailDTO.cs" />
    <Compile Include="DTO\RegisterCompanyLoginDTO.cs" />
    <Compile Include="Enums\VerifiedMailEnum.cs" />
    <Compile Include="IUserApiService.cs" />
    <Compile Include="ITimeUseByUserService.cs" />
    <Compile Include="IUserRoleService.cs" />
    <Compile Include="IUserService.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Readarbor.Master.Entities\Redarbor.Master.Entities.csproj">
      <Project>{72b93ba2-c177-4ddf-9f27-e08151323719}</Project>
      <Name>Redarbor.Master.Entities</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Common.Entities\Redarbor.Common.Entities.csproj">
      <Project>{3da7e832-19ae-4c35-85f8-5faf5a4dabeb}</Project>
      <Name>Redarbor.Common.Entities</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Company.Library\Redarbor.Company.Library.csproj">
      <Project>{8738fc41-64c2-4664-923c-9486f8d4b607}</Project>
      <Name>Redarbor.Company.Library</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Repo.Trackings.Library\Redarbor.Repo.Trackings.Library.csproj">
      <Project>{59200A52-1BFB-47B0-A163-0588FDDACE21}</Project>
      <Name>Redarbor.Repo.Trackings.Library</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Users.Library\Redarbor.Users.Library.csproj">
      <Project>{777498B3-DAFA-49F1-8058-ED3B11C15E10}</Project>
      <Name>Redarbor.Users.Library</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>