using Redarbor.AE.Procs.BrokenOperationsNotifier.Application.Abstractions;
using Redarbor.AE.Procs.BrokenOperationsNotifier.Application.Models;
using Redarbor.Payments.Consumer;
using Redarbor.Payments.Consumer.Enums;
using Redarbor.Payments.Consumer.Models;
using Redarbor.Procs.Domain.Entities;
using Redarbor.Procs.Domain.Enums;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Redarbor.AE.Procs.BrokenOperationsNotifier.Infrastructure.Services
{
    public class PaymentsService : IPaymentsService
    {
        private readonly IPaymentOperationRepository _paymentsRepository;

        private readonly IPaymentConsumerService _paymentConsumerService;

        public PaymentsService(IPaymentOperationRepository paymentsRepository, IPaymentConsumerService paymentConsumerService)
        {
            _paymentsRepository = paymentsRepository;
            _paymentConsumerService = paymentConsumerService;
        }

        public Task<ICollection<CompanyDto>> GetCompanyIdsWithRecentOperationsAsync(IDbConnection connection, CancellationToken stoppingToken)
            => _paymentsRepository.GetCompanyIdsWithRecentOperationsAsync(connection, stoppingToken);

        public async Task<ICollection<Operation>> GetCompanyOperationsAsync(IDbConnection connection, int companyId, PortalEnum portal, CancellationToken stoppingToken)
        {
            var operations = await _paymentsRepository.GetCompanyOperationsAsync(connection, companyId, portal, stoppingToken);
            UpdateOperationsProvider(operations);
            return operations;
        }

        private void UpdateOperationsProvider(ICollection<Operation> operations)
        {
            var paymentsOperations = _paymentConsumerService.GetOperations(new GetOperationsModel
            {
                IdOperations = operations.Select(o => (long)o.PaymentsOperationId).ToList(),
                WithData = false,
            }).ToList();


            foreach (var operation in operations)
            {
                foreach (var paymentsOperation in paymentsOperations)
                {
                    if (operation.PaymentsOperationId == paymentsOperation.Id)
                    {
                        operation.IdOrigin = GetOrigin(operation, paymentsOperation);
                    }
                }
            }
        }

        private PaymentOriginEnum GetOrigin(Operation o, OperationModel po)
        {
            return o.IdOrigin == PaymentOriginEnum.PaymentsTPV
                                ? MapProvider((PaymentProvider)po.PaymentProvider)
                                : o.IdOrigin;
        }

        private PaymentOriginEnum MapProvider(PaymentProvider paymentProvider)
        {
            return paymentProvider switch
            {
                PaymentProvider.WorldPay => PaymentOriginEnum.WorldPay,
                PaymentProvider.PayU => PaymentOriginEnum.PayU,
                PaymentProvider.PayPal => PaymentOriginEnum.PayPal,
                _ => PaymentOriginEnum.PaymentsTPV,
            };
        }

        public Task<ICollection<OperationItemDto>> GetOperationItemsAsync(IDbConnection connection, int idOperacionCompra)
            => _paymentsRepository.GetOperationItemsAsync(connection, idOperacionCompra);

        public Task<UserDto?> GetOperationUserAsync(IDbConnection connection, Operation lastUnsuccessfulOperation)
            => _paymentsRepository.GetOperationUserAsync(connection, lastUnsuccessfulOperation);

        public Task MarkBrokenPaymentAsNotifiedAsync(IDbConnection connection, Operation lastUnsuccessfulOperation, DateTimeOffset date)
            => _paymentsRepository.MarkBrokenPaymentAsNotifiedAsync(connection, lastUnsuccessfulOperation, date);
    }
}
