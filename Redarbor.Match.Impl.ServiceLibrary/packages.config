<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="AutoMapper" version="6.2.2" targetFramework="net452" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="5.0.0" targetFramework="net472" />
  <package id="Newtonsoft.Json" version="7.0.1" targetFramework="net452" />
  <package id="Redarbor.Core.Elastic.Library" version="2.0.1" targetFramework="net472" />
  <package id="Redarbor.Matches.Elastic.Consumer" version="2.1.42" targetFramework="net472" />
  <package id="Redarbor.RabbitMQ.Abstractions" version="2.0.5.2" targetFramework="net472" />
  <package id="Redarbor.RabbitMQ.Model" version="2.0.5.2" targetFramework="net472" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net472" />
  <package id="System.Memory" version="4.5.4" targetFramework="net472" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net472" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="5.0.0" targetFramework="net472" />
  <package id="System.Text.Encodings.Web" version="5.0.1" targetFramework="net472" />
  <package id="System.Text.Json" version="5.0.2" targetFramework="net472" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net472" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net472" />
</packages>