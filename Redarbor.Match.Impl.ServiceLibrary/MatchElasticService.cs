using Redarbor.Core.Cache.Contracts.ServiceLibrary;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Extensions.Library.DI;
using Redarbor.Match.Contracts.ServiceLibrary;
using Redarbor.Match.Contracts.ServiceLibrary.DTO;
using Redarbor.Match.Contracts.ServiceLibrary.Entities;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Redarbor.Match.Impl.ServiceLibrary
{
    [RegisterStatefulService]
    public class MatchElasticService : IMatchElasticService
    {
        private readonly IExceptionPublisherService _exceptionPublisherService;
        private readonly IAdaptorMatchElasticConsumerService _adaptorMatchElasticConsumerService;
        private readonly ITempCache _tempCache;

        private const short MINUTES_CACHE_LAST_MATCHES = 5;

        public MatchElasticService(IExceptionPublisherService exceptionPublisherService,
            IAdaptorMatchElasticConsumerService adaptorMatchElasticConsumerService
,
            ITempCache tempCache)
        {
            _exceptionPublisherService = exceptionPublisherService;
            _adaptorMatchElasticConsumerService = adaptorMatchElasticConsumerService;
            _tempCache = tempCache;
        }

        public bool Delete(KeysMatchElasticDTO keysMatch)
        {
            try
            {
                return _adaptorMatchElasticConsumerService.Delete(new List<KeysMatchElasticDTO>() { keysMatch });
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "MatchElasticService", "Delete - NewElastic");
                return false;
            }
        }

        public MatchElasticEntity Get(KeysMatchElasticDTO keysmatchElasticDTO)
        {
            var matchElastic = new MatchElasticEntity();

            try
            {
                matchElastic = _adaptorMatchElasticConsumerService.Get(keysmatchElasticDTO);
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "MatchElasticService", "Get");
            }

            return matchElastic;
        }

        public List<MatchElasticEntity> GetByIds(KeysMatchListElasticDTO keysmatchListElasticDTO)
        {
            var matchElastic = new List<MatchElasticEntity>();

            try
            {
                matchElastic = _adaptorMatchElasticConsumerService.GetByIds(keysmatchListElasticDTO);
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "MatchElasticService", "GetByIds");
            }

            return matchElastic;
        }

        public bool IndexByIds(List<long> matches, short portalId)
        {
            try
            {
                return _adaptorMatchElasticConsumerService.IndexByIds(matches, portalId);
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "MatchElasticService", "IndexByIds - NewElastic");
                return false;
            }
        }

        public bool UpdatePartialMatch(MatchElasticEntity match)
        {
            try
            {
                return _adaptorMatchElasticConsumerService.Update(new List<MatchElasticEntity>() { match }, match.portalId);
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "MatchElasticService", "UpdatePartialMatch - NewElastic (Update)");
                return false;
            }
        }

        public SearchResultDTO<MatchElasticEntity> SearchElasticMatch(SearchFilterDTO filter, bool withFacets = true, bool counter = false)
        {                        
            try
            {
                return _adaptorMatchElasticConsumerService.Search(filter);
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "MatchElasticService", "SearchElasticMatch");
                return new SearchResultDTO<MatchElasticEntity>();
            }
        }

        public SearchResultDTO<MatchElasticEntity> SearchElasticFacetMatch(SearchFilterDTO searchFilter)
        {            
            try
            {
               return _adaptorMatchElasticConsumerService.GetOnlyFacets(searchFilter);                
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "MatchElasticService", "SearchElasticFacetMatch");
                return new SearchResultDTO<MatchElasticEntity>();
            }
        }

        public List<MatchElasticEntity> GetLastByOfferIds(GetLastByOfferIdsDTO filter)
        {
            try
            {
                var cacheKey = $"LAST_MATCHES_{filter.PortalId}_{filter.CompanyId}_{filter.OfferIds}";
                var cacheResult = _tempCache.Get<List<MatchElasticEntity>>(cacheKey, filter.PortalId);

                if (cacheResult != null)
                    return cacheResult;

                var result = _adaptorMatchElasticConsumerService.GetLastByOfferIds(filter);
                
                _tempCache.Add(cacheKey, result, TimeSpan.FromMinutes(MINUTES_CACHE_LAST_MATCHES), filter.PortalId);

                return result;
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "MatchElasticService", "GetLastByOfferIds");
                return new List<MatchElasticEntity>();
            }




        }
    }
}
