using Newtonsoft.Json;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary.Constants;
using Redarbor.Extensions.Library.DI;
using Redarbor.Tools.Exceptions.Consumer;
using Redarbor.Tools.Exceptions.Consumer.Core;
using Redarbor.Tools.Exceptions.Consumer.Enums;
using Redarbor.Tools.Exceptions.Consumer.Mappings;
using Redarbor.Tools.Exceptions.Consumer.Models;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Diagnostics;
using System.Reflection;

namespace Redarbor.Core.Computrabajo.Impl.ServiceLibrary
{
    [RegisterStatefulService]
    public class ExceptionPublisherNoPortalDependencyService : IExceptionPublisherNoPortalDependencyService
    {
        private readonly IExceptionsConsumerService _exceptionConsumerService;
        private readonly string _buildVersion;

        public ExceptionPublisherNoPortalDependencyService(
           IExceptionsConsumerService exceptionConsumerService)
        {
            _exceptionConsumerService = exceptionConsumerService;
            _buildVersion = ConfigurationManager.AppSettings["BUILD_VERSION"] ?? string.Empty;
        }

        public void PublishInfo(Exception exception, string className, string methodName, Dictionary<string, string> extradata = null)
        {
            try
            {
                PublishException(exception, className, methodName, AddInformationInExtraData(className, extradata, methodName), ExceptionLevel.Info);
            }
            catch (Exception ex)
            {
                Trace.TraceError($"ExceptionPublisherNoPortalDependencyService - PublishInfo: ex:{ex.ToString()}");
            }
        }

        public void PublishWarning(Exception exception, string className, string methodName, Dictionary<string, string> extradata = null)
        {
            try
            {
                PublishException(exception, className, methodName, AddInformationInExtraData(className, extradata, methodName), ExceptionLevel.Warning);
            }
            catch (Exception ex)
            {
                Trace.TraceError($"ExceptionPublisherNoPortalDependencyService - PublishWarning: ex:{ex.ToString()}");
            }
        }

        public void PublishError(Exception exception, string className, string methodName, Dictionary<string, string> extradata = null)
        {
            try
            {
                PublishException(exception, className, methodName, AddInformationInExtraData(className, extradata, methodName), ExceptionLevel.Error);
            }
            catch (Exception ex)
            {
                Trace.TraceError($"ExceptionPublisherNoPortalDependencyService - PublishError: ex:{ex.ToString()}");
            }
        }

        private void PublishException(Exception exception, string className, string methodName,
          Dictionary<string, string> extraData = null, ExceptionLevel level = ExceptionLevel.Error)
        {
            if (ExceptionsConfigurator._configurationExceptionModel.SaveInElastic)
                PublishExceptionInElastic(exception, className, methodName, extraData, level);
            else
                Logger.Log(level, $@"{level}: 
                                      ClassName: {className}
                                      MethodName: {methodName}
                                      ExtraData: {JsonConvert.SerializeObject(extraData)}
                                      ex:{exception.ToString()}");
        }

        private bool PublishExceptionInElastic(Exception exception, string className, string methodName,
         Dictionary<string, string> extraData = null, ExceptionLevel level = ExceptionLevel.Error)
        {
            AddStacksToExtraData(extraData);
            var exceptionModel = this.MapExceptionModel(exception, System.Reflection.MethodBase.GetCurrentMethod(), className, methodName, extraData, level);

            var result = _exceptionConsumerService.AddException(exceptionModel);

            if (string.IsNullOrEmpty(result)) throw new Exception($"No insertado en la API url: {ConfigurationContext.ApiExceptionsEndpoint}. resultado en api elastic vacio id: {result}");

            return !string.IsNullOrEmpty(result);
        }

        private void AddStacksToExtraData(Dictionary<string, string> extraData)
        {
            if (extraData == null)
                extraData = new Dictionary<string, string>();

            StackTrace stackTrace = new StackTrace();
            int i = 0;

            foreach (var sf in stackTrace.GetFrames())
            {
                if (!extraData.ContainsKey($"Stack {i}"))
                    extraData.Add($"Stack {i}", $"Method: {sf.GetMethod().Name}, Class: {sf.GetMethod().DeclaringType.Name}");

                i++;
            }
        }

        private ExceptionModel MapExceptionModel(Exception ex, MethodBase method, string className, string methodName,
            Dictionary<string, string> extraData = null, ExceptionLevel level = ExceptionLevel.Error)
        {
            var exceptionModel = ExceptionModelMappings.Map(ex, method, new AddExceptionSpecifications(), extraData, level);

            if (!String.IsNullOrEmpty(className)) exceptionModel.Class = className;
            if (!String.IsNullOrEmpty(methodName)) exceptionModel.Method = methodName;

            return exceptionModel;
        }

        private Dictionary<string, string> AddInformationInExtraData(string className, Dictionary<string, string> dic, string method = null)
        {
            var extradata = dic ?? new Dictionary<string, string>();

            if (!extradata.ContainsKey(ExtraDataConstants.classNameTryException)) extradata.Add(ExtraDataConstants.classNameTryException, className);

            if (!string.IsNullOrEmpty(method))
            {
                if (!extradata.ContainsKey(ExtraDataConstants.methodNameTryException)) extradata.Add(ExtraDataConstants.methodNameTryException, method);
            }

            if (!extradata.ContainsKey(ExtraDataConstants.buildVersion)) extradata.Add(ExtraDataConstants.buildVersion, _buildVersion);

            return extradata;
        }
    }
}
