using Redarbor.Master.Entities.Product;
using Redarbor.Master.Entities.Searchs;
using Redarbor.Products.Library.DomainServiceContracts;
using Redarbor.ProProduct.Contracts.ServiceLibrary;

namespace Redarbor.ProProduct.Impl.ServiceLibrary
{
    public class ProductsService : IProductsService
    {
        private readonly IProductRecoverAndPersist _productRecoverAndPersist;

        public ProductsService(IProductRecoverAndPersist productRecoverAndPersist)
        {
            _productRecoverAndPersist = productRecoverAndPersist;
        }

        public ProductEntity Get(ProductSearchSpecifications productSpecification)
        {
            var product = new ProductEntity();

            if (productSpecification.Id != 0)
            {
                product = _productRecoverAndPersist.Select(productSpecification);
            }

            return product;
        }
    }
}
