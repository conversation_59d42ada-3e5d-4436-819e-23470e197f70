using Redarbor.Core.Cache.Contracts.ServiceLibrary;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Core.Kpi.Contracts.ServiceLibrary;
using Redarbor.Core.Kpi.Contracts.ServiceLibrary.Enums;
using Redarbor.Core.Resolver.Contracts.ServiceLibrary;
using Redarbor.Core.Shared.Contracts.ServiceLibrary;
using Redarbor.Core.Stack.Contracts.ServiceLibrary;
using Redarbor.Core.TrackingActions.Contracts.ServiceLibrary;
using Redarbor.Extensions.Library.Extensions;
using Redarbor.Master.Entities.Company;
using Redarbor.Master.Entities.Enums;
using Redarbor.Master.Entities.Reports;
using Redarbor.Master.Entities.Stack;
using Redarbor.Report.Contracts.ServiceLibrary;
using Redarbor.Report.Contracts.ServiceLibrary.DTO;
using Redarbor.Report.Library.DomainServiceContracts;
using System;
using System.Text;
using System.Threading.Tasks;

namespace Redarbor.Report.Impl.ServiceLibrary
{    
    public class ReportConfigurationService : IReportConfigurationService
    {
        const string KEY_CACHE_MAX_CREATED_REPORT = "SUPER_LOCAL_CACHE_SAFETY_COMPANY_REPORTS_MAX_CREATED_";
        const string KEY_CACHE_COUNTER_REPORTS_DAY = "SUPER_LOCAL_CACHE_SAFETY_COMPANY_REPORTS_COUNT_DAY_COMPANYID_";

        private readonly ITempCache _tempCache;
        private readonly IReportConfigurationRecoverAndPersist _reportConfigurationRecoverAndPersist;
        private readonly IStackService _stackService;
        private readonly IKpiService _kpiService;
        private readonly ITrackingActionsService _trackingActionsService;
        private readonly IEncryptionService _encryptionService;
        private readonly IAmazonFilesService _amazonFilesService;
        private readonly IPortalResolverService _portalResolverService;

        public ReportConfigurationService(ITempCache tempCache,
            IReportConfigurationRecoverAndPersist reportConfigurationRecoverAndPersist,
            IStackService stackService,
            IKpiService kpiService,
            ITrackingActionsService trackingActionsService,
            IEncryptionService encryptionService,
            IAmazonFilesService amazonFilesService,
            IPortalResolverService portalResolverService)
        {
            _tempCache = tempCache;
            _reportConfigurationRecoverAndPersist = reportConfigurationRecoverAndPersist;
            _stackService = stackService;
            _kpiService = kpiService;
            _trackingActionsService = trackingActionsService;
            _encryptionService = encryptionService;
            _amazonFilesService = amazonFilesService;
            _portalResolverService = portalResolverService;
        }

        public int CountByDay(int companyId, short portalId)
        {
            if (companyId > 0 && portalId > 0)
            {
                var maxDate = _reportConfigurationRecoverAndPersist.GetMaxDateCreteReportConfig(companyId, portalId);

                StringBuilder l_str_max_date_to_ddbb = new StringBuilder();
                l_str_max_date_to_ddbb.Append(KEY_CACHE_COUNTER_REPORTS_DAY);
                l_str_max_date_to_ddbb.Append(companyId.ToString());
                l_str_max_date_to_ddbb.Append("_PORTALID_");
                l_str_max_date_to_ddbb.Append(portalId.ToString());
                l_str_max_date_to_ddbb.Append("_DATE_");
                l_str_max_date_to_ddbb.Append(DateTime.Now.Date.ToString("d"));

                StringBuilder l_str_max_date_to_cache = new StringBuilder();
                l_str_max_date_to_cache.Append(KEY_CACHE_MAX_CREATED_REPORT);
                l_str_max_date_to_cache.Append(companyId.ToString());
                l_str_max_date_to_cache.Append("_PORTALID_");
                l_str_max_date_to_cache.Append(portalId.ToString());

                var getMaxDateCreteReportConfigCache = GetMaxDateCreteReportConfigCache(companyId, portalId, l_str_max_date_to_cache.ToString());

                if (maxDate == DateTime.MinValue || maxDate != getMaxDateCreteReportConfigCache)
                {
                    var count = _reportConfigurationRecoverAndPersist.CountByDay(companyId, portalId);

                    if (maxDate != DateTime.MinValue && count > 0)
                        _tempCache.Add(l_str_max_date_to_cache.ToString(), maxDate, DateTime.Now.AddDays(1));

                    if (count > 0)
                        _tempCache.Add(l_str_max_date_to_ddbb.ToString(), count, DateTime.Now.AddDays(1));

                    return count;
                }
                else
                {
                    return _tempCache.Get<int>(l_str_max_date_to_ddbb.ToString());
                }
            }

            return 0;
        }

        public string Create(ReportConfigurationEntity reportConfiguration)
        {            
            var idReport = _reportConfigurationRecoverAndPersist.Insert(reportConfiguration);
        
            if (idReport > 0)
            {
                Task.Factory.StartNew(() => _stackService.SaveReport(new StackEntity(reportConfiguration.PortalId)
                {
                    ObjectId = idReport,
                    PortalId = reportConfiguration.PortalId,
                    TypeId = (int)StackObjectTypeEnum.Report,
                    StatusId = (short)ReportStatusEnum.Expected
                }));

                _kpiService.AddMonthlyOffersByCompany((short)KpiEnum.COMPANY_REPORT, reportConfiguration.PortalId, reportConfiguration.CompanyId);
                _kpiService.AddSumBlock((short)KpiEnum.COMPANY_REPORT, reportConfiguration.PortalId, 1);
                Task.Factory.StartNew(() => _trackingActionsService.AddActionTracking(new TrackingActionEntity()
                {
                    Type = (short)ProductTraceActionsEnum.REPORTCREATED,
                    IdObject = idReport,
                    IdPortal = reportConfiguration.PortalId,
                    IdStatus = (short)TraceStatus.Pending,
                    IdCompany = reportConfiguration.CompanyId,
                    IdUser = reportConfiguration.UserId,
                    UserName = reportConfiguration.Name,
                    DateUpdate = DateTime.MinValue
                }));
            }

            return _encryptionService.Encrypt(idReport.ToString());
        }

        public bool DeleteConfiguration(int configurationId)
        {
            if (configurationId > 0)
                return _reportConfigurationRecoverAndPersist.RemoveConfiguration(configurationId);                
            
            return false;
        }

        public ReportConfigurationEntity GetByIdReport(string IdReportEncrypted, int portalId)
        {
            if (!string.IsNullOrEmpty(IdReportEncrypted))
                return GetByIdReport(_encryptionService.Decrypt(IdReportEncrypted).ToInt(), portalId);

            return new ReportConfigurationEntity();
        }

        public ReportConfigurationEntity GetByIdReport(int idReport, int portalId)
        {
            if (idReport > 0
                && portalId > 0)
                return _reportConfigurationRecoverAndPersist.GetByIdReport(idReport, portalId);

            return new ReportConfigurationEntity();
        }

        public ReportConfigurationEntity GetById(string companyFileIdEncrypted, int portalId)
        {
            if(!string.IsNullOrEmpty(companyFileIdEncrypted))
                return GetById(_encryptionService.Decrypt(companyFileIdEncrypted).ToInt(), portalId);            

            return new ReportConfigurationEntity();
        }

        public ReportConfigurationEntity GetById(int companyFileId, int portalId)
        {
            if(companyFileId > 0 
                && portalId > 0)
                return _reportConfigurationRecoverAndPersist.GetById(companyFileId, portalId);

            return new ReportConfigurationEntity();
        }

        private DateTime GetMaxDateCreteReportConfigCache(int companyId, short portalId, string key)
        {
            return _tempCache.Get<DateTime>(key);
        }

        public ReportFileDTO GetFile(string fileEncryptedId, int companyId)
        {           
            if (!string.IsNullOrEmpty(fileEncryptedId))
            {
                return GetFile(_encryptionService.Decrypt(fileEncryptedId).ToInt(), companyId);
            }

            return new ReportFileDTO();
        }

        public ReportFileDTO GetFile(int fileId, int companyId)
        {
            var portalId = _portalResolverService.ResolvePortalId();

            if(fileId > 0 
                && portalId > 0)
            {
                var report = GetById(fileId, portalId);

                if (report.CompanyId == companyId 
                    && !string.IsNullOrEmpty(report.Path))
                {
                    using (var fileContent = _amazonFilesService.GetFile(report.Path))
                    {
                        return new ReportFileDTO()
                        {
                            FileName = report.NameFileAmazon,
                            FileContent = fileContent?.ToArray()
                        };
                    }
                }
            }

            return new ReportFileDTO();
        }
    }
}
