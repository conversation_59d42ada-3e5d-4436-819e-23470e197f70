using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace Redarbor.AE.Product.WebAPI.Constants
{
    public class ErrorMessageConstants
    {
        public const string COMPANY_CUSTOMER_MUST_BE = "CompanyId or CustomerId must be greater than 0";
        public const string ID_ORIGIN_NOT_FOUND = "IdOrigin not found";
        public const string MODEL_NULL = "Model is null";
        public const string UNEXPECTED_ERROR = "UnexpectedError";
        public const string ACTIVATION_DATE_CANNOT_GREATER_EXPIRATION_DATE = "Activation date cannot be greater than expiration date";
        public const string PRODUCT_NOT_FOUND = "Product not found";
        public const string PRODUCT_WITHOUT_FEATURES = "Product without features";
        public const string COMPANY_REQUIRED_FOR_ADD_MASTER = "CompanyId is required to add a company product to the master";
        public const string PRODUCT_NOT_ADDED = "Product not added";
        public const string PRODUCT_FEATURES_NOT_ADDED = "Product features not added";
        public const string COMPANY_PRODUCT_NOT_FOUND = "CompanyProduct not found";
    }
}