using Microsoft.Extensions.Configuration;
using Redarbor.Dictionaries.Consumer.Configuration;
using Redarbor.Extensions.NetStandard.DI;
using Redarbor.GlobalConfiguration.Library;
using System.Configuration;


namespace Redarbor.AE.Product.WebAPI.Configuration
{
    [SingleInstanceService]
    public class DictionariesApiConfiguration : IDictionariesApiConfiguration
    {
        public int AppId { get; private set; }

        public string BaseUrl { get; private set; }
        public string CacheKeyPrefix => "AE_P_API";

        public bool EncryptDictionaryCacheWithSHA256 => true;

        public DictionariesApiConfiguration(IGlobalConfigurationService globalConfigurationService)
        {
            AppId = int.Parse(ConfigurationManager.AppSettings["APP_ID"]);
            BaseUrl = globalConfigurationService.Current.GetApiEndPoint("Redarbor.Dictionaries.WebAPI");
        }
    }
}

