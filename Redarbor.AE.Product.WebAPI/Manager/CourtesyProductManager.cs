using Redarbor.AE.Product.WebAPI.Enums;
using Redarbor.AE.Product.WebAPI.Mappers;
using Redarbor.AE.Product.WebAPI.Models.CompanyProduct;
using Redarbor.AE.Product.WebAPI.Models.CourtesyProduct;
using Redarbor.AE.Product.WebAPI.Models.ProProduct;
using Redarbor.Extensions.NetStandard.Extensions;
using Redarbor.Master.Entities.Enums;
using Redarbor.Products.Contracts.ServiceLibrary;
using Redarbor.Products.Contracts.ServiceLibrary.DTO;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Threading.Tasks;

namespace Redarbor.AE.Product.WebAPI.Manager
{
    public class CourtesyProductManager : ICourtesyProductManager
    {
        private readonly IProductService _productService;
        private readonly ICompanyProductAddService _companyProductAddService;
        private readonly ICompanyProductService _companyProductService;
        private bool _enableRepoProduct = false;

        public CourtesyProductManager(IProductService productService, ICompanyProductAddService companyProductAddService, ICompanyProductService companyProductService)
        {
            _productService = productService;
            _companyProductAddService = companyProductAddService;
            _companyProductService = companyProductService;
            _enableRepoProduct = ConfigurationManager.AppSettings["ENABLED_REPO_PRODUCTS"]?.ToLower() == "true";
        }

        public async Task<AddCourtesyProductResponseModel> AddCourtesyProductAsync(AddCourtesyProductModel model)
        {
            var product = await _productService.GetProductByIdProductAsync(model.ProductId, model.PortalId);

            if (product.GroupId <= 0 || string.IsNullOrEmpty(product.ComercialName))
                return new AddCourtesyProductResponseModel(new Models.ProductError(ProductErrorEnum.Validations, $"Error ComercialName is null or empty, or groupId = 0. product {model.ProductId} by portal {model.PortalId}"));

            long newCompanyProductId = 0;

            if (_enableRepoProduct && product.GroupId == (short)ProductGroupsEnum.Pandape)
            {
                var nowByPortal = DateTime.UtcNow.ConvertDateTimeFromUTCByPortal(model.PortalId);
                var newCompanyproductIdValue = await _companyProductService.AddCompanyProductByRepoProductsAsync(
                                            new AddCompanyProductRequestDTO(product.ProductId, model.CompanyId, model.CustomerId, model.PortalId, 0, 0, product.ServiceTypeId,
                                            product.GroupId,0, 0, nowByPortal, nowByPortal.AddDays(product.ExpirationDays), product.ExpirationDays, "", product.ProductTypeId, 
                                            product.LimitationMultiplier, product.SubGroupId, product.TemporalityId, true));
                if(newCompanyproductIdValue == null)
                {
                    return new AddCourtesyProductResponseModel(new Models.ProductError(ProductErrorEnum.GenericError, "UnexpectedError"));
                }
                else
                {
                    newCompanyProductId = newCompanyproductIdValue.GetValueOrDefault();
                }

                if (newCompanyProductId <= 0)
                    return new AddCourtesyProductResponseModel(new Models.ProductError(ProductErrorEnum.Validations, $"Error when try to add the courtesy product {model.ProductId} to the company {model.CompanyId} for portal {model.PortalId}"));                

                var companyProductFeaturesIds = await AddCompanyProductsFeaturesAsync(new CompanyProductFeatureFilterModel(model.CompanyId, model.CustomerId, model.PortalId, 0), newCompanyProductId, product);

                if (!companyProductFeaturesIds.Any())
                    return new AddCourtesyProductResponseModel(true, false, false, product.GroupId, (int)newCompanyProductId, product.ComercialName, false);

                await _companyProductService.UpdateDateModActiveProductsRepoProductsAsync(model.CompanyId, model.PortalId);
            }
            else
            {

                newCompanyProductId = await _companyProductAddService.AddCourtesyProductAsync(new AddCourtesyProductDTO(model.CompanyId, model.PortalId, model.ProductId));

                if (newCompanyProductId <= 0)
                    return new AddCourtesyProductResponseModel(new Models.ProductError(ProductErrorEnum.Validations, $"Error when try to add the courtesy product {model.ProductId} to the company {model.CompanyId} for portal {model.PortalId}"));

                var isSuccessfulAddCourtesyProductFeatures = await _companyProductAddService.AddCourtesyProductFeaturesAsync(new AddCourtesyProductFeatureDTO((int)newCompanyProductId, model.ProductId, model.CompanyId, model.PortalId));

                if (!isSuccessfulAddCourtesyProductFeatures)
                    return new AddCourtesyProductResponseModel(true, false, false, product.GroupId, (int)newCompanyProductId, product.ComercialName, false);

                await _companyProductService.UpdateDateModActiveProductsAsync(model.CompanyId, model.PortalId);
            }
            
            return new AddCourtesyProductResponseModel(true, true, product.ProductTypeId == (short)ProductType.Normal && product.GroupId == (short)ProductGroupsEnum.Membership,
                                                        product.GroupId, (int)newCompanyProductId, product.ComercialName, true);
        }

        public async Task<GetCourtesyProductResponseModel> GetCourtesyAsync(short portalId)
        {
            var productsByPortal = await _productService.GetProProductPortalNoObsoleteMainInfoByPortalId(portalId);

            if (productsByPortal == null)
                return new GetCourtesyProductResponseModel(new Models.ProductError(ProductErrorEnum.Validations, $"Error when try to get the proProductPortal by portal {portalId}"));

            if (!productsByPortal.Any())
                return new GetCourtesyProductResponseModel(new Models.ProductError(ProductErrorEnum.Validations, $"There aren't products by portal {portalId}"));

            var productIdSelected = productsByPortal.FirstOrDefault(p => p.ProductId > 0)?.ProductId ?? 0;

            if (productIdSelected <= 0)
                return new GetCourtesyProductResponseModel(new Models.ProductError(ProductErrorEnum.Validations, $"There aren't products with productId > 0 by portal {portalId}"));

            var featuresByProduct = await _productService.GetProProductPortalFeatureMainInfoByProductIdAndPortalIdAsync(productIdSelected, portalId);

            if (featuresByProduct == null)
                return new GetCourtesyProductResponseModel(new Models.ProductError(ProductErrorEnum.Validations, $"Error when try to get the features by product {productIdSelected} and portal {portalId}"));

            return new GetCourtesyProductResponseModel(productIdSelected, productsByPortal.ToProProductPortalListModel(),
                        new Dictionary<int, List<ProProductPortalFeatureModel>> { { productIdSelected, featuresByProduct.ToProProductPortalFeatureModelList() } });
        }

        public async Task<GetFeaturesByCourtesyProductResponseModel> GetFeaturesByCourtesyProductAsync(GetFeaturesByCourtesyProductModel model)
        {
            var featuresByProduct = await _productService.GetProProductPortalFeatureMainInfoByProductIdAndPortalIdAsync(model.productId, model.portalId);

            if (featuresByProduct == null)
                return new GetFeaturesByCourtesyProductResponseModel(new Models.ProductError(ProductErrorEnum.Validations, $"Error when try to get the features by product {model.productId} and portal {model.portalId}"));

            return new GetFeaturesByCourtesyProductResponseModel(featuresByProduct.ToProProductPortalFeatureModelList());
        }

        private async Task<List<long>> AddCompanyProductsFeaturesAsync(CompanyProductFeatureFilterModel companyProductFeatureFilter, long companyProductId, 
                                                                        ProProductPortalDTO proProductPortalDTO)
        {
            var features = await _companyProductService.GetProproductPortalFeatureByMasterAsync(proProductPortalDTO.PortalId, proProductPortalDTO.ProductId);

            List<long> idFeatures = new List<long>();
            foreach (var proProductPortalFeatureDTO in features)
            {
                long idFeature = await _companyProductService.AddCompanyProductFeatureByRepoProductsAsync(new AddCompanyProductFeatureRequestDTO()
                {
                    CompanyId = companyProductFeatureFilter.CompanyId,
                    ProductId = proProductPortalDTO.ProductId,
                    CustomerId = companyProductFeatureFilter.CustomerId,
                    CompanyProductId = companyProductId,
                    PortalId = companyProductFeatureFilter.PortalId,
                    AmbitId = proProductPortalFeatureDTO.AmbitId,
                    UserId = companyProductFeatureFilter.UserId,
                    PortalFeatureId = proProductPortalFeatureDTO.Id,
                    AvailableUnits = proProductPortalFeatureDTO.AvailableUnits,
                    InitialUnits = proProductPortalFeatureDTO.InitialUnits,
                    IsUnlimited = proProductPortalFeatureDTO.IsUnlimited,
                    IsSimultaneous = proProductPortalFeatureDTO.IsSimultaneous,
                    IsRecurrent = proProductPortalFeatureDTO.IsRecurrent,
                    FrequencyRenewDays = proProductPortalFeatureDTO.FrequencyRenewDays,
                    FeatureTypeId = proProductPortalFeatureDTO.TypeId,
                    GroupId = proProductPortalFeatureDTO.GroupId
                });
                idFeatures.Add(idFeature);
            }
            return idFeatures;
        }
    }
}
