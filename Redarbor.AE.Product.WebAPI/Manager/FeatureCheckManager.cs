using Redarbor.AE.Product.WebAPI.Enums;
using Redarbor.AE.Product.WebAPI.Models;
using Redarbor.AE.Product.WebAPI.Models.FeatureCheck;
using Redarbor.Master.Entities.Enums;
using Redarbor.Master.Entities.Product;
using Redarbor.Master.Entities.Users;
using Redarbor.Products.Contracts.ServiceLibrary;
using Redarbor.User.Contracts.ServiceLibrary;
using System.Collections.Generic;
using System.Linq;

namespace Redarbor.AE.Product.WebAPI.Manager
{
    public class FeatureCheckManager : IFeatureCheckManager
    {
        private readonly ICompanyProductService _companyProductService;
        private readonly ICompanyProductConsumUnitService _companyProductConsumUnitService;
        private readonly IUserService _userService;

        public FeatureCheckManager(
            ICompanyProductService companyProductService,
            ICompanyProductConsumUnitService companyProductConsumUnitService,
            IUserService userService
        )
        {
            _companyProductService = companyProductService;
            _companyProductConsumUnitService = companyProductConsumUnitService;
            _userService = userService;
        }
        public FeatureCheckResponseModel FeatureCheck(FeatureCheckModel model)
        {
            if (model.UserId == 0 && model.Ambits.Any(feature => IsAConsumAmbitWhichNeedsUserToBeConsumed(feature)))
                return new FeatureCheckResponseModel(new List<ProductError>() { new ProductError() { Error = ProductErrorEnum.UserMustBeInformedForThatFeature, Description = $"Features {(string.Join(" - ", model.Ambits.Where(feature => IsAConsumAmbitWhichNeedsUserToBeConsumed(feature))))} require a user to be checked" } });

            if (model.UserId > 0) 
            {
                var user = _userService.Get(model.UserId, false, model.PortalId);
                if (user == null || user.Id <= 0)
                    return new FeatureCheckResponseModel(new List<ProductError>() { new ProductError() { Error = ProductErrorEnum.GenericError, Description = "User not found" } });

                if (user.CompanyId != model.CompanyId)
                    return new FeatureCheckResponseModel(new List<ProductError>() { new ProductError() { Error = ProductErrorEnum.GenericError, Description = "User does not belong to company informed" } });

            }

            var companyProduct = _companyProductService.GetByIdCompany(model.CompanyId, model.PortalId, OfferIntegratorEnum.CompuTrabajo, model.UserId);

            if (companyProduct == null || companyProduct.Id <= 0)
                return new FeatureCheckResponseModel(new List<ProductError>() { new ProductError() { Error = ProductErrorEnum.ProductNotFound, Description = "Product not found" } });

            if (companyProduct.Features == null || !companyProduct.Features.Any())
                return new FeatureCheckResponseModel(new List<ProductError>() { new ProductError() { Error = ProductErrorEnum.ProductWithoutFeatures, Description = "Product without features" } });

            var infoAmbits = new List<FeatureCheckBaseModel>();
            List<UserEntity> usersList = this._userService.GetManagersByIdCompany(companyProduct.IdCompany, companyProduct.PortalId, false);

            foreach (var ambit in model.Ambits)
            {
                infoAmbits.Add(CheckAFeature(ambit, model, companyProduct, usersList));
            }

            return new FeatureCheckResponseModel(infoAmbits, model.CompanyId, model.UserId, model.PortalId);
        }

        private FeatureCheckBaseModel CheckAFeature(ProductAmbitEnum ambit, FeatureCheckModel model, CompanyProductEntity companyProduct, List<UserEntity> usersList)
        {
            var feature = companyProduct.Features.FirstOrDefault(e => e.AmbitId == (short)ambit);
            if (feature == null)
                return new FeatureCheckConsumibleModel(ambit) { FeatureCheckEnum = FeatureCheckEnum.ProductWithoutSpecificFeature, Description = "Product without specific feature" };


            if (ambit == ProductAmbitEnum.Users)
            {                
                if (usersList == null || usersList.Count(x => x.RoleId != (short)UserRoleEnum.COLABORADOR) < feature.InitialUnits)
                {
                    return new FeatureCheckConsumibleModel(ambit) { IsAvailable = true, ConsumedUnits = usersList.Count, InitialUnits = feature.InitialUnits, AvailableUnits = feature.InitialUnits - usersList.Count(x => x.RoleId != (short)UserRoleEnum.COLABORADOR) };
                }

                return new FeatureCheckConsumibleModel(ambit) { FeatureCheckEnum = FeatureCheckEnum.FeatureWithoutCredits, Description = $"Number of maximum users reached ({feature.InitialUnits})" };
            }

            if (IsOfferUp(ambit))
                return new FeatureCheckRenewModel(ambit) { IsAvailable = true, FrequencyRenewDays = feature.FrequencyRenewDays };

            if (!IsAConsumAmbit(ambit))
                return new FeatureCheckOnOffModel(ambit) { IsAvailable = true };

            var hasXn = false;
            if (IsOfferRelated(ambit) && !model.IgnoreXn)
                hasXn = _companyProductService.Has2N(companyProduct);            

            var ambitToSearch = ambit == ProductAmbitEnum.RenewOffer ? ProductAmbitEnum.Offer : ambit;
            var unitsByFeature = _companyProductConsumUnitService.GetConsumUnitByFeature(companyProduct, (short)ambitToSearch, 1, model.UserId, OfferIntegratorEnum.CompuTrabajo, isXN: hasXn);
            if (hasXn && unitsByFeature.CanDoAction) 
            {
                var unitsByFeatureWithoutXn = _companyProductConsumUnitService.GetConsumUnitByFeature(companyProduct, (short)ambitToSearch, 1, model.UserId, OfferIntegratorEnum.CompuTrabajo, isXN: false);
                if (!unitsByFeatureWithoutXn.CanDoAction)
                    unitsByFeature = unitsByFeatureWithoutXn;
            }

            return SetFeatureCheckDescription(feature, unitsByFeature, ambit);
        }

        private static FeatureCheckBaseModel SetFeatureCheckDescription(CompanyProductFeatureEntity feature, ControlFeatureUnitEntity unitsByFeature, ProductAmbitEnum ambit)
        {
            return new FeatureCheckConsumibleModel(ambit)
            {
                IsAvailable = unitsByFeature.CanDoAction,
                AvailableUnits = unitsByFeature.Available,
                InitialUnits = unitsByFeature.Initial,
                ConsumedUnits = unitsByFeature.Consumed,
                IsUnlimited = unitsByFeature.IsUnLimited,
                FeatureCheckEnum = unitsByFeature.CanDoAction ? FeatureCheckEnum.OK : FeatureCheckEnum.FeatureWithoutCredits,
                Description = unitsByFeature.CanDoAction ? "OK" : "Feature Without Credits"
            };
        }

        public bool IsAConsumAmbit(ProductAmbitEnum ambit)
        {
            return ambit == ProductAmbitEnum.Offer
                    || ambit == ProductAmbitEnum.OfferHighlighted
                    || ambit == ProductAmbitEnum.OfferUrgent
                    || ambit == ProductAmbitEnum.OfferHiddenName
                    || ambit == ProductAmbitEnum.OfferFlash
                    || ambit == ProductAmbitEnum.CvBBDD
                    || ambit == ProductAmbitEnum.VisionNumberGetReviews;
        }

        private bool IsAConsumAmbitWhichNeedsUserToBeConsumed(ProductAmbitEnum ambit)
        {
            return ambit == ProductAmbitEnum.Offer
                    || ambit == ProductAmbitEnum.OfferHighlighted
                    || ambit == ProductAmbitEnum.OfferUrgent
                    || ambit == ProductAmbitEnum.OfferHiddenName
                    || ambit == ProductAmbitEnum.OfferFlash
                    || ambit == ProductAmbitEnum.CvBBDD;
        }

        private bool IsOfferRelated(ProductAmbitEnum ambit)
        {
            return ambit == ProductAmbitEnum.Offer
                    || ambit == ProductAmbitEnum.RenewOffer;
        }

        private bool IsOfferUp(ProductAmbitEnum ambit)
        {
            return ambit == ProductAmbitEnum.OfferUp;
        }

        public void ResetCache(ProductAmbitEnum ambit, int companyId)
        {
            if (ambit == ProductAmbitEnum.Users) 
            {
                _userService.RemoveCacheManagerByCompanyId(companyId);
                _userService.RemoveCacheAllByCompanyId(companyId);
            }
        }
    }
}