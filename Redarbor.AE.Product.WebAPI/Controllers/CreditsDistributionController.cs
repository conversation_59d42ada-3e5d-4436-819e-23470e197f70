using Microsoft.Ajax.Utilities;
using Redarbor.AE.Product.WebAPI.Enums;
using Redarbor.AE.Product.WebAPI.Manager;
using Redarbor.AE.Product.WebAPI.Models;
using Redarbor.AE.Product.WebAPI.Models.CreditsDistribution;
using Redarbor.AE.Product.WebAPI.Models.FeatureCheck;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Master.Entities.Enums;
using Redarbor.Tools.Exceptions.Consumer;
using Redarbor.User.Contracts.ServiceLibrary;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Net;
using System.Reflection;
using System.Threading.Tasks;
using System.Web.Http;

namespace Redarbor.AE.Product.WebAPI.Controllers
{
    [RoutePrefix("v1")]
    public class CreditsDistributionController : BaseController
    {
        readonly IUserService _userService;
        readonly ICreditsDistributionManager _companyCreditsDistributionManager;

        public CreditsDistributionController
        (
            IUserService userService,
            ICreditsDistributionManager companyCreditsDistributionManager,
            IExceptionPublisherService exceptionPublisherService
        ) : base(exceptionPublisherService)
        {
            _userService = userService;
            _companyCreditsDistributionManager = companyCreditsDistributionManager;
        }

        [HttpGet()]
        [Route("company/{companyId}/creditsdistribution")]
        public async Task<IHttpActionResult> Get([Required][Range(0, int.MaxValue)] int companyId,
                                                 [Required][Range(0, short.MaxValue)] short portalId, 
                                                 [Required][Range(0, int.MaxValue)] int userId,
                                                 [Required] ProductAmbitEnum feature,
                                                 [Required][Range(0, int.MaxValue)] int idOrigin)
        {
            try
            {
                if (idOrigin <= 0)
                    return BadRequest("IdOrigin not found");

                var user = _userService.Get(userId, false, portalId);
                if (user == null)
                    return Content(HttpStatusCode.BadRequest, new Response(new ProductError(ProductErrorEnum.UserDoesNotExist)));

                if (user.CompanyId != companyId)
                    return Content(HttpStatusCode.BadRequest, new Response(new ProductError(ProductErrorEnum.UserDoesNotBelongToCompany)));


                return Ok(_companyCreditsDistributionManager.Get(user, feature));

            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "CreditsDistributionController", "Get");
                return Content(HttpStatusCode.InternalServerError,
                               new FeatureCheckResponseModel(new List<ProductError>() { new ProductError() { Error = ProductErrorEnum.GenericError, Description = "UnexpectedError" } }));
            }

        }

        [HttpPost()]
        [Route("company/{companyId}/creditsdistribution")]
        public async Task<IHttpActionResult> Save([Required][Range(0, int.MaxValue)] int companyId,
                                                  [Required][Range(0, short.MaxValue)] short portalId,
                                                  [Required] CreditsDistributionEditModel creditsDistributionEditModel,
                                                 [Required][Range(0, int.MaxValue)] int idOrigin)
        {
            try
            {
                if (idOrigin <= 0)
                    return BadRequest("IdOrigin not found");

                var user = _userService.Get(creditsDistributionEditModel.DistributedBy, false, portalId);
                if (user == null)
                    return Content(HttpStatusCode.BadRequest, new Response(new ProductError(ProductErrorEnum.UserDoesNotExist)));

                if (user.CompanyId != companyId)
                    return Content(HttpStatusCode.BadRequest, new Response(new ProductError(ProductErrorEnum.UserDoesNotBelongToCompany)));

                return Ok(_companyCreditsDistributionManager.Save(user, creditsDistributionEditModel));
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "CreditsDistributionController", "Save");
                return Content(HttpStatusCode.InternalServerError,
                               new FeatureCheckResponseModel(new List<ProductError>() { new ProductError() { Error = ProductErrorEnum.GenericError, Description = "UnexpectedError" } }));
            }

        }
    }
}