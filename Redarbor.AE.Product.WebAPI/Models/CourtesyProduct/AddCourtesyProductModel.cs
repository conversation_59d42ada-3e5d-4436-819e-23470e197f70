using Redarbor.AE.Product.WebAPI.Constants;
using System.ComponentModel.DataAnnotations;

namespace Redarbor.AE.Product.WebAPI.Models.CourtesyProduct
{
    public class AddCourtesyProductModel
    {
        [Required]
        [Range(FieldLimitationConstants.ONE, short.MaxValue)]
        public short PortalId { get; set; }
        [Required]
        [Range(FieldLimitationConstants.ONE, int.MaxValue)]
        public int CompanyId { get; set; }
        public int CustomerId { get; set; }
        [Required]
        [Range(FieldLimitationConstants.ONE, int.MaxValue)]
        public int ProductId { get; set; }
    }
}