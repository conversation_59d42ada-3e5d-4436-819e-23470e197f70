using Redarbor.AE.Product.WebAPI.Constants;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Web;

namespace Redarbor.AE.Product.WebAPI.Models.ConsumeFeature
{
    public class ConsumeFeatureModel
    {
        [Required]
        [Range(FieldLimitationConstants.ONE, int.MaxValue)]
        public int CompanyId { get; set; }

        [Required]
        [Range(FieldLimitationConstants.ONE, short.MaxValue)]
        public short PortalId { get; set; }

        [Required]
        [Range(FieldLimitationConstants.ONE, int.MaxValue)]
        public int UserId { get; set; }
    }
}