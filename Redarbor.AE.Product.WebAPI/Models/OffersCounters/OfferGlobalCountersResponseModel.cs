using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;

namespace Redarbor.AE.Product.WebAPI.Models.OffersCounters
{
    [Serializable]
    [DataContract]
    public class OfferGlobalCountersResponseModel : Response
    {
        [DataMember]
        public int CompanyId { get; set; }        
        [DataMember]
        public short PortalId { get; set; }
        [DataMember]
        public string ProductDescription { get; set; }
        [DataMember]
        public List<CounterModel> Counters { get; set; }

        public OfferGlobalCountersResponseModel(IEnumerable<ProductError> errors) : base(!errors.Any())
        {
            Errors = errors.ToList();
        }

        public OfferGlobalCountersResponseModel(int companyId,short portalId, string productDescription, List<CounterModel> counters) : base(true)
        {
            CompanyId = companyId;
            PortalId = portalId;
            ProductDescription = productDescription;
            Counters = counters;
        }
    }
}