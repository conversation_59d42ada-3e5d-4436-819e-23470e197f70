using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Web;

namespace Redarbor.AE.Product.WebAPI.Models.ProProductHUB
{
    public class CompanyProductAddRequestModel
    {
        [Required(ErrorMessage = "PortalId Required")]
        [Range(1, short.MaxValue, ErrorMessage = "PortalId min value 1")]
        public short PortalId { get; set; }
        [Required(ErrorMessage = "ProductId Required")]
        [Range(1, long.MaxValue, ErrorMessage = "ProductId min value 1")]
        public long ProductId { get; set; }
        [Required(ErrorMessage = "TemporalityId Required")]
        [Range(1, short.MaxValue, ErrorMessage = "TemporalityId min value 1")]
        public short TemporalityId { get; set; }
        public decimal Price { get; set; }
        public int CompanyId { get; set; }
        public long CustomerId { get; set; }
        public int UserId { get; set; }
        public int SourceId { get; set; }
        public int PaymentOriginId { get; set; }
        [Required(ErrorMessage = "ActiavationDate Required")]
        public DateTime ActivationDate { get; set; }
        public DateTime? ExpirationDate { get; set; }
        public string ClientIp { get; set; } = string.Empty;
    }
}