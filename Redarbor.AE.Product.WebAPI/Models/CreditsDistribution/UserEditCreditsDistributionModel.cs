using Newtonsoft.Json;
using Redarbor.AE.Product.WebAPI.Constants;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Web;

namespace Redarbor.AE.Product.WebAPI.Models.CreditsDistribution
{
    public class UserEditCreditsDistributionModel
    {
        [Required()]
        [Range(FieldLimitationConstants.ONE, long.MaxValue)]
        public long UserId { get; set; }

        [Range(FieldLimitationConstants.MINUS_ONE, int.MaxValue)]
        public int LimitedUnits { get; set; }

        [JsonIgnore]
        public bool IsChecked => LimitedUnits > -1;
        [JsonIgnore]
        public int ConsumedUnits { get; set; }
        
        
        [JsonIgnore]
        public bool HasChanges { get; set; }
        [JsonIgnore]
        public int LimitedUnitsBefore { get; set; }
        [JsonIgnore]
        public InfoCreditsModel Credits { get; set; }

        public UserEditCreditsDistributionModel()
        {
        }
    }
}