using Redarbor.AE.Product.WebAPI.Constants;
using Redarbor.Master.Entities.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Web;

namespace Redarbor.AE.Product.WebAPI.Models.CreditsDistribution
{
    public class CreditsDistributionEditModel
    {
       
        [Required()]
        [Range(FieldLimitationConstants.ONE, int.MaxValue)]
        public int DistributedBy { get; set; }

        [Required()]
        public ProductAmbitEnum Feature { get; set; }

        [Required()]
        public IEnumerable<UserEditCreditsDistributionModel> Users { get; set; }

        public CreditsDistributionEditModel()
        {
        }


    }
}