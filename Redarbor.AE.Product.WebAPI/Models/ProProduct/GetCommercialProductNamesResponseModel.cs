using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Redarbor.AE.Product.WebAPI.Models.ProProduct
{
    [Serializable]
    [DataContract]
    public class GetCommercialProductNamesResponseModel : Response
    {
        [DataMember]
        public Dictionary<int,string> CommercialProductNames { get; set; } = new Dictionary<int, string>();

        public GetCommercialProductNamesResponseModel(ProductError error) : base(error)
        {
        }

        public GetCommercialProductNamesResponseModel(Dictionary<int, string> comercialProductNames) : base(true)
        {
            CommercialProductNames = comercialProductNames;
        }    
    }
}