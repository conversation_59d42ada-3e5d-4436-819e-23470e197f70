using System.Runtime.Serialization;
using System;

namespace Redarbor.AE.Product.WebAPI.Models.ProProduct
{
    [Serializable]
    [DataContract]
    public class RemoveProProductPortalPricesResponseModel : Response
    {
        [DataMember]
        public bool IsDeleted { get; set; }
        [DataMember]
        public string CommercialProductName { get; set; }
        [DataMember]
        public int NumberPagesUsed { get; set; }
        
        public RemoveProProductPortalPricesResponseModel(ProductError error) : base(error)
        {
        }

        public RemoveProProductPortalPricesResponseModel(bool isDeleted, string commercialProductName, int numberPagesUsed) : base(true)
        {
            IsDeleted = isDeleted;
            CommercialProductName = commercialProductName;
            NumberPagesUsed = numberPagesUsed;
        }
    }
}