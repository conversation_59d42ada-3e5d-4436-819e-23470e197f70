using System.Runtime.Serialization;
using System;

namespace Redarbor.AE.Product.WebAPI.Models.ProProduct
{
    [Serializable]
    [DataContract]
    public class AddProProductResponseModel : Response
    {
        [DataMember]
        public int ProductId { get; set; }

        public AddProProductResponseModel(ProductError error) : base(error)
        {
        }

        public AddProProductResponseModel(int productId) : base(true)
        {
            ProductId = productId;
        }
    }
}