using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Redarbor.AE.Product.WebAPI.Models.ProProduct
{
    [Serializable]
    [DataContract]
    public class GetCompanyProductFeaturesDetailResponseModel : Response
    {
        [DataMember]
        public ProProductPortalModel ProductTemplate { get; set; }
        [DataMember]
        public List<ProProductPortalFeatureModel> CompanyProductFeatures { get; set; }
        [DataMember]
        public List<ProProductPortalFeatureModel> FeaturesTemplate { get; set; }
        [DataMember]
        public ProProductPortalModel CompanyProduct { get; set; }
        [DataMember]
        public Dictionary<long, List<ProProductPortalFeatureModel>> PandapeCompanyProductsFeatures { get; set; }

        public GetCompanyProductFeaturesDetailResponseModel(ProductError error) : base(error)
        {
        }

        public GetCompanyProductFeaturesDetailResponseModel(ProProductPortalModel productTemplate, List<ProProductPortalFeatureModel> companyProductFeatures,
                                                            List<ProProductPortalFeatureModel> featuresTemplate, ProProductPortalModel companyProduct,
                                                            Dictionary<long, List<ProProductPortalFeatureModel>> pandapeCompanyProductsFeatures) : base(true)
        {
            ProductTemplate = productTemplate;
            CompanyProductFeatures = companyProductFeatures;
            FeaturesTemplate = featuresTemplate;
            CompanyProduct = companyProduct;
            PandapeCompanyProductsFeatures = pandapeCompanyProductsFeatures;
        }
    }
}