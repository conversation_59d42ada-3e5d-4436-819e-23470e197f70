using Redarbor.AE.Product.WebAPI.Constants;
using System.ComponentModel.DataAnnotations;

namespace Redarbor.AE.Product.WebAPI.Models.ProProduct
{
    public class SaveProProductPortalPriceModel
    {
        [Required]
        [Range(FieldLimitationConstants.ONE, short.MaxValue)]
        public short TemporalityId { get; set; }
        [Required]
        [Range(FieldLimitationConstants.ONE, int.MaxValue)]
        public int ProductId { get; set; }
        [Required]
        [Range(FieldLimitationConstants.ONE, short.MaxValue)]
        public short PortalId { get; set; }
        [Required]
        [Range(FieldLimitationConstants.ONE, short.MaxValue)]
        public short StatusId { get; set; }
        [Required]
        [Range(FieldLimitationConstants.ONE, double.MaxValue)]
        public double Price { get; set; }
        public double MonthPrice { get; set; }
        public string LiteralShowPrice { get; set; }
        public string LiteralShowUnit { get; set; }
        public string LiteralShowTotal { get; set; }
        public int SavingPercentage { get; set; }
        public bool SelfRenewing { get; set; }
        [Required]
        public bool IsEdition { get; set; }
        public int ExpirationDays { get; set; }
    }
}