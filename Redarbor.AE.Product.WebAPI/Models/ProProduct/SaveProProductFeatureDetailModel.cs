using Redarbor.AE.Product.WebAPI.Constants;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Redarbor.AE.Product.WebAPI.Models.ProProduct
{
    public class SaveProProductFeatureDetailModel
    {
        [Required]
        [Range(FieldLimitationConstants.ONE, short.MaxValue)]
        public short PortalId { get; set; }
        [Required]
        [Range(FieldLimitationConstants.ONE, int.MaxValue)]
        public int ProductId { get; set; }
        public short LimitationMultiplier { get; set; }
        public List<ProProductPortalFeatureModel> ProductsToAdd { get; set; }
        public List<ProProductPortalFeatureModel> ProductsToUpdate { get; set; }
        public List<ProProductPortalFeatureModel> ProductsToDelete { get; set; }
    }
}