using Redarbor.AE.Product.WebAPI.Constants;
using System.ComponentModel.DataAnnotations;

namespace Redarbor.AE.Product.WebAPI.Models.ProProduct
{
    public class GetProProductPriceModel
    {
        [Required]
        [Range(FieldLimitationConstants.ONE, short.MaxValue)]
        public short portalId { get; set; }
        [Required]
        [Range(FieldLimitationConstants.ONE, int.MaxValue)]
        public int productId { get; set; }
    }
}