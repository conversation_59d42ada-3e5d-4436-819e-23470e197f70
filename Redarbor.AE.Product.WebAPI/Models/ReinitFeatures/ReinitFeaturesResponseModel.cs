using System.Runtime.Serialization;
using System;

namespace Redarbor.AE.Product.WebAPI.Models.ReinitFeatures
{
    [Serializable]
    [DataContract]
    public class ReinitFeaturesResponseModel : Response
    {
        [DataMember]
        public int RowsAffectedWithoutUserCredits { get; set; }
        [DataMember]
        public int RowsAffectedWithUserCredits { get; set; }
        [DataMember]
        public int RowsAffectedRecurrentType2 { get; set; }

        public ReinitFeaturesResponseModel(ProductError error) : base(error)
        {
        }

        public ReinitFeaturesResponseModel(int rowsAffectedWithoutUserCredits, 
            int rowsAffectedWithUserCredits, int rowsAffectedReinitRecurrentType2) : base(true)
        {
            RowsAffectedWithoutUserCredits = rowsAffectedWithoutUserCredits;
            RowsAffectedWithUserCredits = rowsAffectedWithUserCredits;
            RowsAffectedRecurrentType2 = rowsAffectedReinitRecurrentType2;
        }
    }
}