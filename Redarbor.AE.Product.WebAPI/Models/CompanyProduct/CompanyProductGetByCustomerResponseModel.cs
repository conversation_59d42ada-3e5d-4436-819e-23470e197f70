using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Web;

namespace Redarbor.AE.Product.WebAPI.Models.CompanyProduct
{
    [Serializable]
    [DataContract]
    public class CompanyProductGetByCustomerResponseModel : Response
    {
        [DataMember]
        public long CustomerId { get; }
        [DataMember]
        public short PortalId { get; }
        [DataMember]
        public IEnumerable<CompanyProductModel> CompanyProducts { get; }
        [DataMember]
        public IEnumerable<string> ActiveProductsName { get; }

        public CompanyProductGetByCustomerResponseModel(IEnumerable<ProductError> errors) : base(!errors.Any())
        {
            Errors = errors.ToList();
            CompanyProducts = new List<CompanyProductModel>();
            ActiveProductsName = new List<string>();
        }

        public CompanyProductGetByCustomerResponseModel(long customerId, short portalId, IEnumerable<CompanyProductModel> companyProducts, IEnumerable<string> activeProductsName) : base(true)
        {
            CustomerId = customerId;
            PortalId = portalId;
            CompanyProducts = companyProducts;
            ActiveProductsName = activeProductsName;
        }
    }
}