using Newtonsoft.Json.Serialization;
using Newtonsoft.Json;
using Redarbor.Master.Entities.Enums;
using System.Security.Cryptography;

namespace Redarbor.Offer.Contracts.ServiceLibrary.DTO
{
    public class OfferConvertTrace
    {
        public short IdPortal { get; set; }
        public int IdOffer { get; set; }
        public bool IsAutoConvertToComplete { get; set; } = false;
        public int IdOriginButton { get; set; } = 0; //TpvButtonOriginEnum
        public int IdPage { get; set; } //PageEnum
        public string OriginMethod { get; set; } = string.Empty;
        public bool IsFromMobile { get; set; } = false;
        public bool DeviceIsKnowed { get; set; } = false;
        public string DeviceType
        {
            get
            {
                if (DeviceIsKnowed) return (IsFromMobile) ? "Mobile" : "Desktop";
                return string.Empty;
            }
        }

        public OfferConvertTraceMatchesMailDTO OfferConvertTraceMatchesMailDTO { get; set; } = new OfferConvertTraceMatchesMailDTO();

        public string ToJson()
        {
            return JsonConvert.SerializeObject(this, new JsonSerializerSettings
            {                
                NullValueHandling = NullValueHandling.Ignore,
                Formatting = Formatting.Indented,
            });
        }
    }
}
