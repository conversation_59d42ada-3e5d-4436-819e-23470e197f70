using Redarbor.Common.Entities.Configuration;
using Redarbor.Master.Entities.Company;
using Redarbor.Master.Entities.Enums;
using Redarbor.Master.Entities.Offers;
using Redarbor.Master.Entities.Product;
using Redarbor.Offer.Contracts.ServiceLibrary.DTO;

namespace Redarbor.Offer.Contracts.ServiceLibrary
{
    public interface IOfferIntegratorService
    {
        void Insert(OfferEntity offer, PortalConfig portalConfig, ref SaveOfferResult saveOfferResult, int userId, string userName, bool isDuplicatedOffer, bool autoExcludeKillerQuestions = true, string defaultSelectStageName = "Seleccionados", bool IsSherlock = false);
        bool PostPublishStep1InsertDraft(OfferEntity offer, PortalConfig portalConfig);
        void Update(OfferEntity offer, OfferEntity offerPreUpdate, ref SaveOfferResult saveOfferResult, PortalConfig portalConfig, int userId, string userName, bool autoExcludeKillerQuestions = true, bool IsSherlock = false);
        bool PostPublishStep1OfferIntegratorUpdate(OfferEntity offer, OfferEntity offerPreUpdate, CompanyProductEntity offerProduct);
        void RePublishAllCompanyOffers(CompanyEntity company, OfferIntegratorEnum offerIntegratorEnum);

        bool SetStatus(SetStatusIntegratorDTO setStatusdto);
        bool RenewOffer(OfferEntity offer, OfferIntegratorEntity offerIntegrator);
        bool ChangeOfferVisualization(OfferEntity offer, short isChecked, int ambitId);
        bool UploadOffer(OfferEntity offer, OfferIntegratorEntity offerIntegrator, short portalId);

        int GetOfferFrequencyRenewDays(OfferEntity offer, OfferIntegratorEntity offerIntegrator);
        bool ConvertToPack(OfferEntity offer, long userId, short subGroupId, OfferConvertTrace offerConvertTrace);
        void OfferProductTrace(OfferEntity offer, OfferIntegratorEntity offerIntegrator, string action);
        void SetPostModerateOffer(OfferEntity offer, PortalConfig portalConfig, CompanyProductEntity companyProduct);
    }
}
