using FluentAssertions;
using Redarbor.AE.Procs.AbandonedOperationsNotifier.Domain.Entities;
using Redarbor.AE.Procs.AbandonedOperationsNotifier.Domain.Extensions;
using System;
using Xunit;

namespace Redarbor.AE.Procs.AbandonedOperationsNotifier.Domain.Tests
{
    public class CompanyTests
    {
        [Theory]
        [InlineData(true, false)]
        [InlineData(false, true)]
        public void CanBeNotified_Test_Success(bool notified, bool expected)
        {
            // Arrange
            Operation.DetectOperationsSinceHours = 1;
            Operation.DetectOperationsUntilHours = 3;

            short portalId = 1;
            var company = new Company
            (
                id: portalId,
                portalId: portalId,
                operation: new Operation
                {
                    Id = 10,
                    IsOk = false,
                    CompanyId = 1,
                    PortalId = portalId,
                    NotifiedAsAbandonedUtc = notified ? DateTime.UtcNow.AddHours(-2.5) : null,
                    CreatedOn = DateTime.UtcNow.AddHours(-1).OnPortal(portalId),
                    LastNotifiedCreatedOn = notified ? DateTime.UtcNow.AddHours(-3.5) : null,
                    Price = 1785488.0m,
                    BasePrice = 1785488.0m,
                    Tax = 0.0,
                    LastNotifiedId = notified ? 8 : null,
                    PaymentsOperationId = 1
                }
            );

            // Act
            // Assert
            company.CanBeNotified.Should().Be(expected);
        }
    }
}
