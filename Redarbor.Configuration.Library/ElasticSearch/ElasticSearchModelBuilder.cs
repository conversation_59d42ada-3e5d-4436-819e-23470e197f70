using Redarbor.Configuration.Library.ElasticSearch.Models;
using System;
using System.Linq;
using System.Xml.Linq;

namespace Redarbor.Configuration.Library.ElasticSearch
{
    internal class ElasticSearchModelBuilder
    {
        internal static XElement GetElasticSearchNode(XDocument globalConfiguration)
        {
            var elasticSearchNode = globalConfiguration.Root?.Element("elasticsearch");

            if (elasticSearchNode == null)
                throw new Exception("Configuration section for elasticsearch not found in global.config");

            return elasticSearchNode;
        }

        private static ElasticSearchModel GetElasticSearchModel(XDocument globalConfiguration)
        {
            var elasticSearchNode = GetElasticSearchNode(globalConfiguration);
            var elasticSearch = DeserializeElasticSeachElement(elasticSearchNode);

            return elasticSearch;
        }

        internal static ElasticSearchServer GetServerByIndexName(XDocument globalConfiguration, string indexName)
        {
            var elasticSearchModel = GetElasticSearchModel(globalConfiguration);

            return elasticSearchModel.Servers.FirstOrDefault(x => x.ElasticSearchIndexes.Any(e => string.Equals(e.Name, indexName, StringComparison.CurrentCultureIgnoreCase)));
        }

        internal static ElasticSearchServer GetServerByType(XDocument globalConfiguration, string type)
        {
            var elasticSearchModel = GetElasticSearchModel(globalConfiguration);

            return elasticSearchModel.Servers.FirstOrDefault(x => x.ElasticSearchIndexes.Any(e => string.Equals(e.Type, type, StringComparison.CurrentCultureIgnoreCase)));
        }

        private static ElasticSearchModel DeserializeElasticSeachElement(XElement elasticSearchNode)
        {
            var serversNode = elasticSearchNode.Element("servers");
            if (serversNode == null) throw new Exception("missing servers getting ElasticSearchModel");

            var elasticSearchModel = new ElasticSearchModel();
            foreach (var serverNode in serversNode.Elements("server"))
            {
                var elasticSearchServer = new ElasticSearchServer()
                {
                    HostName = serverNode.Attribute("host")?.Value ??
                               throw new Exception("missing host getting ElasticSearchServer"),
                    Port = Convert.ToInt32(serverNode.Attribute("port")?.Value ??
                                           throw new Exception("missing port getting ElasticSearchServer")),
                    IsSecure = Convert.ToBoolean(serverNode.Attribute("issecure")?.Value ??
                                                 throw new Exception("missing issecure getting ElasticSearchServer"))
                };

                var indexesElement = serverNode.Element("indexes");
                if (indexesElement != null)
                    foreach (var indexElement in indexesElement.Elements("index"))
                    {
                        var elasticSearchIndex = new ElasticSearchIndex()
                        {
                            Name = indexElement.Attribute("name")?.Value ??
                                   throw new Exception("name port getting ElasticSearchIndex"),
                            Type = indexElement.Attribute("type")?.Value ??
                                   throw new Exception("type port getting ElasticSearchIndex"),
                            Description = indexElement.Attribute("description")?.Value ??
                                          throw new Exception("description port getting ElasticSearchIndex")
                        };

                        if (elasticSearchServer.ElasticSearchIndexes.Any(x =>
                            x.Name.Equals(elasticSearchIndex.Name, StringComparison.OrdinalIgnoreCase)))
                            throw new Exception($" index misconfiguration. Duplicate index id: {elasticSearchIndex.Name}");

                        elasticSearchServer.ElasticSearchIndexes.Add(elasticSearchIndex);
                    }

                elasticSearchModel.Servers.Add(elasticSearchServer);
            }

            return elasticSearchModel;
        }
    }
}
