using System;
using System.Collections.Generic;
using System.Linq;
using System.Xml.Linq;
using Redarbor.Extensions.Library;
using Redarbor.Extensions.Library.Extensions;

namespace Redarbor.Configuration.Library.Models
{
    public class LogConfigurationModel
    {
        public static LogConfigurationModel MapFromXmlConfig(XElement globalLog4netElement, XElement appLog4netElement, XElement log4netRootElement)
        {
            var logConfigurationModel = GetConfigurationInGlobal(globalLog4netElement);

            if (appLog4netElement != null)
            {
                logConfigurationModel = UpdateWithAppConfiguration(logConfigurationModel, appLog4netElement);
                logConfigurationModel.LevelMatchFilters = GetLevelMatchFiltersFromAppConfiguration(appLog4netElement);

                // if there are overrides, we must override all appenders configured in root
                // so we check if there are any appenders in the root not overridden in the app.config 
                // and create them with provide default values
                if (logConfigurationModel.LevelMatchFilters.Any())
                {
                    var missingFilters = CreateLevelMatchFiltersForMissingOverridesInAppConfig(logConfigurationModel.LevelMatchFilters,
                                                                              log4netRootElement).ToList();
                    if (missingFilters != null && missingFilters.Count > 0)
                        logConfigurationModel.LevelMatchFilters.ToList().AddRange(missingFilters);
                }
            }

            return logConfigurationModel;
        }

        public string LogLevel { get; set; }

        public string MailLogLevel { get; set; }

        public string MailFrom { get; set; }

        public string MailTo { get; set; }

        public IEnumerable<LogAppenderLevelMatchFilters> LevelMatchFilters { get; set; }

        private static LogConfigurationModel GetConfigurationInGlobal(XElement globalLog4netElement)
        {
            var result = new LogConfigurationModel()
            {
                LogLevel = globalLog4netElement.GetRequiredConfigurationAttributeValue("defaultLogLevel", "log4net"),
                MailFrom = globalLog4netElement.GetRequiredConfigurationAttributeValue("defaultMailFrom", "log4net"),
                MailTo = globalLog4netElement.GetRequiredConfigurationAttributeValue("defaultMailTo", "log4net"),
                MailLogLevel = globalLog4netElement.GetRequiredConfigurationAttributeValue("defaultMailLevel", "log4net")
            };

            return result;
        }

        private static LogConfigurationModel UpdateWithAppConfiguration(LogConfigurationModel logConfigurationModel, XElement appLog4netElement)
        {
            string tmp = appLog4netElement.GetAttributeValue("logLevel");
            if (!string.IsNullOrEmpty(tmp))
            {
                logConfigurationModel.LogLevel = tmp;
            }

            tmp = appLog4netElement.GetAttributeValue("mailFrom");
            if (!string.IsNullOrEmpty(tmp))
            {
                logConfigurationModel.MailFrom = tmp;
            }

            tmp = appLog4netElement.GetAttributeValue("mailTo");
            if (!string.IsNullOrEmpty(tmp))
            {
                logConfigurationModel.MailTo = tmp;
            }

            tmp = appLog4netElement.GetAttributeValue("mailLevel");
            if (!string.IsNullOrEmpty(tmp))
            {
                logConfigurationModel.MailLogLevel = tmp;
            }

            return logConfigurationModel;
        }

        private static IEnumerable<LogAppenderLevelMatchFilters> GetLevelMatchFiltersFromAppConfiguration(XElement appLog4netElement)
        {
            var filtersList = new List<LogAppenderLevelMatchFilters>();

            var overrideSection = appLog4netElement.Descendants().FirstOrDefault(x => x.Name.LocalName == "overrideAppenderLevels");
            if (overrideSection != null)
            {
                var appenderOverrides = overrideSection.Descendants().Where(x => x.Name.LocalName == "appenderOverride");
                foreach (var appenderOverride in appenderOverrides)
                {
                    string appenderName = appenderOverride.GetRequiredConfigurationAttributeValue("name", "appenderOverride");
                    List<string> levels = appenderOverride.GetRequiredConfigurationAttributeValue("levels", "appenderOverride").Split(',').ToList();
                    var levelsOrderedByPrecendence = ConvertToLogLevels(levels).OrderBy(x => x).ToList();
                    var appenderFilters = new LogAppenderLevelMatchFilters(appenderName, levelsOrderedByPrecendence);
                    filtersList.Add(appenderFilters);
                }
            }

            return filtersList;
        }

        private static IEnumerable<LogAppenderLevelMatchFilters> CreateLevelMatchFiltersForMissingOverridesInAppConfig(IEnumerable<LogAppenderLevelMatchFilters> appConfigAppenderOverrides, XElement log4netRootElement)
        {
            var filtersList = new List<LogAppenderLevelMatchFilters>();

            var appendersInRoot = GetAppendersSpecifiedInRoot(log4netRootElement);
            var missingAppenderOverrides = appendersInRoot.Except(appConfigAppenderOverrides.Select(x => x.AppenderName));

            foreach (var missingAppenderName in missingAppenderOverrides)
                filtersList.Add(new LogAppenderLevelMatchFilters(missingAppenderName, new List<LogLevelType>() { LogLevelType.ERROR, LogLevelType.FATAL }));

            return filtersList;
        }

        private static IEnumerable<string> GetAppendersSpecifiedInRoot(XElement log4netRootElement)
        {
            List<string> appenders = new List<string>();

            //var root = log4netConfiguration.Descendants().FirstOrDefault(x => x.Name.LocalName == "root");
            var appenderTags = log4netRootElement.Descendants().Where(x => x.Name.LocalName == "appender-ref");
            foreach (var tag in appenderTags)
                appenders.Add(tag.GetRequiredConfigurationAttributeValue("ref", "appender-ref"));

            return appenders;
        }

        private static IEnumerable<LogLevelType> ConvertToLogLevels(IEnumerable<string> levels)
        {
            var logLevelTypes = new List<LogLevelType>();

            foreach (var level in levels)
            {
                var levelValue = level.Trim();
                if (string.IsNullOrEmpty(levelValue))
                    throw new Exception("null or empty level filter");

                try
                {
                    logLevelTypes.Add((LogLevelType)Enum.Parse(typeof(LogLevelType), levelValue, true));
                }
                catch (Exception ex)
                {
                    string problemLevel = levelValue ?? "null";
                    throw new Exception("Level filter not recognised: " + problemLevel, ex);
                }
            }

            return logLevelTypes;
        }
    }
}
