using Dapper;
using Redarbor.MatchesRecovery.Abstractions;
using Redarbor.MatchesRecovery.Models;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Redarbor.MatchesRecovery.Repositories
{
    public class OfferProductTraceRepository : IOfferProductTraceRepository
    {
        private readonly IMasterRepository _masterRepository;

        public OfferProductTraceRepository(IMasterRepository masterRepository)
        {
            _masterRepository = masterRepository;
        }

        public async Task<ICollection<OfferProductTrace>> GetAll(short idPortal, int idCompany, int idUser, string action, DateTime iniDate, DateTime lastDate)
        {
            var connection = await _masterRepository.GetMasterAuxConnectionAsync(idPortal);
            if (connection.State != ConnectionState.Open)
            {
                connection.Open();
            }
            var sql = @"select * from dtoffer_product_trace
                        where idportal = @IdPortal and idcompany = @IdCompany and action = @Action and iduser = @IdUser
                        and date_Add >= @IniDate and date_add < @LastDate";
            return (await connection.QueryAsync<OfferProductTrace>(sql, new 
            { 
                IdPortal = idPortal, 
                IdCompany = idCompany, 
                IdUser = idUser, 
                Action = action, 
                IniDate = iniDate, 
                LastDate = lastDate
            })).ToList();
        }
    }
}
