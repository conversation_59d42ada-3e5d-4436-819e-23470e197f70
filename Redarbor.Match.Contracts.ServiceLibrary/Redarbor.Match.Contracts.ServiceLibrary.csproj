<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{28676D69-97F1-49D3-9E92-C0EB95D45426}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Redarbor.Match.Contracts.ServiceLibrary</RootNamespace>
    <AssemblyName>Redarbor.Match.Contracts.ServiceLibrary</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x64\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\x64\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Newtonsoft.Json, Version=7.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.7.0.1\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="DTO\BaseResultDTO.cs" />
    <Compile Include="DTO\CandidateProcessStatusDTO.cs" />
    <Compile Include="DTO\CvFileDTO.cs" />
    <Compile Include="DTO\GetLastByOfferIdsDTO.cs" />
    <Compile Include="DTO\KeysMatchListElasticDTO.cs" />
    <Compile Include="DTO\KeysMatchElasticDTO.cs" />
    <Compile Include="DTO\FilterExclusionMatchesListsDTO.cs" />
    <Compile Include="DTO\FilterExclusionMatchesDTO.cs" />
    <Compile Include="DTO\PublishTotalNewMatchesDecreaserInRabbitDTO.cs" />
    <Compile Include="DTO\SearchBaseDTO.cs" />
    <Compile Include="DTO\SearchFilterDTO.cs" />
    <Compile Include="DTO\SearchResultDTO.cs" />
    <Compile Include="DTO\TimelineExtraParamsChangeMatchDTO.cs" />
    <Compile Include="DTO\TotalNewMatchesDecreaserRabbitMessageDTO.cs" />
    <Compile Include="DTO\WorkExperienceDTO.cs" />
    <Compile Include="Entities\MatchElasticEntity.cs" />
    <Compile Include="Enums\MatchesEsclusionFilterEnum.cs" />
    <Compile Include="IExclusionMatchService.cs" />
    <Compile Include="IMatchDownloaderService.cs" />
    <Compile Include="IAdaptorMatchElasticConsumerService.cs" />
    <Compile Include="IMatchElasticService.cs" />
    <Compile Include="IMatchService.cs" />
    <Compile Include="IMatchDecrementsService.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Readarbor.Master.Entities\Redarbor.Master.Entities.csproj">
      <Project>{72b93ba2-c177-4ddf-9f27-e08151323719}</Project>
      <Name>Redarbor.Master.Entities</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Common.Entities\Redarbor.Common.Entities.csproj">
      <Project>{3da7e832-19ae-4c35-85f8-5faf5a4dabeb}</Project>
      <Name>Redarbor.Common.Entities</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Computrabajo.Contracts.ServiceLibrary\Redarbor.Core.Computrabajo.Contracts.ServiceLibrary.csproj">
      <Project>{9BE71737-B615-4227-BB44-C5ECEC3DCED3}</Project>
      <Name>Redarbor.Core.Computrabajo.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Elastic.Library\Redarbor.Core.Elastic.Library.csproj">
      <Project>{66d471b3-da38-4a79-820b-028cde8bc335}</Project>
      <Name>Redarbor.Core.Elastic.Library</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Geolocation.Contracts.ServiceLibrary\Redarbor.Geolocation.Contracts.ServiceLibrary.csproj">
      <Project>{6db7d0e8-b5c5-42ea-81c1-0e1418cbd36a}</Project>
      <Name>Redarbor.Geolocation.Contracts.ServiceLibrary</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>