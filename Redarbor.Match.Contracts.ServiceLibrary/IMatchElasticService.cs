using Redarbor.Match.Contracts.ServiceLibrary.DTO;
using Redarbor.Match.Contracts.ServiceLibrary.Entities;
using System.Collections.Generic;

namespace Redarbor.Match.Contracts.ServiceLibrary
{
    public interface IMatchElasticService
    {
        bool UpdatePartialMatch(MatchElasticEntity match);
        MatchElasticEntity Get(KeysMatchElasticDTO matchDTO);
        List<MatchElasticEntity> GetByIds(KeysMatchListElasticDTO keysmatchListElasticDTO);
        List<MatchElasticEntity> GetLastByOfferIds(GetLastByOfferIdsDTO filter);
        bool Delete(KeysMatchElasticDTO keysMatch);
        SearchResultDTO<MatchElasticEntity> SearchElasticMatch(SearchFilterDTO filter, bool withFacets = true, bool counter = false);
        SearchResultDTO<MatchElasticEntity> SearchElasticFacetMatch(SearchFilterDTO searchFilter);
        bool IndexByIds(List<long> matches, short portalId);
    }
}
