using Redarbor.Master.Entities.Enums;
using Redarbor.Master.Entities.Stack;

namespace Redarbor.Core.Stack.Library.DomainServiceContracts
{
    public interface IStackModRecoverAndPersist
    {
        bool UpdateStatus(StackModEntity stackmod);
        int Add(StackModEntity stackmod);
        void AddToQueue(short portal, long idoffer, StackModQueueEnum queue);
        void UpdateQueueStatus(short idportal, long idoffer, StackModQueueEnum queue);
        void InsertHistoryQueue(int idOffer, short idQueue, short idPortal);
    }
}
