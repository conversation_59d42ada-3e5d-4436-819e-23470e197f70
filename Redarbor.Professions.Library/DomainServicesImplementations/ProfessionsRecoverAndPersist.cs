using System;
using Redarbor.Professions.Library.DomainServiceContracts;
using MySql.Data.MySqlClient;
using Redarbor.Core.Resolver.Contracts.ServiceLibrary;
using Redarbor.Core.Resolver.Contracts.ServiceLibrary.Enums;
using System.Data;
using System.Diagnostics;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Extensions.Library.Extensions;
using System.Threading.Tasks;
using Redarbor.Professions.Library.DTO;
using Google.Protobuf.WellKnownTypes;

namespace Redarbor.Professions.Library.DomainServicesImplementations
{
    public class ProfessionsRecoverAndPersist : IProfessionsRecoverAndPersist
    {
        private readonly IConnectionStringResolverService _connectionStringResolverService;
        private readonly IExceptionPublisherService _exceptionPublisherService;

        public ProfessionsRecoverAndPersist(IConnectionStringResolverService connectionStringResolverService,
                        IExceptionPublisherService exceptionPublisherService)
        {
            _connectionStringResolverService = connectionStringResolverService;
            _exceptionPublisherService = exceptionPublisherService;
        }

        public int InsertOrUpdateProfession(string profession, short portalId, short appId, int idCategory)
        {
            int id = 0;

            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
            {
                using (MySqlCommand command = new MySqlCommand("sp_professions_Insert_v2", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    try
                    {
                        command.Parameters.Add(new MySqlParameter("_newCargo", profession));
                        command.Parameters.Add(new MySqlParameter("_idportal", portalId));
                        command.Parameters.Add(new MySqlParameter("_idapp", appId));
                        command.Parameters.Add(new MySqlParameter("_idcategory", idCategory));

                        connection.Open();

                        id = command.ExecuteScalar().ToInt();
                    }
                    catch (Exception ex)
                    {
                        Trace.TraceError($"ProfessionsRecoverAndPersist - InsertProfession:{ex} ");
                        _exceptionPublisherService.Publish(ex, "ProfessionsRecoverAndPersist", "InsertProfession");
                    }
                }
            }
            return id;
        }

        public async Task<int> InsertOrUpdateProfessionAsync(InsertProfessionPersistentLayerDTO dto)
        {
            int id = 0;

            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, dto.PortalId)))
            {
                using (MySqlCommand command = new MySqlCommand("sp_professions_Insert_v2", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    try
                    {
                        command.Parameters.Add(new MySqlParameter("_newCargo", dto.Profession));
                        command.Parameters.Add(new MySqlParameter("_idportal", dto.PortalId));
                        command.Parameters.Add(new MySqlParameter("_idapp", dto.AppId));
                        command.Parameters.Add(new MySqlParameter("_idcategory", dto.IdCategory));

                        connection.Open();

                        id = (await command.ExecuteScalarAsync()).ToInt();
                    }
                    catch (Exception ex)
                    {
                        Trace.TraceError($"ProfessionsRecoverAndPersist - InsertOrUpdateProfessionAsync:{ex} ");
                        _exceptionPublisherService.Publish(ex, "ProfessionsRecoverAndPersist", "InsertOrUpdateProfessionAsync", false, null, dto.PortalId);
                    }
                }
            }
            return id;
        }
    }
}
