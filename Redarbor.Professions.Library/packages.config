<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="BouncyCastle" version="1.8.5" targetFramework="net472" />
  <package id="BouncyCastle.Cryptography" version="2.5.1" targetFramework="net472" />
  <package id="Google.Protobuf" version="3.30.0" targetFramework="net472" />
  <package id="K4os.Compression.LZ4" version="1.3.8" targetFramework="net472" />
  <package id="K4os.Compression.LZ4.Streams" version="1.3.8" targetFramework="net472" />
  <package id="K4os.Hash.xxHash" version="1.0.8" targetFramework="net472" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="5.0.0" targetFramework="net472" />
  <package id="MySql.Data" version="9.3.0" targetFramework="net472" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net472" />
  <package id="System.Configuration.ConfigurationManager" version="8.0.0" targetFramework="net472" />
  <package id="System.Diagnostics.DiagnosticSource" version="8.0.1" targetFramework="net472" />
  <package id="System.IO.Pipelines" version="5.0.2" targetFramework="net472" />
  <package id="System.Memory" version="4.5.5" targetFramework="net472" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net472" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net472" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net472" />
  <package id="ZstdSharp.Port" version="0.8.5" targetFramework="net472" />
</packages>