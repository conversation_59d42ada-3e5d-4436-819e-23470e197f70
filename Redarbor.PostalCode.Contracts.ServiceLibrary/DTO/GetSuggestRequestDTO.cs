using System.Collections.Generic;

namespace Redarbor.PostalCode.Contracts.ServiceLibrary.DTO
{
    public class GetSuggestRequestDTO
    {
        public short PortalId { get; set; }
        public string Query { get; set; }
        public int Limit { get; set; }
        public List<string> Fields { get; set; } = new List<string>
        {
            "IdPostalCode", "PostalCode", "IdLocalization", "IdCity", "City", "District"
        };
        public int IdCtity { get; set; }
        public int IdLocalization { get; set; }
    }
}
