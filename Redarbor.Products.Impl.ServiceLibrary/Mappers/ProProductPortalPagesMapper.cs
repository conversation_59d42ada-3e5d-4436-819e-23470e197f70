using Redarbor.Products.Contracts.ServiceLibrary.DTO;
using Redarbor.Products.Library.DTOs;
using System.Collections.Generic;
using System.Linq;

namespace Redarbor.Products.Impl.ServiceLibrary.Mappers
{
    public static class ProProductPortalPagesMapper
    {
        public static List<ProductPageDTO> ToProductPageDTOList(this List<ProProductPortalPagesPersistentLayerDTO> p)
        {
            return p.Select(s => new ProductPageDTO
            {
                AppId = s.AppId,
                IdSegment = s.IdSegment,
                IsDefault = s.IsDefault,
                Name = s.Name,
                PageId = s.PageId,
                PortalId = s.PortalId,
                Priority = s.Priority,
                ProductId = s.ProductId,
                SubgroupId = s.SubgroupId,
                TemporalityId = s.TemporalityId                
            }).ToList();
        }
    }
}
