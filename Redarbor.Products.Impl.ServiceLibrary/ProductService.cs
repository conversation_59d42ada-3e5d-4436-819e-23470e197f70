using Redarbor.Common.Entities.Configuration;
using Redarbor.Common.Entities.Enums;
using Redarbor.Core.Cache.Contracts.ServiceLibrary;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Core.Shared.Contracts.ServiceLibrary;
using Redarbor.Extensions.Library.DI;
using Redarbor.Master.Entities.Enums;
using Redarbor.Master.Entities.Offers;
using Redarbor.Master.Entities.Product;
using Redarbor.Master.Entities.Searchs;
using Redarbor.Products.Contracts.ServiceLibrary;
using Redarbor.Products.Contracts.ServiceLibrary.DTO;
using Redarbor.Products.Impl.ServiceLibrary.Mappers;
using Redarbor.Products.Library.DomainServiceContracts;
using Redarbor.Products.Library.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Redarbor.Products.Impl.ServiceLibrary
{
    [RegisterStatefulService]
    public class ProductService : IProductService
    {
        const string KEY_CACHE_PRODUCTS = "PRODUCTS_";
        const string KEY_CACHE_PRODUCTS_LIST = "PRODUCTS_LIST_PORTAL_";
        const string KEY_CACHE_PROMOTIONS = "PROMOTIONS_";
        const string KEY_CACHE_INITIAL_VALUES_FEATURE = "INITIAL_VALUES_FEATURE_PORTAL_ID_";

        private readonly ITempCache _tempCacheService;
        private readonly IProductRecoverAndPersist _productRecoverAndPersist;
        private readonly IExceptionPublisherService _exceptionPublisherService;
        private readonly IStringToolsService _stringToolsService;
        private readonly IEncryptionService _encryptionService;
        private readonly IPortalConfigurationService _portalConfigurationService;

        public ProductService(ITempCache tempCacheService,
            IProductRecoverAndPersist productRecoverAndPersist,
            IExceptionPublisherService exceptionPublisherService,
            IStringToolsService stringToolsService,
            IEncryptionService encryptionService,
            IPortalConfigurationService portalConfigurationService)
        {
            _tempCacheService = tempCacheService;
            _productRecoverAndPersist = productRecoverAndPersist;
            _exceptionPublisherService = exceptionPublisherService;
            _stringToolsService = stringToolsService;
            _encryptionService = encryptionService;
            _portalConfigurationService = portalConfigurationService;
        }

        public ProductEntity GetCustomOfferProduct(short idPortal)
        {
            var product = new ProductEntity();

            if (idPortal > 0)
            {
                product = _productRecoverAndPersist.GetCustomOfferProduct(idPortal);
            }

            return product;
        }

        public SegmentCompanyEntity GetSegmentByCompanyId(PromotionSearchSpecifications specifications)
        {
            if (specifications == null || specifications.PortalId == 0 || specifications.CompanyId == 0)
                return new SegmentCompanyEntity();

            return _productRecoverAndPersist.GetSegmentByCompanyId(specifications);
        }

        public bool CompanyHasPromotion(SegmentCompanyEntity segmentCompany, List<PromotionEntity> listPromotionsPortal)
        {
            if (segmentCompany == null || listPromotionsPortal == null || listPromotionsPortal.Count == 0)
                return false;

            bool hasPromo = false;
            foreach (var promotion in listPromotionsPortal)
            {
                if (!segmentCompany.HasComercialActivity || (promotion.ApplyToCrmCompanies && segmentCompany.HasComercialActivity))
                {
                    hasPromo = hasPromo || CheckHasPromotion(segmentCompany, promotion);
                }
            }

            return hasPromo;
        }

        public bool CompanyHasPromotionByProduct(SegmentCompanyEntity segmentCompany, List<PromotionEntity> listPromotionsPortal, int productId)
        {
            bool hasPromo = false;
            foreach (var promotion in listPromotionsPortal)
            {
                if (promotion.ProductId == productId)
                {
                    if (!segmentCompany.HasComercialActivity || (promotion.ApplyToCrmCompanies && segmentCompany.HasComercialActivity))
                    {
                        hasPromo = hasPromo || CheckHasPromotion(segmentCompany, promotion);
                    }
                }
            }
            return hasPromo;
        }

        public List<PromotionEntity> GetPromotionsByType(PromotionSearchSpecifications specifications)
        {
            List<PromotionEntity> promotions = new List<PromotionEntity>();

            string cacheName = $"{KEY_CACHE_PROMOTIONS}PR:{specifications.TypePromotion}-POR:{specifications.PortalId}-CMPID:{specifications.CompanyId}-ST:{specifications.StatusId}-PR:{specifications.ProductId}";

            promotions = _tempCacheService.Get<List<PromotionEntity>>(cacheName);

            if (promotions != null
                && promotions.Any())
            {
                var lastMaxDatePromotion = _productRecoverAndPersist.GetLastMaxDateTimePromotion(specifications.PortalId, specifications.TypePromotion, specifications.CompanyId);

                var lastMaxDateTimePromotionCache = promotions.Max(d => d.DateMod);

                if ((lastMaxDatePromotion == lastMaxDateTimePromotionCache))
                    return promotions;
            }

            promotions = _productRecoverAndPersist.SelectAllPromotions(specifications);
            promotions = promotions.OrderBy(m => m.Preferent).ToList();

            _tempCacheService.Add(cacheName, promotions, DateTime.Now.AddMinutes(15));

            return promotions;
        }

        public List<PromotionEntity> GetAllPromotions(PromotionSearchSpecifications specifications)
        {
            try
            {
                var generalPromotions = GetGeneralPromotions(specifications);
                var automaticPacksPromotions = GetAutomaticPacksPromotions(specifications);

                if (!automaticPacksPromotions.Any())
                    return generalPromotions;

                return MergeTypePromotions(generalPromotions, automaticPacksPromotions);
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "ProductRepository", "TrySelectAllPromotions");
                return new List<PromotionEntity>();
            }
        }

        private List<PromotionEntity> MergeTypePromotions(List<PromotionEntity> generalPromotions, List<PromotionEntity> automaticPacksPromotions)
        {
            var result = new List<PromotionEntity>();

            foreach (var generalPromotion in generalPromotions)
            {
                if (automaticPacksPromotions.Exists(e => e.ProductId == generalPromotion.ProductId)
                    && automaticPacksPromotions.FirstOrDefault(p => p.ProductId == generalPromotion.ProductId).NumericPrice <= generalPromotion.NumericPrice)
                    result.Add(automaticPacksPromotions.FirstOrDefault(p => p.ProductId == generalPromotion.ProductId));
                else
                    result.Add(generalPromotion);
            }

            foreach (var automaticPackPromotion in automaticPacksPromotions)
                if (!result.Exists(e => e.ProductId == automaticPackPromotion.ProductId))
                    result.Add(automaticPackPromotion);

            return result;
        }

        public List<PromotionEntity> GetAutomaticPacksPromotions(PromotionSearchSpecifications specifications)
        {
            var result = new List<PromotionEntity>();

            if (specifications.CompanyId > 0
                && specifications.PortalId > 0)
            {
                specifications.TypePromotion = (short)PromotionTypeEnum.AutomaticPacks;

                result = _tempCacheService.Get<List<PromotionEntity>>($"{KEY_CACHE_PROMOTIONS}_AUTOMATICPACKS{specifications.PortalId}_IDCOMPANY_{specifications.CompanyId}", specifications.PortalId);

                if (result != null
                    && result.Any())
                {
                    var lastMaxDatePromotion = _productRecoverAndPersist.GetLastMaxDateTimePromotion(specifications.PortalId, specifications.TypePromotion, specifications.CompanyId);

                    var lastMaxDateTimePromotionCache = result.Max(d => d.DateMod);

                    if ((lastMaxDatePromotion == lastMaxDateTimePromotionCache))
                        return result;
                }

                result = _productRecoverAndPersist.SelectAllPromotions(specifications);
                result = result.OrderBy(m => m.Preferent).ToList();


                _tempCacheService.Add($"{KEY_CACHE_PROMOTIONS}_AUTOMATICPACKS{specifications.PortalId}_IDCOMPANY_{specifications.CompanyId}", result, DateTime.Now.AddMinutes(15), specifications.PortalId);

                return result;
            }

            return result;
        }

        private List<PromotionEntity> GetGeneralPromotions(PromotionSearchSpecifications specifications)
        {
            List<PromotionEntity> promotions = new List<PromotionEntity>();
            specifications.TypePromotion = (short)PromotionTypeEnum.General;

            string cacheName = $"{KEY_CACHE_PROMOTIONS}_IDPORTAL_{specifications.PortalId}_TYPE_{specifications.TypePromotion}_IDCOMPANY_{specifications.CompanyId}";

            promotions = _tempCacheService.Get<List<PromotionEntity>>(cacheName);

            if (promotions != null
                && promotions.Any())
            {
                var lastMaxDatePromotion = _productRecoverAndPersist.GetLastMaxDateTimePromotion(specifications.PortalId, specifications.TypePromotion);

                var lastMaxDateTimePromotionCache = promotions.Max(d => d.DateMod);

                if ((lastMaxDatePromotion == lastMaxDateTimePromotionCache))
                    return promotions;
            }

            promotions = _productRecoverAndPersist.SelectAllPromotions(specifications);
            promotions = promotions.OrderBy(m => m.Preferent).ToList();

            _tempCacheService.Add(cacheName, promotions, DateTime.Now.AddMinutes(15));

            return promotions;
        }

        public PromotionEntity GetPromotionByProductId(int productId, short temporalityId, ref List<PromotionEntity> listPromotion)
        {
            if (productId > 0 && listPromotion.Count > 0)
            {
                listPromotion = listPromotion.OrderBy(p => p.Preferent).ToList();

                foreach (var promotion in listPromotion)
                {
                    if (promotion.ProductId == productId && promotion.TemporalityId == temporalityId)
                        return promotion;
                }
            }

            return new PromotionEntity();
        }

        public void CreateVoucherPromotionPack(int companyId, short portalId, List<ProductVoucherConfigEntity> productVoucherConfigList)
        {
            var productsToFoundString = string.Empty;
            var voucherProductIds = productVoucherConfigList.Select(p => p.ProductId).ToList();
            if (voucherProductIds.Any())
                productsToFoundString = string.Join(",", voucherProductIds);

            var productList = GetProductsByIdsProducts(portalId, productsToFoundString);
            var result = new PromotionEntity();

            if (productList.Any())
            {
                var portalConfig = _portalConfigurationService.GetPortalConfiguration();

                var promotionsVoucher = GetPromotionsByType(new PromotionSearchSpecifications(portalConfig.PortalId)
                {
                    StatusId = (int)PromotionStatusEnum.Active,
                    CompanyId = companyId,
                    TypePromotion = (short)PromotionTypeEnum.VoucherPacks
                });

                foreach (var product in productList)
                {
                    var initialUnits = GetInitialUnitsByAmbit(product.Id, portalId, (short)ProductAmbitEnum.Offer);

                    var productVoucherConfig = productVoucherConfigList.Where(p => p.ProductId == product.Id).OrderByDescending(d => d.DateAdd).FirstOrDefault();

                    var promotionExist = promotionsVoucher.Any(p => p.ProductId == product.Id);

                    if (product.Price > 0 && initialUnits > 0 && productVoucherConfig != null && !promotionExist)
                        Add(GenerateVoucherPromotion(product, productVoucherConfig, portalConfig, initialUnits, companyId));

                    _tempCacheService.Remove($"{KEY_CACHE_PRODUCTS}{portalId}_{(short)PageEnum.PackCart}_{product.SubGroupId}_{companyId}");
                    _tempCacheService.Remove($"{KEY_CACHE_PRODUCTS}{portalId}_{(short)PageEnum.DefaultCart}_{product.SubGroupId}_{companyId}");
                    _tempCacheService.Remove($"{KEY_CACHE_PROMOTIONS}PR:{(short)PromotionTypeEnum.VoucherPacks}-POR:{portalId}-CMPID:{companyId}-ST:{(int)PromotionStatusEnum.Active}-PR:0");
                }
                _tempCacheService.Remove($"{KEY_CACHE_PRODUCTS}{portalId}_{(short)PageEnum.PackCart}_0_{companyId}");
                _tempCacheService.Remove($"{KEY_CACHE_PRODUCTS}{portalId}_{(int)PageEnum.CompanyNewProductsHome}_0");
            }
        }

        public int GetMinimPercentPromoVoucher(int companyId, short portalId)
        {
            var listDiscounts = new List<int>();
            var promotions = GetPromotionsByType(new PromotionSearchSpecifications(portalId)
            {
                StatusId = (int)PromotionStatusEnum.Active,
                CompanyId = companyId,
                TypePromotion = (short)PromotionTypeEnum.VoucherPacks
            });

            if (promotions.Any())
            {
                foreach (var promotion in promotions)
                {
                    int.TryParse(promotion.LiteralPercentDiscount.Contains("%") ? promotion.LiteralPercentDiscount.Replace("%", string.Empty) : promotion.LiteralPercentDiscount, out var discount);
                    listDiscounts.Add(discount);
                }
                if (listDiscounts.Any())
                    return listDiscounts.Min();
            }
            return 0;
        }

        public int GetInitialUnitsByAmbit(int productId, short portalId, int ambitId)
        {
            if (productId == 0 || portalId == 0 || ambitId == 0)
            {
                return 0;
            }

            string key = GetKey(productId, portalId, ambitId);

            var cache = _tempCacheService.Get<int>(key);

            if (cache > 0)
            {
                return cache;
            }

            cache = _productRecoverAndPersist.GetInitialUnitsByAmbit(productId, portalId, ambitId);
            _tempCacheService.Add(key.ToString(), cache, DateTime.Now.AddMinutes(15));

            return cache;
        }

        public List<ProductEntity> GetProducts(ProductSearchSpecifications specifications)
        {
            if (specifications == null)
                return new List<ProductEntity>();

            var keyCache = $"{KEY_CACHE_PRODUCTS_LIST}{specifications.PortalId}";

            List<ProductEntity> listProducts = _tempCacheService.Get<List<ProductEntity>>(keyCache);

            if (listProducts != null && listProducts.Count > 0)
                return listProducts;
            else
            {
                listProducts = _productRecoverAndPersist.GetProducts(specifications);
                _tempCacheService.Add(keyCache, listProducts, DateTime.Now.AddMinutes(15));

                return listProducts;
            }
        }

        public List<ProductEntity> SearchByPage(ProductSearchSpecifications productSpecifications, bool fromCart = false)
        {
            try
            {
                var keyCache = $"{KEY_CACHE_PRODUCTS}{productSpecifications.PortalId}_{productSpecifications.PageId}_{productSpecifications.SubGroup}_{productSpecifications.CompanyId}";

                List<ProductEntity> products = new List<ProductEntity>();

                products = _tempCacheService.Get<List<ProductEntity>>(keyCache);

                if (products != null && products.Count > 0)
                {
                    return products;
                }
                else
                {
                    products = _productRecoverAndPersist.GetProductsByPage(productSpecifications);
                    products.ForEach(c => c.EncryptedId = _encryptionService.Encrypt(c.Id.ToString()));

                    if (productSpecifications.DisableFreemium)
                    {
                        products = products.Where(p => p.GroupId != (short)ProductGroupsEnum.Freemium).ToList();
                    }

                    if (products.Any() && productSpecifications.LoadPromotions)
                    {
                        var promotions = new List<PromotionEntity>();
                        if (_portalConfigurationService.GetPortalConfiguration().AEPortalConfig.DaysPromotion > 0)
                        {
                            promotions = GetPromotionsByType(new PromotionSearchSpecifications(productSpecifications.PortalId)
                            {
                                StatusId = (int)PromotionStatusEnum.Active,
                                CompanyId = productSpecifications.CompanyId,
                                TypePromotion = (short)PromotionTypeEnum.VoucherPacks
                            });
                        }

                        if (!promotions.Any())
                        {
                            promotions = GetAllPromotions(new PromotionSearchSpecifications(productSpecifications.PortalId)
                            {
                                StatusId = (int)PromotionStatusEnum.Active,
                                CompanyId = productSpecifications.CompanyId
                            });
                        }

                        if (promotions.Count > 0)
                        {
                            products.ForEach(product =>
                            {
                                var promotion = promotions.Where(promo => promo.ProductId == product.Id).ToList();
                                if ((promotion.Any(p => p.TypePromotion != (short)PromotionTypeEnum.VoucherPacks) && !fromCart) || fromCart)
                                {
                                    product.Promotions = promotions.Where(promo => promo.ProductId == product.Id).ToList();
                                }
                            });
                        }
                    }
                }
                if (products.Count > 0)
                    _tempCacheService.Add(keyCache, products, DateTime.Now.AddMinutes(15));

                return products;
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "ProductService", "SearchByPage");
                List<ProductEntity> products = new List<ProductEntity>();
                return products;
            }
        }

        public void Add(PromotionEntity promotion)
        {
            if (promotion != null && promotion.InsertPromotionValidate())
                _productRecoverAndPersist.Insert(promotion);

            if (promotion != null && promotion.InsertPromotionSegmentValidate())
            {
                _productRecoverAndPersist.InsertSegment(promotion);
            }
        }

        public List<ProductEntity> GetProductsByIdsProducts(short portalId, string idsProducts)
        {
            var keyCache = $"PRODUCTS_BY_PRODUCT_IDS{idsProducts}_PORTAL_ID_{portalId}";
            var products = _tempCacheService.Get<List<ProductEntity>>(keyCache);

            if (products != null && products.Any())
                return products;

            products = _productRecoverAndPersist.GetByIdsProducts(portalId, idsProducts);

            if (products.Any())
            {
                _tempCacheService.Add(keyCache, products, DateTime.Now.AddMinutes(15));
            }

            return products;
        }

        public List<ProductFeatureEntity> GetProductFeatures(ProductSearchSpecifications productSpecifications)
        {
            var keyCache = $"PRODUCT_FEATURES_BY_PRODUCT_ID_{productSpecifications.Id}_PORTAL_ID_{productSpecifications.PortalId}";
            var productFeatures = _tempCacheService.Get<List<ProductFeatureEntity>>(keyCache);

            if (productFeatures != null && productFeatures.Any())
                return productFeatures;

            productFeatures = _productRecoverAndPersist.GetProductFeatures(productSpecifications);

            if (productFeatures.Any())
                _tempCacheService.Add(keyCache, productFeatures, DateTime.Now.AddMinutes(15));

            return productFeatures;
        }

        public List<ProductFeatureDescriptionEntity> GetSubGroupFeaturesDescriptions(short idSubGroup, short idPortal)
        {
            var keyCache = $"PRODUCT_FEATURES_DESCRIPTION_BY_SUBGROUP_ID_{idSubGroup}_PORTAL_ID_{idPortal}";
            var productFeaturesDescriptions = _tempCacheService.Get<List<ProductFeatureDescriptionEntity>>(keyCache);

            if (productFeaturesDescriptions != null && productFeaturesDescriptions.Any())
                return productFeaturesDescriptions;

            productFeaturesDescriptions = _productRecoverAndPersist.GetSubGroupFeaturesDescriptions(idSubGroup, idPortal);

            if (productFeaturesDescriptions.Any())
                _tempCacheService.Add(keyCache, productFeaturesDescriptions, DateTime.Now.AddMinutes(15));

            return productFeaturesDescriptions;
        }
        public List<ProductFeatureDescriptionEntity> GetProductFeaturesDescriptionsByIdProduct(int idProduct, short idPortal)
        {
            var keyCache = $"PRODUCT_FEATURES_DESCRIPTION_BY_IDPRODUCT_ID_{idProduct}_PORTAL_ID_{idPortal}";
            var productFeaturesDescriptions = _tempCacheService.Get<List<ProductFeatureDescriptionEntity>>(keyCache);

            if (productFeaturesDescriptions != null && productFeaturesDescriptions.Any())
                return productFeaturesDescriptions;

            productFeaturesDescriptions = _productRecoverAndPersist.GetProductFeaturesDescriptionsByIdProduct(idProduct, idPortal);

            if (productFeaturesDescriptions.Any())
                _tempCacheService.Add(keyCache, productFeaturesDescriptions, DateTime.Now.AddMinutes(15));

            return productFeaturesDescriptions;
        }

        public CurrencyEntity GetCurrency(short portalId)
        {
            var cacheKey = $"CURRENCY_BY_PORTAL_ID_{portalId}";
            var currency = _tempCacheService.Get<CurrencyEntity>(cacheKey);

            if (currency != null)
                return currency;

            currency = _productRecoverAndPersist.GetCurrency(portalId);

            if (currency != null)
                _tempCacheService.Add(cacheKey, currency, TimeSpan.FromHours(12));

            return currency;
        }

        public String SetCurrency(decimal money, PortalConfig portal)
        {
            money = RemoveDecimals(money, portal.PortalId);
            string Result = string.Empty;

            var currency = portal.CurrencyPortal;

            string appCurrency = currency.Currency;
            short appPositionCurrency = currency.PositionCurrency;

            if (appPositionCurrency == 0)
            {
                Result = $"{appCurrency} {_stringToolsService.ChangeGroupPointForDecimalPoint(money.ToString(money % 1 == 0 ? "N0" : "N2"), portal.PortalId)}";
            }
            else if (appPositionCurrency == 1)
                Result = $"{_stringToolsService.ChangeGroupPointForDecimalPoint(money.ToString(money % 1 == 0 ? "N0" : "N2"), portal.PortalId)} {appCurrency}";

            return Result;
        }

        public static decimal RemoveDecimals(decimal value, short idPortal)
        {
            switch (idPortal)
            {
                case (int)PortalEnum.ComputrabajoColombia:
                    return decimal.Round(value, 0);
            }

            return value;
        }

        public int GetProductGroupIdByProductId(short idPortal, int idProduct)
        {
            var cacheKey = $"PRODUCT_GROUP_BY_PRODUCT_PORTAL_{idPortal}_ID_PRODUCT_{idProduct}";
            var groupId = _tempCacheService.Get<int>(cacheKey, idPortal);

            if (groupId > 0)
                return groupId;

            groupId = _productRecoverAndPersist.GetProductGroupIdByProductId(idPortal, idProduct);

            if (groupId > 0)
            {
                _tempCacheService.Add(cacheKey, groupId, TimeSpan.FromMinutes(5), idPortal);
            }

            return groupId;
        }

        public int GetProductGroupIdByProductIdByRepoProduct(short idPortal, int idProduct)
        {
            var cacheKey = $"PRODUCT_GROUP_BYREPO_PRODUCTS_BY_PRODUCT_PORTAL_{idPortal}_ID_PRODUCT_{idProduct}";
            var groupId = _tempCacheService.Get<int>(cacheKey, idPortal);

            if (groupId > 0)
                return groupId;

            groupId = _productRecoverAndPersist.GetProductGroupIdByProductIdByRepoProduct(idPortal, idProduct);

            if (groupId > 0)
            {
                _tempCacheService.Add(cacheKey, groupId, TimeSpan.FromMinutes(5), idPortal);
            }

            return groupId;
        }

        public List<ProductEntity> GetWelcomePacks(short portalId)
        {
            return _productRecoverAndPersist.GetWelcomePacks(portalId);
        }

        private string GetKey(int productId, short portalId, int ambitId)
        {
            return $"{KEY_CACHE_INITIAL_VALUES_FEATURE}{portalId}_PRODUCTID_{productId}_AMBITID_{ambitId}";
        }

        private DateTime GetMaxDateProductsMerge(IList<ProductEntity> productsMerge)
        {
            var promotions = productsMerge.SelectMany(b => b.Promotions).Distinct();

            if (promotions != null && promotions.Count() > 0)
                return promotions.Max(d => d.DateMod);
            else
                return DateTime.MinValue;
        }

        private bool CheckHasPromotion(SegmentCompanyEntity segmentCompany, PromotionEntity promotion)
        {
            return
               (ContainSegmentEqual(segmentCompany.CompanyId, promotion.Segments.CompanyIds)
               && ContainSegmentEqual(segmentCompany.EmploymentNumberId, promotion.Segments.EmploymentNumberIds)
               && ContainSegmentEqual(segmentCompany.LocationId, promotion.Segments.LocationIds)
               && ContainSegmentEqual(segmentCompany.SectorId, promotion.Segments.SectorIds)
               && (promotion.Segments.GetterComercialAsigned || (!promotion.Segments.GetterComercialAsigned && !segmentCompany.ComercialAsigned))
               && (segmentCompany.CretedonCompany <= promotion.Segments.CretedonCompanyFrom || promotion.Segments.CretedonCompanyFrom == DateTime.MinValue)
               && (segmentCompany.LastDateCompanyProduct <= promotion.Segments.LastDateCompanyProductFrom || promotion.Segments.LastDateCompanyProductFrom == DateTime.MinValue)
               && (segmentCompany.LastLogin <= promotion.Segments.LastLoginFrom || promotion.Segments.LastLoginFrom == DateTime.MinValue)
               && (segmentCompany.LastDateCompanyActivityCrm <= promotion.Segments.LastDateActivityCrm || promotion.Segments.LastDateActivityCrm == DateTime.MinValue)
               );
        }

        private bool ContainSegmentEqual(int number, List<int> list)
        {
            if (!list.Any())
                return true;

            return list.Exists(m => m == number);
        }

        public string GetProductDescriptionByProductId(short idPortal, int idProduct)
        {
            var keyCache = $"COMPANY_PRO_PRODUCT_NAME_{idPortal}_{idProduct}";
            string productComercialNameDescription;

            if (_tempCacheService.Get<string>(keyCache) != null)
                productComercialNameDescription = _tempCacheService.Get<string>(keyCache);
            else
            {
                productComercialNameDescription = _productRecoverAndPersist.GetProductDescriptionByProductId(idPortal, idProduct);
                _tempCacheService.Add(keyCache, productComercialNameDescription);
            }

            return productComercialNameDescription;
        }

        public List<ProductVoucherConfigEntity> GetProductVoucherConfigList(short portalId, short statusId)
        {
            var productVoucherConfigList = new List<ProductVoucherConfigEntity>();
            if (portalId > 0 && statusId > 0)
            {
                var keyCache = $"PRODUCT_VOUCHER_CONFIG_LIST_GET_PORTALID_{portalId}_STATUSID_{statusId}";

                productVoucherConfigList = _tempCacheService.Get<List<ProductVoucherConfigEntity>>(keyCache);

                if (productVoucherConfigList == null || !productVoucherConfigList.Any())
                {
                    productVoucherConfigList = _productRecoverAndPersist.GetProductVoucherConfig(portalId, statusId);
                    if (productVoucherConfigList.Any())
                        _tempCacheService.Add(keyCache, productVoucherConfigList, DateTime.Now.AddMinutes(10));
                }
            }

            return productVoucherConfigList;
        }

        public List<ConfigurationAutomaticPromotionEntity> GetConfigurationAutomaticPromotion(int productId, short portalId)
        {
            var result = new List<ConfigurationAutomaticPromotionEntity>();

            if (productId > 0
               && portalId > 0)
            {
                var cache = $"CONFIGURATION_AUTOMATIC_PROMOTION_PRODUCTID_{productId}_portalId_{portalId}";

                var configurationListAutomaticPromotion = _tempCacheService.Get<List<ConfigurationAutomaticPromotionEntity>>(cache);
                var goToBBDD = false;

                if (configurationListAutomaticPromotion != null
                    && configurationListAutomaticPromotion.Any()
                    && configurationListAutomaticPromotion.Exists(p => p.IdStatus == (short)StatusEnum.Active))
                {
                    var configurationAutomaticPromotion = configurationListAutomaticPromotion.FirstOrDefault(p => p.IdStatus == (short)StatusEnum.Active);
                    var maxDate = _productRecoverAndPersist.GetMaxDateModConfigurationPromotionByProductId(productId, portalId);

                    if (maxDate > configurationAutomaticPromotion.DateMod)
                        goToBBDD = true;
                }

                if (configurationListAutomaticPromotion == null || goToBBDD)
                    configurationListAutomaticPromotion = _productRecoverAndPersist.GetConfigurationByProductId(productId, portalId);


                if (configurationListAutomaticPromotion.Any()
                    && configurationListAutomaticPromotion.Exists(p => p.IdStatus == (short)StatusEnum.Active))
                {
                    _tempCacheService.Add(cache, configurationListAutomaticPromotion, new TimeSpan(5, 0, 0));

                    return configurationListAutomaticPromotion;
                }
            }

            return result;
        }

        public void CreateAutomaticPromotionByPack(int idCompany, int productId, short portalId)
        {
            if (idCompany > 0
                && productId > 0
                && portalId > 0)
            {
                var configurationPromotionsAutomatic = GetConfigurationAutomaticPromotion(productId, portalId);

                if (configurationPromotionsAutomatic.Any()
                    && configurationPromotionsAutomatic.Exists(p => p.IdStatus == (short)StatusEnum.Active))
                    Add(GeneratePromotionByPack(idCompany, configurationPromotionsAutomatic.FirstOrDefault(p => p.IdStatus == (short)StatusEnum.Active)));
            }
        }

        public string GetProductDescriptionByGroupAndSubGroup(short groupId, short subGroupId, short portalId)
        {
            if (groupId > 0
                && subGroupId > 0
                && portalId > 0)
            {
                var cacheName = $"NAMEHEADER_SUBGROUP_{subGroupId}_portalId_{portalId}_GROUPID_{groupId}";

                var cache = _tempCacheService.Get<string>(cacheName);

                if (cache != null)
                    return cache;

                var nameSubGroup = _productRecoverAndPersist.GetProductDescriptionByGroupAndSubGroup(groupId, subGroupId, portalId);

                if (!string.IsNullOrEmpty(nameSubGroup) && nameSubGroup.Length > 1)
                {
                    nameSubGroup = $"{nameSubGroup[0]}{nameSubGroup.Substring(1, nameSubGroup.Length - 1).ToLower()}";
                    _tempCacheService.Add(cacheName, nameSubGroup);
                    return nameSubGroup;
                }
            }

            return string.Empty;
        }

        private PromotionEntity GenerateVoucherPromotion(ProductEntity product, ProductVoucherConfigEntity productVoucherConfig, PortalConfig portalConfig, int initialUnits, int companyId)
        {
            double numericPrice = product.Price - (product.Price * productVoucherConfig.Discount / 100);
            decimal.TryParse(product.Price.ToString(), out var moneyOld);
            decimal.TryParse(numericPrice.ToString(), out var money);

            var promotionEntity = new PromotionEntity()
            {
                NumericPrice = product.Price - (product.Price * productVoucherConfig.Discount / 100),
                LiteralTotalSaveUpPrice = SetCurrency(((decimal)product.Price * productVoucherConfig.Discount / 100), portalConfig),
                ProductId = product.Id,
                DateAdd = DateTime.Now,
                DateStart = DateTime.Now,
                DateEnd = DateTime.Now.AddDays(portalConfig.AEPortalConfig.DaysPromotion),
                ApplyToCrmCompanies = false,
                DateMod = DateTime.Now,
                LiteralPercentDiscount = $"{productVoucherConfig.Discount}%",
                LiteralOldPrice = SetCurrency(moneyOld, portalConfig),
                LiteralTotalPrice = SetCurrency(money / initialUnits, portalConfig),
                LiteralPriceUnity = $"{SetCurrency(money / initialUnits, portalConfig)} <span>/ oferta</span>",
                Preferent = (short)PriorityEnum.URGENT,
                PortalId = (short)productVoucherConfig.PortalId,
                PromotionName = product.ComercialName,
                StatusId = (short)PromotionStatusEnum.Active,
                TemporalityUnitId = (short)TemporalityUnit.OFFERT,
                TemporalityId = (short)TemporalityEnum.Year,
                TypePromotion = (short)PromotionTypeEnum.VoucherPacks,
            };

            promotionEntity.Segments.CompanyIds.Add(companyId);

            return promotionEntity;
        }

        private PromotionEntity GeneratePromotionByPack(int idCompany, ConfigurationAutomaticPromotionEntity configurationAutomaticPromotion)
        {
            var productList = GetProductsByIdsProducts(configurationAutomaticPromotion.IdPortal, configurationAutomaticPromotion.IdProductNew.ToString());
            var result = new PromotionEntity();

            if (productList.Any())
            {
                var product = productList.FirstOrDefault();
                var initialUnits = GetInitialUnitsByAmbit(product.Id, configurationAutomaticPromotion.IdPortal, (short)ProductAmbitEnum.Offer);

                if (product.Price > 0
                    && initialUnits > 0)
                {
                    var portalConfig = _portalConfigurationService.GetPortalConfiguration();

                    result.NumericPrice = product.Price - (product.Price * configurationAutomaticPromotion.Discount / 100);
                    result.LiteralTotalSaveUpPrice = (product.Price * configurationAutomaticPromotion.Discount / 100).ToString();
                    result.ProductId = product.Id;
                    result.DateAdd = DateTime.Now;
                    result.DateStart = DateTime.Now;
                    result.DateEnd = DateTime.Now.AddDays(configurationAutomaticPromotion.DaysPromo);
                    result.ApplyToCrmCompanies = false;
                    result.DateMod = DateTime.Now;
                    result.LiteralPercentDiscount = $"{configurationAutomaticPromotion.Discount}%";
                    decimal.TryParse(product.Price.ToString(), out var moneyOld);
                    decimal.TryParse(result.NumericPrice.ToString(), out var money);
                    result.LiteralOldPrice = SetCurrency(moneyOld, portalConfig);
                    result.LiteralTotalPrice = SetCurrency(money / initialUnits, portalConfig);
                    result.LiteralPriceUnity = $"{((double)money / initialUnits)} <span>/ oferta</span>";
                    result.Preferent = (short)PriorityEnum.URGENT;
                    result.PortalId = (short)configurationAutomaticPromotion.IdPortal;
                    result.ProductId = product.Id;
                    result.PromotionName = product.ComercialName;
                    result.StatusId = (short)PromotionStatusEnum.Active;
                    result.TemporalityUnitId = (short)TemporalityUnit.OFFERT;
                    result.TemporalityId = (short)TemporalityEnum.Year;
                    result.TypePromotion = (short)PromotionTypeEnum.AutomaticPacks;
                    result.Segments.CompanyIds.Add(idCompany);
                    result.ComercialNotImportant = configurationAutomaticPromotion.ComercialNotImportant;
                }
            }
            return result;
        }

        public void ExpiredAutomaticPromotions(short idPortal, int idCompany)
        {
            if (idPortal > 0
                && idCompany > 0
                && _productRecoverAndPersist.ExpiredAutomaticPromotions(idPortal, idCompany))
                RemoveCachePromotions(idPortal, idCompany);
        }

        public bool HasBasicService(short portalId)
        {
            if (portalId > 0)
            {
                var cacheName = $"HASBASICSERVICE_portalId_{portalId}";

                var cache = _tempCacheService.Get<bool?>(cacheName);

                if (cache != null)
                    return (bool)cache;

                var result = _productRecoverAndPersist.HasBasicService(portalId);

                _tempCacheService.Add(cacheName, result, new TimeSpan(5, 0, 0));

                return result;
            }

            return false;
        }

        private void RemoveCachePromotions(short idPortal, int idCompany)
        {
            var keyList = new List<string>
            {
                $"{KEY_CACHE_PRODUCTS}{idPortal}_{(short) PageEnum.HomePrivada}",
                $"{KEY_CACHE_PRODUCTS}{idPortal}_{(short) PageEnum.MEMBRESIASNUEVASLANDING}",
                $"{KEY_CACHE_PRODUCTS}{idPortal}_{(short) PageEnum.COMPARATIVANUEVALANDING}",
                $"{KEY_CACHE_PRODUCTS}{idPortal}_{(int) PageEnum.PackCart}",
                $"{KEY_CACHE_PRODUCTS}{idPortal}_{(int) PageEnum.Cart}",
                $"{KEY_CACHE_PRODUCTS}{idPortal}_{(int) PageEnum.MembershipCart}",
                $"{KEY_CACHE_PROMOTIONS}_AUTOMATICPACKS{idPortal}_IDCOMPANY_{idCompany}"
            };

            foreach (var item in keyList)
                _tempCacheService.Remove(item);
        }

        public List<int> GetLimitationProductsByIdProduct(int productId, short portalId)
        {
            var cacheKey = $"GetLimitationProductsByIdProduct_{productId}_Portal_{portalId}";

            var cache = _tempCacheService.Get<List<int>>(cacheKey);

            if (cache != null)
                return cache;

            var arrayProducts = _productRecoverAndPersist.GetLimitationProductsByIdProduct(productId, portalId);
            var result = new List<int>();

            if (arrayProducts.Any())
                result = GetItemsForbiddenByArray(arrayProducts);

            _tempCacheService.Add(cacheKey, result, new TimeSpan(0, 15, 0));

            return result;
        }

        private static List<int> GetItemsForbiddenByArray(List<LimitationProductPurchaseEntity> arrayProducts)
        {
            List<int> result = new List<int>();

            foreach (var product in arrayProducts)
            {
                if (!string.IsNullOrEmpty(product.IdForbiddenProducts))
                {
                    if (product.IdForbiddenProducts.Contains(','))
                    {
                        var itemsForbidden = product.IdForbiddenProducts.Split(',');

                        foreach (var item in itemsForbidden)
                        {
                            int.TryParse(item, out var newAdd);

                            if (newAdd > 0)
                                result.Add(newAdd);
                        }
                    }
                    else
                    {
                        int.TryParse(product.IdForbiddenProducts, out var newAdd);

                        if (newAdd > 0)
                            result.Add(newAdd);
                    }
                }
            }

            return result;
        }

        public ProductSearchSpecifications GetProductSearchByPage(short portalId, PageEnum pageEnum, int idCompany, bool loadPromotions)
        {
            return new ProductSearchSpecifications(portalId)
            {
                PageId = (int)pageEnum,
                CompanyId = idCompany,
                LoadPromotions = loadPromotions
            };
        }


        public short ReturnFeatureOfferPopUp(CompanyProductEntity companyProductEntity, string prodCWFeature, short ambitFeature)
        {
            if (companyProductEntity.GroupId == (short)ProductGroupsEnum.Membership)
            {
                return (short)OfferConvertToCompleteEnum.ShowContactPopUp;
            }
            var hasFeatureAvailableUnits = companyProductEntity.GroupId == (short)ProductGroupsEnum.Packs
                                                && companyProductEntity.Features.Exists(f => f.AmbitId == ambitFeature)
                                                && (companyProductEntity.Features.FirstOrDefault(f => f.AmbitId == ambitFeature)?.AvailableUnits ?? 0) > 0;

            if (hasFeatureAvailableUnits)
            {
                return (short)CWFeaturePopUpEnum.HaveUnits;
            }
            else
            {
                if (!string.IsNullOrEmpty(prodCWFeature))
                {
                    return (short)OfferConvertToCompleteEnum.GoToMultiPackCart;
                }
                return (short)OfferConvertToCompleteEnum.ShowContactPopUp;
            }
        }

        public List<ProductEntity> GetAllProductsByPage(ProductSearchSpecifications productSpecifications)
        {
            try
            {
                var keyCache = $"{"GetAllProductsByPage"}_{productSpecifications.PortalId}_{productSpecifications.PageId}_{productSpecifications.CompanyId}";

                List<ProductEntity> products = new List<ProductEntity>();

                products = _tempCacheService.Get<List<ProductEntity>>(keyCache);

                if (products != null && products.Count > 0)
                {
                    return products;
                }
                else
                {
                    products = _productRecoverAndPersist.GetAllProductsByPage(productSpecifications);
                    products.ForEach(c => c.EncryptedId = _encryptionService.Encrypt(c.Id.ToString()));

                    if (products.Any() && productSpecifications.LoadPromotions)
                    {
                        var promotions = new List<PromotionEntity>();
                        if (_portalConfigurationService.GetPortalConfiguration().AEPortalConfig.DaysPromotion > 0)
                        {
                            promotions = GetPromotionsByType(new PromotionSearchSpecifications(productSpecifications.PortalId)
                            {
                                StatusId = (int)PromotionStatusEnum.Active,
                                CompanyId = productSpecifications.CompanyId,
                                TypePromotion = (short)PromotionTypeEnum.VoucherPacks
                            });
                        }

                        if (!promotions.Any())
                        {
                            promotions = GetAllPromotions(new PromotionSearchSpecifications(productSpecifications.PortalId)
                            {
                                StatusId = (int)PromotionStatusEnum.Active,
                                CompanyId = productSpecifications.CompanyId
                            });
                        }

                        if (promotions.Count > 0)
                        {
                            products.ForEach(product =>
                            {
                                var promotion = promotions.Where(promo => promo.ProductId == product.Id).ToList();
                                if (promotion.Any(p => p.TypePromotion != (short)PromotionTypeEnum.VoucherPacks))
                                {
                                    product.Promotions = promotions.Where(promo => promo.ProductId == product.Id).ToList();
                                }
                            });
                        }
                    }
                }
                if (products.Count > 0)
                    _tempCacheService.Add(keyCache, products, DateTime.Now.AddMinutes(15));

                return products;
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "ProductService", "GetAllProductsByPage");
                return new List<ProductEntity>();
            }
        }

        public PromotionEntity GetPromotionById(short portalId, int promotionId)
        {
            return _productRecoverAndPersist.GetPromotionById(portalId, promotionId);
        }

        public List<CustomOfferConditionsEntity> GetPromoOfferConditions(short idPortal)
        {
            string cacheKey = $"PROMO_OFFER_CONDITIONS_{idPortal}";
            var promoOfferConditions = _tempCacheService.Get<List<CustomOfferConditionsEntity>>(cacheKey);

            if (promoOfferConditions != null)
                return promoOfferConditions;

            promoOfferConditions = _productRecoverAndPersist.GetPromoOfferConditions(idPortal);

            if (promoOfferConditions != null)
                _tempCacheService.Add(cacheKey, promoOfferConditions, DateTime.Now.AddMinutes(15), idPortal);

            return promoOfferConditions;
        }

        public short GetSubGroupIdByProduct(int idProduct, PortalConfig portalConfig)
        {
            var product = GetProducts(new ProductSearchSpecifications(portalConfig.PortalId) { Id = idProduct, TemporalityId = (short)TemporalityEnum.Year }).FirstOrDefault(x => x.Id == idProduct);
            return product != null ? product.SubGroupId : (short)0;
        }

        public bool ProductIfExists(int idProduct, short idPortal)
        {
            return _productRecoverAndPersist.ProductIfExists(idProduct, idPortal);
        }

        public List<ProductEntity> GetCourtesyProducts(short idPortal, int idAmbit, int idSubGroup)
        {
            return _productRecoverAndPersist.GetCourtesyProducts(idPortal, idAmbit, idSubGroup);
        }

        public async Task<List<ProProductPortalDTO>> GetProProductPortalNoObsoleteMainInfoByPortalId(short portalId)
        {
            if (portalId <= 0) return new List<ProProductPortalDTO>();

            var result = await _productRecoverAndPersist.GetProProductPortalNoObsoleteMainInfoByPortalId(portalId);

            if (result != null)
            {
                return result.ToProProductPortalDTOList();
            }

            return null;
        }

        public async Task<List<ProProductPortalFeatureDTO>> GetProProductPortalFeatureMainInfoByProductIdAndPortalIdAsync(int productId, short portalId)
        {
            if (portalId <= 0 || productId <= 0) return new List<ProProductPortalFeatureDTO>();

            var result = await _productRecoverAndPersist.GetProProductPortalFeatureMainInfoByProductIdAndPortalIdAsync(productId, portalId);

            if (result != null)
            {
                return result.ToProProductPortalFeatureDTOList();
            }

            return null;
        }

        public async Task<ProProductPortalDTO> GetProductByIdProductAsync(int productId, short portalId)
        {
            if (portalId <= 0 || productId <= 0) return new ProProductPortalDTO();

            var result = await _productRecoverAndPersist.GetProductByIdProductAsync(productId, portalId);

            if (result != null)
            {
                return result.ToProProductPortalDTO();
            }

            return null;
        }

        public async Task<List<ProProductPortalDTO>> GetProProductPortalListByPortalNoIncludeObsolete(short portalId)
        {
            if (portalId <= 0) return new List<ProProductPortalDTO>();

            var result = await _productRecoverAndPersist.GetProProductPortalNoObsoleteMainInfoByPortalId(portalId);

            if (result != null)
            {
                return result.ToProProductPortalDTOList();
            }

            return null;
        }

        public async Task<List<ProProductPortalDTO>> GetProProductPortalListByPortalIncludeObsolete(short portalId)
        {
            if (portalId <= 0) return new List<ProProductPortalDTO>();

            var result = await _productRecoverAndPersist.GetProProductPortalListByPortalIncludeObsolete(portalId);

            if (result != null)
            {
                return result.ToProProductPortalDTOList();
            }

            return null;
        }

        public async Task<Dictionary<int, string>> GetCommercialProductNamesByProductIdsAndPortalIdAsync(List<int> productIds, short portalId)
        {
            if (portalId <= 0 || productIds == null || !productIds.Any()) return new Dictionary<int, string>();

            return await _productRecoverAndPersist.GetCommercialProductNamesByProductIdsAndPortalIdAsync(portalId, string.Join(",", productIds));
        }

        public async Task<List<ProProductPortalPriceDTO>> GetProProductPricesByProductIdAndPortalIdAsync(short portalId, int productId)
        {
            if (portalId <= 0 || productId <= 0) return new List<ProProductPortalPriceDTO>();

            return (await _productRecoverAndPersist.GetProProductPricesByProductIdAndPortalIdAsync(portalId, productId))
                ?.ToProProductPortalPriceDTOList() ?? null;
        }

        public async Task<List<ProductPageDTO>> GetProProductPagesByProductIdAndPortalIdAsync(short portalId, int productId)
        {
            if (portalId <= 0 || productId <= 0) return new List<ProductPageDTO>();

            return (await _productRecoverAndPersist.GetProProductPagesByProductIdAndPortalIdAsync(portalId, productId))
                ?.ToProductPageDTOList() ?? null;
        }

        public async Task<bool> RemoveProProductPricesByProductIdAndPortalIdAsync(RemoveProProductPortalPriceDTO model)
        {
            if (model.PortalId <= 0 || model.ProductId <= 0) return false;

            return await _productRecoverAndPersist.RemoveProProductPricesByProductIdAndPortalIdAsync(new RemoveProProductPortalPricePersitentLayerDTO(model.PortalId, model.ProductId, model.TemporalityId));
        }

        public async Task<ProProductPortalPriceDTO> GetProProductPricesByTemporalityIdAsync(GetProProductPortalPriceDTO model)
        {
            if (model.PortalId <= 0 || model.ProductId <= 0 || model.TemporalityId <= 0) return new ProProductPortalPriceDTO();

            return (await _productRecoverAndPersist.GetProProductPricesByTemporalityIdAsync(new GetProProductPortalPricePersistentLayerDTO(model.TemporalityId, model.PortalId, model.ProductId)))
                ?.ToProProductPortalPriceDTO() ?? null;
        }

        public async Task<bool> SaveProProductPriceAsync(ProProductPortalPriceDTO dto)
        {
            if (dto == null || dto.ProductId <= 0 || dto.TemporalityId <= 0 || dto.PortalId <= 0)
                return false;

            return await _productRecoverAndPersist.SaveProProductPriceAsync(new ProProductPortalPricePersistentLayerDTO(dto.TemporalityId, dto.PortalId, dto.ProductId, dto.ExpirationDays,
                                                                                dto.Price, dto.Discount, dto.LiteralShowPrice, dto.MonthPrice, dto.LiteralShowTotal, dto.LiteralShowUnit, dto.StatusId,
                                                                                dto.RenewalPrice, dto.Saving, dto.SelfRenewing));
        }

        public async Task<List<ProProductPortalDTO>> GetAllProductsAvailablesByCRMAsync(short portalId)
        {
            if (portalId <= 0)
                return new List<ProProductPortalDTO>();

            return (await _productRecoverAndPersist.GetAllProductsAvailablesByCRMAsync(portalId))?.ToProProductPortalDTOList() ?? null;
        }

        public async Task<List<int>> GetSuperiorProductIdsByProductIdAndPortalIdAsync(int productId, short portalId)
        {
            if (productId <= 0 || portalId <= 0)
                return new List<int>();

            return await _productRecoverAndPersist.GetSuperiorProductIdsByProductIdAndPortalIdAsync(productId, portalId);
        }

        public async Task<bool> AddProProductSuperior(SaveProProductSuperiorDTO dto)
        {
            if (dto.ProductSuperiorId <= 0 || dto.ProductActiveId <= 0 || dto.PortalId <= 0)
                return false;

            return await _productRecoverAndPersist.AddProProductSuperior(new SaveProProductSuperiorPersistentLayerDTO(dto.ProductSuperiorId, dto.ProductActiveId, dto.PortalId));
        }

        public async Task<bool> RemoveProProductSuperior(SaveProProductSuperiorDTO dto)
        {
            if (dto.ProductSuperiorId <= 0 || dto.ProductActiveId <= 0 || dto.PortalId <= 0)
                return false;

            return await _productRecoverAndPersist.RemoveProProductSuperior(new SaveProProductSuperiorPersistentLayerDTO(dto.ProductSuperiorId, dto.ProductActiveId, dto.PortalId));
        }

        public async Task<bool> AddFeatureAsync(ProProductPortalFeatureDTO dto)
        {
            if (dto?.PortalId <= 0) return false;

            return await _productRecoverAndPersist.AddFeature(dto.ToProProductPortalFeaturePersistenLayerDTO());
        }

        public async Task<bool> UpdateFeatureAsync(ProProductPortalFeatureDTO dto)
        {
            if (dto?.PortalId <= 0) return false;

            return await _productRecoverAndPersist.UpdateFeature(dto.ToProProductPortalFeaturePersistenLayerDTO());
        }

        public async Task<bool> DeleteFeatureAsync(ProProductPortalFeatureDTO dto)
        {
            if (dto?.PortalId <= 0) return false;

            return await _productRecoverAndPersist.DeleteFeature(dto.ToProProductPortalFeaturePersistenLayerDTO());
        }

        public async Task<bool> SetOfferMultiplierAsync(SetOfferMultiplierDTO dto)
        {
            if (dto?.PortalId <= 0 || dto?.ProductId <= 0) return false;

            return await _productRecoverAndPersist.SetOfferMultiplier(new SetOfferMultiplierPersistentLayerDTO(dto.PortalId, dto.ProductId, dto.LimitationMultiplier));
        }

        public async Task<List<ProProductPortalDTO>> GetDefinitionsProductsShorter(short portalId)
        {
            if (portalId <= 0)
                return new List<ProProductPortalDTO>();

            return (await _productRecoverAndPersist.GetDefinitionsProductsShorter(portalId))?.ToProProductPortalDTOList() ?? null;
        }

        public async Task<int> AddProProductAsync(ProProductPortalDTO dto)
        {
            if (dto != null
                && dto.PortalId > 0
                && !string.IsNullOrEmpty(dto.ComercialName))
            {
                var productPersistentLayerDTO = dto.ToProProductPortalPersistentLayerDTO();
                productPersistentLayerDTO.ProductId = await _productRecoverAndPersist.AddProProductAsync(productPersistentLayerDTO);

                if (productPersistentLayerDTO.ProductId > 0
                    && await _productRecoverAndPersist.AddProProductPortalAsync(productPersistentLayerDTO))
                {
                    return productPersistentLayerDTO.ProductId;
                }
            }

            return 0;
        }
    }
}
