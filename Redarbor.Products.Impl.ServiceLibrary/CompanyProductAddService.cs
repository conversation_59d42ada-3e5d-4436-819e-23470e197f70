using Redarbor.Common.Entities.Configuration;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Core.Resolver.Contracts.ServiceLibrary;
using Redarbor.Core.Stack.Contracts.ServiceLibrary;
using Redarbor.Master.Entities.Enums;
using Redarbor.Master.Entities.Product;
using Redarbor.Master.Entities.PurchaseOperation;
using Redarbor.Products.Contracts.ServiceLibrary;
using Redarbor.Products.Contracts.ServiceLibrary.DTO;
using Redarbor.Products.Library.DomainServiceContracts;
using Redarbor.Products.Library.DTOs;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Redarbor.Products.Impl.ServiceLibrary
{
    public class CompanyProductAddService : ICompanyProductAddService
    {
        private readonly IProductService _productService;
        private readonly ICompanyProductRecoverAndPersist _companyProductRecoverAndPersist;
        private readonly IClientIpAddressResolverService _clientIpAddressResolverService;
        private readonly IExceptionPublisherService _exceptionPublisherService;
        private readonly IStackService _stackService;
        const int MINUTES_TO_PREVENT_DESYNCHRONIZE = -10;

        public CompanyProductAddService(IProductService productService,
            ICompanyProductRecoverAndPersist companyProductRecoverAndPersist,
            IClientIpAddressResolverService clientIpAddressResolverService,
            IStackService stackService,
            IExceptionPublisherService exceptionPublisherService)
        {
            _productService = productService;
            _companyProductRecoverAndPersist = companyProductRecoverAndPersist;
            _clientIpAddressResolverService = clientIpAddressResolverService;
            _stackService = stackService;
            _exceptionPublisherService = exceptionPublisherService;
        }

        public bool AddWelcomePack(int companyId, int userId, PortalConfig portalConfig)
        {
            var defaultProducts = _productService.GetWelcomePacks(portalConfig.PortalId);

            if (defaultProducts.Any())
                defaultProducts.ForEach(product =>
                {
                    AddProduct(
                        portalConfig,
                        product,
                        companyId,
                        (int)PaymentOriginEnum.Free,
                        userId,
                        (int)ProductSourceEnum.Web);
                });

            return true;
        }

        public int CreatePromoOfferProduct(int idCompany, PortalConfig portalConfig, long idUser)
        {
            var product = _productService.GetCustomOfferProduct(portalConfig.PortalId);

            if (product is null || product.Id == 0 || product.GroupId == 0) return 0;

            return AddProduct(portalConfig, product, idCompany, (short)PaymentOriginEnum.Free, (int)idUser, (short)ProductSourceEnum.Web);
        }

        public int AddProduct(PortalConfig portalConfig, ProductEntity product, int companyId, short idOrigin, int userId, int sourceId)
        {
            var companyProductId = 0;
            var extraDays = 0;
            var activationDate = new DateTime();
            var expirationDate = new DateTime();

            if (product.GroupId == (short)ProductGroupsEnum.Freemium)
            {
                activationDate = portalConfig.CurrentDateTimePortal;
                expirationDate = new DateTime(5000, 01, 01);
            }
            else
            {
                extraDays = _companyProductRecoverAndPersist.GetDaysToExpire(companyId, product.Id, portalConfig.PortalId);
                if (extraDays != 0)
                {
                    activationDate = portalConfig.CurrentDateTimePortal.AddDays(extraDays - 1);
                    expirationDate = portalConfig.CurrentDateTimePortal.AddDays(product.ExpirationDays + extraDays + 1);
                }
                else
                {
                    activationDate = portalConfig.CurrentDateTimePortal.AddMinutes(MINUTES_TO_PREVENT_DESYNCHRONIZE);
                    expirationDate = portalConfig.CurrentDateTimePortal.AddDays(product.ExpirationDays + 1);
                }
            }

            companyProductId = _companyProductRecoverAndPersist.AddProduct(companyId, userId, portalConfig.PortalId,
                                                                           product.Id, idOrigin, sourceId, product.Price,
                                                                           activationDate, expirationDate, _clientIpAddressResolverService.GetIpAddress(), product.TemporalityId);

            if (companyProductId <= 0) return companyProductId;

            if (product.Price > 0)
                _companyProductRecoverAndPersist.UpdateIsPayment(companyId, portalConfig.PortalId);

            if (product.GroupId == (int)ProductGroupsEnum.Membership)
            {
                TraceWarningIfGroupIdIsZero(product, companyId);

                _companyProductRecoverAndPersist.UpdateOfferCompanyProductId(portalConfig.PortalId, product.Id,
                    companyId, companyProductId, (short)OfferIntegratorEnum.CompuTrabajo, (short)product.GroupId, (short)ProductClassEnum.Membership);
            }

            return companyProductId;
        }

        private void TraceWarningIfGroupIdIsZero(ProductEntity product, int companyId)
        {
            if(product.GroupId == 0)
            {
                _exceptionPublisherService.PublishWarning(new Exception($@"El GroupId es 0 para agregar nuevo producto. CompanyId: {companyId},
                productId: {product.Id}, GroupId: {product.GroupId}"),"CompanyProductAddService", "TraceWarningIfGroupIdIsZero",null, (short)product.PortalId);
            }
        }

        public int ProduceProduct(PurchaseOperationEntity purchaseOperation, PortalConfig portalConfig, ProductEntity product)
        {
            int companyProductId = AddProduct(portalConfig,
                                                product,
                                                purchaseOperation.Idcompany,
                                                purchaseOperation.IdOrigin,
                                                purchaseOperation.Iduser,
                                                purchaseOperation.SourceId);
            if (companyProductId > 0)
            {
                purchaseOperation.Idproductcontract = companyProductId;
            }

            return companyProductId;
        }

        public async Task<int> AddCourtesyProductAsync(AddCourtesyProductDTO dto)
        {
            if (dto.ProductId <= 0 || dto.CompanyId <= 0 || dto.PortalId <= 0)
                return 0;

            return await _companyProductRecoverAndPersist.AddCourtesyProductAsync(new AddCourtesyProductPersistentLayerDTO(dto.CompanyId, dto.PortalId, dto.ProductId));
        }

        public async Task<bool> AddCourtesyProductFeaturesAsync(AddCourtesyProductFeatureDTO dto)
        {
            if (dto.ProductId <= 0 || dto.CompanyId <= 0 || dto.PortalId <= 0 || dto.CompanyProductId <= 0)
                return false;

            return await _companyProductRecoverAndPersist.AddCourtesyProductFeaturesAsync(new AddCourtesyProductFeaturePersistentLayerDTO(dto.CompanyProductId, dto.ProductId, dto.CompanyId, dto.PortalId));
        }
    }
}
