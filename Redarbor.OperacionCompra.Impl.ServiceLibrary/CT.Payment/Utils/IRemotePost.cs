// --------------------------------------------------------------------------------------------------------------------
// <copyright file="IRemotePost.cs" company="">
//   
// </copyright>
// <summary>
//   The RemotePost interface.
// </summary>
// --------------------------------------------------------------------------------------------------------------------

using System.Collections.Specialized;

namespace Redarbor.PurchaseOperation.Impl.ServiceLibrary.CT.Payment.Utils
{
    /// <summary>
    /// The RemotePost interface.
    /// </summary>
    public interface IRemotePost
    {
        /// <summary>
        /// Gets or sets the input values.
        /// </summary>
        NameValueCollection InputValues { get; set; }

        /// <summary>
        /// Gets or sets the method.
        /// </summary>
        FormMethod Method { get; set; }

        /// <summary>
        /// Gets or sets the url.
        /// </summary>
        string Url { get; set; }

        /// <summary>
        /// The add input.
        /// </summary>
        /// <param name="name">
        /// The name.
        /// </param>
        /// <param name="value">
        /// The value.
        /// </param>
        void AddInput(string name, object value);

        /// <summary>
        /// The post.
        /// </summary>
        /// <param name="formName">
        /// The form name.
        /// </param>
        void Post(string formName);
    }
}