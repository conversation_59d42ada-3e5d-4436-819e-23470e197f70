<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props" Condition="Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{526E0D10-38E3-4DBB-8B6A-9630922F7A23}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Redarbor.Company.WebAPI</RootNamespace>
    <AssemblyName>Redarbor.Company.WebAPI</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <MvcBuildViews>false</MvcBuildViews>
    <UseIISExpress>true</UseIISExpress>
    <Use64BitIISExpress>
    </Use64BitIISExpress>
    <IISExpressSSLPort>44335</IISExpressSSLPort>
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Autofac, Version=*******, Culture=neutral, PublicKeyToken=17863af14b0044da, processorArchitecture=MSIL">
      <HintPath>..\packages\Autofac.4.8.0\lib\net45\Autofac.dll</HintPath>
    </Reference>
    <Reference Include="Autofac.Integration.WebApi, Version=4.1.0.0, Culture=neutral, PublicKeyToken=17863af14b0044da, processorArchitecture=MSIL">
      <HintPath>..\packages\Autofac.WebApi2.4.1.0\lib\net45\Autofac.Integration.WebApi.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Http.Abstractions, Version=2.2.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Http.Abstractions.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Http.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Http.Features, Version=2.2.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Http.Features.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Http.Features.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=8.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.8.0.0\lib\net462\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.Extensions.Configuration, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Configuration.3.1.6\lib\netstandard2.0\Microsoft.Extensions.Configuration.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Configuration.Abstractions.5.0.0\lib\net461\Microsoft.Extensions.Configuration.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration.Binder, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Configuration.Binder.3.1.6\lib\netstandard2.0\Microsoft.Extensions.Configuration.Binder.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection, Version=8.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.DependencyInjection.8.0.0\lib\net462\Microsoft.Extensions.DependencyInjection.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection.Abstractions, Version=8.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.DependencyInjection.Abstractions.8.0.0\lib\net462\Microsoft.Extensions.DependencyInjection.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.FileProviders.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.FileProviders.Abstractions.5.0.0\lib\net461\Microsoft.Extensions.FileProviders.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Hosting.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Hosting.Abstractions.5.0.0\lib\net461\Microsoft.Extensions.Hosting.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Logging.Abstractions.3.1.6\lib\netstandard2.0\Microsoft.Extensions.Logging.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.ObjectPool, Version=********, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.ObjectPool.5.0.11\lib\net461\Microsoft.Extensions.ObjectPool.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Primitives, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Primitives.5.0.0\lib\net461\Microsoft.Extensions.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="RabbitMQ.Client, Version=6.0.0.0, Culture=neutral, PublicKeyToken=89e7d7c5feba84ce, processorArchitecture=MSIL">
      <HintPath>..\packages\RabbitMQ.Client.6.2.2\lib\net461\RabbitMQ.Client.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Api.Contracts, Version=3.0.4.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Api.Contracts.3.0.4\lib\netstandard2.0\Redarbor.Api.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Api.Impl, Version=3.1.4.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Api.Impl.3.1.4\lib\netstandard2.0\Redarbor.Api.Impl.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.AuditApiRequests.Common.Library, Version=2.0.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.AuditApiRequests.Common.Library.2.0.1\lib\netstandard2.0\Redarbor.AuditApiRequests.Common.Library.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.AuditApiRequests.NetFramework.Library, Version=2.0.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.AuditApiRequests.NetFramework.Library.2.0.1\lib\net461\Redarbor.AuditApiRequests.NetFramework.Library.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Cache, Version=1.3.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Cache.2.0.2\lib\netstandard2.0\Redarbor.Cache.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Cache.Disk, Version=1.3.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Cache.Disk.2.0.1\lib\netstandard2.0\Redarbor.Cache.Disk.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Cache.Runtime, Version=1.3.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Cache.Runtime.2.0.1\lib\net461\Redarbor.Cache.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Dictionaries.Consumer, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Dictionaries.Consumer.4.6.2\lib\netstandard2.0\Redarbor.Dictionaries.Consumer.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Encryption.Contracts, Version=1.0.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Encryption.Contracts.1.0.1\lib\netstandard2.0\Redarbor.Encryption.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Encryption.Impl, Version=1.0.2.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Encryption.Impl.1.0.2\lib\netstandard2.0\Redarbor.Encryption.Impl.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Extensions.NetStandard, Version=1.0.7.1, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Extensions.NetStandard.1.0.7.1\lib\netstandard2.0\Redarbor.Extensions.NetStandard.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.GlobalConfiguration.Library, Version=1.1.3.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.GlobalConfiguration.Library.1.1.3\lib\netstandard2.0\Redarbor.GlobalConfiguration.Library.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.KillerQuestions.Consumer, Version=1.0.3.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.KillerQuestions.Consumer.1.0.3\lib\netstandard2.0\Redarbor.KillerQuestions.Consumer.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Kpi.Consumer, Version=3.2.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Kpi.Consumer.3.2.0\lib\netstandard2.0\Redarbor.Kpi.Consumer.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Kpi.Consumer.Abstractions, Version=3.2.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Kpi.Consumer.Abstractions.3.2.1\lib\netstandard2.0\Redarbor.Kpi.Consumer.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Payments.Consumer, Version=4.4.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Payments.Consumer.4.4.1\lib\netstandard2.0\Redarbor.Payments.Consumer.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.RabbitMQ, Version=1.0.0.17, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.RabbitMQ.1.0.0.17\lib\netstandard2.0\Redarbor.RabbitMQ.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.RabbitMQ.Abstractions, Version=1.0.0.17, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.RabbitMQ.Abstractions.1.0.0.17\lib\netstandard2.0\Redarbor.RabbitMQ.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.RabbitMQ.Extensions, Version=1.0.0.17, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.RabbitMQ.Extensions.1.0.0.17\lib\netstandard2.0\Redarbor.RabbitMQ.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.RabbitMQ.Extensions.Autofac, Version=1.0.0.16, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.RabbitMQ.Extensions.Autofac.1.0.0.16\lib\netstandard2.0\Redarbor.RabbitMQ.Extensions.Autofac.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.RabbitMQ.Model, Version=1.0.0.17, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.RabbitMQ.Model.1.0.0.17\lib\netstandard2.0\Redarbor.RabbitMQ.Model.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.RegularExpressions.Constants, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.RegularExpressions.Constants.1.2.2\lib\netstandard2.0\Redarbor.RegularExpressions.Constants.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Timeline.Company, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Timeline.Company.1.1.1\lib\netstandard2.0\Redarbor.Timeline.Company.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Timeline.Consumer.Abstractions, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Timeline.Consumer.Abstractions.2.1.1\lib\netstandard2.0\Redarbor.Timeline.Consumer.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Tools.Exceptions.Consumer, Version=2.1.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Tools.Exceptions.Consumer.2.1.0\lib\netstandard2.0\Redarbor.Tools.Exceptions.Consumer.dll</HintPath>
    </Reference>
    <Reference Include="RestSharp, Version=106.8.10.0, Culture=neutral, PublicKeyToken=598062e77f915f75, processorArchitecture=MSIL">
      <HintPath>..\packages\RestSharp.106.11.5\lib\net452\RestSharp.dll</HintPath>
    </Reference>
    <Reference Include="Swashbuckle.Core, Version=*******, Culture=neutral, PublicKeyToken=cd1bb07a5ac7c7bc, processorArchitecture=MSIL">
      <HintPath>..\packages\Swashbuckle.Core.5.6.0\lib\net40\Swashbuckle.Core.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Annotations, Version=4.2.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ComponentModel.Annotations.4.7.0\lib\net461\System.ComponentModel.Annotations.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.Configuration.ConfigurationManager, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Configuration.ConfigurationManager.4.7.0\lib\net461\System.Configuration.ConfigurationManager.dll</HintPath>
    </Reference>
    <Reference Include="System.Data" />
    <Reference Include="System.Data.OracleClient" />
    <Reference Include="System.Diagnostics.DiagnosticSource, Version=4.0.3.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.DiagnosticSource.4.5.1\lib\net46\System.Diagnostics.DiagnosticSource.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing" />
    <Reference Include="System.Memory, Version=4.0.1.2, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.5\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net" />
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Security" />
    <Reference Include="System.Security.AccessControl, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.AccessControl.4.7.0\lib\net461\System.Security.AccessControl.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Permissions, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Permissions.4.7.0\lib\net461\System.Security.Permissions.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Principal.Windows, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Principal.Windows.4.7.0\lib\net461\System.Security.Principal.Windows.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.Text.Encodings.Web, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encodings.Web.7.0.0\lib\net462\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Json, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Json.7.0.3\lib\net462\System.Text.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Channels, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Channels.4.7.1\lib\net461\System.Threading.Channels.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Transactions" />
    <Reference Include="System.ValueTuple, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net47\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Web.Http, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Core.5.2.5\lib\net45\System.Web.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http.WebHost, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.WebHost.5.2.5\lib\net45\System.Web.Http.WebHost.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Abstractions" />
    <Reference Include="System.Web.Routing" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="Microsoft.Web.Infrastructure, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.Web.Infrastructure.*******\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http">
    </Reference>
    <Reference Include="System.Net.Http.Formatting, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Client.5.2.7\lib\net45\System.Net.Http.Formatting.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.WebRequest">
    </Reference>
    <Reference Include="System.Web.Helpers, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.Helpers.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Mvc, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.2.7\lib\net45\System.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Optimization">
      <HintPath>..\packages\Microsoft.AspNet.Web.Optimization.1.1.3\lib\net40\System.Web.Optimization.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.Razor.3.2.7\lib\net45\System.Web.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
    </Reference>
    <Reference Include="WebActivatorEx, Version=2.0.0.0, Culture=neutral, PublicKeyToken=7b26dc2a43f6a0d4, processorArchitecture=MSIL">
      <HintPath>..\packages\WebActivatorEx.2.0\lib\net40\WebActivatorEx.dll</HintPath>
    </Reference>
    <Reference Include="WebGrease">
      <Private>True</Private>
      <HintPath>..\packages\WebGrease.1.6.0\lib\WebGrease.dll</HintPath>
    </Reference>
    <Reference Include="Antlr3.Runtime">
      <Private>True</Private>
      <HintPath>..\packages\Antlr.3.5.0.2\lib\Antlr3.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="WindowsBase" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Microsoft.CodeDom.Providers.DotNetCompilerPlatform">
      <HintPath>..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\lib\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="App_Start\AutofacConfig.cs" />
    <Compile Include="App_Start\Config.cs" />
    <Compile Include="App_Start\FilterConfig.cs" />
    <Compile Include="App_Start\AddRequiredHeaderParameter.cs" />
    <Compile Include="App_Start\RouteConfig.cs" />
    <Compile Include="App_Start\SwaggerConfig.cs" />
    <Compile Include="App_Start\WebApiConfig.cs" />
    <Compile Include="Configuration\AuditApiRequestsConfiguration.cs" />
    <Compile Include="Configuration\DictionariesApiConfiguration.cs" />
    <Compile Include="Configuration\GlobalConfigurationConfiguration.cs" />
    <Compile Include="Configuration\KillerQuestionsApiConfiguration.cs" />
    <Compile Include="Configuration\RedarborCacheConfiguration.cs" />
    <Compile Include="Configuration\RedarborKpiConsumerConfiguration.cs" />
    <Compile Include="Constants\AccuracyMessageConstants.cs" />
    <Compile Include="Constants\EmailConstants.cs" />
    <Compile Include="Constants\ErrorMessageConstants.cs" />
    <Compile Include="Constants\FieldLimitationConstants.cs" />
    <Compile Include="Constants\PaymentTypes.cs" />
    <Compile Include="Constants\RegularExpressionsConstants.cs" />
    <Compile Include="Controllers\ApiBaseController.cs" />
    <Compile Include="Controllers\CTHROnboardingController.cs" />
    <Compile Include="Controllers\CompanyController.cs" />
    <Compile Include="Controllers\CompanyPreRegisterController.cs" />
    <Compile Include="Controllers\HealthcheckController.cs" />
    <Compile Include="Controllers\NavisionController.cs" />
    <Compile Include="Controllers\OfferPreRegisterController.cs" />
    <Compile Include="Controllers\SessionsManagementController.cs" />
    <Compile Include="Controllers\PaymentProductionController.cs" />
    <Compile Include="Exceptions\CompanyRegisterException.cs" />
    <Compile Include="Exceptions\PaymentProductionException.cs" />
    <Compile Include="Exceptions\NavisionException.cs" />
    <Compile Include="Exceptions\RegisterLoginException.cs" />
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="Helpers\BuildVersionHelper.cs" />
    <Compile Include="Helpers\MapperHelper.cs" />
    <Compile Include="Helpers\ReflectionRegistrator.cs" />
    <Compile Include="Models\CompanyPreRegister\CompanyPreRegisterModel.cs" />
    <Compile Include="Models\CompanyPreRegister\CompanyPreRegisterModelError.cs" />
    <Compile Include="Models\CompanyPreRegister\CompanyPreRegisterReturnModelError.cs" />
    <Compile Include="Models\CompanyPreRegister\CourtesyProductReturnModel.cs" />
    <Compile Include="Models\CompanyPreRegister\CompanyPreRegisterReturnModel.cs" />
    <Compile Include="Models\CompanyRegister\CompanyAddressModel.cs" />
    <Compile Include="Models\CompanyRegister\CompanyContactInformationModel.cs" />
    <Compile Include="Models\CompanyRegister\CompanyErrorResponseModel.cs" />
    <Compile Include="Models\CompanyRegister\CompanyValidationNitResponseModel.cs" />
    <Compile Include="Models\CompanyRegister\CompanyPhoneNumberModel.cs" />
    <Compile Include="Models\CompanyRegister\CompanyRegisterRequestModel.cs" />
    <Compile Include="Models\CompanyRegister\CompanyLoginTokenResponseModel.cs" />
    <Compile Include="Models\CompanyRegister\CompanyRegisterResponseModel.cs" />
    <Compile Include="Models\CompanyRegister\CompanyUserModel.cs" />
    <Compile Include="Models\CTHROnboarding\CTHROnboardingCompanyAddModel.cs" />
    <Compile Include="Models\CTHROnboarding\CTHROnboardingCompanyAddResultModel.cs" />
    <Compile Include="Models\CTHROnboarding\CTHROnboardingRecruiterAddModel.cs" />
    <Compile Include="Models\HealthCheckReturnModel.cs" />
    <Compile Include="Models\NavisionCancelServiceModel.cs" />
    <Compile Include="Models\NavisionReturnModel.cs" />
    <Compile Include="Models\NavisionSavePathModel.cs" />
    <Compile Include="Models\NavisionSetreliabilityModel.cs" />
    <Compile Include="Models\OfferPreRegister\OfferPreRegisterMailModel.cs" />
    <Compile Include="Models\OperationManualPayModel.cs" />
    <Compile Include="Models\OperationProductsReturnModel.cs" />
    <Compile Include="Models\RegisterLoginModel.cs" />
    <Compile Include="Models\SessionsManagementModel.cs" />
    <Compile Include="Models\SessionsManagementReturnModel.cs" />
    <Compile Include="Models\OperationPayReturnModel.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="ValidationAttributes\NoSurrogateTextAllowed.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="favicon.ico" />
    <Content Include="Global.asax" />
    <Content Include="Templates\CompanyPreRegisterEmail.html">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\CompanyReminderImportOffersEmail.html" />
    <Content Include="Templates\CompanyTotalOffersEmail.html" />
    <Content Include="Web.config" />
    <none Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </none>
    <none Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </none>
    <Content Include="Web.PRODUCTION.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Web.BETA.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Web.STAGING.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="App_Data\" />
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Readarbor.Master.Entities\Redarbor.Master.Entities.csproj">
      <Project>{72B93BA2-C177-4DDF-9F27-E08151323719}</Project>
      <Name>Redarbor.Master.Entities</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Candidate.Contracts.ServiceLibrary\Redarbor.Candidate.Contracts.ServiceLibrary.csproj">
      <Project>{adeb0b9a-1f18-49c5-a6ec-20becdab8eaf}</Project>
      <Name>Redarbor.Candidate.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Candidate.Impl.ServiceLibrary\Redarbor.Candidate.Impl.ServiceLibrary.csproj">
      <Project>{a12d1341-ad71-43b8-8c88-beffad211078}</Project>
      <Name>Redarbor.Candidate.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.CommentCv.Contracts.ServiceLibrary\Redarbor.CommentCv.Contracts.ServiceLibrary.csproj">
      <Project>{afb3a08d-aede-49e6-bfe6-585ec43a3eef}</Project>
      <Name>Redarbor.CommentCv.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.CommentCv.Impl.ServiceLibrary\Redarbor.CommentCv.Impl.ServiceLibrary.csproj">
      <Project>{c29e2cde-54a5-4587-b24b-f35a48f9fe32}</Project>
      <Name>Redarbor.CommentCv.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Common.Entities\Redarbor.Common.Entities.csproj">
      <Project>{3da7e832-19ae-4c35-85f8-5faf5a4dabeb}</Project>
      <Name>Redarbor.Common.Entities</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Company.Contracts.ServiceLibrary\Redarbor.Company.Contracts.ServiceLibrary.csproj">
      <Project>{76df1704-a73e-40ed-bbe7-69473d962cb1}</Project>
      <Name>Redarbor.Company.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Company.Impl.ServiceLibrary\Redarbor.Company.Impl.ServiceLibrary.csproj">
      <Project>{04514890-294f-47c8-a92a-a790d850cb10}</Project>
      <Name>Redarbor.Company.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Configuration.Library\Redarbor.Configuration.Library.csproj">
      <Project>{bd90ad61-e12d-4cbd-be3a-81a1a31fddb2}</Project>
      <Name>Redarbor.Configuration.Library</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Cache.Contracts.ServiceLibrary\Redarbor.Core.Cache.Contracts.ServiceLibrary.csproj">
      <Project>{E683967C-B674-4450-B9A6-6030FD65E0F4}</Project>
      <Name>Redarbor.Core.Cache.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Cache.Impl.ServiceLibrary\Redarbor.Core.Cache.Impl.ServiceLibrary.csproj">
      <Project>{6539a9a2-be8c-47d4-adbb-b0c863f49c54}</Project>
      <Name>Redarbor.Core.Cache.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Computrabajo.Contracts.ServiceLibrary\Redarbor.Core.Computrabajo.Contracts.ServiceLibrary.csproj">
      <Project>{9BE71737-B615-4227-BB44-C5ECEC3DCED3}</Project>
      <Name>Redarbor.Core.Computrabajo.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Computrabajo.Impl.ServiceLibrary\Redarbor.Core.Computrabajo.Impl.ServiceLibrary.csproj">
      <Project>{aa731e64-6689-4765-80ee-5beefec24849}</Project>
      <Name>Redarbor.Core.Computrabajo.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Counter.Contracts.ServiceLibrary\Redarbor.Core.Counters.Contracts.ServiceLibrary.csproj">
      <Project>{eec65db1-974d-4172-ba9a-735d63153264}</Project>
      <Name>Redarbor.Core.Counters.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Counter.Impl.ServiceLibrary\Redarbor.Core.Counters.Impl.ServiceLibrary.csproj">
      <Project>{451fa585-dde8-43e2-931d-9365af99ce6b}</Project>
      <Name>Redarbor.Core.Counters.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Kpi.Contracts.ServiceLibrary\Redarbor.Core.Kpi.Contracts.ServiceLibrary.csproj">
      <Project>{69fa56cc-33a8-445a-aa62-6bb70ed1bc70}</Project>
      <Name>Redarbor.Core.Kpi.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Kpi.Impl.ServiceLibrary\Redarbor.Core.Kpi.Impl.ServiceLibrary.csproj">
      <Project>{f773520f-2ef0-44d7-8878-28428e6218b5}</Project>
      <Name>Redarbor.Core.Kpi.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Mailing.Contracts.ServiceLibrary\Redarbor.Core.Mailing.Contracts.ServiceLibrary.csproj">
      <Project>{cdf5549a-6741-4fde-a925-550ccb4d896b}</Project>
      <Name>Redarbor.Core.Mailing.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Mailing.Impl.ServiceLibrary\Redarbor.Core.Mailing.Impl.ServiceLibrary.csproj">
      <Project>{8e69ec0c-a7c0-4c5b-9388-d2ce82e36797}</Project>
      <Name>Redarbor.Core.Mailing.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Resolver.Contracts.ServiceLibrary\Redarbor.Core.Resolver.Contracts.ServiceLibrary.csproj">
      <Project>{27f82506-248d-4117-9dc1-2198a2187fbb}</Project>
      <Name>Redarbor.Core.Resolver.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Resolver.Impl.ServiceLibrary\Redarbor.Core.Resolver.Impl.ServiceLibrary.csproj">
      <Project>{62db8e3d-0cbf-49bb-b06c-eb64709cf099}</Project>
      <Name>Redarbor.Core.Resolver.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.SEO.Contracts.ServiceLibrary\Redarbor.Core.SEO.Contracts.ServiceLibrary.csproj">
      <Project>{34dee7e6-6f70-4f76-bead-27e058716d79}</Project>
      <Name>Redarbor.Core.SEO.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Shared.Contracts.ServiceLibrary\Redarbor.Core.Shared.Contracts.ServiceLibrary.csproj">
      <Project>{21E3ABEA-6637-49C2-8684-E0F842DE8506}</Project>
      <Name>Redarbor.Core.Shared.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Shared.Impl.ServiceLibrary\Redarbor.Core.Shared.Impl.ServiceLibrary.csproj">
      <Project>{d6387058-eb7b-42f7-aa9a-9e73e2a217dd}</Project>
      <Name>Redarbor.Core.Shared.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Stack.Contracts.ServiceLibrary\Redarbor.Core.Stack.Contracts.ServiceLibrary.csproj">
      <Project>{89aacf3b-ac86-48cc-97e9-15bc5c6543e7}</Project>
      <Name>Redarbor.Core.Stack.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Stack.Impl.ServiceLibrary\Redarbor.Core.Stack.Impl.ServiceLibrary.csproj">
      <Project>{7df7bbf8-215d-494a-bb30-9fd1e13ead8b}</Project>
      <Name>Redarbor.Core.Stack.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Timeline.Contracts.ServiceLibrary\Redarbor.Core.Timeline.Contracts.ServiceLibrary.csproj">
      <Project>{e90199e2-5625-46c2-8853-a3e877326f25}</Project>
      <Name>Redarbor.Core.Timeline.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Timeline.Impl.ServiceLibrary\Redarbor.Core.Timeline.Impl.ServiceLibrary.csproj">
      <Project>{5be33fb7-1fd3-4eab-bd5f-6939d23861cf}</Project>
      <Name>Redarbor.Core.Timeline.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.TrackingActions.Contracts.ServiceLibrary\Redarbor.Core.TrackingActions.Contracts.ServiceLibrary.csproj">
      <Project>{a7ec02a5-033a-4ede-ad08-8acbcd6dea55}</Project>
      <Name>Redarbor.Core.TrackingActions.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.TrackingActions.Impl.ServiceLibrary\Redarbor.Core.TrackingActions.Impl.ServiceLibrary.csproj">
      <Project>{219b0007-b45d-493e-af04-4ca73812f496}</Project>
      <Name>Redarbor.Core.TrackingActions.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Curriculum.Contracts.ServiceLibrary\Redarbor.Curriculum.Contracts.ServiceLibrary.csproj">
      <Project>{61935dcf-ffa4-42ef-b825-877dc3b71bbf}</Project>
      <Name>Redarbor.Curriculum.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Curriculum.Impl.ServiceLibrary\Redarbor.Curriculum.Impl.ServiceLibrary.csproj">
      <Project>{41f5aabf-63a7-465a-a698-0c18142c1095}</Project>
      <Name>Redarbor.Curriculum.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.DIRegister.AssemblyDiscovery.ServiceLibrary\Redarbor.DIRegister.AssemblyDiscovery.ServiceLibrary.csproj">
      <Project>{7db913eb-ab01-4a1a-9e8d-1f9d5f7e88cf}</Project>
      <Name>Redarbor.DIRegister.AssemblyDiscovery.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.DIRegister.Autofac.ServiceLibrary\Redarbor.DIRegister.Autofac.ServiceLibrary.csproj">
      <Project>{b9f54550-cf57-4e5d-8383-769df8f18568}</Project>
      <Name>Redarbor.DIRegister.Autofac.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.DIRegister.WebAPI.ServiceLibrary\Redarbor.DIRegister.WebAPI.ServiceLibrary.csproj">
      <Project>{186226e3-fb61-4e6a-9db8-226a477c85be}</Project>
      <Name>Redarbor.DIRegister.WebAPI.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Elastic.Entities\Redarbor.Elastic.Entities.csproj">
      <Project>{2eed9e8d-b4f4-4797-a1be-9068a549c3a0}</Project>
      <Name>Redarbor.Elastic.Entities</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.EmailSender.Contracts.ServiceLibrary\Redarbor.EmailSender.Contracts.ServiceLibrary.csproj">
      <Project>{C84DF25C-43B0-42C8-8D51-4A60C7CE4EC3}</Project>
      <Name>Redarbor.EmailSender.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.EmailSender.Impl.ServiceLibrary\Redarbor.EmailSender.Impl.ServiceLibrary.csproj">
      <Project>{1955a18d-9d7e-42c1-8a92-4bd73aedee4d}</Project>
      <Name>Redarbor.EmailSender.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Extensions.Library\Redarbor.Extensions.Library.csproj">
      <Project>{A30C078B-2F28-42B0-84E7-E02F9C30E27E}</Project>
      <Name>Redarbor.Extensions.Library</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Geolocation.Contracts.ServiceLibrary\Redarbor.Geolocation.Contracts.ServiceLibrary.csproj">
      <Project>{6db7d0e8-b5c5-42ea-81c1-0e1418cbd36a}</Project>
      <Name>Redarbor.Geolocation.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Invoice.Contracts.ServiceLibrary\Redarbor.Invoice.Contracts.ServiceLibrary.csproj">
      <Project>{ffc8183e-9d73-46ad-890f-1f4c8bfa187b}</Project>
      <Name>Redarbor.Invoice.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Invoice.Impl.ServiceLibrary\Redarbor.Invoice.Impl.ServiceLibrary.csproj">
      <Project>{2777dfe6-0a6c-4887-87b4-0418a7074ece}</Project>
      <Name>Redarbor.Invoice.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Master.Contracts.ServiceLibrary\Redarbor.Master.Contracts.ServiceLibrary.csproj">
      <Project>{e0dd7a84-2cdb-4704-b37a-a0367924112f}</Project>
      <Name>Redarbor.Master.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Master.Impl.ServiceLibrary\Redarbor.Master.Impl.ServiceLibrary.csproj">
      <Project>{3fec6bb8-e0b9-4783-8877-f4f79cc7d73b}</Project>
      <Name>Redarbor.Master.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Match.Contracts.ServiceLibrary\Redarbor.Match.Contracts.ServiceLibrary.csproj">
      <Project>{28676d69-97f1-49d3-9e92-c0eb95d45426}</Project>
      <Name>Redarbor.Match.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Match.Impl.ServiceLibrary\Redarbor.Match.Impl.ServiceLibrary.csproj">
      <Project>{a45070d7-9e74-4142-bab7-f4087fc991a4}</Project>
      <Name>Redarbor.Match.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Ndr.Library\Redarbor.Ndr.Library.csproj">
      <Project>{42799AFD-2EA8-448B-81E0-FDBD7ED0737F}</Project>
      <Name>Redarbor.Ndr.Library</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Offer.Contracts.ServiceLibrary\Redarbor.Offer.Contracts.ServiceLibrary.csproj">
      <Project>{5df02fd7-adc3-4aa2-b8ad-f5f2d42c506f}</Project>
      <Name>Redarbor.Offer.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.OperacionCompra.Contracts.ServiceLibrary\Redarbor.PurchaseOperation.Contracts.ServiceLibrary.csproj">
      <Project>{20e076c5-9277-4da3-a256-85f6420dd617}</Project>
      <Name>Redarbor.PurchaseOperation.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Payment.Contracts.ServiceLibrary\Redarbor.Payment.Contracts.ServiceLibrary.csproj">
      <Project>{60E5D47D-0161-4C19-B60A-6F353B2CE937}</Project>
      <Name>Redarbor.Payment.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Payment.Impl.ServiceLibrary\Redarbor.Payment.Impl.ServiceLibrary.csproj">
      <Project>{9D0E3452-AA5B-43FF-824F-FD41C2B4D409}</Project>
      <Name>Redarbor.Payment.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Procs.Domain\Redarbor.Procs.Domain.csproj">
      <Project>{F5287777-BDF4-4D56-B30D-6B7462400E93}</Project>
      <Name>Redarbor.Procs.Domain</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Products.Contracts.ServiceLibrary\Redarbor.Products.Contracts.ServiceLibrary.csproj">
      <Project>{7e7dfd45-2a07-4347-aad6-836b215f129b}</Project>
      <Name>Redarbor.Products.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Products.Impl.ServiceLibrary\Redarbor.Products.Impl.ServiceLibrary.csproj">
      <Project>{E53E025F-0BCA-4E70-8EFD-6C50CA9C246D}</Project>
      <Name>Redarbor.Products.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Products.Library\Redarbor.Products.Library.csproj">
      <Project>{B0546E25-69E2-4B31-9F4C-1F8C74C00586}</Project>
      <Name>Redarbor.Products.Library</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.ProProduct.Contracts.ServiceLibrary\Redarbor.ProProduct.Contracts.ServiceLibrary.csproj">
      <Project>{95891b27-0478-45a5-9ac9-642942960e51}</Project>
      <Name>Redarbor.ProProduct.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.ProProduct.Impl.ServiceLibrary\Redarbor.ProProduct.Impl.ServiceLibrary.csproj">
      <Project>{6208d59d-2662-4e7d-bf7e-b25c202a79b0}</Project>
      <Name>Redarbor.ProProduct.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.PurchaseOperationCtPayment.Library\Redarbor.PurchaseOperationCtPayment.Library.csproj">
      <Project>{9f29690f-5f2f-46dc-a5a4-bb5d0377dfa8}</Project>
      <Name>Redarbor.PurchaseOperationCtPayment.Library</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.RatingCv.Contracts.ServiceLibrary\Redarbor.RatingCv.Contracts.ServiceLibrary.csproj">
      <Project>{2128b8bb-0460-44a2-a624-da77a6b10392}</Project>
      <Name>Redarbor.RatingCv.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.RatingCv.Impl.ServiceLibrary\Redarbor.RatingCv.Impl.ServiceLibrary.csproj">
      <Project>{415ac519-9d3b-4d66-bf2c-da932b104bc8}</Project>
      <Name>Redarbor.RatingCv.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.User.Contracts.ServiceLibrary\Redarbor.User.Contracts.ServiceLibrary.csproj">
      <Project>{79e50e19-d3c9-4d95-890c-ec9e9b1a5b1c}</Project>
      <Name>Redarbor.User.Contracts.ServiceLibrary</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Connected Services\" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <Target Name="MvcBuildViews" AfterTargets="AfterBuild" Condition="'$(MvcBuildViews)'=='true'">
    <AspNetCompiler VirtualPath="temp" PhysicalPath="$(WebProjectOutputDir)" />
  </Target>
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>60785</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>https://localhost:44335/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>Este proyecto hace referencia a los paquetes NuGet que faltan en este equipo. Use la restauración de paquetes NuGet para descargarlos. Para obtener más información, consulte http://go.microsoft.com/fwlink/?LinkID=322105. El archivo que falta es {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props'))" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target> -->
</Project>