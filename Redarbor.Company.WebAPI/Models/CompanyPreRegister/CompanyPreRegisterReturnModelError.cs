using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Web;

namespace Redarbor.Company.WebAPI.Models.CompanyPreRegister
{
    public class CompanyPreRegisterReturnModelError
    {
        public List<CompanyPreRegisterModelError> ListErrors { get; set; } = new List<CompanyPreRegisterModelError>();
        public string Key { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        

        public CompanyPreRegisterReturnModelError()
        {

        }
        public CompanyPreRegisterReturnModelError(List<CompanyPreRegisterModelError> listErrors, string key, string description)
        {
            ListErrors = listErrors;
            Key = key;
            Description = description;
        }
    }
}