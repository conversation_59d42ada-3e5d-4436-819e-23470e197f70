using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace Redarbor.Company.WebAPI.Models
{
    public class NavisionCancelServiceModel
    {
        public int activity_id { get; set; }
        public string origin { get; set; }            

        public bool isValid
        {
            get
            {
                return (origin.Trim().ToLower() == "cw" && activity_id > 0);
            }
        }
    }
}