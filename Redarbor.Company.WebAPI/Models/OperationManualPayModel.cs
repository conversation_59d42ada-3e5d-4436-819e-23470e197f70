using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace Redarbor.Company.WebAPI.Models
{
    public class OperationManualPayModel
    {
        public int IdPurchaseOperation { get; set; } = 0;
        public int Idcompany { get; set; } = 0;
        public short Idportal { get; set; } = 0;
        public short IdOrigin { get; set; } = 0; //aqui casar con el Enum...PaymentOriginEnum
        public String Transactionid { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty; //TpvMessageText  
        public bool IsOperationPayedByForce { get; set; } = false; //Si es true NO COMPROBAMOS que exista en otros proveedores
    }
}