using Newtonsoft.Json;
using Redarbor.Company.Contracts.ServiceLibrary;
using Redarbor.Company.WebAPI.Models;
using Redarbor.Company.WebAPI.Models.CTHROnboarding;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Core.Shared.Contracts.ServiceLibrary;
using Redarbor.Master.Entities.Company;
using Redarbor.Master.Entities.Enums;
using Redarbor.Master.Entities.Users;
using Redarbor.Products.Contracts.ServiceLibrary;
using Redarbor.Products.Impl.ServiceLibrary;
using Redarbor.User.Contracts.ServiceLibrary;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using System.Web.Http;

namespace Redarbor.Company.WebAPI.Controllers
{
    public class CTHROnboardingController : ApiBaseController
    {

        
        private IExceptionPublisherService _exceptionPublisherService;
        private ICompanyProductService _companyProductService;
        private ICompanyService _companyService;
        private IPortalConfigurationService _portalConfigurationService;
        private IUserService _userService;
        private IApiService _apiService;

        public CTHROnboardingController(
            IExceptionPublisherService exceptionPublisherService,
            ICompanyService companyService,
            IUserService userService,
            IApiService apiService,
            ICompanyProductService companyProductService,
            IPortalConfigurationService portalConfigurationService)
        {
            _exceptionPublisherService = exceptionPublisherService;
            _companyService = companyService;
            _userService = userService;
            _companyProductService = companyProductService;
            _apiService = apiService;
            _portalConfigurationService = portalConfigurationService;
        }


        [HttpPost]
        public async Task<IHttpActionResult> AddCompany(int companyid, short portalId)
        {
            string exceptionPrefix = $"CT HR - AddCompany (companyId: {companyid}, portalId: {portalId}) - ";

            try
            {                
                if (PortalId > 0)
                {
                     portalId = PortalId;
                }

                //model validate
                if (portalId <= 0) throw new ArgumentException($"{exceptionPrefix} El HEADER y en la llamada no viene informado el Portal!");
                if (companyid <= 0) throw new ArgumentException($"{exceptionPrefix} Argument idCompany is not valid");

                _exceptionPublisherService.PublishInfo(new Exception($"{exceptionPrefix} WebAPI de compañía ha recibido petición para añadir a CT HR la compañía con id {companyid} del portalId {portalId}"), "CTHROnboardingController", "AddCompany", portalId: portalId);

                //Verificamos que realmente la compañía tenga el producto de CT HR con los ambitos deseados
                var productsActive = _companyProductService.GetAllCompanyActiveProducts(companyid, portalId);
                bool withCTHROnboardingEnabled = productsActive.Any(p => p.Features.Any(f => f.AmbitId == (int)ProductAmbitEnum.CTHROnBoarding));
                bool withCTHRHCMEnabled = productsActive.Any(p => p.Features.Any(f => f.AmbitId == (int)ProductAmbitEnum.CTHRHCM));

                if (!withCTHROnboardingEnabled || !withCTHRHCMEnabled)
                {
                    throw new Exception($"{exceptionPrefix} la compañía no tiene asignado un producto con los ambitos deseados. withCTHROnboardingEnabled: {withCTHROnboardingEnabled}; withCTHRHCMEnabled: {withCTHRHCMEnabled}");
                }

                CompanyEntity companyEntity = _companyService.GetByPK(new Master.Entities.Searchs.CompanySearchSpecifications(portalId)
                {
                    Id = companyid
                });

                if (companyEntity != null && companyEntity.Id == companyid)
                {
                    //Lo recuperamos solo para tener el id country
                    CompanyDetailEntity companyDetailEntity = _companyService.GetCompanyDetail(companyid, idPortal: portalId);

                    List<UserEntity> users = _userService.GetManagersByIdCompany(companyid, portalId);

                    users = users.Where(x => x.StatusId == (int)StatusEnum.Active).ToList();
                    bool addMoreRecruiters = false;

                    if (users.Any())
                    {
                        UserEntity principalRecruiter = users.FirstOrDefault(x => (x.Principal == 1 || x.RoleId == (short)UserRoleEnum.ADMINISTRADOR_PRINCIPAL));
                        if (principalRecruiter == null)
                        {
                            principalRecruiter = users.FirstOrDefault(x => (x.Principal == 1 || x.RoleId == (short)UserRoleEnum.ADMINISTRADOR));
                        }
                        if (principalRecruiter == null)
                        { 
                            principalRecruiter = users.OrderByDescending(x => x.CreatedOn).FirstOrDefault();
                        }

                        addMoreRecruiters = users.Count() > 1;
                        List<CTHROnboardingRecruiterAddModel> recruiters = null;

                        if (principalRecruiter != null)
                        {
                            string recruiterName = principalRecruiter.ContactName;
                            string recruiterFirstLastName = " ";
                            string recruiterSecondLastName = " ";

                            if (principalRecruiter.ContactName.Split(' ').Count() >= 2)
                            {
                                recruiterName = principalRecruiter.ContactName.Split(' ')[0];
                                recruiterFirstLastName = principalRecruiter.ContactName.Split(' ')[1];

                                if (principalRecruiter.ContactName.Split(' ').Count() == 2)
                                {
                                    recruiterSecondLastName = principalRecruiter.ContactName.Replace($"{recruiterName} {recruiterFirstLastName}", "");
                                }
                            }

                            var portalConfig = _portalConfigurationService.GetPortalConfiguration();

                            CTHROnboardingCompanyAddModel cTHROnboardingCompanyAddModel = new CTHROnboardingCompanyAddModel()
                            {
                                SourceCompanyId = companyEntity.Id,
                                SourceId = 2,
                                PortalId = companyEntity.PortalId,
                                BillingNif = companyEntity.Nit,
                                BillingName = !string.IsNullOrEmpty(companyEntity.ComercialName) ? companyEntity.ComercialName : companyEntity.CompanyName,
                                CountryId = companyDetailEntity.IdCountry,
                                Address = companyDetailEntity.Address,
                                LocalizationId = companyDetailEntity.IdLocalization,
                                CityId = companyDetailEntity.IdCity,
                                Email = companyEntity.ContactInformation.Email, 
                                LanguageCode = string.Empty,
                                ContactPhone = companyEntity.ContactInformation.PhoneNumbers != null && companyEntity.ContactInformation.PhoneNumbers.Any()? companyEntity.ContactInformation.PhoneNumbers.FirstOrDefault().Number : string.Empty,

                                Recruiter = new CTHROnboardingRecruiterAddModel()
                                {
                                    Name = recruiterName,
                                    FirstLastName = recruiterFirstLastName,
                                    SecondLastName = recruiterSecondLastName,
                                    Email = principalRecruiter.Email,
                                    SourceEmployeeId = principalRecruiter.Id,
                                    Password = principalRecruiter.Password
                                },
                                Logo = string.Format("{0}{1}", portalConfig.cdn_images, companyEntity.LogoPath),
                                LogoNegative = string.Empty
                            };

                            if (addMoreRecruiters)
                            {
                                recruiters = new List<CTHROnboardingRecruiterAddModel>();

                                foreach (UserEntity recruiter in users.Where(x => x.Email != principalRecruiter.Email))
                                {
                                    if (recruiter.ContactName.Split(' ').Count() >= 2)
                                    {
                                        recruiterName = recruiter.ContactName.Split(' ')[0];
                                        recruiterFirstLastName = recruiter.ContactName.Split(' ')[1];

                                        if (recruiter.ContactName.Split(' ').Count() == 2)
                                        {
                                            recruiterSecondLastName = recruiter.ContactName.Replace($"{recruiterName} {recruiterFirstLastName}", "");
                                        }
                                    }
                                    recruiters.Add(new CTHROnboardingRecruiterAddModel()
                                    {
                                        Name = recruiterName,
                                        FirstLastName = recruiterFirstLastName,
                                        SecondLastName = recruiterSecondLastName,
                                        Email = recruiter.Email,
                                        SourceEmployeeId = recruiter.Id,
                                        Password = recruiter.Password
                                    });
                                }
                            }

                            string urlBase = portalConfig.AEPortalConfig.CTHROnBoardingBaseUrl;
                            string url = string.Empty;
                            string token = portalConfig.AEPortalConfig.CTHROnBoardingToken;

                            bool forceInfoForLocalUse = false;
                            if (forceInfoForLocalUse)
                            {
                                if (string.IsNullOrEmpty(urlBase))
                                {
                                    urlBase = "http://localhost:5000/";
                                }
                                if (string.IsNullOrEmpty(token))
                                {
                                    //DEV
                                    token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1bmlxdWVfbmFtZSI6IjEiLCJwb3J0YWxhZG1pbiI6IlRydWUiLCJuYmYiOjE2ODU0MzI3NjgsImV4cCI6MTY4NjAzNzU2OCwiaWF0IjoxNjg1NDMyNzY4LCJpc3MiOiJIb2xtZXNIUiIsImF1ZCI6ImRldi5vbmJvYXJkaW5nL3Rva2VuIn0.7ryc3G6nzMoFYlO7FfoZ83kW37wmqe8e-sCI1wOVeOY";
                                    //PRE
                                    token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1bmlxdWVfbmFtZSI6IjEiLCJwb3J0YWxhZG1pbiI6IlRydWUiLCJuYmYiOjE2ODU1Mzk5NDEsImV4cCI6MTY4NjE0NDc0MSwiaWF0IjoxNjg1NTM5OTQxLCJpc3MiOiJIb2xtZXNIUiIsImF1ZCI6InByZS5vbmJvYXJkaW5nL3Rva2VuIn0.vsCQry2x1aeGrTETVDRYGjSKGEIoUqTXSB9K9PUiYVo";
                                }
                                if (string.IsNullOrEmpty(portalConfig.cdn_images))
                                {
                                    cTHROnboardingCompanyAddModel.Logo = string.Format("{0}{1}", "https://pre-images.ct-stc.com/1/", companyEntity.LogoPath);
                                }
                            }

                            if (!string.IsNullOrEmpty(urlBase) && !string.IsNullOrEmpty(token))
                            {
                                if (!urlBase.EndsWith("/"))
                                {
                                    urlBase = $"{urlBase}/";
                                }

                                url = $"{urlBase}api/onboarding/company/addct";
                                
                                CTHROnboardingCompanyAddResultModel cTHROnboardingCompanyAddResultModel = _apiService.PostWithAuthorization<CTHROnboardingCompanyAddResultModel>(url, cTHROnboardingCompanyAddModel, token, authorizationType: true, idBusiness: 1, timeoutInMinutes: 5);

                                if (cTHROnboardingCompanyAddResultModel != null)
                                {
                                    if (cTHROnboardingCompanyAddResultModel.Success)
                                    {
                                        _exceptionPublisherService.PublishInfo(new Exception($"{exceptionPrefix} compañía añadida con éxito {JsonConvert.SerializeObject(cTHROnboardingCompanyAddModel)}"), "CTHROnboardingController", "AddCompany", portalId: portalId);

                                        if (addMoreRecruiters)
                                        { 
                                            url = $"{urlBase}api/onboarding/company/addrecruitersct?companyId={cTHROnboardingCompanyAddResultModel.HolmesCompanyId}&ownerFirstEmail={principalRecruiter.Email}";

                                            cTHROnboardingCompanyAddResultModel = _apiService.PostWithAuthorization<CTHROnboardingCompanyAddResultModel>(url, recruiters, token, authorizationType: true, idBusiness: 1, timeoutInMinutes: 5);

                                            if (cTHROnboardingCompanyAddResultModel != null && cTHROnboardingCompanyAddResultModel.Success)
                                            {
                                                _exceptionPublisherService.PublishInfo(new Exception($"{exceptionPrefix} añadidos más recruiters con éxito {JsonConvert.SerializeObject(cTHROnboardingCompanyAddModel)}"), "CTHROnboardingController", "AddCompany", portalId: portalId);
                                            }
                                            else
                                            {
                                                _exceptionPublisherService.PublishWarning(new Exception($"{exceptionPrefix} ha fallado al añadir más recruiters {JsonConvert.SerializeObject(cTHROnboardingCompanyAddModel)}"), "CTHROnboardingController", "AddCompany", portalId: portalId);
                                            }
                                        }
                                    }
                                    else
                                    {
                                        throw new Exception($"{exceptionPrefix} ha fallado la llamada a Onboarding, respuesta: {JsonConvert.SerializeObject(cTHROnboardingCompanyAddResultModel)}");
                                    }
                                }
                                else
                                {
                                    throw new Exception($"{exceptionPrefix} ha fallado la llamada a Onboarding, la respuesta es nula");
                                }
                            }
                            else
                            {
                                throw new Exception($"{exceptionPrefix} configuraciones locales no encontradas, CTHROnBoardingBaseUrl: {url}; token: {token}");
                            }
                        }
                        else
                        { 
                            throw new Exception($"{exceptionPrefix} sin principal recruiter configurado: recruiters encontrados: {users.Count()}");
                        }

                        //TODO: añadir otra llamada para dar de alta el resto de empleados...
                        //Owner el principal
                        //Administradores el resto --> todos

                    }
                    else
                    {
                        //No tenemos recruiters configurados en la company...
                        throw new Exception($"{exceptionPrefix} sin users encontrados");
                    }
                }
                else
                { 
                    throw new EntryPointNotFoundException($"{exceptionPrefix} Company {companyid} not found in portal {portalId}");
                }

                return Ok(new { Error = false, ErrorMessage = "" , Info = $"Compañía con Id {companyid} y portalId {portalId} añadida a CT HR con éxito", StatusCode = HttpStatusCode.OK});
            }
            catch (Exception ex)
            {
                Dictionary<string, string> extradata = new Dictionary<string, string>();
                extradata.Add("companyId", companyid.ToString());
                extradata.Add("portalId", portalId.ToString());
                extradata.Add("CompanyInfo", exceptionPrefix);

                _exceptionPublisherService.Publish(ex, "CTHROnboardingController", "AddCompany", false, null, portalId);
                return ResponseError(new { Error = true, ErrorMessage = $"{exceptionPrefix} {ex.Message}", StatusCode = HttpStatusCode.InternalServerError }, ex);
            }
        }

        private OperationProductsReturnModel GetProduct(int companyproductId, short portalId, int companyId, string productName)
        {
            var companyproduct = _companyProductService.GetByCompanyProductId(companyproductId, portalId, companyId);
            return new OperationProductsReturnModel()
            {

                DateActivation = companyproduct.DateActivation,
                DateExpiration = companyproduct.DateExpiration,
                ProductName = productName,
                CompanyId = companyId,
                PortalId = portalId
            };
        }

        

        
    }
}