using Redarbor.Extensions.Library.DI;
using Redarbor.GlobalConfiguration.Library;
using Redarbor.Kpi.Consumer.Abstractions.Configuration;
using Redarbor.Kpi.Consumer.Abstractions.Enums;
using System.Configuration;

namespace Redarbor.Company.WebAPI.Configuration
{
    [RegisterConfiguration]
    public class RedarborKpiConsumerConfiguration : IRedarborKpiConsumerConfiguration
    {
        public string ApiRabbitMq { get; }

        public string ApiKpi { get; }

        public bool RegisterKpisEnabled { get; }

        public int IdApp { get; }

        public KpiProductEnum Product { get; }

        public RedarborKpiConsumerConfiguration(IGlobalConfigurationService globalConfigurationService)
        {
            ApiRabbitMq = globalConfigurationService.Current.GetApiEndPoint("Redarbor.RabbitMQ.WebAPI");
            ApiKpi = globalConfigurationService.Current.GetApiEndPoint("Redarbor.Kpi.WebAPI");
            RegisterKpisEnabled = true;

            int.TryParse(ConfigurationManager.AppSettings["APP_ID"], out int applicationId);
            IdApp = applicationId;
            Product = KpiProductEnum.JobAds;
        }
    }
}