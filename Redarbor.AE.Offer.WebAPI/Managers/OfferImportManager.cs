using Redarbor.AE.Offer.WebAPI.Mappings;
using Redarbor.AE.Offer.WebAPI.Models.OfferImport;
using Redarbor.Company.Contracts.ServiceLibrary;
using Redarbor.Master.Entities.Enums;
using Redarbor.Offer.Contracts.ServiceLibrary;
using Redarbor.Offer.Contracts.ServiceLibrary.DTO;
using Redarbor.Products.Contracts.ServiceLibrary;
using Redarbor.Professions.Contracts.ServiceLibrary;
using Redarbor.Professions.Contracts.ServiceLibrary.DTO;
using System.Linq;
using System.Threading.Tasks;

namespace Redarbor.AE.Offer.WebAPI.Managers
{
    public class OfferImportManager : IOfferImportManager
    {
        private readonly IOfferImportService _offerImportService;
        private readonly ICompanyService _companyService;
        private readonly ICompanyProductService _companyProductService;
        private readonly IProfessionService _professionService;
        private readonly ICompanyPreRegisterService _companyPreRegisterService;

        public OfferImportManager(IOfferImportService offerImportService, ICompanyService companyService, ICompanyProductService companyProductService, IProfessionService professionService, ICompanyPreRegisterService companyPreRegisterService)
        {
            _offerImportService = offerImportService;
            _companyService = companyService;
            _companyProductService = companyProductService;
            _professionService = professionService;
            _companyPreRegisterService = companyPreRegisterService;
        }

        public async Task<OfferImportPreRegisterResponseModel> ImportFreemiumOffers(OfferImportPreRegisterModel model, int idOrigin)
        {
            var company = await _companyService.GetByPKAsync(new Master.Entities.Searchs.CompanySearchSpecifications(model.IdPortal, model.IdCompany));

            if (company == null || company.Id <= 0)
            {
                return new OfferImportPreRegisterResponseModel($"Company not found in portal {model.IdPortal}");
            }

            var companyProduct = await _companyProductService.GetFreemiumProduct(model.IdCompany, model.IdPortal);

            if (companyProduct == null || companyProduct.Id <= 0)
            {
                return new OfferImportPreRegisterResponseModel($"Company product not found in portal {model.IdPortal}");
            }

            SetIdCargo(model);

            _companyService.UpdateStatusImportOffer(model.IdCompany, model.IdPortal, (short)CompanyImportOfferStatusEnum.StartMigration);
            _companyPreRegisterService.UpdateStatusImporOfferFromCompanyPreRegister(model.IdCompany, model.IdPortal, CompanyImportOfferStatusEnum.StartMigration);


            var result = _offerImportService.ImportFreemiumOffers(new OfferImportPreRegisterDTO(model.IdPortal, model.IdCompany, model.Offers.ToOfferImportDTOList(),
                                                                            (int)ProductEnum.FreemiumService, companyProduct.Id, (short)ProductGroupsEnum.Freemium,
                                                                            (short)ProductClassEnum.Freemium, (int)company.User.Id, (short)UserRoleEnum.ADMINISTRADOR_PRINCIPAL, idOrigin));


            if (result == null 
                || !result.Any())
            {
                _companyService.UpdateStatusImportOffer(model.IdCompany, model.IdPortal, (short)CompanyImportOfferStatusEnum.Pending);
                _companyPreRegisterService.UpdateStatusImporOfferFromCompanyPreRegister(model.IdCompany, model.IdPortal, CompanyImportOfferStatusEnum.Pending);
                return new OfferImportPreRegisterResponseModel("Error importing offers");
            }

            _companyService.UpdateStatusImportOffer(model.IdCompany, model.IdPortal, (short)CompanyImportOfferStatusEnum.Migrated);
            _companyPreRegisterService.UpdateStatusImporOfferFromCompanyPreRegister(model.IdCompany, model.IdPortal, CompanyImportOfferStatusEnum.Migrated);

            return new OfferImportPreRegisterResponseModel(result.ToOfferImportResponseModelList());
        }

        private void SetIdCargo(OfferImportPreRegisterModel model)
        {
            Parallel.ForEach(model.Offers, async offer =>
            {
                if (offer.IdCargo <= 0)
                {
                    if (!string.IsNullOrEmpty(offer.Cargo) && offer.IdCategory > 0)
                    {
                        offer.IdCargo = await _professionService.InsertOrUpdateProfessionAsync(new InsertProfessionDTO(offer.Cargo, model.IdPortal, (short)ApplicationEnum.AreaEmpresa, offer.IdCategory));
                    }
                }
            });
        }
    }
}