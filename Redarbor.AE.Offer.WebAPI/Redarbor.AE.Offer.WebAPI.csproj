<Project ToolsVersion="15.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props" Condition="Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <LangVersion>preview</LangVersion>
    <ProjectGuid>{7F0801D8-F743-4A2F-85A1-718C39E402C0}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Redarbor.AE.Offer.WebAPI</RootNamespace>
    <AssemblyName>Redarbor.AE.Offer.WebAPI</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <UseIISExpress>true</UseIISExpress>
    <Use64BitIISExpress />
    <IISExpressSSLPort>44360</IISExpressSSLPort>
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Autofac, Version=*******, Culture=neutral, PublicKeyToken=17863af14b0044da, processorArchitecture=MSIL">
      <HintPath>..\packages\Autofac.4.8.0\lib\net45\Autofac.dll</HintPath>
    </Reference>
    <Reference Include="Enyim.Caching, Version=2.16.0.0, Culture=neutral, PublicKeyToken=cec98615db04012e, processorArchitecture=MSIL">
      <HintPath>..\packages\EnyimMemcached.2.16.0\lib\net35\Enyim.Caching.dll</HintPath>
    </Reference>
    <Reference Include="log4net, Version=2.0.8.0, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a, processorArchitecture=MSIL">
      <HintPath>..\packages\log4net.2.0.8\lib\net45-full\log4net.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Http.Abstractions, Version=2.2.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Http.Abstractions.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Http.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Http.Features, Version=2.2.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Http.Features.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Http.Features.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=8.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.8.0.0\lib\net462\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.Extensions.Configuration, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Configuration.3.1.6\lib\netstandard2.0\Microsoft.Extensions.Configuration.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Configuration.Abstractions.5.0.0\lib\net461\Microsoft.Extensions.Configuration.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration.Binder, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Configuration.Binder.3.1.6\lib\netstandard2.0\Microsoft.Extensions.Configuration.Binder.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection, Version=8.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.DependencyInjection.8.0.0\lib\net462\Microsoft.Extensions.DependencyInjection.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection.Abstractions, Version=8.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.DependencyInjection.Abstractions.8.0.0\lib\net462\Microsoft.Extensions.DependencyInjection.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.FileProviders.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.FileProviders.Abstractions.5.0.0\lib\net461\Microsoft.Extensions.FileProviders.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Hosting.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Hosting.Abstractions.5.0.0\lib\net461\Microsoft.Extensions.Hosting.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Logging.Abstractions.3.1.6\lib\netstandard2.0\Microsoft.Extensions.Logging.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.ObjectPool, Version=********, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.ObjectPool.5.0.11\lib\net461\Microsoft.Extensions.ObjectPool.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Primitives, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Primitives.5.0.0\lib\net461\Microsoft.Extensions.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Net.Http.Headers, Version=1.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Net.Http.Headers.1.0.0\lib\netstandard1.1\Microsoft.Net.Http.Headers.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Web.Infrastructure, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Web.Infrastructure.1.0.0.0\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=1*******, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="RabbitMQ.Client, Version=6.0.0.0, Culture=neutral, PublicKeyToken=89e7d7c5feba84ce, processorArchitecture=MSIL">
      <HintPath>..\packages\RabbitMQ.Client.6.2.2\lib\net461\RabbitMQ.Client.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Api.Contracts, Version=3.0.4.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Api.Contracts.3.0.4\lib\netstandard2.0\Redarbor.Api.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Api.Impl, Version=3.1.4.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Api.Impl.3.1.4\lib\netstandard2.0\Redarbor.Api.Impl.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.AuditApiRequests.Common.Library, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.AuditApiRequests.Common.Library.3.0.0\lib\netstandard2.0\Redarbor.AuditApiRequests.Common.Library.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.AuditApiRequests.NetFramework.Library, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.AuditApiRequests.NetFramework.Library.3.0.0\lib\net461\Redarbor.AuditApiRequests.NetFramework.Library.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Cache, Version=1.3.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Cache.2.0.2\lib\netstandard2.0\Redarbor.Cache.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Cache.Disk, Version=1.3.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Cache.Disk.2.0.1\lib\netstandard2.0\Redarbor.Cache.Disk.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Cache.Runtime, Version=1.3.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Cache.Runtime.2.0.1\lib\net461\Redarbor.Cache.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Dictionaries.Consumer, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Dictionaries.Consumer.4.6.2\lib\netstandard2.0\Redarbor.Dictionaries.Consumer.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Encryption.Contracts, Version=1.0.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Encryption.Contracts.1.0.1\lib\netstandard2.0\Redarbor.Encryption.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Encryption.Impl, Version=1.0.2.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Encryption.Impl.1.0.2\lib\netstandard2.0\Redarbor.Encryption.Impl.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Extensions.NetStandard, Version=1.0.11.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Extensions.NetStandard.1.0.11\lib\netstandard2.0\Redarbor.Extensions.NetStandard.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.GlobalConfiguration.Library, Version=1.1.3.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.GlobalConfiguration.Library.1.1.3\lib\netstandard2.0\Redarbor.GlobalConfiguration.Library.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.KillerQuestions.Consumer, Version=1.0.3.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.KillerQuestions.Consumer.1.0.3\lib\netstandard2.0\Redarbor.KillerQuestions.Consumer.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Kpi.Consumer, Version=3.2.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Kpi.Consumer.3.2.0\lib\netstandard2.0\Redarbor.Kpi.Consumer.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Kpi.Consumer.Abstractions, Version=3.2.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Kpi.Consumer.Abstractions.3.2.1\lib\netstandard2.0\Redarbor.Kpi.Consumer.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Offer.API.Consumer, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Offer.API.Consumer.3.7.8\lib\netstandard2.0\Redarbor.Offer.API.Consumer.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.RabbitMQ, Version=1.0.0.17, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.RabbitMQ.1.0.0.17\lib\netstandard2.0\Redarbor.RabbitMQ.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.RabbitMQ.Abstractions, Version=1.0.0.17, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.RabbitMQ.Abstractions.1.0.0.17\lib\netstandard2.0\Redarbor.RabbitMQ.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.RabbitMQ.Extensions, Version=1.0.0.17, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.RabbitMQ.Extensions.1.0.0.17\lib\netstandard2.0\Redarbor.RabbitMQ.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.RabbitMQ.Model, Version=1.0.0.17, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.RabbitMQ.Model.1.0.0.17\lib\netstandard2.0\Redarbor.RabbitMQ.Model.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Tools.Exceptions.Consumer, Version=2.1.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Tools.Exceptions.Consumer.2.1.0\lib\netstandard2.0\Redarbor.Tools.Exceptions.Consumer.dll</HintPath>
    </Reference>
    <Reference Include="Swashbuckle.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=cd1bb07a5ac7c7bc, processorArchitecture=MSIL">
      <HintPath>..\packages\Swashbuckle.Core.5.6.0\lib\net40\Swashbuckle.Core.dll</HintPath>
    </Reference>
    <Reference Include="Swashbuckle.Examples, Version=4.1.0.0, Culture=neutral, PublicKeyToken=aa1e9c5053bfbe95, processorArchitecture=MSIL">
      <HintPath>..\packages\Swashbuckle.Examples.4.1.0\lib\net40\Swashbuckle.Examples.dll</HintPath>
    </Reference>
    <Reference Include="System.Buffers, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.NonGeneric, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Collections.NonGeneric.4.0.1\lib\net46\System.Collections.NonGeneric.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Annotations, Version=4.2.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ComponentModel.Annotations.4.7.0\lib\net461\System.ComponentModel.Annotations.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.ComponentModel.Primitives, Version=4.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ComponentModel.Primitives.4.1.0\lib\net45\System.ComponentModel.Primitives.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.ComponentModel.TypeConverter, Version=4.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ComponentModel.TypeConverter.4.1.0\lib\net462\System.ComponentModel.TypeConverter.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Configuration.ConfigurationManager, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Configuration.ConfigurationManager.4.7.0\lib\net461\System.Configuration.ConfigurationManager.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.OracleClient" />
    <Reference Include="System.Diagnostics.DiagnosticSource, Version=4.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.DiagnosticSource.4.5.1\lib\net46\System.Diagnostics.DiagnosticSource.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Linq, Version=4.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Linq.4.1.0\lib\net463\System.Linq.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Linq.Expressions, Version=4.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Linq.Expressions.4.1.0\lib\net463\System.Linq.Expressions.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Memory, Version=4.0.1.2, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.5\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Net.Http.Formatting, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Client.5.2.5\lib\net45\System.Net.Http.Formatting.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection, Version=4.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Reflection.4.1.0\lib\net462\System.Reflection.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime, Version=4.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.4.1.0\lib\net462\System.Runtime.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Extensions, Version=4.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.Extensions.4.1.0\lib\net462\System.Runtime.Extensions.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices, Version=4.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.InteropServices.4.1.0\lib\net462\System.Runtime.InteropServices.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Runtime.Serialization.Primitives, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.Serialization.Primitives.4.1.1\lib\net46\System.Runtime.Serialization.Primitives.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security" />
    <Reference Include="System.Security.AccessControl, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.AccessControl.4.7.0\lib\net461\System.Security.AccessControl.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Permissions, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Permissions.4.7.0\lib\net461\System.Security.Permissions.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Principal.Windows, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Principal.Windows.4.7.0\lib\net461\System.Security.Principal.Windows.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.Text.Encodings.Web, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encodings.Web.7.0.0\lib\net462\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Json, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Json.7.0.3\lib\net462\System.Text.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Channels, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Channels.4.7.1\lib\net461\System.Threading.Channels.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Transactions" />
    <Reference Include="System.ValueTuple, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net47\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Helpers, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.9\lib\net45\System.Web.Helpers.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Core.5.2.5\lib\net45\System.Web.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http.WebHost, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.WebHost.5.2.5\lib\net45\System.Web.Http.WebHost.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Mvc, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.2.9\lib\net45\System.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Razor.3.2.9\lib\net45\System.Web.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.9\lib\net45\System.Web.WebPages.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.9\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.9\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="WebActivatorEx, Version=2.0.0.0, Culture=neutral, PublicKeyToken=7b26dc2a43f6a0d4, processorArchitecture=MSIL">
      <HintPath>..\packages\WebActivatorEx.2.0\lib\net40\WebActivatorEx.dll</HintPath>
    </Reference>
    <Reference Include="WindowsBase" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Microsoft.CodeDom.Providers.DotNetCompilerPlatform">
      <HintPath>..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\lib\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Global.asax" />
    <Content Include="Web.config" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="App_Start\SwaggerConfig.cs" />
    <Compile Include="App_Start\WebApiConfig.cs" />
    <Compile Include="Configuration\AuditApiRequestsConfiguration.cs" />
    <Compile Include="Configuration\DictionariesApiConfiguration.cs" />
    <Compile Include="Configuration\GlobalConfigurationConfiguration.cs" />
    <Compile Include="Configuration\KillerQuestionsApiConfiguration.cs" />
    <Compile Include="Configuration\RedarborCacheConfiguration.cs" />
    <Compile Include="Configuration\RedarborKpiConsumerConfiguration.cs" />
    <Compile Include="Constants\FieldLimitationConstants.cs" />
    <Compile Include="Constants\RegularExpressionsConstants.cs" />
    <Compile Include="Controllers\BaseController.cs" />
    <Compile Include="Controllers\HealthCheckController.cs" />
    <Compile Include="Controllers\OfferImportController.cs" />
    <Compile Include="Controllers\OffersToPandaPeController.cs" />
    <Compile Include="Controllers\ReasignOffersController.cs" />
    <Compile Include="Controllers\OfferActivateController.cs" />
    <Compile Include="Controllers\OfferDeactivateController.cs" />
    <Compile Include="Controllers\OfferHighlightedController.cs" />
    <Compile Include="Controllers\OfferPublishController.cs" />
    <Compile Include="Controllers\OfferUploadController.cs" />
    <Compile Include="Controllers\OfferUrgentController.cs" />
    <Compile Include="Enums\OfferActionEnum.cs" />
    <Compile Include="Controllers\OfferRenewController.cs" />
    <Compile Include="Enums\OfferErrorEnum.cs" />
    <Compile Include="Exceptions\CompuIntegratorIsNullException.cs" />
    <Compile Include="Filters\ValidateModelAttribute.cs" />
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="Helpers\Log4netTraceListener.cs" />
    <Compile Include="Helpers\ReflectionRegistrator.cs" />
    <Compile Include="Managers\IOfferImportManager.cs" />
    <Compile Include="Managers\IOfferPublishManager.cs" />
    <Compile Include="Managers\OfferImportManager.cs" />
    <Compile Include="Managers\OfferPublishManager.cs" />
    <Compile Include="Mappings\ErrorMapping.cs" />
    <Compile Include="Mappings\ErrorPublishMapping.cs" />
    <Compile Include="Mappings\OfferImportMapping.cs" />
    <Compile Include="Mappings\OfferMapping.cs" />
    <Compile Include="Models\HealthCheckResultModel.cs" />
    <Compile Include="Models\OfferActivate\SetActivateOfferModel.cs" />
    <Compile Include="Models\OfferDeactivate\SetDeactivateOfferModel.cs" />
    <Compile Include="Models\OfferError.cs" />
    <Compile Include="Models\OfferHighlighted\SetHighlightedOfferModel.cs" />
    <Compile Include="Models\OfferHighlighted\SetHighlightedOfferModelSample.cs" />
    <Compile Include="Models\OfferImport\LanguageImportPreRegisterModel.cs" />
    <Compile Include="Models\OfferImport\OfferImportModel.cs" />
    <Compile Include="Models\OfferImport\OfferImportResponseModel.cs" />
    <Compile Include="Models\OfferImport\OfferPreRegisterModelSample.cs" />
    <Compile Include="Models\OfferImport\OfferImportPreRegisterModel.cs" />
    <Compile Include="Models\OfferImport\OfferImportPreRegisterResponseModel.cs" />
    <Compile Include="Models\OfferPublishError.cs" />
    <Compile Include="Models\OfferPublish\InfoAboutErrorIdExternal.cs" />
    <Compile Include="Models\OfferPublish\OfferDriveLicenseModel.cs" />
    <Compile Include="Models\OfferPublish\OfferKillerQuestionDataModel.cs" />
    <Compile Include="Models\OfferPublish\OfferKillerQuestionDataRequestModel.cs" />
    <Compile Include="Models\OfferPublish\OfferKillerQuestionModel.cs" />
    <Compile Include="Models\OfferPublish\OfferKillerQuestionRequestModel.cs" />
    <Compile Include="Models\OfferPublish\OfferLanguageModel.cs" />
    <Compile Include="Models\OfferPublish\OfferLanguagesRequestModel.cs" />
    <Compile Include="Models\OfferPublish\OfferModel.cs" />
    <Compile Include="Models\OfferPublish\OfferPublishModel.cs" />
    <Compile Include="Models\OfferPublish\OfferPublishResponseModel.cs" />
    <Compile Include="Models\OfferPublish\OfferPublishSample.cs" />
    <Compile Include="Models\OfferPublish\OfferSkillModel.cs" />
    <Compile Include="Models\OfferRenew\OffersWithExpirationDateModel.cs" />
    <Compile Include="Models\OfferRenew\OffersRenewResponseModel.cs" />
    <Compile Include="Models\OfferRenew\OfferRenewResponseModel.cs" />
    <Compile Include="Models\OfferRenew\OffersRenewModelSample.cs" />
    <Compile Include="Models\OfferRenew\OffersRenewModel.cs" />
    <Compile Include="Models\OffersToPandape\OffersToPandapeModel.cs" />
    <Compile Include="Models\OffersToPandape\OffersToPandapeModelSample.cs" />
    <Compile Include="Models\OfferUpload\OfferUploadResponseModel.cs" />
    <Compile Include="Models\OfferUpload\SetUploadOfferModel.cs" />
    <Compile Include="Models\OfferUrgent\SetUrgentOfferModelSample.cs" />
    <Compile Include="Models\OfferUrgent\SetUrgentOfferModel.cs" />
    <Compile Include="Models\OfferVisualization\ChangeOfferVisualizationResponse.cs" />
    <Compile Include="Models\OfferRenew\OfferRenewModel.cs" />
    <Compile Include="Models\OfferRenew\OfferRenewModelSample.cs" />
    <Compile Include="Models\ResponseModel.cs" />
    <Compile Include="Models\ResponsePublishModel.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="ValidationAttributes\MaxNumberCharactersPerWordAttribute.cs" />
    <Compile Include="ValidationAttributes\NoEmojisAllowedAttribute.cs" />
    <Compile Include="ValidationAttributes\NoUrlAllowedAttribute.cs" />
    <Compile Include="ValidationAttributes\ShortGreaterThanAttribute.cs" />
    <Compile Include="ValidationAttributes\ShortLowerThanAttribute.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
    <None Include="Web.BETA.config" />
    <None Include="Web.PRODUCTION.config" />
    <None Include="Web.STAGING.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Readarbor.Master.Entities\Redarbor.Master.Entities.csproj">
      <Project>{72B93BA2-C177-4DDF-9F27-E08151323719}</Project>
      <Name>Redarbor.Master.Entities</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.CommentCv.Impl.ServiceLibrary\Redarbor.CommentCv.Impl.ServiceLibrary.csproj">
      <Project>{c29e2cde-54a5-4587-b24b-f35a48f9fe32}</Project>
      <Name>Redarbor.CommentCv.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Common.Entities\Redarbor.Common.Entities.csproj">
      <Project>{3DA7E832-19AE-4C35-85F8-5FAF5A4DABEB}</Project>
      <Name>Redarbor.Common.Entities</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Company.Contracts.ServiceLibrary\Redarbor.Company.Contracts.ServiceLibrary.csproj">
      <Project>{76DF1704-A73E-40ED-BBE7-69473D962CB1}</Project>
      <Name>Redarbor.Company.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Configuration.Library\Redarbor.Configuration.Library.csproj">
      <Project>{bd90ad61-e12d-4cbd-be3a-81a1a31fddb2}</Project>
      <Name>Redarbor.Configuration.Library</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Cache.Impl.ServiceLibrary\Redarbor.Core.Cache.Impl.ServiceLibrary.csproj">
      <Project>{6539a9a2-be8c-47d4-adbb-b0c863f49c54}</Project>
      <Name>Redarbor.Core.Cache.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Computrabajo.Contracts.ServiceLibrary\Redarbor.Core.Computrabajo.Contracts.ServiceLibrary.csproj">
      <Project>{9BE71737-B615-4227-BB44-C5ECEC3DCED3}</Project>
      <Name>Redarbor.Core.Computrabajo.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Computrabajo.Impl.ServiceLibrary\Redarbor.Core.Computrabajo.Impl.ServiceLibrary.csproj">
      <Project>{aa731e64-6689-4765-80ee-5beefec24849}</Project>
      <Name>Redarbor.Core.Computrabajo.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Counter.Contracts.ServiceLibrary\Redarbor.Core.Counters.Contracts.ServiceLibrary.csproj">
      <Project>{EEC65DB1-974D-4172-BA9A-735D63153264}</Project>
      <Name>Redarbor.Core.Counters.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Counter.Impl.ServiceLibrary\Redarbor.Core.Counters.Impl.ServiceLibrary.csproj">
      <Project>{451fa585-dde8-43e2-931d-9365af99ce6b}</Project>
      <Name>Redarbor.Core.Counters.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Kpi.Contracts.ServiceLibrary\Redarbor.Core.Kpi.Contracts.ServiceLibrary.csproj">
      <Project>{69FA56CC-33A8-445A-AA62-6BB70ED1BC70}</Project>
      <Name>Redarbor.Core.Kpi.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Kpi.Impl.ServiceLibrary\Redarbor.Core.Kpi.Impl.ServiceLibrary.csproj">
      <Project>{f773520f-2ef0-44d7-8878-28428e6218b5}</Project>
      <Name>Redarbor.Core.Kpi.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Mailing.Impl.ServiceLibrary\Redarbor.Core.Mailing.Impl.ServiceLibrary.csproj">
      <Project>{8e69ec0c-a7c0-4c5b-9388-d2ce82e36797}</Project>
      <Name>Redarbor.Core.Mailing.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Resolver.Contracts.ServiceLibrary\Redarbor.Core.Resolver.Contracts.ServiceLibrary.csproj">
      <Project>{27f82506-248d-4117-9dc1-2198a2187fbb}</Project>
      <Name>Redarbor.Core.Resolver.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Resolver.Impl.ServiceLibrary\Redarbor.Core.Resolver.Impl.ServiceLibrary.csproj">
      <Project>{62DB8E3D-0CBF-49BB-B06C-EB64709CF099}</Project>
      <Name>Redarbor.Core.Resolver.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Shared.Contracts.ServiceLibrary\Redarbor.Core.Shared.Contracts.ServiceLibrary.csproj">
      <Project>{21E3ABEA-6637-49C2-8684-E0F842DE8506}</Project>
      <Name>Redarbor.Core.Shared.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Shared.Impl.ServiceLibrary\Redarbor.Core.Shared.Impl.ServiceLibrary.csproj">
      <Project>{D6387058-EB7B-42F7-AA9A-9E73E2A217DD}</Project>
      <Name>Redarbor.Core.Shared.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Stack.Impl.ServiceLibrary\Redarbor.Core.Stack.Impl.ServiceLibrary.csproj">
      <Project>{7df7bbf8-215d-494a-bb30-9fd1e13ead8b}</Project>
      <Name>Redarbor.Core.Stack.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.TrackingActions.Impl.ServiceLibrary\Redarbor.Core.TrackingActions.Impl.ServiceLibrary.csproj">
      <Project>{219b0007-b45d-493e-af04-4ca73812f496}</Project>
      <Name>Redarbor.Core.TrackingActions.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Curriculum.Impl.ServiceLibrary\Redarbor.Curriculum.Impl.ServiceLibrary.csproj">
      <Project>{41f5aabf-63a7-465a-a698-0c18142c1095}</Project>
      <Name>Redarbor.Curriculum.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.DIRegister.AssemblyDiscovery.ServiceLibrary\Redarbor.DIRegister.AssemblyDiscovery.ServiceLibrary.csproj">
      <Project>{7db913eb-ab01-4a1a-9e8d-1f9d5f7e88cf}</Project>
      <Name>Redarbor.DIRegister.AssemblyDiscovery.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.DIRegister.Autofac.ServiceLibrary\Redarbor.DIRegister.Autofac.ServiceLibrary.csproj">
      <Project>{B9F54550-CF57-4E5D-8383-769DF8F18568}</Project>
      <Name>Redarbor.DIRegister.Autofac.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.DIRegister.WebAPI.ServiceLibrary\Redarbor.DIRegister.WebAPI.ServiceLibrary.csproj">
      <Project>{186226E3-FB61-4E6A-9DB8-226A477C85BE}</Project>
      <Name>Redarbor.DIRegister.WebAPI.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Invoice.Impl.ServiceLibrary\Redarbor.Invoice.Impl.ServiceLibrary.csproj">
      <Project>{2777dfe6-0a6c-4887-87b4-0418a7074ece}</Project>
      <Name>Redarbor.Invoice.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Master.Impl.ServiceLibrary\Redarbor.Master.Impl.ServiceLibrary.csproj">
      <Project>{3fec6bb8-e0b9-4783-8877-f4f79cc7d73b}</Project>
      <Name>Redarbor.Master.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Match.Impl.ServiceLibrary\Redarbor.Match.Impl.ServiceLibrary.csproj">
      <Project>{a45070d7-9e74-4142-bab7-f4087fc991a4}</Project>
      <Name>Redarbor.Match.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Offer.Contracts.ServiceLibrary\Redarbor.Offer.Contracts.ServiceLibrary.csproj">
      <Project>{5DF02FD7-ADC3-4AA2-B8AD-F5F2D42C506F}</Project>
      <Name>Redarbor.Offer.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Products.Contracts.ServiceLibrary\Redarbor.Products.Contracts.ServiceLibrary.csproj">
      <Project>{7E7DFD45-2A07-4347-AAD6-836B215F129B}</Project>
      <Name>Redarbor.Products.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Products.Impl.ServiceLibrary\Redarbor.Products.Impl.ServiceLibrary.csproj">
      <Project>{e53e025f-0bca-4e70-8efd-6c50ca9c246d}</Project>
      <Name>Redarbor.Products.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Professions.Contracts.ServiceLibrary\Redarbor.Professions.Contracts.ServiceLibrary.csproj">
      <Project>{739CD2CB-DB89-4E3E-BB9D-37D31713A273}</Project>
      <Name>Redarbor.Professions.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.RatingCv.Impl.ServiceLibrary\Redarbor.RatingCv.Impl.ServiceLibrary.csproj">
      <Project>{415ac519-9d3b-4d66-bf2c-da932b104bc8}</Project>
      <Name>Redarbor.RatingCv.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.User.Contracts.ServiceLibrary\Redarbor.User.Contracts.ServiceLibrary.csproj">
      <Project>{79E50E19-D3C9-4D95-890C-EC9E9B1A5B1C}</Project>
      <Name>Redarbor.User.Contracts.ServiceLibrary</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup />
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>55210</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>https://localhost:44360/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>Este proyecto hace referencia a los paquetes NuGet que faltan en este equipo. Use la restauración de paquetes NuGet para descargarlos. Para obtener más información, consulte http://go.microsoft.com/fwlink/?LinkID=322105. El archivo que falta es {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props'))" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>