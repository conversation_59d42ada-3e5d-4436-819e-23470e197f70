<?xml version="1.0" encoding="utf-8"?>

<configuration>
	<connectionStrings>
		<add name="Commondbconnectionstring" connectionString="server=dev-common-srv.ct.local;Uid=root;Persist Security Info=True;database=ct_comun;Pwd=*********$;port=3306;Convert Zero Datetime=True;ProcedureCacheSize=1000" providerName="MySql.Data.MySqlClient" />
		<add name="RepoAppSettingConnection" connectionString="server=dev-repo-settings.ct.local;port=3306;database=repo_settings;Uid=usr_ae;Pwd=************;PersistSecurityInfo=False;ConnectionTimeout=15;ConvertZeroDateTime=True;ProcedureCacheSize=100;logging=true;DefaultCommandTimeout=600;SslMode=none;CacheServerProperties=True;" />
	</connectionStrings>
	<appSettings>
		<add key="Environment" value="Staging" />
		<add key="BuildVersion" value="1.0.0" />
		<add key="APPLICATION_ID" value="Redarbor.AE.Offer.WebAPI" />
		<add key="ENVIRONMENT_FOLDER_PATH" value="C:\Redarbor" />
		<add key="APP_ID" value="2" />
		<!--For portalConfig-->
		<add key="APP_ID_API" value="153" />
		<!--For exceptions an so on..-->
		<add key="ExceptionApiEndPoint" value="https://api-tools-audit.computrabajo.com/" />
		<add key="AUDIT_API_REQUESTS_ENABLED" value="false" />
		<add key="AUDIT_API_REQUESTS_THROW_EXCEPTION_ORIGIN_NOT_FOUND" value="true" />
		<!-- SEPARAR POR ; -->
		<add key="AUDIT_API_REQUESTS_PATHS_TO_AUDIT" value="v1/offers" />
		<!--TIMELINE-->
		<add key="TIMELINE_URL_API" value="http://pre-api-timeline.ct.local/" />
		<!-- Add Redarbor.AppSettings.Tool-->
		<add key="APPSETTINGS_FOLDER" value="_resources/_appsettings" />
		<add key="APPSETTINGS_URL_API" value="http://pre-api-settings.ct.local" />
		<add key="APPSETTINGS_RECYCLE_TIME_IN_MINUTES" value="10" />
		<add key="APPSETTINGS_AMBITID" value="1" />		
		<!-- End Redarbor.AppSettings.Tool-->
		<add key="KINESIS_API_ENDPOINT" value="http://pre-api-kinesis.ct.local/" />
		<add key="RABBITMQ_API_ENDPOINT" value="http://pre-api-queues.ct.local/" />
		<add key="KINESIS_BY_RABBIT" value="true" />
		<add key="API_KILLERQUESTIONS_URL_API" value="http://localhost:8779/" />		
	</appSettings>
	<system.web>
		<compilation debug="true" targetFramework="4.7.2" />
		<httpRuntime targetFramework="4.7.2" />
	</system.web>
	<system.codedom>
		<compilers>
			<compiler language="c#;cs;csharp" extension=".cs" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.CSharpCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" warningLevel="4" compilerOptions="/langversion:default /nowarn:1659;1699;1701" />
			<compiler language="vb;vbs;visualbasic;vbscript" extension=".vb" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.VBCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" warningLevel="4" compilerOptions="/langversion:default /nowarn:41008 /define:_MYTYPE=\&quot;Web\&quot; /optionInfer+" />
		</compilers>
	</system.codedom>
	<runtime>
		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
			<dependentAssembly>
				<assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Text.Json" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Web.Http" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-5.2.5.0" newVersion="5.2.5.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Autofac" publicKeyToken="17863af14b0044da" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.8.0.0" newVersion="4.8.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Net.Http.Formatting" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-5.2.5.0" newVersion="5.2.5.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.ObjectPool" publicKeyToken="adb9793829ddae60" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-5.0.11.0" newVersion="5.0.11.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-13.0.0.0" newVersion="13.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Threading.Tasks.Extensions" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.2.0.1" newVersion="4.2.0.1" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.DependencyInjection.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-8.0.0.0" newVersion="8.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Bcl.AsyncInterfaces" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-8.0.0.0" newVersion="8.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Configuration.ConfigurationManager" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.Primitives" publicKeyToken="adb9793829ddae60" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-5.0.0.0" newVersion="5.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Text.Encodings.Web" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-7.0.0.0" newVersion="7.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.FileProviders.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-5.0.0.0" newVersion="5.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.Configuration.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-5.0.0.0" newVersion="5.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Web.Helpers" publicKeyToken="31bf3856ad364e35" />
				<bindingRedirect oldVersion="1.0.0.0-3.0.0.0" newVersion="3.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35" />
				<bindingRedirect oldVersion="1.0.0.0-3.0.0.0" newVersion="3.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35" />
				<bindingRedirect oldVersion="1.0.0.0-5.2.9.0" newVersion="5.2.9.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.0.1.2" newVersion="4.0.1.2" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.DependencyInjection" publicKeyToken="adb9793829ddae60" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-8.0.0.0" newVersion="8.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Enyim.Caching" publicKeyToken="cec98615db04012e" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-2.16.0.0" newVersion="2.16.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.Caching.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-3.1.8.0" newVersion="3.1.8.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.Options" publicKeyToken="adb9793829ddae60" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-3.1.8.0" newVersion="3.1.8.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.Logging.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-3.1.6.0" newVersion="3.1.6.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Threading.Channels" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.0.2.0" newVersion="4.0.2.0" />
			</dependentAssembly>
		</assemblyBinding>
	</runtime>
	<system.webServer>
		<handlers>
			<remove name="ExtensionlessUrlHandler-Integrated-4.0" />
			<remove name="OPTIONSVerbHandler" />
			<remove name="TRACEVerbHandler" />
			<remove name="WebDAV" />
			<add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="*" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" />
		</handlers>
		<modules runAllManagedModulesForAllRequests="true">
			<remove name="WebDAVModule" />
		</modules>
	</system.webServer>
	<system.diagnostics>
		<trace autoflush="true">
			<listeners>
				<add name="Log4netTraceListener" type="Redarbor.AE.Offer.WebAPI.Helpers.Log4netTraceListener, Redarbor.AE.Offer.WebAPI, Version=1.0.0.0, Culture=neutral" />
			</listeners>
		</trace>
	</system.diagnostics>
</configuration>