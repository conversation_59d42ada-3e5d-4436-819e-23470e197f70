using Redarbor.AE.Offer.WebAPI.Enums;
using Redarbor.AE.Offer.WebAPI.Models;
using Redarbor.Offer.Contracts.ServiceLibrary.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace Redarbor.AE.Offer.WebAPI.Mappings
{
    public static class ErrorPublishMapping
    {
        public static OfferPublishError ToResponsePublishError(this OfferIntegratorSaveErrorEnum error)
        {
            var offerError = OfferErrorEnum.GenericError;
            var errorDescription = "Unexpected error ocurred";

            switch (error)
            {
                case OfferIntegratorSaveErrorEnum.NotEnoughtCreditsCreateOffer:
                    offerError = OfferErrorEnum.NotEnoughCredits_Offer;
                    errorDescription = "There's not enough offer credits";
                    break;
                case OfferIntegratorSaveErrorEnum.NotEnoughtCreditsMakeOfferHighlighted:
                    offerError = OfferErrorEnum.NotEnoughCredits_Highlighted;
                    errorDescription = "There's not enough highligted credits";
                    break;
                case OfferIntegratorSaveErrorEnum.NotEnoughtCreditsMakeOfferUrgent:
                    offerError = OfferErrorEnum.NotEnoughCredits_Urgent;
                    errorDescription = "There's not enough urgent credits";
                    break;
                case OfferIntegratorSaveErrorEnum.NotEnoughtCreditsMakeOfferFlash:
                    offerError = OfferErrorEnum.NotEnoughCredits_Flash;
                    errorDescription = "There's not enough flash credits";
                    break;
                case OfferIntegratorSaveErrorEnum.NotEnoughtCreditsMakeOfferConfidential:
                    offerError = OfferErrorEnum.NotEnoughCredits_Hidden;
                    errorDescription = "There's not enough hidden credits";
                    break;
                case OfferIntegratorSaveErrorEnum.ErrorControlInsertUpdate:
                    offerError = OfferErrorEnum.ErrorControlOffer;
                    errorDescription = "Error in control offer.";
                    break;
                case OfferIntegratorSaveErrorEnum.ErrorLoadHasAtsCompany:
                    offerError = OfferErrorEnum.ErrorControlOffer;
                    errorDescription = "Error in method getHasAts for Company in BBDD.";
                    break;
                case OfferIntegratorSaveErrorEnum.ErrorCompanyNotExists:
                    offerError = OfferErrorEnum.ErrorControlOffer;
                    errorDescription = "Error company not exist.";
                    break;
            }

            return new OfferPublishError()
            {
                Error = offerError,
                Description = errorDescription
            };
        }

    }
}