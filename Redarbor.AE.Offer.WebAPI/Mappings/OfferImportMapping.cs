using Redarbor.AE.Offer.WebAPI.Models.OfferImport;
using Redarbor.Offer.Contracts.ServiceLibrary.DTO;
using System.Collections.Generic;
using System.Linq;

namespace Redarbor.AE.Offer.WebAPI.Mappings
{
    public static class OfferImportMapping
    {
        public static List<OfferImportResponseModel> ToOfferImportResponseModelList(this List<OfferImportResponseDTO> o)
        {
            return o.Select(s => new OfferImportResponseModel { 
                IdOffer = s.IdOffer,
                IdPreRegister = s.IdPreRegister,
                StatusId = s.StatusId            
            }).ToList();
        }

        public static List<OfferImportDTO> ToOfferImportDTOList(this List<OfferImportModel> o)
        {
            return o.Select(s=> new OfferImportDTO()
            {
                Description = s.Description,
                Cargo = s.Cargo,
                Complement = s.Complement,
                CreatedBy = s.CreatedBy,
                Disability = s.Disability,
                DriveLicensesList = s.DriveLicensesList,
                ExperienceYears = s.ExperienceYears,
                IdCategory = s.IdCategory,
                IdCity = s.IdCity,
                IdContractType = s.IdContractType,
                IdCountry = s.IdCountry,
                IdEmploymentType = s.IdEmploymentType,
                IdLocalization = s.IdLocalization,
                IdPostalCode = s.IdPostalCode,
                IdPreRegister = s.IdPreRegister,
                IdRolCreatedBy = s.IdRolCreatedBy,
                IdStudy = s.IdStudy,
                IdWorkplaceType = s.IdWorkplaceType,
                LanguagesObjectList = s.LanguagesObjectList.ToLanguageImportPreRegisterDTO(),
                MaxAge = s.MaxAge,
                MinAge = s.MinAge,
                ResidenceChange = s.ResidenceChange,
                Salary = s.Salary,
                ShowSalary = s.ShowSalary,
                SkillsList = s.SkillsList,
                StartTime = s.StartTime,
                Travel = s.Travel,
                Vacancies = s.Vacancies,
                IdCargo = s.IdCargo
            } ).ToList();
        }

        public static List<LanguageImportPreRegisterDTO> ToLanguageImportPreRegisterDTO(this List<LanguageImportPreRegisterModel> o)
        {
            if(o is null || o.Count == 0) return new List<LanguageImportPreRegisterDTO>();

            return o.Select(s => new LanguageImportPreRegisterDTO()
            {
                Language = s.Language,
                Level = s.Level
            }).ToList();
        }
    }
}