using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using System.Reflection;
using System.Threading.Tasks;
using System.Web.Http;

namespace Redarbor.AE.Offer.WebAPI.Controllers
{
    [RoutePrefix("")]
    public class HealthCheckController : ApiController
    {

        private readonly IExceptionPublisherService _exceptionPublisherService;

        public HealthCheckController(IExceptionPublisherService exceptionPublisherService)
        {
            _exceptionPublisherService = exceptionPublisherService;
        }

        [HttpGet]
        [Route("status")]
        public IHttpActionResult Status()
        {
            return Ok("OK");
        }

        [HttpGet]
        [Route("exception")]
        public async Task<IHttpActionResult> Exception(int idOrigin, string message)
        {
            try
            {
                throw new System.Exception(message);
            }
            catch (System.Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "HealthCheckController", "Exception");
            }

            return Ok();
        }
    }
}