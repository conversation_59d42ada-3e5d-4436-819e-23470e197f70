using Redarbor.AE.Offer.WebAPI.Constants;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Web;

namespace Redarbor.AE.Offer.WebAPI.Models.OfferRenew
{
    public class OfferRenewModel
    {
        [Required]
        [Range(FieldLimitationConstants.ONE, short.MaxValue)]
        public short PortalId { get; set; }

        [Required]
        [Range(FieldLimitationConstants.ONE, int.MaxValue)]
        public int UserId { get; set; }
    }
}