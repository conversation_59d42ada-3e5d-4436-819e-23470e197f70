
using Newtonsoft.Json;
using Redarbor.AE.Offer.WebAPI.Constants;
using System.ComponentModel.DataAnnotations;

namespace Redarbor.AE.Offer.WebAPI.Models.OfferUrgent
{
    public class SetUrgentOfferModel
    {
        [Required]
        public bool IsUrgent { get; set; }

        [Required]
        [Range(FieldLimitationConstants.ONE, short.MaxValue)]
        public short PortalId { get; set; }

        [JsonIgnore]
        public short IsUrgentToShort
        {
            get
            {
                return IsUrgent ? (short)1 : (short)0;
            }
        }
    }
}