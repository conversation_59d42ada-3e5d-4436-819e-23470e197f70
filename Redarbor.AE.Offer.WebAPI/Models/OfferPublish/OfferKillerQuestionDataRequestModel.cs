using Redarbor.AE.Offer.WebAPI.Constants;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Web;

namespace Redarbor.AE.Offer.WebAPI.Models
{
    public class OfferKillerQuestionDataRequestModel
    {
        [Required]
        [Range(FieldLimitationConstants.ZERO, short.MaxValue)]
        public short OrderKillerData { get; set; }

        [Required]
        [Range(FieldLimitationConstants.MIN_KILLER_QUESTION_SCORE, short.MaxValue)]
        public short Score { get; set; }

        [Required]
        [StringLength(FieldLimitationConstants.MAX_KILLER_QUESTION_TITLE, MinimumLength = FieldLimitationConstants.ONE)]
        public string Title { get; set; } = string.Empty;

        [Required]
        [StringLength(FieldLimitationConstants.MAX_KILLER_QUESTION_ANSWER, MinimumLength = FieldLimitationConstants.ONE)]
        public string Answer { get; set; } = string.Empty;
    }
}