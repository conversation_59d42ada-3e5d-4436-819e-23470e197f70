using Redarbor.AE.Offer.WebAPI.Configuration;
using Redarbor.AE.Offer.WebAPI.Filters;
using Redarbor.AuditApiRequests.Common.Library.Services;
using Redarbor.AuditApiRequests.NetFramework.Library.Handler;
using Redarbor.GlobalConfiguration.Library;
using System.Web.Http;

namespace Redarbor.AE.Offer.WebAPI.App_Start
{
    public static class WebApiConfig
    {
        public static void Register(HttpConfiguration config)
        {
            config.MapHttpAttributeRoutes();

            config.Filters.Add(new ValidateModelAttribute());

            config.Routes.MapHttpRoute(
                name: "DefaultApi",
                routeTemplate: "api/{controller}/{action}/{id}",
                defaults: new { id = RouteParameter.Optional }
            );

            // Audit Controllers Endpoints
            AuditApiRequestRepository auditApiRequestRepository = (AuditApiRequestRepository)config.DependencyResolver.GetService(typeof(IAuditApiRequestRepository));
            IGlobalConfigurationService apiConnectionService = (IGlobalConfigurationService)config.DependencyResolver.GetService(typeof(IGlobalConfigurationService));

            config.MessageHandlers.Add(new CustomLogHandler(auditApiRequestRepository, new AuditApiRequestsConfiguration(apiConnectionService)));
        }
    }
}