using Redarbor.PaymentsAutomationExternalConsumer.Contracts.ServiceLibrary.DTOs;
using Redarbor.PaymentsAutomationExternalConsumer.Contracts.ServiceLibrary.Enums;
using Product = Redarbor.Products.Contracts.ServiceLibrary;

namespace Redarbor.PaymentsAutomationExternalConsumer.Impl.ServiceLibrary.Mappers
{
    public static class RenewalMapper
    {
        public static CanSelfRenewingCTResultEnum ToCanSelfRenewing(this Product.Enums.CanSelfRenewingCTResultEnum c)
        {
            switch (c)
            { 
                case Product.Enums.CanSelfRenewingCTResultEnum.Ok:
                    return CanSelfRenewingCTResultEnum.Ok;

                case Product.Enums.CanSelfRenewingCTResultEnum.InvalidParams:
                    return CanSelfRenewingCTResultEnum.InvalidParams;

                case Product.Enums.CanSelfRenewingCTResultEnum.ProductDefinitionWitoutSelfRenewing:
                    return CanSelfRenewingCTResultEnum.ProductDefinitionWitoutSelfRenewing;

                case Product.Enums.CanSelfRenewingCTResultEnum.IsAExternalCustomerCProduct:
                    return CanSelfRenewingCTResultEnum.IsAExternalCustomerCProduct;

                case Product.Enums.CanSelfRenewingCTResultEnum.HasExternalCustomerProducts:
                    return CanSelfRenewingCTResultEnum.HasExternalCustomerProducts;

                case Product.Enums.CanSelfRenewingCTResultEnum.HasNextMembershipProducts:
                    return CanSelfRenewingCTResultEnum.HasNextMembershipProducts;

                default: 
                    return CanSelfRenewingCTResultEnum.MapperError;

            }
        }

        public static Product.DTO.SaveCompanyProductDTO ToSaveCompanyProductDTO(this SaveCompanyProductDTO s)
        {
            return new Product.DTO.SaveCompanyProductDTO
            {
                ComercialName = s.ComercialName,
                DateActivation = s.DateActivation,
                DateExpiration = s.DateExpiration,
                GroupId = s.GroupId,
                Id = s.Id,
                LimitationMultiplier = s.LimitationMultiplier,
                PortalId = s.PortalId,
                ProductTypeId = s.ProductTypeId,
                ServiceTypeId = s.ServiceTypeId,
                SubGroupId = s.SubGroupId,
                SelfRenewing = s.SelfRenewing,
                CompanyId = s.CompanyId
            };
        }
    }
}
