using Redarbor.PaymentsAutomationExternalConsumer.Contracts.ServiceLibrary;
using Redarbor.PaymentsAutomationExternalConsumer.Contracts.ServiceLibrary.DTOs;
using Redarbor.PaymentsAutomationExternalConsumer.Contracts.ServiceLibrary.Enums;
using Product = Redarbor.Products.Contracts.ServiceLibrary;
using System.Threading.Tasks;
using Redarbor.PaymentsAutomationExternalConsumer.Impl.ServiceLibrary.Mappers;

namespace Redarbor.PaymentsAutomationExternalConsumer.Impl.ServiceLibrary
{
    public class PaymentsAutomationRenewalService : IPaymentsAutomationRenewalService
    {
        private readonly Product.ICompanyProductService _companyProductService;

        public PaymentsAutomationRenewalService(Product.ICompanyProductService companyProductService)
        {
            _companyProductService = companyProductService;
        }

        public async Task<CanSelfRenewingCTResultEnum> CanSelfRenewingCTAsync(SearchByCompanyProductDTO dto)
        {
            if (dto == null || dto.CompanyProductId <= 0 || dto.CompanyId <= 0 || dto.PortalId <= 0)
                return CanSelfRenewingCTResultEnum.InvalidParams;

            var canSelfRenewingCT = (await _companyProductService.CanSelfRenewingCTAsync(new Product.DTO.SearchByCompanyProductDTO(dto.CompanyProductId, dto.CompanyId, dto.PortalId))).ToCanSelfRenewing();
            
            //To-do: ask to consumer

            return canSelfRenewingCT;
        }

        public async Task<bool?> SetSelfRenewingCTAsync(SaveCompanyProductDTO dto)
        {
            if (dto == null || dto.CompanyId <= 0 || dto.PortalId <= 0)
                return false;

            //To-do: logica de paymentAutomation en el caso de que se quiera poner a true el dto SelfRenewing

            return await _companyProductService.SetSelfRenewingCTAsync(dto.ToSaveCompanyProductDTO());
        }
    }
}
