using Redarbor.Master.Entities.PurchaseOperation;
using Redarbor.Payment.Contracts.ServiceLibrary.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Redarbor.Payment.Contracts.ServiceLibrary
{
    public interface ICancelPaymentService
    {
        ResponseModel CancelService(MultiPurchaseOperationEntity multiPurchaseOperation);
    }
}
