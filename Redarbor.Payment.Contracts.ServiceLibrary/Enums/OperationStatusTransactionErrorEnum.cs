using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Redarbor.Payment.Contracts.ServiceLibrary.Enums
{
    public enum OperationStatusTransactionErrorEnum
    {
            [Description("UNDEFINED")]
            UNDEFINED,
            [Description("ERROR")]
            NETWORK_FAILED,
            [Description("REFUSED")]
            NOT_MONEY,
            [Description("REFUSED")]
            CONTACT_BANK,
            [Description("REFUSED")]
            ANTIFRAUD,
            [Description("CARD BLOCKED")]
            CARD_FAILED,
            [Description("REFUSED")]
            BAD_TRANSACTION,
            [Description("RESOURCE NOT EXISTS")]
            NOT_FOUND
        
    }
}
