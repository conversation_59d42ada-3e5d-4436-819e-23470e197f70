using Microsoft.Extensions.Configuration;
using Redarbor.AE.Product.WebAPI.Pandape.E2ETests.Enums;
using Redarbor.Product.API.Consumer;
using Redarbor.Product.API.Consumer.Enums;
using Redarbor.Product.API.Consumer.Models.CreditDistribution;
using System.Net;
using System.Text.Json;

namespace Redarbor.AE.Product.WebAPI.Pandape.E2ETests.CreditsDistributionControllerTests
{
    public class GetTests : IClassFixture<TestsFixture>
    {
        private readonly short _portalId = 1;
        private readonly int _companyId = 1;
        private readonly int _userId = 1;
        private readonly TestsFixture _fixture;
        const int ID_ORIGIN = 1;
        const int TIMEOUT = 5;

        public GetTests(TestsFixture fixture)
        {
            _fixture = fixture;
            _companyId = _fixture.Configuration.GetValue<int>("IdCompany");
            _userId = _fixture.Configuration.GetValue<int>("IdUser");
            _portalId = _fixture.Configuration.GetValue<short>("IdPortal");
            _fixture.SetParamsEndpoint(new Models.ParamEndpointModel(URLs.CreditDistribution.Get(_fixture.BaseAddress, _companyId, _portalId, _userId, ProductAmbitEnum.Offer, ApiVersion.v1, 0), TIMEOUT));
        }

        [Fact(DisplayName = "GET /company/{companyId}/creditsdistribution (Get_WithoutIdOrigin_ReturnsBadRequest) - debería devolver 400 BadRequest")]
        [Trait("Category", "E2E")]
        public async Task Get_WithoutIdOrigin_ReturnsBadRequest()
        {
            if (!await CheckEndPoint())
                return;

            try
            {
                var response = await _fixture.ApiClient.GetAsync(URLs.CreditDistribution.Get(_fixture.BaseAddress, _companyId, _portalId, _userId, ProductAmbitEnum.Offer, ApiVersion.v1, 0));
                Assert.Equal(HttpStatusCode.BadRequest, response.StatusCode);
            }
            catch (HttpRequestException ex)
            {
                Assert.Fail($"Fallo al llamar a POST /company/{_companyId}/creditsdistribution: {ex.Message}");
            }
        }

        [Fact(DisplayName = "GET /company/{companyId}/creditsdistribution (Get_WithoutCompanyId_ReturnsBadRequest) - debería devolver 400 BadRequest")]
        [Trait("Category", "E2E")]
        public async Task Get_WithoutCompanyId_ReturnsBadRequest()
        {
            if (!await CheckEndPoint())
                return;

            try
            {
                var response = await _fixture.ApiClient.GetAsync(URLs.CreditDistribution.Get(_fixture.BaseAddress, 0, _portalId, _userId, ProductAmbitEnum.Offer, ApiVersion.v1, ID_ORIGIN));
                Assert.Equal(HttpStatusCode.BadRequest, response.StatusCode);
            }
            catch (HttpRequestException ex)
            {
                Assert.Fail($"Fallo al llamar a POST /company/{_companyId}/creditsdistribution: {ex.Message}");
            }
        }

        [Fact(DisplayName = "GET /company/{companyId}/creditsdistribution (Get_WithoutPortalId_ReturnsBadRequest) - debería devolver 400 BadRequest")]
        [Trait("Category", "E2E")]
        public async Task Get_WithoutPortalId_ReturnsBadRequest()
        {
            if (!await CheckEndPoint())
                return;

            try
            {
                var response = await _fixture.ApiClient.GetAsync(URLs.CreditDistribution.Get(_fixture.BaseAddress, _companyId, 0, _userId, ProductAmbitEnum.Offer, ApiVersion.v1, ID_ORIGIN));
                Assert.Equal(HttpStatusCode.BadRequest, response.StatusCode);
            }
            catch (HttpRequestException ex)
            {
                Assert.Fail($"Fallo al llamar a POST /company/{_companyId}/creditsdistribution: {ex.Message}");
            }
        }

        [Fact(DisplayName = "GET /company/{companyId}/creditsdistribution (Get_WithoutUserId_ReturnsBadRequest) - debería devolver 400 BadRequest")]
        [Trait("Category", "E2E")]
        public async Task Get_WithoutUserId_ReturnsBadRequest()
        {
            if (!await CheckEndPoint())
                return;

            try
            {
                var response = await _fixture.ApiClient.GetAsync(URLs.CreditDistribution.Get(_fixture.BaseAddress, _companyId, _portalId, 0, ProductAmbitEnum.Offer, ApiVersion.v1, ID_ORIGIN));
                Assert.Equal(HttpStatusCode.BadRequest, response.StatusCode);
            }
            catch (HttpRequestException ex)
            {
                Assert.Fail($"Fallo al llamar a POST /company/{_companyId}/creditsdistribution: {ex.Message}");
            }
        }

        [Fact(DisplayName = "GET /company/{companyId}/creditsdistribution (Get_ReturnsOk) - debería devolver 400 BadRequest")]
        [Trait("Category", "E2E")]
        public async Task Get_ReturnsOk()
        {
            if (!await CheckEndPoint())
                return;

            try
            {
                var response = await _fixture.ApiClient.GetAsync(URLs.CreditDistribution.Get(_fixture.BaseAddress, _companyId, _portalId, _userId, ProductAmbitEnum.Offer, ApiVersion.v1, ID_ORIGIN));
                Assert.Equal(HttpStatusCode.OK, response.StatusCode);
                var content = await response.Content.ReadAsStringAsync();
                Assert.False(string.IsNullOrWhiteSpace(content));
                AssertsForOk(content);
            }
            catch (HttpRequestException ex)
            {
                Assert.Fail($"Fallo al llamar a POST /company/{_companyId}/creditsdistribution: {ex.Message}");
            }
        }

        private static void AssertsForOk(string content)
        {
            var objectContent = JsonSerializer.Deserialize<CreditDistributionGetResponse>(content);
            Assert.NotNull(objectContent);           
            Assert.NotNull(objectContent.Users);
            Assert.NotEmpty(objectContent.Users);
            Assert.NotNull(objectContent.TotalUnits);
            Assert.True(objectContent.Feature == ProductAmbitEnum.Offer);
        }

        private async Task<bool> CheckEndPoint()
        {
            await _fixture.InizializateCheckEndPoint();

            if (_fixture.EndpointStatus != EndpointStatusEnum.IsAvailable)
            {
                if (_fixture.EndpointStatus == EndpointStatusEnum.Timeout)
                    Assert.Fail("El endPoint no se ejecuta debido a timeOut");
                else if (_fixture.EndpointStatus == EndpointStatusEnum.NotFound)
                    Assert.Fail("El endPoint no se ejecuta debido a NotFound");

                Assert.Fail("El endPoint no se ejecuta correctamente");
            }

            return true;
        }
    }
}