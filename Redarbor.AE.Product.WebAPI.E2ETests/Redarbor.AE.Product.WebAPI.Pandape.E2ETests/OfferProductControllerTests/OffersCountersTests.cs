using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Redarbor.AE.Product.WebAPI.Pandape.E2ETests.Enums;
using Redarbor.Product.API.Consumer;
using Redarbor.Product.API.Consumer.Models.OfferCounters;
using System.Net;
using System.Text.Json;

namespace Redarbor.AE.Product.WebAPI.Pandape.E2ETests.OfferProductControllerTests
{
    public class OffersCountersTests : IClassFixture<TestsFixture>
    {
        private readonly short _portalId = 1;
        private readonly int _companyId = 1;
        private readonly int _userId = 1;
        private readonly TestsFixture _fixture;
        const int ID_ORIGIN = 1;
        const int TIMEOUT = 5;

        public OffersCountersTests(TestsFixture fixture)
        {
            _fixture = fixture;
            _companyId = _fixture.Configuration.GetValue<int>("IdCompany");
            _userId = _fixture.Configuration.GetValue<int>("IdUser");
            _portalId = _fixture.Configuration.GetValue<short>("IdPortal");
            _fixture.SetParamsEndpoint(new Models.ParamEndpointModel(URLs.Product.OfferCounters(_fixture.BaseAddress, Redarbor.Product.API.Consumer.Enums.ApiVersion.v1, ID_ORIGIN), TIMEOUT, new OfferCountersRequest(_companyId,0,_portalId),  Enums.HttpMethodsEnum.Post));
        }

        [Fact(DisplayName = "POST /offers/counters (OffersCounters_WithoutIdOrigin_ReturnsBadRequest) - debería devolver 400 BadRequest")]
        [Trait("Category", "E2E")]
        public async Task OffersCounters_WithoutIdOrigin_ReturnsBadRequest()
        {
            if (!await CheckEndPoint())
                return;

            try
            {                                
                var response = await _fixture.ApiClient.PostAsync(URLs.Product.OfferCounters(_fixture.BaseAddress, Redarbor.Product.API.Consumer.Enums.ApiVersion.v1, 0), new StringContent(System.Text.Json.JsonSerializer.Serialize(new OfferCountersRequest(_companyId, _userId, _portalId)), System.Text.Encoding.UTF8, "application/json") );
                Assert.Equal(HttpStatusCode.BadRequest, response.StatusCode);
            }
            catch (HttpRequestException ex)
            {
                Assert.Fail($"Fallo al llamar a POST /offers/counters: {ex.Message}");
            }
        }

        [Fact(DisplayName = "POST /offers/counters (OffersCounters_WithoutIdPortal_ReturnsBadRequest) - debería devolver 400 BadRequest")]
        [Trait("Category", "E2E")]
        public async Task OffersCounters_WithoutIdPortal_ReturnsBadRequest()
        {
            if (!await CheckEndPoint())
                return;

            try
            {
                var response = await _fixture.ApiClient.PostAsync(URLs.Product.OfferCounters(_fixture.BaseAddress, Redarbor.Product.API.Consumer.Enums.ApiVersion.v1, ID_ORIGIN), new StringContent(System.Text.Json.JsonSerializer.Serialize(new OfferCountersRequest(_companyId, _userId, 0)), System.Text.Encoding.UTF8, "application/json"));
                Assert.Equal(HttpStatusCode.BadRequest, response.StatusCode);
            }
            catch (HttpRequestException ex)
            {
                Assert.Fail($"Fallo al llamar a POST /offers/counters: {ex.Message}");
            }
        }

        [Fact(DisplayName = "POST /offers/counters (OffersCounters_WithoutIdCompany_ReturnsBadRequest) - debería devolver 400 BadRequest")]
        [Trait("Category", "E2E")]
        public async Task OffersCounters_WithoutIdCompany_ReturnsBadRequest()
        {
            if (!await CheckEndPoint())
                return;

            try
            {
                var response = await _fixture.ApiClient.PostAsync(URLs.Product.OfferCounters(_fixture.BaseAddress, Redarbor.Product.API.Consumer.Enums.ApiVersion.v1, ID_ORIGIN), new StringContent(System.Text.Json.JsonSerializer.Serialize(new OfferCountersRequest(0, _userId, _portalId)), System.Text.Encoding.UTF8, "application/json"));
                Assert.Equal(HttpStatusCode.BadRequest, response.StatusCode);
            }
            catch (HttpRequestException ex)
            {
                Assert.Fail($"Fallo al llamar a POST /offers/counters: {ex.Message}");
            }
        }

        [Fact(DisplayName = "POST /offers/counters (OffersCounters_WithoutIdUser_ReturnsBadRequest) - debería devolver 400 BadRequest")]
        [Trait("Category", "E2E")]
        public async Task OffersCounters_WithoutIdUser_ReturnsBadRequest()
        {
            if (!await CheckEndPoint())
                return;

            try
            {
                var response = await _fixture.ApiClient.PostAsync(URLs.Product.OfferCounters(_fixture.BaseAddress, Redarbor.Product.API.Consumer.Enums.ApiVersion.v1, ID_ORIGIN), new StringContent(System.Text.Json.JsonSerializer.Serialize(new OfferCountersRequest(_companyId, 0, _portalId)), System.Text.Encoding.UTF8, "application/json"));
                Assert.Equal(HttpStatusCode.BadRequest, response.StatusCode);
            }
            catch (HttpRequestException ex)
            {
                Assert.Fail($"Fallo al llamar a POST /offers/counters: {ex.Message}");
            }
        }

        [Fact(DisplayName = "POST /offers/counters (OffersCounters_ReturnsOk) - debería devolver 200 OK")]
        [Trait("Category", "E2E")]
        public async Task OffersCounters_ReturnsOk()
        {
            if (!await CheckEndPoint())
                return;

            try
            {
                var response = await _fixture.ApiClient.PostAsync(URLs.Product.OfferCounters(_fixture.BaseAddress, Redarbor.Product.API.Consumer.Enums.ApiVersion.v1, ID_ORIGIN), new StringContent(System.Text.Json.JsonSerializer.Serialize(new OfferCountersRequest(_companyId, _userId, _portalId)), System.Text.Encoding.UTF8, "application/json"));
                Assert.Equal(HttpStatusCode.OK, response.StatusCode);
                var content = await response.Content.ReadAsStringAsync();
                Assert.False(string.IsNullOrWhiteSpace(content));
                AssertsForOk(content);
            }
            catch (HttpRequestException ex)
            {
                Assert.Fail($"Fallo al llamar a POST /offers/counters: {ex.Message}");
            }
        }

        private static void AssertsForOk(string content)
        {
            var objectContent = JsonSerializer.Deserialize<OfferCountersResponse>(content);
            Assert.NotNull(objectContent);
            Assert.True(objectContent.IsSuccessful, "IsSuccessful debe ser true");
            Assert.True(objectContent.CompanyId > 0, "CompanyId debe ser superior a 0");
            Assert.True(objectContent.PortalId > 0, "PortalId debe ser superior a 0");
            Assert.NotNull(objectContent.Counters);
            Assert.NotEmpty(objectContent.Counters);
            Assert.NotNull(objectContent.ProductDescription);
            Assert.NotEmpty(objectContent.ProductDescription);
        }

        private async Task<bool> CheckEndPoint()
        {
            await _fixture.InizializateCheckEndPoint();

            if (_fixture.EndpointStatus != EndpointStatusEnum.IsAvailable)
            {
                if (_fixture.EndpointStatus == EndpointStatusEnum.Timeout)
                    Assert.Fail("El endPoint no se ejecuta debido a timeOut");
                else if (_fixture.EndpointStatus == EndpointStatusEnum.NotFound)
                    Assert.Fail("El endPoint no se ejecuta debido a timeOut");

                Assert.Fail("El endPoint no se ejecuta correctamente");
            }

            return true;
        }
    }
}
