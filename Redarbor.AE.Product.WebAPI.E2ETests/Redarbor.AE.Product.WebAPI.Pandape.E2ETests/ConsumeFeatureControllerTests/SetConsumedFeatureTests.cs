using Redarbor.AE.Product.WebAPI.Pandape.E2ETests.Enums;
using Redarbor.Product.API.Consumer;
using Redarbor.Product.API.Consumer.Enums;
using System.Net;

namespace Redarbor.AE.Product.WebAPI.Pandape.E2ETests.ConsumeFeatureControllerTests
{
    public class SetConsumedFeatureTests : IClassFixture<TestsFixture>
    {
        private readonly short _portalId = 1;
        private readonly int _companyId = 1;
        private readonly int _userId = 1;
        private readonly TestsFixture _fixture;
        const int ID_ORIGIN = 1;
        const int TIMEOUT = 5;

        public SetConsumedFeatureTests(TestsFixture fixture)
        {
            _fixture = fixture;
            _companyId = 12;
            _userId = 2;
            _portalId = 1;
            _fixture.SetParamsEndpoint(new Models.ParamEndpointModel(URLs.Product.SetConsumedFeature(_fixture.BaseAddress, ApiVersion.v1, 0), TIMEOUT, null, HttpMethodsEnum.Post));
        }

        [Fact(DisplayName = "POST /feature/setconsumed (ConsumeFeature_WithoutIdOrigin_ReturnsBadRequest) - debería devolver 400 BadRequest")]
        [Trait("Category", "E2E")]
        public async Task SetConsumedFeature_WithoutIdOrigin_ReturnsBadRequest()
        {
            if (!await CheckEndPoint())
                return;

            try
            {
                Console.WriteLine("Falta desarrollar el test de response OK debido a que produce cambios en bbdd y hay que escoger una empresa adecuadamente de pruebas");
                var response = await _fixture.ApiClient.PostAsync(URLs.Product.SetConsumedFeature(_fixture.BaseAddress, ApiVersion.v1, 0), new StringContent(""));
                Assert.Equal(HttpStatusCode.BadRequest, response.StatusCode);
            }
            catch (HttpRequestException ex)
            {
                Assert.Fail($"Fallo al llamar a POST /feature/setconsumed: {ex.Message}");
            }
        }

        private async Task<bool> CheckEndPoint()
        {
            await _fixture.InizializateCheckEndPoint();

            if (_fixture.EndpointStatus != EndpointStatusEnum.IsAvailable)
            {
                if (_fixture.EndpointStatus == EndpointStatusEnum.Timeout)
                    Assert.Fail("El endPoint no se ejecuta debido a timeOut");
                else if (_fixture.EndpointStatus == EndpointStatusEnum.NotFound)
                    Assert.Fail("El endPoint no se ejecuta debido a NotFound");

                Assert.Fail("El endPoint no se ejecuta correctamente");
            }

            return true;
        }
    }
}