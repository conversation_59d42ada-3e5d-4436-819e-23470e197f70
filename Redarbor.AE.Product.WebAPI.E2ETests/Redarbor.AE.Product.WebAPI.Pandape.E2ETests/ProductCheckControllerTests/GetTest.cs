using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Redarbor.AE.Product.WebAPI.Pandape.E2ETests;
using Redarbor.AE.Product.WebAPI.Pandape.E2ETests.Enums;
using Redarbor.Product.API.Consumer;
using Redarbor.Product.API.Consumer.Models.CompanyProducts;
using System.Net;
using System.Text.Json;

namespace Redarbor.AE.Product.WebAPI.E2ETests.ProductCheckControllerTests
{
    public class GetTest : IClassFixture<TestsFixture>
    {
        private readonly int _companyId = 1;
        private readonly short _portalId = 1;
        private readonly TestsFixture _fixture;
        const int ID_ORIGIN = 1;
        const int TIMEOUT = 5;

        public GetTest(TestsFixture fixture)
        {
            _fixture = fixture;
            _companyId = _fixture.Configuration.GetValue<int>("IdCompany");
            _portalId = _fixture.Configuration.GetValue<short>("IdPortal");
            _fixture.SetParamsEndpoint(new Pandape.E2ETests.Models.ParamEndpointModel(URLs.Product.GetActiveCompanyProducts(_fixture.BaseAddress, Redarbor.Product.API.Consumer.Enums.ApiVersion.v1, 0, _portalId, ID_ORIGIN), TIMEOUT));
        }

        [Fact(DisplayName = "GET /company/get (Get_Without_OriginId_ReturnsBadRequest) - debería devolver 400 BadRequest")]
        [Trait("Category", "E2E")]
        public async Task Get_Without_OriginId_ReturnsBadRequest()
        {
            if (!await CheckEndPoint())
                return;

            try
            {
                var response = await _fixture.ApiClient.GetAsync(URLs.Product.GetActiveCompanyProducts(_fixture.BaseAddress, Redarbor.Product.API.Consumer.Enums.ApiVersion.v1, _companyId, _portalId, 0));
                Assert.Equal(HttpStatusCode.BadRequest, response.StatusCode);
            }
            catch (HttpRequestException ex)
            {
                Assert.Fail($"Fallo al llamar a /company/get: {ex.Message}");
            }
        }

        [Fact(DisplayName = "GET /company/get (Get_Without_PortalId_ReturnsOkWithErrors) - debería devolver 200 OK with errors")]
        [Trait("Category", "E2E")]
        public async Task Get_Without_PortalId_ReturnsOkWithErrors()
        {
            if (!await CheckEndPoint())
                return;

            try
            {
                var response = await _fixture.ApiClient.GetAsync(URLs.Product.GetActiveCompanyProducts(_fixture.BaseAddress, Redarbor.Product.API.Consumer.Enums.ApiVersion.v1, _companyId, 0, ID_ORIGIN));
                Assert.Equal(HttpStatusCode.OK, response.StatusCode);
                var content = await response.Content.ReadAsStringAsync();
                Assert.False(string.IsNullOrWhiteSpace(content));
                AssertsForOkWithErrors(content);
            }
            catch (HttpRequestException ex)
            {
                Assert.Fail($"Fallo al llamar a /company/get: {ex.Message}");
            }
        }

        [Fact(DisplayName = "GET /company/get (Get_Without_CompanyId_ReturnsOkWithErrors) - debería devolver 200 OK with errors")]
        [Trait("Category", "E2E")]
        public async Task Get_Without_CompanyId_ReturnsOkWithErrors()
        {
            if (!await CheckEndPoint())
                return;

            try
            {
                var response = await _fixture.ApiClient.GetAsync(URLs.Product.GetActiveCompanyProducts(_fixture.BaseAddress, Redarbor.Product.API.Consumer.Enums.ApiVersion.v1, 0, _portalId, ID_ORIGIN));
                Assert.Equal(HttpStatusCode.OK, response.StatusCode);
                var content = await response.Content.ReadAsStringAsync();
                Assert.False(string.IsNullOrWhiteSpace(content));
                AssertsForOkWithErrors(content);
            }
            catch (HttpRequestException ex)
            {
                Assert.Fail($"Fallo al llamar a /company/get: {ex.Message}");
            }
        }

        [Fact(DisplayName = "GET /company/get (Get_ReturnsOk) - debería devolver 200 OK")]
        [Trait("Category", "E2E")]
        public async Task Get_ReturnsOk()
        {
            if (!await CheckEndPoint())
                return;

            try
            {
                var response = await _fixture.ApiClient.GetAsync(URLs.Product.GetActiveCompanyProducts(_fixture.BaseAddress, Redarbor.Product.API.Consumer.Enums.ApiVersion.v1, _companyId, _portalId, ID_ORIGIN));
                Assert.Equal(HttpStatusCode.OK, response.StatusCode);
                var content = await response.Content.ReadAsStringAsync();
                Assert.False(string.IsNullOrWhiteSpace(content));
                AssertsForOk(content);
            }
            catch (HttpRequestException ex)
            {
                Assert.Fail($"Fallo al llamar a /company/get: {ex.Message}");
            }
        }

        private static void AssertsForOkWithErrors(string content)
        {
            var objectContent = JsonSerializer.Deserialize<CompanyProductResponse>(content);
            Assert.NotNull(objectContent);
            Assert.False(objectContent.IsSuccessful, "IsSuccessful debe ser false");
            Assert.NotNull(objectContent.Errors);
            Assert.NotEmpty(objectContent.Errors);
        }

        private static void AssertsForOk(string content)
        {
            var objectContent = JsonSerializer.Deserialize<CompanyProductResponse>(content);
            Assert.NotNull(objectContent);
            Assert.True(objectContent.IsSuccessful, "IsSuccessful debe ser true");
            Assert.True(objectContent.CompanyId > 0, "CompanyId debe ser superior a 0");
            Assert.True(objectContent.PortalId > 0, "PortalId debe ser superior a 0");
            Assert.NotNull(objectContent.CompanyProducts);
            Assert.NotEmpty(objectContent.CompanyProducts);
            Assert.NotNull(objectContent.ActiveProductsName);
            Assert.NotEmpty(objectContent.ActiveProductsName);

            objectContent.CompanyProducts.Should().OnlyContain(p => p.Id > 0, "todos los productos deben tener un identificador");
            objectContent.CompanyProducts.Should().OnlyContain(p => p.IdCompany > 0, "todos los productos deben tener un identificador para la empresa");
        }

        private async Task<bool> CheckEndPoint()
        {
            await _fixture.InizializateCheckEndPoint();

            if (_fixture.EndpointStatus != EndpointStatusEnum.IsAvailable)
            {
                if (_fixture.EndpointStatus == EndpointStatusEnum.Timeout)
                    Assert.Fail("El endPoint no se ejecuta debido a timeOut");
                else if (_fixture.EndpointStatus == EndpointStatusEnum.NotFound)
                    Assert.Fail("El endPoint no se ejecuta debido a NotFound");

                Assert.Fail("El endPoint no se ejecuta correctamente");
            }

            return true;
        }
    }
}
