using Microsoft.Extensions.Configuration;
using Redarbor.AE.Product.WebAPI.Pandape.E2ETests.Enums;
using System.Net;

namespace Redarbor.AE.Product.WebAPI.Pandape.E2ETests.ProductCheckControllerTests
{
    public class GetByCustomerTest : IClassFixture<TestsFixture>
    {
        private readonly short _portalId = 1;
        private readonly long _customerId = 1;
        private readonly TestsFixture _fixture;
        const int ID_ORIGIN = 1;
        const string PREFIX_PATH = "v1/products/";
        const int TIMEOUT = 5;


        public GetByCustomerTest(TestsFixture fixture)
        {
            _fixture = fixture;
            _portalId = _fixture.Configuration.GetValue<short>("IdPortal");
            _customerId = _fixture.Configuration.GetValue<long>("IdCustomer");
            _fixture.SetParamsEndpoint(new Models.ParamEndpointModel($"{_fixture.BaseAddress}{PREFIX_PATH}customer/get?portalId=0&idOrigin={ID_ORIGIN}&customerId={_customerId}", TIMEOUT));
        }

        [Fact(DisplayName = "GET /customer/get (GetByCustomer_Without_OriginId_ReturnsBadRequest) - debería devolver 400 BadRequest")]
        [Trait("Category", "E2E")]
        public async Task GetByCustomer_Without_OriginId_ReturnsBadRequest()
        {
            if (!await CheckEndPoint())
                return;

            try
            {
                var response = await _fixture.ApiClient.GetAsync($"{_fixture.BaseAddress}{PREFIX_PATH}customer/get?portalId={_portalId}&customerId={_customerId}&idOrigin=0");
                Assert.Equal(HttpStatusCode.BadRequest, response.StatusCode);
            }
            catch (HttpRequestException ex)
            {
                Assert.Fail($"Fallo al llamar a /customer/get: {ex.Message}");
            }
        }

        [Fact(DisplayName = "GET /customer/get (GetByCustomer_Without_PortalId_ReturnsOkWithErrors) - debería devolver 200 OK with errors")]
        [Trait("Category", "E2E")]
        public async Task GetByCustomer_Without_PortalId_ReturnsOkWithErrors()
        {
            if (!await CheckEndPoint())
                return;

            try
            {
                var response = await _fixture.ApiClient.GetAsync($"{_fixture.BaseAddress}{PREFIX_PATH}customer/get?portalId=0&idOrigin={ID_ORIGIN}&customerId={_customerId}");
                Assert.Equal(HttpStatusCode.OK, response.StatusCode);
                var content = await response.Content.ReadAsStringAsync();
                Assert.False(string.IsNullOrWhiteSpace(content));
            }
            catch (HttpRequestException ex)
            {
                Assert.Fail($"Fallo al llamar a /customer/get: {ex.Message}");
            }
        }

        [Fact(DisplayName = "GET /customer/get (GetByCustomer_Without_CustomerId_ReturnsOkWithErrors) - debería devolver 200 OK with errors")]
        [Trait("Category", "E2E")]
        public async Task GetByCustomer_Without_CustomerId_ReturnsOkWithErrors()
        {
            if (!await CheckEndPoint())
                return;

            try
            {
                var response = await _fixture.ApiClient.GetAsync($"{_fixture.BaseAddress}{PREFIX_PATH}customer/get?portalId={_portalId}&customerId=0&idOrigin={ID_ORIGIN}");
                Assert.Equal(HttpStatusCode.OK, response.StatusCode);
                var content = await response.Content.ReadAsStringAsync();
                Assert.False(string.IsNullOrWhiteSpace(content));
            }
            catch (HttpRequestException ex)
            {
                Assert.Fail($"Fallo al llamar a /customer/get: {ex.Message}");
            }
        }

        [Fact(DisplayName = "GET /customer/get (GetByCustomer_ReturnsOk) - debería devolver 200 OK")]
        [Trait("Category", "E2E")]
        public async Task GetByCustomer_ReturnsOk()
        {
            if (!await CheckEndPoint())
                return;

            try
            {
                var response = await _fixture.ApiClient.GetAsync($"{_fixture.BaseAddress}{PREFIX_PATH}customer/get?portalId={_portalId}&idOrigin={ID_ORIGIN}&customerId={_customerId}");
                Assert.Equal(HttpStatusCode.OK, response.StatusCode);
                var content = await response.Content.ReadAsStringAsync();
                Assert.False(string.IsNullOrWhiteSpace(content));
            }
            catch (HttpRequestException ex)
            {
                Assert.Fail($"Fallo al llamar a /customer/get: {ex.Message}");
            }
        }

        private async Task<bool> CheckEndPoint()
        {
            await _fixture.InizializateCheckEndPoint();

            if (_fixture.EndpointStatus != EndpointStatusEnum.IsAvailable)
            {
                if (_fixture.EndpointStatus == EndpointStatusEnum.Timeout)
                    Assert.Fail("El endPoint no se ejecuta debido a timeOut");
                else if (_fixture.EndpointStatus == EndpointStatusEnum.NotFound)
                    Assert.Fail("El endPoint no se ejecuta debido a NotFound");

                Assert.Fail("El endPoint no se ejecuta correctamente");
            }

            return true;
        }
    }
}
