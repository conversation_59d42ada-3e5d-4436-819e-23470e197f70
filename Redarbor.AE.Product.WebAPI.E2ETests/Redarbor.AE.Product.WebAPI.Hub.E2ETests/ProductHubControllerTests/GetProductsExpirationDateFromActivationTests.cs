using Microsoft.Extensions.Configuration;
using Redarbor.AE.Product.WebAPI.Hub.E2ETests.Enums;
using Redarbor.Product.API.Consumer;
using Redarbor.Product.API.Consumer.Models.CompanyProducts;
using System.Net;
using System.Text.Json;

namespace Redarbor.AE.Product.WebAPI.Hub.E2ETests.ProductHubControllerTests
{
    public class GetProductsExpirationDateFromActivationTests : IClassFixture<TestsFixture>
    {
        private readonly int _companyId = 1;
        private readonly short _portalId = 1;
        private readonly int _customerId = 1;
        private readonly int _companyProductId = 1;
        private readonly int _productId = 1;
        private readonly int _temporalityId = 1;
        private readonly TestsFixture _fixture;
        const int ID_ORIGIN = 1;
        const short SB_ProductId = 6;
        const int TIMEOUT = 5;

        public GetProductsExpirationDateFromActivationTests(TestsFixture fixture)
        {
            _fixture = fixture;
            _companyId = _fixture.Configuration.GetValue<int>("IdCompany");
            _portalId = _fixture.Configuration.GetValue<short>("IdPortal");
            _customerId = _fixture.Configuration.GetValue<int>("IdCustomer");
            _companyProductId = _fixture.Configuration.GetValue<int>("IdCompanyProduct");
            _productId = _fixture.Configuration.GetValue<int>("IdProduct");
            _temporalityId = _fixture.Configuration.GetValue<int>("IdTemporality");
            _fixture.SetParamsEndpoint(new Models.ParamEndpointModel(ProductHubURLs.GetProductsExpirationDateFromActivation(_fixture.BaseAddress, _portalId, _productId, _temporalityId, DateTime.UtcNow, 0), TIMEOUT));
        }

        [Fact(DisplayName = "GET /hub/GetProductsExpirationDateFromActivation (GetProductsExpirationDateFromActivation_Without_OriginId_ReturnsBadRequest) - debería devolver 400 BadRequest")]
        [Trait("Category", "E2E")]
        public async Task GetProductsExpirationDateFromActivation_Without_OriginId_ReturnsBadRequest()
        {
            if (!await CheckEndPoint())
                return;

            try
            {
                var response = await _fixture.ApiClient.GetAsync(ProductHubURLs.GetProductsExpirationDateFromActivation(_fixture.BaseAddress, _portalId, _productId, _temporalityId, DateTime.UtcNow, 0));
                Assert.Equal(HttpStatusCode.BadRequest, response.StatusCode);
                var content = await response.Content.ReadAsStringAsync();
                Assert.False(string.IsNullOrWhiteSpace(content));
                AssertsForBadRequestWithErrors(content);
            }
            catch (HttpRequestException ex)
            {
                Assert.Fail($"Fallo al llamar a /hub/GetProductsExpirationDateFromActivation: {ex.Message}");
            }
        }

        [Fact(DisplayName = "GET /hub/GetProductsExpirationDateFromActivation (GetProductsExpirationDateFromActivation_Without_PortalId_ReturnsBadRequest) - debería devolver 400 BadRequest")]
        [Trait("Category", "E2E")]
        public async Task GetProductsExpirationDateFromActivation_Without_PortalId_ReturnsBadRequest()
        {
            if (!await CheckEndPoint())
                return;

            try
            {
                var response = await _fixture.ApiClient.GetAsync(ProductHubURLs.GetProductsExpirationDateFromActivation(_fixture.BaseAddress, 0, _productId, _temporalityId, DateTime.UtcNow, ID_ORIGIN));
                Assert.Equal(HttpStatusCode.BadRequest, response.StatusCode);
                var content = await response.Content.ReadAsStringAsync();
                Assert.False(string.IsNullOrWhiteSpace(content));
                AssertsForBadRequestWithErrors(content);
            }
            catch (HttpRequestException ex)
            {
                Assert.Fail($"Fallo al llamar a /hub/GetProductsExpirationDateFromActivation: {ex.Message}");
            }
        }

        [Fact(DisplayName = "GET /hub/GetProductsExpirationDateFromActivation (GetProductsExpirationDateFromActivation_Without_ProductId_ReturnsBadRequest) - debería devolver 400 BadRequest")]
        [Trait("Category", "E2E")]
        public async Task GetProductsExpirationDateFromActivation_Without_ProductId_ReturnsBadRequest()
        {
            if (!await CheckEndPoint())
                return;

            try
            {
                var response = await _fixture.ApiClient.GetAsync(ProductHubURLs.GetProductsExpirationDateFromActivation(_fixture.BaseAddress, _portalId, 0, _temporalityId, DateTime.UtcNow, ID_ORIGIN));
                Assert.Equal(HttpStatusCode.BadRequest, response.StatusCode);
                var content = await response.Content.ReadAsStringAsync();
                Assert.False(string.IsNullOrWhiteSpace(content));
                AssertsForBadRequestWithErrors(content);
            }
            catch (HttpRequestException ex)
            {
                Assert.Fail($"Fallo al llamar a /hub/GetProductsExpirationDateFromActivation: {ex.Message}");
            }
        }

        [Fact(DisplayName = "GET /hub/GetProductsExpirationDateFromActivation (GetProductsExpirationDateFromActivation_Without_TemporalityId_ReturnsBadRequest) - debería devolver 400 BadRequest")]
        [Trait("Category", "E2E")]
        public async Task GetProductsExpirationDateFromActivation_Without_TemporalityId_ReturnsBadRequest()
        {
            if (!await CheckEndPoint())
                return;

            try
            {
                var response = await _fixture.ApiClient.GetAsync(ProductHubURLs.GetProductsExpirationDateFromActivation(_fixture.BaseAddress, _portalId, _productId, 0, DateTime.UtcNow, ID_ORIGIN));
                Assert.Equal(HttpStatusCode.BadRequest, response.StatusCode);
                var content = await response.Content.ReadAsStringAsync();
                Assert.False(string.IsNullOrWhiteSpace(content));
                AssertsForBadRequestWithErrors(content);
            }
            catch (HttpRequestException ex)
            {
                Assert.Fail($"Fallo al llamar a /hub/GetProductsExpirationDateFromActivation: {ex.Message}");
            }
        }

        [Fact(DisplayName = "GET /hub/GetProductsExpirationDateFromActivation (GetProductsExpirationDateFromActivation_With_IncorrectDateTimeMinValue_ReturnsBadRequest) - debería devolver 200 OK")]
        [Trait("Category", "E2E")]
        public async Task GetProductsExpirationDateFromActivation_With_IncorrectDateTimeMinValue_ReturnsOk()
        {
            if (!await CheckEndPoint())
                return;

            try
            {
                var activationDate = DateTime.MinValue;
                var response = await _fixture.ApiClient.GetAsync(ProductHubURLs.GetProductsExpirationDateFromActivation(_fixture.BaseAddress, _portalId, _productId, _temporalityId, activationDate, ID_ORIGIN));
                Assert.Equal(HttpStatusCode.OK, response.StatusCode);
                var content = await response.Content.ReadAsStringAsync();
                Assert.False(string.IsNullOrWhiteSpace(content));
                AssertsForOk(content, activationDate);
            }
            catch (HttpRequestException ex)
            {
                Assert.Fail($"Fallo al llamar a /hub/GetProductsExpirationDateFromActivation: {ex.Message}");
            }
        }

        [Fact(DisplayName = "GET /hub/GetProductsExpirationDateFromActivation (GetProductsExpirationDateFromActivation_With_DateTime_ReturnsOk) - debería devolver 200 OK")]
        [Trait("Category", "E2E")]
        public async Task GetProductsExpirationDateFromActivation_With_DateTime_ReturnsOk()
        {
            if (!await CheckEndPoint())
                return;

            try
            {
                var activationDate = DateTime.UtcNow;
                var response = await _fixture.ApiClient.GetAsync(ProductHubURLs.GetProductsExpirationDateFromActivation(_fixture.BaseAddress, _portalId, _productId, _temporalityId, activationDate, ID_ORIGIN));
                Assert.Equal(HttpStatusCode.OK, response.StatusCode);
                var content = await response.Content.ReadAsStringAsync();
                Assert.False(string.IsNullOrWhiteSpace(content));
                AssertsForOk(content, activationDate);
            }
            catch (HttpRequestException ex)
            {
                Assert.Fail($"Fallo al llamar a /hub/GetProductsExpirationDateFromActivation: {ex.Message}");
            }
        }

        private static void AssertsForOk(string content, DateTime activationDate)
        {
            var objectContent = JsonSerializer.Deserialize<ProductWithTemporalityResponseModel>(content);
            Assert.NotNull(objectContent);
            Assert.True(activationDate < objectContent.ExpirationDate);
        }

        private static void AssertsForBadRequestWithErrors(string content)
        {
            var objectContent = JsonSerializer.Deserialize<CompanyProductWithAmbitResponseModel>(content);
            Assert.NotNull(objectContent);
            Assert.NotNull(objectContent.Errors);
            Assert.NotEmpty(objectContent.Errors);
        }

        private async Task<bool> CheckEndPoint()
        {
            await _fixture.InizializateCheckEndPoint();

            if (_fixture.EndpointStatus != EndpointStatusEnum.IsAvailable)
            {
                if (_fixture.EndpointStatus == EndpointStatusEnum.Timeout)
                    Assert.Fail("El endPoint no se ejecuta debido a timeOut");
                else if (_fixture.EndpointStatus == EndpointStatusEnum.NotFound)
                    Assert.Fail("El endPoint no se ejecuta debido a NotFound");

                Assert.Fail("El endPoint no se ejecuta correctamente");
            }

            return true;
        }
    }
}
