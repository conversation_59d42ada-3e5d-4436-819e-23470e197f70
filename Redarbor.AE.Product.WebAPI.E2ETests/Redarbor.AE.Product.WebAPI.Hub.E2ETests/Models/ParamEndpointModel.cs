using Redarbor.AE.Product.WebAPI.Hub.E2ETests.Enums;

namespace Redarbor.AE.Product.WebAPI.Hub.E2ETests.Models
{
    public class ParamEndpointModel
    {
        public string EndPoint { get; set; } = "";
        public short TimeOut { get; set; }
        public object? ObjectToSend { get; set; }
        public HttpMethodsEnum HttpMethod { get; set; }

        public ParamEndpointModel(string endPoint, short timeOut, object? objectToSend, HttpMethodsEnum httpMethod)
        {
            EndPoint = endPoint;
            TimeOut = timeOut;
            ObjectToSend = objectToSend;
            HttpMethod = httpMethod;
        }

        public ParamEndpointModel(string endPoint, short timeOut, HttpMethodsEnum httpMethod)
        {
            EndPoint = endPoint;
            TimeOut = timeOut;
            ObjectToSend = null;
            HttpMethod = httpMethod;
        }

        public ParamEndpointModel(string endPoint, short timeOut)
        {
            EndPoint = endPoint;
            TimeOut = timeOut;
            ObjectToSend = null;
            HttpMethod = HttpMethodsEnum.Get;
        }
    }
}
