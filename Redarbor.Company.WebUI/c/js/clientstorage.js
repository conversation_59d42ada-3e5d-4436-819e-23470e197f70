(function (window, document, undefined) {
    //clientStorage
    //  si volem salvar: clientStorage.set(key, value [,expires][,path]);
    //  es guarda a sessionStorage si està disponible i a cookie si no
    //  podem salvar directament a cookie fent clientStorage.inCookie().set(key, value [,expires][,path]);
    //  per recuperar -->clientStorage.get(key);
    //  per borrar -->clientStorage.remove(key);
    var clientStorage = (function () {
        var getPathForCookie = function (path) {
            var pathParts;
            path = path || window.location.pathname;
            pathParts = path.split("/");
            if (path.charAt(path.length - 1) !== "/" && pathParts[pathParts.length - 1].indexOf(".") !== -1) {
                pathParts.splice(pathParts.length - 1, 1);
                path = pathParts.join("/") + "/";
            }
            return path;
        },
            storage = (function () {
                try {
                    return typeof Storage !== "undefined";
                }
                catch (error) {
                    return false;
                }
            }()),
            saveCookie = false;
        return {
            //if we need to save in cookie, use clientStorage.inCookie().set(...)
            inCookie: function () {
                saveCookie = true;
                return this;
            },
            get: function (key) {
                var stored, expirity, i, cookieValue, cookieName,
                    cookies = document.cookie.split(";"),
                    cookiesLength = cookies.length;
                if (!key || typeof key !== "string") {
                    return undefined;
                }
                if (!saveCookie && storage) {
                    stored = JSON.parse(window.sessionStorage.getItem(key));
                    if (stored && stored.value) {
                        if (stored.expires) {
                            expirity = new Date(stored.expires);
                            if (expirity < new Date()) {
                                this.remove(key);
                                return undefined;
                            }
                        }
                        return stored.value;
                    }
                }
                else {
                    saveCookie = false;
                    for (i = 0; i < cookiesLength; i++) {
                        cookieValue = unescape(cookies[i].substr(cookies[i].indexOf("=") + 1));
                        if (cookieValue) {
                            cookieName = cookies[i].substr(0, cookies[i].indexOf("="));
                            cookieName = cookieName.replace(/^\s+|\s+$/g, "");
                            if (cookieName === key) {
                                return JSON.parse(cookieValue);
                            }
                        }
                    }
                }
                return undefined;
            },
            set: function (key, value, expires, pathToSave) {
                var objToSave = {}, expiration = new Date(),
                    path = getPathForCookie(pathToSave);
                if (!key || !value || typeof key !== "string") {
                    return false;
                }
                if (expires && typeof expires === "number") {
                    expiration.setHours(0, 0, 0, 0);
                    expiration.setDate(expiration.getDate() + expires);
                    objToSave.expires = expiration;
                }
                if (saveCookie || !storage) {
                    saveCookie = false;
                    document.cookie = key + "=" + escape(JSON.stringify(value)) + ((expires) ? "; expires=" + expiration.toUTCString() : "") + "; path=" + path;
                }
                else {
                    objToSave.value = value;
                    window.sessionStorage.setItem(key, JSON.stringify(objToSave));
                }
                return true;
            },
            remove: function (key, path) {
                path = getPathForCookie(path);
                if (!key || typeof key !== "string") {
                    return false;
                }
                if (!saveCookie && storage) {
                    window.sessionStorage.removeItem(key);
                }
                else {
                    saveCookie = false;
                    document.cookie = key + "=; expires=-1; path=" + path;
                }
                return true;
            },
            update: function (key, value, expires) {
                this.remove(key);
                this.set(key, value, expires);
            }
        };
    }());

    //nextPrevNavigation
    //  per generar la navegació anterior/siguiente, a la plana de llistat
    //  hem d'afegir un objecte de configuració amb una key identificadora
    //  i un valor. A la plana de detall, hem de tenir una div#breadcrumb
    //  i hem de cridar nextPrevNavigation.getNavigation(type, key)

    var nextPrevNavigation = (function () {
        return {
            getNavigation: function (type, key, isCVDetail) {
                var isCVDetail = isCVDetail || false;
                var navDt = clientStorage.get(key);
                if (navDt) {
                    navDt.au = location.href.substr(location.href.indexOf(location.host) + location.host.length);
                    navDt.t = type;
                    //si estem a la última URL de la llista hem d'anar a buscar
                    //la plana següent
                    if (navDt.urls[navDt.urls.length - 1] === decodeURI(navDt.au)) {
                        navDt.gn = true;
                    }
                    else {
                        navDt.gn = false;
                    }

                    navDt.cvDetail = isCVDetail;

                    //si estem a la primera URL de la llista i no és la pàgina 1,
                    //hem d'anar a buscar la plana anterior
                    if (navDt.urls[0] === decodeURI(navDt.au) && navDt.p > 1) {
                        navDt.gp = true;
                    }
                    $.ajax({
                        url: '/Ajax/getNavigation.aspx',
                        data: navDt,
                        traditional: true,
                        dataType: "json",
                        type: "POST"
                    }).done(function (data) {
                        if (data.gn || data.gp) {
                            //actualitzar el objecte per la navegació
                            navDt.urls = data.urls.split(',');
                            navDt.p = data.p;
                            navDt.gn = false;
                            clientStorage.update(key, navDt);
                        }
                        $(".ant_sig").append(data.html);
                    });
                }
            },
            setData: function (key, data) {
                data.urls = $(".js-o-link").map(function (idx, itm) {
                    return $(itm).attr("href");
                }).get();
                data.rtu = location.href;
                clientStorage.set(key, data);
            },
            removeUrlFromNavigation: function (key, url) {
                var navDt = clientStorage.get(key),
                    indexUrl = navDt.urls.indexOf(url);

                if (indexUrl > -1) {
                    navDt.urls.splice(indexUrl, 1);
                    clientStorage.update(key, navDt);
                }
            }
        };
    }());


    var ctUtils = {
        navigateUrl: function (url) {
            if (url) {
                window.location.href = url;
            }
        }
    };

    window.ctUtils = ctUtils;
    window.nextPrevNavigation = nextPrevNavigation;
    window.clientStorage = clientStorage;
}(window, document));