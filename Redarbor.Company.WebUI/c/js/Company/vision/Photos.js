(() => {
    let imagesToUpLoad = [];
    let _parametersPage = window.parametersPage;
    const LIST_NEW_IMAGE = "listNewPhotos";
    const NAME_NEW_IMAGE = "newImageCompany";
    const LINK_QUIT_IMAGE_CSS = "dB mt10 dB_m";
    const FILTER_ORIGIN_SELECTED_CSS = "sel";
    const DIV_MESSAGE_INFORMATION = "divMessageInformation";
    const DIV_MESSAGE_INFORMATION_BY_FILTER = "divMessageInformationNotPhotos";
    const DIV_MESSAGE_INFORMATION_NEWPHOTO = "divMessageInformation_NewPhoto";
    const DIV_PAGINATOR = "divPaginator";
    const DIV_LOADING_GENERAL = 'DivLoadingPhotoGeneral';
    const DIV_SHOW_ADD_PHOTOS = "divShowAddPhotos";
    const PAGINATOR_CLASS_NEXT = "b_next";
    const PAGINATOR_CLASS_PREVIOUS = "b_prev";
    const UL_PHOTOS = "ulPhotos";
    const UL_PAGINATOR = "ulPaginator";
    const UL_FILTERS = "ulFilters";
    const FILTERS_ATTRIBUTE_COUNTER = 'counterPhotos';
    const ORIGIN_COMPANY = 2;
    const DIV_UP_PHOTOS = "divUpPhotos";
    let actualImageContainer;
    let actimg;
    let userAgent = navigator && (navigator.userAgent && navigator.userAgent.toLowerCase());
    let _actualFilterOrigin;
    let _actualCounterPhotos = 0;
    let _actualImages;
    let _actualPage = 1;
    let _counters;
    let _ulPhotos;
    let _ulFilters;
    let _ulPaginator;
    let _photosUploaded = 0;
    let _photosForUpload = 0;
    let _photosUploadedError = 0;

    function load() {
        loadEvents();
        _actualFilterOrigin = _parametersPage.getActualFilterOrigin();
        refreshFilterOrigin(_actualFilterOrigin);
        findPhotosAndCountersFromServer();
    }

    function loadEvents() {
        let btnShowAddPhotos = document.getElementById("BtnShowAddPhotos");
        let lnkBackToCompanyFromCancel = document.getElementById("lnkBackToCompanyFromCancel");
        let lnkSendedPhotosChoose = document.getElementById("lnkSendedPhotosChoose");
        let btnUploadPhoto = document.getElementById("BtnUploadPhoto");
        let ulpLoadPhoto = document.getElementById("ulpLoadPhoto");
        let btnSendPhotos = document.getElementById("btnSendPhotos");

        let slideImgFlechaIzquierda = document.getElementById("slideImgFlechaIzquierda");
        let slideImgFlechaDerecha = document.getElementById("slideImgFlechaDerecha");
        let lnkCancelarRemovePhoto = document.getElementById("lnkCancelarRemovePhoto");
        let deletePhotoTrue = document.getElementById("deletePhotoTrue");

        _ulPhotos = document.getElementById(UL_PHOTOS);
        _ulPaginator = document.getElementById(UL_PAGINATOR);
        _ulFilters = document.getElementById(UL_FILTERS);
        _divShowAddPhotos = document.getElementById(DIV_SHOW_ADD_PHOTOS)

        if (_parametersPage.blockCompuAdvisorSections === "True") {
            btnShowAddPhotos.addEventListener("click", ShowPopUpContactVisionBlocked);
        } else {
            if (_parametersPage.isActionPermitedByRole === "False") {
                btnShowAddPhotos.addEventListener("click", ShowPopUpContactBlocked);
                $("#BtnShowAddPhotos").addClass("js_show_popup_default");
                $("#lnkSendedPhotosChoose").addClass('js_show_popup_default');
                $("#BtnUploadPhoto").addClass('js_show_popup_default');
                $("#" + UL_PHOTOS).addClass('js_show_popup_default');

                $(_ulFilters).each(function (index) {
                    $(this).addClass('js_show_popup_default');
                });
                $(_ulPaginator).each(function (index) {
                    $(this).addClass('js_show_popup_default');
                });

                $("#deletePhotoTrue").addClass('js_show_popup_default');
            }
            else
            {
                btnShowAddPhotos.addEventListener("click", showSelectPhoto);
                lnkSendedPhotosChoose.addEventListener("click", showSelectPhoto);
                btnUploadPhoto.addEventListener("click", showSelectPhoto);
                _ulPhotos.addEventListener('click', function (event) { manageUlPhotosClick(event); });
                _ulFilters.addEventListener('click', function (event) { manageUlFiltersClick(event); });
                _ulPaginator.addEventListener('click', function (event) { manageUlPaginatorClick(event); });
                deletePhotoTrue.addEventListener('click', function (event) { acceptDeletePhoto(); });
            }
        
        }




        lnkBackToCompanyFromCancel.addEventListener("click", cancelAddPhotos);


        ulpLoadPhoto.addEventListener("change", inputFileChange);

        btnSendPhotos.addEventListener('click', function (event) {
            sendPhotosAction();
        });
        slideImgFlechaIzquierda.addEventListener('click', function (event) {
            showNextPhoto();
        });
        slideImgFlechaDerecha.addEventListener('click', function (event) {
            showPreviousPhoto();
        });
        lnkCancelarRemovePhoto.addEventListener('click', function (event) {
            cancelDeletePhoto();
        });
    }

    function manageUlPhotosClick(event) {
        let Elemento;
        let Nombre;

        notShowMessageInformation();

        if (event.target.nodeName === "A") {
            element = event.target;
            this.actualImageContainer = element.parentElement.parentElement;
            showDeletePhotoConfirm();
            return;
        }

        if (event.target.nodeName === "IMG") {
            element = event.target;
            showPhotoDetail(element);
            return;
        }
    }

    function manageUlFiltersClick(event) {
        let elemento;
        let Nombre;

        elemento = event.target;

        while (elemento && elemento.parentNode !== _ulFilters) {
            elemento = elemento.parentNode;
            if (!elemento) { return; }
        }

        if (elemento.tagName === 'LI') {
            let codeFilterOrigin = $(elemento).attr('data-path');
            changeFilterOrigin(codeFilterOrigin);
        }
    }

    function manageUlPaginatorClick(event) {
        let ul = document.getElementById(UL_PAGINATOR);
        let elemento;
        let Nombre;

        elemento = event.target;

        while (elemento && elemento.parentNode !== ul) {
            elemento = elemento.parentNode;
            if (!elemento) { return; }
        }

        if (elemento.tagName === 'A') {
            changePaginatorPage(elemento);
        }
    }

    function changePaginatorPage(elementPage) {
        actualizeActualPage(elementPage);
        findPhotosAndCountersFromServer();
    }

    function actualizeActualPage(elementPage) {
        if (elementPage.classList.contains(PAGINATOR_CLASS_PREVIOUS)) {
            _actualPage--;
            return;
        }

        if (elementPage.classList.contains(PAGINATOR_CLASS_NEXT)) {
            _actualPage++;
            return;
        }

        _actualPage = parseInt($(elementPage).attr('title'));
    }

    function changeFilterOrigin(codeFilterOrigin) {
        _actualFilterOrigin = codeFilterOrigin;
        _actualPage = 1;

        refreshFilterOrigin(_actualFilterOrigin);

        findPhotosAndCountersFromServer();
    }

    function refreshFilterOrigin(codeFilterOrigin) {
        [].forEach.call(_ulFilters.childNodes, function (item) {
            if (codeFilterOrigin === $(item).attr('data-path')) {
                $(item).addClass(FILTER_ORIGIN_SELECTED_CSS);
            }
            else {
                $(item).removeClass(FILTER_ORIGIN_SELECTED_CSS);
            }
        });
    }

    function ShowAddPhotos(codeFilterOrigin) {
        let filter = _ulFilters.querySelector('[data-path="' + codeFilterOrigin + '"]');
        let allowAddPhoto;

        allowAddPhoto = $(filter).attr('allowAddPhoto');
        showElement(allowAddPhoto, _divShowAddPhotos);
    }

    function showButtonAddPhoto(show) {
        alert(show);
    }

    function showPhotoDetail(elementPhoto) {
        actimg = _actualImages.indexOf($(elementPhoto).attr('src'));
        $('#divSlideImg').removeClass('hide');
        cambiaIMG();
    }

    function showDeletePhotoConfirm() {
        $("#deletePhotoPopup").removeClass("hide");
    }

    function cancelDeletePhoto() {
        $("#deletePhotoPopup").addClass("hide");
        return false;
    }

    function acceptDeletePhoto() {
        deletePhoto(this.actualImageContainer);
    }

    function showNextPhoto() {
        if (actimg < _actualImages.length - 1) {
            actimg++;
            cambiaIMG();
        }
    }

    function showPreviousPhoto() {
        if (actimg > 0) {
            actimg--;
            cambiaIMG();
        }
    }

    function showAddPhotos() {
        let divFirstUpload = document.getElementById("divAddPhotos");
        let divPhotos = document.getElementById("divPhotos");

        notShowMessageInformation();
        showElement(false, divPhotos);
        showElement(false, _divShowAddPhotos);
    }

    function notShowAddPhotos() {
        let divPhotos = document.getElementById("divPhotos");

        notShowMessageInformation();
        showElement(true, divPhotos);
        actualiceCounterOfPhotos();
    }

    function showSelectPhoto(element) {

        let ulpLoadPhoto = document.getElementById("ulpLoadPhoto");

        showElement(false, divPaginator);
        notShowMessageInformation();

        if (ulpLoadPhoto.files.length === 1) {
            ulpLoadPhoto.value = "";
        }
        ulpLoadPhoto.click();

    }

    function cancelAddPhotos() {
        let divSendedPhotos = document.getElementById("divSendedPhotos");
        let listNewPhotos = document.getElementById(LIST_NEW_IMAGE);

        notShowMessageInformation();
        $(listNewPhotos).empty();
        showElement(false, divSendedPhotos);
        showElement(true, divPhotos);

        if (havePhotos()) {
            ShowAddPhotos(_actualFilterOrigin);
        }
    }

    function inputFileChange() {
        let files
        let img;
        let divSendedPhotos = document.getElementById("divSendedPhotos");
        let ulpLoadPhoto = document.getElementById("ulpLoadPhoto");

        notShowMessageInformationNewPhoto();
        showElement(true, divSendedPhotos);
        showElement(false, divShowAddPhotos);
        showElement(false, divPhotos);

        files = ulpLoadPhoto.files;

        if (files.length === 0) {
            return;
        }

        img = new Image();
        img.onload = function () {
            if (validateImage(this, files)) {
                uplodPhoto(files);
            }
        };
        img.src = window.URL.createObjectURL(files[0]);
    }

    function validateImage(image, files) {
        let maxSize = 5;
        let minHeight = 300;
        let minWidth = 300;

        if (image.width < minWidth || image.height < minHeight) {
            showMessageInformationNewPhoto(_parametersPage.getLiteralInvalidPhoto(), _parametersPage.getLiteralInvalidPhotoWeight());
            return;
        }

        let imaSize = (files[0].size / (1024 * 1024)).toFixed(2);
        if (imaSize > maxSize) {
            showMessageInformationNewPhoto(_parametersPage.getLiteralInvalidPhoto(), _parametersPage.getLiteralInvalidPhotoSize());
            return;
        }

        if (!(image.height > image.width && image.height <= _parametersPage.getPhotoMaxDimension()
            || image.height < image.width && image.width <= _parametersPage.getPhotoMaxDimension()
            || image.height == image.width && image.width <= _parametersPage.getPhotoMaxDimension()))
        {
            showMessageInformationNewPhoto(_parametersPage.getLiteralInvalidPhoto(), _parametersPage.getLiteralErrorDimension());
            return;
        }



        return true;
    }

    function putImageSelectedInImg(file, imageControl) {
        let reader = new FileReader();

        reader.onload = function (e) {
            $(imageControl).attr('src', e.target.result);
        }

        reader.readAsDataURL(file);
    }

    function uplodPhoto(files) {
        let listNewPhotos = document.getElementById(LIST_NEW_IMAGE);
        let newItem = document.createElement("li");
        let newImage = document.createElement("img");
        let newLinkRemove = document.createElement("a");

        $(newLinkRemove).addClass(LINK_QUIT_IMAGE_CSS);
        $(newLinkRemove).text(_parametersPage.getLiteralDelete());
        newLinkRemove.addEventListener('click', function (event) {
            quitAddPhoto(event.target);
        });

        putImageSelectedInImg(files[0], newImage);
        newImage.name = NAME_NEW_IMAGE;

        newItem.appendChild(newImage);
        newItem.appendChild(newLinkRemove);
        listNewPhotos.appendChild(newItem);

        imagesToUpLoad.push(files[0]);
        showElement(true, document.getElementById("btnSendPhotos"));
    }

    function quitAddPhoto(linkRemove) {
        let indexToDelete;

        indexToDelete = $(linkRemove.parentElement).index();
        imagesToUpLoad.splice(indexToDelete, 1);

        linkRemove.parentElement.remove();

        if (imagesToUpLoad.length === 0) {
            showElement(false, document.getElementById("btnSendPhotos"));
        }
    }

    function showMessageInformation(title, description) {
        let divMessageInformation = document.getElementById(DIV_MESSAGE_INFORMATION);
        let pTitle = document.getElementById("MessageInformation_title");
        let pDescription = document.getElementById("MessageInformation_description");

        pTitle.textContent = title;
        pDescription.textContent = description;
        showElement(true, divMessageInformation);
    }

    function notShowMessageInformation() {
        let divMessageInformation = document.getElementById(DIV_MESSAGE_INFORMATION);

        showElement(false, divMessageInformation);
    }

    function showMessageInformationNewPhoto(title, description) {
        let divMessageInformation = document.getElementById(DIV_MESSAGE_INFORMATION_NEWPHOTO);
        let pTitle = divMessageInformation.children.MessageInformation_title_NewPhoto;
        let pDescription = divMessageInformation.children.MessageInformation_description_NewPhoto;

        pTitle.textContent = title;
        pDescription.textContent = description;
        showElement(true, divMessageInformation);
    }

    function notShowMessageInformationNewPhoto() {
        let divMessageInformation = document.getElementById(DIV_MESSAGE_INFORMATION_NEWPHOTO);

        showElement(false, divMessageInformation);
    }

    function getFiltersPage() {
        let formData = new FormData();

        formData.append('actualPage', getActualPage());
        formData.append('FilterOrigin', _actualFilterOrigin);

        return formData;
    }

    function sendPhotosAction() {
        notShowMessageInformation();
        sendPhotos();
        cancelAddPhotos();
        notShowAddPhotos();
    }

    function sendPhotos() {
        let newImages = document.getElementsByName(NAME_NEW_IMAGE);

        _photosForUpload = newImages.length;
        $("#uploadingPhotoPopup").removeClass("hide");

        for (i = 0; i < _photosForUpload; i++) {
            sendPhotoToServer(imagesToUpLoad[i]);
        }

        if (_photosForUpload === 0)
            $("#uploadingPhotoPopup").addClass("hide");
    }

    function sendPhotoToServer(newImage) {
        let formData = getFiltersPage();
        formData.append('newImage', newImage);

        sendPhotoToServerBegin();

        $.ajax({
            url: _parametersPage.getAjaxCallSavePhoto(),
            type: "POST",
            async: true,
            processData: false,
            contentType: false,
            data: formData,
            success: function (dataView) {
                sendPhotoToServerEnd();
            },
            error: function (er) {
                sendPhotoToServerError(newImage);
            }
        });
    }

    function sendPhotoToServerBegin() {
        _counters.CounterAll++;
        _counters.CounterByCompany++;
        _actualCounterPhotos++;

        actualiceCounterOfPhotos();
    }

    function sendPhotoToServerEnd() {
        sendPhotoToServerFinished();
    }

    function sendPhotoToServerError(newImage) {
        _photosUploadedError++;
        sendPhotoToServerFinished();
    }

    function sendPhotoToServerFinished() {
        _photosUploaded++;

        if (_photosUploaded === _photosForUpload) {
            if (_photosUploadedError > 0) {
                alert(_parametersPage.getLiteralUploadingPhotosError() + _photosUploadedError + " " + _parametersPage.getLiteralPhotos());
            }

            imagesToUpLoad = [];
            _photosUploaded = 0;
            _photosUploaded = 0;
            _photosUploadedError = 0;
            $("#uploadingPhotoPopup").addClass("hide");

            cancelAddPhotos();
            notShowAddPhotos();
            changeFilterOrigin(_actualFilterOrigin);
        }
    }

    function uploadNewPhotoFromServerEnd(newImageContainer, newIdImage) {
        let DivNotLoadingPhoto = newImageContainer.querySelector('[name=DivNotLoadingPhoto]');
        let DivLoadingPhoto = newImageContainer.querySelector('[name=DivLoadingPhoto]');

        $(newImageContainer).attr('idimage', newIdImage);
        showElement(true, DivNotLoadingPhoto);
        showElement(false, DivLoadingPhoto);
    }

    function findPhotosAndCountersFromServer() {
        let formData;

        formData = getFiltersPage();

        $.ajax({
            url: _parametersPage.getAjaxCallFindPhotosAndCounters(),
            type: "POST",
            processData: false,
            contentType: false,
            data: formData,
            success: function (dataView) {
                findPhotosAndCountersFromServerEnd(dataView);
                showLoading(false);
                showDivUpPhotos(true);
            },
            error: function (er) {
                showLoading(false);
                showMessageInformation("Error", er.responseText);
            }
        });
    }

    function findPhotosAndCountersFromServerBegin() {
        showLoadingGeneral(true);
    }

    function findPhotosAndCountersFromServerEnd(dataToLoad) {
        loadPagination(dataToLoad.PhotosCounter);
        loadPhotos(dataToLoad.PhotosForScreen);
    }

    function findPhotosAndCountersFromServerError() {
        showLoadingGeneral(false);
    }

    function loadPhotos(itemsFinded) {
        let newImageContainer;
        let counterPhotos = 0;

        $(_ulPhotos).empty();
        _actualImages = [];

        itemsFinded.forEach(function (element) {
            newImageContainer = printNewPhotoInScreen(element.Path, _ulPhotos, element.Origin);
            uploadNewPhotoFromServerEnd(newImageContainer, element.Id);
            counterPhotos++;
        });

        _actualCounterPhotos = counterPhotos;
        actualiceCounterOfPhotos();
        showLoadingGeneral(false);
    }

    function loadPagination(counters) {
        _counters = counters;
        refreshCounterFilters();
        refreshPaginator();
    }

    function refreshCounterFilters() {
        let actualFilter = _ulFilters.querySelector('[data-path="' + _actualFilterOrigin + '"]');
        let photoNumberTotal = document.getElementById("photoNumberTotal");
        let liFilterAll = document.getElementById("filterAll");
        let liFilterCompany = document.getElementById("filterCompany");
        let liFilterCandidate = document.getElementById("filterCandidate");
        let imageCounter = 0;
        let imageCounterTotal = 0;
        let counterItems;


        setLiteralFromOriginFilter(liFilterAll, _counters.CounterAll);
        setLiteralFromOriginFilter(liFilterCompany, _counters.CounterByCompany);
        setLiteralFromOriginFilter(liFilterCandidate, _counters.CounterByCandidate);

        if (_actualPage === 1) {
            imageCounter = 1;
        }
        else {
            imageCounter = (_actualPage - 1) * _parametersPage.getItemsForPage();
            imageCounter++;

        }

        counterItems = +($(actualFilter).attr(FILTERS_ATTRIBUTE_COUNTER));
        imageCounterTotal = imageCounter + parseInt(_parametersPage.getItemsForPage());
        imageCounterTotal--;
        if (imageCounterTotal > counterItems) {
            imageCounterTotal = counterItems;
        }

        photoNumberTotal.textContent = counterItems;

        if (+counterItems === 0)
            showMessageInformationNotPhotos();
        else
            notShowMessageInformationNotPhotos();
    }

    function setLiteralFromOriginFilter(liOriginFilter, counter) {
        $(liOriginFilter).attr(FILTERS_ATTRIBUTE_COUNTER, counter);
        liOriginFilter.firstElementChild.firstElementChild.innerText = $(liOriginFilter).attr("title") + " (" + counter + ")";
    }

    function refreshPaginator() {
        let actualFilter = _ulFilters.querySelector('[data-path="' + _actualFilterOrigin + '"]');
        let counterItems = 0;
        let divPaginator = document.getElementById(DIV_PAGINATOR);
        let numberOfPages = 0;

        counterItems = $(actualFilter).attr(FILTERS_ATTRIBUTE_COUNTER);
        numberOfPages = calculateNumberOfPages(counterItems, _parametersPage.getItemsForPage());

        if (numberOfPages > 1) {
            showElement(true, divPaginator);
            CreatePaginator(_ulPaginator, numberOfPages, _actualPage);
        }
        else {
            showElement(false, divPaginator);
        }
    }

    function calculateNumberOfPages(counterItems, itemsFromPage) {
        numberOfPages = parseInt(counterItems / itemsFromPage);
        if ((counterItems % itemsFromPage) !== 0) {
            numberOfPages++;
        }

        return numberOfPages;
    }

    function CreatePaginator(ulPaginator, numberOfPage, actualPage) {
        let i = 1;
        let newPage;
        let isPageActive = false;

        $(ulPaginator).empty();

        if (actualPage !== 1) {
            newPage = createPaginatorPagePrevious();
            ulPaginator.appendChild(newPage);
        }

        while (i <= numberOfPage) {
            if (actualPage === i) {
                isPageActive = true;
            }
            else {
                isPageActive = false;
            }

            newPage = createPaginatorPage(i, isPageActive)
            ulPaginator.appendChild(newPage);
            i++;
        }

        if (actualPage !== numberOfPage) {
            newPage = createPaginatorPageNext();
            ulPaginator.appendChild(newPage);
        }
    }

    function createPaginatorPage(numberPage, isPageActive) {

        let newLinkPage = document.createElement("a");

        $(newLinkPage).text(numberPage).attr('title', numberPage);

        if (isPageActive) {
            $(newLinkPage).addClass("sel");
        }

        return newLinkPage;
    }

    function createPaginatorPagePrevious() {

        let newLinkPage = document.createElement("a");
        var node = document.createElement("span");
        $(node).addClass("icon i_prev");

        $(newLinkPage).text(_parametersPage.getLiteralButtonPrevious()).addClass(PAGINATOR_CLASS_PREVIOUS);
        newLinkPage.prepend(node);

        return newLinkPage;
    }

    function createPaginatorPageNext() {

        let newLinkPage = document.createElement("a");
        var node = document.createElement("span");
        $(node).addClass("icon i_next");

        $(newLinkPage).text(_parametersPage.getLiteralButtonNext()).addClass(PAGINATOR_CLASS_NEXT);
        newLinkPage.appendChild(node);

        return newLinkPage;
    }

    function printNewPhotoInScreen(srcToCopy, ulCompanyPhotos, origin) {
        let newDivNotLoading = document.createElement("div");
        let newItem = document.createElement("li");
        let newImage = document.createElement("img");
        let newLinkRemove = document.createElement("a");

        $(newLinkRemove).addClass(LINK_QUIT_IMAGE_CSS);
        $(newLinkRemove).text(_parametersPage.getLiteralDelete());

        newImage.src = srcToCopy;
        $(newImage).attr('name', "ImgToPhoto");

        newDivNotLoading.appendChild(newImage);
        newDivNotLoading.appendChild(newLinkRemove);
        $(newDivNotLoading).attr('name', "DivNotLoadingPhoto");
        newItem.appendChild(newDivNotLoading);
        $(newItem).attr('origin', origin);
        $(newItem).append(_parametersPage.getStructeLoadingPhoto());

        changePhotoUploadBegin(newItem);
        ulCompanyPhotos.insertBefore(newItem, ulCompanyPhotos.firstChild);
        _actualImages.push(srcToCopy);

        return newItem;
    }

    function createNewLinkRemove() {


        return newLinkRemove;
    }

    function changePhotoUploadBegin(newImageContainer) {
        let divNotLoadingPhoto = newImageContainer.querySelector('[name=DivNotLoadingPhoto]');
        let divLoadingPhoto = newImageContainer.querySelector('[name=DivLoadingPhoto]');

        showElement(false, divNotLoadingPhoto);
        showElement(true, divLoadingPhoto);
    }

    function deletePhoto(imageContainer) {
        $("#StatusDeletePhotosPopUp").removeClass("hide");
        deletePhotoToServer(imageContainer);

        changeFilterOrigin(_actualFilterOrigin);
    }

    function deletePhotoToServer(imageContainer) {
        let formData = getFiltersPage();
        let photoId = $(imageContainer).attr('idimage');

        formData.append('photoId', photoId);

        $.ajax({
            url: _parametersPage.getAjaxCallDeletePhoto(),
            type: "POST",
            async: true,
            processData: false,
            contentType: false,
            data: formData,
            success: function (dataView) {
                deletePhotoToServerEnd(imageContainer);
            },
            error: function (er) {
                deletePhotoToServerError(imageContainer, _parametersPage.getLiteralErrorDelete());
            }
        });
    }

    function deletePhotoToServerEnd(imageContainer) {
        $("#StatusDeletePhotosPopUp").addClass("hide");
        changeFilterOrigin(_actualFilterOrigin);
    }

    function deletePhotoToServerError(imageContainer, errorMessage) {
        let divNotLoadingPhoto = imageContainer.querySelector('[name=DivNotLoadingPhoto]');
        let divLoadingPhoto = imageContainer.querySelector('[name=DivLoadingPhoto]');

        $("#StatusDeletePhotosPopUp").addClass("hide");

        showElement(true, divNotLoadingPhoto);
        showElement(false, divLoadingPhoto);

        showMessageInformation("Error", errorMessage);
    }

    function getActualPage() {
        return _actualPage;
    }

    function actualiceCounterOfPhotos() {
        let divMessageInformationNotPhotos = document.getElementById("divMessageInformationNotPhotos");
        let divCompanyWithoutPhotos = document.getElementById("divCompanyWithoutPhotos");
        let divAddPhotosWithoutPhotos = document.getElementById("divAddPhotosWithoutPhotos");
        let divFiltros = document.getElementById("divFiltros");

        if (havePhotos()) {
            ShowAddPhotos(_actualFilterOrigin);
            showElement(true, divFiltros);
            showElement(false, divCompanyWithoutPhotos);
            showElement(false, divAddPhotosWithoutPhotos);
        }
        else {
            showElement(false, _divShowAddPhotos);
            showElement(false, divFiltros);
            showElement(true, divCompanyWithoutPhotos);
            showElement(true, divAddPhotosWithoutPhotos);
        }

        refreshCounterFilters();

        if (!havePhotos()) {
            showElement(false, divMessageInformationNotPhotos);
        }
    }

    function cambiaIMG() {
        var alto, ancho, ancho_pant, alto_pant, ruta_img;

        if (actimg > 0) $('.i_next_w').show();
        else $('.i_next_w').hide();

        if (actimg < _actualImages.length - 1) $('.i_prev_w').show();
        else $('.i_prev_w').fadeOut('slow');

        $('#foto_actual').text((actimg + 1));
        $('#total_fotos').text(_actualImages.length);

        ruta_img = _actualImages[actimg];
        $('#lb_img').hide().attr('src', ruta_img);

        if (userAgent && userAgent.search(/iphone|ipod|ipad|android/) > -1) {
            alto = $(window).height();
            ancho = $(window).width();
            ancho_pant = '0';
            alto_pant = '0';
        }
        else {
            if ($('#lb_img').prop("naturalWidth") < 300) ancho = '300';
            else ancho = ($('#lb_img').prop("naturalWidth") + 10);
            if ($('#lb_img').prop("naturalHeight") < 300) alto = '300';
            else alto = $('#lb_img').prop("naturalHeight");
            ancho_pant = (($(window).width() / 2) - (ancho / 2));
            alto_pant = (($(window).height() / 2) - (alto / 2)) + $(window).scrollTop();
        }
        $('#lb_img').css('width', '100%').fadeIn('slow');
    }

    function showElement(show, element) {

        let classToAdd = "";
        let classToRemove = "";

        if (show === true || show === "1") {
            classToAdd = "show";
            classToRemove = "hide";
        }
        else {
            classToAdd = "hide";
            classToRemove = "show";
        }

        $(element).addClass(classToAdd);
        $(element).removeClass(classToRemove);
    }

    function showLoadingGeneral(show) {
        let divLoadingPhotos = document.getElementById("DivLoadingPhotos");
        showElement(show, divLoadingPhotos);
    }

    function showLoading(show) {
        let divLoading = document.getElementById(DIV_LOADING_GENERAL);
        showElement(show, divLoading);
    }

    function showDivUpPhotos(show) {
        let divUpPhotos = document.getElementById(DIV_UP_PHOTOS);
        showElement(show, divUpPhotos);
    }

    window.addEventListener("load", load());

    $('.i_close_w').click(function () { $('#divSlideImg').addClass('hide') });

    $(document).keydown(function (event) {
        if (event.which === 39) {
            showPreviousPhoto();
        }
        if (event.which === 37) {
            showNextPhoto();
        }
    });

    function showMessageInformationNotPhotos() {
        let divMessageInformationNotPhotos = document.getElementById(DIV_MESSAGE_INFORMATION_BY_FILTER);
        showElement(true, divMessageInformationNotPhotos);

    }

    function notShowMessageInformationNotPhotos() {
        let divMessageInformationNotPhotos = document.getElementById(DIV_MESSAGE_INFORMATION_BY_FILTER);
        showElement(false, divMessageInformationNotPhotos);

    }

    function havePhotos() {
        if (+_counters.CounterAll === 0) {
            return false;
        }

        return true;
    }
})();