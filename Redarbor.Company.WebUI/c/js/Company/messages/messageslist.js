var configModel = null;

function InitializeMessage(configModel) {
    this.configModel = configModel;
    this.literals = literals;
};

window.onload = function () {
    if ($("#firstConverBanner").length === 0) {
        $('#Messages').animate({ scrollTop: $('#Messages')[0].scrollHeight }, 0);
    }   
};

$(document).ready(function () {    

    InitializePopupDeleteConversation();
    $("#SelectedOfferName").change(function () {

        if ($(this).val() === null || $(this).val() === "") {
            window.location.href = configModel.urlActions.messageurl;
        }
        else {
            window.location.href = configModel.urlActions.messageurl + "?oi=" + $(this).val();
        }

});

$(document).on("click", ".it-conver", function () {

    let $this = $(this);
    $(".it-conver").removeClass("sel");

    ishowInLoading(true);
    $this.removeClass("it-conver").addClass("it-conver sel");
    $this.find(".num").removeClass("show").addClass("hide");
    $("#nameCandidateMessage").html($this.data("name"));
    GetMessagesOfConversationByIdConversation($this[0].id);
    goToTheEndConver();

});

bindDinamicEvents();

///// Timers de recarga de las conversaciones y mensajes/////
//setInterval(function () { loadConversationsInterval($(".it-conver.current").first().data(), $("#SelectedOfferName option:selected").val()); }, 5000);

setInterval(function () { loadMessagesCurrentConverInterval($(".it-conver.sel").first().data()); }, 5000);



});


function ishowInLoading(waiting) {
    if (waiting) {
        $('#Messages').removeClass("show").addClass("hide");
        $('#writtingArea').removeClass("show").addClass("hide");
        $("#loadingConvers").removeClass("hide").addClass("show");
    }
    else {
        $('#loadingConvers').removeClass("show").addClass("hide");
        $('#Messages').removeClass("hide").addClass("show");
        $('#writtingArea').removeClass("hide").addClass("show");
    }
}

function goToTheEndConver() {
    $('#Messages').animate({ scrollTop: $('#Messages')[0].scrollHeight }, 0);
}

function refreshInfoOfMessage(textToWrite, isMessgeCorrent) {
    let divNumeroCaracteres = $('#charNumMessage');
    let colorMessage = '#228b22';

    if (!isMessgeCorrent) {
        colorMessage = '#ff0000';
    }

    divNumeroCaracteres.css('color', colorMessage);
    divNumeroCaracteres.text(textToWrite);
}

function deleteConversation(elemButton) {

    if (elemButton.classList.contains("disabled")) return false;

    var $converData = $(".it-conver.sel").first().data();
    elemButton.classList.add("disabled");

    $.ajax({
        async: false,
        cache: false,
        type: 'POST',
        url: configModel.urlActions.deleteConversation,
        data: {
            idConversationEncrypted: $converData.idconver,
            idOfferEncrypted: $converData.idoffer,
        },
        success: function () {

            if ($("#Conver").children().length > 1)
                location.reload();
            else
                location.href = configModel.urlActions.returnHome;
        }
    });

}

function addMessage() {
    var $converData = $(".it-conver.sel").first().data();
    removeErrorStyleChar();
    if ($('#txtMessage').val().length > 0 && !$("#txtMessage").val().match(new RegExp(/[~%&¬\[\]}\-{<>]/g))) {

        var token = $('input[name="__RequestVerificationToken"]', $('#__AjaxAntiForgeryForm')).val();

        $.ajax({
            async: false,
            cache: false,
            type: 'POST',
            url: configModel.urlActions.insertMessage,
            data: {
                idConversationEncrypted: $converData.idconver,
                idCandidateEncrypted: $converData.idcandidate,
                idCvEncrypted: $converData.idcv,
                idOfferEncrypted: $converData.idoffer,
                idTypeENcrypted: $converData.type,
                message: $('#txtMessage').val(),
                logoPath: configModel.logoPath,
                __RequestVerificationToken: token
            },
            success: function(respuesta) {
                if ($("#firstConverBanner").length > 0)
                    $("#firstConverBanner").addClass("hide");

                if ($(""))
                    $("#Messages").removeClass("no_conv tc");
                    $("#Messages").append(respuesta);
                goToTheEndConver();
                $("#txtMessage").val("");
                $("#charNumMessage").html("");                
            },
            error: function (request, status, error) {
                addErrorStyleChar();
                $("#charNumMessage").html(error);
            }
        });
    }
    else {
        addErrorStyleChar();
        if ($('#txtMessage').val() !== undefined && $('#txtMessage').val() !== "") {
            $("#charNumMessage").html(literals.litCharNotAllowed);
        }
    }
}

function addErrorStyleChar() {
    let divError = $('#charNumMessage');   
    divError.css('color', '#ff0000');   
}

function removeErrorStyleChar() {
    let divError = $('#charNumMessage');  
    divError.css('color', '#228b22');
}
function loadConversationsInterval(currentConver, offerSelected) {
    let $convers = $("#Conver");
  
   let statusConver ;
    $.ajax({
        async: false,
        cache: false,
        type: 'POST',
        url: configModel.urlActions.getConversation,
        data: {
            idConversationEncrypted: currentConver.idconver,
            idOfferEncrypted: offerSelected,
            isFromHeader: configModel.isFromHeader
        },
        success: function(respuesta) {
                 if (respuesta.trim() != '') {
                    if(respuesta != '0')
                         {
                            $convers.html(respuesta);
                            var messagesPending = $('#' + $("ul#Conver li:first").get(0).id).data().pending;
                            if (messagesPending != ''){
                                for( var x = 0; x < messagesPending; x++)  {
                                    loadMessagesCurrentConverInterval(currentConver);                                                           
                                }
                               let id = $("ul#Conver li:first").get(0).id;
                               $('#' + id + " span.num").removeClass("show").addClass("hide");                            
                           }     
                        }
                }
             }
         });
}

function loadMessagesCurrentConverInterval(currentConver) {

    $.ajax({
        async: false,
        cache: false,
        type: 'POST',
        url: configModel.urlActions.getMessagesActiveConver,
        data: {
            idConversationEncrypted: currentConver.idconver
        },
        success: function(respuesta) { 
            if( respuesta != '')
            {      
                $("#Messages").append(respuesta);
                $('#Messages').animate({ scrollTop: $('#Messages')[0].scrollHeight }, 100);
                let id = $("ul#Conver li:first").get(0).id;
                $('#' + id + " span.num").removeClass("show").addClass("hide");           
            }
            bindDinamicEvents();
        }
    });
}


function GetMessagesOfConversationByIdConversation(id) {

    $.ajax({
        async: false,
        cache: false,
        type: 'POST',
        url: configModel.urlActions.getMessagesByIdConversation,
        data: { idConversationEncrypted: id },
        success: function(respuesta) {
            ishowInLoading(false);
            $("#boxConversationMessages").html(respuesta);
            bindDinamicEvents();
        },
    });
}

function InitializePopupDeleteConversation() {

    document.getElementById('delConversationKO').addEventListener('click', function (event) {

        event.preventDefault();

        var elemPopUp = document.getElementById("deleteConversationPopup");
        elemPopUp.classList.add("hide");
    });

    document.getElementById('delConversationOK').addEventListener('click', function (event) {

        event.preventDefault();

        var elemButton = document.getElementById("delConversationOK");
        deleteConversation(elemButton);
    });

    document.getElementById('delConversationClose').addEventListener('click', function (event) {

        event.preventDefault();

        var elemPopUp = document.getElementById("deleteConversationPopup");
        elemPopUp.classList.add("hide");
    });
}

function bindDinamicEvents() {   
    
    $("#delConversation").one('click', function (event) {
        event.preventDefault();
        $("#delConversationOK").removeClass("disabled");   
        $('#deleteConversationPopup').removeClass("hide");     
    });

    ///// Funciones para agregar mensajes/////
    $('#addMessage').click(function () {
        addMessage();
    });

    $('#txtMessage').keypress(function (e) {
        var key = e.which;
        if (key === 13) {
            e.preventDefault();
            if ($('#txtMessage').val().length > 0) {
                addMessage();
            }
        }
    });

    $('#txtMessage').focusout(function() {
        if ($('#txtMessage').val() === "") {
            $("#charNumMessage").html("");
            removeErrorStyleChar();
        }
    });

    $('#txtMessage').keyup(function (e) {
        const cajaMensaje = $(this);
        const numeroCaracteresMaximos = cajaMensaje.attr('maxlength');
        const numeroCaracteresActuales = cajaMensaje.val().length;
        let divNumeroCaracteres = $('#charNumMessage');
        let numeroCaracteresPendientes = 0;
        let textToRefresh = '';
        let isMessageCorrect = true;

        if (e.which === 13) {
            return;
        }

        if (numeroCaracteresActuales >= numeroCaracteresMaximos) {
            textToRefresh = divNumeroCaracteres.attr('message_chars_ko');
            isMessageCorrect = false;
        }
        else {
            numeroCaracteresPendientes = numeroCaracteresMaximos - numeroCaracteresActuales;
            textToRefresh = numeroCaracteresPendientes + ' ' + divNumeroCaracteres.attr('message_chars_ok');
            isMessageCorrect = true;
        }

        refreshInfoOfMessage(textToRefresh, isMessageCorrect);
    });

    $('#txtMessage').bind("paste", function (e) {

        let pasteData = $('#txtMessage').length + e.originalEvent.clipboardData.getData('text').length;
        if (pasteData > 499)
            return false;
        return true;
    });

    $('#gotoList').click(function() {
        gotoList();
    });



    $(window).resize(function () {
        if ($(window).width() > 768) {
            $('.box_conv, .box_send').removeAttr("style");
            $(".box_send").removeClass("fixed show");
            $(".box_conv").removeClass("show hide");
            $("#charNumMessage").removeClass("show hide");
        }
        else {
            $(".box_send").addClass(" fixed");
        }
    });

    function deleteClass() {
        $(".box_conv").removeClass("show").addClass("hide");
    }

    function gotoList() {
        if ($(window).width() <= 768) {
            $(".box_conv").animate({
                left: "100%"
            }, 500);

            setTimeout(deleteClass, 500);

            $(".box_send").addClass(" fixed");
            $(".box_send").animate({
                left: "110%"
            }, 500);
        }
    }

    if ($(window).width() <= 768) {
        $(".box_conv").removeClass("hide").addClass("show");
        $(".box_conv").css("position", "absolute").animate({
            left: 0
        }, 500);

        $(".box_send").addClass(" fixed");

        $(".box_send").animate({
            left: 0
        }, 500);

        $("#charNumMessage").removeClass("show").addClass("hide");
    }
    else {
        $(".box_conv").removeClass("hide");
    }

}
