var convertToCompleteModel = null;

function InitializeConvertToCompletePopUp(convertToCompleteModel) {
    this.convertToCompleteModel = convertToCompleteModel;
};

function setValues(element) {
    let el = $(element);
    $("#encryptedoffer").val(el.attr("data-idoffer"));
    if (el.attr("data-featureprod") != "") {
        $("#featureprod").val(el.attr("data-featureprod"));
    }
}

function hideOtherPopUps() {
    closePopUp()
}

function showFullPopUp(selector) {
    popupDetail(selector);
}

$(document).ready(function () {
    $(".showConvertToCompleteCompPopUpBtt").click(function () {
        hideOtherPopUps();
        setValues(this);
    });

    $(".showConvertToCompleteCompPopUpBasicBtt").click(function () {
        hideOtherPopUps();
        setValues(this);
    });

    $(".convertToCompleteImproveOfferButton").click(function (event) {
        setValues(this);
        ajaxCallConvertToComplete(event);
    });

    $("#popUpConverToCompleteBtt, #popUpConverToCompleteBttFromBasic").click(function (event) {
        ajaxCallConvertToComplete(event);
    });

    function ajaxCallConvertToComplete(event) {
        loadingFullShow();
        let target = event.currentTarget;
        let offerId = $(target).attr("data-idoffer");
        let featureProd = $(target).attr("data-featureprod");
        $.ajax({
            async: true,
            cache: false,
            dataType: "json",
            type: 'POST',
            url: convertToCompleteModel.ajaxManagePopUpConvertToCompleteBtt,
            data: {},
            success: function (respuesta) {
                if (respuesta != null && respuesta.toString() != '') {

                    if (respuesta === 1) {
                        let location = convertToCompleteModel.goToPostPublishStepTwo.replace(/&amp;/g, '&').replace(/%23/g, '#').replace('#idofenc#', offerId);
                        window.location.href = location;
                        
                    }
                    if (respuesta === 2) {
                        let location = convertToCompleteModel.goToMPCart.replace(/&amp;/g, '&').replace(/%23/g, '#').replace('#product', featureProd).replace('#idofenc#', offerId);
                        trackButtonPage(1, convertToCompleteModel.idPage);
                        window.location.href = location;
                    }
                    if (respuesta === 3) {
                        let location = convertToCompleteModel.goToChooseMP.replace(/&amp;/g, '&').replace(/%23/g, '#').replace('#idofenc#', offerId);
                        trackButtonPage(1, convertToCompleteModel.idPage);
                        window.location.href = location;
                    }
                    if (respuesta === 4) {
                        event.preventDefault();
                        convertOfferToComplete(offerId);
                        loadingFullHide();
                    }
                }
            },
        });
    }

    function convertOfferToComplete(p_offers) {

        var btnConvertToComplete = !convertToCompleteModel.buttonConvertToCompleteEncryptedName ? "" : convertToCompleteModel.buttonConvertToCompleteEncryptedName;

        $.blockUI({ message: "<h3>Convirtiendo a completa...</h3>" });

        $.ajax({
            async: true,
            cache: false,
            dataType: "html",
            type: 'POST',
            url: convertToCompleteModel.ajaxConvertToCompleteUrl,
            data: "idoffers=" + p_offers + "&ac=6" + "&btnConvertEnc=" + btnConvertToComplete,
            success: function (respuesta) {
                let obj = null;

                if (isJson(respuesta)) {
                    obj = JSON.parse(respuesta);
                }
                if (+respuesta > 0
                    || respuesta.toLowerCase() == "true" || obj != null && obj.isValid) {
                    location.reload();
                }
            },
            beforeSend: function () { },
            error: function (objXMLHttpRequest) { }
        });
    }
});

