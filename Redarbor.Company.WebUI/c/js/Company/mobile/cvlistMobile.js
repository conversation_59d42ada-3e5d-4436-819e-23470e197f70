const boughtCvs = 2;
var configCvListModel = null;
var FirstSearchKeyName = null;


(function ($) {

    const bulletFilterSelector = '#bulletFilter';
    const tagIconSelector = '.tag_icon';

    $(function () {
        const numberOfFiltersApplied = getNumberOfFiltersApplied();

        showNumberOfFiltersApplied(numberOfFiltersApplied);

        showHideBullet(numberOfFiltersApplied);

        setupLinkedDropdowns("minAge", "maxAge");
        setupLinkedDropdowns("minYears", "maxYears");

        setupOnClickEvents();

        if (configCvListModel.isFiltered == 'False') {
            var locsearchvalue = clientStorage.inCookie().get(FirstSearchKeyName);
            if (locsearchvalue > 0) {
                $('#MultifiltersDataModel_SelectedLocalization').val(locsearchvalue);
            }
        }
    });

    function showHideBullet(numberOfFiltersApplied) {
        if (!numberOfFiltersApplied) {
            hideBullet();
        } else {
            showBullet();
        }
    }

    function showBullet() {
        const $bullet = $(bulletFilterSelector);
        $bullet.removeClass("hide");

        const $tagIcon = $(tagIconSelector);
        $tagIcon.addClass("sel");
    }

    function hideBullet() {
        const $bullet = $(bulletFilterSelector);
        $bullet.addClass("hide");

        const $tagIcon = $(tagIconSelector);
        $tagIcon.removeClass("sel");
    }

    function getNumberOfFiltersApplied() {
        const $spans = $('.countMobile > span');
        return $spans.length;
    }

    function showNumberOfFiltersApplied(totalSpans) {
        const bullet = document.querySelector(bulletFilterSelector);
        if (bullet) {
            bullet.innerHTML = totalSpans >= 0 ? totalSpans : 0;
        }
    }

    function setupOnClickEvents() {

        $("#js_isvisualizated").click(function () {
            $("#MultifiltersDataModel_IsCvVisualitzed").val(boughtCvs);
            $('#MultifiltersDataModel_SelectedLocalization').val('');
            reinitPage();
            $("#searchCvsMobile").submit();
            loadingFullShow();
        });

        $(".cvsFilterButton").click(function () {
            reinitPage();
            $("#searchCvsMobile").submit();
            loadingFullShow();
        });


        $("#js_cvsPreFilterButton").click(function () {
            if ($('#MultifiltersDataModel_SearchText').val().length > 0 || $('#MultifiltersDataModel_SelectedLocalization').val().length > 0) {
                clientStorage.inCookie().set(FirstSearchKeyName, $('#MultifiltersDataModel_SelectedLocalization').val(), 30, "/");
                reinitPage();
                $("#searchCvsMobile").submit();
                loadingFullShow();
            }
            else {
                $("#divErrorSearch").removeClass("hide");
            }
        });

        $(".js_hide_popup").click(function () {
            $(this).closest(".popup").removeClass("show_popup");
            $(".overlay").addClass("hide");
            $("body").removeClass("flowHid");
        });

        $("[data-selected-id]").click(function () {
            $(`#${$(this).data("selected-id")}`).val($(this).attr("value"));
            reinitPage();
            $("#searchCvsMobile").submit();
            loadingFullShow();
        });
    }

})(jQuery);

function showPopupAllFilters() {
    popupDetail('#popupAllFilters');
}

function InitializeCvList(configCvListModel) {
    this.configCvListModel = configCvListModel;
    FirstSearchKeyName = "FIRST_CVSEARCH_" + configCvListModel.idPortal + "_" + configCvListModel.idCompany;
}

function seeLess(event) {
    const eventElement = event.target;
    const seeMoreText = eventElement.getAttribute('data-see-more-text');
    const contenedor = eventElement.closest('.field_checkbox_box');
    const elements = contenedor.querySelectorAll('label.checkbox');

    elements.forEach((element, idx) => {
        if (idx > 2 && !element.querySelector('input').checked) {
            element.classList.add('hide');
        }
    });

    const elementsToHide = Array.prototype.slice.call(elements).filter(x => x.classList.contains('hide'));
    if (!elementsToHide.length) return;

    if (seeMoreText) eventElement.innerText = seeMoreText
    eventElement.onclick = seeMore;
}

function seeMore(event) {
    const eventElement = event.target;
    const seeLessText = eventElement.getAttribute('data-see-less-text');
    const contenedor = eventElement.closest('.field_checkbox_box');
    const elements = contenedor.querySelectorAll('label.checkbox');
    if (elements.length <= 3) return;
    elements.forEach(element => {
        element.classList.remove('hide');
    });
    if (seeLessText) eventElement.innerText = seeLessText;
    eventElement.onclick = seeLess;
}

function setupLinkedDropdowns(minDropdownId, maxDropdownId) {
    const minSelect = document.getElementById(minDropdownId);
    const maxSelect = document.getElementById(maxDropdownId);

    if (!minSelect || !maxSelect) {
        return;
    }
    maxSelect.addEventListener("change", function () {
        const maxValue = parseInt(maxSelect.value);
        const minValue = parseInt(minSelect.value);

        if (minValue > maxValue) {
            minSelect.value = maxValue.toString();
        }
    });

    minSelect.addEventListener("change", function () {
        const minValue = parseInt(minSelect.value);
        const maxValue = parseInt(maxSelect.value);

        if (minValue > maxValue) {
            maxSelect.value = minValue.toString();
        }
    });
}

function reinitPage() {
    $("#Pager_PageSelected").val(1);
}

function getFormattedDate() {
    const date = new Date();

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0'); // Enero es 0
    const day = String(date.getDate()).padStart(2, '0');

    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    const milliseconds = String(date.getMilliseconds()).padStart(3, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;
}

//FIN TODO