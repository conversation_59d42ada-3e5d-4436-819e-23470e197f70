(() => {
    let _awardId;
    let _listAwards;
    let _txAwardName;
    let _txAwardSource;
    let _txAwardWeb;
    let _txAwardYear;
    let _btnSaveAwards;
    let _parametersJs;

    window.addEventListener("load", load());

    function load() {
        loadElements();
        if (_listAwards != null && _btnSaveAwards != null)
        {
            loadEvents();
        }       
    }

    function loadElements() {
        _parametersJs = window.parametersJs;

        _listAwards = document.getElementById("listAwards");
        _txAwardName = document.getElementById("txAwardName");
        _txAwardSource = document.getElementById("txAwardSource");
        _txAwardWeb = document.getElementById("txAwardWeb");
        _txAwardYear = document.getElementById("txAwardYear");
        _btnSaveAwards = document.getElementById("btnSaveAwards");
    }

    function loadEvents() {
        _listAwards.addEventListener('click', function (event) { listAwardsClick(event); });
        if (_parametersJs.AwardsModel.IsAwardsEditable === true) {
            _btnSaveAwards.addEventListener('click', function (event) { saveAwards(); });
        }
        document.getElementById("bttDeletePopUpOk").addEventListener('click', function (event) { deleteAward(event); });
    }

    $("#bttCancel").click(function () {
        $('#divAwardParams').addClass('hide');
        $("#divAddAward").removeClass("hide");
        $("#p_premio").addClass("hide");
        $("#awardsList").removeClass("hide");
        $('#EditAward').addClass("hide");
    });

    $("#divAddAward").click(function () {
        prepareScreenForItem();
    });

    function prepareScreenForItem() {
        clearMessageErrors();

        _parametersJs.AwardsModel.Id = "";
        _txAwardName.value = "";
        _txAwardSource.value = "";
        _txAwardWeb.value = "";
        _txAwardYear.value = "";

        $("#divAddAward").addClass("hide");
        $("#p_premio").removeClass("hide");
        $("#divAwardParams").removeClass('hide');
    }

    function saveAwards() {
        if (!isFormValid()) {
            return;
        }

        saveAwardsInServer(getModel());
    }

    function getModel() {
        let model = _parametersJs.AwardsModel;

        model.Name = _txAwardName.value;
        model.Source = _txAwardSource.value;
        model.Web = _txAwardWeb.value;
        model.Year = parseInt(_txAwardYear.value);

        return model;
    }

    function saveAwardsInServer(awardsModel) {
        let formData = new FormData();
        formData.append('visionAwardsItemModelJson', JSON.stringify(awardsModel));

        showLoaderAndClosePopups();

        $.ajax({
            url: _parametersJs.UrlSaveAward,
            type: "POST",
            processData: false,
            contentType: false,
            data: formData,
            success: function (dataView) {
                saveAwardsInServerEnd();
            },
            error: function (er) {
                alert(er.statusText);
            },
            complete: function () {
                loadingFullHide();
            }
        });
    }

    function saveAwardsInServerEnd() {
        window.location.href = _parametersJs.UrlListAwards;
    }

    function deleteAwardsInServer(awardId) {
        let formData = new FormData();
        formData.append('awardClientId', awardId);

        showLoaderAndClosePopups();

        $.ajax({
            url: _parametersJs.UrlDeleteAward,
            type: "POST",
            processData: false,
            contentType: false,
            data: formData,
            success: function (dataView) {
                deleteAwardsInServerEnd();
            },
            error: function (er) {
                alert(er.statusText);
            },
            complete: function () {
                loadingFullHide();
            }
        });
    }

    function deleteAwardsInServerEnd() {
        window.location.href = _parametersJs.UrlListAwards;
    }

    function isFormValid() {
        let $validationForm = $("#validationForm");

        clearMessageErrors();

        if ($validationForm.data("validator").pendingRequest !== 0 || $validationForm.valid()) {
            return true;
        }

        return false;
    }

    function clearMessageErrors() {
        let elementMessageError = document.getElementsByName("element-error");
        let parentNode;

        elementMessageError.forEach(function (element) {
            parentNode = element.parentNode;
            parentNode.removeChild(element);
        });
    }

    function listAwardsClick(event) {
        let target = event.target;

        if (target.getAttribute("action") === "editAction") {
            editAwardPrepare(getIdAward(target));
            return;
        }

        if (target.getAttribute("action") === "deleteAction") {
            deleteAwardPrepare(getIdAward(target));
            return;
        }
    }

    function getIdAward(element) {
        let awardId = element.parentElement.parentElement.getAttribute("awardId");

        if (awardId === null) {
            awardId = element.parentElement.parentElement.parentElement.getAttribute("awardId");
        }

        return awardId;
    }

    function editAwardPrepare(awardId) {
        let award = _parametersJs.ListAwards.find(function (awardItem) { return awardItem.Id === awardId; });

        if (award === undefined) {
            return;
        }

        prepareScreenForItem();
        setModelToScreen(award);
    }

    function deleteAwardPrepare(awardId) {
        _awardId = awardId;
        $("#DeletePopUp").removeClass("hide");
    }

    function deleteAward() {
        deleteAwardsInServer(_awardId);
    }

    function setModelToScreen(award) {
        _parametersJs.AwardsModel = award
        _txAwardName.value = award.Name;
        _txAwardSource.value = award.Source;
        _txAwardWeb.value = award.Web;
        _txAwardYear.value = award.Year;
    }



})();

function resetForm() {
    closePopUp()
    var form = document.getElementById("validationForm");
    form.reset();
}

$(document).ready(function () {
    const overlay = document.querySelector('.overlay');
    $(overlay).off('click').on('click', resetForm);
});