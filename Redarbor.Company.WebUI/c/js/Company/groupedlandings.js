$(window).on('pageshow', function () {
    var ids = $('[id*="bt_m_"]');

    for (var i = 0; i < ids.length; i++) {
        manageGroups(ids[i].id, false);
    }
});

$(document).ready(function () {
    $('button').click(function () { manageGroups(this.id, true); });
});

function manageGroups(btn, check) {
    if (btn == "" || btn == undefined || btn == null) {
        return;
    }
    var bt = $('#' + btn)[0];
    var parent = $('#' + btn).parent().parent();
    var group = parent.attr("data-g");
    var indexString = parent.attr("data-i");
    if (group == undefined && indexString == undefined) {
        return;
    }

    if (check) {
        var index = parseInt(indexString);
        if (bt.id.indexOf("bt_m_") > -1) {
            if (index == 0 || $('#' + bt.id).hasClass('disabled')) {
                $('#' + bt.id).addClass('disabled');
                return;
            }
            $('#' + bt.id.replace('m', 'p')).removeClass('disabled');
            index = index - 1;
        } else {
            if (index == obj.LandingGroupsProductsContentDataModel.find((o) => { return o["SubGroupId"] == group }).Products.length - 1) {
                $('#' + bt.id).addClass('disabled');
                return;
            }
            $('#' + bt.id.replace('p', 'm')).removeClass('disabled');
            index = index + 1;
        }
    } else {
        if (index == obj.LandingGroupsProductsContentDataModel.find((o) => { return o["SubGroupId"] == group }).Products.length - 1) {
            $('#' + bt.id).removeClass('disabled');
            $('#' + bt.id.replace('m', 'p')).addClass('disabled');
        }

        if (index < obj.LandingGroupsProductsContentDataModel.find((o) => { return o["SubGroupId"] == group }).Products.length - 1) {
            $('#' + bt.id).removeClass('disabled');
        }
    }

    var objGroup = obj.LandingGroupsProductsContentDataModel.find((o) => { return o["SubGroupId"] == group });
    var product = obj.LandingGroupsProductsContentDataModel.find((o) => { return o["SubGroupId"] == group }).Products[index];
    var html = '';

    for (var i = 0; i < product.FeaturesDescriptions.length; i++) {

        var item = product.FeaturesDescriptions[i];

        html += '<li class="mt10">';
        html += '   <span title="Contiene" class="icon ' + (item.Available == 1 ? "estado_verde_s" : "estado_rojo_s") + ' fl d_ib"></span>';
        html += '   <span class="pl10px ' + (item.Place == 1 ? "fw_b" : "") + '">' + item.Literal + '</span>';
        html += '</li>';
    }

    parent.find('#s_price').text(product.PriceLiteral.toString());
    parent.find('#s_units').val(product.Units.toString());
    parent.find('#features').html(html);

    var linkToCart = `${urlCart}?prod=${product.ProductId}&p=${param_p}`;

    if (objGroup.BtnOriginId != null) {
        linkToCart = `${linkToCart}&btn=${objGroup.BtnOriginId}`;
    }

    parent.find("#lnk_buy").attr("href", linkToCart);

    if (index > 0) {
        parent.find('#s_saving').text(product.Saving.toString());
        parent.find('#s_price_multiple').text(product.PriceBaseUnitLiteral);
        parent.find('#lit_you_save').text(lit_you_save);

        if (index == obj.LandingGroupsProductsContentDataModel.find((o) => { return o["SubGroupId"] == group }).Products.length - 1) {
            if (check) {
                $('#' + bt.id).addClass('disabled');
            }
        }

    } else {
        $('#' + bt.id).addClass('disabled');
        parent.find('#s_saving').text('');
        parent.find('#s_price_multiple').text('');
        parent.find('#lit_you_save').text(lit_landing_info);
    }

    parseInt($(bt).parent().parent().attr("data-i", index));
}