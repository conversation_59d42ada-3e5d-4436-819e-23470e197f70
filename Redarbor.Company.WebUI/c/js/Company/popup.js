var PopUp = PopUp || {};

PopUp.action = (frmAction) => {
    PopUp.show(frmAction.form);
    $(frmAction.btnAction).one("click", function (event) {
        frmAction.functionAction.apply(this, [frmAction.messageInit, frmAction.messageError]);
    });
    $(frmAction.btnCancel).click(function () {
        PopUp.hide(frmAction.form)
    });
}

PopUp.show = (name) => {
    if ($(name).hasClass("hide")) {
        $(name).removeClass("hide");
    };
};

PopUp.hide = (name) => {

    if (!$(name).hasClass("hide")) {
        $(name).addClass("hide");
    };
};