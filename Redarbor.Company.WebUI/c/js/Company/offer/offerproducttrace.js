var configProductTraceModel = null;

function InitializeProductTraceModel(configurationProductTraceModel) { this.configProductTraceModel = configurationProductTraceModel; };

$(document).ready(function () {
    $("#search_DateFilterInit").datepicker({
        maxDate: 0,
        dateFormat: configProductTraceModel.dateFormatPortal.replace("yyyy", "yy"),
        setDate: $("#search_DateFilterInit").val(),
        onSelect: function () {
            var date = $('#search_DateFilterInit').datepicker('getDate');
            if (date !== null) {
                $('#search_DateFilterEnd').datepicker("option", "minDate", date);
            }
            SetAndSubmit();
        }
    });
    $("#search_DateFilterEnd").datepicker({
        maxDate: 0,
        dateFormat: configProductTraceModel.dateFormatPortal.replace("yyyy", "yy"),
        minDate: $("#search_DateFilterInit").val(),
        onSelect: function () {
            var date = $('#search_DateFilterEnd').datepicker('getDate');
            if (date !== null) {
                var dt1 = $('#search_DateFilterInit').datepicker('getDate');
                var dt2 = $('#search_DateFilterEnd').datepicker('getDate');
                //check to prevent a user from entering a date below date of dt1
                if (dt2 <= dt1) {
                    $('#search_DateFilterEnd').datepicker('option', 'minDate', dt1);
                    $('#search_DateFilterEnd').datepicker('setDate', dt1);
                }
                $("#search_DateFilterInit").datepicker("option", "maxDate", date);
            }
            SetAndSubmit();
        }
    });
});

function SetAndSubmit() {

    var dt1 = $('#search_DateFilterInit').datepicker('getDate');
    $("#search_DateTimeIntStart").val(DateToString(dt1));

    var dt2 = $('#search_DateFilterEnd').datepicker('getDate');
    $("#search_DateTimeIntEnd").val(DateToString(dt2));

    $("#companyProductTracePagination").submit();
}
function DateToString(dt) {
    var day = ("0" + dt.getDate()).slice(-2);
    var month = ("0" + (dt.getMonth() + 1)).slice(-2);
    var year = dt.getFullYear().toString();

    return year + month + day;
}
