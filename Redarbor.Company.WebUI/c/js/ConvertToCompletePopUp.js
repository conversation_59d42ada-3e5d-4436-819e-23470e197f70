var convertToCompleteModel = null;

function InitializeConvertToCompletePopUp(convertToCompleteModel) {
    this.convertToCompleteModel = convertToCompleteModel;
};


$(document).ready(function () {

    $(".showConvertToCompleteCompPopUpBtt").click(function () {
        $("#encryptedoffer").val($(this).attr("data-idoffer"));
        if ($(this).attr("data-featureprod") != "") {
            $("#featureprod").val($(this).attr("data-featureprod"));
        }
        $('#PopUpComparative').removeClass("hide");
    });
   
    $(".convertToCompleteImproveOfferButton").click(function () {
        $("#encryptedoffer").val($(this).attr("data-idoffer"));
        if ($(this).attr("data-featureprod") != "") {
            $("#featureprod").val($(this).attr("data-featureprod"));
        }

        AjaxCallConvertToComplete();
    });

    $(".showConvertToCompleteCompPopUpBasicBtt").click(function () {
        $("#encryptedoffer").val($(this).attr("data-idoffer"));
        if ($(this).attr("data-featureprod") != "") {
            $("#featureprod").val($(this).attr("data-featureprod"));
        }
        $('#PopUpComparativeBasic').removeClass("hide");
    });

    $(".showConvertToCompleteCompPopUpBttOLD").click(function () {
        $("#encryptedoffer").val($(this).attr("data-idoffer"));
        if ($(this).attr("data-featureprod") != "") {
            $("#featureprod").val($(this).attr("data-featureprod"));
        }
        ShowPopUp('#PopUpComparative');
    });

    $("#popUpConverToCompleteBtt, #popUpConverToCompleteBttFromBasic").click(function () {
        AjaxCallConvertToComplete();
    });

    function AjaxCallConvertToComplete() {
        $.ajax({
            async: false,
            cache: false,
            dataType: "json",
            type: 'POST',
            url: convertToCompleteModel.ajaxManagePopUpConvertToCompleteBtt,
            data: {},
            success: function (respuesta) {
                if (respuesta != null && respuesta.toString() != '') {

                    if (respuesta === 1) {
                        var location = convertToCompleteModel.goToPostPublishStepTwo.replace(/&amp;/g, '&').replace(/%23/g, '#').replace('#idofenc#', $("#encryptedoffer").val());
                        window.location.href = location;
                        loadingFullShow();
                    }
                    if (respuesta === 2) {
                        var location = convertToCompleteModel.goToMPCart.replace(/&amp;/g, '&').replace(/%23/g, '#').replace('#product', $("#featureprod").val()).replace('#idofenc#', $("#encryptedoffer").val());
                        trackButtonPage(1, convertToCompleteModel.idPage);
                        window.location.href = location;
                        loadingFullShow();
                    }
                    if (respuesta === 3) {
                        var location = convertToCompleteModel.goToChooseMP.replace(/&amp;/g, '&').replace(/%23/g, '#').replace('#idofenc#', $("#encryptedoffer").val());
                        trackButtonPage(1, convertToCompleteModel.idPage);
                        window.location.href = location;
                        loadingFullShow();
                    }
                    if (respuesta === 4) {
                        event.preventDefault();
                        convertOfferToComplete(document.getElementById('encryptedoffer').value);
                    }
                }
            },
        });
    }


    function convertOfferToComplete(p_offers) {   

        var btnConvertToComplete = !convertToCompleteModel.buttonConvertToCompleteEncryptedName ? "" : convertToCompleteModel.buttonConvertToCompleteEncryptedName;

        loadingFullShow();
        $.blockUI({ message: "<h1>Convirtiendo a completa...</h1>" });

        $.ajax({
            async: true,
            cache: false,
            dataType: "html",
            type: 'POST',
            url: convertToCompleteModel.ajaxConvertToCompleteUrl,
            data: "idoffers=" + p_offers + "&ac=6" + "&btnConvertEnc=" + btnConvertToComplete,
            success: function (respuesta) {
                let obj = null;

                if (isJson(respuesta)) {
                    loadingFullHide();
                    obj = JSON.parse(respuesta);
                }               
                if (+respuesta > 0
                    || respuesta.toLowerCase() == "true" || obj != null && obj.isValid) {
                    location.reload();
                }
            },
            beforeSend: function () { },
            error: function (objXMLHttpRequest) { }
        });
    }

});