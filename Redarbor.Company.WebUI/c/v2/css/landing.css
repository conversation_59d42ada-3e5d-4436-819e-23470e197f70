[class*=field_]:after {
    content: '';
    display: table;
    clear: both
}

.field_group_hor, [class*=field_].hor {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start
}

    .field_group_hor:not([class*=v]), [class*=field_].hor:not([class*=v]) {
        -webkit-box-align: start;
        -ms-flex-align: start;
        align-items: flex-start
    }

    .field_group_hor > label:not([class*=mt]):not(.checkbox):not(.radio), [class*=field_].hor > label:not([class*=mt]):not(.checkbox):not(.radio) {
        margin-top: 14px
    }

    .field_group_hor.small > label:not(.checkbox):not(.radio), [class*=field_].hor.small > label:not(.checkbox):not(.radio) {
        margin-top: 11px
    }

    .field_group_hor > label.inside_label, [class*=field_].hor > label.inside_label {
        margin-top: 38px
    }

.field_select .field_tag {
    margin-bottom: 0
}

    .field_select .field_tag span {
        margin-bottom: 0;
        margin-top: 10px
    }

.field_select .opt_link {
    display: inline-block;
    padding: 14px 0
}

.box {
    background-color: #fff;
    border-radius: 4px;
    -webkit-box-shadow: 0 0 9px 1px rgba(53,62,74,0.07);
    box-shadow: 0 0 9px 1px rgba(53,62,74,0.07);
    padding: 30px;
    position: relative
}

    .box > .i_close, .box > .i_close_w {
        position: absolute;
        right: 15px;
        top: 15px
    }

.box_border {
    border: 1px solid #e1e5ea;
    background-color: #fff;
    border-radius: 4px;
    padding: 15px
}

.box_error, .box_info, .box_ok {
    border-radius: 3px;
    position: relative;
    padding: 20px 35px 20px 20px
}

    .box_error h3, .box_info h3, .box_ok h3 {
        font-size: 17px;
        font-size: 1.7rem
    }

        .box_error h3 + p, .box_info h3 + p, .box_ok h3 + p {
            margin-top: 5px
        }

    .box_error .i_close_w, .box_error > .i_close, .box_info .i_close_w, .box_info > .i_close, .box_ok .i_close_w, .box_ok > .i_close {
        position: absolute;
        right: 10px;
        top: 10px
    }

    .box_error.small, .box_info.small, .box_ok.small {
        color: #fff;
        padding: 13px 35px 13px 15px
    }

        .box_error.small *, .box_info.small *, .box_ok.small * {
            color: #fff
        }

        .box_error.small h3, .box_info.small h3, .box_ok.small h3 {
            font-size: 14px;
            font-size: 1.4rem;
            font-weight: bold
        }

            .box_error.small h3 + p, .box_error.small p + p, .box_info.small h3 + p, .box_info.small p + p, .box_ok.small h3 + p, .box_ok.small p + p {
                margin-top: 5px
            }

        .box_error.small a, .box_info.small a, .box_ok.small a {
            text-decoration: underline
        }

.box_ok {
    background-color: #f0fcf7;
    border: 1px solid #94c161;
    color: #7bac43
}

    .box_ok * {
        color: #7bac43
    }

    .box_ok.small {
        background-color: #7bac43;
        border: 1px solid #7bac43
    }

.box_error {
    background-color: #fbeded;
    border: 1px solid #e26868;
    color: #c82626
}

    .box_error * {
        color: #c82626
    }

    .box_error.small {
        background-color: #da3d3d;
        border: 1px solid #da3d3d
    }

.box_info {
    background-color: #fff5eb;
    border: 1px solid #ffb166;
    color: #e57000
}

    .box_info * {
        color: #e57000
    }

    .box_info.small {
        background-color: #ff9733;
        border: 1px solid #ff9733
    }

.b_primary, .b_primary_inv, .boton_base {
    border-radius: 4px;
    cursor: pointer;
    display: inline-block;
    position: relative;
    text-align: center;
    width: auto;
    border: 1px solid transparent;
    padding: 13px 17px;
    z-index: 1
}

    .b_primary:not(span):after, .b_primary_inv:not(span):after, .boton_base:not(span):after {
        border-radius: 4px;
        position: absolute;
        -webkit-transition: background-color 0.4s;
        -o-transition: background-color 0.4s;
        transition: background-color 0.4s;
        content: '';
        background-color: rgba(53,62,74,0);
        bottom: -1px;
        left: -1px;
        right: -1px;
        top: -1px;
        z-index: -1
    }

    .b_primary.rounded, .b_primary.rounded:after, .b_primary_inv.rounded, .b_primary_inv.rounded:after, .boton_base.rounded, .boton_base.rounded:after {
        border-radius: 35px
    }

    .b_primary.circle, .b_primary.circle:after, .b_primary_inv.circle, .b_primary_inv.circle:after, .boton_base.circle, .boton_base.circle:after {
        border-radius: 50%;
        padding: 15px;
        width: auto
    }

        .b_primary.circle:hover, .b_primary_inv.circle:hover, .boton_base.circle:hover {
            background-color: transparent
        }

    .b_primary:hover:not(span):not(.b_transparent), .b_primary_inv:hover:not(span):not(.b_transparent), .boton_base:hover:not(span):not(.b_transparent) {
        -webkit-box-shadow: none;
        box-shadow: none;
        cursor: pointer
    }

        .b_primary:hover:not(span):not(.b_transparent):after, .b_primary_inv:hover:not(span):not(.b_transparent):after, .boton_base:hover:not(span):not(.b_transparent):after {
            background-color: rgba(53,62,74,0.1)
        }

    .b_primary.big, .b_primary_inv.big, .boton_base.big {
        font-size: 16px;
        font-size: 1.6rem;
        font-weight: bold;
        padding: 14px 35px
    }

    .b_primary.small, .b_primary_inv.small, .boton_base.small {
        padding: 10px 12px
    }

    .b_primary.tiny, .b_primary_inv.tiny, .boton_base.tiny {
        font-size: 13px;
        font-size: 1.3rem;
        font-weight: normal;
        padding: 8px 15px
    }

.b_primary {
    color: #fff;
    font-weight: bold;
    background-color: #1e82c4
}

    .b_primary:hover {
        color: #fff
    }

input.b_primary:hover {
    background-color: #1b76b2
}

.b_primary_inv {
    background-color: #fff;
    color: #1e82c4;
    border-color: #1e82c4;
    z-index: 1
}

    .b_primary_inv:hover {
        color: #fff;
        background-color: #1e82c4
    }

        .b_primary_inv:hover:after {
            display: none !important
        }

input.b_primary_inv {
    padding: 0 15px;
    height: 42px;
}

button, input, select, textarea {
    border: 1px solid #e1e5ea;
    background-color: #fff;
    border-radius: 4px;
    display: block;
    color: #313944;
    font-weight: normal;
    width: 100%;
    font-family: inherit;
    line-height: 1.4;
    padding: 13px 15px
}

    button.small, input.small, select.small, textarea.small {
        padding: 10px 12px
    }

[class*=field_] {
    margin-bottom: 25px
}

    [class*=field_]:not(.field_group) .field_on_off {
        display: inline-block;
        margin-bottom: 0
    }

    [class*=field_].field_info {
        margin-bottom: 0
    }

input:not([type=button]):not([type=submit]):not([readonly]):focus, select:not([readonly]):focus, textarea:not([readonly]):focus {
    border-color: #1e82c4
}

label {
    display: inline-block;
    margin-bottom: 8px
}

.no_label {
    margin-top: calc(20px + 7px)
}

.required {
    color: #c82626;
    margin-left: 2px
}

.field_input input[type=time] {
    padding: 12px 15px
}

.field_input input[type=number] {
    padding-right: 5px
}

.field_input.small input {
    padding: 10px 12px
}

    .field_input.small input[type=number] {
        padding-right: 5px
    }

select {
    cursor: pointer;
    background-image: url("../img/sprite_basic.svg?v=16");
    background-repeat: no-repeat;
    background-position: calc(100% - 15px) 0;
    padding-right: 35px
}

    select.small {
        padding: 10px 35px 10px 12px
    }

.field_select.small select {
    background-position: calc(100% - 12px) -4px;
    padding: 10px 35px 10px 12px
}

.cols {
    display: table;
    border-collapse: separate;
    width: 100%
}

    .cols.mB_neg {
        width: calc(100% + 30px * 2)
    }

    .cols > :not(script) {
        display: table-cell;
        vertical-align: middle
    }

        .cols > :not(script).vt {
            vertical-align: top
        }

        .cols > :not(script).vb {
            vertical-align: bottom
        }

    .cols.flex {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -ms-flex-line-pack: justify;
        align-content: space-between;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center
    }

        .cols.flex.vb_fx {
            -webkit-box-align: end;
            -ms-flex-align: end;
            align-items: flex-end
        }

        .cols.flex.vj_fx {
            -webkit-box-align: stretch;
            -ms-flex-align: stretch;
            align-items: stretch
        }

        .cols.flex.vt_fx {
            -webkit-box-align: start;
            -ms-flex-align: start;
            align-items: flex-start
        }

        .cols.flex > * {
            -webkit-box-flex: 1;
            -ms-flex: auto;
            flex: auto
        }

        .cols.flex > .has_width {
            -webkit-box-flex: 0;
            -ms-flex: none;
            flex: none
        }

        .cols.flex > :not(.has_width) {
            min-width: 0
        }

        .cols.flex .fx_none {
            -webkit-box-flex: 0;
            -ms-flex: none;
            flex: none
        }

.overlay {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    top: 0;
    background-color: rgba(53,62,74,0.6);
    z-index: 999
}


.popup {
    position: fixed
}

    .popup:not(.flex) {
        background-color: #fff;
        border-radius: 4px;
        -webkit-box-shadow: 0 0 15px 4px rgba(53,62,74,0.1);
        box-shadow: 0 0 15px 4px rgba(53,62,74,0.1);
        left: 50%;
        min-width: 550px;
        top: 50%;
        -webkit-transform: translate(-50%,calc(-50% + 0.5px));
        -ms-transform: translate(-50%,calc(-50% + 0.5px));
        transform: translate(-50%,calc(-50% + 0.5px));
        z-index: 9999
    }

        .popup:not(.flex):after {
            content: '';
            display: table;
            clear: both
        }

        .popup:not(.flex) > div {
            padding: 30px;
            max-height: 95vh;
            overflow-y: auto;
            overflow-x: hidden
        }

            .popup:not(.flex) > div.pb0 {
                padding-bottom: 0
            }

            .popup:not(.flex) > div.pAll20 {
                padding: 20px
            }

    .popup.flex {
        bottom: 0;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        left: 0;
        right: 0;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        justify-content: center;
        top: 0;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        z-index: 9999
    }

        .popup.flex:not(.bg_none) {
            background-color: rgba(53,62,74,0.6)
        }

        .popup.flex > div {
            background-color: #fff;
            border-radius: 4px;
            -webkit-box-shadow: 0 0 15px 4px rgba(53,62,74,0.1);
            box-shadow: 0 0 15px 4px rgba(53,62,74,0.1);
            position: relative;
            min-width: 400px
        }

            .popup.flex > div:after {
                content: '';
                display: table;
                clear: both
            }

            .popup.flex > div > div {
                max-height: 95vh;
                overflow-y: auto;
                overflow-x: hidden;
                padding: 30px 30px 0 30px
            }

                .popup.flex > div > div:after {
                    content: '';
                    display: block;
                    padding-bottom: 30px
                }

                .popup.flex > div > div.pb0 {
                    padding-bottom: 0
                }

                .popup.flex > div > div.pAll20 {
                    padding: 20px
                }

    .popup h2 {
        padding-left: 30px;
        padding-right: 30px;
        text-align: center
    }

        .popup h2:not([class*=mb]) {
            margin-bottom: 20px
        }

    .popup .desc {
        text-align: center
    }

    .popup .form_action {
        border-top: 0;
        padding-top: 0
    }

    .popup .i_close, .popup .i_close_w {
        position: absolute;
        right: 15px;
        top: 15px
    }

    .popup .header_img {
        margin: -30px -30px 30px;
        max-width: none
    }

.blockUI {
    z-index: 9999
}

.i_field_select {
    background-position: calc(100% - 15px) 0
}

.i_field_select_small {
    background-position: calc(100% - 12px) -4px
}

.i_field_time {
    background-position: calc(100% - 15px) -45px
}

.i_field_calendar {
    background-position: calc(100% - 15px) -90px
}

.i_field_calendar_small {
    background-position: calc(100% - 12px) -92px
}

.i_field_captcha {
    width: 19px;
    height: 21px;
    background-position: -109px -150px
}

.i_field_search {
    width: 45px;
    height: 45px;
    background-position: -45px -135px
}

.i_field_search_small {
    background-position: -47px -138px
}

.i_field_search_w {
    width: 45px;
    height: 45px;
    background-position: 0 -135px
}

.i_field_search_w_small {
    background-position: -2px -138px
}

.i_search {
    width: 24px;
    height: 24px;
    background-position: -56px -146px
}

.i_search_w {
    width: 24px;
    height: 24px;
    background-position: -11px -146px
}

.i_close {
    background-position: -105px -135px;
    height: 14px;
    width: 14px
}

.i_close_w {
    background-position: -90px -135px;
    height: 14px;
    width: 14px
}

.i_next {
    background-position: -134px -135px;
    height: 14px;
    width: 14px
}

.i_next_w {
    background-position: -164px -135px;
    height: 14px;
    width: 14px
}

.i_prev {
    background-position: -120px -135px;
    height: 14px;
    width: 14px
}

.i_prev_w {
    background-position: -149px -135px;
    height: 14px;
    width: 14px
}

.i_tooltip {
    background-position: -90px -150px;
    height: 18px;
    width: 18px
}

.i_remove_small {
    background-position: -107px -137px;
    height: 10px;
    width: 10px
}

.icon {
    display: inline-block;
    vertical-align: middle;
    background-image: url("../img/sprite-landing-AE.svg?v=16");
    background-repeat: no-repeat
}

    .icon.i_close, .icon.i_close_w, .icon.i_field_search, .icon.i_next, .icon.i_next_w, .icon.i_prev, .icon.i_prev_w, .icon.i_search, .icon.i_search_w, .icon.i_tooltip {
        background-image: url("../img/sprite_basic.svg?v=16");
        background-repeat: no-repeat
    }

.icon_basic {
    background-image: url("../img/sprite_basic.svg?v=16");
    background-repeat: no-repeat
}

* {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}

a, body, div, html, span {
    border: 0;
    font-size: 100%;
    font-style: inherit;
    font-weight: inherit;
    outline: 0;
    vertical-align: baseline
}

html {
    min-height: 100%;
    -webkit-text-size-adjust: 100%;
    -moz-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    text-size-adjust: 100%
}

footer, main {
    display: block
}

body {
    background-color: white !important
}

a:active, a:focus, a:hover {
    outline: 0;
    text-decoration: none
}

a img, img {
    border: 0;
    max-width: 100%;
    vertical-align: middle
}

.container {
    margin: 0 auto;
    padding: 0 15px
}

a label {
    color: #313944;
}

.logo {
    display: inline-block;
    margin: 11px 0
}

footer {
    background: #efefef;
    padding: 15px 0;
    border-top: 1px solid #ddd;
    color: #808080;
    font-size: 14px;
    font-size: 1.4rem
}

    footer h3 {
        font-size: 15px;
        font-size: 1.5rem;
        margin: 15px 0;
        color: #313944
    }

    footer a {
        color: #808080;
        display: inline-block;
        font-size: 14px;
        font-size: 1.4rem
    }

        footer a:hover {
            color: #535151
        }

    footer ul {
        font-size: 14px;
        font-size: 1.4rem;
        display: inline-block
    }

        footer ul li {
            display: block;
            margin-bottom: 0
        }

            footer ul li > :not(script) {
                display: inline-block
            }

button, input, select, textarea {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none
}

input, select, textarea {
    font-size: 16px;
    font-size: 1.6rem
}

label {
    font-weight: bold
}

ul li {
    list-style: none
}

h2 {
    font-weight: bold !important;
    font-size: 16px !important;
    font-size: 1.6rem !important;
    margin-bottom: 10px
}

.tLayoutFix {
    table-layout: fixed
}

.menu_tools {
    padding: 15px 0
}

    .menu_tools * {
        z-index: 99
    }

    .menu_tools div > div.dFlex {
        -webkit-box-pack: end;
        -ms-flex-pack: end;
        justify-content: flex-end;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center
    }

        .menu_tools div > div.dFlex a {
            margin-left: 30px
        }

            .menu_tools div > div.dFlex a:hover {
                color: #fff
            }

.b_primary:not([class*=fs]), .b_primary_inv:not([class*=fs]), p:not([class*=fs]) {
    font-size: 16px;
    font-size: 1.6rem
}

.fc_brand2 {
    color: #426b4b
}

.fc_brand3 {
    color: #ff5b00
}

.fc_brand4 {
    color: #dea84e
}

.b_primary {
    background-color: #ff5b00
}

input.b_primary:hover {
    background-color: #eb5400
}

.b_primary.blue {
    font-weight: normal;
    font-size: 14px;
    font-size: 1.4rem;
    background-color: #1058c1
}

input.b_primary.blue:hover {
    background-color: #0e4fae
}

.b_primary_inv:not(.blue) {
    color: #ff5b00;
    border-color: #ff5b00
}

    .b_primary_inv:not(.blue):hover {
        background-color: #ff5b00;
        color: #fff
    }

.intro {
    padding: 120px 0 160px;
    position: relative;
    color: #fff;
    background-image: url(../img/landing/forma-landing-AE-2.svg);
    background-repeat: no-repeat;
    background-size: cover;
    margin-top: -83px
}

.box_fix {
    position: fixed;
    background: #036fd8;
    background: -webkit-gradient(linear,left top,right top,color-stop(26%,#0e54bd),to(#1782dd));
    background: -o-linear-gradient(left,#0e54bd 26%,#1782dd 100%);
    background: linear-gradient(90deg,#0e54bd 26%,#1782dd 100%);
    width: 100%;
    z-index: 9999
}

.offers_list {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    text-align: center
}

    .offers_list > .box.gratuita:before, .offers_list > .box.premium:before, .offers_list > .box.standard:before {
        -webkit-transform: skewX(-25deg);
        -ms-transform: skewX(-25deg);
        transform: skewX(-25deg);
        content: '';
        width: 70px;
        height: 4px;
        position: absolute;
        top: -2px;
        left: calc(50% - 35px)
    }

    .offers_list > .box.gratuita:before {
        background-color: #f1122d
    }

    .offers_list > .box.standard:before {
        background-color: #197cb5
    }

    .offers_list > .box.premium:before {
        background-color: #dea84e
    }

    .offers_list > .box ul {
        padding: 20px 0;
        margin: 15px 0;
        border-top: 1px dashed #e1e5ea;
        border-bottom: 1px dashed #e1e5ea
    }

        .offers_list > .box ul li {
            display: table;
            border-collapse: separate;
            width: 100%;
            text-align: left
        }

            .offers_list > .box ul li.mB_neg {
                width: calc(100% + 30px * 2)
            }

            .offers_list > .box ul li > :not(script) {
                display: table-cell;
                vertical-align: middle
            }

                .offers_list > .box ul li > :not(script).vt {
                    vertical-align: top
                }

                .offers_list > .box ul li > :not(script).vb {
                    vertical-align: bottom
                }

            .offers_list > .box ul li.flex {
                display: -webkit-box;
                display: -ms-flexbox;
                display: flex;
                -ms-flex-line-pack: justify;
                align-content: space-between;
                -webkit-box-align: center;
                -ms-flex-align: center;
                align-items: center
            }

                .offers_list > .box ul li.flex.vb_fx {
                    -webkit-box-align: end;
                    -ms-flex-align: end;
                    align-items: flex-end
                }

                .offers_list > .box ul li.flex.vj_fx {
                    -webkit-box-align: stretch;
                    -ms-flex-align: stretch;
                    align-items: stretch
                }

                .offers_list > .box ul li.flex.vt_fx {
                    -webkit-box-align: start;
                    -ms-flex-align: start;
                    align-items: flex-start
                }

                .offers_list > .box ul li.flex > * {
                    -webkit-box-flex: 1;
                    -ms-flex: auto;
                    flex: auto
                }

                .offers_list > .box ul li.flex > .has_width {
                    -webkit-box-flex: 0;
                    -ms-flex: none;
                    flex: none
                }

                .offers_list > .box ul li.flex > :not(.has_width) {
                    min-width: 0
                }

                .offers_list > .box ul li.flex .fx_none {
                    -webkit-box-flex: 0;
                    -ms-flex: none;
                    flex: none
                }

            .offers_list > .box ul li div {
                padding-right: 15px;
                vertical-align: top
            }

            .offers_list > .box ul li p {
                width: 100%;
                font-size: 14px;
                font-size: 1.4rem
            }

            .offers_list > .box ul li:not(:last-child) {
                margin-bottom: 15px
            }

.bg_blue {
    background: #036fd8;
    background: -webkit-gradient(linear,left top,right top,color-stop(26%,#0e54bd),to(#1782dd));
    background: -o-linear-gradient(left,#0e54bd 26%,#1782dd 100%);
    background: linear-gradient(90deg,#0e54bd 26%,#1782dd 100%)
}

.box_border.js_box_legal p {
    font-size: 12px;
    font-size: 1.2rem
}

.photos {
    position: relative;
    height: 300px;
    overflow: hidden
}

    .photos .box {
        padding: 15px;
        width: 150px;
        position: absolute
    }

        .photos .box:first-child {
            left: 5px;
            top: 20px
        }

            .photos .box:first-child span {
                background-position: 0 -7px
            }

        .photos .box:nth-child(2) {
            left: 190px;
            top: 80px
        }

            .photos .box:nth-child(2) span {
                background-position: -184px -7px
            }

        .photos .box:nth-child(3) {
            left: 375px;
            top: 40px
        }

            .photos .box:nth-child(3) span {
                background-position: -92px -7px
            }

        .photos .box_landing {
            overflow:initial;
        }


        .photos .box span {
            margin-top: -30px;
            background-image: url("../img/landing/trio-landing-AE.png");
            background-repeat: no-repeat;
            display: inline-block;
            background-size: 275px;
            width: 90px;
            height: 110px
        }

.field-validation-error span {
    position:relative;
    bottom: 0;
    left: 0;
    background:none;
    color: #fff;
    font-weight: normal;
    padding: 0 10px;
    border-radius: 3px;
    font-size: 13px;
    z-index: 9;
}

.icon {
    display: inline-block;
    vertical-align: middle;
    background-image: url("../img/landing/sprite-landing-AE.svg?v=16");
    background-repeat: no-repeat
}

.i_cv, .i_empresas, .i_eye, .i_flash, .i_postulaciones, .i_star, .i_usuarios {
    width: 42px;
    height: 42px
}

.i_flash {
    background-position: 0 0
}

.i_eye {
    background-position: -42px 0
}

.i_star {
    background-position: -84px 0
}

.i_usuarios {
    background-position: -126px 0
}

.i_cv {
    background-position: -168px 0
}

.i_empresas {
    background-position: -210px 0
}

.i_postulaciones {
    background-position: -252px 0
}

.i_publicar {
    height: 42px;
    width: 60px;
    background-position: -296px 0
}

.i_ok {
    width: 14px;
    height: 14px;
    background-position: -359px 1px
}

.i_ko {
    width: 14px;
    height: 14px;
    background-position: -359px -10px
}

.i_menu {
    width: 24px;
    height: 24px;
    background-position: -296px 1px
}

.i_ilust {
    width: 350px;
    height: 450px;
    background-position: 0px -43px
}

.transparent {
    background-color: transparent !important
}

    .transparent:hover:after {
        background-color: transparent !important
    }

@media only screen and (min-width:1200px) {
    .container {
        width: 1170px
    }
}

@media only screen and (min-width:1390px) {
    .b_primary.blue {
        background-color: transparent !important
    }

        .b_primary.blue:hover:after {
            background-color: transparent !important
        }
}

.bb1 {
    border-bottom: 1px solid #e1e5ea
}

.dFlex {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.vm_fx {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.tc_fx {
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.tj_fx {
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
}

.mtB {
    margin-top: 30px
}

.mbB {
    margin-bottom: 30px
}

.mb5 {
    margin-bottom: 5px
}

.mb10 {
    margin-bottom: 10px
}

.mt10 {
    margin-top: 10px
}

.mb15 {
    margin-bottom: 15px
}

.pAllB {
    padding: 30px
}

.plB {
    padding-left: 30px
}

.prB {
    padding-right: 30px
}

.ptB {
    padding-top: 30px
}

.pbB {
    padding-bottom: 30px
}

.pt15 {
    padding-top: 15px
}

.pb15 {
    padding-bottom: 15px
}

.pr15 {
    padding-right: 15px
}

.pl15 {
    padding-left: 15px
}

.hide {
    display: none !important
}

.posRel {
    position: relative
}

.posAbs {
    position: absolute
}

.top0 {
    top: 0
}

.right0 {
    right: 0
}

.vt {
    vertical-align: top
}

.tr {
    text-align: right
}

.tc {
    text-align: center
}

.tl {
    text-align: left
}

.fwB {
    font-weight: bold
}

.fs14 {
    font-size: 14px;
    font-size: 1.4rem
}

.fs16 {
    font-size: 16px;
    font-size: 1.6rem
}

.fs18 {
    font-size: 18px;
    font-size: 1.8rem
}

.fs20 {
    font-size: 20px;
    font-size: 2rem
}

.fs22 {
    font-size: 22px;
    font-size: 2.2rem
}

.fs30 {
    font-size: 30px;
    font-size: 3rem
}

.fs32 {
    font-size: 32px;
    font-size: 3.2rem
}

.fc_aux {
    color: #97a3b4
}

.fc_white {
    color: #fff
}

.fc_base {
    color: #313944
}

.fc_ok {
    color: #7bac43
}

.w25 {
    width: 25%
}

.w50 {
    width: 50%
}

.w60 {
    width: 60%
}

.w80 {
    width: 80%
}

.w100 {
    width: 100%
}

html {
    position: relative;
    background-color: #f1f2f3;
    font-size: 62.5%
}

body {
    color: #313944;
    background-color: #f1f2f3;
    font-family: Arial,sans-serif;
    font-size: 14px;
    font-size: 1.4rem;
    line-height: 1.4
}

a {
    text-decoration: none;
    color: #1c7bba
}

    a:hover {
        cursor: pointer;
        text-decoration: none;
        color: #135580
    }

        a:hover.fc_base {
            color: #313944
        }

        a:hover.fc_aux {
            color: #97a3b4
        }

h1, h2, h3, h4, h5, h6 {
    font-weight: normal
}

h1 {
    font-size: 20px;
    font-size: 2rem;
    line-height: 1.2
}

h2 {
    font-size: 18px;
    font-size: 1.8rem;
    line-height: 1.2
}

h3 {
    font-size: 16px;
    font-size: 1.6rem;
    line-height: 1.2
}

h4 {
    font-size: 14px;
    font-size: 1.4rem;
    line-height: 1.2
}

.container:after {
    content: '';
    display: table;
    clear: both
}

@media only screen and (max-width:1200px) {
    .container {
        width: 100%
    }
}

@media only screen and (max-width:1024px) {
    body, body > footer, body > header {
        min-width: 1024px
    }
}
