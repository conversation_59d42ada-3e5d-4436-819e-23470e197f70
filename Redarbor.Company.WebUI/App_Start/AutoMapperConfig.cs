using AutoMapper;
using Redarbor.Candidates.Consumer.Domain.Dto;
using Redarbor.Candidates.Consumer.Domain.Enums;
using Redarbor.Company.Contracts.ServiceLibrary.DTO;
using Redarbor.Company.WebUI.Enums;
using Redarbor.Company.WebUI.Models;
using Redarbor.Company.WebUI.Models.Company;
using Redarbor.Company.WebUI.Models.Company.Candidate;
using Redarbor.Company.WebUI.Models.Company.Candidate.Cv;
using Redarbor.Company.WebUI.Models.Company.Cart;
using Redarbor.Company.WebUI.Models.Company.Configuration;
using Redarbor.Company.WebUI.Models.Company.Configuration.EditCredits;
using Redarbor.Company.WebUI.Models.Company.Configuration.TrackingActions;
using Redarbor.Company.WebUI.Models.Company.Cv;
using Redarbor.Company.WebUI.Models.Company.Match;
using Redarbor.Company.WebUI.Models.Company.Match.Cv;
using Redarbor.Company.WebUI.Models.Company.Messages;
using Redarbor.Company.WebUI.Models.Company.Offer;
using Redarbor.Company.WebUI.Models.Company.Product;
using Redarbor.Company.WebUI.Models.Company.Shared;
using Redarbor.Company.WebUI.Models.Company.Vision;
using Redarbor.Company.WebUI.Models.Company.Vision.Interviews;
using Redarbor.Company.WebUI.Models.Company.Vision.Valuations;
using Redarbor.Company.WebUI.Models.Home;
using Redarbor.Company.WebUI.Models.SimplifiedRegister;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary.Enums;
using Redarbor.Core.Elastic.Library;
using Redarbor.Elastic.Entities.Candidate;
using Redarbor.Master.Entities.Candidate;
using Redarbor.Master.Entities.Company;
using Redarbor.Master.Entities.Counters;
using Redarbor.Master.Entities.CustomFolder;
using Redarbor.Master.Entities.Cv;
using Redarbor.Master.Entities.Enums;
using Redarbor.Master.Entities.Invoice;
using Redarbor.Master.Entities.Mailing.ZendeskTicket;
using Redarbor.Master.Entities.Match;
using Redarbor.Master.Entities.Message;
using Redarbor.Master.Entities.Offers;
using Redarbor.Master.Entities.Payment;
using Redarbor.Master.Entities.Product;
using Redarbor.Master.Entities.PurchaseOperation;
using Redarbor.Master.Entities.Reports;
using Redarbor.Master.Entities.Tracking;
using Redarbor.Master.Entities.Users;
using Redarbor.Master.Entities.Vision;
using Redarbor.Offer.Contracts.ServiceLibrary.DTO;
using Redarbor.Vision.Contracts.ServiceLibrary.DTO;
using System;
using System.Collections.Generic;
using System.Linq;
using conversations = Redarbor.Company.WebUI.Models.Company.Conversations;

namespace Redarbor.Company.WebUI
{
    public static class AutoMapperConfig
    {
        private const int CREATED_BY_DEFAULT = 1;
        public static void Initialize()
        {

            Mapper.Initialize(cfg =>
            {
                cfg.CreateMap<CandidateLocalizationEntity, CandidateByLocalizationDataModel>();
                cfg.CreateMap<CandidateCategoryEntity, CandidateByCategoryDataModel>();
                cfg.CreateMap<ExperienceByCvEntity, ExperienceByCvDataModel>();
                cfg.CreateMap<LanguageByCvEntity, LanguageByCvDataModel>();
                cfg.CreateMap<SkillByCvEntity, SkillByCvDataModel>();
                cfg.CreateMap<FormationByCvEntity, FormationByCvDataModel>();
                cfg.CreateMap<CommentCvEntity, CommentCvDataModel>();
                cfg.CreateMap<RatingCvEntity, RatingCvDataModel>();
                cfg.CreateMap<CvEntity, CvDataModel>();
                cfg.CreateMap<ProductEntity, ProductDataModel>();
                cfg.CreateMap<ChartDataEntity, ChartDataDataModel>();
                cfg.CreateMap<InvoiceUnifiedEntity, InvoiceUnifiedDataModel>();
                GetCompanyDataModelMapping(cfg);
                GetCompanyEntityMapping(cfg);
                cfg.CreateMap<CompanyProductEntity, CompanyProductDataModel>();
                cfg.CreateMap<ProductFeatureEntity, ProductFeatureDataModel>();
                cfg.CreateMap<ProductFeatureDescriptionEntity, ProductFeatureDescriptionDataModel>();
                cfg.CreateMap<CvsCounters, CvsCountersDataModel>();
                GetOfferDataModelMapping(cfg);
                GetPostPublishOfferDataModelMapping(cfg);
                GetOfferEntityMapping(cfg);
                GetPostPublishOfferEntityMapping(cfg);
                cfg.CreateMap<KillerQuestionEntity, KillerQuestionDataModel>();
                cfg.CreateMap<KillerQuestionDataEntity, KillerQuestionDataDataModel>();
                cfg.CreateMap<MailingCountersEntity, KpiDataModel>();
                GetUserDataModelMapping(cfg);
                cfg.CreateMap<UserDataModel, UserEntity>();
                cfg.CreateMap<CompanyEntity, Models.RegisterDataModel>();
                cfg.CreateMap<ConversationEntity, ConversationDataModel>();
                cfg.CreateMap<MessageEntity, MessageDataModel>();
                cfg.CreateMap<ConversationEntity, conversations.ConversationDataModel>();
                cfg.CreateMap<MessageEntity, conversations.MessageDataModel>();
                cfg.CreateMap<CandidateReadEntity, CandidateReadDataModel>();
                cfg.CreateMap<MatchEntity, MatchDataModel>();
                cfg.CreateMap<CompanyFileEntity, CompanyFilesDataModel>();
                cfg.CreateMap<TrackingActionEntity, TrackingActionsDataModel>();
                cfg.CreateMap<CustomFolderEntity, FoldersDataModel>();
                cfg.CreateMap<MatchDownloaderEntity, CvDownloaderDataModel>();
                cfg.CreateMap<ProductEntity, ContentProductPackDataModel>();
                cfg.CreateMap<ProductEntity, ContentProductPackDataModelV2>();
                cfg.CreateMap<RatedPriceEntity, CompanyCartDataModel>();
                cfg.CreateMap<ProProductEntity, ProProductDataModel>();
                cfg.CreateMap<DtInvoiceEntity, DtInvoiceDataModel>();
                cfg.CreateMap<InvoiceEntity, InvoiceDataModel>();
                cfg.CreateMap<PurchaseOperationEntity, PurchaseOperationDataModel>();
                GetZendeskTicketTraceMapping(cfg);
                GetZendeskTicketMapping(cfg);
                GetLandingZendeskTicketTraceMapping(cfg);
                GetLandingZendeskTicketMapping(cfg);
                GetOfferListCvDataModel(cfg);
                GetCustomFoldersByCvs(cfg);
                GetCandidateDataModel(cfg);
                GetCompanyCartInvoceMapping(cfg);
                GetRatedPriceMapping(cfg);
                GetRatedPriceMappingFinalizePurchase(cfg);
                GetPaymentPurchaseOperationMapping(cfg);
                GetCompanyCartCompanyMapping(cfg);
                cfg.CreateMap<CvDownloaderEntity, CvDownloaderDataModel>();
                cfg.CreateMap<OfferCandidateEntity, OfferCandidateDataModel>();
                cfg.CreateMap<CompanyValuation, VisionCompanyValuationDataModel>();
                cfg.CreateMap<RatingDetail, VisionRatingDetailDataModel>();
                cfg.CreateMap<CompanyDetailEntity, VisionCompanyDetailDataModel>();
                GetUserEditCreditsDataModelMapping(cfg);
                cfg.CreateMap<ControlFeatureUnitEntity, ControlFeatureUnitsDataModel>();
                GetUserCreditsEntity(cfg);
                cfg.CreateMap<CompanyValuationRating, VisionRatingsDataModel>();
                cfg.CreateMap<CompanyAboutEntity, VisionAboutCompanyDataModel>();
                cfg.CreateMap<CompanyBenefit, VisionCompanyBenefitDataModel>();
                cfg.CreateMap<PortalBenefit, VisionPortalBenefitDataModel>();
                cfg.CreateMap<PortalBenefitCategory, VisionPortalBenefitCategoryDataModel>();
                GetVisionInterviewCommentsDataModel(cfg);
                cfg.CreateMap<VisionSummaryDTO, VisionSummaryDataModel>();
                cfg.CreateMap<NewsEntity, VisionNewsDetailDataModel>();
                GetNewsEntity(cfg);
                cfg.CreateMap<OfferFilterDTO, CompanyFilterEntity>();
                cfg.CreateMap<CompanyFilterEntity, OfferFilterDTO>();
                cfg.CreateMap<ProductSubGroupsEntity, ProductSubGroupsDataModel>();
                GetCvsSearchFilterToSearchFilterDTO(cfg);
                cfg.CreateMap<CandidateReadSearchDto, CandidateReadSearch>();
                GetSkills(cfg);
                cfg.CreateMap<FacetResultDto, FacetResult>();
                GetCompanyEntityToRegisterMapping(cfg);
                cfg.CreateMap<ProductTraceEntity, ProductTraceDataModel>();
                GetPreRegisterCompanyDataModel(cfg);
                GetCompanyEntityToPreRegisterMapping(cfg);
                cfg.CreateMap<MatchesOfferMailSettingsEntity, MatchesOfferMailSettingsDTO>();
                cfg.CreateMap<MatchesOfferMailSettingsDTO, MatchesOfferMailSettingsEntity>();
            });
        }

        private static void GetSkills(IMapperConfigurationExpression cfg)
        {
            cfg.CreateMap<CandidateSkillsEntityDto, CandidateSkills>()
                .ForMember(x => x.IdSkill, opt => opt.MapFrom(i => i.IdSkill))
                .ForMember(x => x.IdSkillType, opt => opt.MapFrom(i => i.IdSkillType))
                .ForMember(x => x.IdCv, opt => opt.MapFrom(i => i.IdCv))
                .ForMember(x => x.Value, opt => opt.MapFrom(i => i.Value));
        }

        private static void GetCvsSearchFilterToSearchFilterDTO(IMapperConfigurationExpression cfg)
        {
            cfg.CreateMap<Elastic.Entities.Candidate.SearchFilter, SearchFilterDto>()
                .ForMember(dest => dest.IsWorking, opt => opt.MapFrom(o => o.IsWorking > 0 ? o.IsWorking > 1 ? WorkingStatusEnumDTO.NotWorking : WorkingStatusEnumDTO.Working : WorkingStatusEnumDTO.None))
                .ForMember(dest => dest.IsStudying, opt => opt.MapFrom(o => o.IsStudying > 0 ? o.IsStudying > 1 ? StudyingStatusEnumDTO.NotStudying : StudyingStatusEnumDTO.Studying : StudyingStatusEnumDTO.None))
                .ForMember(dest => dest.HasDisability, opt => opt.MapFrom(o => o.HasDisability > 0 ? o.HasDisability > 1 ? HasDisabilityStatusEnumDTO.NotHasDisability : HasDisabilityStatusEnumDTO.HasDisability : HasDisabilityStatusEnumDTO.None));
        }

        private static void GetCustomFoldersByCvs(IMapperConfigurationExpression cfg)
        {
            cfg.CreateMap<CustomFolderEntity, FoldersCvDataModel>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(o => o.Id))
                .ForMember(dest => dest.IdEncrypted, opt => opt.MapFrom(o => o.IdEncrypted))
                .ForMember(dest => dest.NameFolder, opt => opt.MapFrom(o => o.Name));
        }

        private static void GetCandidateDataModel(IMapperConfigurationExpression cfg)
        {
            cfg.CreateMap<CandidateEntity, CandidateDataModel>()
                .ForMember(dest => dest.ClientIdAdd, opt => opt.MapFrom(o => o.client_ip_add))
                .ForMember(dest => dest.ClientIdMod, opt => opt.MapFrom(o => o.client_ip_mod));
        }

        private static void GetOfferListCvDataModel(IMapperConfigurationExpression cfg)
        {
            cfg.CreateMap<OfferEntity, OffersListDataModel>()
                .ForMember(dest => dest.IdOfferEncrypted, opt => opt.MapFrom(o => o.idofferencrypted))
                .ForMember(dest => dest.IsFreemium, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).idproduct == (int)ProductEnum.FreemiumService))
                .ForMember(dest => dest.IsComplete, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).idproduct != (int)ProductEnum.FreemiumService))
                .ForMember(dest => dest.IdProductEncrypted, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).idproductencrypted))
                .ForMember(dest => dest.Title, opt => opt.MapFrom(o => o.title))
                .ForMember(dest => dest.IdCity, opt => opt.MapFrom(o => o.idcity))
                .ForMember(dest => dest.IdCountry, opt => opt.MapFrom(o => o.idcountry))
                .ForMember(dest => dest.IdLocalization, opt => opt.MapFrom(o => o.idlocalization))
                .ForMember(dest => dest.IdOffer, opt => opt.MapFrom(o => o.idoffer))
                .ForMember(dest => dest.IdOfferCT, opt => opt.MapFrom(o => o.idofferCT))
                .ForMember(dest => dest.IdProduct, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).idproduct))
                .ForMember(dest => dest.IdStatus, opt => opt.MapFrom(o => o.idstatus))
                .ForMember(dest => dest.DateLastUp, opt => opt.MapFrom(o => o.datelastup))
                .ForMember(dest => dest.ExpirationTime, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).expirationtime))
                .ForMember(dest => dest.IdCompanyProduct, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).idcompanyproduct));
        }

        private static void GetCompanyCartInvoceMapping(IProfileExpression cfg)
        {
            cfg.CreateMap<EditDataCompanyCartDataModel, InvoiceEntity>()
                   .ForMember(dest => dest.CompanyName, opt => opt.MapFrom(o => !string.IsNullOrEmpty(o.CompanyName) ? o.CompanyName : string.Empty))
                   .ForMember(dest => dest.CompleteAddress, opt => opt.MapFrom(o => !string.IsNullOrEmpty(o.Address) ? o.Address : string.Empty))
                   .ForMember(dest => dest.IdCountry, opt => opt.MapFrom(o => o.CountryIdSelected > 0 ? o.CountryIdSelected : 0))
                   .ForMember(dest => dest.TieneActividadEmpresarial, opt => opt.Ignore())
                   .ForMember(dest => dest.Nit, opt => opt.MapFrom(o => !string.IsNullOrEmpty(o.Nit) ? o.Nit : string.Empty))
                   .ForMember(dest => dest.ContactEmail, opt => opt.MapFrom(o => !string.IsNullOrEmpty(o.ContactEmail) ? o.ContactEmail : string.Empty))
                   .ForMember(dest => dest.ContactFullName, opt => opt.MapFrom(o => !string.IsNullOrEmpty(o.ContactName) ? o.ContactName : string.Empty))
                   .ForMember(dest => dest.ContactTelePhone1, opt => opt.MapFrom(o => !string.IsNullOrEmpty(o.ContactPhone) ? o.ContactPhone : string.Empty));
        }

        private static void GetCompanyCartCompanyMapping(IProfileExpression cfg)
        {
            cfg.CreateMap<CompanyEntity, EditDataCompanyCartDataModel>()
                   .ForMember(dest => dest.CompanyName, opt => opt.MapFrom(o => !string.IsNullOrEmpty(o.CompanyName) ? o.CompanyName : string.Empty))
                   .ForMember(dest => dest.Nit, opt => opt.MapFrom(o => !string.IsNullOrEmpty(o.Nit) ? o.Nit : string.Empty))
                   .ForMember(dest => dest.CountryIdSelected, opt => opt.MapFrom(o => o.Address.CountryId > 0 ? o.Address.CountryId : 0))
                   .ForMember(dest => dest.CityIdSelected, opt => opt.MapFrom(o => o.Address.CityId > 0 ? o.Address.CityId : 0))
                   .ForMember(dest => dest.LocalizationIdSelected, opt => opt.MapFrom(o => o.Address.LocationId > 0 ? o.Address.LocationId : 0))
                   .ForMember(dest => dest.Address, opt => opt.MapFrom(o => !string.IsNullOrEmpty(o.Address.StreetAddress.ToString()) ? o.Address.StreetAddress.ToString() : string.Empty))
                   .ForMember(dest => dest.ContactEmail, opt => opt.MapFrom(o => !string.IsNullOrEmpty(o.ContactInformation.Email) ? o.ContactInformation.Email : string.Empty))
                   .ForMember(dest => dest.ContactName, opt => opt.MapFrom(o => !string.IsNullOrEmpty(o.ContactInformation.Name) ? o.ContactInformation.Name : string.Empty))
                   .ForMember(dest => dest.ContactPhone, opt => opt.MapFrom(o => GetContactNameByContactInformation(o, o.ContactInformation.PhoneNumbers[1].Number, string.Empty)))
                   .ForMember(dest => dest.CountryId, opt => opt.MapFrom(o => o.Address.CountryId > 0 ? o.Address.CountryId : 0));

        }

        private static string GetContactNameByContactInformation(CompanyEntity o, string telephone, string controlTelephone)
        {
            string companyPhone = string.Empty;

            if (!string.IsNullOrEmpty(telephone))
                companyPhone = telephone;
            else
            {
                var contactInformation = o.ContactInformation.PhoneNumbers.FirstOrDefault(q => q.Number != telephone && controlTelephone == telephone);
                if (contactInformation != null)
                {
                    companyPhone = contactInformation.Number;
                }
            }
            return companyPhone;
        }

        private static void GetOfferDataModelMapping(IProfileExpression cfg)
        {
            cfg.CreateMap<OfferEntity, OfferDataModel>()
                    .ForMember(dest => dest.RenewOffer, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).RenewOfferStatus != (short)OfferRenewStatusEnum.Manual))
                    .ForMember(dest => dest.idcompanyproduct, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).idcompanyproduct))
                    .ForMember(dest => dest.idcompanyproductencrypted, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).idcompanyproductencrypted))
                    .ForMember(dest => dest.idproduct, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).idproduct))
                    .ForMember(dest => dest.idproductencrypted, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).idproductencrypted))
                    .ForMember(dest => dest.ExternalApplicationEmail, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).ExternalApplicationEmail))
                    .ForMember(dest => dest.ExternalReference, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).ExternalReference))
                    .ForMember(dest => dest.show_contact_email, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).show_contact_email))
                    .ForMember(dest => dest.contact_email, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).contact_email))
                    .ForMember(dest => dest.show_phone_offer, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).show_phone_offer))
                    .ForMember(dest => dest.contact_phone, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).contact_phone))
                    .ForMember(dest => dest.show_contact_address, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).show_contact_address))
                    .ForMember(dest => dest.contact_address, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).contact_address))
                    .ForMember(dest => dest.idofferstatus, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).idstatus))
                    .ForMember(dest => dest.Isflash, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).Isflash))
                    .ForMember(dest => dest.ishighlighted, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).ishighlighted))
                    .ForMember(dest => dest.isurgent, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).isurgent))
                    .ForMember(dest => dest.urlrewrite, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).urlrewrite))
                    .ForMember(dest => dest.publicationdays, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).publicationdays))
                    .ForMember(dest => dest.publicationtime, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).publicationtime))
                    .ForMember(dest => dest.expirationtime, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).expirationtime))
                    .ForMember(dest => dest.total_applied, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).total_applied))
                    .ForMember(dest => dest.TotalAppliedNotViewed, opt => opt.MapFrom(o => o.TotalNewNotViewed))
                    .ForMember(dest => dest.TotalNew, opt => opt.MapFrom(o => o.TotalNew))
                    .ForMember(dest => dest.TotalModeratedTimes, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).TotalModeratedTimes))
                    .ForMember(dest => dest.moderationtime, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).moderationtime))
                    .ForMember(dest => dest.moderatedby, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).moderatedby))
                    .ForMember(dest => dest.client_ip_add, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).client_ip_add))
                    .ForMember(dest => dest.client_ip_mod, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).client_ip_mod))
                    .ForMember(dest => dest.disabilityyes, opt => opt.MapFrom(o => o.disability == 2 || o.disability == 1))
                    .ForMember(dest => dest.disabilityno, opt => opt.MapFrom(o => o.disability == 2 || o.disability == 0))
                    .ForMember(dest => dest.starttime, opt => opt.MapFrom(o => o.starttime == DateTime.MinValue ? (DateTime?)null : o.starttime))
                    .ForMember(dest => dest.Languages, opt => opt.Ignore())
                    .ForMember(dest => dest.DriveLicenses, opt => opt.Ignore())
                    .ForMember(dest => dest.Skills, opt => opt.Ignore())
                    .ForMember(dest => dest.OfficeSkillsSelected, opt => opt.MapFrom(o => o.skills.Select(d => d.idskill).ToList()))
                    .ForMember(dest => dest.SelectedDriveLicenses, opt => opt.MapFrom(o => o.drivelicenses.Select(d => d.iddrivelicense).ToList()))
                    .ForMember(dest => dest.SelectedOfficeSkills, opt => opt.MapFrom(o => o.skills.Select(d => d.idskill).ToArray()))
                    .ForMember(dest => dest.LanguageLevelsSelected, opt => opt.MapFrom(o => o.Languages.Select(d => new OfferLanguageDataModel() { LanguageId = d.ididiom, LanguageLevelId = d.ididiomlevel }).ToList()))
                    .ForMember(dest => dest.LastProfession, opt => opt.MapFrom(o => o.cargo))
                    .ForMember(dest => dest.OtherLocalizations, opt => opt.MapFrom(o => GetOtherLocalizationsModel(o.idotherlocalizations)))
                    .ForMember(dest => dest.IdPostalCode, opt => opt.MapFrom(o => o.IdPostalCode));
        }

        private static void GetPostPublishOfferDataModelMapping(IProfileExpression cfg)
        {
            cfg.CreateMap<OfferEntity, OfferPostPublishDataModel>()
                    .ForMember(dest => dest.RenewOffer, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).RenewOfferStatus != (short)OfferRenewStatusEnum.Manual))
                    .ForMember(dest => dest.idcompanyproduct, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).idcompanyproduct))
                    .ForMember(dest => dest.idcompanyproductencrypted, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).idcompanyproductencrypted))
                    .ForMember(dest => dest.idproduct, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).idproduct))
                    .ForMember(dest => dest.idproductencrypted, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).idproductencrypted))
                    .ForMember(dest => dest.ExternalApplicationEmail, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).ExternalApplicationEmail))
                    .ForMember(dest => dest.ExternalReference, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).ExternalReference))
                    .ForMember(dest => dest.show_contact_email, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).show_contact_email))
                    .ForMember(dest => dest.contact_email, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).contact_email))
                    .ForMember(dest => dest.show_phone_offer, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).show_phone_offer))
                    .ForMember(dest => dest.contact_phone, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).contact_phone))
                    .ForMember(dest => dest.show_contact_address, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).show_contact_address))
                    .ForMember(dest => dest.contact_address, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).contact_address))
                    .ForMember(dest => dest.idofferstatus, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).idstatus))
                    .ForMember(dest => dest.Isflash, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).Isflash))
                    .ForMember(dest => dest.ishighlighted, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).ishighlighted))
                    .ForMember(dest => dest.isurgent, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).isurgent))
                    .ForMember(dest => dest.urlrewrite, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).urlrewrite))
                    .ForMember(dest => dest.publicationdays, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).publicationdays))
                    .ForMember(dest => dest.publicationtime, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).publicationtime))
                    .ForMember(dest => dest.expirationtime, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).expirationtime))
                    .ForMember(dest => dest.total_applied, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).total_applied))
                    .ForMember(dest => dest.TotalAppliedNotViewed, opt => opt.MapFrom(o => o.TotalNewNotViewed))
                    .ForMember(dest => dest.TotalModeratedTimes, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).TotalModeratedTimes))
                    .ForMember(dest => dest.moderationtime, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).moderationtime))
                    .ForMember(dest => dest.moderatedby, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).moderatedby))
                    .ForMember(dest => dest.client_ip_add, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).client_ip_add))
                    .ForMember(dest => dest.client_ip_mod, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).client_ip_mod))
                    .ForMember(dest => dest.disabilityyes, opt => opt.MapFrom(o => o.disability == 2 || o.disability == 1))
                    .ForMember(dest => dest.disabilityno, opt => opt.MapFrom(o => o.disability == 2 || o.disability == 0))
                    .ForMember(dest => dest.starttime, opt => opt.MapFrom(o => o.starttime == DateTime.MinValue ? (DateTime?)null : o.starttime))
                    .ForMember(dest => dest.Languages, opt => opt.Ignore())
                    .ForMember(dest => dest.DriveLicenses, opt => opt.Ignore())
                    .ForMember(dest => dest.SelectedDriveLicenses, opt => opt.MapFrom(o => o.drivelicenses.Select(d => d.iddrivelicense).ToList()))
                    .ForMember(dest => dest.SelectedOfficeSkills, opt => opt.MapFrom(o => o.skills.Select(d => d.idskill).ToArray()))
                    .ForMember(dest => dest.Skills, opt => opt.Ignore())
                    .ForMember(dest => dest.LanguageLevelsSelected, opt => opt.MapFrom(o => o.Languages.Select(d => new OfferLanguageDataModel() { LanguageId = d.ididiom, LanguageLevelId = d.ididiomlevel }).ToList()))
                    .ForMember(dest => dest.OfficeSkillsSelected, opt => opt.MapFrom(o => o.skills.Select(d => d.idskill).ToList()))
                    .ForMember(dest => dest.LastProfession, opt => opt.MapFrom(o => o.cargo))
                    .ForMember(dest => dest.ispayment, opt => opt.MapFrom(o => o.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).IsPayment))
                    .ForMember(dest => dest.OtherLocalizations, opt => opt.MapFrom(o => GetOtherLocalizationsModel(o.idotherlocalizations)))
                    .ForMember(dest => dest.IdPostalCode, opt => opt.MapFrom(o => o.IdPostalCode));
        }

        private static void GetOfferEntityMapping(IProfileExpression cfg)
        {
            cfg.CreateMap<OfferDataModel, OfferEntity>()
                .ForMember(dest => dest.complement,
                    opt => opt.MapFrom(o => o.complement != null ? o.complement.Trim() : string.Empty))
                .ForMember(dest => dest.cargo, opt => opt.MapFrom(o => o.cargo != null ? o.cargo.Trim() : string.Empty))
                .ForMember(dest => dest.title, opt => opt.MapFrom(o => SetTitle(o.cargo, o.complement)))
                .ForMember(dest => dest.disability,
                    opt => opt.MapFrom(o => o.disabilityyes && o.disabilityno ? 2 : o.disabilityyes ? 1 : 0))
                .ForMember(dest => dest.starttime,
                    opt => opt.MapFrom(o => o.starttime == DateTime.MinValue ? (DateTime?)null : o.starttime))
                .ForMember(dest => dest.Languages,
                    opt => opt.MapFrom(o => o.LanguageLevelsSelected.Select(l =>
                        new LanguageEntity() { ididiom = l.LanguageId, ididiomlevel = l.LanguageLevelId })))
                .ForMember(dest => dest.drivelicenses,
                    opt => opt.MapFrom(o =>
                        o.SelectedDriveLicenses.Select(d => new DriveLicenseEntity() { iddrivelicense = d })))
                .ForMember(dest => dest.skills,
                    opt => opt.MapFrom(o => o.OfficeSkillsSelected.Select(d =>
                        new Master.Entities.Offers.SkillEntity() { idskill = d })))
                .ForMember(dest => dest.TotalNewNotViewed,
                    opt => opt.MapFrom(o => o.TotalAppliedNotViewed))
                .ForMember(dest => dest.TotalNew,
                    opt => opt.MapFrom(o => o.TotalNew))
                .ForMember(dest => dest.Integrations,
                    opt => opt.MapFrom(o => new List<OfferIntegratorEntity>()
                    {
                        new OfferIntegratorEntity()
                        {
                            idproductencrypted = o.idproductencrypted,
                            idcompanyproductencrypted = o.idcompanyproductencrypted,
                            IdIntegrator = (short)OfferIntegratorEnum.CompuTrabajo,
                            idstatus = o.idstatus,
                            IdOffer = o.idoffer,
                            ExternalApplicationEmail = o.ExternalApplicationEmail,
                            ExternalReference = o.ExternalReference,
                            IsPayment = o.ispayment,
                            show_contact_email = o.show_contact_email,
                            contact_email = o.contact_email,
                            show_phone_offer = o.show_phone_offer,
                            contact_phone = o.contact_phone,
                            show_contact_address = o.show_contact_address,
                            contact_address = o.contact_address,
                            Isflash = o.Isflash,
                            ishighlighted = o.ishighlighted,
                            isurgent = o.isurgent,
                            urlrewrite = o.urlrewrite,
                            publicationtime = o.publicationtime,
                            publicationdays = o.publicationdays,
                            expirationtime = o.expirationtime,
                            total_applied = o.total_applied,
                            TotalModeratedTimes = o.TotalModeratedTimes,
                            moderationtime = o.moderationtime,
                            moderatedby = o.moderatedby,
                            createdby = o.createdby,
                            createdon = o.createdon,
                            updatedon = o.updatedon,
                            deletedon = o.deletedon,
                            client_ip_add = o.client_ip_add,
                            client_ip_mod = o.client_ip_mod,
                            RenewOfferStatus = o.RenewOffer ? (short)OfferRenewStatusEnum.Automatic : (short)OfferRenewStatusEnum.Manual
                        }
                    }))
                .ForMember(dest => dest.idotherlocalizations, opt => opt.MapFrom(o => GetOtherLocalizationsString(o.OtherLocalizations)));
        }

        private static void GetPostPublishOfferEntityMapping(IProfileExpression cfg)
        {
            cfg.CreateMap<OfferPostPublishDataModel, OfferEntity>()
                .ForMember(dest => dest.complement,
                    opt => opt.MapFrom(o => o.complement != null ? o.complement.Trim() : string.Empty))
                .ForMember(dest => dest.cargo, opt => opt.MapFrom(o => o.cargo != null ? o.cargo.Trim() : string.Empty))
                .ForMember(dest => dest.title, opt => opt.MapFrom(o => SetTitle(o.cargo, o.complement)))
                .ForMember(dest => dest.disability,
                    opt => opt.MapFrom(o => o.disabilityyes && o.disabilityno ? 2 : o.disabilityyes ? 1 : 0))
                .ForMember(dest => dest.starttime,
                    opt => opt.MapFrom(o => o.starttime == DateTime.MinValue ? (DateTime?)null : o.starttime))
                .ForMember(dest => dest.Languages,
                    opt => opt.MapFrom(o => o.LanguageLevelsSelected.Select(l =>
                        new LanguageEntity() { ididiom = l.LanguageId, ididiomlevel = l.LanguageLevelId })))
                .ForMember(dest => dest.drivelicenses,
                    opt => opt.MapFrom(o =>
                        o.SelectedDriveLicenses.Select(d => new DriveLicenseEntity() { iddrivelicense = d })))
                .ForMember(dest => dest.skills,
                    opt => opt.MapFrom(o => o.OfficeSkillsSelected.Select(d =>
                        new Master.Entities.Offers.SkillEntity() { idskill = d })))
                .ForMember(dest => dest.TotalNewNotViewed,
                    opt => opt.MapFrom(o => o.TotalAppliedNotViewed))
                .ForMember(dest => dest.HiddenCompany, opt => opt.MapFrom(o => o.HiddenCompany))
                .ForMember(dest => dest.HiddenCompanyName, opt => opt.MapFrom(o => o.HiddenCompanyName))
                .ForMember(dest => dest.Integrations,
                    opt => opt.MapFrom(o => new List<OfferIntegratorEntity>()
                    {
                        new OfferIntegratorEntity()
                        {
                            idproductencrypted = o.idproductencrypted,
                            idcompanyproductencrypted = o.idcompanyproductencrypted,
                            IdIntegrator = (short)OfferIntegratorEnum.CompuTrabajo,
                            idstatus = o.idstatus,
                            IdOffer = o.idoffer,
                            ExternalApplicationEmail = o.ExternalApplicationEmail,
                            ExternalReference = o.ExternalReference,
                            IsPayment = o.ispayment,
                            show_contact_email = o.show_contact_email,
                            contact_email = o.contact_email,
                            show_phone_offer = o.show_phone_offer,
                            contact_phone = o.contact_phone,
                            show_contact_address = o.show_contact_address,
                            contact_address = o.contact_address,
                            Isflash = o.Isflash,
                            ishighlighted = o.ishighlighted,
                            isurgent = o.isurgent,
                            urlrewrite = o.urlrewrite,
                            publicationtime = o.publicationtime,
                            publicationdays = o.publicationdays,
                            expirationtime = o.expirationtime,
                            total_applied = o.total_applied,
                            TotalModeratedTimes = o.TotalModeratedTimes,
                            moderationtime = o.moderationtime,
                            moderatedby = o.moderatedby,
                            createdby = o.createdby,
                            createdon = o.createdon,
                            updatedon = o.updatedon,
                            deletedon = o.deletedon,
                            client_ip_add = o.client_ip_add,
                            client_ip_mod = o.client_ip_mod,
                            RenewOfferStatus = o.RenewOffer ? (short)OfferRenewStatusEnum.Automatic : (short)OfferRenewStatusEnum.Manual
                        }
                    }))
                .ForMember(dest => dest.idotherlocalizations, opt => opt.MapFrom(o => GetOtherLocalizationsString(o.OtherLocalizations)));
        }

        /// <summary>
        /// la logica de este metodo es igual al js que muestra al cliente. //archivo offerpublixh.js (function SetTitle())
        /// </summary>
        /// <param name="cargo">required value</param>
        /// <param name="complement">optional value</param>
        /// <returns>title = cargo - complement</returns>
        private static string SetTitle(string cargo, string complement)
        {
            var cargoAux = cargo != null ? cargo.Trim() : string.Empty;
            var complementAux = complement != null ? complement.Trim() : string.Empty;

            if (cargoAux != string.Empty && complementAux != string.Empty)
            {
                return cargoAux + " - " + complementAux;
            }
            else
            {
                return cargoAux + complementAux;
            }
        }

        private static void GetCompanyDataModelMapping(IProfileExpression cfg)
        {

            cfg.CreateMap<CompanyEntity, CompanyDataModel>()
                .ForMember(dest => dest.Email, opt => opt.MapFrom(o => o.User.Username))
                .ForMember(dest => dest.Password, opt => opt.MapFrom(o => o.User.Password))
                .ForMember(dest => dest.CountryIdSelected, opt => opt.MapFrom(o => o.Address.CountryId))
                .ForMember(dest => dest.CountryId, opt => opt.MapFrom(o => o.Address.CountryId))
                .ForMember(dest => dest.Address, opt => opt.MapFrom(o => o.Address.StreetAddress))
                .ForMember(dest => dest.CityIdSelected, opt => opt.MapFrom(o => o.Address.CityId))
                .ForMember(dest => dest.CityId, opt => opt.MapFrom(o => o.Address.CityId))
                .ForMember(dest => dest.LocalizationIdSelected, opt => opt.MapFrom(o => o.Address.LocationId))
                .ForMember(dest => dest.LocalizationId, opt => opt.MapFrom(o => o.Address.LocationId))
                .ForMember(dest => dest.ZipCode, opt => opt.MapFrom(o => o.Address.ZipCode))
                .ForMember(dest => dest.CompanyTypeIdSelected, opt => opt.MapFrom(o => o.TypeId))
                .ForMember(dest => dest.IndustryIdSelected, opt => opt.MapFrom(o => o.IndustryId))
                .ForMember(dest => dest.NumberEmployeesIdSelected, opt => opt.MapFrom(o => o.EmploymentNumber))
                .ForMember(dest => dest.NumberEmployeesId, opt => opt.MapFrom(o => o.EmploymentNumber))
                .ForMember(dest => dest.CompanyUrl, opt => opt.MapFrom(o => o.Url))
                .ForMember(dest => dest.CompanySocial, opt => opt.MapFrom(o => o.CompanyName))
                .ForMember(dest => dest.ContactName, opt => opt.MapFrom(o => !string.IsNullOrEmpty(o.ContactInformation.Name) ? o.ContactInformation.Name : string.Empty))
                .ForMember(dest => dest.ContactSurName, opt => opt.MapFrom(o => o.ContactInformation.Surname))
                .ForMember(dest => dest.ContactPositionIdSelected, opt => opt.MapFrom(o => o.ContactInformation.PositionId))
                .ForMember(dest => dest.CompanyPhone, opt => opt.MapFrom(o => GetContactNameByContactInformation(o, o.ContactInformation.PhoneNumbers[0].Number, "")))
                .ForMember(dest => dest.CompanyPhoneIdSelected, opt => opt.MapFrom(o => o.ContactInformation.PhoneNumbers[0].TypeId))
                .ForMember(dest => dest.ContactPhone, opt => opt.MapFrom(o => GetContactNameByContactInformation(o, o.ContactInformation.PhoneNumbers[1].Number, o.ContactInformation.PhoneNumbers[0].Number)))
                .ForMember(dest => dest.ContactPhoneIdSelected, opt => opt.MapFrom(o => o.ContactInformation.PhoneNumbers[1].TypeId))
                .ForMember(dest => dest.UserId, opt => opt.MapFrom(o => o.User.Id))
                .ForMember(dest => dest.LogoPath, opt => opt.Ignore())
                .ForMember(dest => dest.OtherLocalizations, opt => opt.MapFrom(o => GetOtherLocalizationsModel(o.OtherLocationsId)))
                .ForMember(dest => dest.RegisterAction, opt => opt.MapFrom(o => o.RegisterAction));

        }

        private static DependingDropdownGroupModel GetOtherLocalizationsModel(string otherLocalizationsIds)
        {
            var otherLocationsList = new List<DependingDropdownModel>();

            if (!string.IsNullOrEmpty(otherLocalizationsIds))
            {
                var listlevels = otherLocalizationsIds.Split('|');
                foreach (var objlevel in listlevels)
                {
                    var valueslevel = objlevel.Split(';');
                    int levelNumber = 0, selectedLocation = 0;
                    if (valueslevel.Length == 2 && int.TryParse(valueslevel[0], out levelNumber) && int.TryParse(valueslevel[1], out selectedLocation))
                    {
                        otherLocationsList.Add(new DependingDropdownModel()
                        {
                            Level = levelNumber,
                            SelectedKey = selectedLocation
                        });
                    }
                }
            }
            return new DependingDropdownGroupModel() { DropdownList = otherLocationsList };
        }

        private static string GetOtherLocalizationsString(DependingDropdownGroupModel otherLocalizations)
        {
            return string.Join("|", otherLocalizations.DropdownList.Where(d => d.SelectedKey.GetValueOrDefault() > 0).Select(d => $"{d.Level};{d.SelectedKey.GetValueOrDefault()}"));
        }

        private static void GetCompanyEntityMapping(IProfileExpression cfg)
        {
            cfg.CreateMap<CompanyDataModel, CompanyEntity>()
                    .ForMember(dest => dest.User, opt => opt.MapFrom(o => new UserEntity()
                    {
                        Username = o.Email,
                        Password = o.Password,
                        OriginId = (int)OfferOriginEnum.WEB,
                        TypeId = (int)UserTypeEnum.Company,
                        ContactName = o.ContactName,
                        Email = o.Email,
                        PhoneNumbers = o.CompanyPhone,
                        Id = o.UserId
                    }))
                    .ForMember(dest => dest.CreatedBy, opt => opt.MapFrom(o => CREATED_BY_DEFAULT))
                    .ForMember(dest => dest.CompanyName, opt => opt.MapFrom(o => o.CompanySocial))
                    .ForMember(dest => dest.Address, opt => opt.MapFrom(o => new AddressEntity()
                    {
                        StreetAddress = o.Address,
                        CityId = o.CityIdSelected,
                        CountryId = o.CountryIdSelected,
                        LocationId = o.LocalizationIdSelected,
                        ZipCode = o.ZipCode ?? string.Empty
                    }))
                    .ForMember(dest => dest.TypeId, opt => opt.MapFrom(o => o.CompanyTypeIdSelected))
                    .ForMember(dest => dest.EmploymentNumber, opt => opt.MapFrom(o => o.NumberEmployeesIdSelected))
                    .ForMember(dest => dest.Url, opt => opt.MapFrom(o => o.CompanyUrl ?? string.Empty))
                    .ForMember(dest => dest.IndustryId, opt => opt.MapFrom(o => o.IndustryIdSelected))
                    .ForMember(dest => dest.ContactInformation, opt => opt.MapFrom(o => new ContactInformationEntity()
                    {
                        Email = o.Email,
                        Username = o.Email,
                        Name = o.ContactName,
                        Surname = o.ContactSurName,
                        PositionId = o.ContactPositionIdSelected,
                        PhoneNumbers = new List<PhoneNumberEntity>()
                                       {
                                           new PhoneNumberEntity()
                                           {
                                              Number = o.CompanyPhone ?? string.Empty,
                                              TypeId = o.CompanyPhoneIdSelected
                                           },
                                           new PhoneNumberEntity()
                                           {
                                              Number = o.ContactPhone ?? string.Empty,
                                              TypeId = o.ContactPhoneIdSelected
                                           }
                                       }
                    }))
                    .ForMember(dest => dest.OtherLocationsId, opt => opt.MapFrom(o => GetOtherLocalizationsString(o.OtherLocalizations)));
        }

        private static void GetZendeskTicketTraceMapping(IProfileExpression cfg)
        {
            cfg.CreateMap<ContactDataModel, ZendeskTicketTraceEntity>()
                .ForMember(dest => dest.EmailFrom, opt => opt.MapFrom(c => c.Email))
                .ForMember(dest => dest.Subject, opt => opt.MapFrom(c => GetSubject(c)));

        }

        private static void GetZendeskTicketMapping(IProfileExpression cfg)
        {
            cfg.CreateMap<ContactDataModel, ZendeskTicketEntity>()
                   .ForMember(dest => dest.ticket, opt => opt.MapFrom(c => new TicketEntity()
                   {
                       comment = new CommentEntity()
                       {
                           body = c.Message
                       },
                       subject = GetSubject(c),
                       requester = new RequesterEntity()
                       {
                           email = c.Email,
                           name = c.Email
                       }
                   }));

            cfg.CreateMap<ContactDataModel, ZendeskTicketNewAccountEntity>()
                   .ForMember(dest => dest.ticket, opt => opt.MapFrom(c => new TicketNewAccountEntity()
                   {
                       comment = new CommentEntity()
                       {
                           body = c.Message
                       },
                       subject = GetSubject(c),
                       requester = new RequesterEntity()
                       {
                           email = c.Email,
                           name = c.Email
                       }
                   }));
        }

        private static string GetSubject(ContactDataModel c)
        {
            string subjectText = c.Subjects.Exists(x => x.Value == c.SubjectSelected.ToString())
                ? c.Subjects.FirstOrDefault(x => x.Value == c.SubjectSelected).Text
                : string.Empty;

            string subSubjectText = c.SubSubjects.Exists(x => x.Value == c.SubSubjectSelected.ToString())
                ? c.SubSubjects.FirstOrDefault(x => x.Value == c.SubSubjectSelected).Text
                : string.Empty;

            return $"{subjectText}. {subSubjectText}";
        }


        private static void GetLandingZendeskTicketTraceMapping(IProfileExpression cfg)
        {
            cfg.CreateMap<EmailDataModel, ZendeskTicketTraceEntity>()
                   .ForMember(dest => dest.EmailFrom, opt => opt.MapFrom(c => c.Email))
                   .ForMember(dest => dest.Subject, opt => opt.MapFrom(c => c.Subjects.Exists(x => x.Value == c.SubjectSelected.ToString())
                                                                            ? c.Subjects.FirstOrDefault(x => x.Value == c.SubjectSelected).Text
                                                                            : string.Empty));
        }

        private static void GetLandingZendeskTicketMapping(IProfileExpression cfg)
        {
            cfg.CreateMap<EmailDataModel, ZendeskTicketEntity>()
                   .ForMember(dest => dest.ticket, opt => opt.MapFrom(c => new TicketEntity()
                   {
                       comment = new CommentEntity()
                       {
                           body = c.Message
                       },
                       subject = c.Subjects.Exists(x => x.Value == c.SubjectSelected.ToString())
                                    ? c.Subjects.FirstOrDefault(x => x.Value == c.SubjectSelected).Text
                                    : string.Empty,
                       requester = new RequesterEntity()
                       {
                           email = c.Email,
                           name = c.Email
                       }
                   }));
        }


        private static void GetUserDataModelMapping(IProfileExpression cfg)
        {
            cfg.CreateMap<UserEntity, UserDataModel>()
               .ForMember(dest => dest.PrefixEmail, opt => opt.MapFrom(u => u.Email.Contains('@') ? u.Email.Split('@')[0] : string.Empty))
               .ForMember(dest => dest.DomainEmail, opt => opt.MapFrom(u => u.Email.Contains('@') ? $"@{u.Email.Split('@')[1]}" : string.Empty));
        }

        private static void GetRatedPriceMapping(IProfileExpression cfg)
        {
            cfg.CreateMap<CompanyCartDataModel, RatedPriceEntity>()
                  .ForMember(dest => dest.Price, opt => opt.MapFrom(o => o.Price > 0 ? o.Price : 0))
                   .ForMember(dest => dest.PriceWithVAT, opt => opt.MapFrom(o => o.PriceWithVAT > 0 ? o.PriceWithVAT : 0))
                   .ForMember(dest => dest.RenewPrice, opt => opt.MapFrom(o => o.RenewPrice > 0 ? o.RenewPrice : 0))
                   .ForMember(dest => dest.VATNumber, opt => opt.MapFrom(o => o.VATNumber > 0 ? o.VATNumber : 0))
                   .ForMember(dest => dest.RenewalPriceWithVAT, opt => opt.MapFrom(o => o.RenewalPriceWithVAT > 0 ? o.RenewalPriceWithVAT : 0));
        }

        private static void GetRatedPriceMappingFinalizePurchase(IProfileExpression cfg)
        {
            cfg.CreateMap<CompanyCartFinalizePurchaseDataModel, RatedPriceEntity>()
                  .ForMember(dest => dest.Price, opt => opt.MapFrom(o => o.Price > 0 ? o.Price : 0))
                   .ForMember(dest => dest.PriceWithVAT, opt => opt.MapFrom(o => o.PriceWithVAT > 0 ? o.PriceWithVAT : 0))
                   .ForMember(dest => dest.RenewPrice, opt => opt.MapFrom(o => o.RenewPrice > 0 ? o.RenewPrice : 0))
                   .ForMember(dest => dest.VATNumber, opt => opt.MapFrom(o => o.VATNumber > 0 ? o.VATNumber : 0))
                   .ForMember(dest => dest.RenewalPriceWithVAT, opt => opt.MapFrom(o => o.RenewalPriceWithVAT > 0 ? o.RenewalPriceWithVAT : 0));
        }

        private static void GetPaymentPurchaseOperationMapping(IProfileExpression cfg)
        {
            cfg.CreateMap<PaymentEntity, PurchaseOperationEntity>()
                  .ForMember(dest => dest.Idportal, opt => opt.MapFrom(o => o.PortalId > 0 ? o.PortalId : 0))
                  .ForMember(dest => dest.Idinvoiceentity, opt => opt.MapFrom(o => o.InvoiceEntity.Id > 0 ? o.InvoiceEntity.Id : 0))
                  .ForMember(dest => dest.Idcompany, opt => opt.MapFrom(o => o.CompanyId > 0 ? o.CompanyId : 0))
                  .ForMember(dest => dest.Iduser, opt => opt.MapFrom(o => o.UserId > 0 ? o.UserId : 0))
                  .ForMember(dest => dest.CreatedBy, opt => opt.MapFrom(o => o.UserId > 0 ? o.UserId : 0))
                  .ForMember(dest => dest.IdPurchaseOperationstatus, opt => opt.MapFrom(o => (int)PurchaseOperationEnum.InProcess))
                  .ForMember(dest => dest.Idproduct, opt => opt.MapFrom(o => o.ProductSelected > 0 ? o.ProductSelected : 0))
                  .ForMember(dest => dest.Price, opt => opt.MapFrom(o => o.PriceSelected > 0 ? o.PriceSelected : 0))
                  .ForMember(dest => dest.PriceBase, opt => opt.MapFrom(o => o.PriceBase > 0 ? o.PriceBase : 0))
                  .ForMember(dest => dest.Tax, opt => opt.MapFrom(o => o.Tax > 0 ? o.Tax : 0))
                  .ForMember(dest => dest.Idtemporality, opt => opt.MapFrom(o => o.TemporalitySelected > 0 ? o.TemporalitySelected : 0))
                  .ForMember(dest => dest.SourceId, opt => opt.Ignore())
                  .ForMember(dest => dest.CrmActivityId, opt => opt.Ignore())
                  .ForMember(dest => dest.IdVisitorSource, opt => opt.MapFrom(o => o.VisitorSourceId > 0 ? o.VisitorSourceId : 0))
                  .ForMember(dest => dest.PromotionId, opt => opt.MapFrom(o => o.PromotionId > 0 ? o.PromotionId : 0));
        }

        private static void GetUserEditCreditsDataModelMapping(IProfileExpression cfg)
        {
            cfg.CreateMap<UserEntity, UserEditCreditsDataModel>()
                .ForMember(dest => dest.IdUser, opt => opt.MapFrom(u => u.Id))
                .ForMember(dest => dest.IdUserEncrypt, opt => opt.MapFrom(u => u.IdEncrypt));
        }

        private static void GetUserCreditsEntity(IProfileExpression cfg)
        {
            cfg.CreateMap<UserEditCreditsDataModel, UserCreditsEntity>()
                .ForMember(dest => dest.IdStatus, opt => opt.MapFrom(u => u.IsChecked ? (short)StatusEnum.Active : (short)StatusEnum.Deleted))
                .ForMember(dest => dest.IdUser, opt => opt.MapFrom(u => u.IdUser))
                .ForMember(dest => dest.Units, opt => opt.MapFrom(u => u.AssignUnits))
                .ForMember(dest => dest.UnitsInitials, opt => opt.MapFrom(u => u.AssignUnits));
        }

        private static void GetVisionInterviewCommentsDataModel(IProfileExpression cfg)
        {
            cfg.CreateMap<InterviewEntity, VisionInterviewDataModel>()
               .ForMember(dest => dest.Date, opt => opt.MapFrom(i => i.InterviewDate))
               .ForMember(dest => dest.Questions, opt => opt.MapFrom(i => i.InterviewQuestions))
               .ForMember(dest => dest.VisibleBlockAnswer, opt => opt.MapFrom(i => i.ModerationStatusId != (short)InterviewModerationStatusEnum.Denounced
                                                                                   || i.HasCompanyComment))
               .ForMember(dest => dest.VisibleBoxLeaveComment, opt => opt.MapFrom(i => !i.HasCompanyComment))
               .ForMember(dest => dest.CompanyCommentDate, opt => opt.MapFrom(i => i.DateComment))
               .ForMember(dest => dest.VisibleLinkDenounced, opt => opt.MapFrom(i => i.ModerationStatusId != (short)InterviewModerationStatusEnum.Denounced))
               .ForMember(dest => dest.VisibleReportLinkBlocked, opt => opt.MapFrom(i => i.ModerationStatusId != (short)InterviewModerationStatusEnum.Denounced));
        }

        private static void GetNewsEntity(IProfileExpression cfg)
        {
            cfg.CreateMap<VisionNewsDetailDataModel, NewsEntity>()
                .ForMember(dest => dest.Id, opt => opt.Ignore());
        }

        private static void GetCompanyEntityToRegisterMapping(IProfileExpression cfg)
        {
            cfg.CreateMap<Models.SimplifiedRegister.RegisterDataModel, CompanyEntity>()
                    .ForMember(dest => dest.User, opt => opt.MapFrom(o => new UserEntity()
                    {
                        Username = o.Email,
                        Password = o.Password,
                        OriginId = (int)OfferOriginEnum.WEB,
                        TypeId = (int)UserTypeEnum.Company,
                        ContactName = GetContactName(o.ContactFullName),
                        Email = o.Email,
                        PhoneNumbers = o.CompanyPhone,
                        Id = o.UserId
                    }))
                    .ForMember(dest => dest.CreatedBy, opt => opt.MapFrom(o => CREATED_BY_DEFAULT))
                    .ForMember(dest => dest.CompanyName, opt => opt.MapFrom(o => o.CompanySocial))
                    .ForMember(dest => dest.Address, opt => opt.MapFrom(o => new AddressEntity()
                    {
                        CityId = o.CityIdSelected,
                        CountryId = o.CountryIdSelected,
                        LocationId = o.LocalizationIdSelected,
                        StreetAddress = o.Address
                    }))
                    .ForMember(dest => dest.TypeId, opt => opt.MapFrom(o => (int)CompanyTypeEnum.DIRECT_EMPLOYEE))
                    .ForMember(dest => dest.EmploymentNumber, opt => opt.MapFrom(o => o.NumberEmployeesIdSelected))
                    .ForMember(dest => dest.IndustryId, opt => opt.MapFrom(o => o.IndustryIdSelected))
                    .ForMember(dest => dest.ContactInformation, opt => opt.MapFrom(o => new ContactInformationEntity()
                    {
                        Email = o.Email,
                        Username = o.Email,
                        Name = GetContactName(o.ContactFullName),
                        Surname = GetContactSurName(o.ContactFullName),
                        PositionId = 0,
                        PhoneNumbers = new List<PhoneNumberEntity>()
                                       {
                                           new PhoneNumberEntity()
                                           {
                                              Number = o.CompanyPhone ?? string.Empty,
                                              TypeId = (int)TelephoneTypeEnum.FIJO
                                           },
                                       }
                    }))
                    .ForMember(dest => dest.OtherLocationsId, opt => opt.MapFrom(o => GetOtherLocalizationsString(o.OtherLocalizations)))
                    .ForMember(dest => dest.VacanciesPerYearId, opt => opt.MapFrom(o => o.VacanciesPerYearIdSelected));
        }

        private static void GetCompanyEntityToPreRegisterMapping(IProfileExpression cfg)
        {
            cfg.CreateMap<PreRegisterDataModel, CompanyEntity>()
                    .ForMember(dest => dest.User, opt => opt.MapFrom(o => new UserEntity()
                    {
                        Username = o.Email,
                        Password = o.Password,
                        OriginId = (int)OfferOriginEnum.WEB,
                        TypeId = (int)UserTypeEnum.Company,
                        ContactName = GetContactName(o.ContactFullName),
                        Email = o.Email,
                        PhoneNumbers = o.CompanyPhone,
                        Id = o.UserId
                    }))
                    .ForMember(dest => dest.CreatedBy, opt => opt.MapFrom(o => CREATED_BY_DEFAULT))
                    .ForMember(dest => dest.CompanyName, opt => opt.MapFrom(o => o.CompanySocial))
                    .ForMember(dest => dest.Address, opt => opt.MapFrom(o => new AddressEntity()
                    {
                        CityId = o.CityId,
                        CountryId = o.CountryId,
                        LocationId = o.LocalizationId,
                        StreetAddress = o.Address
                    }))
                    .ForMember(dest => dest.TypeId, opt => opt.MapFrom(o => (int)CompanyTypeEnum.DIRECT_EMPLOYEE))
                    .ForMember(dest => dest.EmploymentNumber, opt => opt.MapFrom(o => o.NumberEmployeesId))
                    .ForMember(dest => dest.IndustryId, opt => opt.MapFrom(o => o.IndustryId))
                    .ForMember(dest => dest.ContactInformation, opt => opt.MapFrom(o => new ContactInformationEntity()
                    {
                        Email = o.Email,
                        Username = o.Email,
                        Name = GetContactName(o.ContactFullName),
                        Surname = GetContactSurName(o.ContactFullName),
                        PositionId = 0,
                        PhoneNumbers = new List<PhoneNumberEntity>()
                                       {
                                           new PhoneNumberEntity()
                                           {
                                              Number = o.CompanyPhone ?? string.Empty,
                                              TypeId = (int)TelephoneTypeEnum.FIJO
                                           },
                                       }
                    }))
                    .ForMember(dest => dest.VacanciesPerYearId, opt => opt.MapFrom(o => o.VacanciesPerYearId));
        }

        private static void GetPreRegisterCompanyDataModel(IProfileExpression cfg)
        {
            cfg.CreateMap<CompanyPreRegisterDTO, PreRegisterDataModel>()
               .ForMember(dest => dest.CompanyIdPreRegister, opt => opt.MapFrom(i => i.Id))
               .ForMember(dest => dest.CompanyPhone, opt => opt.MapFrom(i => i.ContactTelephone))
               .ForMember(dest => dest.Email, opt => opt.MapFrom(i => i.ContactEmail))
               .ForMember(dest => dest.CompanySocial, opt => opt.MapFrom(i => i.CompanyName))
               .ForMember(dest => dest.CountryId, opt => opt.MapFrom(i => i.IdCountry))
               .ForMember(dest => dest.LocalizationId, opt => opt.MapFrom(i => i.IdLocalization))
               .ForMember(dest => dest.CityId, opt => opt.MapFrom(i => i.IdCity))
               .ForMember(dest => dest.IndustryId, opt => opt.MapFrom(i => i.IdIndustry))
               .ForMember(dest => dest.NumberEmployeesId, opt => opt.MapFrom(i => i.IdEmploymentNumber))
               .ForMember(dest => dest.VacanciesPerYearId, opt => opt.MapFrom(i => i.VacanciesPerYearId))
               .ForMember(dest => dest.CourtesyProductsId, opt => opt.MapFrom(i => i.ProductId))
               .ForMember(dest => dest.CourtesyProductsTemporalityId, opt => opt.MapFrom(i => i.ProductTemporalityId))
               .ForMember(dest => dest.ContactFullName, opt => opt.MapFrom(i => $"{i.ContactName} {i.ContactSurname}"));
        }

        private static string GetContactName(string contactFullName)
        {
            if (string.IsNullOrWhiteSpace(contactFullName))
                return string.Empty;

            return contactFullName.Split(' ').First().Trim();
        }

        private static string GetContactSurName(string contactFullName)
        {
            if (string.IsNullOrWhiteSpace(contactFullName))
                return string.Empty;

            return contactFullName.Substring(contactFullName.IndexOf(' ') + 1).Trim();
        }
    }
}