//using Braintree;
using PayPal.Api;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Core.Shared.Contracts.ServiceLibrary;
using Redarbor.Master.Entities.Payment;
using Redarbor.Master.Entities.PurchaseOperation;
using Redarbor.Payment.Contracts.ServiceLibrary;
using Redarbor.ProProduct.Contracts.ServiceLibrary;
using Redarbor.PurchaseOperation.Contracts.ServiceLibrary;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Web.Mvc;

namespace Redarbor.Company.WebUI.Controllers
{
    public class PayPalMultiPurchaseController : Controller
    {
        private string _userId = "";
        private int _orderNumber = 0;
        private int _offerId = 0;

        PayPal.Api.Payment payment;
        APIContext apiContext;
        ItemList _itemsList = new ItemList();
        PaymentPayPal _paymentConfig = new PaymentPayPal();
        MultiPurchaseOperationEntity _multipurchaseOperation = new MultiPurchaseOperationEntity();

        private readonly IMultiPurchaseOperationService _multipurchaseOperationService;
        private readonly IPaymentPayPalService _paymentPayPalService;
        private readonly IPaymentPayUService _paymentPayUService;
        private readonly IEncryptionService _encryptionService;
        private readonly IProProductService _proProductService;
        private readonly IExceptionPublisherService _exceptionPublisherService;
        private readonly IPortalConfigurationService _portalConfigurationService;
        private readonly IConfigurationService _configurationService;

        public PayPalMultiPurchaseController(IMultiPurchaseOperationService multipurchaseOperationService, IPaymentPayUService paymentPayUService, IPaymentPayPalService paymentPayPalService,
                                IEncryptionService encryptionService, IProProductService proProductService, IExceptionPublisherService exceptionPublisherService,
                                IPortalConfigurationService portalConfigurationService, IConfigurationService configurationService)
        {
            _multipurchaseOperationService = multipurchaseOperationService;
            _paymentPayUService = paymentPayUService;
            _paymentPayPalService = paymentPayPalService;
            _encryptionService = encryptionService;
            _proProductService = proProductService;
            _exceptionPublisherService = exceptionPublisherService;
            _portalConfigurationService = portalConfigurationService;
            _configurationService = configurationService;
        }

        public ActionResult Index(string orderNumber, string idUser, string idOffer)
        {
            int.TryParse(_encryptionService.Decrypt(orderNumber), out _orderNumber);
            int.TryParse(_encryptionService.Decrypt(idOffer), out _offerId);

            if (LoadParameters())
            {
                var urlRedirect = GenerateOrExecutePayment(idUser);
                if (!string.IsNullOrEmpty(urlRedirect))
                    return Redirect(urlRedirect);
            }

            return RedirectToAction("Index", "MultiPurchaseCartMessages", new { ppal = 1, hasError = true });
        }

        private string GenerateOrExecutePayment(string idUser)
        {
            var urlRedirect = "";

            if (string.IsNullOrEmpty(this.Request.QueryString["PayerID"]))
            {
                LoadItems();
                urlRedirect = GeneratePayment();
            }
            else
            {
                if (string.IsNullOrEmpty(this.Request.QueryString["token"]))
                {
                    _userId = this.Request.QueryString["PayerID"];
                }

                urlRedirect = ExecutePayment();
            }

            return urlRedirect;
        }

        #region "LoadData"

        private bool LoadPurchaseOrderData()
        {
            _multipurchaseOperation = _multipurchaseOperationService.GetByPK(_orderNumber);

            return _multipurchaseOperation.IdPurchaseOperation > 0;
        }

        private bool LoadPaymentConfig()
        {
            _paymentConfig = _paymentPayUService.GetPaymentPayPalByPortal(_multipurchaseOperation.Idportal);

            return _paymentConfig.ClientId != "";
        }

        #endregion

        private bool LoadParameters()
        {
            if (_orderNumber > 0)
            {
                return LoadPurchaseOrderData() && LoadPaymentConfig();
            }

            return false;
        }     

        private void LoadItems()
        {        
          
            if (_itemsList.items is null) _itemsList.items = new List<Item>();

            foreach (var item in _multipurchaseOperation.PurchaseProductsList)
            {              
                _itemsList.items.Add(new Item() { 
                    name = item.ComercialName,
                    price = item.Price.ToString("#0.00").Replace(',', '.'),
                    currency = _paymentConfig.Currency,
                    quantity = item.Unit.ToString(),
                    sku = item.IdProduct.ToString()
                });
            }         
        }

        #region GeneratePayment

        private string GeneratePayment()
        {
            PrepareObjectsForPayment();

            try
            {
                PayPal.Api.Payment createdPayment = payment.Create(apiContext);
                Session["PAYMENT_ID"] = createdPayment.id;

                return createdPayment.links[1].href;
            }
            catch (PayPal.Exception.PayPalException ex)
            {
                Trace.TraceError($"PayPalController GeneratePayment {ex}");
                _exceptionPublisherService.Publish(ex, "PayPalController", "GeneratePayment");

                return Url.Action("Index", "MultiPurchaseCartMessages", new { ppal = 1, hasError = true });
            }
        }

        private void PrepareObjectsForPayment()
        {
            try
            {
                Dictionary<string, string> sdkConfig = new Dictionary<string, string>();

                //Get the correct configuration to use the sandbox or live paypal environment
                PayPalConfiguration();

                sdkConfig.Add("mode", _paymentConfig.Mode);

                string accessToken = new PayPal.OAuthTokenCredential(_paymentConfig.ClientId, _paymentConfig.SecretCode, sdkConfig).GetAccessToken();
                Session["TOKEN"] = accessToken;
                apiContext = new PayPal.Api.APIContext(accessToken);
                apiContext.Config = sdkConfig;

                PayPalTransaction transaction = new PayPalTransaction();
                transaction.idOperacionCompra = _orderNumber;
                transaction.paypalToken = accessToken;
                transaction.idStatus = 1;
                _paymentPayPalService.Add(transaction, _paymentConfig.IdPortal);

                List<Transaction> transactionList = GetTransactionList();

                var pay = new Payer();
                pay.payment_method = "paypal";

                RedirectUrls redirUrls = GetRedirectUrls();

                payment = new PayPal.Api.Payment();
                payment.intent = "sale";
                payment.payer = pay;
                payment.transactions = transactionList;
                payment.redirect_urls = redirUrls;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"PayPalController PrepareCreatePayment {ex}");
                _exceptionPublisherService.Publish(ex, "PayPalController", "PrepareCreatePayment");

                RedirectToAction("Index", "MultiPurchaseCartMessages", new { ppal = 1, hasError = 1 });
            }
        }

        private List<Transaction> GetTransactionList()
        {
            PaymentPayPal _paymentConfig = _paymentPayUService.GetPaymentPayPalByPortal(_multipurchaseOperation.Idportal);

            var amount = new Amount();
            amount.currency = _paymentConfig.Currency;
            amount.total = _multipurchaseOperation.Price.ToString("#0.00").Replace(',', '.');

            var transactionList = new List<Transaction>();
            var transaction = new Transaction();
            transaction.description = _multipurchaseOperation.GetProductsDescription();
            transaction.amount = amount;
            transaction.item_list = _itemsList;
            transactionList.Add(transaction);
            return transactionList;
        }

        private RedirectUrls GetRedirectUrls()
        {
            string accessToken = Session["TOKEN"].ToString();

            var redirUrls = new RedirectUrls();
            string cancelUrl = "";
            string paymentUrl = "";
            var portalConfig = _portalConfigurationService.GetPortalConfiguration();

            if (_configurationService.AppSettings["PAYPAL_TEST"].ToString() != "0")
            {
                cancelUrl = $@"{portalConfig.AEPortalConfig.PaypalUrlReturn}{Url.Action("Index", "MultiPurchaseCartMessages",
                      new
                      {
                          hasError = 1,
                          ppal = 1,
                          Ds_order = _encryptionService.Encrypt(_multipurchaseOperation.IdPurchaseOperation.ToString())
                      })}";

                paymentUrl = $@"{portalConfig.AEPortalConfig.PaypalUrlReturn}{Url.Action("Index", "PayPalMultiPurchase",
                    new
                    {
                        ppal = 1,
                        orderNumber = _encryptionService.Encrypt(_multipurchaseOperation.IdPurchaseOperation.ToString())
                    })}";
            }

            else
            {
                cancelUrl = $@"{portalConfig.url_empresa}{Url.Action("Index", "MultiPurchaseCartMessages",
                       new
                       {
                           hasError = 1,
                           ppal = 1,
                           Ds_order = _encryptionService.Encrypt(_multipurchaseOperation.IdPurchaseOperation.ToString())
                       })}";

                paymentUrl = $@"{portalConfig.url_empresa}{Url.Action("Index", "PayPalMultiPurchase",
                       new
                       {
                           ppal = 1,
                           orderNumber = _encryptionService.Encrypt(_multipurchaseOperation.IdPurchaseOperation.ToString())
                       })}";
            }

            redirUrls.cancel_url = cancelUrl.ToString();

            redirUrls.return_url = paymentUrl.ToString();

            return redirUrls;
        }

        private void PayPalConfiguration()
        {
            if (_configurationService.AppSettings["PAYPAL_TEST"].ToString() != "0")
            {
                // Enable to use the PayPal Sandbox
                _paymentConfig.ClientId = _configurationService.AppSettings["PAYPAL_CLIENT_ID"].ToString();
                _paymentConfig.SecretCode = _configurationService.AppSettings["PAYPAL_CLIENT_SECRET"].ToString();
                _paymentConfig.Mode = _configurationService.AppSettings["PAYPAL_MODE"].ToString();
            }
        }

        #endregion

        #region ExecutePayment

        private string ExecutePayment()
        {
            _paymentConfig = _paymentPayUService.GetPaymentPayPalByPortal(_multipurchaseOperation.Idportal);
            string pagePaymentOk = "";
            var sdkConfig = new Dictionary<string, string>();

            //Get the correct configuration to use the sandbox or live paypal environment
            PayPalConfiguration();

            sdkConfig.Add("mode", _paymentConfig.Mode);

            try
            {
                string accessToken = new PayPal.OAuthTokenCredential(_paymentConfig.ClientId, _paymentConfig.SecretCode, sdkConfig).GetAccessToken();

                Session["TOKEN"] = accessToken;
                apiContext = new PayPal.Api.APIContext(accessToken);
                apiContext.Config = sdkConfig;

                payment = new PayPal.Api.Payment();

                payment.id = Session["PAYMENT_ID"].ToString();

                var paymentExecution = new PaymentExecution();
                paymentExecution.payer_id = this.Request.QueryString["PayerID"];// _userId;

                var executedPayment = new PayPal.Api.Payment();
                executedPayment = payment.Execute(apiContext, paymentExecution);

                if (executedPayment.state == "approved")
                {
                    UpdateTransaction(accessToken, executedPayment, _orderNumber);
                    pagePaymentOk = $"{Url.Action("Index", "MultiPurchaseCartMessages", new { ppal = 1, token = accessToken, Ds_AuthorisationCode = executedPayment.transactions[0].related_resources[0].sale.id })}";
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"PayPalController ExecutePayment {ex}");
                _exceptionPublisherService.Publish(ex, "PayPalController", "ExecutePayment");

                return Url.Action("Index", "MultiPurchaseCartMessages", new { ppal = 1, hasError = true });
            }

            return pagePaymentOk;
        }

        private void UpdateTransaction(string token, PayPal.Api.Payment executedPayment, int orderId)
        {
            var transaction = new PayPalTransaction();
            transaction.idOperacionCompra = orderId;
            transaction.paypalToken = token;
            transaction.idStatus = 2;
            transaction.idTransaction = executedPayment.transactions[0].related_resources[0].sale.id;
            _paymentPayPalService.UpdateStatus(transaction, _paymentConfig.IdPortal);
        }

        #endregion
    }
}