using Redarbor.Company.WebUI.Models.Company.Cart;
using Redarbor.Company.WebUI.Security;
using Redarbor.Web.UI.Library.Controllers;
using System.Web.Mvc;

namespace Redarbor.Company.WebUI.Controllers
{
    public class CompanyCartPaymentFormController : RedarborController
    {
        [RedarborAuthorize]
        public ActionResult Index(CompanyCartPaymentFormDataModel dataModel)
        {
            return View(dataModel);
        }
        
    }
}