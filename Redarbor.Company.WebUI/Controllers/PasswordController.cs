using Redarbor.Common.Entities.Configuration;
using Redarbor.Common.Entities.Enums;
using Redarbor.Company.WebUI.Helpers;
using Redarbor.Company.WebUI.Models;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Core.Shared.Contracts.ServiceLibrary;
using Redarbor.Master.Contracts.ServiceLibrary;
using Redarbor.Master.Entities.Users;
using Redarbor.User.Contracts.ServiceLibrary;
using Redarbor.Web.UI.Library.Controllers;
using System;
using System.Diagnostics;
using System.Linq;
using System.Web.Mvc;

namespace Redarbor.Company.WebUI.Controllers
{
    [RoutePrefix("SetPassword.aspx")]
    public class PasswordController : RedarborController
    {
        private readonly IEncryptionService _encryptionService;
        private readonly IUserService _userService;
        private readonly IPortalConfigurationService _portalConfigurationService;
        private readonly IUserCredentialService _userCredentialService;
        private readonly IExceptionPublisherService _exceptionPublisherService;
        private readonly ISecurityService _securityService;

        public PasswordController(IEncryptionService encryptionService,
            IUserService userService,
            IPortalConfigurationService portalConfigurationService,
            IUserCredentialService userCredentialService,
            IExceptionPublisherService exceptionPublisherService,
            ISecurityService securityService)
        {
            _encryptionService = encryptionService;
            _userService = userService;
            _portalConfigurationService = portalConfigurationService;
            _userCredentialService = userCredentialService;
            _exceptionPublisherService = exceptionPublisherService;
            _securityService = securityService;
        }

        [Route]
        public ActionResult Index(string ID = "", string token= "")
        {
            try
            {
                var user = new UserEntity();
                var portalConfig = _portalConfigurationService.GetPortalConfiguration();
                if (IsValidToken(ID, token, ref user))
                    return View(GetPasswordDataModel(ID, token));
                else
                {
                    TempData["message_alert_token"] = "LIT_ALERT_TOKEN_NO_VALIDO";
                    return RedirectToAction("Index", "RecoverPassword");
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"PasswordController Index {ex}");
                _exceptionPublisherService.Publish(ex, "PasswordController", "Index");
                return RedirectToAction("Index", "Home");
            }
        }

        [Route]
        [HttpPost]
        [ValidateAntiForgeryToken()]
        public ActionResult Index(PasswordDataModel model)
        {
            try
            {
                var user = new UserEntity();
                if (IsValidToken(model.EncryptedId, model.Token, ref user))
                {
                    if (_userCredentialService.ChangePassword(user, user.Password, model.NewPassword, true))
                    {
                        _securityService.ConsumeToken(model.Token, (int)user.Id);
                        return RedirectToAction("Index", "Company");
                    }
                }
                TempData["message_alert_token"] = "LIT_ALERT_TOKEN_NO_VALIDO";
                return RedirectToAction("Index", "RecoverPassword");
            }
            catch (Exception ex)
            {
                Trace.TraceError($"PasswordController Index-Post {ex}");
                _exceptionPublisherService.Publish(ex, "PasswordController", "Index-Post");
                return RedirectToAction("Index", "Home");
            }
        }

        public JsonResult CheckNewPasswordFormat(string newPassword)
        {
            var currentPassword = newPassword ?? string.Empty;

            return Json(currentPassword != string.Empty &&
                (currentPassword.Length > 4 &&
                currentPassword.Length < 20), JsonRequestBehavior.AllowGet);
        }

        public JsonResult CheckRepeatNewPassword(string repeatNewPassword, string newPassword)
        {
            return Json(newPassword == repeatNewPassword, JsonRequestBehavior.AllowGet);
        }

        private PasswordDataModel GetPasswordDataModel(string encryptedId, string token)
        {
            return new PasswordDataModel()
            {
                EncryptedId = encryptedId,
                NewPassword = string.Empty,
                RepeatNewPassword = string.Empty,
                ShowPassword = false,
                Token = token
            };
        }

        private bool IsValidToken(string ID, string token, ref UserEntity user)
        {
            var portalConfig = _portalConfigurationService.GetPortalConfiguration();
            if (!string.IsNullOrEmpty(ID))
            {
                var id = _encryptionService.Decrypt(ID);

                if (!string.IsNullOrEmpty(id) && id.Split('|').Count() == 2)
                {
                    var email = id.Split('|')[0];

                    if (!string.IsNullOrEmpty(email))
                    {
                        user = _userService.GetByEmail(email, portalConfig.PortalId);

                        if (user.Id == 0)
                        {
                            ModelState.AddModelError("", PageLiteralsHelper.GetLiteral("LIT_ALERT_CORREO_NO_EXISTE", (short)PageEnum.RecoverPasswordCompany, portalConfig));
                            return false;
                        }
                        else
                        {
                            var userIdByToken = _securityService.GetUserIdByToken(token);
                            if (userIdByToken == user.Id) return true;

                            ModelState.AddModelError("", PageLiteralsHelper.GetLiteral("LIT_ALERT_PROBLEMA_AL_MODIFICAR", (short)PageEnum.RecoverPasswordCompany, portalConfig));
                        }
                    }
                }
            }
            ModelState.AddModelError("", PageLiteralsHelper.GetLiteral("LIT_ALERT_CORREO_NO_EXISTE", (short)PageEnum.RecoverPasswordCompany, portalConfig));
            return false;
        }

    }
}