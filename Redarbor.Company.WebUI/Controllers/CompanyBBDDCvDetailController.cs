using AutoMapper;
using Redarbor.Candidate.Contracts.ServiceLibrary;
using Redarbor.Candidate.Contracts.ServiceLibrary.DTO;
using Redarbor.CommentCv.Contracts.ServiceLibrary;
using Redarbor.Common.Entities;
using Redarbor.Common.Entities.Configuration;
using Redarbor.Common.Entities.Enums;
using Redarbor.Company.Contracts.ServiceLibrary;
using Redarbor.Company.Contracts.ServiceLibrary.Configuration;
using Redarbor.Company.Contracts.ServiceLibrary.Enums;
using Redarbor.Company.WebUI.Enums;
using Redarbor.Company.WebUI.Helpers;
using Redarbor.Company.WebUI.Helpers.Mappings;
using Redarbor.Company.WebUI.Models;
using Redarbor.Company.WebUI.Models.Company;
using Redarbor.Company.WebUI.Models.Company.Candidate;
using Redarbor.Company.WebUI.Models.Company.Candidate.Competences;
using Redarbor.Company.WebUI.Models.Company.Candidate.Cv;
using Redarbor.Company.WebUI.Models.Company.Cv;
using Redarbor.Company.WebUI.Models.Company.Home;
using Redarbor.Company.WebUI.Models.Company.Match;
using Redarbor.Company.WebUI.Models.Company.Offer;
using Redarbor.Company.WebUI.Security;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary.Enums;
using Redarbor.Core.Contracts.ServiceLibrary;
using Redarbor.Core.Counter.Contracts.ServiceLibrary;
using Redarbor.Core.Elastic.Library;
using Redarbor.Core.Event.Contracts.ServiceLibrary;
using Redarbor.Core.Kpi.Contracts.ServiceLibrary;
using Redarbor.Core.Kpi.Contracts.ServiceLibrary.Enums;
using Redarbor.Core.Resolver.Contracts.ServiceLibrary;
using Redarbor.Core.Shared.Contracts.ServiceLibrary;
using Redarbor.Curriculum.Contracts.ServiceLibrary;
using Redarbor.CustomFolders.Contracts.ServiceLibrary;
using Redarbor.Dictionaries.Consumer.Enums;
using Redarbor.Elastic.Entities.Candidate;
using Redarbor.Elastic.Entities.Enums;
using Redarbor.Extensions.Library.Extensions;
using Redarbor.Geolocation.Contracts.ServiceLibrary;
using Redarbor.Master.Contracts.ServiceLibrary;
using Redarbor.Master.Contracts.ServiceLibrary.DTO;
using Redarbor.Master.Entities.Candidate;
using Redarbor.Master.Entities.Counters;
using Redarbor.Master.Entities.CustomFolder;
using Redarbor.Master.Entities.Cv;
using Redarbor.Master.Entities.Enums;
using Redarbor.Master.Entities.Match;
using Redarbor.Master.Entities.Offers;
using Redarbor.Master.Entities.Product;
using Redarbor.Master.Entities.Searchs;
using Redarbor.Master.Impl.ServiceLibrary.KinesisEventHelper;
using Redarbor.Master.Impl.ServiceLibrary.KinesisEventHelper.Entities;
using Redarbor.Match.Contracts.ServiceLibrary;
using Redarbor.Match.Contracts.ServiceLibrary.DTO;
using Redarbor.Offer.Contracts.ServiceLibrary;
using Redarbor.Offer.Contracts.ServiceLibrary.DTO;
using Redarbor.Offer.Contracts.ServiceLibrary.Enums;
using Redarbor.Products.Contracts.ServiceLibrary;
using Redarbor.Products.Contracts.ServiceLibrary.DTO;
using Redarbor.ProductSubGroups.Contracts.ServiceLibrary;
using Redarbor.Quiz.Contract.ServiceLibrary.DTO;
using Redarbor.RatingCv.Contracts.ServiceLibrary;
using Redarbor.Repo.Trackings.Library.Entities;
using Redarbor.TrackingRepo.Contracts.ServiceLibrary;
using Redarbor.Web.UI.Library;
using Redarbor.Web.UI.Library.Exceptions;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using System.Web.Mvc;

namespace Redarbor.Company.WebUI.Controllers
{
    [RoutePrefix("Company/BBDDCvDetail")]
    [RedarborAuthorize]
    public class CompanyBBDDCvDetailController : CompanyBaseController
    {
        short PageId = (short)PageEnum.DetailCVCompany;
        PortalConfig _portalConfig = new PortalConfig();

        private const string HAVE_BBDDCVPRODUCT_ACTIVE = "1";
        private const short MAX_STRING_LENGTH_BY_CV_VISUALIZATION_SEARCH_FILTER = 255;
        private const char SEPARATOR_SPLIT = ',';
        private const string EXTRA_NUM_WHATSAPP_MX = "1";
        private const string EXTRA_NUM_WHATSAPP_AR = "9";
        private readonly string _maxFolders = ConfigurationManager.AppSettings["MAX_CUSTOM_FOLDER"] ?? "6";
        private string COMPETENCES = "Competencias";
        private string VALUES = "Valores";
        private string PERSONALITY = "Personalidad laboral";

        private readonly ICompanyCountersService _companyCountersService;
        private readonly ICurriculumService _curriculumService;
        private readonly IRatingCvService _ratingCvService;
        private readonly ICommentCvService _commentCvService;
        private readonly ICustomFolderService _customFoldersService;
        private readonly IEncryptionService _encryptionService;
        private readonly IMatchService _matchService;
        private readonly ICandidateService _candidateService;
        private readonly IDateTimeToolsService _dateTimeToolsService;
        private readonly IKillerQuestionsService _killerquestionService;
        private readonly IOfferCvCountersService _offerCvCountersService;
        private readonly IKpiService _kpiService;
        private readonly ISessionService _sessionService;
        private readonly IGeoLocationService _geolocationService;
        private readonly IQuizConsumerService _quizConsumerService;
        private readonly ICvVisualizationControlService _cvVisualizationControlService;
        private readonly ICvVisualizationCountersService _cvVisualizationCountersService;
        private readonly ICvVisualizationHistoryService _cvVisualizationHistoryService;
        private readonly ICvVisualizationSearchFIltersService _cvVisualizationSearchFIltersService;
        private readonly ICvVisualizationsTotalsService _cvVisualizationsTotalsService;
        private readonly IClientIpAddressResolverService _clientIpAddressResolverService;
        private readonly IElasticCandidateService _elasticCandidateService;
        private readonly ILandingProductsService _landingProductsService;
        private readonly IReportCandidatePhotoRequestEventService _reportCandidatePhotoRequestEventService;
        private readonly ISecurityOfferService _securityOfferService;

        Dictionary<string, string> CountryDic = new Dictionary<string, string>();
        Dictionary<string, string> LocalizationDic = new Dictionary<string, string>();
        Dictionary<string, string> MaritalStatusDic = new Dictionary<string, string>();
        Dictionary<string, string> EmploymentStatusDic = new Dictionary<string, string>();
        Dictionary<string, string> LegalSituationDic = new Dictionary<string, string>();
        Dictionary<string, string> CategoryDic = new Dictionary<string, string>();
        Dictionary<string, string> IndustryDic = new Dictionary<string, string>();
        Dictionary<string, string> LanguageDic = new Dictionary<string, string>();
        Dictionary<string, string> LanguageLevelDic = new Dictionary<string, string>();
        Dictionary<string, string> MonthDic = new Dictionary<string, string>();
        Dictionary<string, string> IdentificationTypeDic = new Dictionary<string, string>();
        Dictionary<string, string> SkillTypeDic = new Dictionary<string, string>();


        public CompanyBBDDCvDetailController(
            IKpiService kpiService,
            ICompanyCountersService companyCountersService,
            IKillerQuestionsService killerquestionService,
            IDateTimeToolsService dateTimeToolsService,
            IProductService productService,
            IProductSubGroupsService productSubGroupsService,
            IOfferService offerService,
            ICompanyProductService companyProductService,
            ICompanyService companyService,
            IMatchService matchService,
            IPortalConfigurationService portalConfigurationService,
            IDictionaryService dictionaryService,
            ISecurityService securityService,
            ICurriculumService curriculumService,
            IRatingCvService ratingCvService,
            ICommentCvService commentCvService,
            ICustomFolderService customFoldersService,
            IEncryptionService encryptionService,
            ICandidateService candidateService,
            ISessionService sessionService,
            IOfferCvCountersService offerCvCountersService,
            IGeoLocationService geolocationService,
            IExceptionPublisherService exceptionPublisherService,
            IQuizConsumerService quizConsumerService,
            ICvVisualizationControlService cvVisualizationControlService,
            ICvVisualizationCountersService cvVisualizationCountersService,
            ICvVisualizationHistoryService cvVisualizationHistoryService,
            ICvVisualizationSearchFIltersService cvVisualizationSearchFIltersService,
            ICvVisualizationsTotalsService cvVisualizationsTotalsService,
            IClientIpAddressResolverService clientIpAddressResolverService,
            IElasticCandidateService elasticCandidateService,
            ILandingProductsService landingProductsService,
            IReportCandidatePhotoRequestEventService reportCandidatePhotoRequestEventService,
            ISecurityOfferService securityOfferService) : base(productService, productSubGroupsService, offerService, companyProductService, companyService, portalConfigurationService, dictionaryService, securityService, exceptionPublisherService)
        {
            _kpiService = kpiService;
            _companyCountersService = companyCountersService;
            _killerquestionService = killerquestionService;
            _curriculumService = curriculumService;
            _ratingCvService = ratingCvService;
            _commentCvService = commentCvService;
            _matchService = matchService;
            _customFoldersService = customFoldersService;
            _encryptionService = encryptionService;
            _candidateService = candidateService;
            _dateTimeToolsService = dateTimeToolsService;
            _sessionService = sessionService;
            _offerCvCountersService = offerCvCountersService;
            _geolocationService = geolocationService;
            _quizConsumerService = quizConsumerService;
            _cvVisualizationControlService = cvVisualizationControlService;
            _cvVisualizationCountersService = cvVisualizationCountersService;
            _cvVisualizationHistoryService = cvVisualizationHistoryService;
            _cvVisualizationSearchFIltersService = cvVisualizationSearchFIltersService;
            _cvVisualizationsTotalsService = cvVisualizationsTotalsService;
            _clientIpAddressResolverService = clientIpAddressResolverService;
            _elasticCandidateService = elasticCandidateService;
            _landingProductsService = landingProductsService;
            _reportCandidatePhotoRequestEventService = reportCandidatePhotoRequestEventService;
            _securityOfferService = securityOfferService;
        }

        [HttpPost]
        public bool ConsumeCreditToViewCv(string idcv, string idc, string oi = null)
        {
            try
            {
                var companyCredentials = SecurityHelper.GetCompanyCredentials();
                _portalConfig = PortalConfigurationService.GetPortalConfiguration();

                OfferEntity offer = new OfferEntity();

                Int32.TryParse(_encryptionService.Decrypt(idcv), out int idCv);
                if (idCv <= 0) return false;

                Int32.TryParse(_encryptionService.Decrypt(idc), out int idCandidate);
                if (idCandidate <= 0) return false;

                Int32.TryParse(_encryptionService.Decrypt(oi), out int idOffer);

                if (idOffer > 0)
                {
                    offer = OfferService.GetByPk(idOffer, _portalConfig.PortalId);
                }

                if (!CompanyService.ExistsVisualization(companyCredentials.IdCompany, idCv))
                {
                    if (CompanyProductService.ConsumeUnitsCVBBDD(companyCredentials.IdCompany, idCv, idCandidate, (int)companyCredentials.UserId, offer.Integrations.FirstOrDefault()?.expirationtime ?? DateTime.MinValue, _portalConfig.PortalId, offer.idofferCT, offer.Integrations.FirstOrDefault()?.idproduct ?? 0, offer.Integrations.FirstOrDefault()?.idcompanyproduct ?? 0, OfferIntegratorEnum.CompuTrabajo))
                    {
                        if (offer.Integrations.FirstOrDefault()?.idproduct > 0 && ProductService.GetProductGroupIdByProductId(companyCredentials.PortalId, offer.Integrations.FirstOrDefault()?.idproduct ?? 0) == (short)ProductGroupsEnum.Packs)
                            _offerCvCountersService.IncrementCounterCvDetailByPacks(offer.idofferCT, companyCredentials.PortalId);

                        Task.Factory.StartNew(() =>
                        {
                            _companyCountersService.AddCounterCompany((int)KpiEnum.CV_CONSUMED_TODAY, _portalConfig.PortalId, companyCredentials.IdCompany, 1, true);
                        });

                        _elasticCandidateService.InsertInElastic(new List<int> { idCandidate }, _portalConfig.PortalId);

                        return true;
                    }
                }
                return false;
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyBBDDCvDetailController ConsumeCreditToViewCv {ex}");
                ExceptionPublisherService.Publish(ex, "CompanyBBDDCvDetailController", "ConsumeCreditToViewCv");
                return false;
            }
        }


        [Route("BBDDDetail")]
        public ActionResult Index(string oi = null, string ims = null, string idcv = null, string cf = null, string q = null, string cats = null, string prov = null)
        {
            var watch = new Stopwatch();

            watch.Start();
            CompanyCredentials companycredentialsAux = null;
            var cvDetail = new CvDetailDataModel();
            var offer = new OfferEntity();

            try
            {
                var companyCredentials = SecurityHelper.GetCompanyCredentials();
                companycredentialsAux = companyCredentials;
                _portalConfig = PortalConfigurationService.GetPortalConfiguration();

                var company = CompanyService.GetByPK(new CompanySearchSpecifications(companyCredentials.PortalId) { Id = companyCredentials.IdCompany });
                var companyProduct = CompanyProductService.GetByIdCompany(companyCredentials.IdCompany, companyCredentials.PortalId, OfferIntegratorEnum.CompuTrabajo);

                if (checkBlocked(_portalConfig.company_block, company.Blocked, company.LastDateBlockedUpdate, _portalConfig.DaysBlock))
                    return RedirectToAction("Blocked", "Account");

                if (company.CompanyStatusId == (short)CompanyStatusEnum.Discarted)
                    return RedirectToAction("ViewCvsAttempt", "CompanyRejected");

                LoadDictionaries(_portalConfig);

                Int32.TryParse(_encryptionService.Decrypt(oi), out var idOffer);

                offer = idOffer > 0 ? OfferService.GetByPk(idOffer, _portalConfig.PortalId) : new OfferEntity();

                var referenceProduct = CompanyProductService.GetBBDDcvProductToConsume(offer, companyCredentials, _portalConfig);

                if (_portalConfig.AEPortalConfig.ActivateCompanyCvsConsumible &&
                    referenceProduct.SubGroupId != (short)ProductSubGroupsEnum.BBDDcv &&
                    referenceProduct.GroupId != (short)ProductGroupsEnum.Membership)
                {
                    FillBBDDcvProdForSale(cvDetail, companyCredentials);
                }

                cvDetail.HaveMembresyActive = (referenceProduct.GroupId == (short)ProductGroupsEnum.Membership);
                cvDetail.CvDetailPackAndBasicModel.ItsBBDDcvProdActivated = _portalConfig.AEPortalConfig.ActivateCompanyCvsConsumible && referenceProduct.GroupId != (short)ProductGroupsEnum.Membership;
                cvDetail.IsBasicOffer = referenceProduct.GroupId == (short)ProductGroupsEnum.Freemium;
                cvDetail.HasCustomFolders = referenceProduct.Features.Any(n => n.AmbitId == (short)ProductAmbitEnum.CustomFolders);

                if (offer.idoffer > 0)
                {
                    cvDetail.OfferIdEncrypted = EncryptationHelper.Encrypt(offer.idoffer.ToString());
                    cvDetail.OfferCtIdEncrypted = EncryptationHelper.Encrypt(offer.idofferCTencrypted.ToString());
                    cvDetail.OfferTitle = offer.title;
                    cvDetail.HasFoldersDetail = referenceProduct.GroupId == (short)ProductGroupsEnum.Packs || referenceProduct.SubGroupId == (short)ProductSubGroupsEnum.BBDDcv;
                }

                Int32.TryParse(_encryptionService.Decrypt(idcv), out int idCv);
                cvDetail.IsViewed = CompanyService.ExistsVisualization(companyCredentials.IdCompany, idCv);

                var fullCv = _curriculumService.GetCandidateCompletCV(idCv, _portalConfig);

                LogicShowVisitsMyProfile(companyCredentials, fullCv.IdCandidate, fullCv.IdCv, q, cats, prov, cvDetail.IsMatch, company.CvVisualizationPrivacy, offer);
                CandidateReadDataModel candidate = LoadDataCv(idCv, fullCv.IdCandidate, companyCredentials, referenceProduct);
                cvDetail.Candidate = candidate;

                if (cvDetail.HasCustomFolders && !cvDetail.HasFoldersDetail)
                    LoadDataFoldersBBDD(cvDetail, companyCredentials);

                if (cvDetail.HasFoldersDetail)
                    LoadDataFolders(cvDetail, offer, referenceProduct, cvDetail.HasCustomFolders, companyCredentials, cf);

                cvDetail.HasCompanyCommentsFeature = referenceProduct.Features.Any(n => n.AmbitId == (short)ProductAmbitEnum.CommentsCV);
                cvDetail.HasCompanyRatingsFeature = referenceProduct.Features.Any(n => n.AmbitId == (short)ProductAmbitEnum.RatingsCvs);
                cvDetail.HasCompanyTestCompetencesFeature = referenceProduct.Features.Any(n => n.AmbitId == (short)ProductAmbitEnum.CompetencesTest);
                cvDetail.HasTalentView3dFeature = referenceProduct.Features.Any(n => n.AmbitId == (short)ProductAmbitEnum.TalentView3D);
                SetCVFiles(cvDetail, fullCv);
                cvDetail.IdCandidateEncrypted = _encryptionService.Encrypt(cvDetail.Candidate.Candidate.IdCandidate.ToString());
                cvDetail.IdCvEncrypted = idcv;

                VisibilityDetailCv(offer, cvDetail, referenceProduct, companyCredentials, _portalConfig);

                if (!cvDetail.IsViewed)
                {
                    CvIsBlock(candidate);
                }
                else
                {
                    cvDetail.HasWhatsapp = referenceProduct.Features.Any(n => n.AmbitId == (short)ProductAmbitEnum.Whatsapp);
                    cvDetail.HasSkype = referenceProduct.Features.Any(n => n.AmbitId == (short)ProductAmbitEnum.Skype);
                    if (cvDetail.HasWhatsapp)
                        GenerateWhatsAppData(candidate, offer, cvDetail, companyCredentials);
                }

                FillNextPreviousFromCvs(cvDetail, idcv, oi, cf, companyCredentials, offer);

                var hasNewFreemiumChat = HasNewFreemiumChat(_portalConfig, companyCredentials.IdCompany);
                cvDetail.IsPremium = _portalConfig.AEPortalConfig.IsPremiumActive ? cvDetail.Candidate.Candidate.IsPremium : false;
                cvDetail.ShowVideoPresentation = HasPermissionsPremiumToShow(cvDetail.IsPremium, candidate.Candidate.HasCodePresentationVideo || candidate.Candidate.HasVideoPresentationPreAssigned, _portalConfig.AEPortalConfig.HasFreeFeatureVideoPresentation);
                cvDetail.ShowPhone1Verification = HasPermissionsPremiumToShow(cvDetail.IsPremium, candidate.Candidate.Phone1VerificationStatus, _portalConfig.AEPortalConfig.HasFreeFeaturePhone1Verification);
                cvDetail.UrlPresentationVideo = GetUrlPresentationVideo(cvDetail.Candidate.Candidate.CodePresentationVideo);
                cvDetail.ProdCWConvertToComplete = _landingProductsService.ManageCWFeatures((int)ProductAmbitEnum.CVsFreemium, companyCredentials.IdCompany, _portalConfig);
                cvDetail.BtnConvertToCompleteEncryptedName = _encryptionService.Encrypt(((int)TpvButtonOriginEnum.OfferMatchesDetailConvertToPack).ToString());
                cvDetail.BtnAutoPopUpConvertToCompleteEncryptedName = _encryptionService.Encrypt(((int)TpvButtonOriginEnum.OfferMatchesDetailConvertToPack_autoPopUp).ToString());
                cvDetail.HasNewFreemiumChat = hasNewFreemiumChat;
                cvDetail.UrlCreateConversation = GetUrlCreateConversation(hasNewFreemiumChat, referenceProduct);
                cvDetail.UrlIndexConversations = GetUrlIndexConversations(hasNewFreemiumChat, referenceProduct);
                cvDetail.UrlInsertMessage = GetUrlInsertMessage(hasNewFreemiumChat, referenceProduct);

                DetailKpiAction(cvDetail.IsMatch, !string.IsNullOrEmpty(cvDetail.Candidate.Candidate.Photo), companyCredentials);

                cvDetail.PageLiteralsDataModel.PageLiterals = FillPageLiterals(_portalConfig);

                watch.Stop();

                KinesisEventHelper.Add(new EventDataBuildDto()
                {
                    IdPortal = companyCredentials.PortalId,
                    TotalTimeOfActions = watch.ElapsedMilliseconds,
                    //todo: hablar con Xavi Jacas
                    Type = cvDetail.IsMatch ? TypeEventDataEnum.CvDetailMatchLoadTime : TypeEventDataEnum.CvDetailSearchLoadTime
                });

                return View("Index", cvDetail);
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyBBDDCvDetailController Index {ex}");
                Dictionary<string, string> extradata = new Dictionary<string, string>();
                extradata.Add("Idcompany", companycredentialsAux?.IdCompany.ToString());
                extradata.Add("IdPortal", companycredentialsAux?.PortalId.ToString());
                extradata.Add("CompanyName", companycredentialsAux?.CompanyName.ToString());
                extradata.Add("UserRole", companycredentialsAux?.UserRole.ToString());
                extradata.Add("UserId", companycredentialsAux?.UserId.ToString());
                extradata.Add("UserName", companycredentialsAux?.Username.ToString());
                extradata.Add("idcv", idcv);
                extradata.Add("OfferId", offer?.idoffer.ToString());
                extradata.Add("CandidateEncryptId", cvDetail?.IdCandidateEncrypted);
                extradata.Add("UserIdCvDetail", cvDetail?.Candidate.User.Id.ToString());
                extradata.Add("CandidateId", cvDetail?.Candidate.Candidate.IdCandidate.ToString());
                ExceptionPublisherService.Publish(ex, "CompanyBBDDCvDetailController", "Index", false, extradata);
                return RedirectToAction("Index", "Home");
            }
        }

        private void SetCVFiles(CvDetailDataModel cvDetail, CvEntity fullCv)
        {
            CandidateFileEntity candidateFile = _candidateService.GetPrincipalCandidateFile(fullCv.IdCandidate, _portalConfig.PortalId);
            if (candidateFile.Id != 0)
            {
                cvDetail.HasDocument = true;
                cvDetail.TypeDocument = "i_doc";
                if (candidateFile.ExtensionId == (short)CandidateFileExtensionEnum.PDF)
                {
                    cvDetail.TypeDocument = "i_pdf";
                }
            }
        }

        private void FillBBDDcvProdForSale(CvDetailDataModel cvDetail, CompanyCredentials companyCredentials)
        {
            var listProductsByPage = ProductService.SearchByPage(GetProductSearchByPage(_portalConfig.PortalId, PageEnum.PackCVSearch, companyCredentials.IdCompany, _portalConfig.active_promotions, IsDisableFreemiumOffers(companyCredentials.IdCompany, _portalConfig)));
            cvDetail.CvDetailPackAndBasicModel.IdProductBBDDcv = listProductsByPage.Find(x => x.SubGroupId == (short)ProductSubGroupsEnum.BBDDcv)?.Id ?? 0;

            if (listProductsByPage.Any())
            {
                var product = listProductsByPage.Find(x => x.SubGroupId == (short)ProductSubGroupsEnum.BBDDcv);

                cvDetail.CvDetailPackAndBasicModel.ProductPriceBBDDcv = product.Promotions.Any() ? product.Promotions.FirstOrDefault().LiteralPriceUnity : product.LiteralShowUnit;
            }
        }

        private string GetUrlCreateConversation(bool hasNewFreemiumChat, CompanyProductEntity offerProduct)
        {
            return offerProduct.GroupId == (short)ProductGroupsEnum.Membership
                || offerProduct.GroupId != (short)ProductGroupsEnum.Membership && !hasNewFreemiumChat
                ? Url.Action("OpenOrCreateConverAsync", "CompanyMessages")
                : Url.Action("OpenOrCreateConverAsync", "CompanyConversations");
        }

        private string GetUrlInsertMessage(bool hasNewFreemiumChat, CompanyProductEntity offerProduct)
        {
            return offerProduct.GroupId == (short)ProductGroupsEnum.Membership
                || offerProduct.GroupId != (short)ProductGroupsEnum.Membership && !hasNewFreemiumChat
                ? Url.Action("InsertMessage", "CompanyMessages")
                : Url.Action("InsertMessage", "CompanyConversations");
        }

        private string GetUrlIndexConversations(bool hasNewFreemiumChat, CompanyProductEntity offerProduct)
        {
            return offerProduct.GroupId == (short)ProductGroupsEnum.Membership
                || offerProduct.GroupId != (short)ProductGroupsEnum.Membership && !hasNewFreemiumChat
                ? Url.Action("Index", "CompanyMessages")
                : Url.Action("Index", "CompanyConversations");
        }

        private bool HasPermissionsPremiumToShow(bool isPremiumCandidate, bool hasFeaturePremium, bool hasFreeFeaturePremiumInPortal)
        {
            return ((isPremiumCandidate && hasFeaturePremium) || (hasFreeFeaturePremiumInPortal && hasFeaturePremium));
        }

        private void GenerateWhatsAppData(CandidateReadDataModel candidate, OfferEntity offer, CvDetailDataModel cvDetail, CompanyCredentials companyCredentials)
        {

            if (candidate.Candidate.IdPhoneType1 == (int)TelephoneTypeEnum.CELULAR)
            {
                cvDetail.WhatsappMessage = GetMessageWhatsApp(offer, candidate, cvDetail, companyCredentials);
                cvDetail.WhatsappPhone = GetPhoneWhatsApp(candidate.Candidate.Phone1, candidate.Candidate.IdCountry);
            }

            if (candidate.Candidate.IdPhoneType2 == (int)TelephoneTypeEnum.CELULAR)
            {
                cvDetail.WhatsappMessage2 = GetMessageWhatsApp(offer, candidate, cvDetail, companyCredentials);
                cvDetail.WhatsappPhone2 = GetPhoneWhatsApp(candidate.Candidate.Phone2, candidate.Candidate.IdCountry);
            }

        }

        private bool HasNewFreemiumChat(PortalConfig portalConfig, int idCompany)
        {
            return portalConfig.AEPortalConfig.EnableProgressiveChatFreemium
                && CompanyProductService.HasActiveFeatureByProduct(new SearchCompanyProductsByCompanyDTO(idCompany, ProductAmbitEnum.ChatContactFreemium, portalConfig.PortalId, ProductEnum.FreemiumService));
        }

        private string GetPhoneWhatsApp(string phone, int country)
        {
            string prefix = _geolocationService.GetCountryPrefix(country).ToString();

            if (!string.IsNullOrEmpty(phone))
            {
                if (phone.Length < prefix.Length || phone.Substring(0, prefix.Length) != prefix)
                {
                    phone = prefix + "-" + phone;
                }

                phone = phone.Insert(phone.Substring(0, prefix.Length).Length, GetCountryCodeForWatsApp(country));

                return phone.Replace("-", string.Empty).Replace("+", string.Empty);
            }
            return string.Empty;
        }

        private string GetCountryCodeForWatsApp(int country)
        {
            switch (country)
            {
                case (short)CountryEnum.Mexico:
                    return EXTRA_NUM_WHATSAPP_MX;
                case (short)CountryEnum.Argentina:
                    return EXTRA_NUM_WHATSAPP_AR;
                default:
                    return "";
            }
        }

        private string GetMessageWhatsApp(OfferEntity offer, CandidateReadDataModel candidate, CvDetailDataModel cvDetail, CompanyCredentials companyCredentials)
        {
            string messageWhatsApp;
            if (cvDetail.IsMatch)
                messageWhatsApp = string.Format(PageLiteralsHelper.GetLiteral("LIT_WHATSAPP_DEFAULT", PageId, _portalConfig), candidate.Candidate.Name, CompanyHelper.GetComercialName(companyCredentials), offer.title);
            else
                messageWhatsApp = string.Format(PageLiteralsHelper.GetLiteral("LIT_WHATSAPP_CVSEARCH", PageId, _portalConfig), candidate.Candidate.Name, CompanyHelper.GetComercialName(companyCredentials));
            return messageWhatsApp;
        }

        private void VisibilityDetailCv(OfferEntity offer, CvDetailDataModel cvDetail, CompanyProductEntity referenceProduct, CompanyCredentials companyCredentials, PortalConfig portalConfig)
        {
            var cvFeature = referenceProduct.Features.FirstOrDefault(feat => feat.AmbitId == (int)ProductAmbitEnum.CvBBDD);

            if (cvFeature != null && (cvFeature.AvailableUnits > 0 || referenceProduct.GroupId == (int)ProductGroupsEnum.Membership))
            {
                if (referenceProduct.GroupId == (int)ProductGroupsEnum.Packs)
                {
                    var avaiablesUnits = cvFeature.IsUnlimited ? int.MaxValue : cvFeature.InitialUnits - GetCvsByOffer(offer.idofferCT);
                    cvDetail.TotalAllowedCvVisualization = avaiablesUnits >= 0 ? avaiablesUnits : 0;
                }
                else if (referenceProduct.GroupId == (int)ProductGroupsEnum.Membership)
                {
                    cvDetail.TotalAllowedCvVisualization = CompanyProductService.GetTotalAllowedCvVisualization(referenceProduct, portalConfig, companyCredentials);
                    if (cvFeature.IsUnlimited) cvDetail.IsViewed = true;
                }
                else if (referenceProduct.SubGroupId == (short)ProductSubGroupsEnum.BBDDcv)
                {
                    var BBDDcvProducts = CompanyProductService.GetBBDDsProductsEntitiy(companyCredentials.IdCompany, companyCredentials.PortalId);
                    cvDetail.TotalAllowedCvVisualization = BBDDcvProducts.AvailableUnits;
                    cvDetail.InitialBBDDCvVisualization = BBDDcvProducts.InitialUnits;
                }
                else
                {
                    cvDetail.TotalAllowedCvVisualization = CompanyProductService.GetTotalAllowedCvVisualization(referenceProduct, portalConfig, companyCredentials);
                }
            }
            else if (portalConfig.AEPortalConfig.UseBBDDcvProductForOffers && referenceProduct.GroupId != (short)ProductGroupsEnum.Freemium)
            {
                var BBDDcvProducts = CompanyProductService.GetBBDDsProductsEntitiy(companyCredentials.IdCompany, companyCredentials.PortalId);
                cvDetail.TotalAllowedCvVisualization = BBDDcvProducts.AvailableUnits;
                cvDetail.InitialBBDDCvVisualization = BBDDcvProducts.InitialUnits;
            }
        }

        private void CvIsBlock(CandidateReadDataModel candidate)
        {
            if (candidate.User.Email.Contains("@"))
            {
                string beforeat = candidate.User.Email.Split('@')[0];
                candidate.User.Email = string.Concat(
                    new string('*', beforeat.Length),
                    '@',
                    candidate.User.Email.Split('@')[1]);
            }
            if (!string.IsNullOrEmpty(candidate.Candidate.Phone1))
            {
                if (candidate.Candidate.Phone1.Length < 3)
                    candidate.Candidate.Phone1 = "***";
                else
                    candidate.Candidate.Phone1 = $"{candidate.Candidate.Phone1.Substring(0, 3)} {new string('*', candidate.Candidate.Phone1.Length - 3)}";
            }
            if (!string.IsNullOrEmpty(candidate.Candidate.Name))
            {
                candidate.Candidate.Name = new string('*', candidate.Candidate.Name.Length);
            }
            if (!string.IsNullOrEmpty(candidate.Candidate.Surname))
            {
                candidate.Candidate.Surname = new string('*', candidate.Candidate.Surname.Length);
            }
        }

        [Route("BBDDPrint")]
        public ActionResult Print(string oi, string ids, string cfo, string k)
        {
            try
            {
                var companyCredentials = SecurityHelper.GetCompanyCredentials();
                var matchesToReIndex = new List<long>();
                _portalConfig = PortalConfigurationService.GetPortalConfiguration();

                var kpiPrint = KpiEnum.COMPANY_PRINT_CV_DETAIL;

                Int32.TryParse(_encryptionService.Decrypt(oi), out var idOffer);

                var companyProduct = CompanyProductService.GetByIdCompany(companyCredentials.IdCompany, companyCredentials.PortalId, OfferIntegratorEnum.CompuTrabajo);

                var commentsFeature = companyProduct.Features.Any(n => n.AmbitId == (short)ProductAmbitEnum.CommentsCV);
                var ratingsFeature = companyProduct.Features.Any(n => n.AmbitId == (short)ProductAmbitEnum.RatingsCvs);
                var ofertaFlashFeature = companyProduct.Features.Any(n => n.AmbitId == (short)ProductAmbitEnum.OfferFlash);
                var competencesTestFeature = companyProduct.Features.Any(n => n.AmbitId == (short)ProductAmbitEnum.CompetencesTest);
                var talentViewFeature = companyProduct.Features.Any(n => n.AmbitId == (short)ProductAmbitEnum.TalentView3D);

                if (idOffer == 0) return View("Print", new CvDetailDataModel());
                LoadDictionaries(_portalConfig);

                if (!IsMy(companyCredentials, idOffer, _portalConfig))
                    return new HttpStatusCodeResult(HttpStatusCode.Forbidden, "BadOffer");

                var offer = OfferService.GetByPk(idOffer, _portalConfig.PortalId);
                var offerProduct = CompanyProductService.GetByCompanyProductId(offer.Integrations.FirstOrDefault()?.idcompanyproduct ?? 0, offer.idportal, offer.idcompany);
                var matchList = BuildListMatches(ids, offer.idofferCT);

                if (ids.Contains('|'))
                    kpiPrint = KpiEnum.COMPANY_PRINT_CV_LIST;

                var cvDetails = new CvDetailDataModel
                {
                    IsBasicOffer = offerProduct.GroupId == (short)ProductGroupsEnum.Freemium,
                    HasCompanyOffertFlashFeature = ofertaFlashFeature,
                    HasFoldersDetail = false,
                    HasCompanyCommentsFeature = commentsFeature,
                    HasCompanyRatingsFeature = ratingsFeature,
                    HasCompanyTestCompetencesFeature = competencesTestFeature,
                    HasTalentView3dFeature = talentViewFeature
                };

                foreach (var match in matchList)
                {
                    CandidateReadDataModel candidate = Mapper.Map<CandidateReadEntity, CandidateReadDataModel>(_candidateService.GetCandidateRead(match.CurriculumId, _portalConfig.PortalId, match.CandidateId, companyCredentials.IdCompany));

                    var hasHistoryOffersFeature = offerProduct.Features.Any(n => n.AmbitId == (short)ProductAmbitEnum.HistoryOffer);
                    FillAditionalInfoCandidate(candidate, match, companyCredentials, idOffer, offer.has_killer_questions == 1, hasHistoryOffersFeature, offerProduct, _portalConfig);

                    cvDetails.Candidates.Add(candidate);

                    if (!match.IsSeen
                        && _matchService.UpdateShowedMatch(match))
                    {
                        matchesToReIndex.Add(match.Id);
                    }
                }

                _matchService.IndexMatchesByIds(matchesToReIndex, _portalConfig.PortalId);

                ActionKpiCount((short)kpiPrint, (short)matchList.Count(), companyCredentials);

                return View("Print", cvDetails);
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyController Print {ex}");
                ExceptionPublisherService.Publish(ex, "CompanyController", "Print");
                return RedirectToAction("Index", "Home");
            }
        }

        private bool IsMy(CompanyCredentials companyCredentials, int idOffer, PortalConfig portalConfig)
        {
            return _securityOfferService.IsMy(new IsMyOfferDTO(companyCredentials.UserRole, companyCredentials.IdCompany, companyCredentials.PortalId, (int)companyCredentials.UserId, idOffer, portalConfig.EnabledNewIsMy));
        }

        private void LoadDictionaries(PortalConfig portalConfig)
        {
            CountryDic = DictionaryService.GetDictionary(DictionaryEnum.COUNTRY, portalConfig.PortalId);
            LocalizationDic = DictionaryService.GetDictionary(DictionaryEnum.LOCALIZATION_BY_COUNTRY, portalConfig.countryId, portalConfig.PortalId);
            MaritalStatusDic = DictionaryService.GetDictionary(DictionaryEnum.MARITAL_STATUS, portalConfig.PortalId);
            EmploymentStatusDic = DictionaryService.GetDictionary(DictionaryEnum.EMPLOYMENT_STATUS, portalConfig.PortalId);
            LegalSituationDic = DictionaryService.GetDictionary(DictionaryEnum.LEGAL_SITUATION, portalConfig.PortalId);
            CategoryDic = DictionaryService.GetDictionary(DictionaryEnum.CATEGORY, portalConfig.PortalId);
            IndustryDic = DictionaryService.GetDictionary(DictionaryEnum.INDUSTRY, portalConfig.PortalId);
            LanguageDic = DictionaryService.GetDictionary(DictionaryEnum.LANGUAGE, portalConfig.PortalId);
            LanguageLevelDic = DictionaryService.GetDictionary(DictionaryEnum.LANGUAGE_LEVEL, portalConfig.PortalId);
            MonthDic = DictionaryService.GetDictionary(DictionaryEnum.MONTHS, portalConfig.PortalId);
            IdentificationTypeDic = DictionaryService.GetDictionary(DictionaryEnum.IDENTIFICATION_TYPE, portalConfig.PortalId);
            SkillTypeDic = DictionaryService.GetDictionary(DictionaryEnum.SKILL_TYPE, portalConfig.PortalId);
        }

        private void FillAditionalInfoCandidate(CandidateReadDataModel candidate, MatchEntity match, CompanyCredentials companyCredentials, int offerId, bool offerHasKQ, bool historyOffersFeature, CompanyProductEntity offerProduct, PortalConfig _portalConfig)
        {

            CandidateDetailsByCv(candidate, match);

            SkillsByCv(candidate, _portalConfig);
            LanguagesByCv(candidate);
            FormationsByCv(candidate, companyCredentials.PortalId);
            ExperiencesByCv(candidate);
            RatingsByCv(candidate, companyCredentials);
            CommentsByCv(candidate, companyCredentials);
            CompetencesByCv(candidate, offerId, offerProduct);

            if (match.Id <= 0) return;
            if (offerHasKQ)
            {
                KillerQuestionsByOffer(candidate, match, offerId, offerHasKQ);
            }
            if (historyOffersFeature)
            {
                HistoryOffers(candidate, match, companyCredentials);
            }

            candidate.Candidate.HasCodePresentationVideo = !string.IsNullOrEmpty(candidate.Candidate.CodePresentationVideo);
        }



        private void HistoryOffers(CandidateReadDataModel candidate, MatchEntity match, CompanyCredentials companyCredentials)
        {
            var listOfferHistory = Mapper.Map<List<OfferCandidateEntity>, List<OfferCandidateDataModel>>(OfferService.GetOfferCandidateHistory(match.CandidateId, match.JobOfferId, companyCredentials.IdCompany));

            foreach (var offer in listOfferHistory)
            {
                offer.TextOfferDate = GetFormatDate(offer.PublicationOfferDate);
                offer.TextCandidateDate = GetFormatDate(offer.CandidateProcessDate);
            }

            candidate.HistoryOffers = listOfferHistory.OrderByDescending(n => n.CandidateProcessDate).ToList();
        }

        private string GetFormatDate(DateTime dateTime)
        {
            string month = CultureInfo.CurrentCulture.DateTimeFormat.GetMonthName(dateTime.Month);
            return $"{dateTime.Day} {month} {dateTime.Year}";
        }

        private void KillerQuestionsByOffer(CandidateReadDataModel candidate, MatchEntity match, int offerId, bool offerHasKQ)
        {
            candidate.HasKillerQuestions = offerHasKQ;

            if (!candidate.HasKillerQuestions) return;

            foreach (var kq in _killerquestionService.GetKillerQuestionDataByIdCandidateIdOffer(match.CandidateId, offerId, _portalConfig.PortalId))
            {
                candidate.KillerQuestions.Add(new KillerQuestionDataModel()
                {
                    Title = kq.Title,
                    Answer = kq.Answer,
                    Score = kq.Score,
                    ScoreMax = kq.ScoreMax,
                    Type = kq.IdKillerQuestionType,
                    TypeDesc = DictionaryService.GetDictionaryValue(DictionaryEnum.KILLER_QUESTIONS_TYPE, kq.IdKillerQuestionType.ToString(), _portalConfig.PortalId)
                });
            }
        }

        private void CandidateDetailsByCv(CandidateReadDataModel candidate, MatchEntity match)
        {
            candidate.UserLastLoginCultureInfo = candidate.User.LastLoginOn.ToString("D", new CultureInfo(_portalConfig.cultura)).Substring(candidate.User.LastLoginOn.ToString("D", new CultureInfo(_portalConfig.cultura)).IndexOf(",") + 1);
            candidate.CandidateUpdatedOnCultureInfo = candidate.Candidate.UpdatedOn.ToString("D", new CultureInfo(_portalConfig.cultura)).Substring(candidate.Candidate.UpdatedOn.ToString("D", new CultureInfo(_portalConfig.cultura)).IndexOf(",") + 1);
            candidate.UserCreatedOnCultureInfo = candidate.User.CreatedOn.ToString("D", new CultureInfo(_portalConfig.cultura)).Substring(candidate.User.CreatedOn.ToString("D", new CultureInfo(_portalConfig.cultura)).IndexOf(",") + 1);
            candidate.CanShowNit = (_portalConfig.nit_show_in_details_cv && candidate.Candidate.Nit != string.Empty);
            candidate.IdentificationType = (candidate.CanShowNit ? GetSafetyValueFromDictionary(candidate.Candidate.IdIdentificationType.ToString(), IdentificationTypeDic, "value") : string.Empty);
            candidate.CanShowRace = (candidate.Candidate.IdRace != 0);
            //candidate.CandidateRaceString = GetSafetyValueFromDictionary(candidate.Candidate.IdRace.ToString(), RaceDic, "Value");
            candidate.ProvinceString = GetSafetyValueFromDictionary(candidate.Candidate.IdLocalization.ToString(), LocalizationDic, "Value");

            var LocKey = GetSafetyValueFromDictionary(candidate.Candidate.IdLocalization.ToString(), LocalizationDic, "key");
            int.TryParse(LocKey, out int cityByLoc);
            var CityDic = DictionaryService.GetDictionary(DictionaryEnum.CITIES_BY_LOCALIZATION, cityByLoc, _portalConfig.PortalId);

            candidate.CityString = GetSafetyValueFromDictionary(candidate.Candidate.IdCity.ToString(), CityDic, "value");

            candidate.NationlaityString = GetSafetyValueFromDictionary(candidate.Candidate.Nationality.ToString(), CountryDic, "value");
            candidate.CivilianStatus = GetSafetyValueFromDictionary(candidate.Candidate.IdMaritalStatus.ToString(), MaritalStatusDic, "value");

            candidate.ActualWork = GetSafetyValueFromDictionary(candidate.Candidate.IdemploymentStatus.ToString(), EmploymentStatusDic, "value");
            candidate.HasDriverLicense = candidate.Candidate.DriveLicence != -1;
            candidate.DriveLicense = candidate.Candidate.DriveLicence == 0 ? false : true;
            candidate.HasVehicles = candidate.Candidate.Car != -1;
            candidate.Vehicles = candidate.Candidate.Car == 0 ? false : true;
            candidate.HasTravel = candidate.Candidate.Travel != -1;
            candidate.Travel = candidate.Candidate.Travel == 0 ? false : true;
            candidate.HasChangeResidence = candidate.Candidate.ResidenceChange != -1;
            candidate.ChangeResidence = candidate.Candidate.ResidenceChange == 0 ? false : true;
            candidate.IsYourNationality = _portalConfig.countryId == candidate.Candidate.IdCountry;
            candidate.LegalSituationString = _portalConfig.countryId != candidate.Candidate.Nationality ? GetSafetyValueFromDictionary(candidate.Candidate.IdLegalSituation.ToString(), LegalSituationDic, "value") : string.Empty;
            candidate.ShowMinSalary = _portalConfig.CurrencyPortal != null;
            candidate.Salary = buildSalary(candidate.Candidate.MinimumSalary);
            candidate.IsFromOfferFlash = match.OfferFlashMatch == 1;
            candidate.IsAutoExcluding = match.AutoFilterStatus == 1;

        }

        private string GetSafetyValueFromDictionary(string keyFilter, Dictionary<string, string> dictionary, string getValue)
        {
            if (dictionary != null && dictionary.Any())
                return getValue.ToLower() == "key"
                    ? dictionary.ContainsKey(keyFilter) ? dictionary.FirstOrDefault(n => n.Key == keyFilter).Key : string.Empty
                    : dictionary.ContainsKey(keyFilter) ? dictionary.FirstOrDefault(n => n.Key == keyFilter).Value : string.Empty;

            return string.Empty;
        }

        private void SkillsByCv(CandidateReadDataModel candidate, PortalConfig _portalConfig)
        {
            if (!candidate.Cv.SkillsByCv.Any()) return;

            var literalMap = new Dictionary<int, string>
            {
                { (int)CvsSkillsGroupEnum.Other, "LIT_SKILL_GROUP_OTHER" },
                { (int)CvsSkillsGroupEnum.Soft, "LIT_SKILL_GROUP_INTERPERSONAL_SKILLS" },
                { (int)CvsSkillsGroupEnum.Hard, "LIT_SKILL_GROUP_TECHNICAL_KNOWLEDGE" }
            };

            foreach (var skill in candidate.Cv.SkillsByCv)
            {
                string literalKey;
                if (!literalMap.TryGetValue(skill.IdSkillType, out literalKey))
                {
                    literalKey = "LIT_SKILL_GROUP_OTHER";
                }

                skill.SkillTypeDescription = PageLiteralsHelper.GetLiteral(literalKey, (int)PageEnum.DetailCVCompany, _portalConfig);
            }
        }

        private void LanguagesByCv(CandidateReadDataModel candidate)
        {
            if (!candidate.Cv.LanguagesByCv.Any()) return;
            foreach (var language in candidate.Cv.LanguagesByCv)
            {
                language.Lenguage = GetSafetyValueFromDictionary(language.IdIdiom.ToString(), LanguageDic, "value");
                language.LenguageLevel = GetSafetyValueFromDictionary(language.IdIdiomLevel.ToString(), LanguageLevelDic, "value");
            }
        }

        private void CompetencesByCv(CandidateReadDataModel candidate, int offerId, CompanyProductEntity offerProduct)
        {
            var talentViewFeature = offerProduct.Features.Any(n => n.AmbitId == (short)ProductAmbitEnum.TalentView3D);
            var competenceTest = offerProduct.Features.Any(n => n.AmbitId == (short)ProductAmbitEnum.CompetencesTest);
            if ((talentViewFeature || competenceTest) && candidate.Candidate.TestCompetencesTalentViewVisible)
            {
                if (talentViewFeature && candidate.Candidate.TalentView3D)
                {
                    var quizRelation = _quizConsumerService.GetCompetenceTest(0, candidate.Candidate.IdCandidate, (short)QuizObjectTypeEnum.TalentView3D, (short)QuizDefinitionTypeEnum.TalentView3D, 3, _portalConfig.PortalId, (short)OriginRequest.CompanyCT);
                    BuildTalentView3DList(quizRelation, candidate.Cv);
                    candidate.HasTalentView3D = true;
                }
                else if (competenceTest && candidate.Candidate.TestCompetences)
                {
                    var config = new CompanyConfiguration();
                    var quizRelation = _quizConsumerService.GetCompetenceTest(0, candidate.Candidate.IdCandidate, (short)QuizObjectTypeEnum.Master, (short)QuizDefinitionTypeEnum.CompetenceTest, Convert.ToInt16(config.IdBusiness), _portalConfig.PortalId, (short)OriginRequest.None);
                    BuildCompetenceList(quizRelation, candidate.Cv.CompetenceList, offerId, candidate.Candidate.IdCandidate, _portalConfig.PortalId);
                }

                candidate.HasTestCompetences = candidate.Cv.CompetenceList.Any();
            }
        }

        private void BuildTalentView3DList(QuizRelationDTO quizRelation, CvDataModel cvData)
        {
            if (quizRelation.QuizAnswers.Any())
            {
                BuildTalentView3DSubList(quizRelation, cvData.CompetenceList, COMPETENCES);
                BuildTalentView3DSubList(quizRelation, cvData.ValuesList, VALUES);
                BuildTalentView3DSubList(quizRelation, cvData.PersonalityList, PERSONALITY);
            }
        }

        private void BuildTalentView3DSubList(QuizRelationDTO quizRelation, List<CompetenceDataModel> competenceList, string groupName)
        {
            var group = quizRelation.QuizDefinition.ListQuestionGroups.Find(x => x.Title == groupName);

            if (group != null)
            {
                FillCandidateTalentView3D(quizRelation, competenceList, group.ListQuestionGroups);
            }
        }

        private static void FillCandidateTalentView3D(QuizRelationDTO quizRelation, List<CompetenceDataModel> competenceList, List<QuestionGroupsDTO> competences)
        {
            var cont = 1;
            foreach (var subgroup in competences)
            {
                var answerByRule = quizRelation.QuizAnswers[0].QuizAnswersGroups.FirstOrDefault(rule => rule.RuleKey == subgroup.QuestionGroupsScoreRules.RuleKey);

                if (answerByRule != null && int.TryParse(answerByRule.Score.ToString("0"), out int scoreInt))
                {
                    var competenceDataModel = new CompetenceDataModel
                    {
                        Id = cont,
                        Key = answerByRule.RuleKey,
                        Description = answerByRule.Note,
                        Title = quizRelation.QuizDefinition.ListQuestionGroups
                                    .SelectMany(group => group.ListQuestionGroups)
                                    .FirstOrDefault(c => c.QuestionGroupsScoreRules.RuleKey == answerByRule.RuleKey)?.Title,
                        Result = scoreInt
                    };

                    if (!competenceList.Any(x => x.Key == competenceDataModel.Key))
                    {
                        competenceList.Add(competenceDataModel);
                        cont++;
                    }
                }
            }
        }

        private void BuildCompetenceList(QuizRelationDTO quizRelation, List<CompetenceDataModel> competenceList, int offerId, int candidateId, short portalId)
        {
            if (quizRelation.QuizAnswers.Any())
            {
                var quizAnswersGroups = quizRelation.QuizAnswers.FirstOrDefault().QuizAnswersGroups;
                if (quizAnswersGroups.Count() == 8)
                {
                    var cont = 1;
                    quizAnswersGroups.ForEach(qa =>
                    {
                        competenceList.Add(new CompetenceDataModel()
                        {
                            Id = cont,
                            Key = quizRelation.QuizDefinition.ListQuestionGroups.FirstOrDefault(c => c.QuestionGroupsScoreRules.RuleKey == qa.RuleKey)?.QuestionGroupsScoreRules.RuleKey,
                            Description = qa.Note,
                            Title = quizRelation.QuizDefinition.ListQuestionGroups.FirstOrDefault(c => c.QuestionGroupsScoreRules.RuleKey == qa.RuleKey)?.Title,
                            Result = qa.Score.ToString("0").ToInt()
                        });
                        cont++;
                    });
                }
                else
                {
                    var extradata = new Dictionary<string, string>();
                    extradata.Add("UrlReferrer", Request.UrlReferrer != null ? Request.UrlReferrer.ToString() : string.Empty);
                    ExceptionPublisherService.PublishWarning(new Exception("QuizAnswersGroups are less than 8 - offerId:" + offerId + "- candidateId:" + candidateId + "- portalId:" + portalId), "CompanyBBDDCvDetailController", extradata);
                }
            }
        }

        private void CommentsByCv(CandidateReadDataModel candidate, CompanyCredentials companyCredentials)
        {
            candidate.ListComments = Mapper.Map<List<CommentCvEntity>, List<CommentCvDataModel>>(_commentCvService
                    .GetCommentsCv(companyCredentials.IdCompany, candidate.Candidate.IdCandidate, candidate.Cv.IdCv, _portalConfig.PortalId));
            candidate.HasComments = candidate.ListComments.Any();
            foreach (var c in candidate.ListComments)
            {
                c.TextDate = GetTextDate(c.CreatedOn);
                c.CanRemoving = c.UserId == companyCredentials.UserId || SecurityHelper.IsActionPemitedByRoleAndIsNotAdvisorRole(SecurityActionEnum.CompanyCvsCommentsRemoveOtherUsers);
            }
        }

        private void RatingsByCv(CandidateReadDataModel candidate, CompanyCredentials companyCredentials)
        {
            candidate.ListRating = Mapper.Map<List<RatingCvEntity>, List<RatingCvDataModel>>(_ratingCvService
                .GetRatingsCvsByCompanyAndCandidate(candidate.Candidate.IdCandidate, companyCredentials.IdCompany, _portalConfig.PortalId));
            candidate.HasRatings = candidate.ListRating.Any();
            
            if (!candidate.HasRatings) return;

            candidate.RatingTop = Mapper.Map<RatingCvEntity, RatingCvDataModel>(_ratingCvService
                .GetHighRatingByCandidate(candidate.Candidate.IdCandidate, companyCredentials.IdCompany));
            foreach (var c in candidate.ListRating)
            {
                c.TextDate = GetTextDate(c.CreatedOn);
                c.CanRemoving = c.UserId == companyCredentials.UserId || SecurityHelper.IsActionPemitedByRoleAndIsNotAdvisorRole(SecurityActionEnum.CompanyCvsRatingsRemoveOtherUsers);
            }
        }

        private string GetTextDate(DateTime createdOn)
        {
            var result = new StringBuilder();
            var t = _portalConfig.CurrentDateTimePortal - createdOn;

            if (t.Days > 31)
            {
                result.Append(PageLiteralsHelper.GetLiteral("LIT_MAS_1_MES", PageId, _portalConfig));
            }
            else
            {
                if (t.Days < 1 && t.Minutes <= 1)
                {
                    result.Clear();
                    result.Append(PageLiteralsHelper.GetLiteral("LIT_NOW", PageId, _portalConfig));
                }

                if (t.Days == 0 && t.Hours < 1 && t.Minutes > 2)
                {
                    result.Clear();
                    result.Append(PageLiteralsHelper.GetLiteral("LIT_HACE", PageId, _portalConfig));
                    result.Append(Math.Truncate(t.TotalMinutes).ToString());
                    result.Append($" {PageLiteralsHelper.GetLiteral("LIT_MINUTES", PageId, _portalConfig)}");
                }
                if (t.Days < 1 && t.Hours >= 1)
                {
                    result.Clear();
                    result.Append(PageLiteralsHelper.GetLiteral("LIT_HACE", PageId, _portalConfig));
                    result.Append(Math.Truncate(t.TotalHours).ToString());
                    result.Append($" {PageLiteralsHelper.GetLiteral("LIT_HOURS", PageId, _portalConfig)}");
                }
                if (t.Days == 1)
                {
                    result.Clear();
                    result.Append(PageLiteralsHelper.GetLiteral("LIT_AYER", PageId, _portalConfig));
                }
                if (t.Days > 1 && t.Days < 31)
                {
                    result.Clear();
                    result.Append(PageLiteralsHelper.GetLiteral("LIT_HACE", PageId, _portalConfig));
                    result.Append(Math.Truncate(t.TotalDays).ToString());
                    result.Append($" {PageLiteralsHelper.GetLiteral("LIT_DIAS", PageId, _portalConfig)}");
                }
            }
            return result.ToString();
        }

        private void FormationsByCv(CandidateReadDataModel candidate, short portalId)
        {
            if (!candidate.Cv.FormationsByCv.Any())
            {
                return;
            }

            candidate.HasFormation = true;
            Dictionary<string, string> studyDic = new Dictionary<string, string>();
            Dictionary<string, string> studyStatusDic = new Dictionary<string, string>();
            Dictionary<string, string> specializationDic = new Dictionary<string, string>();

            studyDic = DictionaryService.GetDictionary(DictionaryEnum.EDUCATION_LEVEL, portalId);
            studyStatusDic = DictionaryService.GetDictionary(DictionaryEnum.STUDY_STATUS, portalId);

            foreach (var formation in candidate.Cv.FormationsByCv)
            {
                formation.Study = this.GetStudyFromDictionary(formation.IdlevelStudy, studyDic);
                formation.StudyLevel = this.GetStudyLevelFromDictionary(formation.IdStudyStatus, studyStatusDic);
                specializationDic = DictionaryService.GetDictionary(DictionaryEnum.SPECIALIZATION, formation.IdlevelStudy, portalId);
                formation.IdSpecializationstr = this.GetSpecialization(formation.IdSpecialization, specializationDic);
                formation.InitDateString =
                    $"{GetSafetyValueFromDictionary(Convert.ToDateTime(formation.InitDate).Month.ToString(), MonthDic, "value") + " " + PageLiteralsHelper.GetLiteral("LIT_DE", PageId, _portalConfig) + " " + formation.InitDate.Year}";
                formation.EndDateString = formation.EndDate == DateTime.MinValue
                    ? PageLiteralsHelper.GetLiteral("LIT_INICIADO_EN", (int)PageEnum.PrinctCVCompany, _portalConfig)
                    : $"{GetSafetyValueFromDictionary(Convert.ToDateTime(formation.EndDate).Month.ToString(), MonthDic, "value") + " " + PageLiteralsHelper.GetLiteral("LIT_DE", PageId, _portalConfig) + " " + formation.EndDate.Year}";
            }
        }

        private string GetStudyFromDictionary(int keyToGet, Dictionary<string, string> studyDic)
        {
            if (!studyDic.ContainsKey(keyToGet.ToString()))
            {
                return "";
            }

            return studyDic[keyToGet.ToString()];
        }

        private string GetStudyLevelFromDictionary(int keyToGet, Dictionary<string, string> studyStatusDic)
        {
            if (!studyStatusDic.ContainsKey(keyToGet.ToString()))
            {
                return "";
            }

            return studyStatusDic[keyToGet.ToString()];
        }

        private string GetSpecialization(int specializationId, Dictionary<string, string> specializationDic)
        {
            if (specializationId == 0)
            {
                return "";
            }

            if (!specializationDic.ContainsKey(specializationId.ToString()))
            {
                return "";
            }

            return specializationDic[specializationId.ToString()];
        }

        private void ExperiencesByCv(CandidateReadDataModel candidate)
        {
            if (!candidate.Cv.ExperiencesByCv.Any()) return;
            candidate.HasExperiences = true;

            foreach (var experience in candidate.Cv.ExperiencesByCv)
            {
                experience.Area = GetSafetyValueFromDictionary(experience.IdArea.ToString(), CategoryDic, "value");
                experience.Category = GetSafetyValueFromDictionary(experience.IdCategory.ToString(), IndustryDic, "value");
                experience.AñoInicioStrint = experience.FechaInicio.Year.ToString();
                experience.MesInicioString = GetSafetyValueFromDictionary(experience.FechaInicio.Month.ToString(), MonthDic, "value");
                experience.FechaFin = experience.FechaFin == DateTime.MinValue ? DateTime.Now : experience.FechaFin;
                if (experience.FechaFin < DateTime.Now)
                {
                    experience.AñoFinString = experience.FechaFin.Year.ToString();
                    experience.MesFinString = GetSafetyValueFromDictionary(experience.FechaFin.Month.ToString(), MonthDic, "value");
                }
                else
                    experience.IsActual = true;

                experience.DiferenciaDeFechas = _dateTimeToolsService.CompareDates(experience.FechaInicio, experience.FechaFin);
            }
        }

        private string buildSalary(int minimumSalary)
        {
            if (_portalConfig.CurrencyPortal == null)
                return string.Empty;

            return _portalConfig.CurrencyPortal.PositionCurrency == 0
                        ? $"{_portalConfig.CurrencyPortal.Currency} {minimumSalary.ToString("N2")}"
                        : $"{minimumSalary.ToString("N2")} {_portalConfig.CurrencyPortal.Currency}";
        }

        private List<MatchEntity> BuildListMatches(string ids, int offerId)
        {
            char[] delimiterChars = { '|' };
            var matchEncryptedList = ids.Split(delimiterChars);
            var matches = new List<MatchEntity>();

            if (!matchEncryptedList.Any()) return matches;
            foreach (var match in matchEncryptedList)
            {
                long.TryParse(_encryptionService.Decrypt(match), out var id);
                if (id <= 0) continue;
                var matchEnt = _matchService.GetByPk(id);
                if (matchEnt.JobOfferId == offerId)
                    matches.Add(matchEnt);
            }

            return matches;
        }

        private void LoadDataFolders(CvDetailDataModel cvDetail, OfferEntity offer, CompanyProductEntity referenceProduct, bool customFolders, CompanyCredentials companyCredentials, string cf)
        {
            var currentFolder = (int)CompanyOfferFolderEnum.Recibidos;
            if (!string.IsNullOrEmpty(cf))
                Int32.TryParse(_encryptionService.Decrypt(cf), out currentFolder);

            cvDetail.FolderSelected = currentFolder;
            cvDetail.FolderSelectedEncrypted = _encryptionService.Encrypt(currentFolder.ToString());

            var listFolderDm = new List<FoldersDataModel>();

            InitialiteFolders(listFolderDm, referenceProduct, customFolders, offer, companyCredentials);
            cvDetail.Folders = listFolderDm;
        }

        private CandidateReadDataModel LoadDataCv(int idcv, int candidateId, CompanyCredentials companyCredentials, CompanyProductEntity offerProduct)
        {
            CandidateReadDataModel candidate = Mapper.Map<CandidateReadEntity, CandidateReadDataModel>(_candidateService.GetCandidateRead(idcv, _portalConfig.PortalId, candidateId, companyCredentials.IdCompany));

            FillAditionalInfoCandidate(candidate, new MatchEntity(), companyCredentials, 0, false, false, offerProduct, _portalConfig);

            return candidate;
        }

        private void InitialiteFolders(List<FoldersDataModel> listFolderDm, CompanyProductEntity referenceProduct, bool customFolders, OfferEntity offer, CompanyCredentials companyCredentials)
        {
            var counterFolders = new FolderCountersEntity();

            foreach (CompanyOfferFolderEnum foo in Enum.GetValues(typeof(CompanyOfferFolderEnum)))
            {
                if ((int)foo > 0)
                {
                    listFolderDm.Add(new FoldersDataModel()
                    {
                        Position = (int)foo,
                        PositionEncrypted = _encryptionService.Encrypt(((int)foo).ToString()),
                        Count = 0,
                        LiName = $"li_{foo}",
                        AnchorName = $"link_{foo}",
                        NameFolder = GetFolderName((int)foo)
                    });
                }
            }

            if (referenceProduct.GroupId != (int)ProductGroupsEnum.Packs &&
                !(offer.idoffer > 0 && referenceProduct.SubGroupId == (short)ProductSubGroupsEnum.BBDDcv))
            {
                listFolderDm.Remove(listFolderDm.Single(r => r.Position == (int)CompanyOfferFolderEnum.BBDD));
            }


            if (customFolders)
            {
                var cf = _customFoldersService.GetCustomFolders(new CustomFoldersFilterEntity()
                {
                    Environment = (short)CustomFoldersEnvironmentEnum.Matches,
                    IdOffer = offer.idofferCT,
                    IdPortal = companyCredentials.PortalId
                });

                foreach (var folder in cf)
                {
                    if (folder.Position == (int)CompanyCustomFoldersMatchEnum.Folder1 || folder.Position == (int)CompanyCustomFoldersMatchEnum.Folder2)
                        listFolderDm.Add(new FoldersDataModel()
                        {
                            Position = folder.Position,
                            PositionEncrypted = _encryptionService.Encrypt((folder.Position).ToString()),
                            Count = 0,
                            NameFolder = folder.Name,
                            Id = folder.Id,
                            IdEncrypted = folder.IdEncrypted
                        });
                }
            }

            var facetsFolder = _matchService.GetFacetsFolderByElastic(new SearchFilterDTO()
            {
                Facets = new List<string>
                (new string[] { FacetsByMatchEnum.folderId.ToString() }),
                IdPortal = _portalConfig.PortalId,
                IdOffer = offer.idofferCT,
                IdCompany = offer.idcompany
            });

            var folderFacets = facetsFolder.ContainsKey(FacetsByMatchEnum.folderId.ToString()) ? facetsFolder[FacetsByMatchEnum.folderId.ToString()] : new List<FacetResult>();

            counterFolders = new FolderCountersEntity()
            {
                Folder1 = folderFacets.Exists(e => e.Key == ((short)CompanyOfferFolderEnum.Recibidos).ToString()) ? folderFacets.FirstOrDefault(e => e.Key == ((short)CompanyOfferFolderEnum.Recibidos).ToString()).Count : 0,
                Folder2 = folderFacets.Exists(e => e.Key == ((short)CompanyOfferFolderEnum.Seleccionados).ToString()) ? folderFacets.FirstOrDefault(e => e.Key == ((short)CompanyOfferFolderEnum.Seleccionados).ToString()).Count : 0,
                Folder3 = folderFacets.Exists(e => e.Key == ((short)CompanyOfferFolderEnum.Finalistas).ToString()) ? folderFacets.FirstOrDefault(e => e.Key == ((short)CompanyOfferFolderEnum.Finalistas).ToString()).Count : 0,
                Folder4 = folderFacets.Exists(e => e.Key == ((short)CompanyOfferFolderEnum.Descartados).ToString()) ? folderFacets.FirstOrDefault(e => e.Key == ((short)CompanyOfferFolderEnum.Descartados).ToString()).Count : 0,
                Folder5 = folderFacets.Exists(e => e.Key == ((short)CompanyCustomFoldersMatchEnum.Folder1).ToString()) ? folderFacets.FirstOrDefault(e => e.Key == ((short)CompanyCustomFoldersMatchEnum.Folder1).ToString()).Count : 0,
                Folder6 = folderFacets.Exists(e => e.Key == ((short)CompanyCustomFoldersMatchEnum.Folder2).ToString()) ? folderFacets.FirstOrDefault(e => e.Key == ((short)CompanyCustomFoldersMatchEnum.Folder2).ToString()).Count : 0,
                Folder7 = folderFacets.Exists(e => e.Key == ((short)CompanyOfferFolderEnum.Contratados).ToString()) ? folderFacets.FirstOrDefault(e => e.Key == ((short)CompanyOfferFolderEnum.Contratados).ToString()).Count : 0
            };

            foreach (var folder in listFolderDm)
            {
                switch (folder.Position)
                {
                    case 1:
                        folder.Count = counterFolders.Folder1;
                        folder.IcoFolder = "i_fold_recibido";
                        break;
                    case 2:
                        folder.Count = counterFolders.Folder2;
                        folder.IcoFolder = "icon i_fold_sel";
                        break;
                    case 3:
                        folder.Count = counterFolders.Folder3;
                        folder.IcoFolder = "i_fold_final";
                        break;
                    case 4:
                        folder.Count = counterFolders.Folder4;
                        folder.IcoFolder = "i_fold_descartado";
                        break;
                    case 6:
                        folder.Count = counterFolders.Folder5;
                        folder.IcoFolder = "i_fold_interes";
                        break;
                    case 7:
                        folder.Count = counterFolders.Folder6;
                        folder.IcoFolder = "i_fold_interes";
                        break;
                    case 8:
                        folder.Count = counterFolders.Folder7;
                        folder.IcoFolder = "i_fold_contratado";
                        break;
                }
            }
        }

        private void LoadDataFoldersBBDD(CvDetailDataModel cvDetail, CompanyCredentials companyCredentials)
        {
            int.TryParse(_maxFolders, out var maxFolder);
            cvDetail.CustomFoldersDropDownList.Add(new SelectListItem() { Text = PageLiteralsHelper.GetLiteral("LIT_CV_SEARCH", PageId, _portalConfig), Value = "0" });

            var customFolders = _customFoldersService.GetCustomFolders(new CustomFoldersFilterEntity()
            {
                Environment = (short)CustomFoldersEnvironmentEnum.CvBBDD,
                IdUser = companyCredentials.UserId,
                IdPortal = companyCredentials.PortalId,
            });

            if (customFolders.Any())
            {
                var foldersToMapper = customFolders.Count > maxFolder ? customFolders.Take(maxFolder).ToList() : customFolders;
                cvDetail.CustomFoldersBBDD = Mapper.Map<List<CustomFolderEntity>, List<FoldersCvDataModel>>(foldersToMapper);
                FillDropDownList(cvDetail);

                var idfolder = _customFoldersService.GetFolderCustomByIdCv(new CustomFoldersFilterEntity()
                {
                    IdUser = companyCredentials.UserId,
                    IdPortal = companyCredentials.PortalId,
                    IdCv = cvDetail.Candidate.Cv.IdCv,
                    IdCvs = cvDetail.Candidate.Cv.IdCv.ToString()
                });

                if (idfolder > 0)
                {
                    cvDetail.FolderSelected = idfolder;
                    cvDetail.FolderSelectedEncrypted = _encryptionService.Encrypt(idfolder.ToString());
                }
            }

            cvDetail.CanCreateNewFolder = maxFolder > customFolders.Count();
        }

        private void FillDropDownList(CvDetailDataModel cvDetail)
        {
            foreach (var folder in cvDetail.CustomFoldersBBDD)
                cvDetail.CustomFoldersDropDownList.Add(new SelectListItem() { Text = folder.NameFolder, Value = folder.IdEncrypted });
        }

        private string GetFolderName(int foo)
        {
            var name = string.Empty;
            switch (foo)
            {
                case (int)CompanyOfferFolderEnum.Recibidos:
                    name = PageLiteralsHelper.GetLiteral("LIT_INSCRITOS", PageId, _portalConfig);
                    break;
                case (int)CompanyOfferFolderEnum.Seleccionados:
                    name = PageLiteralsHelper.GetLiteral("LIT_ENPROCESO", PageId, _portalConfig);
                    break;
                case (int)CompanyOfferFolderEnum.Finalistas:
                    name = PageLiteralsHelper.GetLiteral("LIT_FINALISTAS", PageId, _portalConfig);
                    break;
                case (int)CompanyOfferFolderEnum.Descartados:
                    name = PageLiteralsHelper.GetLiteral("LIT_DESCARTADOS", PageId, _portalConfig);
                    break;
                case (int)CompanyOfferFolderEnum.BBDD:
                    name = PageLiteralsHelper.GetLiteral("LIT_BBDD_HDV", PageId, _portalConfig);
                    break;
            }
            return name;
        }

        private void FillNextPreviousFromCvs(CvDetailDataModel cvDetail, string idcv, string oi, string cf, CompanyCredentials companyCredentials,
                                             OfferEntity offer)
        {
            var homeCvsDataModel = (HomeCvsDataModel)_sessionService.Get(typeof(HomeCvsDataModel)) ?? new HomeCvsDataModel();
            var homeMatchesDataModel = new HomeMatchesDataModel();
            Int32.TryParse(_encryptionService.Decrypt(cf), out int currentFolder);

            if (currentFolder == (int)CompanyOfferFolderEnum.BBDD && !string.IsNullOrEmpty(cf))
            {
                homeMatchesDataModel = (HomeMatchesDataModel)_sessionService.Get(typeof(HomeMatchesDataModel)) ?? new HomeMatchesDataModel();
                homeCvsDataModel.MultifiltersDataModel = homeMatchesDataModel.MultifiltersDataModel;
                homeCvsDataModel.MultifiltersDataModel.SelectedLocalization = new List<int>() { offer.idlocalization };
                homeCvsDataModel.MultifiltersDataModel.SelectedProfesionalCategories = new List<int>() { offer.idcategory };
            }

            homeCvsDataModel.IsNuggetCv = ConfigurationManager.AppSettings["SEARCH_CANDIDATES_BY_API"] != null && ConfigurationManager.AppSettings["SEARCH_CANDIDATES_BY_API"].ToLower() == "true";
            homeCvsDataModel.IsNewFiltersActive = ConfigurationManager.AppSettings["NEW_FILTERS_CVS"] != null && ConfigurationManager.AppSettings["NEW_FILTERS_CVS"].ToLower() == "true";

            var searchFilter = GetElasticFilter(homeCvsDataModel.MultifiltersDataModel, homeCvsDataModel.Pager, companyCredentials, homeCvsDataModel.IsNuggetCv, homeCvsDataModel.IsNewFiltersActive, offer);
            FillCandidates(homeCvsDataModel, searchFilter, companyCredentials, _portalConfig);

            if (!homeCvsDataModel.Candidates.Any()) return;

            var index = homeCvsDataModel.Candidates.FindIndex(c => c.idcvprincipalEncrypted == idcv);

            if (index == -1)
            {
                homeCvsDataModel.Pager.PageSelected = homeCvsDataModel.Pager.PageSelected + 1;
                searchFilter = GetElasticFilter(homeCvsDataModel.MultifiltersDataModel, homeCvsDataModel.Pager, companyCredentials, homeCvsDataModel.IsNuggetCv, homeCvsDataModel.IsNewFiltersActive, offer);
                FillCandidates(homeCvsDataModel, searchFilter, companyCredentials, _portalConfig);

                index = homeCvsDataModel.Candidates.FindIndex(c => c.idcvprincipalEncrypted == idcv);
                if (index == -1)
                {
                    homeCvsDataModel.Pager.PageSelected = homeCvsDataModel.Pager.PageSelected - 2;
                    searchFilter = GetElasticFilter(homeCvsDataModel.MultifiltersDataModel, homeCvsDataModel.Pager, companyCredentials, homeCvsDataModel.IsNuggetCv, homeCvsDataModel.IsNewFiltersActive, offer);
                    FillCandidates(homeCvsDataModel, searchFilter, companyCredentials, _portalConfig);
                    index = homeCvsDataModel.Candidates.FindIndex(c => c.idcvprincipalEncrypted == idcv);
                }

                _sessionService.Remember(homeCvsDataModel);
            }
            var currentCandidatePage = homeCvsDataModel.Candidates;


            if (index > 0 && homeCvsDataModel.Candidates.ElementAtOrDefault(index - 1) != null)
            {
                cvDetail.PreviousUrl = Url.Action("Index", "CompanyBBDDCvDetail",
                    new { idcv = homeCvsDataModel.Candidates[index - 1].idcvprincipalEncrypted, oi = oi, cf = cf });
            }
            else if (index == 0 && homeCvsDataModel.Pager.PageSelected > 1)
            {
                homeCvsDataModel.Pager.PageSelected = homeCvsDataModel.Pager.PageSelected - 1;
                searchFilter = GetElasticFilter(homeCvsDataModel.MultifiltersDataModel, homeCvsDataModel.Pager, companyCredentials, homeCvsDataModel.IsNuggetCv, homeCvsDataModel.IsNewFiltersActive, offer);
                FillCandidates(homeCvsDataModel, searchFilter, companyCredentials, _portalConfig);

                if (homeCvsDataModel.Candidates.Any())
                    cvDetail.PreviousUrl = Url.Action("Index", "CompanyBBDDCvDetail",
                        new { idcv = homeCvsDataModel.Candidates[homeCvsDataModel.Candidates.Count - 1].idcvprincipalEncrypted, oi = oi, cf = cf });
            }
            else if (homeCvsDataModel.Pager.PageSelected > 1
                && currentCandidatePage.ElementAtOrDefault(currentCandidatePage.Count - 1) != null)
            {
                cvDetail.PreviousUrl = Url.Action("Index", "CompanyBBDDCvDetail",
                    new { idcv = currentCandidatePage[currentCandidatePage.Count - 1].idcvprincipalEncrypted, oi = oi, cf = cf });
            }

            if (index + 1 < homeCvsDataModel.Pager.PageSizeSelected)
            {
                if (currentCandidatePage.Count > index + 1
                    && currentCandidatePage.ElementAtOrDefault(index + 1) != null)
                {
                    cvDetail.NextUrl = Url.Action("Index", "CompanyBBDDCvDetail",
                        new { idcv = currentCandidatePage[index + 1].idcvprincipalEncrypted, oi = oi, cf = cf });
                }
            }
            else
            {
                homeCvsDataModel.Pager.PageSelected = homeCvsDataModel.Pager.PageSelected + 1;
                searchFilter = GetElasticFilter(homeCvsDataModel.MultifiltersDataModel, homeCvsDataModel.Pager, companyCredentials, homeCvsDataModel.IsNuggetCv, homeCvsDataModel.IsNewFiltersActive, offer);
                FillCandidates(homeCvsDataModel, searchFilter, companyCredentials, _portalConfig);

                if (homeCvsDataModel.Candidates.Any())
                    cvDetail.NextUrl = Url.Action("Index", "CompanyBBDDCvDetail",
                        new { idcv = homeCvsDataModel.Candidates[0].idcvprincipalEncrypted, oi = oi, cf = cf });
            }
        }

        private int GetCvsByOffer(int idOffer)
        {
            var cvCountersByOffers = _offerCvCountersService.GetCounterByIdOffers(idOffer.ToString(), _portalConfig.PortalId);

            int CVUnitsConsumed = 0;
            if (cvCountersByOffers.Count > 0)
                CVUnitsConsumed = cvCountersByOffers.First().UnitsConsumed;

            return CVUnitsConsumed;
        }

        #region Move to base controller (CvsController)

        private SearchFilter GetElasticFilter(MultifiltersDataModel m, PagerDataModel pagerDataModel,
                                                                         CompanyCredentials companyCredentials, bool isNuggetCv,
                                                                         bool IsNewFiltersActive, OfferEntity offer)
        {
            return new SearchFilter
            {
                PortalId = _portalConfig.PortalId,
                Facets = new List<string>(),
                IdPrivacy = Convert.ToInt32(PrivacyEnum.Visible),
                PageSize = pagerDataModel.PageSizeSelected,
                PageNumber = pagerDataModel.PageSelected,
                OrderSearch = new List<BaseOrderSearch>()
                {
                   _elasticCandidateService.GetOrderSearch(new MultiFiltersDTO(m.Order, m.MultiSearchText, m.NameUniversity, m.CompanyName,
                                                                               m.OrderDirectionSelected, _portalConfig.AEPortalConfig.IsPremiumActive))
                },
                Nit = m.SearchNit,
                CandidatesIds = GetIdCandidatesByNit(m, companyCredentials.IdCompany, offer),
                Nationalities = m.SelectedNationatility,
                LocalizationIds = m.SelectedLocalization,
                CityIds = m.SelectedCities,
                CategoryIds = m.SelectedProfesionalCategories,
                LevelStudyIds = m.SelectedStudyLevel,
                AgeMin = m.MinAge ?? 0,
                AgeMax = m.MaxAge ?? 0,
                MinSalary = m.MinSalary ?? 0,
                MaxSalary = m.MaxSalary ?? 0,
                LastLogin = m.LastLogin != 0
                    ? DateTime.Now.AddDays(m.LastLogin * (-1)).Date
                    : DateTime.MinValue,
                Exact = m.ExactSearchText,
                Query = m.ExactSearchText
                    ? $"'\'{m.MultiSearchText}'\'"
                    : m.MultiSearchText,
                IsVisualized = m.IsCvVisualitzed,
                VisualizedIds = !isNuggetCv
                                    ? GetVisualizedCvsIds(m.IsCvVisualitzed, companyCredentials)
                                    : new List<int>(),
                Gender = m.Gender != 0
                    ? new List<int>() { m.Gender }
                    : new List<int>(),
                HasPhoto = m.HasPhoto,
                LanguagesIds = TransformLanguageDataModelToLanguageSearchFilter(m.SelectedLanguage, m.SelectedLanguageLevel),
                RatingsIds = GetIdsByRatings(m, companyCredentials.IdCompany),
                IsRating = m.Rating ?? 0,
                CommentedIds = GetIdsByComments(m, companyCredentials.IdCompany, companyCredentials.PortalId),
                IsCommented = m.Comments,
                CompanyFolder = GetCompanyFolders(m, companyCredentials),
                CompanyFolderSelected = GetShortAndDecrypt(m.FolderSelected),
                IsWorking = isNuggetCv && IsNewFiltersActive ? m.IsWorking : (short)0,
                MaxYearsExperience = isNuggetCv && IsNewFiltersActive && m.MaxYearsExperience != null ? (short)m.MaxYearsExperience : (short)0,
                MinYearsExperience = isNuggetCv && IsNewFiltersActive && m.MinYearsExperience != null ? (short)m.MinYearsExperience : (short)0,
                University = isNuggetCv && IsNewFiltersActive ? m.NameUniversity : string.Empty,
                IsStudying = isNuggetCv && IsNewFiltersActive ? m.IsStudying : (short)StudyingStatusEnum.None,
                StudyStatus = isNuggetCv && IsNewFiltersActive ? m.StudyStatus : (short)StudyStatusEnum.None,
                HasDisability = isNuggetCv && IsNewFiltersActive ? m.HasDisability : (short)HasDisabilityStatusEnum.None,
                Companies = isNuggetCv
                           && m.IsCvVisualitzed != 0
                           ? new List<int>() { companyCredentials.IdCompany }
                           : new List<int>(),
                HasTalentViewTest = m.HasTalentViewTest
            };
        }

        private List<int> GetIdCandidatesByNit(MultifiltersDataModel m, int idCompany, OfferEntity offer)
        {
            if (m.IsMySelection && offer != null && offer.idoffer > 0)
            {
                var cvs = _curriculumService.GetCVsByOffer(offer.idofferCT, offer.idcompany, offer.idportal);
                return cvs.Any() ? cvs.Select(x => x.IdCandidate).ToList() : new List<int>();
            }
            else if (m.ShowNitFilter && idCompany > 0 && !string.IsNullOrEmpty(m.SearchNit))
                return new List<int>() { _matchService.GetIdCandidateByMatchNitAndCompanyId(m.SearchNit, idCompany) };

            return new List<int>();
        }

        private List<int> GetVisualizedCvsIds(short isCvVisualitzed, CompanyCredentials companyCredentials)
        {
            if (isCvVisualitzed != 0)
                return _curriculumService.GetCVsByCompany(companyCredentials.IdCompany, companyCredentials.PortalId)
                    .Select(x => x.IdCandidate)
                    .ToList();

            return new List<int>();
        }

        private List<string> TransformLanguageDataModelToLanguageSearchFilter(int? selectedLanguage, int? selectedLanguageLevel)
        {
            var result = new List<string>();

            if (selectedLanguage != null && selectedLanguage > 0)
            {
                var stringBuilder = string.Empty;
                stringBuilder = selectedLanguage.ToString();

                if (selectedLanguageLevel != null)
                    stringBuilder = $"{stringBuilder}_{selectedLanguageLevel.ToString()}";

                result.Add(stringBuilder);
            }

            return result;
        }

        private List<int> GetIdsByComments(MultifiltersDataModel multifiltersDataModel, int idCompany, short idPortal)
        {
            if (multifiltersDataModel.ShowCommentFilter
                && multifiltersDataModel.Comments > 0)
                return _commentCvService.GetIdCandidatesByCompany(idCompany, idPortal);

            return new List<int>();
        }

        private List<int> GetIdsByRatings(MultifiltersDataModel multifiltersDataModel, int idCompany)
        {
            if (multifiltersDataModel.ShowRatingFilter
                && multifiltersDataModel.Rating != null)
                return _ratingCvService.GetIdsByRating(idCompany, (short)multifiltersDataModel.Rating);

            return new List<int>();
        }

        private List<int> GetCompanyFolders(MultifiltersDataModel multifiltersDataModel, CompanyCredentials companyCredentials)
        {
            if (multifiltersDataModel.ShowCustomFoldersFilter
                && !string.IsNullOrEmpty(multifiltersDataModel.FolderSelected))
            {
                int.TryParse(_encryptionService.Decrypt(multifiltersDataModel.FolderSelected), out var idFolder);

                return _customFoldersService.GetCvsByFolderCustom(new CustomFoldersFilterEntity()
                {
                    Environment = (short)CustomFoldersEnvironmentEnum.CvBBDD,
                    Id = idFolder,
                    IdPortal = companyCredentials.PortalId,
                    IdUser = companyCredentials.UserId
                });
            }

            return new List<int>();
        }

        private short GetShortAndDecrypt(string folderSelected)
        {
            short result = 0;

            if (!string.IsNullOrEmpty(folderSelected)
                && folderSelected != "0")
                short.TryParse(_encryptionService.Decrypt(folderSelected), out result);

            return result;
        }

        private void FillCandidates(HomeCvsDataModel homeCvsDataModel, SearchFilter searchFilter, CompanyCredentials companyCredentials, PortalConfig portalConfig)
        {
            if (searchFilter.IsRating != 0 && !searchFilter.RatingsIds.Any()
                || searchFilter.IsCommented != 0 && !searchFilter.CommentedIds.Any()
                || searchFilter.CompanyFolderSelected != 0 && !searchFilter.CompanyFolder.Any()
                || searchFilter.IsVisualized != 0 && !searchFilter.VisualizedIds.Any())
            {
                homeCvsDataModel.Candidates = new List<CandidateReadSearchDataModel>();
                homeCvsDataModel.Pager.TotalRows = 0;
            }
            else
            {
                var candidates = GetCandidateElastic(searchFilter);

                if (candidates.Candidates.Any())
                {
                    homeCvsDataModel.Candidates = CandidateReadSearchDataModelMapping.MappingCandidateReadSearchDTOToCandidateReadSearchDataModel(candidates.Candidates, portalConfig);
                    homeCvsDataModel.Pager.TotalRows = candidates.Total;
                }
            }
        }

        private SearchResult<CandidateReadSearch> GetCandidateElastic(SearchFilter searchFilter)
        {
            var searchResult = _elasticCandidateService.GetCandidates(searchFilter);

            if (searchResult != null
                && searchResult.Candidates.Any())
            {
                return searchResult;
            }

            return new SearchResult<CandidateReadSearch>();
        }

        #endregion


        private void DetailKpiAction(bool isMatch, bool hasPhoto, CompanyCredentials companyCredentials)
        {
            short kpiType = 0;
            short kpiHavePhoto = 0;

            if (isMatch)
            {
                kpiHavePhoto = (hasPhoto) ? (short)KpiEnum.COMPANY_VIEW_MATCH_CV_WITH_PHOTO : (short)KpiEnum.COMPANY_VIEW_MATCH_CV_WITHOUT_PHOTO;
                ActionKpiCount(kpiHavePhoto, 1, companyCredentials);
                kpiType = (short)KpiEnum.COMPANY_VIEW_MATCH_CV;
                ActionKpiCount(kpiType, 1, companyCredentials);
            }
            else
            {
                kpiHavePhoto = (hasPhoto) ? (short)KpiEnum.COMPANY_VIEW_BBDD_CV_WITH_PHOTO : (short)KpiEnum.COMPANY_VIEW_BBDD_CV_WITHOUT_PHOTO;
                ActionKpiCount(kpiHavePhoto, 1, companyCredentials);
                kpiType = (short)KpiEnum.COMPANY_VIEW_BBDD_CV;
                ActionKpiCount(kpiType, 1, companyCredentials);
            }
        }

        private void ActionKpiCount(short _kpiType, short count, CompanyCredentials companyCredentials)
        {
            Task.Factory.StartNew(() =>
            {
                _companyCountersService.AddCounterCompany(_kpiType, _portalConfig.PortalId, companyCredentials.IdCompany, count, false);
                _kpiService.AddSumBlock(_kpiType, _portalConfig.PortalId, count);

            });
        }

        private void LogicShowVisitsMyProfile(CompanyCredentials companyCredentials, int idCandidate, int idCv, string q, string cats, string prov, bool isMatch, short cvVisualizationPrivacy, OfferEntity offer)
        {
            if (_portalConfig.HasCvVisualization && idCandidate > 0 && int.TryParse(_portalConfig.CurrentDateTimePortal.ToString("yyyyMMdd"), out var dateInt))
            {
                if (!_cvVisualizationControlService.Exists(companyCredentials.IdCompany, idCandidate, _portalConfig.PortalId, 0, idCv, dateInt))
                {
                    SetCvVisualizationCounters(idCandidate, _portalConfig.PortalId, dateInt);
                    SetCvVisualizationsTotals(idCandidate, _portalConfig.PortalId, dateInt);
                    SetCvVisualizationHistory(companyCredentials.IdCompany, idCandidate, _portalConfig.PortalId, isMatch, dateInt);
                    _kpiService.Add((int)KpiEnum.CVS_VISUALIZATIONS_TOTAL, _portalConfig.PortalId);
                }
                SetCvVisualizationControl(companyCredentials, idCandidate, _portalConfig.PortalId, idCv, isMatch, dateInt, cvVisualizationPrivacy, offer);
                SetCvVisualizationSearchFilters(q, cats, prov, companyCredentials.IdCompany, idCandidate, _portalConfig.PortalId, dateInt);
            }
        }

        private int SetCvVisualizationControl(CompanyCredentials companyCredentials, int idCandidate, short portalId, int idCv, bool isMatch, int dateInt, short cvVisualizationPrivacy, OfferEntity offer)
        {
            var cvVisualizationControl = new CvVisualizationControlEntity()
            {
                IdCompany = companyCredentials.IdCompany,
                IdCandidate = idCandidate,
                IdPortal = portalId,
                IdCv = idCv,
                IdType = isMatch ? (short)CvVisualizationTypeEnum.Match : (short)CvVisualizationTypeEnum.Cv,
                DateInt = dateInt,
                StatusId = (short)CompanyStatusEnum.Any,
                IdOffer = offer.idoffer,
                IsCompanyVIsible = GetIsCompanyVisible(cvVisualizationPrivacy, offer, isMatch),
                CreationDate = _portalConfig.CurrentDateTimePortal,
                IP = _clientIpAddressResolverService.GetIpAddress(),
                IdApp = _portalConfig.idapp,
                IdCompanyUser = (int)companyCredentials.UserId
            };
            return _cvVisualizationControlService.Insert(cvVisualizationControl);
        }

        private short GetIsCompanyVisible(short cvVisualizationPrivacy, OfferEntity offer, bool isMatch)
        {
            if (!isMatch || offer.HiddenCompany == (short)OfferVisibilityEnum.Visible)
                return cvVisualizationPrivacy == (short)CvVisualizationPrivacity.Hidden ? (short)CompanyVisibilityEnum.Hidden : (short)CompanyVisibilityEnum.Visible;
            return (short)CompanyVisibilityEnum.Hidden;
        }

        private void SetCvVisualizationCounters(int idCandidate, short portalId, int dateInt)
        {
            var cvVisualizattionCounters = new CvVisualizationCountersEntity
            {
                IdCandidate = idCandidate,
                IdPortal = portalId,
                DateInt = dateInt,
                Total = 1
            };

            _cvVisualizationCountersService.Insert(cvVisualizattionCounters);
        }

        private void SetCvVisualizationHistory(int idCompany, int idcandidate, short idPortal, bool idType, int dateShowed)
        {
            var CvVisualizationHistory = new CvVisualizationHistoryEntity
            {
                IdCompany = idCompany,
                IdCandidate = idcandidate,
                IdPortal = idPortal,
                DateShowed = dateShowed,
                IdType = idType ? (short)CvVisualizationTypeEnum.Match : (short)CvVisualizationTypeEnum.Cv
            };

            _cvVisualizationHistoryService.Insert(CvVisualizationHistory);
        }

        private void SetCvVisualizationSearchFilters(string q, string cats, string prov, int idCompany, int idCandidate, short idPortal, int dateInt)
        {
            if (string.IsNullOrEmpty(q) && string.IsNullOrEmpty(cats) && string.IsNullOrEmpty(prov))
                return;

            var query = string.Empty;
            if (!string.IsNullOrEmpty(q))
                query = _encryptionService.Decrypt(q);

            var idsCategory = string.Empty;
            if (!string.IsNullOrEmpty(cats))
                idsCategory = _encryptionService.Decrypt(cats);

            var idsProvince = string.Empty;
            if (!string.IsNullOrEmpty(prov))
                idsProvince = _encryptionService.Decrypt(prov);

            var cvVisualizationSearchFilter = _cvVisualizationSearchFIltersService.Get(idCompany, idCandidate, idPortal, dateInt);
            if (cvVisualizationSearchFilter.Id > 0)
            {
                if (!string.IsNullOrEmpty(cvVisualizationSearchFilter.Query) && !string.IsNullOrEmpty(query))
                {
                    var queryList = query.Split(SEPARATOR_SPLIT).ToList();
                    if (queryList.Any())
                    {
                        queryList.ForEach(qu =>
                        {
                            if (!cvVisualizationSearchFilter.Query.Contains(qu))
                                query = $"{cvVisualizationSearchFilter.Query},{qu}";
                        });
                    }
                }
                if (!string.IsNullOrEmpty(cvVisualizationSearchFilter.IdsCategory) && !string.IsNullOrEmpty(idsCategory))
                {
                    var idsCategoryList = idsCategory.Split(SEPARATOR_SPLIT).ToList();
                    if (idsCategoryList.Any())
                    {
                        idsCategoryList.ForEach(ca =>
                        {
                            if (!cvVisualizationSearchFilter.IdsCategory.Contains(idsCategory))
                                idsCategory = $"{cvVisualizationSearchFilter.IdsCategory},{ca}";
                        });
                    }
                }
                if (!string.IsNullOrEmpty(cvVisualizationSearchFilter.IdsProvince) && !string.IsNullOrEmpty(idsProvince))
                {
                    var idsProvinceList = idsProvince.Split(SEPARATOR_SPLIT).ToList();
                    if (idsProvinceList.Any())
                    {
                        idsProvinceList.ForEach(pr =>
                        {
                            if (!cvVisualizationSearchFilter.IdsProvince.Contains(pr))
                                idsProvince = $"{cvVisualizationSearchFilter.IdsProvince},{pr}";
                        });
                    }
                }
            }

            var cvVisualizationSearchFilters = new CvVisualizationSearchFiltersEntity()
            {
                IdCompany = idCompany,
                IdCandidate = idCandidate,
                IdPortal = idPortal,
                Query = query.SubstringByMaxLength(MAX_STRING_LENGTH_BY_CV_VISUALIZATION_SEARCH_FILTER),
                IdsCategory = idsCategory.SubstringByMaxLength(MAX_STRING_LENGTH_BY_CV_VISUALIZATION_SEARCH_FILTER),
                IdsProvince = idsProvince.SubstringByMaxLength(MAX_STRING_LENGTH_BY_CV_VISUALIZATION_SEARCH_FILTER),
                DateInt = dateInt
            };

            _cvVisualizationSearchFIltersService.Insert(cvVisualizationSearchFilters);
        }

        private void SetCvVisualizationsTotals(int idcandidate, short idportal, int companyDateLastShowed)
        {
            var cvVisualizationsTotals = new CvVisualizationsTotalsEntity()
            {
                IdCandidate = idcandidate,
                IdPortal = idportal,
                TotalShowed = 1,
                TotalNewShowed = 1,
                CompanyDateLastShowed = companyDateLastShowed,
                CandidateDateLastShowed = 0
            };

            _cvVisualizationsTotalsService.Insert(cvVisualizationsTotals);
        }

        public bool InsertCandidatePhotoComplaint(string idc, string reason)
        {
            try
            {
                var companyCredentials = SecurityHelper.GetCompanyCredentials();

                int.TryParse(_encryptionService.Decrypt(idc), out int idcInt);
                short.TryParse(reason, out short reasonInt);

                var candidatePhotoComplaint = new Core.Event.Contracts.ServiceLibrary.DTO.CompanyComplaintEntityRequestDTO()
                {
                    IdCompany = companyCredentials.IdCompany,
                    IdUser = (int)companyCredentials.UserId,
                    IdCandidate = idcInt,
                    IdPortal = companyCredentials.PortalId,
                    IdComplaintType = (short)ComplaintTypeEnum.CandidatePhoto,
                    IdComplaintReason = reasonInt,
                    IdPageOrigin = PageId
                };

                var result = _reportCandidatePhotoRequestEventService.SendReportCandidatePhotoRequest(candidatePhotoComplaint);
                if (result)
                {
                    _kpiService.Add((int)KpiEnum.REPORT_CANDIDATE_PHOTO, _portalConfig.PortalId);
                }

                return result;
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyBBDDCvDetailController InsertCandidatePhotoComplaint {ex}");
                ExceptionPublisherService.Publish(ex, "CompanyBBDDCvDetailController", "InsertCandidatePhotoComplaint");
                return false;
            }
        }

        private Dictionary<string, string> FillPageLiterals(PortalConfig portalConfig)
        {
            Dictionary<string, string> pageLiterals = new Dictionary<string, string>();

            pageLiterals.Add("LIT_TITLE_BBDDCV_PROD", PageLiteralsHelper.GetLiteral("LIT_TITLE_BBDDCV_PROD", (int)PageEnum.DetailCVCompany, portalConfig));
            pageLiterals.Add("LIT_TEXT_BBDDCV_PROD", PageLiteralsHelper.GetLiteral("LIT_TEXT_BBDDCV_PROD", (int)PageEnum.DetailCVCompany, portalConfig));
            pageLiterals.Add("LIT_BTT_BBDDCV_PROD", PageLiteralsHelper.GetLiteral("LIT_BTT_BBDDCV_PROD", (int)PageEnum.DetailCVCompany, portalConfig));
            pageLiterals.Add("LIT_CANCEL_BBDDCV_PROD", PageLiteralsHelper.GetLiteral("LIT_CANCEL_BBDDCV_PROD", (int)PageEnum.DetailCVCompany, portalConfig));
            pageLiterals.Add("LIT_SUBTEXT_BBDDCV_PROD", PageLiteralsHelper.GetLiteral("LIT_SUBTEXT_BBDDCV_PROD", (int)PageEnum.DetailCVCompany, portalConfig));
            pageLiterals.Add("LIT_ANCHOR_BBDDCV_PROD", PageLiteralsHelper.GetLiteral("LIT_ANCHOR_BBDDCV_PROD", (int)PageEnum.DetailCVCompany, portalConfig));
            pageLiterals.Add("LIT_CLOSE_BBDDCV_PROD", PageLiteralsHelper.GetLiteral("LIT_CLOSE_BBDDCV_PROD", (int)PageEnum.DetailCVCompany, portalConfig));
            pageLiterals.Add("LIT_PRODUCT_PRICE", PageLiteralsHelper.GetLiteral("LIT_PRODUCT_PRICE", (int)PageEnum.DetailCVCompany, portalConfig));

            return pageLiterals;
        }

        private string GetUrlPresentationVideo(string codePresentationVideo)
        {
            //pendiente implementar que se vea el video
            var config = new CompanyConfiguration();
            var urlVideoInterview = _portalConfig.UrlVideoInterview;
            var apiKey = _quizConsumerService.GetApiKeyForVideoInterviews(Convert.ToInt16(config.IdBusiness), _portalConfig.PortalId);
            return $"{urlVideoInterview}?apiKey={apiKey}&video={codePresentationVideo}";
        }
    }
}