using Redarbor.AccessControl.ServiceLibrary.CrossCutting.Entities;
using Redarbor.AccessControl.ServiceLibrary.CrossCutting.Enums;
using Redarbor.AccessControl.ServiceLibrary.Services;
using Redarbor.Company.WebUI.Helpers;
using Redarbor.Core.Resolver.Contracts.ServiceLibrary;
using Redarbor.Master.Contracts.ServiceLibrary.DTO;
using Redarbor.Master.Entities.Enums;
using Redarbor.Web.UI.Library.Controllers;
using System.Web.Mvc;

namespace Redarbor.Company.WebUI.Controllers
{
    public class UnusualActivityController : RedarborController
    {
        private readonly IClientIpAddressResolverService _clientIpAddressResolverService;
        private readonly IBlockingPageService _blockingPageService;

        public UnusualActivityController(IClientIpAddressResolverService clientIpAddressResolverService
            , IBlockingPageService blockingPageService)
        {
            _clientIpAddressResolverService = clientIpAddressResolverService;
            _blockingPageService = blockingPageService;
        }

        [Route("UnusualActivity")]
        public ActionResult Index()
        {
            var portalConfig = PortalConfigHelper.GetPortalConfiguration();

            if (portalConfig.ShowCaptchaValidation)
            {
                var companyCredentials = SecurityHelper.GetCompanyCredentials();

                if (GetAccessControlByCredentials(companyCredentials) == AccessControlEnum.BlockingPage)
                {
                    IncrementShows(companyCredentials);

                    return View("Index");
                }
            }            

            return RedirectToAction("Index", "Company");            
        }

        private AccessControlEnum GetAccessControlByCredentials(CompanyCredentials companyCredentials)
        {
            return AccessControlHelper.GetAccessControl(new AbuseSearch()
            {
                AppId = (short)ApplicationEnum.AreaEmpresa,
                CustomerId = companyCredentials.IdCompany,
                PortalId = companyCredentials.PortalId,
                UserId = (int)companyCredentials.UserId,
                ActionType = ActionTypeEnum.AuthorizeControl
            }, companyCredentials);
        }

        private bool IncrementShows(CompanyCredentials companyCredentials)
        {
            return _blockingPageService.IncrementShow(new BlockingPageResults()
            {
                CustomerId = companyCredentials.IdCompany,
                ClientIP = _clientIpAddressResolverService.GetIpAddress(),
                UserId = (int)companyCredentials.UserId,
                ActionType = (short)ActionTypeEnum.AuthorizeControl,
                PortalId = companyCredentials.PortalId,
                AppId = (short)ApplicationEnum.AreaEmpresa
            });
        }
    }
}