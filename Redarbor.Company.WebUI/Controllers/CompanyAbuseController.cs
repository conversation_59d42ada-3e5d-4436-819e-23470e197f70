using Redarbor.Company.WebUI.Helpers;
using Redarbor.Company.WebUI.Models;
using Redarbor.Company.WebUI.Validation;
using Redarbor.Web.UI.Library.Controllers;
using System.Web.Mvc;

namespace Redarbor.Company.WebUI.Controllers
{
    public class CompanyAbuseController : RedarborController
    {
        private readonly ICaptchaValidationService _captchaValidationService;

        public CompanyAbuseController(ICaptchaValidationService captchaValidationService)
        {
            _captchaValidationService = captchaValidationService;
        }

        [Route("AccesControl")]
        public ActionResult Index()
        {
            var portalConfig = PortalConfigHelper.GetPortalConfiguration();

            if (portalConfig.ShowCaptchaValidation)
            {
                var companyCredentials = SecurityHelper.GetCompanyCredentials();
                var model = new CaptchaAbuseDataModel()
                {
                    PublicKey = _captchaValidationService.GetControlKey(companyCredentials)
                };

                if (!string.IsNullOrEmpty(model.PublicKey))
                {
                    _captchaValidationService.IncrementShows(companyCredentials);
                    return View("Index", model);
                }
            }

            return RedirectToAction("Index", "Company");
        }

        public bool AddCompanyAbuseResult(string is_ok_captcha)
        {
            var companyCredentials = SecurityHelper.GetCompanyCredentials();
            return _captchaValidationService.IsCaptchaValid(is_ok_captcha, companyCredentials);
        }
    }
}