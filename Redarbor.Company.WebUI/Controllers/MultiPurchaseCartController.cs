using AutoMapper;
using AutoMapper.Mappers;
using Fasterflect;
using Redarbor.Candidate.Contracts.ServiceLibrary;
using Redarbor.Common.Entities.Configuration;
using Redarbor.Common.Entities.Constants;
using Redarbor.Common.Entities.Enums;
using Redarbor.Company.Contracts.ServiceLibrary;
using Redarbor.Company.Contracts.ServiceLibrary.Enums;
using Redarbor.Company.WebUI.Helpers;
using Redarbor.Company.WebUI.Helpers.Mappings;
using Redarbor.Company.WebUI.Models.Company.Cart;
using Redarbor.Company.WebUI.Models.Company.Product;
using Redarbor.Company.WebUI.Security;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary.Constants;
using Redarbor.Core.Contracts.ServiceLibrary;
using Redarbor.Core.Kpi.Contracts.ServiceLibrary;
using Redarbor.Core.Kpi.Contracts.ServiceLibrary.Enums;
using Redarbor.Core.Shared.Contracts.ServiceLibrary;
using Redarbor.Dictionaries.Consumer.Enums;
using Redarbor.Elastic.Entities.Candidate;
using Redarbor.Extensions.Library.Extensions;
using Redarbor.Invoice.Contracts.ServiceLibrary;
using Redarbor.Master.Contracts.ServiceLibrary;
using Redarbor.Master.Contracts.ServiceLibrary.DTO;
using Redarbor.Master.Entities.Company;
using Redarbor.Master.Entities.Enums;
using Redarbor.Master.Entities.Invoice;
using Redarbor.Master.Entities.Payment;
using Redarbor.Master.Entities.Product;
using Redarbor.Master.Entities.PurchaseOperation;
using Redarbor.Master.Entities.Searchs;
using Redarbor.Offer.Contracts.ServiceLibrary;
using Redarbor.Payment.Contracts.ServiceLibrary;
using Redarbor.Payment.Contracts.ServiceLibrary.Models;
using Redarbor.Products.Contracts.ServiceLibrary;
using Redarbor.ProductSubGroups.Contracts.ServiceLibrary;
using Redarbor.ProProduct.Contracts.ServiceLibrary;
using Redarbor.PurchaseOperation.Contracts.ServiceLibrary;
using Redarbor.Web.UI.Library.Exceptions;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Web.Mvc;

namespace Redarbor.Company.WebUI.Controllers
{
    [RedarborAuthorize]
    public class MultiPurchaseCartController : CompanyBaseController
    {
        private readonly IEncryptionService _encryptionService;
        private readonly IInvoiceEntityService _invoiceService;
        private readonly IProProductService _proProductService;
        private readonly ICompanyCheckIdentifierService _companyCheckIdentifierService;
        private readonly IMultiPurchaseOperationService _multipurchaseOperationService;
        private readonly IPurchaseOperationWorldpayService _purchaseOperationWorldpayService;
        private readonly IPurchaseOperationPayUService _purchaseOperationPayUService;
        private readonly IPaymentPayUService _paymentPayUService;
        private readonly IPaymentPayPalService _purchaseOperationPayPalService;
        private readonly IKpiService _kpiService;
        private readonly IPaymentService _paymentService;
        private readonly IConfigurationService _configurationService;
        private readonly ILandingProductsService _landingProductsService;
        private readonly IProductsService _productsService;
        private readonly IRequestDeviceRecorder _requestDeviceRecorder;
        private readonly IElasticCandidateService _elasticCandidateService;
        private readonly IPageProductsService _pageProductService;
        public MultiPurchaseCartController(
            IProductService productService,
            IProductSubGroupsService productSubGroupsService,
            IOfferService offerService,
            ICompanyProductService companyProductService,
            ICompanyService companyService,
            IPortalConfigurationService portalConfigurationService,
            IEncryptionService encryptionService,
            IInvoiceEntityService invoiceService,
            IDictionaryService dictionaryService,
            IProProductService proProductService,
            ISecurityService securityService,
            ICompanyCheckIdentifierService companyCheckIdentifierService,
             IMultiPurchaseOperationService multipurchaseOperationService,
            IPurchaseOperationWorldpayService purchaseOperationWorldpayService,
            IPurchaseOperationPayUService purchaseOperationPayUService,
            IPaymentPayUService paymentPayUService,
            IPaymentPayPalService purchaseOperationPayPalService,
            IExceptionPublisherService exceptionPublisherService,
            IKpiService kpiService,
            IPaymentService paymentService,
            IConfigurationService configurationService,
            IElasticCandidateService elasticCandidateService,
            ILandingProductsService landingProductsService, 
            IProductsService productsService, 
            IRequestDeviceRecorder deviceTrafficChecker,
            IPageProductsService pageProductService) : base(productService, productSubGroupsService, offerService, companyProductService, companyService, portalConfigurationService, dictionaryService, securityService, exceptionPublisherService)
        {
            _encryptionService = encryptionService;
            _invoiceService = invoiceService;
            _proProductService = proProductService;
            _companyCheckIdentifierService = companyCheckIdentifierService;
            _multipurchaseOperationService = multipurchaseOperationService;
            _purchaseOperationPayUService = purchaseOperationPayUService;
            _purchaseOperationWorldpayService = purchaseOperationWorldpayService;
            _paymentPayUService = paymentPayUService;
            _purchaseOperationPayPalService = purchaseOperationPayPalService;
            _kpiService = kpiService;
            _paymentService = paymentService;
            _configurationService = configurationService;
            _landingProductsService = landingProductsService;
            _productsService = productsService;
            _requestDeviceRecorder = deviceTrafficChecker;
            _elasticCandidateService = elasticCandidateService;
            _pageProductService = pageProductService;
        }

        public ActionResult Index(string prod, string rm, string p, string oi, string originRequestPurchase, string cc, string btn, string mp)
        {
            try
            {                       
                var portalConfig = PortalConfigurationService.GetPortalConfiguration();
                var companyCredentials = SecurityHelper.GetCompanyCredentials();
                var company = CompanyService.GetByPK(new CompanySearchSpecifications(companyCredentials.PortalId) { Id = companyCredentials.IdCompany });

                if (checkBlocked(portalConfig.company_block, company.Blocked, company.LastDateBlockedUpdate, portalConfig.DaysBlock))
                    return RedirectToAction("Blocked", "Account");

                var urlRedirect = IsCompanyRejectedOrIsMembership(company, portalConfig, companyCredentials.IdCompany);
                if (!string.IsNullOrEmpty(urlRedirect))
                    return Redirect(urlRedirect);

                SumKPIStartPurchase(originRequestPurchase, companyCredentials.PortalId, companyCredentials.IdCompany, companyCredentials.UserId);

                var companyCartDataModel = GetCompanyCartDataModel(prod, rm, oi, originRequestPurchase, cc, btn, mp, portalConfig, company, companyCredentials);

                if (companyCartDataModel.HasVoucherPromotion)
                    _kpiService.AddSumBlock((int)KpiEnum.COMPANY_CART_WITH_PROMOTION_VOUCHER, portalConfig.PortalId, 1);

                _requestDeviceRecorder.RecordMultiPurchaseCartKpi(portalConfig.PortalId);

                TempData[PianoEvents.EVENT_KEY] = PianoEvents.OFFER_PURCHASED;
                if (!string.IsNullOrWhiteSpace(oi))
                {                    
                    TempData[PianoEvents.OFFER_ID_KEY] = EncryptationHelper.Decrypt(oi);
                }
                if (portalConfig.AEPortalConfig.NewWebShoppingCart)
                {
                    return View("IndexNew", companyCartDataModel);
                }
                return View(companyCartDataModel);
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"MultiPurchaseCartController -- Index {ex.ToString()}");
                ExceptionPublisherService.Publish(ex, "MultiPurchaseCartController", "Index-Get");
                return RedirectToAction("Index", "Home");
            }
        }

        private CompanyCartDataModel GetCompanyCartDataModel(string prod, string rm, string oi, string startPurchaseOrigin, string cc, string btn, string mp, PortalConfig portalConfig, CompanyEntity company, CompanyCredentials companyCredentials)
        {
            var cartDataModel = new CompanyCartDataModel();
            cartDataModel.TestAB = companyCredentials.TestAB;
            var subGroupProduct = GetSubGroupByProduct(prod, portalConfig);
            int idBtnOrigin = !string.IsNullOrEmpty(btn) ? _encryptionService.Decrypt(btn).ToInt() : 0;

            cartDataModel.FirtsPayMethod = GetFirstPayMethod(portalConfig);
            cartDataModel.MakePayment = mp == "True";
            cartDataModel.SubGroupTitle = FillSubGroupTitle(subGroupProduct, portalConfig.PortalId);
            cartDataModel.IsConvertToComplete = isConvertToComplete(idBtnOrigin, cc);

            LoadProductsCart(prod, rm, cartDataModel, company, subGroupProduct, startPurchaseOrigin, oi, portalConfig, companyCredentials.TestAB);
            LoadStyleCart(cartDataModel);
            LoadEditDataCompanyCart(oi, cartDataModel, company, portalConfig);

            cartDataModel.BtnOriginId = idBtnOrigin;
            cartDataModel.IsFromPostPublishStepTwo = startPurchaseOrigin == ((int)KpiEnum.START_PURCHASE_POSTPUBLISH_STEP2).ToString();
            cartDataModel.HeaderText = FillHeaderText(idBtnOrigin, portalConfig, subGroupProduct);
            cartDataModel.HasVoucherPromotion = cartDataModel.CartProducts.Any(c => c.Temporalities?.FirstOrDefault().TypePromotion == (short)PromotionTypeEnum.VoucherPacks);
            cartDataModel.IdCartOriginTrack = SaveCartOriginTrackButton(idBtnOrigin, company.Id, portalConfig.PortalId);

            if (cartDataModel.IsBBDDCvProduct)
            {
                var searchFilter = new SearchFilter { PortalId = portalConfig.PortalId };
                cartDataModel.TotalBBDDCvs = StringToolsHelper.ChangeGroupPointForDecimalPoint(_elasticCandidateService.GetCandidates(searchFilter).Total.ToString("N0"), portalConfig.PortalId);
            }

            FillProductSubGroupListText(subGroupProduct, portalConfig, cartDataModel);
            cartDataModel.PageLiteralsDataModel.PageLiterals = FillPageLiterals(portalConfig);
            cartDataModel.HasPrimeProductActive = FillHasPrimeProductActive(portalConfig, companyCredentials.TestAB, companyCredentials.IdCompany);

            return cartDataModel;
        }

        private bool FillHasPrimeProductActive(PortalConfig portalConfig, TestABEnum testABEnum, int idCompany)
        {
            if (portalConfig.AEPortalConfig.ActivatePrimeProduct && testABEnum == TestABEnum.A)
            {
                var allCompanyActiveProducts = CompanyProductService.GetAllCompanyActiveProducts(idCompany, portalConfig.PortalId);
                return allCompanyActiveProducts.Exists(x => x.SubGroupId == (short)ProductSubGroupsEnum.Prime);
            }
            return false;
        }

        private void FillProductSubGroupListText(short subGroupProduct, PortalConfig portalConfig, CompanyCartDataModel cartDataModel)
        {
            //SE DESACTIVA POR EL MOMENTO SubGroupDescriptionListText, POR PETICION DE PERE, SI SE VUELVE A ACTIVAR SE DEBERIA CREAR EL LITERAL EN BDD CORRESPONDIENTE A CADA TIPO DE SUBGRUPO

            switch (subGroupProduct)
            {
                case (short)ProductSubGroupsEnum.Standard:
                    cartDataModel.SubGroupListText = PageLiteralsHelper.GetLiteral("LIT_SUB_GROUP_LIST_TEXT_STANDARD", (int)PageEnum.PackCart, portalConfig);
                    cartDataModel.SubGroupProductListText = PageLiteralsHelper.GetLiteral("LIT_PRODUCT_LIST_TEXT_STANDARD", (int)PageEnum.PackCart, portalConfig);
                    //cartDataModel.SubGroupDescriptionListText = PageLiteralsHelper.GetLiteral("LIT_DESCRIPTION_SUB_GROUP_PRODUCT_LIST_TEXT_STANDARD", (int)PageEnum.PackCart, portalConfig);
                    break;
                case (short)ProductSubGroupsEnum.Advanced:
                    cartDataModel.SubGroupListText = PageLiteralsHelper.GetLiteral("LIT_SUB_GROUP_LIST_TEXT_ADVANCED", (int)PageEnum.PackCart, portalConfig);
                    cartDataModel.SubGroupProductListText = PageLiteralsHelper.GetLiteral("LIT_PRODUCT_LIST_TEXT_ADVANCED", (int)PageEnum.PackCart, portalConfig);
                    //cartDataModel.SubGroupDescriptionListText = PageLiteralsHelper.GetLiteral("LIT_DESCRIPTION_SUB_GROUP_PRODUCT_LIST_TEXT_ADVANCED", (int)PageEnum.PackCart, portalConfig);
                    break;
                case (short)ProductSubGroupsEnum.Premium:
                    cartDataModel.SubGroupListText = PageLiteralsHelper.GetLiteral("LIT_SUB_GROUP_LIST_TEXT_PREMIUM", (int)PageEnum.PackCart, portalConfig);
                    cartDataModel.SubGroupProductListText = PageLiteralsHelper.GetLiteral("LIT_PRODUCT_LIST_TEXT_PREMIUM", (int)PageEnum.PackCart, portalConfig);
                    //cartDataModel.SubGroupDescriptionListText = PageLiteralsHelper.GetLiteral("LIT_DESCRIPTION_GROUP_PRODUCT_LIST_TEXT_PREMIUM", (int)PageEnum.PackCart, portalConfig);
                    break;
                case (short)ProductSubGroupsEnum.BBDDcv:
                    cartDataModel.SubGroupListText = PageLiteralsHelper.GetLiteral("LIT_BBDD_SUBT_V2", (int)PageEnum.PackCart, portalConfig).Replace("#TOTALBBDDCVS#", cartDataModel.TotalBBDDCvs);
                    cartDataModel.SubGroupProductListText = PageLiteralsHelper.GetLiteral("LIT_OFFERS_BBDD", (int)PageEnum.PackCart, portalConfig);
                    //cartDataModel.SubGroupDescriptionListText = PageLiteralsHelper.GetLiteral("LIT_DESCRIPTION_GROUP_PRODUCT_LIST_TEXT_PREMIUM", (int)PageEnum.PackCart, portalConfig);
                    break;
                default:
                    cartDataModel.SubGroupListText = PageLiteralsHelper.GetLiteral("LIT_SUB_GROUP_LIST_TEXT_DEFAULT", (int)PageEnum.PackCart, portalConfig);
                    cartDataModel.SubGroupProductListText = PageLiteralsHelper.GetLiteral("LIT_PRODUCT_LIST_TEXT_DEFAULT", (int)PageEnum.PackCart, portalConfig);
                    //cartDataModel.SubGroupDescriptionListText = PageLiteralsHelper.GetLiteral("LIT_DESCRIPTION_GROUP_PRODUCT_LIST_TEXT_DEFAULT", (int)PageEnum.PackCart, portalConfig);
                    break;
            }
        }

        private int SaveCartOriginTrackButton(int decrypBtn, int idCompany, short idPortal)
        {
            return _multipurchaseOperationService.SaveCartOriginTrackButton(decrypBtn, idPortal, idCompany);
        }

        private short GetSubGroupByProduct(string prod, PortalConfig portalConfig)
        {
            int decrypProduct = 0;
            short subGroup = 0;
            if (!string.IsNullOrEmpty(prod))
                decrypProduct = EncryptationHelper.Decrypt(prod).ToInt();
            if (decrypProduct != 0)
            {
                return ProductService.GetSubGroupIdByProduct(decrypProduct, portalConfig);
            }
            return subGroup;
        }

        private string FillSubGroupTitle(short subGroup, short idPortal)
        {
            if (subGroup > 0)
            {
                var productSubGroups = ProductSubGroupsService.Get(idPortal);
                return productSubGroups.Any() ? productSubGroups.Exists(p => p.Id == subGroup) ? productSubGroups.FirstOrDefault(p => p.Id == subGroup).Name : string.Empty : string.Empty;
            }
            return string.Empty;
        }

        private string IsCompanyRejectedOrIsMembership(CompanyEntity company, PortalConfig portalConfig, int idCompany)
        {
            var companyProduct = CompanyProductService.GetByIdCompany(idCompany, portalConfig.PortalId, OfferIntegratorEnum.CompuTrabajo);

            if (companyProduct.GroupId == (short)ProductGroupsEnum.Membership ||
                company.CompanyStatusId == (short)CompanyStatusEnum.Discarted
                || companyProduct.IsBlockedByOldMembership
                && portalConfig.blockOldMembership)
            {
                if (company.CompanyStatusId == (short)CompanyStatusEnum.Discarted)
                    return Url.Action("Index", "CompanyRejected");
                else
                    return Url.Action("Index", "Home");
            }

            return string.Empty;
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Index(CompanyCartDataModel cartModel, string ov)
        {
            try
            {
                var portalConfig = PortalConfigurationService.GetPortalConfiguration();
                var companyCredentials = SecurityHelper.GetCompanyCredentials();

                return CheckModelAndMultiPurchaseProducts(cartModel, ov, portalConfig, companyCredentials);
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyCartController -- Index-Post {ex}");
                ExceptionPublisherService.Publish(ex, "MultiPurchaseCartController", "Index-Post");
                return RedirectToAction("Index", "Home");
            }
        }

        private bool IsValidPayWithTokenReusable(PaymentOriginEnum paymentMethod, string IdTokenSelected, bool featurePortalConfigShowCompanyCardTokenActivate,
                                                bool featurePortalConfigActiveRecurrentPayment)
        {
            return (paymentMethod == PaymentOriginEnum.PaymentsTPV &&
                    featurePortalConfigActiveRecurrentPayment &&
                    featurePortalConfigShowCompanyCardTokenActivate &&
                    !String.IsNullOrEmpty(IdTokenSelected));
        }

        private ActionResult CheckModelAndMultiPurchaseProducts(CompanyCartDataModel cartModel, string ov, PortalConfig portalConfig, CompanyCredentials companyCredentials)
        {
            if (cartModel == null
                || cartModel.EditDataCompanyCart == null
                || cartModel.ProductsListSelected == null)
            {
                TraceCartModelWithNullObject(cartModel, portalConfig, companyCredentials.IdCompany);
                return RedirectToAction("Index", "Home");
            }

            if (!ModelState.IsValid)
            {
                TraceModelStateIsInvalid(portalConfig, companyCredentials.IdCompany);
                return RedirectToAction("Index", "Home");
            }

            if (_companyCheckIdentifierService.CheckIdentifier(cartModel.EditDataCompanyCart.Nit, cartModel.EditDataCompanyCart.CountryIdSelected, portalConfig, companyCredentials.IdCompany, false) != (int)NitResultEnum.IncorrectNit)
            {
                return MultiPurchaseProducts(cartModel, ov, portalConfig, companyCredentials);
            }

            return TraceCartModelWithInvalidNIT(cartModel, portalConfig, companyCredentials.IdCompany);
        }

        private ActionResult TraceCartModelWithInvalidNIT(CompanyCartDataModel cartModel, PortalConfig portalConfig, int idCompany)
        {
            var extraData = new Dictionary<string, string>();
            extraData.Add("NIT", !string.IsNullOrEmpty(cartModel.EditDataCompanyCart.Nit) ? cartModel.EditDataCompanyCart.Nit : "vacío o nulo");
            extraData.Add("CountryIdSelected", cartModel.EditDataCompanyCart.CountryIdSelected.ToString());
            extraData.Add("IdCompany", idCompany.ToString());
            ExceptionPublisherService.Publish(new Exception("ERROR en Post al comprovar el NIT"), "MultiPurchaseCartController", "CheckModelAndMultiPurchaseProducts", false, extraData, portalConfig.PortalId);
            return RedirectToAction("Index", "Home");
        }

        private ActionResult MultiPurchaseProducts(CompanyCartDataModel cartModel, string ov, PortalConfig portalConfig, CompanyCredentials companyCredentials)
        {
            InvoiceEntity newInvoice = Mapper.Map<EditDataCompanyCartDataModel, InvoiceEntity>(cartModel.EditDataCompanyCart);
            var invoiceEntity = _invoiceService.GenerateNewInvoiceEntity(newInvoice, companyCredentials.IdCompany, portalConfig.PortalId);
            if (invoiceEntity.Id > 0)
            {
                MultiPaymentEntity multiPaymentEntity = LoadMultiPaymentEntity(cartModel, ov, invoiceEntity, companyCredentials, portalConfig);
                var purchaseOperation = _multipurchaseOperationService.ExecutePayment(multiPaymentEntity, TryGetVisitorSourceFromSession(), cartModel.BtnOriginId, cartModel.IdCartOriginTrack);

                if (purchaseOperation.IdPurchaseOperation > 0)
                {                    
                    MultiPurchaseProductsSectionHavePostPublish(portalConfig, companyCredentials, multiPaymentEntity, purchaseOperation);
                    MultiPurchaseProductsSectionConvertCompleteOffer(cartModel, portalConfig, companyCredentials);

                    if (IsValidPayWithTokenReusable((PaymentOriginEnum)multiPaymentEntity.PaymentMethod, cartModel.EditDataCompanyCart?.IdTokenEncryptedSelected, portalConfig.AEPortalConfig.ShowCompanyCardToken, portalConfig.AEPortalConfig.ActiveRecurrentPayment))
                    {
                        if (portalConfig.AEPortalConfig.AllowPayWithTokenReusableAutologinBackweb || !companyCredentials.IsAutologinBackweb)
                        {
                            return Redirect(PayMultiWithReusableTokenGetUrlFinal(multiPaymentEntity, purchaseOperation, cartModel.EditDataCompanyCart.IdTokenEncryptedSelected, portalConfig, companyCredentials));
                        }
                        else
                        {
                            var extraData = new Dictionary<string, string>
                            {
                                { "InvoiceId", invoiceEntity.Id.ToString() },
                                { "CompanyId", companyCredentials.IdCompany.ToString() },
                                { "IdOperation", purchaseOperation.IdPurchaseOperation.ToString() },
                                { "IdToken", cartModel.EditDataCompanyCart.IdTokenEncryptedSelected },
                                { "IsAutologinBackweb", companyCredentials.IsAutologinBackweb.ToString() },
                                { "AEPortalConfig.AllowPayWithTokenReusableAutologinBackweb", portalConfig.AEPortalConfig.AllowPayWithTokenReusableAutologinBackweb.ToString() }
                            };
                            ExceptionPublisherService.Publish(new Exception("Pago abortado y operación de compra creada con AutologinBackweb y tarjeta guardada"), "MultiPurchaseCartController", "MultiPurchaseProducts",
                                                   false, extraData, portalConfig.PortalId);
                        }
                    }

                    var cartHasAutomaticPromotions = HasAutomaticPromotions(cartModel.PageId, portalConfig, companyCredentials.IdCompany);
                    return Redirect(RedirectMultiToPayMethod(multiPaymentEntity, purchaseOperation, cartHasAutomaticPromotions, cartModel.PageId, portalConfig, companyCredentials));
                }

                ExceptionPublisherService.Publish(new Exception("No se creo la operación de compra en Post"), "MultiPurchaseCartController", "MultiPurchaseProducts",
                                       false, new Dictionary<string, string>() { { "InvoiceId", invoiceEntity.Id.ToString() } }, portalConfig.PortalId);
                return RedirectToAction("Index", "Home");
            }

            ExceptionPublisherService.Publish(new Exception("No se creo la InvoiceEntity en Post"), "MultiPurchaseCartController", "MultiPurchaseProducts",
                                      false, new Dictionary<string, string>() { { "companyId", companyCredentials.IdCompany.ToString() } }, portalConfig.PortalId);
            return RedirectToAction("Index", "Home");
        }

        private void MultiPurchaseProductsSectionConvertCompleteOffer(CompanyCartDataModel cartModel, PortalConfig portalConfig, CompanyCredentials companyCredentials)
        {
            if (!string.IsNullOrEmpty(cartModel.IsConvertToComplete))
            {
                _kpiService.Add((short)KpiEnum.CONVERT_TO_COMPLETE_PAID, portalConfig.PortalId);
            }
        }

        private void MultiPurchaseProductsSectionHavePostPublish(PortalConfig portalConfig, CompanyCredentials companyCredentials, MultiPaymentEntity multiPaymentEntity, MultiPurchaseOperationEntity purchaseOperation)
        {
            if (portalConfig.AEPortalConfig.HavePostPublish && multiPaymentEntity.OfferId > 0)
            {
                string offerIdEnc = _encryptionService.Encrypt(multiPaymentEntity.OfferId.ToString());
                string idPurchaseOperationEnc = _encryptionService.Encrypt(purchaseOperation.IdPurchaseOperation.ToString());
                _multipurchaseOperationService.SavePostPublishTraceOffer(idPurchaseOperationEnc, offerIdEnc, portalConfig.PortalId);
                _multipurchaseOperationService.SaveIdPurchaseOperationInPurchaseProductList(multiPaymentEntity.OfferId, companyCredentials.IdCompany, portalConfig.PortalId, purchaseOperation.IdPurchaseOperation);
            }
        }

        private void TraceModelStateIsInvalid(PortalConfig portalConfig, int idCompany)
        {
            var extraData = new Dictionary<string, string>();
            extraData.Add("IdCompany", idCompany.ToString());
            var index = 0;
            ModelState.Values.ForEach(f => { f.Errors.ForEach(e => extraData.Add(ModelState.Keys.ToList()[index], e.ErrorMessage)); index++; });
            ExceptionPublisherService.Publish(new Exception("Post:ModelState is invalid"), "MultiPurchaseCartController", "CheckModelAndMultiPurchaseProducts", false, extraData, portalConfig.PortalId);
        }

        private void TraceCartModelWithNullObject(CompanyCartDataModel cartModel, PortalConfig portalConfig, int idCompany)
        {
            var extraData = new Dictionary<string, string>();
            extraData.Add("cartModel", cartModel == null ? "null" : "correct");
            extraData.Add("IdCompany", idCompany.ToString());

            if (cartModel != null)
            {
                extraData.Add("cartModel.ProductsListSelected", string.IsNullOrEmpty(cartModel.ProductsListSelected) ? "null or empty" : "correct");
                extraData.Add("cartModel.EditDataCompanyCart", cartModel.EditDataCompanyCart == null ? "null" : "correct");
            }

            ExceptionPublisherService.Publish(new Exception("CartModel en Post tiene objetos nulos o es nulo"), "MultiPurchaseCartController", "CheckModelAndMultiPurchaseProducts", false, extraData, portalConfig.PortalId);
        }

        private string PayMultiWithReusableTokenGetUrlFinal(MultiPaymentEntity multiPaymentEntity, MultiPurchaseOperationEntity multipurchaseOperation, string idTokenEncrypted, PortalConfig portalConfig, CompanyCredentials companyCredentials)
        {
            bool isValid = true;
            OperationPaymentCompleteResponseModel responsePayRecurrent = new OperationPaymentCompleteResponseModel();

            try
            {

                if (multiPaymentEntity.PaymentMethod != (int)PaymentOriginEnum.PaymentsTPV) isValid = false;
                if (String.IsNullOrEmpty(idTokenEncrypted)) isValid = false;

                if (!isValid)
                {
                    responsePayRecurrent.Error = true;
                    responsePayRecurrent.ErrorMessage = "model not valid";
                }
                else
                {

                    Int32.TryParse(_encryptionService.Decrypt(idTokenEncrypted), out var idToken);
                    responsePayRecurrent = _paymentService.PayMultiOperationWithTokenRecurrent(portalConfig, companyCredentials, multiPaymentEntity, multipurchaseOperation, idToken);

                    if (!responsePayRecurrent.Error && !String.IsNullOrEmpty(responsePayRecurrent.UrlOk)) return responsePayRecurrent.UrlOk;

                    if (responsePayRecurrent.Error && !String.IsNullOrEmpty(responsePayRecurrent.UrlKO)) return responsePayRecurrent.UrlKO;
                }

                return Url.Action("Error", "CompanyCartMessagesPayment",
                               new
                               {
                                   ipo = _encryptionService.Encrypt(multipurchaseOperation.IdPurchaseOperation.ToString()),
                                   message = responsePayRecurrent.ErrorMessage
                               });
            }
            catch (Exception ex)
            {

                var extradata = new Dictionary<string, string>();
                extradata.Add("IdPurchaseOperation", multipurchaseOperation?.IdPurchaseOperation.ToString());
                extradata.Add("responsePayRecurrent.ErrorMessage", responsePayRecurrent.ErrorMessage);

                ExceptionPublisherService.Publish(ex, "MultiPurchaseCartController", "PayMultiWithReusableTokenGetUrlFinal", false, extradata);

                return Url.Action("Error", "CompanyCartMessagesPayment",
                              new
                              {
                                  ipo = _encryptionService.Encrypt(multipurchaseOperation?.IdPurchaseOperation.ToString()),
                                  message = responsePayRecurrent.ErrorMessage
                              });
            }
        }

        private string RedirectMultiToPayMethod(MultiPaymentEntity multiPaymentEntity, MultiPurchaseOperationEntity multipurchaseOperation, bool cartHasAutomaticPromotion, int idPage, PortalConfig portalConfig, CompanyCredentials companyCredentials)
        {
            switch (multiPaymentEntity.PaymentMethod)
            {

                case (int)PaymentOriginEnum.PayU:
                    PurchaseOperationPayUEntity purchaseOperationPayU = _purchaseOperationPayUService.LoadPayUEntity(multipurchaseOperation, multiPaymentEntity, companyCredentials.UserId, portalConfig);
                    _multipurchaseOperationService.GetPayUURLPayment(portalConfig, purchaseOperationPayU, multiPaymentEntity.CompanyId, multiPaymentEntity.UserId);

                    var payUModel = new CompanyCartPaymentFormDataModel();

                    LoadModelPaymentForm(purchaseOperationPayU, payUModel, multipurchaseOperation.GetProductsDescription(), portalConfig, companyCredentials);

                    if (companyCredentials.UserId > 0)
                        if (purchaseOperationPayU.IsCaixa || purchaseOperationPayU.IsPayU)
                            return Url.Action("Index", "CompanyCartPaymentForm", payUModel);

                    return Url.Action("Index", "Company");


                case (int)PaymentOriginEnum.PayPal:
                    PurchaseOperationPayPalEntity purchaseOperationPayPal = _purchaseOperationPayPalService.LoadPayPalEntity(multipurchaseOperation, multiPaymentEntity, companyCredentials.UserId);
                    _multipurchaseOperationService.GetPayPalURLPayment(portalConfig, purchaseOperationPayPal, companyCredentials.IdCompany, companyCredentials.UserId);
                    return Url.Action("Index", "PayPalMultiPurchase", new
                    {
                        orderNumber = EncryptationHelper.Encrypt(purchaseOperationPayPal.IdPurchaseOperation.ToString()),
                        idUser = EncryptationHelper.Encrypt(companyCredentials.UserId.ToString()),
                        idOffer = EncryptationHelper.Encrypt(purchaseOperationPayPal.OfferId.ToString())
                    });


                case (int)PaymentOriginEnum.Transferencia:
                    //_purchaseOperationService.SetTransferURLPayment(purchaseOperation.IdPurchaseOperation, paymentEntity.PriceSelected, paymentEntity.OfferId, _portalConfig);
                    _multipurchaseOperationService.SetTransferURLPayment(multipurchaseOperation.IdPurchaseOperation, multipurchaseOperation.Price, multiPaymentEntity.OfferId, portalConfig, companyCredentials.IdCompany, companyCredentials.UserId);

                    if (cartHasAutomaticPromotion)
                        ProductService.ExpiredAutomaticPromotions(multipurchaseOperation.Idportal, multipurchaseOperation.Idcompany);

                    return Url.Action("Index", "CompanyCartTransferPayment");

                case (int)PaymentOriginEnum.WorldPay:

                    if (portalConfig.WorldPayByToken)
                    {
                        //MQC 2021-12-21 ni AR ni Bestjobs pagan con Token. Quitar porque sino hay que implementar más lógica 
                        //tb se podría quitar este condicionante, lo dejo porque en el single purchase esta puesto..                      
                        return Url.Action("Index", "MultiPurchaseCartMessages",
                              new
                              {
                                  hasError = 1,
                                  wp = 1,
                                  message = "Wrong configuration: no exists implementation by _portalConfig.WorldPayByToken",
                                  Ds_order = _encryptionService.Encrypt(multipurchaseOperation.IdPurchaseOperation.ToString())
                              });
                    }
                    else
                    {
                        PurchaseOperationWorldPayEntity purchaseOperationWorldPay = _purchaseOperationWorldpayService.LoadWorldPayEntity(multiPaymentEntity, multipurchaseOperation);

                        LoadResponseUrlsWorldPay(multipurchaseOperation, purchaseOperationWorldPay);

                        if (!_multipurchaseOperationService.GetWorldPayURLPayment(portalConfig, purchaseOperationWorldPay))
                            return Url.Action("Index", "MultiPurchaseCartMessages",
                                new
                                {
                                    hasError = 1,
                                    wp = 1,
                                    Ds_order = _encryptionService.Encrypt(multipurchaseOperation.IdPurchaseOperation.ToString())
                                });
                    }
                    break;

                case (int)PaymentOriginEnum.PaymentsTPV:


                    var urlPaymentsTPV = _paymentService.GetUrlFromMultiPaymentResponse(portalConfig, companyCredentials, multipurchaseOperation, multiPaymentEntity);
                    if (string.IsNullOrEmpty(urlPaymentsTPV))
                    {
                        return Url.Action("Index", "MultiPurchaseCartMessages",
                                new
                                {
                                    hasError = 1,
                                    wp = 1,
                                    Ds_order = _encryptionService.Encrypt(multipurchaseOperation.IdPurchaseOperation.ToString())
                                });
                    }
                    else
                        return urlPaymentsTPV;

            }

            return string.Empty;
        }


        #region "Other methods different to Payments"


        private void LoadResponseUrlsWorldPay(MultiPurchaseOperationEntity purchaseOperation, PurchaseOperationWorldPayEntity purchaseOperationWorldPay)
        {
            var IdOperationEncrypted = _encryptionService.Encrypt(purchaseOperation.IdPurchaseOperation.ToString());
            purchaseOperationWorldPay.McReturnPageC = $@"{PortalConfigurationService.GetPortalConfiguration().url_empresa}{Url.Action("Index", "MultiPurchaseCartMessages",
                                                                                                                                new
                                                                                                                                {
                                                                                                                                    hasError = 1,
                                                                                                                                    wp = 1,
                                                                                                                                    Ds_order = IdOperationEncrypted
                                                                                                                                })}";
            purchaseOperationWorldPay.McReturnPageY = $@"{PortalConfigurationService.GetPortalConfiguration().url_empresa}{Url.Action("Index", "MultiPurchaseCartMessages",
                                                                                                                                new
                                                                                                                                {
                                                                                                                                    wp = 1,
                                                                                                                                    Ds_order = IdOperationEncrypted
                                                                                                                                })}";

            purchaseOperationWorldPay.McCallback = $"{PortalConfigurationService.GetPortalConfiguration().url_empresa}{Url.Action("CallBackRBS", "MultiPurchaseCartMessages", new { id = IdOperationEncrypted })}";

            Trace.TraceInformation($"Callback {purchaseOperationWorldPay.McCallback}");
        }

        private void LoadModelPaymentForm(PurchaseOperationPayUEntity purchaseOperationPayU, CompanyCartPaymentFormDataModel payUModel, string productDescription, PortalConfig portalConfig, CompanyCredentials companyCredentials)
        {
            var paymentConfig = _paymentPayUService.GetPaymentPayUByPortal(portalConfig.PortalId);
            payUModel.PayUUrl = paymentConfig.PayuUrl;
            payUModel.PayUMerchandtId = paymentConfig.PayuMerchantId;
            payUModel.PayUApiKey = paymentConfig.PayuApiKey;
            payUModel.AccountId = paymentConfig.PayuAccountId;
            payUModel.Currency = paymentConfig.PayuCurrency;
            payUModel.Test = paymentConfig.PayuTitular.ToString();
            payUModel.Desciption = productDescription;
            payUModel.ResponseUrl = $"{PortalConfigurationService.GetPortalConfiguration().url_empresa}{SetResponseUrlPayU(portalConfig, purchaseOperationPayU)}";
            payUModel.ConfirmationUrl = $"{PortalConfigurationService.GetPortalConfiguration().url_empresa}{SetConfirmResponseUrlPayU(purchaseOperationPayU)}";
            payUModel.Amount = purchaseOperationPayU.Amount;
            payUModel.RefVenta = purchaseOperationPayU.IdPurchaseOperation.ToString();
            payUModel.ReferenceCode = purchaseOperationPayU.IdPurchaseOperation;
            payUModel.BuyerEmail = companyCredentials.Email;
            payUModel.Tax = purchaseOperationPayU.Tax.ToString();
            payUModel.PayerDocument = purchaseOperationPayU.Nit;
            payUModel.ShowNitWithValueInPayU = portalConfig.AEPortalConfig.ShowNitWithValueInPayU;
            if (companyCredentials.UserId > 0)
            {
                if (purchaseOperationPayU.IsCaixa)
                {
                }
                else if (purchaseOperationPayU.IsPayU)
                    payUModel.Signature = _purchaseOperationPayUService.PayUSignature(paymentConfig, purchaseOperationPayU);
            }
            Trace.TraceInformation($"ResponseURL: {payUModel.ResponseUrl}");

            var environment = _configurationService.AppSettings["ENVIRONMENT"]?.ToString();
            if (payUModel.Test != "0" && environment == "DEVELOPMENT")
            {
                var urlTest = _configurationService.AppSettings["PAYU_URLTEST"]?.ToString();
                payUModel.ResponseUrl = $"{urlTest}{SetResponseUrlPayU(portalConfig, purchaseOperationPayU)}";
                payUModel.ConfirmationUrl = $"{urlTest}{SetConfirmResponseUrlPayU(purchaseOperationPayU)}";
                payUModel.BuyerEmail = _configurationService.AppSettings["PAYU_MAILTEST"]?.ToString();
            }
        }

        private string SetResponseUrlPayU(PortalConfig portalConfig, PurchaseOperationPayUEntity purchaseOperationPayU)
        {

            if (purchaseOperationPayU.IsPayU)
                return $"{Url.Action("PayUResponse", "MultiPurchaseCartMessages")}?{QueryStringParams.PURCHASE_OPERATION_ID}={_encryptionService.Encrypt(purchaseOperationPayU.IdPurchaseOperation.ToString())}";

            return Url.Action("Index", "Company");
        }

        private string SetConfirmResponseUrlPayU(PurchaseOperationPayUEntity purchaseOperationPayU)
        {
            if (purchaseOperationPayU.IsPayU)
            {
                return $"{Url.Action("PayUConfirmationResponse", "MultiPurchaseCartMessages")}";
            }
            else
            {
                return string.Empty;
            }
        }

        #endregion



        private string FillHeaderText(int idBtnOrigin, PortalConfig portalConfig, short subGroupProduct)
        {
            switch (idBtnOrigin)
            {
                case (int)TpvButtonOriginEnum.OfferPublishFlashOffer:
                    return PageLiteralsHelper.GetLiteral("LIT_HEADER_FLASH", (int)PageEnum.PackCart, portalConfig);
                case (int)TpvButtonOriginEnum.OfferPublishHiddenCompanyOffer:
                    return PageLiteralsHelper.GetLiteral("LIT_HEADER_HIDDENCOMPANY", (int)PageEnum.PackCart, portalConfig);
                case (int)TpvButtonOriginEnum.OfferPublishEmailOffer:
                    return PageLiteralsHelper.GetLiteral("LIT_HEADER_EMAIL", (int)PageEnum.PackCart, portalConfig);
                case (int)TpvButtonOriginEnum.OfferPublishPhoneOffer:
                    return PageLiteralsHelper.GetLiteral("LIT_HEADER_PHONE", (int)PageEnum.PackCart, portalConfig);
                case (int)TpvButtonOriginEnum.OfferPublishAddressOffer:
                    return PageLiteralsHelper.GetLiteral("LIT_HEADER_ADRESS", (int)PageEnum.PackCart, portalConfig);
                case (int)TpvButtonOriginEnum.OfferListHighlightOffer:
                case (int)TpvButtonOriginEnum.OfferPublishHighlightOffer:
                    return PageLiteralsHelper.GetLiteral("LIT_HEADER_HIGHLIGHT", (int)PageEnum.PackCart, portalConfig);
                case (int)TpvButtonOriginEnum.OfferListUrgentOffer:
                case (int)TpvButtonOriginEnum.OfferPublishUrgentOffer:
                    return PageLiteralsHelper.GetLiteral("LIT_HEADER_URGENT", (int)PageEnum.PackCart, portalConfig);
                case (int)TpvButtonOriginEnum.OfferListEditOffer:
                    return PageLiteralsHelper.GetLiteral("LIT_HEADER_EDIT", (int)PageEnum.PackCart, portalConfig);
                case (int)TpvButtonOriginEnum.PrivateHomePublish:
                case (int)TpvButtonOriginEnum.HeaderMenuPublish:
                    return PageLiteralsHelper.GetLiteral("LIT_HEADER_PUBLISH", (int)PageEnum.PackCart, portalConfig);
                case (int)TpvButtonOriginEnum.OfferListConvertToPack:
                case (int)TpvButtonOriginEnum.PrivateHomeOfferListConvertToPack:
                case (int)TpvButtonOriginEnum.OfferSearchCVConvertToPack:
                case (int)TpvButtonOriginEnum.OfferMatchesListConvertToPack:
                    return PageLiteralsHelper.GetLiteral("LIT_HEADER_CONVERT", (int)PageEnum.PackCart, portalConfig);
                case (int)TpvButtonOriginEnum.OfferMatchesDetailConvertToPack:
                    return PageLiteralsHelper.GetLiteral("LIT_HEADER_CONVERT_CVDETAIL", (int)PageEnum.PackCart, portalConfig);
                case (int)TpvButtonOriginEnum.PostPublishStepTwoBuy:
                    return PageLiteralsHelper.GetLiteral("LIT_CONSUMIBLES_TITLE", (int)PageEnum.PackCart, portalConfig);
                default:
                    switch (subGroupProduct)
                    {
                        case (short)ProductSubGroupsEnum.Standard:
                            return PageLiteralsHelper.GetLiteral("LIT_TITLE_STANDARD", (int)PageEnum.PackCart, portalConfig);
                        case (short)ProductSubGroupsEnum.Advanced:
                            return PageLiteralsHelper.GetLiteral("LIT_TITLE_ADVANCED", (int)PageEnum.PackCart, portalConfig);
                        case (short)ProductSubGroupsEnum.Premium:
                            return PageLiteralsHelper.GetLiteral("LIT_HEADER_PREMIUM", (int)PageEnum.PackCart, portalConfig);
                        default:
                            return PageLiteralsHelper.GetLiteral("LIT_HEADER_DEFAULT", (int)PageEnum.PackCart, portalConfig);
                    }
            }
        }

        private string isConvertToComplete(int idBtnOrigin, string cc)
        {
            switch (idBtnOrigin)
            {
                case (int)TpvButtonOriginEnum.OfferListConvertToPack:
                case (int)TpvButtonOriginEnum.PrivateHomeOfferListConvertToPack:
                case (int)TpvButtonOriginEnum.OfferSearchCVConvertToPack:
                case (int)TpvButtonOriginEnum.OfferMatchesListConvertToPack:
                case (int)TpvButtonOriginEnum.OfferMatchesDetailConvertToPack:
                    return _encryptionService.Encrypt("1");
                default:
                    return cc;
            }
        }

        private string GetFirstPayMethod(PortalConfig portalConfig)
        {
            string payMethod = portalConfig.AllowedPayments.First();

            switch (payMethod.ToLower().Trim())
            {
                case "lacaixa":
                    return PageLiteralsHelper.GetLiteral("LIT_TARJETA_CREDITO", (int)PageEnum.PackCart, portalConfig);
                case "worldpay":
                    return PageLiteralsHelper.GetLiteral("LIT_PAGO_TARJETA", (int)PageEnum.PackCart, portalConfig);
                case "payu":
                    return PageLiteralsHelper.GetLiteral("LIT_OTRAS_FORMAS_PAGO", (int)PageEnum.PackCart, portalConfig);
                case "transferencia":
                    return PageLiteralsHelper.GetLiteral("LIT_TRANSFERENCIA", (int)PageEnum.PackCart, portalConfig);
                default:
                    return string.Empty;
            }
        }

        private void SumKPIStartPurchase(string startPurchaseOrigin, short portalId, int companyId, long userId)
        {
            if (checkStartPurchase(startPurchaseOrigin))
            {
                short.TryParse(startPurchaseOrigin, out var result);
                _kpiService.AddSumBlock(result, portalId, 1);
            }
        }

        private bool checkStartPurchase(string startPurchaseOrigin)
        {
            return !string.IsNullOrEmpty(startPurchaseOrigin) &&
                   (startPurchaseOrigin == ((int)KpiEnum.START_PURCHASE_FROM_POPUP_AUTOMATIC_PROMOTION).ToString()
                   || startPurchaseOrigin == ((int)KpiEnum.START_PURCHASE_BOX_PROMOTION_HOME).ToString()
                   || startPurchaseOrigin == ((int)KpiEnum.START_PURCHASE_LANDING_PROMOTION).ToString()
                   || startPurchaseOrigin == ((int)KpiEnum.START_PURCHASE_PUBLISHED_PAGE).ToString());
        }

        private void LoadProductsCart(string prod, string rm, CompanyCartDataModel cartDataModel, CompanyEntity company, short subGroup, string startPurchaseOrigin, string idOffer, PortalConfig portalConfig, TestABEnum testABEnum)
        {            
            cartDataModel.CartProducts = GetContentPackProductsByPage(cartDataModel, company, subGroup, portalConfig, company.Id, testABEnum);
            if (!cartDataModel.CartProducts.Any())
            {
                return;
            }
            SetHasOnePlusOnePackProduct(cartDataModel.CartProducts, company.Id, portalConfig);

            List<ProductFeatureDescriptionEntity> productFeaturesDescriptions = new List<ProductFeatureDescriptionEntity>();
            if (subGroup == 0)
            {
                int.TryParse(_encryptionService.Decrypt(prod), out int idProduct);
                productFeaturesDescriptions = ProductService.GetProductFeaturesDescriptionsByIdProduct(idProduct, portalConfig.PortalId);
                if (productFeaturesDescriptions.Any())
                {
                    productFeaturesDescriptions = productFeaturesDescriptions.Where(x => x.LandingId == (short)PlaceLandingEnum.CartfeaturesDescriptions).ToList();
                }
            }
            else
            {
                productFeaturesDescriptions = ProductService
                .GetSubGroupFeaturesDescriptions(subGroup, portalConfig.PortalId)
                .Where(x => x.LandingId == (short)PlaceLandingEnum.CartfeaturesDescriptions).ToList();
            }

            cartDataModel.FeaturesDescription = Mapper.Map<List<ProductFeatureDescriptionEntity>, List<ProductFeatureDescriptionDataModel>>(productFeaturesDescriptions);

            var isConvertToComplete = _encryptionService.Decrypt(cartDataModel.IsConvertToComplete) == "1";

            if (!isConvertToComplete &&
                portalConfig.AEPortalConfig.PostPublishWithConsumables)
            {
                cartDataModel.PurchaseProductsList = GetPurchaseProductsList(idOffer, prod,
                    cartDataModel.CartProducts, startPurchaseOrigin, company.Id, portalConfig.PortalId);
            }
            else
            {
                LoadProductSelected(cartDataModel, prod, portalConfig);

                if (isConvertToComplete && !portalConfig.AEPortalConfig.ShowAllProdsToConvToPay)
                {
                    cartDataModel.CartProducts.RemoveAll(x => x.Product.Id != cartDataModel.ProducSelected.ProductId);
                }
            }

            cartDataModel.IsBBDDCvProduct = cartDataModel.CartProducts.Exists(x => x.Product.SubGroupId == (short)ProductSubGroupsEnum.BBDDcv);
        }

        private void TraceCartWithoutProducts(int idPage, short subGroup, PortalConfig portalConfig, CompanyEntity company)
        {
            var extradata = new Dictionary<string, string>();
            extradata.Add("IdPage", idPage.ToString());
            extradata.Add("SubGroup", subGroup.ToString());
            extradata.Add("UrlReferrer", HttpContext?.Request?.UrlReferrer?.AbsoluteUri ?? "Sin urlReferrer");
            extradata.Add("IdCompany", company.Id.ToString());

            ExceptionPublisherService.Publish(new Exception($"ERROR carrito sin productos"), "MultiPurchaseCartController", "LoadProductsCart", false, extradata, portalConfig.PortalId);
        }

        private void TraceDirectBBDDWithoutProducts(int idPage, short subGroup, PortalConfig portalConfig, CompanyEntity company, bool isConsumables)
        {

            var extradata = new Dictionary<string, string>();
            extradata.Add("IdPage", isConsumables ? ((int)PageEnum.ConsumablesCart).ToString() : idPage.ToString());
            extradata.Add("SubGroup", subGroup.ToString());
            extradata.Add("IdPortal", portalConfig.PortalId.ToString());
            extradata.Add("IsConsumables", isConsumables ? "Sí" : "No");

            ExceptionPublisherService.Publish(new Exception($"ERROR BBDD no devuelve productos en el carrito, se adjuntan los parámetros de entrada que usa el stored procedure"), "MultiPurchaseCartController", "GetContentPackProductsByPage", false, extradata, portalConfig.PortalId);
        }

        private void SetHasOnePlusOnePackProduct(List<CompanyCartProductDataModel> cartProducts, int idCompany, PortalConfig portalConfig)
        {
            var productOnePlusOne = cartProducts.FirstOrDefault(x =>
                                x.Product.Features.Any(y =>
                                    y.AmbitId == (int)ProductAmbitEnum.Offer && y.InitialUnits == 2));
            if (portalConfig.AEPortalConfig.HasOnePlusOnePack &&
                !CompanyProductService.CompanyHasMoreThanOneContractedProduct(idCompany, portalConfig.PortalId))
            {
                var productOneUnit = cartProducts.FirstOrDefault(x =>
                            x.Product.Features.Any(y =>
                                y.AmbitId == (int)ProductAmbitEnum.Offer && y.InitialUnits == 1));

                if (productOneUnit != null)
                {
                    cartProducts.Remove(productOneUnit);
                }
                if (productOnePlusOne != null)
                {
                    productOnePlusOne.Product.Default = true;
                }
            }
            else if (productOnePlusOne != null)
            {
                cartProducts.Remove(productOnePlusOne);
            }
        }

        private void LoadEditDataCompanyCart(string oi, CompanyCartDataModel cartDataModel, CompanyEntity company, PortalConfig portalConfig)
        {
            cartDataModel.EditDataCompanyCart = Mapper.Map<CompanyEntity, EditDataCompanyCartDataModel>(company);
            cartDataModel.EditDataCompanyCart.OfferId = !string.IsNullOrEmpty(oi) ? _encryptionService.Decrypt(oi).ToInt() : 0;
            cartDataModel.EditDataCompanyCart.Countries = GetFromDictionary(portalConfig, DictionaryEnum.COUNTRY);
            cartDataModel.EditDataCompanyCart.LitPrice = ProductService.SetCurrency(cartDataModel.Price, portalConfig);
            cartDataModel.EditDataCompanyCart.LitPriceWithVAT = ProductService.SetCurrency(cartDataModel.PriceWithVAT, portalConfig);
            cartDataModel.EditDataCompanyCart.ProductName = cartDataModel.ProducSelected?.ComercialName ?? "";
            cartDataModel.EditDataCompanyCart.HasVat = portalConfig.EnableBreakDownPrices && cartDataModel.VATNumber > 0;
            if (cartDataModel.ShowProductFeatures)
                cartDataModel.EditDataCompanyCart.HasVat = false;

            cartDataModel.EditDataCompanyCart.IsCompanyInCRM = !string.IsNullOrEmpty(CompanyService.GetCompanyCRMNavisionId(company.Id, portalConfig.PortalId));

            var userId = company.User?.Id ?? 0;
            cartDataModel.EditDataCompanyCart.CompanyCard = CompanyCardTokenMapping.Map(_paymentService.GetCompanyCardDefault(company.Id, portalConfig.PortalId, (int)userId), _encryptionService);

            var ratedPrice = CompanyService.GetRatedPricesByPrice(1, portalConfig.countryId);
            if (ratedPrice.VATNumber > 0)
            {
                cartDataModel.EditDataCompanyCart.PriceWithoutTaxes = false;
                cartDataModel.EditDataCompanyCart.PriceWithTaxes = true;
                cartDataModel.EditDataCompanyCart.InfoPago = "total_pago iva";
                cartDataModel.EditDataCompanyCart.LitVatNumber = $"{ratedPrice.VATNumber} %";
                cartDataModel.EditDataCompanyCart.LitVatLiteral = ratedPrice.VATLiteral;
            }
            else
            {
                cartDataModel.EditDataCompanyCart.PriceWithoutTaxes = true;
                cartDataModel.EditDataCompanyCart.PriceWithTaxes = false;
            }
        }

        private static void LoadStyleCart(CompanyCartDataModel cartModel)
        {
            if (cartModel.CartProducts.Any())
            {
                if (cartModel.CartProducts.Find(x => x.Product.GroupId == (short)ProductGroupsEnum.Membership) != null)
                {
                    cartModel.TypeCart = (int)TypeCartEnum.MemberShipCart;
                    cartModel.ClassRepeaterTypeCart = "membresia";
                }
                else
                {
                    cartModel.TypeCart = (int)TypeCartEnum.PackCart;
                    cartModel.ClassRepeaterTypeCart = "packs";
                }
            }
        }

        private void LoadProductSelected(CompanyCartDataModel cartModel, string prod, PortalConfig portalConfig)
        {
            var idProductSelected = 0;
            decimal price = 0;

            if (!string.IsNullOrEmpty(prod))
                int.TryParse(_encryptionService.Decrypt(prod), out idProductSelected);

            int productPreSelected = 0;

            if (portalConfig.AEPortalConfig.ShowAllProdsToConvToPay)
            {
                productPreSelected = cartModel.CartProducts.Find(x => x.Product.Default == true)?.Product.Id ?? cartModel.CartProducts[0].Product.Id;
            }
            else
            {
                if (cartModel.CartProducts.Exists(x => x.Product.Id == idProductSelected))
                {
                    productPreSelected = idProductSelected;
                }
                else
                {
                    productPreSelected = cartModel.CartProducts.Find(x => x.Product.Default == true)?.Product.Id ?? cartModel.CartProducts[0].Product.Id;
                }
            }

            if (productPreSelected > 0)
            {
                CheckCartProduct(cartModel.CartProducts, productPreSelected);

                var selectedProductTemporality = cartModel.CartProducts.Find(x => x.Product.Id == productPreSelected)?.Product.TemporalityId ?? (int)TemporalityEnum.Year;

                cartModel.ProducSelected = Mapper.Map<ProProductEntity, ProProductDataModel>(_proProductService.GetProProduct(portalConfig.PortalId, productPreSelected, selectedProductTemporality));
                cartModel.LitProductSelected = cartModel.ProducSelected?.ComercialName ?? string.Empty;

                if (cartModel.CartProducts.Exists(c => c.Product.HasPromotionByProduct && c.Product.Id == productPreSelected))
                    decimal.TryParse(cartModel.CartProducts.FirstOrDefault(c => c.Product.HasPromotionByProduct && c.Product.Id == productPreSelected).Temporalities.FirstOrDefault().NumericPrice.ToString(), out price);
                else
                    price = cartModel.ProducSelected?.Price ?? 0;
            }

            FillPricesProduct(cartModel, price, portalConfig);
        }

        private void FillPricesProduct(CompanyCartDataModel cartModel, decimal price, PortalConfig portalConfig)
        {
            var ratedPrice = CompanyService.GetRatedPricesByPrice(price, portalConfig.countryId);

            cartModel.Price = ratedPrice.Price;
            cartModel.PriceWithVAT = ratedPrice.PriceWithVAT;
            cartModel.RenewalPriceWithVAT = ratedPrice.RenewalPriceWithVAT;
            cartModel.RenewPrice = ratedPrice.RenewPrice;
            cartModel.VATNumber = ratedPrice.VATNumber;
            cartModel.VATLiteral = ratedPrice.VATLiteral;
            if (cartModel.CartProducts != null && cartModel.CartProducts.Any())
                cartModel.CartProducts.ForEach(p => p.HasVat = portalConfig.EnableBreakDownPrices && cartModel.VATNumber > 0);
        }

        private List<CompanyCartProductDataModel> GetContentPackProductsByPage(CompanyCartDataModel cartDataModel, CompanyEntity company, short subGroup, PortalConfig portalConfig, int idCompany, TestABEnum testABEnum)
        {
            var idPage = portalConfig.AEPortalConfig.PostPublishWithConsumables ? (int)PageEnum.ConsumablesCart : (int)PageEnum.PackCart;
            var productsPage = GetAllProductsByPage(idPage, subGroup, portalConfig, idCompany, testABEnum);

            cartDataModel.PageId = idPage;
            if (productsPage == null || !productsPage.Any())
            {
                if (portalConfig.AEPortalConfig.PostPublishWithConsumables)
                {
                    TraceDirectBBDDWithoutProducts(idPage, subGroup, portalConfig, company, portalConfig.AEPortalConfig.PostPublishWithConsumables);
                }
                else
                {
                    TraceCartWithoutProducts(idPage, subGroup, portalConfig, company);
                }
                return new List<CompanyCartProductDataModel>();
            }

            var productsPageFiltered = new List<ProductEntity>();

            if (portalConfig.AEPortalConfig.LimitationPurchase)
            {
                productsPageFiltered = _landingProductsService.FilterProductsByArrayLimit(idCompany, portalConfig.PortalId, productsPage);
            }
            else
            {
                productsPageFiltered = productsPage;
            }

            var lst = new List<CompanyCartProductDataModel>();
            var position = 0;

            productsPageFiltered.GroupBy(group => group.Id).ToList().ForEach(x =>
            {
                x.ForEach(producto =>
                {
                    var product = lst.Where(q => q.Product.Id == producto.Id);
                    var data = Mapper.Map<ProductEntity, ProductDataModel>(producto);
                    var promotion = producto.Promotions.Any()
                                    && producto.Promotions.ToList().Exists(p => p.TemporalityId == data.TemporalityId)
                                    ? producto.Promotions.Where(p => p.TemporalityId == data.TemporalityId).FirstOrDefault()
                                    : new PromotionEntity();

                    data.HasPromotionByProduct = promotion.Id > 0;

                    decimal decimalPriceUnity = 0;
                    decimal.TryParse(promotion.LiteralPriceUnity, out decimalPriceUnity);
                    var hasPromotionWithoutVoucher = producto.HasPromotion && promotion.TypePromotion != (short)PromotionTypeEnum.VoucherPacks;
                    var totalPriceWithPromotion = CompanyHelper.HasRates(portalConfig.countryId)
                        ? $"{ProductService.SetCurrency(new decimal(promotion.NumericPrice), portalConfig)}*"
                        : $"{ProductService.SetCurrency(new decimal(promotion.NumericPrice), portalConfig)}";

                    var saving = GetSavingString(data.HasPromotion, promotion.LiteralPercentDiscount, producto.Saving);

                    var temporality = new CompanyCartProductTemporalityDataModel
                    {
                        UnitPriceLabel = data.HasPromotion ? promotion.LiteralPriceUnity : data.LiteralShowUnit,
                        UnitPriceLabelWithoutPromotion = data.LiteralShowUnit,
                        TotalPriceLabel = data.HasPromotion ? totalPriceWithPromotion : data.LiteralShowTotal,
                        Temporality = data.TemporalityId,
                        NumericPrice = data.HasPromotion ? promotion.NumericPrice : data.Price,
                        ProductName = producto.ComercialName,
                        TotalPriceLabelWithoutPromotion = promotion.LiteralOldPrice,
                        TotalPriceLabelWithPromotion = totalPriceWithPromotion,
                        PercentageDiscount = promotion.LiteralPercentDiscount,
                        PriceToDiscount = promotion.LiteralTotalSaveUpPrice,
                        TypePromotion = promotion.TypePromotion,
                        Saving = saving
                    };

                    var ratedPrice = CompanyService.GetRatedPricesByPrice(producto.HasPromotion ? (decimal)promotion.NumericPrice : (decimal)producto.Price, portalConfig.countryId);

                    if (portalConfig.EnableBreakDownPrices && ratedPrice.VATNumber > 0)
                    {
                        temporality.ValueVat = $"{ratedPrice.VATNumber} %";
                        temporality.ValuePriceWithVat = ProductService.SetCurrency(ratedPrice.PriceWithVAT, portalConfig);
                    }

                    if (!product.Any())
                    {
                        data.EncryptedId = _encryptionService.Encrypt(data.Id.ToString());

                        lst.Add(new CompanyCartProductDataModel()
                        {
                            Product = data,
                            PositionId = position,
                            Temporalities = new List<CompanyCartProductTemporalityDataModel>() { temporality }
                        });
                        position += 1;
                    }
                    else
                    {
                        product.FirstOrDefault().Temporalities.Add(temporality);
                    }
                });
            });

            FillProductsFeatures(lst, portalConfig.PortalId);

            return lst;
        }

        private List<ProductEntity> GetAllProductsByPage(int idPage, short subGroup, PortalConfig portalConfig, int idCompany, TestABEnum testABEnum)
        {   
            var pageProductsSearchFilter = GetPageProductsSearchFilter(idCompany, testABEnum, portalConfig, (PageEnum)idPage);
            pageProductsSearchFilter.SubGroup = subGroup;
            pageProductsSearchFilter.FromCart = true;

            return _pageProductService.SearchByPage(pageProductsSearchFilter);
        }

        private void FillProductsFeatures(List<CompanyCartProductDataModel> products, short idPortal)
        {
            products.ForEach(p => p.Product.Features = Mapper.Map<List<ProductFeatureEntity>, List<ProductFeatureDataModel>>
                (ProductService.GetProductFeatures(
                    new ProductSearchSpecifications(idPortal)
                    {
                        Id = p.Product.Id
                    }))
            );
        }

        [HttpPost]
        public JsonResult CheckIdentifierCart(string nit, string idcountry)
        {
            var portalConfig = PortalConfigurationService.GetPortalConfiguration();
            var companyCredentials = SecurityHelper.GetCompanyCredentials();

            int.TryParse(idcountry, out int countryIdSelected);
            var checkIdentifierId = _companyCheckIdentifierService.CheckIdentifier(nit, countryIdSelected, portalConfig, companyCredentials.IdCompany, false);

            switch (checkIdentifierId)
            {
                case 2:
                    return Json(PageLiteralsHelper.GetLiteral("CODE_NIT_INCORRECT", (short)PageEnum.CompanyRegister, portalConfig));
                default:
                    return Json("");
            }
        }

        [HttpPost]
        public JsonResult SumProducts(string price)
        {
            var portalConfig = PortalConfigurationService.GetPortalConfiguration();

            decimal.TryParse(price, out var decimalPrice);

            var ratedPrice = CompanyService.GetRatedPricesByPrice(decimalPrice, portalConfig.countryId);
            if (portalConfig.EnableBreakDownPrices && ratedPrice.VATNumber > 0)
            {
                decimalPrice = (int)ratedPrice.PriceWithVAT;
            }
            string returnPrice = ProductService.SetCurrency(decimalPrice, portalConfig);

            return Json(returnPrice);
        }

        private int TryGetVisitorSourceFromSession()
        {
            //---------------- Helper id visit origin--------------
            // PushByCategory = 1,
            // PushByProfessions = 2,
            // MailCompraRotaPack = 3,
            // CVFreemiumLimitReached = 4,
            // FirstFreeJobOffer = 5,
            // LastFreeJobOffer = 6,
            // MailCompraRotaMembresia = 7,
            // RememberNewOffersEnabled = 8
            //---------------- Helper id visit origin--------------
            int idVisitorSource = 0;
            if (this.Session[QueryStringParams.VISIT_ORIGIN] != null)
            {
                string ovEnc = EncryptationHelper.Decrypt((string)this.Session[QueryStringParams.VISIT_ORIGIN]);
                int.TryParse(ovEnc, out idVisitorSource);
            }
            return idVisitorSource;
        }

        private MultiPaymentEntity LoadMultiPaymentEntity(CompanyCartDataModel cartModel, string ov, InvoiceEntity invoiceEntity, CompanyCredentials companyCredentials, PortalConfig portalConfig)
        {
            var productSelected = cartModel.ProductsListSelected.Split('|');

            List<PurchaseProductItem> PurchaseProductsList = new List<PurchaseProductItem>();
            foreach (var item in productSelected)
            {
                if (item == string.Empty) continue;

                int.TryParse(item.Split(',')[0], out var idProduct);
                int.TryParse(item.Split(',')[1], out var idTemporality);

                PurchaseProductsList.Add(SetPurchaseProduct(idProduct, idTemporality, cartModel.PageId, portalConfig, companyCredentials.IdCompany, companyCredentials.UserId));
            }

            MultiPaymentEntity paymentEntity = new MultiPaymentEntity
            {
                CompanyId = companyCredentials.IdCompany,
                UserId = (int)companyCredentials.UserId,
                PaymentMethod = cartModel.EditDataCompanyCart.PayMethodSelected,
                InvoiceEntity = invoiceEntity,
                PortalId = companyCredentials.PortalId,
                OfferId = cartModel.EditDataCompanyCart.OfferId,
                VisitorSourceId = (!string.IsNullOrEmpty(ov) ? ov.ToInt() : 0),
                RenovarProduct = false,
                HasVat = cartModel.EditDataCompanyCart.HasVat,
                PurchaseProductsList = PurchaseProductsList,
                Impersonated = companyCredentials.IsAutologinBackweb,
            };

            return paymentEntity;
        }

        private PurchaseProductItem SetPurchaseProduct(int productId, int temporalityid, int pageId, PortalConfig portalConfig, int idCompany, long idUser)
        {
            var payProduct = _productsService.Get(new ProductSearchSpecifications(portalConfig.PortalId) { Id = productId, TemporalityId = temporalityid });

            var purchaseProduct = new PurchaseProductItem();

            decimal price = (decimal)payProduct.Price;

            if (portalConfig.active_promotions)
            {
                var productsPage = ProductService.SearchByPage(new ProductSearchSpecifications(portalConfig.PortalId) { PageId = pageId, CompanyId = idCompany, LoadPromotions = true, DisableFreemium = IsDisableFreemiumOffers(idCompany, portalConfig) }, true).ToList();
                var cartHasAutomaticPromotions = productsPage.Exists(p => p.Promotions != null
                                                        && p.Promotions.Any()
                                                        && p.Promotions.ToList().Exists(pr => pr.TypePromotion == (short)PromotionTypeEnum.AutomaticPacks && pr.TypePromotion != (short)PromotionTypeEnum.VoucherPacks));

                if (productsPage.Exists(e => e.Id == payProduct.Id)
                    && productsPage.FirstOrDefault(e => e.Id == payProduct.Id).Promotions.ToList().Exists(p => p.TemporalityId == payProduct.TemporalityId))
                {
                    var promotion = productsPage.FirstOrDefault(e => e.Id == payProduct.Id).Promotions.FirstOrDefault(p => p.TemporalityId == payProduct.TemporalityId);
                    decimal.TryParse(promotion.NumericPrice.ToString(), out price);
                    purchaseProduct.PromotionId = promotion.Id;

                    if (cartHasAutomaticPromotions)
                    {
                        _kpiService.AddSumBlock((int)KpiEnum.PURCHASE_INIT_WITH_PROMOTION, (short)portalConfig.PortalId, 1);
                    }
                }
                else if (cartHasAutomaticPromotions)
                {
                    _kpiService.AddSumBlock((int)KpiEnum.PROMOTION_AUTOMATIC_REJECTED, (short)portalConfig.PortalId, 1);
                }
            }

            var ratedPrice = CompanyService.GetRatedPricesByPrice(price, portalConfig.countryId);

            purchaseProduct.ComercialName = payProduct.ComercialName;
            purchaseProduct.IdProduct = productId;
            purchaseProduct.PriceBase = ratedPrice.Price;
            purchaseProduct.Price = ratedPrice.PriceWithVAT;
            purchaseProduct.IdTemporality = payProduct.TemporalityId;
            purchaseProduct.Tax = ratedPrice.VATNumber;
            purchaseProduct.OriginalPriceBase = (decimal)payProduct.Price;
            //purchaseProduct.RenewalPriceWithVAT = ratedPrice.RenewalPriceWithVAT;
            //purchaseProduct.RenewPrice = ratedPrice.RenewPrice;

            //purchaseProduct.VATNumber = ratedPrice.VATNumber;
            //purchaseProduct.VATLiteral = ratedPrice.VATLiteral;
            //if (purchaseProduct.CartProducts != null && cartModel.CartProducts.Any())
            //    purchaseProduct.CartProducts.ForEach(p => p.HasVat = portalConfig.EnableBreakDownPrices && cartModel.VATNumber > 0);

            return purchaseProduct;
        }

        private bool HasAutomaticPromotions(int pageId, PortalConfig portalConfig, int idCompany)
        {
            if (portalConfig.active_promotions)
            {
                var productsPage = ProductService.SearchByPage(new ProductSearchSpecifications(portalConfig.PortalId) { PageId = pageId, CompanyId = idCompany, LoadPromotions = true, DisableFreemium = IsDisableFreemiumOffers(idCompany, portalConfig) }, true).ToList();
                return productsPage.Exists(p => p.Promotions != null
                                                        && p.Promotions.Any()
                                                        && p.Promotions.ToList().Exists(pr => pr.TypePromotion == (short)PromotionTypeEnum.AutomaticPacks && pr.TypePromotion != (short)PromotionTypeEnum.VoucherPacks));
            }
            return false;
        }

        private List<int> GetPurchaseProductsList(string idOfferStr, string idProduct, List<CompanyCartProductDataModel> cartProducts, string startPurchaseOrigin, int idCompany, short idPortal)
        {
            List<int> purchaseProductsList = new List<int>();

            if (startPurchaseOrigin == ((int)KpiEnum.START_PURCHASE_POSTPUBLISH_STEP2).ToString())
            {
                LoadConsumablesCartByPostPublishStepTwo(idOfferStr, cartProducts, idCompany, idPortal, purchaseProductsList);
            }
            else
            {
                LoadConsumablesCartOtherCases(idProduct, cartProducts, idPortal, purchaseProductsList);
            }

            return purchaseProductsList.Distinct().ToList();
        }

        private void LoadConsumablesCartOtherCases(string idProduct, List<CompanyCartProductDataModel> cartProducts, short idPortal, List<int> purchaseProductsList)
        {
            var idProductDecrypt = _encryptionService.Decrypt(idProduct);
            int.TryParse(idProductDecrypt, out int idProductInt);
            var productFeatures = ProductService.GetProductFeatures(new ProductSearchSpecifications(idPortal) { Id = idProductInt });

            foreach (var item in productFeatures)
            {
                var cartFeatureProducts = cartProducts.Where(x => x.Product.Default == true && x.Product.Features.Exists(x => x.AmbitId == item.AmbitId)).ToList();
                if (cartFeatureProducts.Any())
                {
                    AssignPurchaseProductsAndCheckedProductByProductId(cartProducts, cartFeatureProducts.First().Product.Id, purchaseProductsList);
                }
            }

            TraceSelectedProductConsumablesPostPublishOtherCases(cartProducts, idPortal, idProductInt);
        }

        private void TraceSelectedProductConsumablesPostPublishOtherCases(List<CompanyCartProductDataModel> cartProducts, short idPortal, int idProductInt)
        {
            if (!cartProducts.Exists(e => e.IsChecked))
            {
                ExceptionPublisherService.Publish(new Exception($"ERROR ningún producto seleccionado en consumibles"), "MultiPurchaseCartController", "LoadProductsCart", false,
                                                                new Dictionary<string, string>() { { ExtraDataConstants.ID_PRODUCT, idProductInt.ToString() } }, idPortal);
            }
        }

        private void LoadConsumablesCartByPostPublishStepTwo(string idOfferStr, List<CompanyCartProductDataModel> cartProducts, int idCompany, short idPortal, List<int> purchaseProductsList)
        {
            int.TryParse(_encryptionService.Decrypt(idOfferStr), out int idOffer);
            PurchaseProductsListEntity purchaseProductsListEntity = _multipurchaseOperationService.GetPurchaseProducts(idOffer, idCompany, idPortal);

            if (purchaseProductsListEntity.OfferProductId > 0)
            {
                AssignPurchaseProductsAndCheckedProductByProductId(cartProducts, purchaseProductsListEntity.OfferProductId, purchaseProductsList);
            }
            if (purchaseProductsListEntity.HighlightProductId > 0)
            {
                AssignPurchaseProductsAndCheckedProductByProductId(cartProducts, purchaseProductsListEntity.HighlightProductId, purchaseProductsList);
            }
            if (purchaseProductsListEntity.UrgentProductId > 0)
            {
                AssignPurchaseProductsAndCheckedProductByProductId(cartProducts, purchaseProductsListEntity.UrgentProductId, purchaseProductsList);
            }
            if (purchaseProductsListEntity.FlashProductId > 0)
            {
                AssignPurchaseProductsAndCheckedProductByProductId(cartProducts, purchaseProductsListEntity.FlashProductId, purchaseProductsList);
            }
            if (purchaseProductsListEntity.HiddenCompanyProductId > 0)
            {
                AssignPurchaseProductsAndCheckedProductByProductId(cartProducts, purchaseProductsListEntity.HiddenCompanyProductId, purchaseProductsList);
            }
            if (purchaseProductsListEntity.KQProductId > 0)
            {
                AssignPurchaseProductsAndCheckedProductByProductId(cartProducts, purchaseProductsListEntity.KQProductId, purchaseProductsList);
            }

            TraceSelectedProductConsumiblesPostPublishStepTwoException(cartProducts, idCompany, idPortal, idOffer, purchaseProductsListEntity);
        }

        private void TraceSelectedProductConsumiblesPostPublishStepTwoException(List<CompanyCartProductDataModel> cartProducts, int idCompany, short idPortal, int idOffer, PurchaseProductsListEntity purchaseProductsListEntity)
        {
            if (!cartProducts.Exists(e => e.IsChecked))
            {
                var extradata = new Dictionary<string, string>();
                extradata.Add(ExtraDataConstants.ID_OFFER, idOffer.ToString());
                extradata.Add(ExtraDataConstants.OFFER_PRODUCT_ID, purchaseProductsListEntity.OfferProductId.ToString());
                extradata.Add(ExtraDataConstants.HIGHLIGHT_PRODUCT_ID, purchaseProductsListEntity.HighlightProductId.ToString());
                extradata.Add(ExtraDataConstants.URGENT_PRODUCT_ID, purchaseProductsListEntity.UrgentProductId.ToString());
                extradata.Add(ExtraDataConstants.FLASH_PRODUCT_ID, purchaseProductsListEntity.FlashProductId.ToString());
                extradata.Add(ExtraDataConstants.HIDDEN_COMPANY_PRODUCT_ID, purchaseProductsListEntity.HiddenCompanyProductId.ToString());
                extradata.Add(ExtraDataConstants.KQ_PRODUCT_ID, purchaseProductsListEntity.KQProductId.ToString());
                extradata.Add(ExtraDataConstants.COMPANY_ID, idCompany.ToString());

                ExceptionPublisherService.Publish(new Exception($"ERROR ningún producto seleccionado en consumibles desde PostPublishStepTwo"), "MultiPurchaseCartController", "LoadProductsCart", false, extradata, idPortal);
            }
        }

        private static void AssignPurchaseProductsAndCheckedProductByProductId(List<CompanyCartProductDataModel> cartProducts, int productIdSelected, List<int> purchaseProductsList)
        {
            purchaseProductsList.Add(productIdSelected);
            CheckCartProduct(cartProducts, productIdSelected);
        }

        private static void CheckCartProduct(List<CompanyCartProductDataModel> cartProducts, int productPreSelected)
        {
            var productCartToChecked = cartProducts.FirstOrDefault(e => e.Product.Id == productPreSelected);

            if (productCartToChecked != null)
            {
                productCartToChecked.IsChecked = true;
            }
        }

        public string GetSavingString(bool HasPromotion, string PromoSavingLiteral, decimal productSaving)
        {
            if (HasPromotion)
            {
                if (!PromoSavingLiteral.Contains("%"))
                {
                    return string.Format("{0} %", PromoSavingLiteral);
                }
                return PromoSavingLiteral;
            }
            else if (productSaving > 0)
            {
                return string.Format("{0:0} %", productSaving);
            }
            return string.Empty;
        }

        private Dictionary<string, string> FillPageLiterals(PortalConfig portalConfig)
        {
            Dictionary<string, string> pageLiterals = new Dictionary<string, string>();

            pageLiterals.Add("LIT_TIT_PAG_1", PageLiteralsHelper.GetLiteral("LIT_TIT_PAG_1", (int)PageEnum.PackCart, portalConfig));
            pageLiterals.Add("LIT_BENEFIT", PageLiteralsHelper.GetLiteral("LIT_BENEFIT", (int)PageEnum.PackCart, portalConfig));
            pageLiterals.Add("LIT_DISCOUNT_ADITIONAL", PageLiteralsHelper.GetLiteral("LIT_DISCOUNT_ADITIONAL", (int)PageEnum.PackCart, portalConfig));
            pageLiterals.Add("LIT_COMPLETE_CART", PageLiteralsHelper.GetLiteral("LIT_COMPLETE_CART", (int)PageEnum.PackCart, portalConfig));
            pageLiterals.Add("H2_ERROR_PAGO", PageLiteralsHelper.GetLiteral("H2_ERROR_PAGO", (int)PageEnum.PackCart, portalConfig));
            pageLiterals.Add("LIT_OFFERS_BBDD", PageLiteralsHelper.GetLiteral("LIT_OFFERS_BBDD", (int)PageEnum.PackCart, portalConfig));
            pageLiterals.Add("LIT_BBDD_DESC", PageLiteralsHelper.GetLiteral("LIT_BBDD_DESC", (int)PageEnum.PackCart, portalConfig));
            pageLiterals.Add("LIT_BBDD_SUBT", PageLiteralsHelper.GetLiteral("LIT_BBDD_SUBT", (int)PageEnum.PackCart, portalConfig));
            pageLiterals.Add("LIT_CONSUMABLES_TITLE", PageLiteralsHelper.GetLiteral("LIT_CONSUMABLES_TITLE", (int)PageEnum.PackCart, portalConfig));
            pageLiterals.Add("LIT_OFFERS_HIDDENNAME", PageLiteralsHelper.GetLiteral("LIT_OFFERS_HIDDENNAME", (int)PageEnum.PackCart, portalConfig));
            pageLiterals.Add("LIT_HIDDENNAME_DESC", PageLiteralsHelper.GetLiteral("LIT_HIDDENNAME_DESC", (int)PageEnum.PackCart, portalConfig));
            pageLiterals.Add("LIT_HIDDENNAME_SUBT", PageLiteralsHelper.GetLiteral("LIT_HIDDENNAME_SUBT", (int)PageEnum.PackCart, portalConfig));
            pageLiterals.Add("LIT_OFFERS_HIGHLIGHTED_TITLE", PageLiteralsHelper.GetLiteral("LIT_OFFERS_HIGHLIGHTED_TITLE", (int)PageEnum.PackCart, portalConfig));
            pageLiterals.Add("LIT_HIGHLIGHT_DESC", PageLiteralsHelper.GetLiteral("LIT_HIGHLIGHT_DESC", (int)PageEnum.PackCart, portalConfig));
            pageLiterals.Add("LIT_HIGHLIGHT_SUBT", PageLiteralsHelper.GetLiteral("LIT_HIGHLIGHT_SUBT", (int)PageEnum.PackCart, portalConfig));
            pageLiterals.Add("LIT_OFFERS_URGENT_TITLE", PageLiteralsHelper.GetLiteral("LIT_OFFERS_URGENT_TITLE", (int)PageEnum.PackCart, portalConfig));
            pageLiterals.Add("LIT_URGENT_DESC", PageLiteralsHelper.GetLiteral("LIT_URGENT_DESC", (int)PageEnum.PackCart, portalConfig));
            pageLiterals.Add("LIT_URGENT_SUBT", PageLiteralsHelper.GetLiteral("LIT_URGENT_SUBT", (int)PageEnum.PackCart, portalConfig));
            pageLiterals.Add("LIT_OFFERS_FLASH_TITLE", PageLiteralsHelper.GetLiteral("LIT_OFFERS_FLASH_TITLE", (int)PageEnum.PackCart, portalConfig));
            pageLiterals.Add("LIT_FLASH_DESC", PageLiteralsHelper.GetLiteral("LIT_FLASH_DESC", (int)PageEnum.PackCart, portalConfig));
            pageLiterals.Add("LIT_FLASH_SUBT", PageLiteralsHelper.GetLiteral("LIT_FLASH_SUBT", (int)PageEnum.PackCart, portalConfig));
            pageLiterals.Add("LIT_OFFERS_KQ", PageLiteralsHelper.GetLiteral("LIT_OFFERS_KQ", (int)PageEnum.PackCart, portalConfig));
            pageLiterals.Add("LIT_KQ_DESC", PageLiteralsHelper.GetLiteral("LIT_KQ_DESC", (int)PageEnum.PackCart, portalConfig));
            pageLiterals.Add("LIT_KQ_SUBT", PageLiteralsHelper.GetLiteral("LIT_KQ_SUBT", (int)PageEnum.PackCart, portalConfig));
            pageLiterals.Add("LIT_WITHOUT_VAT", PageLiteralsHelper.GetLiteral("LIT_WITHOUT_VAT", (int)PageEnum.PackCart, portalConfig));
            pageLiterals.Add("LIT_ORDER_SUMMARY", PageLiteralsHelper.GetLiteral("LIT_ORDER_SUMMARY", (int)PageEnum.PackCart, portalConfig));
            pageLiterals.Add("LIT_BUY", PageLiteralsHelper.GetLiteral("LIT_BUY", (int)PageEnum.PackCart, portalConfig));
            pageLiterals.Add("LIT_PRODUCT_FEATURES", PageLiteralsHelper.GetLiteral("LIT_PRODUCT_FEATURES", (int)PageEnum.PackCart, portalConfig));
            pageLiterals.Add("LIT_TOTAL", PageLiteralsHelper.GetLiteral("LIT_TOTAL", (int)PageEnum.PackCart, portalConfig));
            pageLiterals.Add("LIT_ADDUNITS", PageLiteralsHelper.GetLiteral("LIT_ADDUNITS", (int)PageEnum.PackCart, portalConfig));
            pageLiterals.Add("LIT_REMOVEUNITS", PageLiteralsHelper.GetLiteral("LIT_REMOVEUNITS", (int)PageEnum.PackCart, portalConfig));
            pageLiterals.Add("LIT_TEMPLIMIT1", PageLiteralsHelper.GetLiteral("LIT_TEMPLIMIT1", (int)PageEnum.PackCart, portalConfig));
            pageLiterals.Add("LIT_TEMPLIMIT2", PageLiteralsHelper.GetLiteral("LIT_TEMPLIMIT2", (int)PageEnum.PackCart, portalConfig));
            pageLiterals.Add("LIT_TEMPLIMIT3", PageLiteralsHelper.GetLiteral("LIT_TEMPLIMIT3", (int)PageEnum.PackCart, portalConfig));
            pageLiterals.Add("LIT_TEMPLIMIT4", PageLiteralsHelper.GetLiteral("LIT_TEMPLIMIT4", (int)PageEnum.PackCart, portalConfig));
            pageLiterals.Add("LIT_TEMPLIMIT6", PageLiteralsHelper.GetLiteral("LIT_TEMPLIMIT6", (int)PageEnum.PackCart, portalConfig));
            pageLiterals.Add("LIT_AVALIBLE_DURING1", PageLiteralsHelper.GetLiteral("LIT_AVALIBLE_DURING1", (int)PageEnum.PackCart, portalConfig));
            pageLiterals.Add("LIT_AVALIBLE_DURING2", PageLiteralsHelper.GetLiteral("LIT_AVALIBLE_DURING2", (int)PageEnum.PackCart, portalConfig));
            pageLiterals.Add("LIT_AVALIBLE_DURING3", PageLiteralsHelper.GetLiteral("LIT_AVALIBLE_DURING3", (int)PageEnum.PackCart, portalConfig));
            pageLiterals.Add("LIT_AVALIBLE_DURING4", PageLiteralsHelper.GetLiteral("LIT_AVALIBLE_DURING4", (int)PageEnum.PackCart, portalConfig));
            pageLiterals.Add("LIT_AVALIBLE_DURING5", PageLiteralsHelper.GetLiteral("LIT_AVALIBLE_DURING5", (int)PageEnum.PackCart, portalConfig));
            pageLiterals.Add("LIT_AVALIBLE_DURING6", PageLiteralsHelper.GetLiteral("LIT_AVALIBLE_DURING6", (int)PageEnum.PackCart, portalConfig));
            pageLiterals.Add("LIT_OFERTAS", PageLiteralsHelper.GetLiteral("LIT_OFERTAS", (int)PageEnum.PackCart, portalConfig));
            pageLiterals.Add("LIT_OFERTA", PageLiteralsHelper.GetLiteral("LIT_OFERTA", (int)PageEnum.PackCart, portalConfig).ToLower());
            pageLiterals.Add("LIT_PRIME_EXPIRATION", PageLiteralsHelper.GetLiteral("LIT_PRIME_EXPIRATION", (int)PageEnum.PackCart, portalConfig).ToLower());
            pageLiterals.Add("LIT_PRIME_DISCOUNT", PageLiteralsHelper.GetLiteral("LIT_PRIME_DISCOUNT", (int)PageEnum.PackCart, portalConfig).ToLower());
            pageLiterals.Add("LIT_DESCUENTO", PageLiteralsHelper.GetLiteral("LIT_DESCUENTO", (int)PageEnum.PackCart, portalConfig));
            return pageLiterals;
        }
    }
}