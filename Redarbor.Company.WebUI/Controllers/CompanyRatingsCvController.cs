using Redarbor.Candidate.Contracts.ServiceLibrary;
using Redarbor.Company.Contracts.ServiceLibrary;
using Redarbor.Company.WebUI.Helpers;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Core.Contracts.ServiceLibrary;
using Redarbor.Core.Counter.Contracts.ServiceLibrary;
using Redarbor.Core.Kpi.Contracts.ServiceLibrary;
using Redarbor.Core.Kpi.Contracts.ServiceLibrary.Enums;
using Redarbor.Core.Shared.Contracts.ServiceLibrary;
using Redarbor.Curriculum.Contracts.ServiceLibrary;
using Redarbor.Master.Contracts.ServiceLibrary;
using Redarbor.Master.Entities.Cv;
using Redarbor.Master.Entities.Enums;
using Redarbor.Master.Entities.Product;
using Redarbor.Offer.Contracts.ServiceLibrary;
using Redarbor.Products.Contracts.ServiceLibrary;
using Redarbor.ProductSubGroups.Contracts.ServiceLibrary;
using Redarbor.RatingCv.Contracts.ServiceLibrary;
using Redarbor.Web.UI.Library.Exceptions;
using System;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;

namespace Redarbor.Company.WebUI.Controllers
{
    public class CompanyRatingsCvController : CompanyBaseController
    {

        private readonly IRatingCvService _ratingCvService;
        private readonly IEncryptionService _encryptionService;
        private readonly ICurriculumService _curriculumService;
        private readonly IKpiService _kpiService;
        private readonly ICompanyCountersService _companyCountersService;
        private readonly ICandidateService _candidateService;

        public CompanyRatingsCvController(
           IEncryptionService encryptionService,
            ICompanyCountersService companyCountersService,
            IKpiService kpiService,
            ICurriculumService curriculumService,
            IProductService productService,
            IProductSubGroupsService productSubGroupsService,
            IOfferService offerService,
            ICompanyProductService companyProductService,
            ICompanyService companyService,
            IPortalConfigurationService portalConfigurationService,
            IDictionaryService dictionaryService,
            ISecurityService securityService,
            IRatingCvService ratingCvService,
            IExceptionPublisherService exceptionPublisherService,
            ICandidateService candidateService) : base(productService, productSubGroupsService, offerService, companyProductService, companyService, portalConfigurationService, dictionaryService, securityService, exceptionPublisherService)
        {
            _companyCountersService = companyCountersService;
            _kpiService = kpiService;
            _ratingCvService = ratingCvService;
            _curriculumService = curriculumService;
            _encryptionService = encryptionService;
            _candidateService = candidateService;
        }

        public bool InsertRating(string rtg, string oi, string idc, string idcv, string idu, string ic)
        {
            try
            {
                Int32.TryParse(rtg, out var rating);
                Int32.TryParse(_encryptionService.Decrypt(oi), out var offerId);
                Int32.TryParse(_encryptionService.Decrypt(idc), out var candidateId);
                Int32.TryParse(_encryptionService.Decrypt(idcv), out var cvId);
                Int32.TryParse(_encryptionService.Decrypt(idu), out var UserId);
                Int32.TryParse(_encryptionService.Decrypt(ic), out var companyId);
                var companyCredendentials = SecurityHelper.GetCompanyCredentials();
                var portalConfig = PortalConfigurationService.GetPortalConfiguration();

                var companyProduct = GetCompanyProduct(companyCredendentials, offerId);

                if (!companyProduct.Features.Any(n => n.AmbitId == (short)ProductAmbitEnum.RatingsCvs))
                    return false;

                if ((rating < 0 && rating >= 5) || offerId < 0 || candidateId < 0 || cvId < 0 || UserId < 0 || companyId < 0) return false;
                                
                var cv = _curriculumService.GetCandidateCompletCV(cvId, portalConfig);

                if (cv != null)
                {
                    if (!cv.Ratings.Any(c => c.CvId == cvId && c.UserId == UserId))
                    {
                        RatingCvEntity ratingCV = new RatingCvEntity()
                        {
                            Rating = (short)rating,
                            OfferId = offerId,
                            CandidateId = candidateId,
                            CvId = cvId,
                            UserId = UserId,
                            CompanyId = companyId,
                            NameAuthor = companyCredendentials.Name,
                            PortalId = portalConfig.PortalId
                        };

                        if (ratingCV.OfferId == 0) SelectBBDDRatingForKPI(rating);
                        else SelectMatchRatingForKPI(rating);

                        if (_ratingCvService.InsertRatingCv(ratingCV))
                        {
                            _candidateService.RemoveCandidateCache(portalConfig.PortalId, cvId);
                            return true;
                        }
                    }
                }

                return false;
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyRatingsCvController InsertRating {ex}");
                ExceptionPublisherService.Publish(ex, "CompanyRatingsCvController", "InsertRating");
                return false;
            }
        }

        private CompanyProductEntity GetCompanyProduct(Master.Contracts.ServiceLibrary.DTO.CompanyCredentials companyCredendentials, int offerId)
        {
            var companyProduct = CompanyProductService.GetByIdCompany(companyCredendentials.IdCompany, companyCredendentials.PortalId, OfferIntegratorEnum.CompuTrabajo);

            if (offerId > 0 && companyProduct.GroupId != (short)ProductGroupsEnum.Membership)
            {
                var offer = OfferService.GetByPk(offerId, companyCredendentials.PortalId);

                if (companyProduct.GroupId != (short)ProductGroupsEnum.Membership && offer.idoffer != 0)
                {
                    companyProduct = CompanyProductService.GetByCompanyProductId(offer.Integrations.FirstOrDefault()?.idcompanyproduct ?? 0, offer.idportal, offer.idcompany);
                }
            }

            return companyProduct;
        }

        public bool DeleteRating(string oi, string idc, string idu, string ic, string idcv)
        {
            try
            {
                Int32.TryParse(_encryptionService.Decrypt(oi), out var offerId);
                Int32.TryParse(_encryptionService.Decrypt(idc), out var candidateId);
                Int32.TryParse(_encryptionService.Decrypt(idu), out var UserId);
                Int32.TryParse(_encryptionService.Decrypt(ic), out var companyId);
                Int32.TryParse(_encryptionService.Decrypt(idcv), out var cvId);
                var companyCredendentials = SecurityHelper.GetCompanyCredentials();
                var portalConfig = PortalConfigurationService.GetPortalConfiguration();

                var companyProduct = GetCompanyProduct(companyCredendentials, offerId);

                if (!companyProduct.Features.Any(n => n.AmbitId == (short)ProductAmbitEnum.RatingsCvs))
                    return false;
                
                if (offerId < 0 || candidateId < 0 || UserId < 0 || companyId < 0) return false;

                RatingCvEntity ratingCV = new RatingCvEntity()
                {
                    OfferId = offerId,
                    CandidateId = candidateId,
                    UserId = UserId,
                    CompanyId = companyId
                };

                var count = ratingCV.OfferId == 0 ? (ActionRatingCount((int)KpiEnum.COMPANY_BBDD_RATING_DELETE)) : (ActionRatingCount((int)KpiEnum.COMPANY_MATCH_RATING_DELETE));

                if (_ratingCvService.RemoveRatingCv(ratingCV))
                {
                    _candidateService.RemoveCandidateCache(portalConfig.PortalId, cvId);
                    return true;
                }

                return false;
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyRatingsCvController DeleteRating {ex}");
                ExceptionPublisherService.Publish(ex, "CompanyRatingsCvController", "DeleteRating");
                return false;
            }
        }

        private void SelectMatchRatingForKPI(int rating)
        {
            switch (rating)
            {
                case 1:
                    ActionRatingCount((int)KpiEnum.COMPANY_MATCH_RATING_1_ADD);
                    break;
                case 2:
                    ActionRatingCount((int)KpiEnum.COMPANY_MATCH_RATING_2_ADD);
                    break;
                case 3:
                    ActionRatingCount((int)KpiEnum.COMPANY_MATCH_RATING_3_ADD);
                    break;
                case 4:
                    ActionRatingCount((int)KpiEnum.COMPANY_MATCH_RATING_4_ADD);
                    break;
                case 5:
                    ActionRatingCount((int)KpiEnum.COMPANY_MATCH_RATING_5_ADD);
                    break;
            }
        }

        private void SelectBBDDRatingForKPI(int rating)
        {
            switch (rating)
            {
                case 1:
                    ActionRatingCount((int)KpiEnum.COMPANY_BBDD_RATING_1_ADD);
                    break;
                case 2:
                    ActionRatingCount((int)KpiEnum.COMPANY_BBDD_RATING_2_ADD);
                    break;
                case 3:
                    ActionRatingCount((int)KpiEnum.COMPANY_BBDD_RATING_3_ADD);
                    break;
                case 4:
                    ActionRatingCount((int)KpiEnum.COMPANY_BBDD_RATING_4_ADD);
                    break;
                case 5:
                    ActionRatingCount((int)KpiEnum.COMPANY_BBDD_RATING_5_ADD);
                    break;
            }
        }

        private bool ActionRatingCount(short _kpiType)
        {
            var companyCredendentials = SecurityHelper.GetCompanyCredentials();
            var portalConfig = PortalConfigurationService.GetPortalConfiguration();

            Task.Factory.StartNew(() =>
            {
                _companyCountersService.AddCounterCompany(_kpiType, portalConfig.PortalId, companyCredendentials.IdCompany, 1, false);
                _kpiService.AddSumBlock(_kpiType, portalConfig.PortalId, 1);

            });
            return true;
        }
    }
}