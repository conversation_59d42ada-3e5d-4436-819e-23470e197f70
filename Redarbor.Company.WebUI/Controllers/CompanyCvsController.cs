using AutoMapper;
using Redarbor.Candidate.Contracts.ServiceLibrary;
using Redarbor.Candidate.Contracts.ServiceLibrary.DTO;
using Redarbor.CommentCv.Contracts.ServiceLibrary;
using Redarbor.Common.Entities;
using Redarbor.Common.Entities.Configuration;
using Redarbor.Common.Entities.Constants;
using Redarbor.Common.Entities.Enums;
using Redarbor.Company.Contracts.ServiceLibrary;
using Redarbor.Company.WebUI.Enums;
using Redarbor.Company.WebUI.Helpers;
using Redarbor.Company.WebUI.Helpers.Mappings;
using Redarbor.Company.WebUI.Models;
using Redarbor.Company.WebUI.Models.Company;
using Redarbor.Company.WebUI.Models.Company.Candidate;
using Redarbor.Company.WebUI.Models.Company.Cv;
using Redarbor.Company.WebUI.Models.Company.Home;
using Redarbor.Company.WebUI.Models.Company.Shared;
using Redarbor.Company.WebUI.Security;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Core.Contracts.ServiceLibrary;
using Redarbor.Core.Elastic.Library;
using Redarbor.Core.Resolver.Contracts.ServiceLibrary;
using Redarbor.Core.Shared.Contracts.ServiceLibrary;
using Redarbor.Curriculum.Contracts.ServiceLibrary;
using Redarbor.CustomFolders.Contracts.ServiceLibrary;
using Redarbor.DataAnalytics.ServiceLibrary.DTO;
using Redarbor.Dictionaries.Consumer.Enums;
using Redarbor.Elastic.Entities.Candidate;
using Redarbor.Elastic.Entities.Enums;
using Redarbor.Extensions.Library.Extensions;
using Redarbor.Master.Contracts.ServiceLibrary;
using Redarbor.Master.Contracts.ServiceLibrary.DTO;
using Redarbor.Master.Entities.Company;
using Redarbor.Master.Entities.CustomFolder;
using Redarbor.Master.Entities.Cv;
using Redarbor.Master.Entities.Enums;
using Redarbor.Master.Entities.Product;
using Redarbor.Master.Entities.Searchs;
using Redarbor.Master.Impl.ServiceLibrary.KinesisEventHelper;
using Redarbor.Match.Contracts.ServiceLibrary;
using Redarbor.Offer.Contracts.ServiceLibrary;
using Redarbor.Products.Contracts.ServiceLibrary;
using Redarbor.ProductSubGroups.Contracts.ServiceLibrary;
using Redarbor.RatingCv.Contracts.ServiceLibrary;
using Redarbor.User.Contracts.ServiceLibrary;
using Redarbor.Web.UI.Library;
using Redarbor.Web.UI.Library.Exceptions;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Web.Mvc;
using KinesisEntities = Redarbor.Master.Impl.ServiceLibrary.KinesisEventHelper.Entities;

namespace Redarbor.Company.WebUI.Controllers
{
    [RoutePrefix("Company/Cvs")]
    [RedarborAuthorize]
    public class CompanyCvsController : CompanyBaseController
    {
        private const string HAVE_BBDDCVPRODUCT_ACTIVE = "1";
        private const short PAGE_SIZE = 20;
        private const short PAGE_ID = (int)PageEnum.PackCVSearch;
        private const string SEPARATOR_SPLIT = ",";
        private const int IS_CV_VISUALITZED = 2;

        private string _maxFolders = ConfigurationManager.AppSettings["MAX_CUSTOM_FOLDER"] ?? "6";
        private string _maxCvsByFolder = ConfigurationManager.AppSettings["MAX_CVS_BY_FOLDER"] ?? "30";
        private readonly ICandidateService _candidateService;
        private readonly ICurriculumService _curriculumService;
        private readonly IRatingCvService _ratingCvService;
        private readonly ICommentCvService _commentCvService;
        private readonly ICustomFolderService _folderService;
        private readonly IEncryptionService _encryptionService;
        private readonly IMatchService _matchService;
        private readonly ISessionService _sessionService;
        private readonly ICompanyFiltersService _companyFiltersService;
        private readonly IElasticCandidateService _elasticCandidateService;
        private readonly IClientIpAddressResolverService _clientIpAddressResolverService;
        private readonly IUserService _userService;

        public CompanyCvsController(IProductService productService,
            IProductSubGroupsService productSubGroupsService,
            IOfferService offerService,
            ICompanyProductService companyProductService,
            ICompanyService companyService,
            IMatchService matchService,
            IPortalConfigurationService portalConfigurationService,
            IDictionaryService dictionaryService,
            ICandidateService candidateService,
            ISecurityService securityService,
            ICurriculumService curriculumService,
            IRatingCvService ratingCvService,
            ICommentCvService commentCvService,
            ICustomFolderService customFolderService,
            IEncryptionService encryptionService,
            ISessionService sessionService,
            IExceptionPublisherService exceptionPublisherService,
            IElasticCandidateService elasticCandidateService,
            ICompanyFiltersService companyFiltersService,
            IClientIpAddressResolverService clientIpAddressResolverService,
            IUserService userService) : base(productService, productSubGroupsService, offerService, companyProductService, companyService, portalConfigurationService, dictionaryService, securityService, exceptionPublisherService)
        {
            _candidateService = candidateService;
            _curriculumService = curriculumService;
            _ratingCvService = ratingCvService;
            _commentCvService = commentCvService;
            _matchService = matchService;
            _folderService = customFolderService;
            _encryptionService = encryptionService;
            _sessionService = sessionService;
            _elasticCandidateService = elasticCandidateService;
            _companyFiltersService = companyFiltersService;
            _clientIpAddressResolverService = clientIpAddressResolverService;
            _userService = userService;
        }

        [Route]
        public ActionResult Index(string idFolder, string cat, string q, string idLoc1, string ft, string iscvv)
        {
            CompanyCredentials companycredentialsAux = null;
            try
            {
                var portalConfig = PortalConfigurationService.GetPortalConfiguration();
                var companyCredentials = SecurityHelper.GetCompanyCredentials();
                companycredentialsAux = companyCredentials;
                var company = CompanyService.GetByPK(new CompanySearchSpecifications(companyCredentials.PortalId) { Id = companyCredentials.IdCompany });
                var companyProduct = CompanyProductService.GetByIdCompany(companyCredentials.IdCompany, companyCredentials.PortalId, OfferIntegratorEnum.CompuTrabajo);

                if (checkBlocked(portalConfig.company_block, company.Blocked, company.LastDateBlockedUpdate, portalConfig.DaysBlock))
                    return RedirectToAction("Blocked", "Account");

                if (company.CompanyStatusId == (short)CompanyStatusEnum.Discarted)
                    return RedirectToAction("ViewCvsAttempt", "CompanyRejected");

                var doAutoScroll = string.IsNullOrEmpty(idFolder);

                if (!HttpContext.Request.UrlReferrer?.AbsolutePath
                        .Contains(Url.Action("Index", "CompanyBBDDCvDetail") ?? string.Empty) ?? false)
                {
                    _sessionService.Forget(typeof(HomeCvsDataModel));
                    doAutoScroll = false;
                }

                var homeCvsModel = (HomeCvsDataModel)_sessionService.Get(typeof(HomeCvsDataModel))
                                   ?? new HomeCvsDataModel()
                                   {
                                       MultifiltersDataModel = new Models.Company.MultifiltersDataModel()
                                       {
                                           FolderSelected = idFolder,
                                           SelectedProfesionalCategories = cat?.Split(',').Where(w => w != string.Empty && w.ToInt() > 0).Select(categori => categori.ToInt()).ToList() ?? new List<int>(),
                                           SearchText = q,
                                           SelectedLocalization = idLoc1?.Split(',').Where(w => w != string.Empty && w.ToInt() > 0).Select(loc => loc.ToInt()).ToList() ?? new List<int>(),
                                           EncryptedSaveFilterSelected = ft ?? string.Empty
                                       }
                                   };

                if (homeCvsModel.MultifiltersDataModel.FolderSelected != idFolder)
                {
                    homeCvsModel.MultifiltersDataModel.FolderSelected = idFolder;
                }

                homeCvsModel.DoAutoScroll = doAutoScroll;
                homeCvsModel.MultifiltersDataModel.Order = (short)CvOrderEnum.Relevance;

                if (iscvv == IS_CV_VISUALITZED.ToString())
                {
                    homeCvsModel.MultifiltersDataModel.IsCvVisualitzed = IS_CV_VISUALITZED;
                }
                var model = GetHomeDataModel(homeCvsModel, !string.IsNullOrEmpty(idFolder) || !string.IsNullOrEmpty(ft), portalConfig, companyCredentials);

                if ((model.HaveMembershipActive &&
                    SecurityHelper.GetCompanyCredentials().UserRole != UserRoleEnum.COMPUADVISOR) ||
                    model.HomeCvsPackAndBasicDataModel.ItsBBDDcvProdActivated)
                {
                    if (portalConfig.AEPortalConfig.FindCVsNewPem && companyCredentials.TestAB == TestABEnum.A)
                    {
                        return View("IndexNewPem", model);
                    }

                    return View(model);
                }
                else
                    return RedirectToAction("", "Company");
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyCvsController Index-Get {ex}");
                Dictionary<string, string> extradata = new Dictionary<string, string>();
                extradata.Add("Idcompany", companycredentialsAux?.IdCompany.ToString());
                extradata.Add("IdPortal", companycredentialsAux?.PortalId.ToString());
                extradata.Add("CompanyName", companycredentialsAux?.CompanyName.ToString());
                extradata.Add("UserRole", companycredentialsAux?.UserRole.ToString());
                extradata.Add("UserId", companycredentialsAux?.UserId.ToString());
                extradata.Add("UserName", companycredentialsAux?.Username.ToString());
                ExceptionPublisherService.Publish(ex, "CompanyCvsController", "Index-Get", false, extradata);
                return RedirectToAction("Index", "Home");
            }
        }

        [Route]
        [HttpPost]
        [ValidateAntiForgeryToken()]
        public ActionResult Index(string idFolder, HomeCvsDataModel homeCvsDataModel)
        {
            CompanyCredentials companycredentialsAux = null;
            try
            {
                var portalConfig = PortalConfigurationService.GetPortalConfiguration();
                var companyCredentials = SecurityHelper.GetCompanyCredentials();
                companycredentialsAux = companyCredentials;

                if (!string.IsNullOrEmpty(homeCvsDataModel.MultifiltersDataModel.NameNewSaveFilter))
                {
                    var companyProduct = CompanyProductService.GetByIdCompany(companyCredentials.IdCompany, companyCredentials.PortalId, OfferIntegratorEnum.CompuTrabajo);
                    if (this.CanCreateNewSaveFilter(companyProduct))
                    {
                        return this.CreateNewSaveFilterAndRedirect(homeCvsDataModel.MultifiltersDataModel, idFolder);
                    }
                }

                var model = GetHomeDataModel(homeCvsDataModel, true, portalConfig, companyCredentials);



                if (portalConfig.AEPortalConfig.FindCVsNewPem && companyCredentials.TestAB == TestABEnum.A)
                {
                    return View("IndexNewPem", model);
                }
                return View(model);
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyCvsController Index-Post {ex}");
                Dictionary<string, string> extradata = new Dictionary<string, string>();
                extradata.Add("Idcompany", companycredentialsAux?.IdCompany.ToString());
                extradata.Add("IdPortal", companycredentialsAux?.PortalId.ToString());
                extradata.Add("CompanyName", companycredentialsAux?.CompanyName.ToString());
                extradata.Add("UserRole", companycredentialsAux?.UserRole.ToString());
                extradata.Add("UserId", companycredentialsAux?.UserId.ToString());
                extradata.Add("UserName", companycredentialsAux?.Username.ToString());
                ExceptionPublisherService.Publish(ex, "CompanyCvsController", "Index-Post", false, extradata);
                return RedirectToAction("Index", "Home");
            }
        }

        private void SendKinesisSearchCV(PortalConfig portalConfig, SearchFilter searchFilter, CompanyCredentials companyCredentials, HomeCvsDataModel homeCvsDataModel)
        {
            var idSession = (System.Web.HttpContext.Current != null
                && System.Web.HttpContext.Current.Session != null
                && !string.IsNullOrEmpty(System.Web.HttpContext.Current.Session.SessionID)) ? System.Web.HttpContext.Current.Session.SessionID : string.Empty;

            var userDeviceTokenList = _userService.GetDeviceTokens((int)companyCredentials.UserId, portalConfig.PortalId);
            var userAgent = Request.UserAgent ?? string.Empty;
            var ua = userAgent.ToLower();

            KinesisEventHelper.SendKinesisSearchCV(new KinesisEntities.EventSearchCV()
            {
                Exact = searchFilter.Exact,
                UserToken = userDeviceTokenList.Count > 0 ? userDeviceTokenList.First().DeviceToken : "0",
                IdUser = companyCredentials.UserId,
                Company = searchFilter.Company,
                PortalId = searchFilter.PortalId,
                ServerTimestamp = DateTime.Now,
                ClientTimestamp = ConvertToTimestamp(homeCvsDataModel.UserDate),
                Platform = ua,
                UserAgent = Request.UserAgent,
                Device = Request.UserAgent ?? "",
                Source = Request.Browser.Browser,
                IdApp = portalConfig.idapp,
                IdPage = PAGE_ID,
                NumResults = homeCvsDataModel.Pager.TotalRows,
                IdSession = idSession,
                PageNumber = searchFilter.PageNumber,
                SearchWord = searchFilter.Query,
                SearchCompany = searchFilter.CompanyExperience,
                Visualization = searchFilter.IsVisualized,
                CategoryIds = searchFilter.CategoryIds,
                MinYearsExperience = searchFilter.MinYearsExperience,
                MaxYearsExperience = searchFilter.MaxYearsExperience,
                IsWorking = searchFilter.IsWorking,
                HasDisability = searchFilter.HasDisability,
                Department = searchFilter.LocalizationIds,
                Nationalities = searchFilter.Nationalities,
                CityIds = searchFilter.CityIds,
                LastUpdate = searchFilter.LastLogin,
                University = searchFilter.University,
                LevelStudyIds = searchFilter.LevelStudyIds,
                IsStudying = searchFilter.IsStudying,
                LanguagesIds = searchFilter.LanguagesIds,
                MinSalary = searchFilter.MinSalary,
                MaxSalary = searchFilter.MaxSalary,
                CommentedIds = searchFilter.CommentedIds,
                Valuations = searchFilter.IsRating,
                HasPhoto = searchFilter.HasPhoto,
                NIT = searchFilter.Nit,
                OrderSearch = searchFilter.OrderSearch,
                ClientIP = _clientIpAddressResolverService.GetIpAddress(),
                CandidateInfo = FillCandidateInfoForKinesis(homeCvsDataModel.Candidates)
            });
        }

        private DateTime ConvertToTimestamp(string jsDate)
        {
            if (DateTime.TryParse(jsDate, CultureInfo.InvariantCulture, DateTimeStyles.AdjustToUniversal | DateTimeStyles.AssumeLocal, out DateTime dateTime))
            {
                return dateTime;
            }
            return new DateTime();
        }

        public class CandidateInfo
        {
            public int IdCandidate { get; set; }
            public int Score { get; set; }
            public int Position { get; set; }
        }

        private Array FillCandidateInfoForKinesis(List<CandidateReadSearchDataModel> candidates)
        {
            var candidateInfo = new List<CandidateInfo>();
            foreach (var item in candidates)
            {
                int.TryParse(item.Score, out int ScoreInt);
                candidateInfo.Add(new CandidateInfo() { IdCandidate = item.idcandidate, Position = candidateInfo.Count() + 1, Score = ScoreInt });
            }

            return candidateInfo.ToArray();
        }


        public ActionResult ClearFilter()
        {
            _sessionService.Forget(typeof(HomeCvsDataModel));
            return RedirectToAction("Index");
        }

        private HomeCvsDataModel GetHomeDataModel(HomeCvsDataModel homeCvsDataModel, bool remember, PortalConfig portalConfig, CompanyCredentials companyCredentials)
        {
            var companyProduct = CompanyProductService.GetByIdCompany(companyCredentials.IdCompany, companyCredentials.PortalId, OfferIntegratorEnum.CompuTrabajo);
            homeCvsDataModel.HomeCvsPackAndBasicDataModel.ItsBBDDcvProdActivated = portalConfig.AEPortalConfig.ActivateCompanyCvsConsumible && companyProduct.GroupId != (short)ProductGroupsEnum.Membership;

            FillBBDDcvUnits(homeCvsDataModel, portalConfig, companyCredentials, companyProduct);

            ModelState.Clear();
            homeCvsDataModel.IsNuggetCv = ConfigurationManager.AppSettings["SEARCH_CANDIDATES_BY_API"] != null && ConfigurationManager.AppSettings["SEARCH_CANDIDATES_BY_API"].ToLower() == "true";
            homeCvsDataModel.IsNewFiltersActive = ConfigurationManager.AppSettings["NEW_FILTERS_CVS"] != null && ConfigurationManager.AppSettings["NEW_FILTERS_CVS"].ToLower() == "true";
            homeCvsDataModel.HaveMembershipActive = (companyProduct.GroupId == (short)ProductGroupsEnum.Membership);
            homeCvsDataModel.PortalId = portalConfig.PortalId;
            homeCvsDataModel.Pager.PageSizeSelected = PAGE_SIZE;
            homeCvsDataModel.IdCompany = companyCredentials.IdCompany;

            FillMultifiltersDataModel(homeCvsDataModel.MultifiltersDataModel, companyProduct, portalConfig, homeCvsDataModel.HaveMembershipActive);

            if (homeCvsDataModel.MultifiltersDataModel.ShowSaveFilter)
            {
                this.ApplySavedFilter(homeCvsDataModel.MultifiltersDataModel, companyCredentials);
            }

            SetFoldersPopUps(homeCvsDataModel, portalConfig);
            SetFilterPopUps(homeCvsDataModel, portalConfig);

            if (!homeCvsDataModel.HaveMembershipActive &&
                !homeCvsDataModel.HomeCvsPackAndBasicDataModel.ItsBBDDcvProdActivated)
                return homeCvsDataModel;

            DeleteMultifilterTags(homeCvsDataModel.MultifiltersDataModel, homeCvsDataModel.IsNuggetCv);
            FillMultifilterPropertiesForCvDetail(homeCvsDataModel.MultifiltersDataModel);
            FillOrderBy(homeCvsDataModel.MultifiltersDataModel, portalConfig);

            var searchFilter = GetElasticFilter(homeCvsDataModel.MultifiltersDataModel, homeCvsDataModel.Pager, portalConfig, companyCredentials, homeCvsDataModel.IsNuggetCv, homeCvsDataModel.IsNewFiltersActive);
            FillCandidates(homeCvsDataModel, searchFilter, companyCredentials, portalConfig, homeCvsDataModel.IsNuggetCv, homeCvsDataModel.IsNewFiltersActive, companyProduct);

            homeCvsDataModel.MultifiltersDataModel.SearchText = string.Empty;
            homeCvsDataModel.MultifiltersDataModel.SearchNit = string.Empty;
            homeCvsDataModel.MultifiltersDataModel.OrderDropDownList = GetOrderDropDownList(portalConfig);

            if (remember && homeCvsDataModel.HaveMembershipActive)
                _sessionService.Remember(homeCvsDataModel);

            MapToDataLayer(homeCvsDataModel, companyCredentials, portalConfig.PortalId.ToString());

            return homeCvsDataModel;
        }

        private void FillBBDDcvUnits(HomeCvsDataModel homeCvsDataModel, PortalConfig portalConfig, CompanyCredentials companyCredentials, CompanyProductEntity companyProduct)
        {
            if (portalConfig.AEPortalConfig.ActivateCompanyCvsConsumible && companyProduct.GroupId != (short)ProductGroupsEnum.Membership)
            {
                var BBDDcvProducts = CompanyProductService.GetBBDDsProductsEntitiy(companyCredentials.IdCompany, companyCredentials.PortalId);
                homeCvsDataModel.TotalAllowedCvVisualization = BBDDcvProducts.AvailableUnits;
                homeCvsDataModel.InitialBBDDCvVisualization = BBDDcvProducts.InitialUnits;

                if (portalConfig.AEPortalConfig.ActivatePrimeProduct && companyCredentials.TestAB == TestABEnum.A)
                {
                    var companyActiveProducts = CompanyProductService.GetAllCompanyActiveProducts(companyCredentials.IdCompany, companyCredentials.PortalId);
                    var primeProduct = companyActiveProducts.Where(p => p.SubGroupId == (short)ProductSubGroupsEnum.Prime).ToList();
                    primeProduct = primeProduct.Where(x => x.Features.Find(y => y.AmbitId == (short)ProductAmbitEnum.CvBBDD)?.AvailableUnits > 0).ToList();
                    if (primeProduct.Count() > 0)
                    {
                        homeCvsDataModel.TotalAllowedCvVisualization += primeProduct.Sum(x => x.Features.Find(n => n.AmbitId == (short)ProductAmbitEnum.CvBBDD).AvailableUnits);
                        homeCvsDataModel.InitialBBDDCvVisualization += primeProduct.Sum(x => x.Features.Find(n => n.AmbitId == (short)ProductAmbitEnum.CvBBDD).InitialUnits);
                    }
                }

                var listProductsByPage = ProductService.SearchByPage(GetProductSearchByPage(portalConfig.PortalId, PageEnum.PackCVSearch, companyCredentials.IdCompany, portalConfig.active_promotions, IsDisableFreemiumOffers(companyCredentials.IdCompany, portalConfig)));
                homeCvsDataModel.IdProductBBDDcv = listProductsByPage.Find(x => x.SubGroupId == (short)ProductSubGroupsEnum.BBDDcv)?.Id ?? 0;
            }
            else if (companyProduct.Features.Exists(x => x.AmbitId == (short)ProductAmbitEnum.CvBBDD))
                homeCvsDataModel.TotalAllowedCvVisualization = CompanyProductService.GetTotalAllowedCvVisualization(companyProduct, portalConfig, companyCredentials);
        }

        private void ApplySavedFilter(MultifiltersDataModel dataModel, CompanyCredentials companyCredentials)
        {
            dataModel.SavedFilters = this._companyFiltersService.GetCompanyFiltersByIdPage(companyCredentials.IdCompany, (int)companyCredentials.UserId, companyCredentials.PortalId, PAGE_ID);
            dataModel.SelectSavedFilters = this.GetSelectListSavedFilters(dataModel.SavedFilters);

            if (!string.IsNullOrEmpty(dataModel.EncryptedSaveFilterSelected) && dataModel.SavedFilters.Count > 0)
            {
                int.TryParse(_encryptionService.Decrypt(dataModel.EncryptedSaveFilterSelected), out var decryptSaveFilterSelected);

                CompanyFilterEntity filterSavedToApply = dataModel.SavedFilters.Exists(filter => filter.FilterId == decryptSaveFilterSelected)
                          ? dataModel.SavedFilters.FirstOrDefault(filter => filter.FilterId == decryptSaveFilterSelected)
                          : new CompanyFilterEntity();

                if (filterSavedToApply.FilterId > 0)
                {
                    dataModel.SelectedCities = filterSavedToApply.Cities;
                    dataModel.Comments = filterSavedToApply.Comment;
                    dataModel.IsCvVisualitzed = filterSavedToApply.CvVisualitzed;
                    dataModel.SelectedLanguage = filterSavedToApply.Language;
                    dataModel.SelectedLanguageLevel = filterSavedToApply.LanguageLevel;
                    dataModel.SelectedLocalization = filterSavedToApply.Localizations;
                    dataModel.MaxAge = filterSavedToApply.MaxAge;
                    dataModel.MinAge = filterSavedToApply.MinAge;
                    dataModel.MaxSalary = filterSavedToApply.MaxSalary;
                    dataModel.MinSalary = filterSavedToApply.MinSalary;
                    dataModel.SelectedNationatility = filterSavedToApply.Nationalities;
                    dataModel.HasPhoto = filterSavedToApply.Photo;
                    dataModel.Gender = filterSavedToApply.Gender;
                    dataModel.SelectedProfesionalCategories = filterSavedToApply.ProfesionalCategories;
                    dataModel.Rating = filterSavedToApply.Rating;
                    //dataModel.WithoutRating = filterSavedToApply.Rating == 0;
                    dataModel.MultiSearchText = filterSavedToApply.SearchName;
                    dataModel.ExactSearchText = filterSavedToApply.ExactSearch;
                    dataModel.SearchNit = filterSavedToApply.SearchNit;
                    dataModel.SelectedStudyLevel = filterSavedToApply.StudyLevels;
                    dataModel.IsWorking = filterSavedToApply.IsWorking;
                    dataModel.StudyAndStatus = filterSavedToApply.StudyAndStatus;
                    dataModel.IsStudying = filterSavedToApply.IsStudying;
                    dataModel.HasDisability = filterSavedToApply.HasDisability;
                    dataModel.MinYearsExperience = filterSavedToApply.MinYearsExperience;
                    dataModel.StudyStatus = filterSavedToApply.StudyStatus;
                    dataModel.MaxYearsExperience = filterSavedToApply.MaxYearsExperience;
                    dataModel.LastLogin = filterSavedToApply.LastLogin;
                }
            }
        }

        private void FillOrderBy(MultifiltersDataModel multifiltersDataModel, PortalConfig portalConfig)
        {
            multifiltersDataModel.CustomOrderDirectionDropDownList.Add(new SelectListItem()
            {
                Text = PageLiteralsHelper.GetLiteral("ORDERBY_ASC", PAGE_ID, portalConfig),
                Value = ((int)OrderSearchDirectionEnum.asc).ToString()
            });
            multifiltersDataModel.CustomOrderDirectionDropDownList.Add(new SelectListItem()
            {
                Text = PageLiteralsHelper.GetLiteral("ORDERBY_DESC", PAGE_ID, portalConfig),
                Value = ((int)OrderSearchDirectionEnum.desc).ToString()
            });
        }

        private List<SelectListItem> GetOrderDropDownList(PortalConfig portalConfig)
        {
            return new List<SelectListItem>
            {
                new SelectListItem { Text = PageLiteralsHelper.GetLiteral("RELEVANCY", PAGE_ID, portalConfig), Value = ((short)CvOrderEnum.Relevance).ToString() },
                new SelectListItem { Text = PageLiteralsHelper.GetLiteral("DATELASTUP", PAGE_ID, portalConfig), Value = ((short)CvOrderEnum.UpdateDate).ToString() }
            };
        }

        private void FillMultifiltersDataModel(MultifiltersDataModel multifiltersDataModel, CompanyProductEntity companyProduct, PortalConfig portalConfig, bool haveMembershipActive)
        {
            if (!haveMembershipActive &&
                portalConfig.AEPortalConfig.ActivateCompanyCvsConsumible)
            {
                var companyActiveProducts = CompanyProductService.GetAllCompanyActiveProducts(companyProduct.IdCompany, companyProduct.PortalId);
                var BBDDcvProducts = companyActiveProducts.Where(p => p.SubGroupId == (short)ProductSubGroupsEnum.BBDDcv).ToList();
                BBDDcvProducts = BBDDcvProducts.Where(x => x.Features.Find(y => y.AmbitId == (short)ProductAmbitEnum.CvBBDD)?.AvailableUnits > 0).ToList();
                if (BBDDcvProducts.Count() > 0)
                {
                    companyProduct = BBDDcvProducts.First();
                }
            }

            multifiltersDataModel.PortalId = portalConfig.PortalId;
            multifiltersDataModel.ShowVisualitzedCvsFilter = portalConfig.AEPortalConfig.ActivateCompanyCvsConsumible || (companyProduct.Features.Any(f => f.AmbitId == (short)ProductAmbitEnum.CvBBDD && !f.IsUnlimited));
            multifiltersDataModel.ShowRatingFilter = companyProduct.Features.Any(f => f.AmbitId == (short)ProductAmbitEnum.RatingsCvs);
            multifiltersDataModel.ShowNitFilter = portalConfig.search_by_nit && haveMembershipActive;
            multifiltersDataModel.ShowCommentFilter = companyProduct.Features.Any(f => f.AmbitId == (short)ProductAmbitEnum.CommentsCV);
            multifiltersDataModel.ShowCustomFoldersFilter = companyProduct.Features.Any(f => f.AmbitId == (short)ProductAmbitEnum.CustomFolders);
            multifiltersDataModel.ShowSaveFilter = companyProduct.Features.Any(productFeature => productFeature.AmbitId.Equals((short)ProductAmbitEnum.SaveFilter));
            multifiltersDataModel.HasTalentViewFeature = companyProduct.Features.Exists(f => f.AmbitId == (short)ProductAmbitEnum.TalentView3D);
        }

        private void SetFoldersPopUps(HomeCvsDataModel homeCvsDataModel, PortalConfig portalConfig)
        {
            //Obsoleto con NewPem
            if (homeCvsDataModel.MultifiltersDataModel.ShowCustomFoldersFilter)
            {
                homeCvsDataModel.UpdateFolderPopUp = SetUpdateFolderPopUp(portalConfig);
                homeCvsDataModel.AddFolderPopUp = SetAddFolderPopUp(portalConfig);
                homeCvsDataModel.MaxCvFolderPopUp = SetMaxCvFolderPopUp(portalConfig);
                homeCvsDataModel.NotViewCvFolderPopUp = SetNotViewCvFolderPopUp(portalConfig);
                homeCvsDataModel.NotAllCvFolderPopUp = SetNotAllCvFolderPopUp(portalConfig);
                homeCvsDataModel.AdvertFolderPopUp = SetAdvertFolderPopUp(portalConfig);
                homeCvsDataModel.CandidatesSelectedPopUp = SetCandidatesSelectedPopUp(portalConfig);
                homeCvsDataModel.DeleteCvsFolderPopUp = SetDeleteCvsFolderPopUp(portalConfig);
                homeCvsDataModel.DeleteFolderPopUp = SetDeleteFolderPopUp(portalConfig);
                homeCvsDataModel.SelectCvsPopUp = SetSelectCvsPopUp(portalConfig);
            }
        }

        private void SetFilterPopUps(HomeCvsDataModel homeCvsDataModel, PortalConfig portalConfig)
        {
            if (homeCvsDataModel.MultifiltersDataModel.ShowSaveFilter)
            {
                homeCvsDataModel.SaveFilterLimitPopUp = this.FillPopUpLimitSaveFilters(portalConfig);
                homeCvsDataModel.SaveFilterPopUp = this.FillPopUpSaveFilters(portalConfig);
                homeCvsDataModel.RemoveSaveFilterPopUp = this.FillPopUpRemoveSaveFilter(portalConfig);
            }

        }

        private PopUpDataModel FillPopUpLimitSaveFilters(PortalConfig portalConfig)
        {
            return new PopUpDataModel()
            {
                Title = PageLiteralsHelper.GetLiteral("LIT_SAVE_FILTER_LIMIT_TITLE", PAGE_ID, portalConfig),
                Message = $"{PageLiteralsHelper.GetLiteral("LIT_SAVE_FILTER_LIMIT_INFO_1", PAGE_ID, portalConfig)} {PageLiteralsHelper.GetLiteral("LIT_SAVE_FILTER_LIMIT_INFO_2", PAGE_ID, portalConfig)}",
                HasButtonOk = false
            };
        }

        private PopUpDataModel FillPopUpSaveFilters(PortalConfig portalConfig)
        {
            return new PopUpDataModel()
            {
                Title = PageLiteralsHelper.GetLiteral("LIT_SAVE_FILTER", PAGE_ID, portalConfig),
                Message = PageLiteralsHelper.GetLiteral("LIT_SAVE_FILTER_OFFER", PAGE_ID, portalConfig),
                HasInput = true,
                HasCheckBox = false,
                TitleBtnOk = PageLiteralsHelper.GetLiteral("LIT_SAVE_FILTER", PAGE_ID, portalConfig),
                LiteralProcessing = PageLiteralsHelper.GetLiteral("LIT_PROCESSING", PAGE_ID, portalConfig),
                PlaceHolderInput = PageLiteralsHelper.GetLiteral("LIT_WRITE_FILTER_NAME", PAGE_ID, portalConfig),
                LiteralError = "errorNameSaveFilter"
            };
        }

        private PopUpDataModel FillPopUpRemoveSaveFilter(PortalConfig portalConfig)
        {
            return new PopUpDataModel()
            {
                Title = PageLiteralsHelper.GetLiteral("LIT_DELETE_SAVED_FILTER", PAGE_ID, portalConfig),
                Message = PageLiteralsHelper.GetLiteral("LIT_DELETE_SAVED_FILTER_CONFIRM", PAGE_ID, portalConfig),
                TitleBtnOk = PageLiteralsHelper.GetLiteral("LIT_ELIMINAR", PAGE_ID, portalConfig),
                LiteralProcessing = PageLiteralsHelper.GetLiteral("LIT_PROCESSING", PAGE_ID, portalConfig)
            };
        }

        private PopUpDataModel SetSelectCvsPopUp(PortalConfig portalConfig)
        {
            return new PopUpDataModel()
            {
                HasInput = false,
                HasButtonOk = false,
                Message = PageLiteralsHelper.GetLiteral("LIT_SELECT_CANDIDATES_DESC", PAGE_ID, portalConfig),
                Title = PageLiteralsHelper.GetLiteral("LIT_SELECT_CANDIDATES", PAGE_ID, portalConfig)
            };
        }

        private PopUpDataModel SetDeleteFolderPopUp(PortalConfig portalConfig)
        {
            return new PopUpDataModel()
            {
                HasInput = false,
                HasButtonOk = true,
                Message = PageLiteralsHelper.GetLiteral("LIT_DELETE_DESCRIPTION", PAGE_ID, portalConfig),
                LiteralProcessing = PageLiteralsHelper.GetLiteral("LIT_PROCESSING", PAGE_ID, portalConfig),
                Title = PageLiteralsHelper.GetLiteral("LIT_DELETE_FOLDER", PAGE_ID, portalConfig),
                TitleBtnKo = PageLiteralsHelper.GetLiteral("LIT_BTN_CANCELAR", PAGE_ID, portalConfig),
                TitleBtnOk = PageLiteralsHelper.GetLiteral("LIT_DELETE", PAGE_ID, portalConfig),
                LiteralError = "errDeleteFolder"
            };
        }

        private PopUpDataModel SetDeleteCvsFolderPopUp(PortalConfig portalConfig)
        {
            return new PopUpDataModel()
            {
                HasInput = false,
                HasButtonOk = true,
                Message = PageLiteralsHelper.GetLiteral("LIT_MESSAGE_DELETE_CV_FOLDER", PAGE_ID, portalConfig),
                LiteralProcessing = PageLiteralsHelper.GetLiteral("LIT_PROCESSING", PAGE_ID, portalConfig),
                Title = PageLiteralsHelper.GetLiteral("TITLE_DELETE", PAGE_ID, portalConfig),
                TitleBtnKo = PageLiteralsHelper.GetLiteral("LIT_BTN_CANCELAR", PAGE_ID, portalConfig),
                TitleBtnOk = PageLiteralsHelper.GetLiteral("LIT_BTN_DELETE", PAGE_ID, portalConfig),
                LiteralError = "errDeleteCvsFolder"
            };
        }

        //Obsoleto con NewPem
        private PopUpDataModel SetCandidatesSelectedPopUp(PortalConfig portalConfig)
        {
            return new PopUpDataModel()
            {
                HasInput = false,
                HasButtonOk = false,
                Message = PageLiteralsHelper.GetLiteral("LIT_SELECT_CANDIDATES_DESC", PAGE_ID, portalConfig),
                Title = PageLiteralsHelper.GetLiteral("LIT_SELECT_CANDIDATES", PAGE_ID, portalConfig),
                TitleBtnKo = PageLiteralsHelper.GetLiteral("LIT_CERRAR", PAGE_ID, portalConfig)
            };
        }
        //

        private PopUpDataModel SetAddFolderPopUp(PortalConfig portalConfig)
        {
            return new PopUpDataModel()
            {
                HasInput = true,
                HasButtonOk = true,
                Message = PageLiteralsHelper.GetLiteral("LIT_NAME_FOLDER", PAGE_ID, portalConfig),
                LiteralError = "errAddFolder",
                LiteralProcessing = PageLiteralsHelper.GetLiteral("LIT_PROCESSING", PAGE_ID, portalConfig),
                Title = PageLiteralsHelper.GetLiteral("LIT_ADD_FODLER", PAGE_ID, portalConfig),
                TitleBtnKo = PageLiteralsHelper.GetLiteral("LIT_CERRAR", PAGE_ID, portalConfig),
                TitleBtnOk = PageLiteralsHelper.GetLiteral("LIT_ADD", PAGE_ID, portalConfig)
            };
        }

        private PopUpDataModel SetAdvertFolderPopUp(PortalConfig portalConfig)
        {
            return new PopUpDataModel()
            {
                HasInput = false,
                HasButtonOk = false,
                Message = PageLiteralsHelper.GetLiteral("LIT_ADVERT_DESCRIPTION", PAGE_ID, portalConfig),
                Title = PageLiteralsHelper.GetLiteral("LIT_ADVERT", PAGE_ID, portalConfig),
                TitleBtnKo = PageLiteralsHelper.GetLiteral("LIT_CERRAR", PAGE_ID, portalConfig)
            };
        }

        private PopUpDataModel SetNotAllCvFolderPopUp(PortalConfig portalConfig)
        {
            return new PopUpDataModel()
            {
                HasInput = false,
                HasButtonOk = false,
                Message = PageLiteralsHelper.GetLiteral("LIT_NOT_ALL_CV_DESCRIPTION", PAGE_ID, portalConfig),
                Title = PageLiteralsHelper.GetLiteral("LIT_NOT_ALL_CV_TITLE", PAGE_ID, portalConfig),
                TitleBtnKo = PageLiteralsHelper.GetLiteral("LIT_CERRAR", PAGE_ID, portalConfig)
            };
        }

        private PopUpDataModel SetNotViewCvFolderPopUp(PortalConfig portalConfig)
        {
            return new PopUpDataModel()
            {
                HasInput = false,
                HasButtonOk = false,
                Message = PageLiteralsHelper.GetLiteral("LIT_CV_BLOQUED_DESCRIPTION", PAGE_ID, portalConfig),
                Title = PageLiteralsHelper.GetLiteral("LIT_CV_BLOQUED", PAGE_ID, portalConfig),
                TitleBtnKo = PageLiteralsHelper.GetLiteral("LIT_CERRAR", PAGE_ID, portalConfig)
            };
        }

        private PopUpDataModel SetMaxCvFolderPopUp(PortalConfig portalConfig)
        {
            return new PopUpDataModel()
            {
                HasInput = false,
                HasButtonOk = false,
                Message = PageLiteralsHelper.GetLiteral("LIT_MAX_CV_IN_FOLDER", PAGE_ID, portalConfig),
                Title = PageLiteralsHelper.GetLiteral("LIT_MAX_CV", PAGE_ID, portalConfig),
                TitleBtnKo = PageLiteralsHelper.GetLiteral("LIT_CERRAR", PAGE_ID, portalConfig)
            };
        }

        private PopUpDataModel SetUpdateFolderPopUp(PortalConfig portalConfig)
        {
            return new PopUpDataModel()
            {
                HasInput = true,
                HasButtonOk = true,
                Message = PageLiteralsHelper.GetLiteral("LIT_NAME_FOLDER", PAGE_ID, portalConfig),
                LiteralError = "errUpdateFolder",
                LiteralProcessing = PageLiteralsHelper.GetLiteral("LIT_PROCESSING", PAGE_ID, portalConfig),
                Title = PageLiteralsHelper.GetLiteral("LIT_UPDATE_FOLDER", PAGE_ID, portalConfig),
                TitleBtnKo = PageLiteralsHelper.GetLiteral("LIT_CERRAR", PAGE_ID, portalConfig),
                TitleBtnOk = PageLiteralsHelper.GetLiteral("LIT_UPDATE", PAGE_ID, portalConfig)
            };
        }

        private void DeleteMultifilterTags(MultifiltersDataModel multifiltersDataModel, bool isNuggetCv)
        {
            if (!string.IsNullOrEmpty(multifiltersDataModel.SelectedCitiesDelete))
            {
                int idToDelete = 0;
                int.TryParse(multifiltersDataModel.SelectedCitiesDelete, out idToDelete);
                multifiltersDataModel.SelectedCities.Remove(idToDelete);
                multifiltersDataModel.SelectedCitiesDelete = string.Empty;
            }

            if (!string.IsNullOrEmpty(multifiltersDataModel.HasPhotoDelete))
            {
                multifiltersDataModel.HasPhoto = 0;
                multifiltersDataModel.HasPhotoDelete = string.Empty;
            }

            if (!string.IsNullOrEmpty(multifiltersDataModel.RatingDelete))
            {
                multifiltersDataModel.Rating = null;
                multifiltersDataModel.RatingDelete = string.Empty;
            }

            if (!string.IsNullOrEmpty(multifiltersDataModel.CommentsDelete))
            {
                multifiltersDataModel.Comments = 0;
                multifiltersDataModel.CommentsDelete = string.Empty;
            }

            if (!string.IsNullOrEmpty(multifiltersDataModel.LastLoginDelete))
            {
                multifiltersDataModel.LastLogin = 0;
                multifiltersDataModel.LastLoginDelete = string.Empty;
            }

            if (!string.IsNullOrEmpty(multifiltersDataModel.MaxSalaryDelete))
            {
                multifiltersDataModel.MaxSalary = null;
                multifiltersDataModel.MaxSalaryDelete = string.Empty;
            }

            if (!string.IsNullOrEmpty(multifiltersDataModel.MinSalaryDelete))
            {
                multifiltersDataModel.MinSalary = null;
                multifiltersDataModel.MinSalaryDelete = string.Empty;
            }

            if (!string.IsNullOrEmpty(multifiltersDataModel.SelectedLanguageDelete))
            {
                multifiltersDataModel.SelectedLanguage = null;
                multifiltersDataModel.SelectedLanguageLevel = null;
                multifiltersDataModel.SelectedLanguageDelete = string.Empty;
            }

            if (!string.IsNullOrEmpty(multifiltersDataModel.GenderDelete))
            {
                multifiltersDataModel.Gender = 0;
                multifiltersDataModel.GenderDelete = string.Empty;
            }

            if (!string.IsNullOrEmpty(multifiltersDataModel.MinAgeDelete))
            {
                multifiltersDataModel.MinAge = null;
                multifiltersDataModel.MinAgeDelete = string.Empty;
            }

            if (!string.IsNullOrEmpty(multifiltersDataModel.MaxAgeDelete))
            {
                multifiltersDataModel.MaxAge = null;
                multifiltersDataModel.MaxAgeDelete = string.Empty;
            }

            if (!string.IsNullOrEmpty(multifiltersDataModel.IsCvVisualitzedDelete))
            {
                multifiltersDataModel.IsCvVisualitzed = 0;
                multifiltersDataModel.IsCvVisualitzedDelete = string.Empty;
            }

            if (!string.IsNullOrEmpty(multifiltersDataModel.SelectedStudyLevelDelete))
            {
                int idToDelete = 0;
                int.TryParse(multifiltersDataModel.SelectedStudyLevelDelete, out idToDelete);
                multifiltersDataModel.SelectedStudyLevel.Remove(idToDelete);
                multifiltersDataModel.SelectedStudyLevelDelete = string.Empty;
            }

            if (!string.IsNullOrEmpty(multifiltersDataModel.SelectedNationatilityDelete))
            {
                int idToDelete = 0;
                int.TryParse(multifiltersDataModel.SelectedNationatilityDelete, out idToDelete);
                multifiltersDataModel.SelectedNationatility.Remove(idToDelete);
                multifiltersDataModel.SelectedNationatilityDelete = string.Empty;
            }

            if (!string.IsNullOrEmpty(multifiltersDataModel.SelectedLocalizationDelete))
            {
                int idToDelete = 0;
                int.TryParse(multifiltersDataModel.SelectedLocalizationDelete, out idToDelete);
                multifiltersDataModel.SelectedLocalization.Remove(idToDelete);
                multifiltersDataModel.SelectedLocalizationDelete = string.Empty;
            }

            if (!string.IsNullOrEmpty(multifiltersDataModel.SelectedProfesionalCategoriesDelete))
            {
                int idToDelete = 0;
                int.TryParse(multifiltersDataModel.SelectedProfesionalCategoriesDelete, out idToDelete);
                multifiltersDataModel.SelectedProfesionalCategories.Remove(idToDelete);
                multifiltersDataModel.SelectedProfesionalCategoriesDelete = string.Empty;
            }

            if (!string.IsNullOrEmpty(multifiltersDataModel.SelectedProfesionalCategoriesDelete))
            {
                int idToDelete = 0;
                int.TryParse(multifiltersDataModel.SelectedProfesionalCategoriesDelete, out idToDelete);
                multifiltersDataModel.SelectedProfesionalCategories.Remove(idToDelete);
                multifiltersDataModel.SelectedProfesionalCategoriesDelete = string.Empty;
            }

            if (!string.IsNullOrEmpty(multifiltersDataModel.HasTalentViewTestDelete))
            {
                multifiltersDataModel.HasTalentViewTest = (short)HasTalentViewTestEnum.None;
                multifiltersDataModel.HasTalentViewTestDelete = string.Empty;
            }


            if (isNuggetCv)
            {
                if (!string.IsNullOrEmpty(multifiltersDataModel.IsWorkingDelete))
                {

                    multifiltersDataModel.IsWorkingDelete = string.Empty;
                    multifiltersDataModel.IsWorking = (short)WorkingStatusEnum.None;
                }

                if (!string.IsNullOrEmpty(multifiltersDataModel.NameUniversityDelete))
                {

                    multifiltersDataModel.NameUniversityDelete = string.Empty;
                    multifiltersDataModel.NameUniversity = string.Empty;
                }

                if (!string.IsNullOrEmpty(multifiltersDataModel.MinYearsExperienceDelete))
                {
                    multifiltersDataModel.MinYearsExperienceDelete = string.Empty;
                    multifiltersDataModel.MinYearsExperience = null;
                }

                if (!string.IsNullOrEmpty(multifiltersDataModel.MaxYearsExperienceDelete))
                {
                    multifiltersDataModel.MaxYearsExperienceDelete = string.Empty;
                    multifiltersDataModel.MaxYearsExperience = null;
                }

                if (!string.IsNullOrEmpty(multifiltersDataModel.CompanyNameDelete))
                {

                    multifiltersDataModel.CompanyNameDelete = string.Empty;
                    multifiltersDataModel.CompanyName = string.Empty;
                }

                if (!string.IsNullOrEmpty(multifiltersDataModel.StudyingStatusDelete))
                {

                    multifiltersDataModel.StudyingStatusDelete = string.Empty;
                    multifiltersDataModel.IsStudying = (short)StudyingStatusEnum.None;
                }

                if (!string.IsNullOrEmpty(multifiltersDataModel.StudyStatusDelete))
                {

                    multifiltersDataModel.StudyStatusDelete = string.Empty;
                    multifiltersDataModel.StudyStatus = (short)StudyingStatusEnum.None;
                }

                if (!string.IsNullOrEmpty(multifiltersDataModel.HasDisabilityStatusDelete))
                {

                    multifiltersDataModel.HasDisabilityStatusDelete = string.Empty;
                    multifiltersDataModel.HasDisability = (short)HasDisabilityStatusEnum.None;
                }

            }

            List<string> MultiSearchList = new List<string>();
            if (!string.IsNullOrEmpty(multifiltersDataModel.MultiSearchText))
                MultiSearchList.Add(multifiltersDataModel.MultiSearchText);
            if (!string.IsNullOrEmpty(multifiltersDataModel.MultiSearchDelete))
            {
                if (multifiltersDataModel.MultiSearchText != null)
                {
                    MultiSearchList = multifiltersDataModel.MultiSearchText.Split(new string[] { " AND " }, StringSplitOptions.None).ToList<string>();
                    MultiSearchList.Remove(multifiltersDataModel.MultiSearchDelete);
                }
            }
            multifiltersDataModel.MultiSearchDelete = string.Empty;

            if (!string.IsNullOrEmpty(multifiltersDataModel.SearchText))
                MultiSearchList.Add(multifiltersDataModel.SearchText);

            multifiltersDataModel.SearchText = string.Join(" AND ", MultiSearchList);
            multifiltersDataModel.MultiSearchText = string.Join(" AND ", MultiSearchList);

            List<string> multiSearchNit = new List<string>();
            if (!string.IsNullOrEmpty(multifiltersDataModel.MultiSearchNitText))
                multiSearchNit.Add(multifiltersDataModel.MultiSearchNitText);
            if (!string.IsNullOrEmpty(multifiltersDataModel.MultiSearchNitDelete))
            {
                if (multifiltersDataModel.MultiSearchNitText != null)
                {
                    multiSearchNit = multifiltersDataModel.MultiSearchNitText.Split(new string[] { " OR " }, StringSplitOptions.None).ToList<string>();
                    multiSearchNit.Remove(multifiltersDataModel.MultiSearchNitDelete);
                }
            }
            multifiltersDataModel.MultiSearchNitDelete = string.Empty;

            if (!string.IsNullOrEmpty(multifiltersDataModel.SearchNit))
                multiSearchNit.Add(multifiltersDataModel.SearchNit);

            multifiltersDataModel.SearchNit = string.Join(" OR ", multiSearchNit);
            multifiltersDataModel.MultiSearchNitText = string.Join(" OR ", multiSearchNit);
        }

        private void FillMultifilterPropertiesForCvDetail(MultifiltersDataModel multifiltersDataModel)
        {

            multifiltersDataModel.ListSearchName = !string.IsNullOrEmpty(multifiltersDataModel.SearchText) ? _encryptionService.Encrypt(multifiltersDataModel.SearchText.Replace(" AND ", ","))
                                                                                                          : string.Empty;
            multifiltersDataModel.IdsProfesionalCategoriesSelected = multifiltersDataModel.SelectedProfesionalCategories.Any()
                                                                                        ? _encryptionService.Encrypt(string.Join(SEPARATOR_SPLIT, multifiltersDataModel.SelectedProfesionalCategories))
                                                                                        : string.Empty;
            multifiltersDataModel.IdsLocalizationsSelected = multifiltersDataModel.SelectedLocalization.Any() ? _encryptionService.Encrypt(string.Join(SEPARATOR_SPLIT, multifiltersDataModel.SelectedLocalization))
                                                                                                    : string.Empty;

        }

        private void PopulateFilterList(MultifiltersDataModel multifiltersDataModel, PortalConfig portalConfig, SearchResult<CandidateReadSearch> candidates, SearchFilter searchFilter, bool isNuggetCv, bool isNewFilter)
        {
            if (isNuggetCv && isNewFilter)
            {
                GetAndSetNewFacets(multifiltersDataModel, portalConfig, candidates.Facets);
                multifiltersDataModel.ProfesionalCategories = SetSelectedsToDictionary(multifiltersDataModel.SelectedProfesionalCategories, GetFromDictionary(portalConfig, DictionaryEnum.CATEGORY));
                multifiltersDataModel.Localizations = SetSelectedsToDictionary(multifiltersDataModel.SelectedLocalization, GetFromDictionaryDependantKey(portalConfig, DictionaryEnum.LOCALIZATION_BY_COUNTRY, dependantKey: portalConfig.countryId));
                multifiltersDataModel.StudyLevels = SetSelectedsToDictionary(multifiltersDataModel.SelectedStudyLevel, GetFromDictionary(portalConfig, DictionaryEnum.EDUCATION_LEVEL));
            }

            if (multifiltersDataModel.SelectedLocalization.Any())
            {
                multifiltersDataModel.Cities = GetCitiesSelectListItems(multifiltersDataModel.SelectedLocalization, multifiltersDataModel.SelectedCities, portalConfig.PortalId);
            }

            multifiltersDataModel.Languages = GetFromDictionary(portalConfig, DictionaryEnum.LANGUAGE, PageEnum.PublishOffer);
            multifiltersDataModel.LanguageLevels = GetFromDictionary(portalConfig, DictionaryEnum.LANGUAGE_LEVEL, PageEnum.PublishOffer);
        }

        private static List<SelectListItem> SetSelectedsToDictionary(List<int> selecteds, List<SelectListItem> dictionary)
        {
            var result = new List<SelectListItem>();

            foreach (var item in dictionary)
            {
                int.TryParse(item.Value, out var id);

                result.Add(new SelectListItem()
                {
                    Value = item.Value,
                    Text = item.Text,
                    Selected = selecteds.Exists(e => e == id)
                });
            }

            if (result.Any())
                result = result.OrderByDescending(o => o.Selected).ToList();

            return result;
        }

        private void GetAndSetNewFacets(MultifiltersDataModel multifiltersDataModel, PortalConfig portalConfig, Dictionary<string, List<FacetResult>> facets)
        {
            if (facets != null &&
                facets.ContainsKey(FacetsByCvEnum.nationality.ToString()))
                multifiltersDataModel.Nationatilities = GetSelectListItemsFromNewFacets(facets, multifiltersDataModel.SelectedNationatility, GetFromDictionary(portalConfig, DictionaryEnum.COUNTRY), FacetsByCvEnum.nationality.ToString(), portalConfig);
        }

        private SearchFilter GetElasticFilter(MultifiltersDataModel m, PagerDataModel pagerDataModel, PortalConfig portalConfig, CompanyCredentials companyCredentials, bool isNuggetCv, bool IsNewFiltersActive)
        {
            if (m.SelectedLocalization.Count == 1 && m.SelectedLocalization[0] == 0)
            {
                m.SelectedLocalization.Clear();
            }

            return new SearchFilter
            {
                PortalId = portalConfig.PortalId,
                Facets = GetFacetsToElastic(isNuggetCv, IsNewFiltersActive),
                IdPrivacy = Convert.ToInt32(PrivacyEnum.Visible),
                PageSize = PAGE_SIZE,
                PageNumber = pagerDataModel.PageSelected,
                OrderSearch = new List<BaseOrderSearch>()
                {
                    _elasticCandidateService.GetOrderSearch(new MultiFiltersDTO(m.Order, m.MultiSearchText, m.NameUniversity, m.CompanyName,
                                                                               m.OrderDirectionSelected, portalConfig.AEPortalConfig.IsPremiumActive))
                },
                Nit = isNuggetCv ? m.SearchNit : string.Empty,
                CandidatesIds = !isNuggetCv
                            ? GetIdCandidatesByNit(m, companyCredentials.IdCompany)
                            : new List<int>(),
                Nationalities = m.SelectedNationatility,
                LocalizationIds = m.SelectedLocalization,
                CityIds = m.SelectedCities,
                CategoryIds = m.SelectedProfesionalCategories,
                LevelStudyIds = m.SelectedStudyLevel,
                AgeMin = m.MinAge ?? 0,
                AgeMax = m.MaxAge ?? 0,
                MinSalary = m.MinSalary ?? 0,
                MaxSalary = m.MaxSalary ?? 0,
                LastLogin = m.LastLogin != 0
                    ? DateTime.Now.AddDays(m.LastLogin * (-1)).Date
                    : DateTime.MinValue,
                Exact = m.ExactSearchText,
                Query = m.ExactSearchText
                    ? $"\"{m.MultiSearchText}\""
                    : m.MultiSearchText,
                IsVisualized = m.IsCvVisualitzed,
                VisualizedIds = !isNuggetCv
                                    ? GetVisualizedCvsIds(companyCredentials, m.IsCvVisualitzed)
                                    : new List<int>(),
                Companies = isNuggetCv
                            && m.IsCvVisualitzed != 0
                            ? new List<int>() { companyCredentials.IdCompany }
                            : new List<int>(),
                Gender = m.Gender != 0
                    ? new List<int>() { m.Gender }
                    : new List<int>(),
                HasPhoto = m.HasPhoto,
                LanguagesIds = TransformLanguageDataModelToLanguageSearchFilter(m.SelectedLanguage, m.SelectedLanguageLevel),
                RatingsIds = GetIdsByRatings(m, companyCredentials.IdCompany),
                IsRating = m.Rating ?? 0,
                CommentedIds = GetIdsByComments(m, companyCredentials.IdCompany, companyCredentials.PortalId),
                IsCommented = m.Comments,
                CompanyFolder = GetCompanyFolders(m, companyCredentials),
                CompanyFolderSelected = GetShortAndDecrypt(m.FolderSelected),
                IsWorking = isNuggetCv && IsNewFiltersActive ? m.IsWorking : (short)0,
                MaxYearsExperience = isNuggetCv && IsNewFiltersActive && m.MaxYearsExperience != null ? (short)m.MaxYearsExperience : (short)0,
                MinYearsExperience = isNuggetCv && IsNewFiltersActive && m.MinYearsExperience != null ? (short)m.MinYearsExperience : (short)0,
                University = isNuggetCv && IsNewFiltersActive ? m.NameUniversity : string.Empty,
                Company = companyCredentials.IdCompany,
                CompanyExperience = isNuggetCv && IsNewFiltersActive ? m.CompanyName : string.Empty,
                IsStudying = isNuggetCv && IsNewFiltersActive ? m.IsStudying : (short)StudyingStatusEnum.None,
                StudyStatus = isNuggetCv && IsNewFiltersActive ? m.StudyStatus : (short)StudyStatusEnum.None,
                HasDisability = isNuggetCv && IsNewFiltersActive ? m.HasDisability : (short)HasDisabilityStatusEnum.None,
                OrderByScore = m.Order == (short)CvOrderEnum.Relevance
            };
        }

        private List<string> GetFacetsToElastic(bool isNuggetCv, bool isNewFiltersActive)
        {
            if (isNuggetCv && isNewFiltersActive)
            {
                return new List<string>() {
                    FacetsByCvEnum.nationality.ToString()
                };
            }
            else
            {
                return new List<string>()
                {
                    FacetsByCvEnum.categories_search.ToString(),
                    FacetsByCvEnum.idlocalization.ToString(),
                    FacetsByCvEnum.idemploymenttype.ToString(),
                    FacetsByCvEnum.idcity.ToString(),
                    FacetsByCvEnum.last_formation_idstudy.ToString(),
                    FacetsByCvEnum.idsalaryread.ToString(),
                    FacetsByCvEnum.hasphoto.ToString(),
                    FacetsByCvEnum.idioms_search.ToString(),
                    FacetsByCvEnum.nationality.ToString()
                };
            }
        }

        private int GetShortAndDecrypt(string folderSelected)
        {
            int result = 0;

            if (!string.IsNullOrEmpty(folderSelected)
                && folderSelected != "0")
                int.TryParse(_encryptionService.Decrypt(folderSelected), out result);

            return result;
        }

        private List<int> GetCompanyFolders(MultifiltersDataModel multifiltersDataModel, CompanyCredentials companyCredentials)
        {
            if (multifiltersDataModel.ShowCustomFoldersFilter
                 && !string.IsNullOrEmpty(multifiltersDataModel.FolderSelected))
            {
                int.TryParse(_encryptionService.Decrypt(multifiltersDataModel.FolderSelected), out var idFolder);

                return _folderService.GetCvsByFolderCustom(new CustomFoldersFilterEntity()
                {
                    Environment = (short)CustomFoldersEnvironmentEnum.CvBBDD,
                    Id = idFolder,
                    IdPortal = companyCredentials.PortalId,
                    IdUser = companyCredentials.UserId
                });
            }

            return new List<int>();
        }

        private List<int> GetIdCandidatesByNit(MultifiltersDataModel multifiltersDataModel, int idCompany)
        {
            if (multifiltersDataModel.ShowNitFilter && idCompany > 0 && !string.IsNullOrEmpty(multifiltersDataModel.SearchNit))
            {
                var listIds = (new List<int>() { _matchService.GetIdCandidateByMatchNitAndCompanyId(multifiltersDataModel.SearchNit, idCompany) }).Union(new List<int>() { CompanyService.GetIdCandidateByCompanyNit(idCompany, multifiltersDataModel.PortalId, multifiltersDataModel.SearchNit) }).ToList();
                return listIds.Distinct().ToList();
            }

            return new List<int>();
        }

        private List<int> GetIdsByComments(MultifiltersDataModel multifiltersDataModel, int idCompany, short idPortal)
        {
            if (multifiltersDataModel.ShowCommentFilter
                && multifiltersDataModel.Comments > 0)
                return _commentCvService.GetIdCandidatesByCompany(idCompany, idPortal);

            return new List<int>();
        }

        private List<int> GetIdsByRatings(MultifiltersDataModel multifiltersDataModel, int idCompany)
        {
            if (multifiltersDataModel.ShowRatingFilter
                && multifiltersDataModel.Rating != null)
                return _ratingCvService.GetIdsByRating(idCompany, (short)multifiltersDataModel.Rating);

            return new List<int>();
        }

        private List<string> TransformLanguageDataModelToLanguageSearchFilter(int? selectedLanguage, int? selectedLanguageLevel)
        {
            var result = new List<string>();

            if (selectedLanguage != null && selectedLanguage > 0)
            {
                var stringBuilder = selectedLanguage.ToString();

                if (selectedLanguageLevel != null)
                    stringBuilder = $"{stringBuilder}_{selectedLanguageLevel}";

                result.Add(stringBuilder);
            }

            return result;
        }

        private List<SelectListItem> GetSelectListItemsFromNewFacets(Dictionary<string, List<FacetResult>> facets, List<int> selecteds, List<SelectListItem> dictionary, string facet, PortalConfig portalConfig)
        {
            var result = new List<SelectListItem>();

            if (!facets.ContainsKey(facet)) return result;

            foreach (var item in facets[facet])
            {
                var dic = dictionary.FirstOrDefault(c => c.Value == item.Key);
                if (dic is null) continue;

                int.TryParse(dic.Value, out var id);

                result.Add(new SelectListItem()
                {
                    Value = dic.Value,
                    Text = dic.Text,
                    Selected = selecteds.Exists(e => e == id)
                });
            }

            if (result.Any())
            {
                if (result.Exists(e => e.Selected))
                    result = result.OrderByDescending(o => o.Selected).ToList();
                else if (result.Exists(e => e.Value == portalConfig.countryId.ToString()))
                {
                    var firstItem = result.Find(f => f.Value == portalConfig.countryId.ToString());
                    result.Remove(firstItem);
                    result.Insert(0, firstItem);
                }
            }
            return result;
        }

        private List<SelectListItem> GetCitiesSelectListItems(List<int> selectedLocalizations, List<int> seletedCities, short portalId)
        {
            var result = new List<SelectListItem>();
            foreach (var localizationId in selectedLocalizations)
            {
                var dicCity = DictionaryService.GetDictionary(DictionaryEnum.CITIES_BY_LOCALIZATION, localizationId, portalId);
                foreach (var city in dicCity)
                {
                    int.TryParse(city.Key, out var id);

                    if (!result.Exists(r => r.Value == city.Key))
                    {
                        result.Add(new SelectListItem()
                        {
                            Value = city.Key,
                            Text = city.Value,
                            Selected = seletedCities.Exists(c => c == id)
                        });
                    }
                }
            }

            if (result.Any())
                result = result.OrderByDescending(o => o.Selected).ToList();

            return result;
        }

        private void FillCandidates(HomeCvsDataModel homeCvsDataModel, SearchFilter searchFilter, CompanyCredentials companyCredentials, PortalConfig portalConfig,
                                    bool isNuggetCv, bool isNewFilter, CompanyProductEntity companyProduct)
        {
            if (searchFilter.IsRating != 0 && !searchFilter.RatingsIds.Any()
                || searchFilter.IsCommented != 0 && !searchFilter.CommentedIds.Any()
                || searchFilter.CompanyFolderSelected != 0 && !searchFilter.CompanyFolder.Any()
                || searchFilter.IsVisualized != 0
                    && !searchFilter.VisualizedIds.Any()
                    && !isNuggetCv)
            {
                homeCvsDataModel.Candidates = new List<CandidateReadSearchDataModel>();
                homeCvsDataModel.Pager.TotalRows = 0;
            }
            else
            {
                var candidates = _elasticCandidateService.GetCandidates(searchFilter);
                PopulateFilterList(homeCvsDataModel.MultifiltersDataModel, portalConfig, candidates, searchFilter, isNuggetCv, isNewFilter);
                homeCvsDataModel.Candidates = CandidateReadSearchDataModelMapping.MappingCandidateReadSearchDTOToCandidateReadSearchDataModel(candidates.Candidates, portalConfig);
                homeCvsDataModel.Pager.TotalRows = candidates.Total;

                if (homeCvsDataModel.Candidates.Any())
                    SetCommentsAndRatings(homeCvsDataModel, companyCredentials, companyProduct);

                SendKinesisSearchCV(portalConfig, searchFilter, companyCredentials, homeCvsDataModel);
            }

            SetCustomFolders(homeCvsDataModel.MultifiltersDataModel, companyCredentials, portalConfig);
        }

        private void SetCustomFolders(MultifiltersDataModel multifiltersDataModel, CompanyCredentials companyCredentials, PortalConfig portalConfig)
        {
            if (multifiltersDataModel.ShowCustomFoldersFilter)
            {
                int.TryParse(_maxFolders, out var maxFolder);
                multifiltersDataModel.CustomFoldersDropDownList = new List<SelectListItem>();
                multifiltersDataModel.CustomFoldersDropDownList.Add(new SelectListItem() { Text = PageLiteralsHelper.GetLiteral("LIT_CV_SEARCH", PAGE_ID, portalConfig), Value = "0" });

                var customFolders = _folderService.GetCustomFolders(new CustomFoldersFilterEntity()
                {
                    Environment = (short)CustomFoldersEnvironmentEnum.CvBBDD,
                    IdUser = companyCredentials.UserId,
                    IdPortal = companyCredentials.PortalId,
                });

                if (customFolders.Any())
                {

                    var foldersToMapper = customFolders.Count > maxFolder ? customFolders.Take(maxFolder).ToList() : customFolders;
                    multifiltersDataModel.CustomFolders = Mapper.Map<List<CustomFolderEntity>, List<FoldersCvDataModel>>(foldersToMapper);
                    FillDropDownList(multifiltersDataModel);
                }

                multifiltersDataModel.CanCreateNewFolder = maxFolder > customFolders.Count();
            }
        }

        private List<SelectListItem> GetSelectListSavedFilters(List<CompanyFilterEntity> saveFilters)
        {
            var result = new List<SelectListItem>();

            if (saveFilters.Any())
            {
                foreach (var item in saveFilters)
                    result.Add(new SelectListItem() { Text = item.FilterName, Value = this._encryptionService.Encrypt(item.FilterId.ToString()) });
            }

            return result;
        }

        private void FillDropDownList(MultifiltersDataModel multifiltersDataModel)
        {
            foreach (var folder in multifiltersDataModel.CustomFolders)
                multifiltersDataModel.CustomFoldersDropDownList.Add(new SelectListItem() { Text = folder.NameFolder, Value = folder.IdEncrypted });
        }

        private void SetCommentsAndRatings(HomeCvsDataModel homeCvsDataModel, CompanyCredentials companyCredentials, CompanyProductEntity companyProduct)
        {
            var ratingDictionary = new Dictionary<int, RatingCvEntity>();
            var commentDictionary = new Dictionary<int, int>();

            if (homeCvsDataModel.MultifiltersDataModel.ShowCommentFilter)
                commentDictionary = _commentCvService.GetCountersComments(companyCredentials.IdCompany);

            if (homeCvsDataModel.MultifiltersDataModel.ShowRatingFilter && (homeCvsDataModel.MultifiltersDataModel.Rating == null || homeCvsDataModel.MultifiltersDataModel.Rating > -1))
                ratingDictionary = _ratingCvService.GetHighRatingsCvsByCompanyAndRating(companyCredentials.IdCompany, homeCvsDataModel.MultifiltersDataModel.Rating ?? 0);

            homeCvsDataModel.Candidates = MatchWithCommentsAndRatings(homeCvsDataModel.Candidates, ratingDictionary, commentDictionary, companyCredentials, homeCvsDataModel.IsNuggetCv, companyProduct);
        }

        private List<CandidateReadSearchDataModel> MatchWithCommentsAndRatings(List<CandidateReadSearchDataModel> candidates, Dictionary<int, RatingCvEntity> ratings, Dictionary<int, int> comments,
                                                                                CompanyCredentials companyCredentials, bool isNuggetCv, CompanyProductEntity companyProduct)
        {
            var hasRating = ratings.Any();
            var hasComment = comments.Any();
            var isUnlimited = companyProduct.Features.Any(f => f.AmbitId == (short)ProductAmbitEnum.CvBBDD && f.IsUnlimited);

            foreach (var candidate in candidates)
            {
                if (hasComment && comments.ContainsKey(candidate.idcandidate))
                    candidate.NumberComments = comments[candidate.idcandidate];

                if (hasRating && ratings.ContainsKey(candidate.idcandidate))
                    candidate.Rating = ratings[candidate.idcandidate].Rating;

                if (isNuggetCv)
                {
                    candidate.IsViewed = isUnlimited || (!isUnlimited && candidate.Companies.Any(c => c == companyCredentials.IdCompany));
                }
            }

            if (hasRating)
            {
                candidates = candidates.OrderByDescending(o => o.Rating).ToList();
            }

            return candidates;
        }

        private List<int> GetVisualizedCvsIds(CompanyCredentials companyCredentials, short isCvVisualitzed)
        {
            if (isCvVisualitzed != -1)
                return _curriculumService.GetCVsByCompany(companyCredentials.IdCompany, companyCredentials.PortalId)
                    .Select(x => x.IdCandidate)
                    .ToList();

            return new List<int>();
        }

        [HttpPost]
        public JsonResult UpdateFolder(string namefd, string idfd)
        {
            var response = false;

            if (!string.IsNullOrEmpty(namefd) && !string.IsNullOrEmpty(idfd))
            {
                var companyCredentials = SecurityHelper.GetCompanyCredentials();

                response = _folderService.CheckAndUpdateCustomFolder(new CustomFoldersFilterEntity()
                {
                    Environment = (short)CustomFoldersEnvironmentEnum.CvBBDD,
                    IdPortal = companyCredentials.PortalId,
                    IdUser = companyCredentials.UserId,
                    Name = namefd
                }, idfd);
            }

            return Json(response, JsonRequestBehavior.AllowGet);
        }

        [HttpPost]
        public short AddCvsToFolder(string idcvs, string idfdprev, string idfd)
        {
            if (!string.IsNullOrEmpty(idcvs)
                && !string.IsNullOrEmpty(idfd))
            {
                var companyCredentials = SecurityHelper.GetCompanyCredentials();
                short.TryParse(_maxCvsByFolder, out var maxCvsByFolder);
                return _folderService.GetResponseAddCvsFolderAndExecute(idcvs, idfdprev, idfd,
                    companyCredentials.IdCompany, (int)companyCredentials.UserId, companyCredentials.PortalId,
                    maxCvsByFolder, (short)CustomFoldersEnvironmentEnum.CvBBDD, false);
            }

            return (short)FolderCustomStatusResponseEnum.NOT_CAN_ADD_CVS;
        }

        [HttpPost]
        public JsonResult AddFolder(string namefd)
        {
            var response = false;

            if (!string.IsNullOrEmpty(namefd))
            {
                namefd = namefd.TruncateWithFinalPoints(_folderService.GetMaxLenghtNameFolder());
                var companyCredentials = SecurityHelper.GetCompanyCredentials();
                response = _folderService.CheckAndAddCustomFolder(new CustomFoldersFilterEntity()
                {
                    Environment = (short)CustomFoldersEnvironmentEnum.CvBBDD,
                    Name = namefd,
                    IdUser = companyCredentials.UserId,
                    IdPortal = companyCredentials.PortalId
                });
            }

            return Json(response, JsonRequestBehavior.AllowGet);
        }

        [HttpPost]
        public JsonResult DelecteCvsFromFolder(string idfd, string idcvs)
        {
            var response = false;

            if (!string.IsNullOrEmpty(idfd)
                && !string.IsNullOrEmpty(idcvs))
            {
                var companyCredentials = SecurityHelper.GetCompanyCredentials();
                response = _folderService.DeleteCustomCvsByFolderEncrypted(idfd, idcvs, (int)companyCredentials.UserId,
                    companyCredentials.PortalId, false);
            }

            return Json(response, JsonRequestBehavior.AllowGet);
        }

        [HttpPost]
        public JsonResult DeleteFolder(string idfd)
        {
            var response = false;

            if (!string.IsNullOrEmpty(idfd))
            {
                var companyCredentials = SecurityHelper.GetCompanyCredentials();

                response = _folderService.CheckAndDeleteCustomFolder(new CustomFoldersFilterEntity()
                {
                    Environment = (short)CustomFoldersEnvironmentEnum.CvBBDD,
                    IdUser = companyCredentials.UserId,
                    IdPortal = companyCredentials.PortalId
                }, idfd);
            }

            return Json(response, JsonRequestBehavior.AllowGet);
        }

        [HttpPost]
        public JsonResult DeleteSaveFilter(string ft)
        {
            try
            {
                var response = false;

                if (!string.IsNullOrEmpty(ft))
                {
                    CompanyCredentials companyCredentials = SecurityHelper.GetCompanyCredentials();

                    if (SecurityHelper.IsActionPemitedByRoleAndIsNotAdvisorRole(SecurityActionEnum.CompanyOffersManageFilters))
                        response = this._companyFiltersService.DeleteFilterEncrypted("0", ft, companyCredentials.IdCompany, (int)companyCredentials.UserId, companyCredentials.PortalId);
                }

                return Json(response, JsonRequestBehavior.AllowGet);
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"{nameof(CompanyCvsController)} {nameof(DeleteSaveFilter)} {ex}");
                ExceptionPublisherService.Publish(ex, nameof(CompanyCvsController), nameof(DeleteSaveFilter));
                return new JsonResult();
            }
        }

        private bool CanCreateNewSaveFilter(CompanyProductEntity companyProduct)
        {
            if (companyProduct.Features.Any(f => f.AmbitId == (int)ProductAmbitEnum.SaveFilter)
                && SecurityHelper.IsActionPemitedByRoleAndIsNotAdvisorRole(SecurityActionEnum.CompanyOffersManageFilters))
                return true;

            return false;
        }

        private ActionResult CreateNewSaveFilterAndRedirect(MultifiltersDataModel DataModel, string idFolderEncrypted)
        {
            CompanyCredentials companyCredentials = SecurityHelper.GetCompanyCredentials();

            var companyFilter = new CompanyFilterEntity()
            {
                Cities = DataModel.SelectedCities,
                Comment = DataModel.Comments,
                CvVisualitzed = DataModel.IsCvVisualitzed,
                FilterName = DataModel.NameNewSaveFilter,
                Gender = DataModel.Gender,
                Language = DataModel.SelectedLanguage,
                LanguageLevel = DataModel.SelectedLanguageLevel,
                Localizations = DataModel.SelectedLocalization,
                MaxAge = DataModel.MaxAge,
                MinAge = DataModel.MinAge,
                MaxSalary = DataModel.MaxSalary,
                MinSalary = DataModel.MinSalary,
                Nationalities = DataModel.SelectedNationatility,
                Photo = DataModel.HasPhoto,
                ProfesionalCategories = DataModel.SelectedProfesionalCategories,
                Rating = DataModel.Rating,
                SearchName = DataModel.MultiSearchText,
                ExactSearch = DataModel.ExactSearchText,
                SearchNit = DataModel.SearchNit,
                StudyLevels = DataModel.SelectedStudyLevel,
                IsWorking = DataModel.IsWorking,
                IsStudying = DataModel.IsStudying,
                StudyStatus = DataModel.StudyStatus,
                HasDisability = DataModel.HasDisability,
                MinYearsExperience = DataModel.MinYearsExperience,
                MaxYearsExperience = DataModel.MaxYearsExperience,
                LastLogin = DataModel.LastLogin,
                NameUniversity = DataModel.NameUniversity
            };

            string idNewSavedFilter = _companyFiltersService.AddFilterFromCompanyFilterEntityByIdPage(companyFilter, companyCredentials.IdCompany, companyCredentials.UserId, companyCredentials.PortalId, PAGE_ID);

            return RedirectToAction("Index", "CompanyCvs", new { cf = idFolderEncrypted, ft = idNewSavedFilter });
        }

        private void MapToDataLayer(HomeCvsDataModel homeDataModel, CompanyCredentials companyCredentials, string portalId)
        {
            MultifiltersDataModel multifiltersDataModel = homeDataModel.MultifiltersDataModel;
            GTMDataLayerCVFilters dataLayerCVFilters = new GTMDataLayerCVFilters()
            {
                IdPortal = portalId,
                CompanyId = _encryptionService.Encrypt(companyCredentials.IdCompany.ToString()),
                UserId = companyCredentials.UserId > 0 ? "computrabajo-" + companyCredentials.PortalId.ToString() + "-" + _encryptionService.Encrypt(companyCredentials.UserId.ToString()) : "0",
                ise_ct_palabraclave = !string.IsNullOrWhiteSpace(multifiltersDataModel.MultiSearchText),
                ise_ct_empresa = !string.IsNullOrWhiteSpace(multifiltersDataModel.CompanyName),
                ise_ct_visualizacioncvs = multifiltersDataModel.IsCvVisualitzed > 0,
                ise_ct_categoriaprofesional = multifiltersDataModel.SelectedProfesionalCategories.Any(),
                ise_ct_edad = multifiltersDataModel.MinAge > 0 || multifiltersDataModel.MaxAge > 0,
                ise_ct_anosexperiencia = multifiltersDataModel.MinYearsExperience > 0 || multifiltersDataModel.MaxYearsExperience > 0,
                ise_ct_trabajandoactualmente = multifiltersDataModel.IsWorking > 0,
                ise_ct_discapacidad = multifiltersDataModel.HasDisability > -1,
                ise_ct_genero = multifiltersDataModel.Gender > 0,
                ise_ct_estado = multifiltersDataModel.SelectedLocalization.Any(),
                ise_ct_nacionalidad = multifiltersDataModel.SelectedNationatility.Any(),
                ise_ct_ultimaactualizacion = multifiltersDataModel.LastLogin > 0,
                ise_ct_universidad = !string.IsNullOrWhiteSpace(multifiltersDataModel.NameUniversity),
                ise_ct_nivelestudios = multifiltersDataModel.SelectedStudyLevel.Any(),
                filter_education_level_status = multifiltersDataModel.StudyStatus > 0,
                ise_ct_estudiando = multifiltersDataModel.IsStudying > 0,
                ise_ct_idiomas = multifiltersDataModel.SelectedLanguage > 0,
                ise_ct_pretensionsalarial = multifiltersDataModel.MinSalary > 0 || multifiltersDataModel.MaxSalary > 0,
                ise_ct_fotografiacv = multifiltersDataModel.HasPhoto > 0,
                ise_page = homeDataModel.Pager.PageSelected
            };

            TempData[PianoEvents.FILTER_DATALAYER_KEY] = dataLayerCVFilters;
        }
    }
}