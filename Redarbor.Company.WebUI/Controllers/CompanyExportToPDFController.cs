using AutoMapper;
using Redarbor.Candidate.Contracts.ServiceLibrary;
using Redarbor.Common.Entities.Configuration;
using Redarbor.Company.Contracts.ServiceLibrary;
using Redarbor.Company.Contracts.ServiceLibrary.Enums;
using Redarbor.Company.WebUI.Helpers;
using Redarbor.Company.WebUI.Models.Company.Candidate;
using Redarbor.Company.WebUI.Models.Company.Candidate.Competences;
using Redarbor.Company.WebUI.Models.Company.Candidate.Cv;
using Redarbor.Company.WebUI.Security;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Core.Contracts.ServiceLibrary;
using Redarbor.Core.PDFTools.Impl.ServiceLibrary;
using Redarbor.Core.Shared.Contracts.ServiceLibrary;
using Redarbor.Elastic.Entities.Candidate;
using Redarbor.Master.Contracts.ServiceLibrary;
using Redarbor.Master.Contracts.ServiceLibrary.DTO;
using Redarbor.Master.Entities.Product;
using Redarbor.Offer.Contracts.ServiceLibrary;
using Redarbor.Products.Contracts.ServiceLibrary;
using Redarbor.ProductSubGroups.Contracts.ServiceLibrary;
using Redarbor.Quiz.Contract.ServiceLibrary.DTO;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using System.Web.Mvc;

namespace Redarbor.Company.WebUI.Controllers
{
    [RoutePrefix("Company/ExportToPDF")]
    [RedarborAuthorize]
    public class CompanyExportToPDFController : CompanyBaseController
    {
        PortalConfig _portalConfig = new PortalConfig();

        private string COMPETENCES = "Competencias";
        private string VALUES = "Valores";
        private string PERSONALITY = "Personalidad laboral";

        private readonly IEncryptionService _encryptionService;
        private readonly ICandidateService _candidateService;
        private readonly IQuizConsumerService _quizConsumerService;

        public CompanyExportToPDFController(
            IProductService productService,
            IProductSubGroupsService productSubGroupsService,
            IOfferService offerService,
            ICompanyProductService companyProductService,
            ICompanyService companyService,
            IPortalConfigurationService portalConfigurationService,
            IDictionaryService dictionaryService,
            ISecurityService securityService,
            IEncryptionService encryptionService,
            ICandidateService candidateService,
            IExceptionPublisherService exceptionPublisherService,
            IQuizConsumerService quizConsumerService) : base(productService, productSubGroupsService, offerService, companyProductService, companyService, portalConfigurationService, dictionaryService, securityService, exceptionPublisherService)
        {
            _encryptionService = encryptionService;
            _candidateService = candidateService;
            _quizConsumerService = quizConsumerService;
        }


        [Route("ExportPDF")]
        [HttpPost]
        public ActionResult ExportPdf(string c, string cv)
        {
            try
            {
                if (string.IsNullOrEmpty(c) || !int.TryParse(_encryptionService.Decrypt(c), out int idCandidate) || idCandidate <= 0)
                {
                    throw new Exception("El parámetro Candidato no es válido.");
                }

                if (string.IsNullOrEmpty(cv) || !int.TryParse(_encryptionService.Decrypt(cv), out int idCv) || idCv <= 0)
                {
                    throw new Exception("El parámetro CV no es válido.");
                }

                _portalConfig = PortalConfigurationService.GetPortalConfiguration();

                var Pdf = new PdfTool(
                    context: ControllerContext,
                    viewPath: "~/Views/CompanyExportToPDF/ExportPdf.cshtml",
                    model: BuildQuizTalentviewExportModel(idCandidate, idCv)
                );

                return Pdf.GetFileByHtml("Talentview3D.pdf");
            }
            catch (Exception ex)
            {
                Trace.TraceError($"ExportToPDF Export {ex}");
                ExceptionPublisherService.Publish(ex, "ExportToPDF", "Export");
                return new HttpStatusCodeResult(HttpStatusCode.InternalServerError, ex.Message);
            }
        }


        //ESTO ES PARA MAQUETEAR LA PAGINA I VER COMO QUEDA EN HTML
        //[Route("ExportPDFDos")]
        //[HttpGet]
        //public ActionResult ExportPdfDos(string c, string cv)
        //{
        //    try
        //    {
        //        if (string.IsNullOrEmpty(c) || !int.TryParse(_encryptionService.Decrypt(c), out int idCandidate) || idCandidate <= 0)
        //        {
        //            return Content("El parámetro Candidato no es válido.");
        //        }

        //        if (string.IsNullOrEmpty(cv) || !int.TryParse(_encryptionService.Decrypt(cv), out int idCv) || idCv <= 0)
        //        {
        //            return Content("El parámetro CV no es válido.");
        //        }

        //        _portalConfig = PortalConfigurationService.GetPortalConfiguration();
        //         return View(BuildQuizTalentviewExportModel(idCandidate, idCv));
        //    }
        //    catch (Exception ex)
        //    {
        //        Trace.TraceError($"ExportToPDF Export {ex}");
        //        ExceptionPublisherService.Publish(ex, "ExportToPDF", "Export");
        //        return RedirectToAction("Index", "Home");
        //    }
        //}

        private QuizTalentviewExportDataModel BuildQuizTalentviewExportModel(int idCandidate, int idcv)
        {
            var candidate = LoadDataCv(idcv, idCandidate, SecurityHelper.GetCompanyCredentials());


            return new QuizTalentviewExportDataModel()
            {
                CompetenceList = candidate.Cv.CompetenceList,
                ValuesList = candidate.Cv.ValuesList,
                PersonalityList = candidate.Cv.PersonalityList,
                CandidateImgPath = candidate.Candidate.Photo,
                CandidateName = candidate.Candidate.Name,
                CandidatePhone = candidate.Candidate.Phone1,
                CandidateSkype = candidate.Candidate.SkypeName,
                CandidateMail = candidate.User.Email
            };
        }



        private QuizRelationDTO QuizRelation(CandidateReadDataModel candidate)
        {
            return _quizConsumerService.GetCompetenceTest(0, candidate.Candidate.IdCandidate, (short)QuizObjectTypeEnum.TalentView3D, (short)QuizDefinitionTypeEnum.TalentView3D, 3, _portalConfig.PortalId, (short)OriginRequest.CompanyCT);
        }

        private void BuildTalentView3DList(QuizRelationDTO quizRelation, CvDataModel cvData)
        {
            if (quizRelation.QuizAnswers.Any())
            {
                BuildTalentView3DSubList(quizRelation, cvData.CompetenceList, COMPETENCES);
                BuildTalentView3DSubList(quizRelation, cvData.ValuesList, VALUES);
                BuildTalentView3DSubList(quizRelation, cvData.PersonalityList, PERSONALITY);
            }
        }

        private void BuildTalentView3DSubList(QuizRelationDTO quizRelation, List<CompetenceDataModel> competenceList, string groupName)
        {
            var group = quizRelation.QuizDefinition.ListQuestionGroups.Find(x => x.Title == groupName);

            if (group != null)
            {
                FillCandidateTalentView3D(quizRelation, competenceList, group.ListQuestionGroups);
            }
        }

        private static void FillCandidateTalentView3D(QuizRelationDTO quizRelation, List<CompetenceDataModel> competenceList, List<QuestionGroupsDTO> competences)
        {
            var cont = 1;
            foreach (var subgroup in competences)
            {
                var answerByRule = quizRelation.QuizAnswers[0].QuizAnswersGroups.FirstOrDefault(rule => rule.RuleKey == subgroup.QuestionGroupsScoreRules.RuleKey);

                if (answerByRule != null && int.TryParse(answerByRule.Score.ToString("0"), out int scoreInt))
                {
                    var competenceDataModel = new CompetenceDataModel
                    {
                        Id = cont,
                        Key = answerByRule.RuleKey,
                        Description = answerByRule.Note,
                        Title = quizRelation.QuizDefinition.ListQuestionGroups
                                    .SelectMany(group => group.ListQuestionGroups)
                                    .FirstOrDefault(c => c.QuestionGroupsScoreRules.RuleKey == answerByRule.RuleKey)?.Title,
                        Result = scoreInt
                    };

                    if (!competenceList.Any(x => x.Key == competenceDataModel.Key))
                    {
                        competenceList.Add(competenceDataModel);
                        cont++;
                    }
                }
            }
        }

        private CandidateReadDataModel LoadDataCv(int idcv, int candidateId, CompanyCredentials companyCredentials)
        {
            CandidateReadDataModel candidate = Mapper.Map<CandidateReadEntity, CandidateReadDataModel>(_candidateService.GetCandidateRead(idcv, _portalConfig.PortalId, candidateId, companyCredentials.IdCompany));
            QuizRelationDTO quizRelation = QuizRelation(candidate);
            BuildTalentView3DList(quizRelation, candidate.Cv);
            return candidate;
        }
    }
}