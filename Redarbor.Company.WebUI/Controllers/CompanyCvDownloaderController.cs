using AutoMapper;
using Redarbor.Common.Entities.Configuration;
using Redarbor.Common.Entities.Enums;
using Redarbor.Company.Contracts.ServiceLibrary;
using Redarbor.Company.WebUI.Helpers;
using Redarbor.Company.WebUI.Models.Company.Match.Cv;
using Redarbor.Company.WebUI.Security;
using Redarbor.Core.Cache.Contracts.ServiceLibrary;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Core.Contracts.ServiceLibrary;
using Redarbor.Core.Contracts.ServiceLibrary.Enums;
using Redarbor.Core.Counter.Contracts.ServiceLibrary;
using Redarbor.Core.Kpi.Contracts.ServiceLibrary;
using Redarbor.Core.Kpi.Contracts.ServiceLibrary.Enums;
using Redarbor.Core.Shared.Contracts.ServiceLibrary;
using Redarbor.Core.TrackingActions.Contracts.ServiceLibrary;
using Redarbor.Curriculum.Contracts.ServiceLibrary;
using Redarbor.Dictionaries.Consumer.Enums;
using Redarbor.Master.Contracts.ServiceLibrary;
using Redarbor.Master.Contracts.ServiceLibrary.DTO;
using Redarbor.Master.Entities.Company;
using Redarbor.Master.Entities.Cv;
using Redarbor.Master.Entities.Enums;
using Redarbor.Master.Entities.Match;
using Redarbor.Master.Entities.Offers;
using Redarbor.Master.Entities.Product;
using Redarbor.Master.Entities.Searchs;
using Redarbor.Match.Contracts.ServiceLibrary;
using Redarbor.Offer.Contracts.ServiceLibrary;
using Redarbor.Offer.Contracts.ServiceLibrary.DTO;
using Redarbor.Products.Contracts.ServiceLibrary;
using Redarbor.ProductSubGroups.Contracts.ServiceLibrary;
using Redarbor.Web.UI.Library.Exceptions;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using System.Web.Mvc;

namespace Redarbor.Company.WebUI.Controllers
{
    [RoutePrefix("Company/CvDownloader")]
    [RedarborAuthorize]
    public class CompanyCvDownloaderController : CompanyBaseController
    {
        private const short PAGE_SIZE_SELECTED = 10;

        private readonly IMatchService _matchService;
        private readonly IMatchDownloaderService _matchDownloaderService;
        private readonly IEncryptionService _encryptionService;
        private readonly IKpiService _kpiService;
        private readonly ICompanyCountersService _companyCountersService;
        private readonly ITrackingActionsService _trackingActionsService;
        private readonly ICurriculumService _curriculumService;
        private readonly ITempCache _tempCache;
        private readonly ISecurityOfferService _securityOfferService;

        public CompanyCvDownloaderController(
            IProductSubGroupsService productSubGroupsService,
            ICurriculumService curriculumService,
            ITrackingActionsService trackingActionsService,
            ICompanyCountersService companyCountersService,
            IKpiService kpiService,
            ICompanyProductService companyProductService,
            IProductService productService,
            IOfferService offerService,
            IMatchService matchService,
            ICompanyService companyService,
            IMatchDownloaderService matchDownloaderService,
            IEncryptionService encryptionService,
            IDictionaryService dictionaryService,
            IPortalConfigurationService portalConfigurationService,
            ISecurityService securityService,
            IExceptionPublisherService exceptionPublisherService,
            ITempCache tempCache,
            ISecurityOfferService securityOfferService
            ) : base(productService, productSubGroupsService, offerService, companyProductService, companyService, portalConfigurationService, dictionaryService, securityService, exceptionPublisherService)
        {
            _curriculumService = curriculumService;
            _trackingActionsService = trackingActionsService;
            _companyCountersService = companyCountersService;
            _kpiService = kpiService;
            _matchService = matchService;
            _matchDownloaderService = matchDownloaderService;
            _encryptionService = encryptionService;
            _tempCache = tempCache;
            _securityOfferService = securityOfferService;
        }

        public ActionResult DownloadCVCreatedOK(string cvd)
        {
            try
            {
                var portalConfig = PortalConfigurationService.GetPortalConfiguration();
                var companyCredentials = SecurityHelper.GetCompanyCredentials();
                var company = CompanyService.GetByPK(new CompanySearchSpecifications(companyCredentials.PortalId) { Id = companyCredentials.IdCompany });

                if (checkBlocked(portalConfig.company_block, company.Blocked, company.LastDateBlockedUpdate, portalConfig.DaysBlock))
                    return RedirectToAction("Blocked", "Account");

                if (!string.IsNullOrEmpty(cvd))
                {
                    var cvDownload = _matchDownloaderService.GetFileByIdEncrypted(cvd, companyCredentials.IdCompany);

                    if (cvDownload.FileContent != null
                        && !string.IsNullOrEmpty(cvDownload.FileName))
                        return File(cvDownload.FileContent, System.Net.Mime.MediaTypeNames.Application.Octet, cvDownload.FileName);
                }
                return RedirectToAction("DownloadCV");
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyCvDownloaderController DownloadCVCreatedOK {ex}");
                ExceptionPublisherService.Publish(ex, "CompanyCvDownloaderController", "DownloadCVCreatedOK");
                return RedirectToAction("Index", "Home");
            }
        }

        [HttpPost]
        [Route("DownloadCV")]
        public int DownloadCVCreated(string oi, string cvd)
        {
            try
            {
                Int32.TryParse(_encryptionService.Decrypt(cvd), out int idCVD);
                CvDownloaderDataModel cvD = Mapper.Map<CvDownloaderEntity, CvDownloaderDataModel>(_matchDownloaderService.GetByPk(idCVD, (int)StatusEnum.Active));

                if (cvD.IdStatus == 2)
                    return (int)HttpStatusCode.Created;

                return (int)HttpStatusCode.Continue;
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyCvDownloaderController DownloadCVCreatedOK {ex}");
                ExceptionPublisherService.Publish(ex, "CompanyCvDownloaderController", "DownloadCVCreatedOK");
                return (int)HttpStatusCode.Continue;
            }
        }

        [Route("DownloadCV")]
        public ActionResult DownloadCV(string oi, string cvd, string cf, CvDownloaderDataModel model = null)
        {
            try
            {
                PortalConfig portalConfig = PortalConfigurationService.GetPortalConfiguration();
                CompanyCredentials companyCredentials = SecurityHelper.GetCompanyCredentials();
                CompanyProductEntity companyProduct = CompanyProductService.GetByIdCompany(companyCredentials.IdCompany, companyCredentials.PortalId, OfferIntegratorEnum.CompuTrabajo);

                var offer = OfferService.GetByPk(oi, portalConfig.PortalId);

                if (!IsMy(companyCredentials, offer.idoffer, portalConfig))
                    return RedirectToAction("Index", "Company");

                Int32.TryParse(_encryptionService.Decrypt(cvd), out int idCVD);
                CvDownloaderDataModel cvD = Mapper.Map<CvDownloaderEntity, CvDownloaderDataModel>(_matchDownloaderService.GetByPk(idCVD, (int)StatusEnum.Pending));

                if (cvD.IdStatus == 0)
                    cvD = Mapper.Map<CvDownloaderEntity, CvDownloaderDataModel>(_matchDownloaderService.GetByPk(idCVD, (int)StatusEnum.Active));

                model = LoadCvDownloaderDataModel(offer, oi, cvd, true, cvD.IdExtension, cvD, portalConfig, companyCredentials, companyProduct, cf);

                if (model.IdStatus == 1)
                {
                    var actionsPendings = _trackingActionsService.GetTrackingActionsPending(new TrackingActionFilterEntity()
                    {
                        CompanyId = companyCredentials.IdCompany,
                        PortalId = portalConfig.PortalId,
                        UserId = (int)companyCredentials.UserId
                    });

                    var action = actionsPendings.FirstOrDefault(c => c.IdObject == idCVD);
                    if (action == null)
                    {
                        _trackingActionsService.AddActionTracking(new TrackingActionEntity()
                        {
                            Type = (short)ProductTraceActionsEnum.CV_DOWNLOADER,
                            IdObject = idCVD,
                            IdPortal = portalConfig.PortalId,
                            IdStatus = (short)StatusEnum.Pending,
                            IdCompany = companyCredentials.IdCompany,
                            IdUser = (int)companyCredentials.UserId,
                            UserName = companyCredentials.Name
                        });
                    }

                }

                return View("DownloadCV", model);
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyCvDownloaderController DownloadCV {ex}");
                ExceptionPublisherService.Publish(ex, "CompanyCvDownloaderController", "DownloadCV");
                return RedirectToAction("Index", "Home");
            }
        }

        private bool IsMy(CompanyCredentials companyCredentials, int idOffer, PortalConfig portalConfig)
        {
            return _securityOfferService.IsMy(new IsMyOfferDTO(companyCredentials.UserRole, companyCredentials.IdCompany, companyCredentials.PortalId, (int)companyCredentials.UserId, idOffer, portalConfig.EnabledNewIsMy));
        }

        private CvDownloaderDataModel LoadCvDownloaderDataModel(OfferEntity offer, string oi, string cvd, bool f, short ext,
            CvDownloaderDataModel model, PortalConfig portalConfig, CompanyCredentials companyCredentials,
            CompanyProductEntity companyProduct, string cf)
        {
            Int32.TryParse(_encryptionService.Decrypt(cvd), out int idCVD);
            int cvCount = _matchDownloaderService.GetCountCvForDownload(idCVD, portalConfig.PortalId);

            Int32.TryParse(_encryptionService.Decrypt(cf), out int idFolderSelected);

            var hasCvsDownloadLimitFeature = companyProduct.Features.FirstOrDefault(f => f.AmbitId == (int)ProductAmbitEnum.CVsDownloadLimit);

            model.Id = idCVD;
            model.IdEncrypt = cvd;
            model.IdCompany = companyCredentials.IdCompany;
            model.UserId = companyCredentials.UserId;
            model.IdOffer = offer.idoffer;
            model.IdOfferEncrypted = oi;
            model.OfferTitle = offer.title;
            model.TypeExtract = f;
            model.IdExtension = ext;
            model.ShowNitByConfigPortal = portalConfig.nit_show_in_details_cv;
            model.ShowTestCompetences = companyProduct.Features.Any(n => n.AmbitId == (int)ProductAmbitEnum.CompetencesTest);
            model.CountCvs = (cvCount > 0 ? cvCount : _matchService.GetCandidateIdMatchesByOfferId(offer.idofferCT).Count);
            model.ShowCVDownloadCSVXLS = companyProduct.Features.Any(n => n.AmbitId == (int)ProductAmbitEnum.CVDownloadCSVSXLS);
            model.ShowCVDownloadMatchesReceived = companyProduct.Features.Any(f => f.AmbitId == (int)ProductAmbitEnum.CVDownloadMatchesReceived);
            model.CVsDownloadLimit = hasCvsDownloadLimitFeature;
            model.CVsDownload = companyProduct.Features.Any(n => n.AmbitId == (int)ProductAmbitEnum.CVsDownload);
            model.CanExtractCV = ((idFolderSelected == (int)CompanyOfferFolderEnum.Recibidos && model.ShowCVDownloadMatchesReceived && model.CVsDownload) ||
                                    (idFolderSelected != (int)CompanyOfferFolderEnum.Recibidos && model.CVsDownload)) &&
                                    model.CVsDownloadLimit != null && (model.CVsDownloadLimit.IsUnlimited || model.CVsDownloadLimit.AvailableUnits > 0);
            model.IsActionSuported = ((idFolderSelected == (int)CompanyOfferFolderEnum.Recibidos && model.ShowCVDownloadMatchesReceived && model.CVsDownload) ||
                                    (idFolderSelected != (int)CompanyOfferFolderEnum.Recibidos && model.CVsDownload));

            if (hasCvsDownloadLimitFeature != null)
                model.CVsDownloadLimit.ConsumedUnits = model.CVsDownloadLimit.AvailableUnits <= 0 ? model.CVsDownloadLimit.InitialUnits : model.CVsDownloadLimit.InitialUnits - model.CVsDownloadLimit.AvailableUnits;

            return model;
        }

        [HttpPost]
        [Route("Extract")]
        public ActionResult Extract(string oi, string cvd, CvDownloaderDataModel model)
        {
            try
            {

                Int32.TryParse(_encryptionService.Decrypt(cvd), out int idCVD);

                if (_matchDownloaderService.GetStatus(idCVD) == (int)StatusEnum.Active)
                    return Redirect(Url.Action("Index", "CompanyMatches", new { oi = model.IdOfferEncrypted, f = _encryptionService.Encrypt(((int)CompanyOfferFolderEnum.Recibidos).ToString()) }));

                PortalConfig portalConfig = PortalConfigurationService.GetPortalConfiguration();
                CompanyCredentials companyCredentials = SecurityHelper.GetCompanyCredentials();
                CompanyProductEntity companyProduct = CompanyProductService.GetByIdCompany(companyCredentials.IdCompany, companyCredentials.PortalId, OfferIntegratorEnum.CompuTrabajo);

                if (model.TypeExtract)
                {
                    model.CVDownloaderConfig.CompleteCvs = true;
                    model.CVDownloaderConfig.Name = true;
                    model.CVDownloaderConfig.MatchDate = true;
                    model.CVDownloaderConfig.LastUpdate = true;
                    model.CVDownloaderConfig.Telephone = true;
                    model.CVDownloaderConfig.Age = true;
                    model.CVDownloaderConfig.CvTitle = true;
                    model.CVDownloaderConfig.Address = true;
                    model.CVDownloaderConfig.LastStudy = true;
                    model.CVDownloaderConfig.CvAttachment = true;
                    model.CVDownloaderConfig.Email = true;
                    model.CVDownloaderConfig.ShowGender = true;
                    model.CVDownloaderConfig.ShowNit = model.ShowNitByConfigPortal;
                    model.CVDownloaderConfig.TestCompetences = model.ShowTestCompetences;
                }

                model.CVDownloaderConfig.Id = idCVD;
                model.CVDownloaderConfig.IdFormat = model.IdExtension;

                bool returnConsume = CompanyProductService.CheckAndConsumeCVsDownload(companyProduct, (short)ProductAmbitEnum.CVsDownloadLimit, OfferIntegratorEnum.CompuTrabajo, (short)model.CountCvs);

                if (returnConsume && _matchDownloaderService.AddCVDConfig(Mapper.Map<CVDownloaderConfigDataModel, CVDownloaderConfigEntity>(model.CVDownloaderConfig)))
                {

                    _matchDownloaderService.AddStack(idCVD, portalConfig.PortalId, 0);

                    short kpi = 0;

                    switch (model.IdExtension)
                    {
                        case ((int)CvsDownloadFormatsEnum.CSV):
                            kpi = (short)KpiEnum.COMPANY_EXTRACT_CV_CSV;
                            break;
                        case ((int)CvsDownloadFormatsEnum.EXCEL):
                            kpi = (short)KpiEnum.COMPANY_EXTRACT_CV_EXCEL;
                            break;
                        case ((int)CvsDownloadFormatsEnum.PDF):
                            kpi = (short)KpiEnum.COMPANY_EXTRACT_CV_PDF;
                            break;
                    }

                    Task.Factory.StartNew(() =>
                    {
                        if (kpi != 0)
                        {
                            _companyCountersService.AddCounterCompany((int)KpiEnum.COMPANY_EXTRACT_CV, portalConfig.PortalId, companyCredentials.IdCompany, 1, false);
                            _kpiService.AddSumBlock((int)KpiEnum.COMPANY_EXTRACT_CV, portalConfig.PortalId, 1);
                            _companyCountersService.AddCounterCompany(kpi, portalConfig.PortalId, companyCredentials.IdCompany, 1, false);
                            _kpiService.AddSumBlock(kpi, portalConfig.PortalId, 1);
                        }
                    });
                }
                return Redirect(Url.Action("DownloadCV", "CompanyCvDownloader", new { oi = model.IdOfferEncrypted, cvd = model.IdEncrypt }));
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyCvDownloaderController Extract {ex}");
                ExceptionPublisherService.Publish(ex, "CompanyCvDownloaderController", "Extract");
                return RedirectToAction("Index", "Home");
            }
        }

        [Route("Extract")]
        public ActionResult Extract(string oi, string cvd, string cf)
        {
            try
            {
                PortalConfig portalConfig = PortalConfigurationService.GetPortalConfiguration();
                CompanyCredentials companyCredentials = SecurityHelper.GetCompanyCredentials();
                CompanyProductEntity companyProduct = CompanyProductService.GetByIdCompany(companyCredentials.IdCompany, companyCredentials.PortalId, OfferIntegratorEnum.CompuTrabajo);

                var offer = OfferService.GetByPk(oi, portalConfig.PortalId);

                if (!IsMy(companyCredentials, offer.idoffer, portalConfig))
                    return RedirectToAction("Index", "Company");

                CvDownloaderDataModel cvD = new CvDownloaderDataModel();
                var model = LoadCvDownloaderDataModel(offer, oi, cvd, true, (int)CvsDownloadFormatsEnum.PDF, cvD, portalConfig, companyCredentials, companyProduct, cf);

                return View(model);
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyCvDownloaderController Extract {ex}");
                ExceptionPublisherService.Publish(ex, "CompanyCvDownloaderController", "Extract");
                return RedirectToAction("Index", "Home");
            }
        }

        [Route]
        public ActionResult Index(bool error = false)
        {
            try
            {
                if (error) ViewBag.Message = PageLiteralsHelper.GetLiteral("FOLLOW_GENERIC_ERROR", (short)PageEnum.SiteMasterPortal, PortalConfigurationService.GetPortalConfiguration());
                return LoadDataModel();
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyCvDownloaderController Index-Get {ex}");
                ExceptionPublisherService.Publish(ex, "CompanyCvDownloaderController", "Index-Get");
                return RedirectToAction("Index", "Home");
            }
        }

        [Route]
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Index(CompanyCvDownloaderListDataModel companyCvDownloaderListDataModel)
        {
            try
            {
                return LoadDataModel((short)companyCvDownloaderListDataModel.Pager.PageSelected);
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyCvDownloaderController Index-Post {ex}");
                ExceptionPublisherService.Publish(ex, "CompanyCvDownloaderController", "Index-Post");
                return RedirectToAction("Index", "Home");
            }
        }

        private ActionResult LoadDataModel(short idPageSelected = 1)
        {
            return View(GetDataModel(idPageSelected));
        }

        private CompanyCvDownloaderListDataModel GetDataModel(short idPageSelected)
        {
            var companyCredentials = SecurityHelper.GetCompanyCredentials();

            var companyCvDownloaderListDataModel = new CompanyCvDownloaderListDataModel()
            {
                cvDownloaderList = GetCvList(companyCredentials, idPageSelected),

                Pager = new Models.PagerDataModel()
                {
                    PageSizeSelected = PAGE_SIZE_SELECTED,
                    TotalRows = GetTotalCvDownload(companyCredentials),
                    PageSelected = idPageSelected
                }
            };

            FillExtraFields(companyCvDownloaderListDataModel.cvDownloaderList, companyCredentials.PortalId);
            return companyCvDownloaderListDataModel;
        }

        private void FillExtraFields(List<CvDownloaderDataModel> cvDownloaderList, short portalId)
        {
            foreach (var item in cvDownloaderList)
            {
                item.IdEncrypt = _encryptionService.Encrypt(item.Id.ToString());
                item.MonthDateAddDescription = DictionaryService.GetDictionaryValue(DictionaryEnum.MONTHS, item.DateAdd.Month.ToString(), portalId);
                item.MonthOfferDateDescription = DictionaryService.GetDictionaryValue(DictionaryEnum.MONTHS, item.OfferDate.Month.ToString(), portalId);
            }
        }

        private int GetTotalCvDownload(CompanyCredentials companyCredentials)
        {
            if (companyCredentials.IdCompany > 0)
                return _matchDownloaderService.GetTotalByIdCompanyIdUser(companyCredentials.IdCompany,
                                            !SecurityHelper.IsActionPemitedByRoleAndIsNotAdvisorRole(SecurityActionEnum.CompanyCvsDownload)
                                            ? (int)companyCredentials.UserId : 0);

            return 0;
        }

        private List<CvDownloaderDataModel> GetCvList(CompanyCredentials companyCredentials, short idPageSelected)
        {
            if (companyCredentials.IdCompany > 0)
            {
                return Mapper.Map<List<MatchDownloaderEntity>, List<CvDownloaderDataModel>>
                        (_matchDownloaderService.GetByIdCompanyIdUser(companyCredentials.IdCompany, PAGE_SIZE_SELECTED, idPageSelected,
                        !SecurityHelper.IsActionPemitedByRoleAndIsNotAdvisorRole(SecurityActionEnum.CompanyCvsDownload)
                        ? (int)companyCredentials.UserId : 0));
            }

            return new List<CvDownloaderDataModel>();
        }

        [HttpPost]
        public PartialViewResult Delete(string idEncrypted, int idPageSelected)
        {
            if (!string.IsNullOrEmpty(idEncrypted) && idPageSelected > 0)
                _matchDownloaderService.DeleteByIdEncrypted(idEncrypted, SecurityHelper.GetCompanyCredentials().PortalId);

            ViewData["portalConfig"] = PortalConfigurationService.GetPortalConfiguration();
            return PartialView("_CompanyCvDownloaderList", GetDataModel((short)idPageSelected));
        }

        public ActionResult Download(string cvd)
        {
            try
            {
                var companyCredentials = SecurityHelper.GetCompanyCredentials();

                if (!string.IsNullOrEmpty(cvd))
                {
                    var cvDownload = _matchDownloaderService.GetFileByIdEncrypted(cvd, companyCredentials.IdCompany);

                    if (cvDownload.FileContent != null
                        && !string.IsNullOrEmpty(cvDownload.FileName))
                        return File(cvDownload.FileContent, System.Net.Mime.MediaTypeNames.Application.Octet, cvDownload.FileName);
                }

                return RedirectToAction("Index", new { error = true });
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyCvDownloaderController Download {ex}");
                ExceptionPublisherService.Publish(ex, "CompanyCvDownloaderController", "Download");
                return RedirectToAction("Index", "Home");
            }
        }

        [Route("Company/CvDetail/Download")]
        public ActionResult DownloadCvAmazon(string ic, string oi, string ims, string idcv, string mfid)
        {
            try
            {
                var companyCredentials = SecurityHelper.GetCompanyCredentials();
                var portalConfig = PortalConfigurationService.GetPortalConfiguration();

                string AmazonBucket = PortalConfigurationService.GetPortalConfiguration().AEPortalConfig.AmazonBucketUserFiles;
                string AmazonAccesKey = PortalConfigurationService.GetPortalConfiguration().AEPortalConfig.AmazonAccesKeyUserFiles;
                string AmazonSecreKey = PortalConfigurationService.GetPortalConfiguration().AEPortalConfig.AmazonSecretKeyUserFiles;

                if (!string.IsNullOrEmpty(idcv))
                {
                    int.TryParse(_encryptionService.Decrypt(idcv), out var idCV);
                    var idCandidate = _curriculumService.GetCandidateCompletCV(idCV, portalConfig).IdCandidate;
                    ic = _encryptionService.Encrypt(idCandidate.ToString());
                }

                if (!string.IsNullOrEmpty(ic))
                {
                    var cvDownload = _matchDownloaderService.GetFileCandidateByIdEncryptedWithKeys(ic, mfid, companyCredentials.IdCompany, AmazonBucket, AmazonAccesKey, AmazonSecreKey);

                    if (cvDownload.FileContent != null
                        && !string.IsNullOrEmpty(cvDownload.FileName))
                        return File(cvDownload.FileContent, System.Net.Mime.MediaTypeNames.Application.Octet, cvDownload.FileName);
                }

                if (!string.IsNullOrEmpty(idcv))
                    return RedirectToAction("Index", "CompanyMatchCvDetail", new { idcv = idcv });

                return RedirectToAction("Index", "CompanyMatchCvDetail", new { oi = oi, ims = ims });
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyCvDownloaderController DownloadCvAmazon {ex}");
                ExceptionPublisherService.Publish(ex, "CompanyCvDownloaderController", "DownloadCvAmazon");
                return RedirectToAction("Index", "Home");
            }
        }

        [Route("Company/CvDetail/CheckFile")]
        [HttpPost]
        public JsonResult CheckFileCvAmazon(string ic, string idcv, string mfid)
        {
            try
            {
                var companyCredentials = SecurityHelper.GetCompanyCredentials();
                var portalConfig = PortalConfigurationService.GetPortalConfiguration();

                string AmazonBucket = PortalConfigurationService.GetPortalConfiguration().AEPortalConfig.AmazonBucketUserFiles;
                string AmazonAccesKey = PortalConfigurationService.GetPortalConfiguration().AEPortalConfig.AmazonAccesKeyUserFiles;
                string AmazonSecreKey = PortalConfigurationService.GetPortalConfiguration().AEPortalConfig.AmazonSecretKeyUserFiles;

                bool checkCache = !string.IsNullOrEmpty(ic) && !string.IsNullOrEmpty(idcv);
                var cacheKey = checkCache ? $"check_cv_{ic}-{idcv}-{mfid}" : string.Empty;
                if (checkCache)
                {
                    bool checkOk = _tempCache.Get<bool>(cacheKey);
                    if (checkOk) { return Json(true); }
                }

                if (!string.IsNullOrEmpty(idcv))
                {
                    int.TryParse(_encryptionService.Decrypt(idcv), out var idCV);
                    var idCandidate = _curriculumService.GetCandidateCompletCV(idCV, portalConfig).IdCandidate;
                    ic = _encryptionService.Encrypt(idCandidate.ToString());
                }

                if (!string.IsNullOrEmpty(ic))
                {
                    var cvDownload = _matchDownloaderService.GetFileCandidateByIdEncryptedWithKeys(ic, mfid, companyCredentials.IdCompany, AmazonBucket, AmazonAccesKey, AmazonSecreKey);

                    if (cvDownload.FileContent != null
                        && cvDownload.FileContent.Length >= 1024
                        && !string.IsNullOrEmpty(cvDownload.FileName))
                    {
                        if (checkCache) _tempCache.Add(cacheKey, true, companyCredentials.PortalId);
                        return Json(true);
                    }


                }
                return Json(false);
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyCvDownloaderController CheckFileCvAmazon {ex}");
                ExceptionPublisherService.Publish(ex, "CompanyCvDownloaderController", "CheckFileCvAmazon");
                return Json(false);
            }
        }

        public ActionResult DownloadWithUrlReturn(string cvd, string redirect, string actionRedirect)
        {
            try
            {
                var companyCredentials = SecurityHelper.GetCompanyCredentials();

                if (!string.IsNullOrEmpty(cvd))
                {
                    var cvDownload = _matchDownloaderService.GetFileByIdEncrypted(cvd, companyCredentials.IdCompany);

                    if (cvDownload.FileContent != null
                        && !string.IsNullOrEmpty(cvDownload.FileName))
                        return File(cvDownload.FileContent, System.Net.Mime.MediaTypeNames.Application.Octet, cvDownload.FileName);
                }

                return RedirectToAction(actionRedirect, redirect);
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyCvDownloaderController DownloadWithUrlReturn {ex}");
                ExceptionPublisherService.Publish(ex, "CompanyCvDownloaderController", "DownloadWithUrlReturn");
                return RedirectToAction("Index", "Home");
            }
        }

    }
}