using Redarbor.Common.Entities.Configuration;
using Redarbor.Company.Contracts.ServiceLibrary;
using Redarbor.Company.WebUI.Helpers;
using Redarbor.Company.WebUI.Models.Company.Configuration;
using Redarbor.Company.WebUI.Security;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Core.Contracts.ServiceLibrary;
using Redarbor.Core.Mailing.Contracts.ServiceLibrary;
using Redarbor.Core.Shared.Contracts.ServiceLibrary;
using Redarbor.Master.Contracts.ServiceLibrary;
using Redarbor.Master.Contracts.ServiceLibrary.Constants;
using Redarbor.Master.Entities.Enums;
using Redarbor.Master.Entities.Searchs;
using Redarbor.Master.Entities.Users;
using Redarbor.Offer.Contracts.ServiceLibrary;
using Redarbor.Products.Contracts.ServiceLibrary;
using Redarbor.ProductSubGroups.Contracts.ServiceLibrary;
using Redarbor.User.Contracts.ServiceLibrary;
using Redarbor.Web.UI.Library.Exceptions;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Web.Mvc;

namespace Redarbor.Company.WebUI.Controllers
{
    [RoutePrefix("Company/Configuration/EditData")]
    [RedarborAuthorize]
    public class CompanyConfigurationEditDataController : CompanyBaseController
    {
        private readonly IUserCredentialService _userCredentialService;
        private readonly IUserService _userService;
        private readonly IHashService _hashService;
        private readonly ICookieService _cookieService;
        private readonly IExceptionPublisherService _exceptionPublisherService;
        private readonly IMailingService _mailingService;

        public CompanyConfigurationEditDataController(IProductService productService,
            IProductSubGroupsService productSubGroupsService,
            IOfferService offerService,
            ICompanyProductService companyProductService,
            ICompanyService companyService,
            IPortalConfigurationService portalConfigurationService,
            IDictionaryService dictionaryService,
            IUserCredentialService userCredentialService,
            IHashService hashService,
            IUserService userService,
            ICookieService cookieService,
            ISecurityService securityService,
            IExceptionPublisherService exceptionPublisherService,
            IMailingService mailingService) : base(productService, productSubGroupsService, offerService, companyProductService, companyService, portalConfigurationService, dictionaryService, securityService, exceptionPublisherService)
        {
            _userCredentialService = userCredentialService;
            _hashService = hashService;
            _userService = userService;
            _cookieService = cookieService;
            _exceptionPublisherService = exceptionPublisherService;
            _mailingService = mailingService;
        }

        [Route]
        public ActionResult Index(int result = 0)
        {
            try
            {
                var portalConfig = PortalConfigurationService.GetPortalConfiguration();
                var companyCredentials = SecurityHelper.GetCompanyCredentials();
                var company = CompanyService.GetByPK(new CompanySearchSpecifications(companyCredentials.PortalId) { Id = companyCredentials.IdCompany });

                if (checkBlocked(portalConfig.company_block, company.Blocked, company.LastDateBlockedUpdate, portalConfig.DaysBlock))
                    return RedirectToAction("Blocked", "Account");

                var user = _userService.Get(companyCredentials.UserId, true, portalConfig.PortalId);

                var companyDomainsItems = new List<SelectListItem>();
                CompanyService.GetCompanyDomains(companyCredentials.IdCompany)
                    .ForEach(domains => companyDomainsItems.Add(new SelectListItem() { Value = domains, Text = domains }));

                return View(new ConfigurationEditDataDataModel()
                {
                    CompanyEditDataChangeEmail = new CompanyEditDataChangeEmailDataModel()
                    {
                        CurrentEmail = user.Email,
                        NewEmailDomains = companyDomainsItems,
                        RepeatNewEmailDomains = companyDomainsItems,
                    },
                    operationResult = result
                });
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyConfigurationEditDataController Index-Get {ex}");
                _exceptionPublisherService.Publish(ex, "CompanyConfigurationEditDataController", "Index-Get");
                return RedirectToAction("Index", "Home");
            }
        }

        [Route("ChangeMail")]
        [HttpPost]
        [ValidateAntiForgeryToken()]
        public ActionResult ChangeMail(ConfigurationEditDataDataModel configurationEditData)
        {
            try
            {
                var portalConfig = PortalConfigurationService.GetPortalConfiguration();

                if (ModelState.IsValid 
                    && ValidationBackendEmail(configurationEditData.CompanyEditDataChangeEmail, portalConfig))
                {                    
                    var companyCredentials = SecurityHelper.GetCompanyCredentials();
                    var user = _userService.Get(companyCredentials.UserId, true, portalConfig.PortalId);

                    if (user.Email != configurationEditData.CompanyEditDataChangeEmail.NewEmail)
                    {
                        if (portalConfig.SendMailAddedCompanyUser && user.RoleId != (short)UserRoleEnum.ADMINISTRADOR_PRINCIPAL)
                        {
                            var company = CompanyService.GetByPK(new CompanySearchSpecifications(user.PortalId) { Id = user.CompanyId });
                            user.StatusId = (short)UserStatusEnum.Desactivado;
                            if (UpdateEmail(configurationEditData, user))
                            {
                                _mailingService.AddNewsLetterMail(company, (short)NewsLetterEnum.ConfirmationMailCreateCompanyUser, user.PortalId, (int)NotificationTypeEnum.Company, (int)PriorityEnum.MEDIUM);
                                return RedirectToAction("Index", new { result = 1 });
                            }
                        }
                        if (UpdateEmail(configurationEditData, user))
                            return RedirectToAction("Index", new { result = 1 });
                    }
                }
                return RedirectToAction("Index", new { result = 3 });
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyConfigurationEditDataController ChangeMail-Post {ex}");
                _exceptionPublisherService.Publish(ex, "CompanyConfigurationEditDataController", "ChangeMail-Post");
                return RedirectToAction("Index", "Home");
            }
        }

        [Route("ChangePassword")]
        [HttpPost]
        [ValidateAntiForgeryToken()]
        public ActionResult ChangePassword(ConfigurationEditDataDataModel configurationEditData)
        {
            try
            {
                var portalConfig = PortalConfigurationService.GetPortalConfiguration();
                var companyCredentials = SecurityHelper.GetCompanyCredentials();
                var user = _userService.Get(companyCredentials.UserId, true, portalConfig.PortalId);

                if (ModelState.IsValid 
                    && ValidationBackendPassword(configurationEditData.CompanyEditDataChangePassword,user))
                {                    
                    if (UpdatePassword(configurationEditData, user, portalConfig.cookie_domain))
                        return RedirectToAction("Index", new { result = 2 });

                }
                return RedirectToAction("Index", new { result = 3 });
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyConfigurationEditDataController ChangePassword-Post {ex}");
                _exceptionPublisherService.Publish(ex, "CompanyConfigurationEditDataController", "ChangePassword-Post");
                return RedirectToAction("Index", "Home");
            }
        }

        private bool UpdateEmail(ConfigurationEditDataDataModel configurationEditData, UserEntity user)
        {
            return _userCredentialService.UpdateUserEmail(user,
                    $"{configurationEditData.CompanyEditDataChangeEmail.NewEmail}{configurationEditData.CompanyEditDataChangeEmail.SelectedNewEmailDomain}");
        }


        private bool UpdatePassword(ConfigurationEditDataDataModel configurationEditData, UserEntity user, string cookieDomain)
        {
            if (_userCredentialService.ChangePassword(user,
                configurationEditData.CompanyEditDataChangePassword.CurrentPassword,
                configurationEditData.CompanyEditDataChangePassword.NewPassword))
            {
                _cookieService.Delete(CookieType.USER_COMPANY, cookieDomain);
                return true;
            }

            return false;
        }

        #region Validations

        public JsonResult CheckCurrentPassword()
        {
            var currentPassword = Request.QueryString["CompanyEditDataChangePassword.CurrentPassword"] ?? string.Empty;

            var companyCredentials = SecurityHelper.GetCompanyCredentials();
            var user = _userService.Get(companyCredentials.UserId, true, companyCredentials.PortalId);

            return Json(user.Password == _hashService.Encrypt(currentPassword), JsonRequestBehavior.AllowGet);
        }


        public JsonResult CheckNewPasswordFormat()
        {
            var currentPassword = Request.QueryString["CompanyEditDataChangePassword.NewPassword"] ?? string.Empty;

            return Json(currentPassword != string.Empty &&
                (currentPassword.Length > 4 &&
                currentPassword.Length < 20), JsonRequestBehavior.AllowGet);
        }


        public JsonResult CheckRepeatNewPassword()
        {
            var newPassword = Request.QueryString["CompanyEditDataChangePassword.NewPassword"] ?? string.Empty;
            var repeatNewPassword = Request.QueryString["CompanyEditDataChangePassword.RepeatNewPassword"] ?? string.Empty;

            return Json(newPassword == repeatNewPassword, JsonRequestBehavior.AllowGet);
        }

        public JsonResult ExistNewEmail()
        {
            var newEmail = Request.QueryString["CompanyEditDataChangeEmail.NewEmail"] ?? string.Empty;
            var selectedNewEmailDomain = Request.QueryString["CompanyEditDataChangeEmail.SelectedNewEmailDomain"] ?? string.Empty;

            var portalConfig = PortalConfigurationService.GetPortalConfiguration();
            return Json(!_userService.ExistsMail($"{newEmail}{selectedNewEmailDomain}", portalConfig.PortalId), JsonRequestBehavior.AllowGet);
        }


        public JsonResult NewEmailEqualsRepeatMail()
        {
            var newEmail = Request.QueryString["CompanyEditDataChangeEmail.NewEmail"] ?? string.Empty;
            var repeatNewEmail = Request.QueryString["CompanyEditDataChangeEmail.RepeatNewEmail"] ?? string.Empty;

            return Json(newEmail == repeatNewEmail, JsonRequestBehavior.AllowGet);
        }

        private bool ValidationBackendEmail(CompanyEditDataChangeEmailDataModel modelEmail, PortalConfig portalConfig)
        {
            return !_userService.ExistsMail($"{modelEmail.NewEmail}{modelEmail.SelectedNewEmailDomain}", portalConfig.PortalId);
        }

        private bool ValidationBackendPassword(CompanyEditDataChangePasswordDataModel modelPassword, UserEntity user)
        {
            return user.Password == _hashService.Encrypt(modelPassword.CurrentPassword);
        }
        #endregion
    }
}