using AutoMapper;
using Redarbor.Common.Entities.Configuration;
using Redarbor.Common.Entities.Enums;
using Redarbor.Company.Contracts.ServiceLibrary;
using Redarbor.Company.Contracts.ServiceLibrary.DTO;
using Redarbor.Company.Contracts.ServiceLibrary.Enums;
using Redarbor.Company.WebUI.Enums;
using Redarbor.Company.WebUI.Helpers;
using Redarbor.Company.WebUI.Models.Company;
using Redarbor.Company.WebUI.Models.Company.Home;
using Redarbor.Company.WebUI.Models.Company.Offer;
using Redarbor.Company.WebUI.Models.Company.Product;
using Redarbor.Company.WebUI.Models.Company.Shared;
using Redarbor.Company.WebUI.Security;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Core.Contracts.ServiceLibrary;
using Redarbor.Core.Counter.Contracts.ServiceLibrary;
using Redarbor.Core.Kpi.Contracts.ServiceLibrary;
using Redarbor.Core.Kpi.Contracts.ServiceLibrary.Enums;
using Redarbor.Core.Shared.Contracts.ServiceLibrary;
using Redarbor.Dictionaries.Consumer.Enums;
using Redarbor.Extensions.Library.Extensions;
using Redarbor.Jobads.Chat.Listener.Abstractions.Events;
using Redarbor.Jobads.Chat.Listener.Abstractions.ItemQueues;
using Redarbor.Master.Contracts.ServiceLibrary;
using Redarbor.Master.Contracts.ServiceLibrary.DTO;
using Redarbor.Master.Entities.Company;
using Redarbor.Master.Entities.Counters;
using Redarbor.Master.Entities.Enums;
using Redarbor.Master.Entities.Offers;
using Redarbor.Master.Entities.Product;
using Redarbor.Master.Entities.Searchs;
using Redarbor.Master.Entities.Users;
using Redarbor.Message.Jobads.Chat.Impl.ServiceLibrary;
using Redarbor.Offer.Contracts.ServiceLibrary;
using Redarbor.Offer.Contracts.ServiceLibrary.DTO;
using Redarbor.Offer.Contracts.ServiceLibrary.Enums;
using Redarbor.Offer.Elastic.Contracts.ServiceLibrary.DTO.Filters;
using Redarbor.Products.Contracts.ServiceLibrary;
using Redarbor.Products.Contracts.ServiceLibrary.DTO;
using Redarbor.ProductSubGroups.Contracts.ServiceLibrary;
using Redarbor.RabbitMQ.Abstractions;
using Redarbor.User.Contracts.ServiceLibrary;
using Redarbor.Web.UI.Library.Exceptions;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using System.Web.Mvc;
using System.Web.Script.Serialization;
namespace Redarbor.Company.WebUI.Controllers
{
    [RoutePrefix("Company/Offers")]
    [RedarborAuthorize]
    public class CompanyOffersController : CompanyBaseController
    {
        short PageId = (short)PageEnum.OfferList;
        const int QUERY_SIZE = 2000;

        private readonly IEncryptionService _encryptionService;
        private readonly IUserService _userService;
        private readonly ICompanyProductService _companyProductService;
        private readonly ILandingProductsService _landingProductsService;
        private readonly ICompanyTrackingService _companyTrackingService;
        private readonly IOfferMatchesNotificationKpiService _offerMatchesNotificationKpiService;
        private readonly IProducerRabbitMQ _producerRabbitMQ;
        private readonly ISecurityOfferService _securityOfferService;
        private readonly IOfferKpisElasticService _offerKpisElasticService;
        private readonly bool _isNewSuggest;
        private readonly IMessageJobadsChatService _messageJobdsChatService;
        private readonly IOfferCvCountersService _offerCvCountersService;
        private readonly IRequestDeviceRecorder _requestDeviceRecorder;
        private readonly IHttpService _httpService;
        private readonly IKpiService _kpiService;

        public CompanyOffersController(IProductService productService,
            IProductSubGroupsService productSubGroupsService,
            IOfferService offerService,
            ICompanyProductService companyProductService,
            ICompanyService companyService,
            IPortalConfigurationService portalConfigurationService,
            IDictionaryService dictionaryService,
            IEncryptionService encryptionService,
            IUserService userService,
            ISecurityService securityService,
            IExceptionPublisherService exceptionPublisherService,
            ILandingProductsService landingProductsService,
            IMessageJobadsChatService messageJobdsChatService,
            ICompanyTrackingService companyTrackingService,
            IOfferMatchesNotificationKpiService offerMatchesNotificationKpiService,
            IOfferKpisElasticService offerKpisElasticService,
            IProducerRabbitMQ producerRabbitMQ,
            ISecurityOfferService securityOfferService,
            IOfferCvCountersService offerCvCountersService,
            IRequestDeviceRecorder requestDeviceRecorder,
            IHttpService httpService,
            IKpiService kpiService) : base(productService, productSubGroupsService, offerService, companyProductService, companyService, portalConfigurationService, dictionaryService, securityService, exceptionPublisherService)
        {
            _encryptionService = encryptionService;
            _userService = userService;
            _companyProductService = companyProductService;
            _landingProductsService = landingProductsService;
            _isNewSuggest = ConfigurationManager.AppSettings["NewSuggestByCT"] != null && ConfigurationManager.AppSettings["NewSuggestByCT"].ToLower() == "true";
            _companyTrackingService = companyTrackingService;
            _offerMatchesNotificationKpiService = offerMatchesNotificationKpiService;
            _producerRabbitMQ = producerRabbitMQ;
            _messageJobdsChatService = messageJobdsChatService;
            _securityOfferService = securityOfferService;
            _offerKpisElasticService = offerKpisElasticService;
            _offerCvCountersService = offerCvCountersService;
            _requestDeviceRecorder = requestDeviceRecorder;
            _httpService = httpService;
            _kpiService = kpiService;
        }

        [Route]
        public async Task<ActionResult> Index(string titleOfferOriginEmail = null, int selectedOrder = 0)
        {
            CompanyCredentials companycredentialsAux = null;
            try
            {
                var portalConfig = PortalConfigurationService.GetPortalConfiguration();
                var companyCredentials = SecurityHelper.GetCompanyCredentials();
                companycredentialsAux = companyCredentials;
                var company = CompanyService.GetByPK(GetCompanySearchById(portalConfig.PortalId, companyCredentials.IdCompany));

                if (checkBlocked(portalConfig.company_block, company.Blocked, company.LastDateBlockedUpdate, portalConfig.DaysBlock))
                    return RedirectToAction("Blocked", "Account");

                if (companyCredentials.UserRole == UserRoleEnum.COMPUADVISOR)
                    return RedirectToAction("", "Company");

                var companyProduct = CompanyProductService.GetByIdCompany(companyCredentials.IdCompany, companyCredentials.PortalId, OfferIntegratorEnum.CompuTrabajo);
                var hasNewFreemiumChat = HasNewFreemiumChat(portalConfig, companyCredentials.IdCompany);

                var titleOffer = (titleOfferOriginEmail != null && portalConfig.SendMailsMatchesForOffers) ? _encryptionService.Decrypt(titleOfferOriginEmail) : string.Empty;
                var homeOfferDataModel = new HomeOfferDataModel()
                {
                    Pager = new Models.PagerDataModel()
                    {
                        PageSizeSelected = 10
                    },
                    HaveMembresyActive = companyProduct.GroupId == (short)ProductGroupsEnum.Membership,
                    AlertDataModel = GetAlertInformation(company, companyProduct, portalConfig),
                    ConvertToCompletePopUp = GetConvertToCompletePopUpDataModel(portalConfig),
                    SelectOfferPopUp = GetSelectOfferPopUpDataModel(portalConfig),
                    DeleteOfferPopUp = GetDeleteOfferPopUp(portalConfig),
                    ActiveByUserCreditsPopUp = GetActiveByUserCreditsPopUp(portalConfig),
                    ActionNotAllowedByFeature = GetActionNotAllowedPopUpByFeature(portalConfig),
                    ActionNotAllowedByMembership = GetActionNotAllowedPopUpByMembership(portalConfig),
                    NotFeaturePopUp = GetNotFeaturePopUp(portalConfig),
                    CanDelete = SecurityService.IsActionPemitedByRole((short)companyCredentials.UserRole, (short)portalConfig.PortalId, SecurityActionEnum.CompanyOffersDeleteAllOnesCanSee),
                    HasCvsExtractions = companyProduct.Features.Any(f => f.AmbitId == (int)ProductAmbitEnum.CVsDownload),
                    CanManageOffers = SecurityHelper.IsActionPemitedByRole(SecurityActionEnum.ManageOffers),
                    BtnConvertToCompleteEncryptedName = _encryptionService.Encrypt(((int)TpvButtonOriginEnum.OfferListConvertToPack).ToString()),
                    ProdCWConvertToComplete = _landingProductsService.ManageCWFeatures((int)ProductAmbitEnum.CVsFreemium, companyProduct.IdCompany, portalConfig),
                    HasNewFreemiumChat = hasNewFreemiumChat,
                    UrlChatControllerName = GetUrlChatControllerName(hasNewFreemiumChat, companyProduct),
                    ShowPopupMatchesMailTitleOffer = !string.IsNullOrWhiteSpace(titleOffer) ? titleOffer : string.Empty,
                    HasNewMatchesNotificationsFeature = companyProduct.GroupId != (short)ProductGroupsEnum.Membership
                    && _companyProductService.HasFeature(ProductAmbitEnum.NewMatchesPushNotifications, companyProduct) 
                    && portalConfig.NewMatchesPushNotifications 
                    && companyCredentials.TestAB == TestABEnum.A
                };
                FillOfferEditFeatureData(homeOfferDataModel, companyProduct, portalConfig);
                FillOfferHighlightFeatureData(homeOfferDataModel, companyProduct, portalConfig);
                FillOfferUrgentFeatureData(homeOfferDataModel, companyProduct, portalConfig);

                List<UserEntity> usersVisible = GetUsersVisible(companyCredentials.IdCompany, companyCredentials);
                PopulateFilterList(homeOfferDataModel, portalConfig, usersVisible, companyProduct);
                homeOfferDataModel.OfferOrderSelected = selectedOrder;
                await FillOffersAsync(homeOfferDataModel, portalConfig, companyCredentials, homeOfferDataModel.Pager.PageSelected, usersVisible, companyProduct);

                return View("Index", homeOfferDataModel);
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyOffersController Index-Get {ex}");
                Dictionary<string, string> extradata = new Dictionary<string, string>();
                extradata.Add("Idcompany", companycredentialsAux?.IdCompany.ToString());
                extradata.Add("IdPortal", companycredentialsAux?.PortalId.ToString());
                extradata.Add("CompanyName", companycredentialsAux?.CompanyName.ToString());
                extradata.Add("UserRole", companycredentialsAux?.UserRole.ToString());
                extradata.Add("UserId", companycredentialsAux?.UserId.ToString());
                extradata.Add("UserName", companycredentialsAux?.Username.ToString());

                ExceptionPublisherService.Publish(ex, "CompanyOffersController", "Index-Get", false, extradata);
                return RedirectToAction("Index", "Company");
            }
        }

        private string GetUrlChatControllerName(bool hasNewFreemiumChat, CompanyProductEntity companyProduct)
        {
            return companyProduct.GroupId == (short)ProductGroupsEnum.Membership
                || companyProduct.GroupId != (short)ProductGroupsEnum.Membership && !hasNewFreemiumChat
                ? "CompanyMessages"
                : "CompanyConversations";
        }

        private bool HasNewFreemiumChat(PortalConfig portalConfig, int idCompany)
        {
            return portalConfig.AEPortalConfig.EnableProgressiveChatFreemium
                && CompanyProductService.HasActiveFeatureByProduct(new SearchCompanyProductsByCompanyDTO(idCompany, ProductAmbitEnum.ChatContactFreemium, portalConfig.PortalId, ProductEnum.FreemiumService));
        }

        private void FillOfferEditFeatureData(HomeOfferDataModel homeOfferDataModel, CompanyProductEntity companyProduct, PortalConfig portalConfig)
        {
            var prodCWEditOffer = _landingProductsService.ManageCWFeatures((short)ProductAmbitEnum.OfferEdit, companyProduct.IdCompany, portalConfig);
            homeOfferDataModel.ProdCWEditOffer = prodCWEditOffer;
            homeOfferDataModel.NotOfferEditPopUp = ProductService.ReturnFeatureOfferPopUp(companyProduct, prodCWEditOffer, (short)ProductAmbitEnum.OfferEdit);
            homeOfferDataModel.BtnEnableEditOffer = _encryptionService.Encrypt(((int)TpvButtonOriginEnum.OfferListEditOffer).ToString());
        }

        private void FillOfferHighlightFeatureData(HomeOfferDataModel homeOfferDataModel, CompanyProductEntity companyProduct, PortalConfig portalConfig)
        {
            var prodCWHighlightOffer = _landingProductsService.ManageCWFeatures((short)ProductAmbitEnum.OfferHighlighted, companyProduct.IdCompany, portalConfig);
            homeOfferDataModel.ProdCWHighlightOffer = prodCWHighlightOffer;
            homeOfferDataModel.NotHighlightOfferPopUp = ProductService.ReturnFeatureOfferPopUp(companyProduct, prodCWHighlightOffer, (short)ProductAmbitEnum.OfferHighlighted);
            homeOfferDataModel.BtnEnableHighlightOffer = _encryptionService.Encrypt(((int)TpvButtonOriginEnum.OfferListHighlightOffer).ToString());
        }

        private void FillOfferUrgentFeatureData(HomeOfferDataModel homeOfferDataModel, CompanyProductEntity companyProduct, PortalConfig portalConfig)
        {
            var prodCWUrgentOffer = _landingProductsService.ManageCWFeatures((short)ProductAmbitEnum.OfferUrgent, companyProduct.IdCompany, portalConfig);
            homeOfferDataModel.ProdCWUrgentOffer = prodCWUrgentOffer;
            homeOfferDataModel.NotUrgentOfferPopUp = ProductService.ReturnFeatureOfferPopUp(companyProduct, prodCWUrgentOffer, (short)ProductAmbitEnum.OfferUrgent);
            homeOfferDataModel.BtnEnableUrgentOffer = _encryptionService.Encrypt(((int)TpvButtonOriginEnum.OfferListUrgentOffer).ToString());
        }

        private PopUpDataModel GetConvertToCompletePopUpDataModel(PortalConfig portalConfig)
        {
            return new PopUpDataModel()
            {
                Title = PageLiteralsHelper.GetLiteral("LIT_INFO", PageId, portalConfig),
                Message = PageLiteralsHelper.GetLiteral("LIT_RENEW_OFFER_CONSUME", PageId, portalConfig),
                TitleBtnOk = PageLiteralsHelper.GetLiteral("BTN_POP_CONSUMIR", PageId, portalConfig),
                TitleBtnKo = PageLiteralsHelper.GetLiteral("LIT_CANCELAR", PageId, portalConfig),
                LiteralProcessing = PageLiteralsHelper.GetLiteral("LIT_PROCESSING", PageId, portalConfig),
                LiteralError = string.Empty,
                HasInput = false,
                HasButtonOk = true
            };
        }

        private PopUpDataModel GetActiveByUserCreditsPopUp(PortalConfig portalConfig)
        {
            return new PopUpDataModel()
            {
                Title = PageLiteralsHelper.GetLiteral("LIT_POPUP_OFERT_DESTA", PageId, portalConfig),
                Message = PageLiteralsHelper.GetLiteral("LIT_DESCRIPTION_POPUP_ACTIVE_USERS", PageId, portalConfig),
                TitleBtnOk = PageLiteralsHelper.GetLiteral("LIT_BTT_RENEW_OFFER", PageId, portalConfig),
                HasButtonOk = true
            };
        }

        private PopUpDataModel GetNotFeaturePopUp(PortalConfig portalConfig)
        {
            return new PopUpDataModel()
            {
                Title = PageLiteralsHelper.GetLiteral("LIT_ACTION_NOT_ALLOWED_FEATURE_TITLE", PageId, portalConfig),
                Message = PageLiteralsHelper.GetLiteral("LIT_ACTION_NOT_ALLOWED_FEATURE", PageId, portalConfig),
                TitleBtnOk = PageLiteralsHelper.GetLiteral("LIT_BTN_CONTRATAR", PageId, portalConfig),
                TitleBtnKo = PageLiteralsHelper.GetLiteral("LIT_CANCELAR", PageId, portalConfig),
                LiteralError = string.Empty
            };
        }

        private PopUpDataModel GetActionNotAllowedPopUpByFeature(PortalConfig portalConfig)
        {
            return new PopUpDataModel()
            {
                Title = PageLiteralsHelper.GetLiteral("LIT_ACTION_NOT_ALLOWED_FEATURE_TITLE", PageId, portalConfig),
                Message = PageLiteralsHelper.GetLiteral("LIT_ACTION_NOT_ALLOWED_FEATURE", PageId, portalConfig),
                TitleBtnOk = PageLiteralsHelper.GetLiteral("LIT_BTT_BUY", PageId, portalConfig),
                HasButtonOk = true,
            };
        }

        private PopUpDataModel GetActionNotAllowedPopUpByMembership(PortalConfig portalConfig)
        {
            return new PopUpDataModel()
            {
                Title = PageLiteralsHelper.GetLiteral("LIT_ACTION_NOT_ALLOWED_MEMBERSHIP_TITLE", PageId, portalConfig),
                Message = PageLiteralsHelper.GetLiteral("LIT_ACTION_NOT_ALLOWED_MEMBERSHIP", PageId, portalConfig),
                TitleBtnOk = PageLiteralsHelper.GetLiteral("LIT_BTN_CONTACTAR", PageId, portalConfig),
                HasButtonOk = true,
            };
        }

        [Route("Print")]
        public ActionResult Print(string oi)
        {
            try
            {
                var companyCredendentials = SecurityHelper.GetCompanyCredentials();
                var portalConfig = PortalConfigurationService.GetPortalConfiguration();

                if (!string.IsNullOrEmpty(oi))
                {
                    if (!IsMy(companyCredendentials, Convert.ToInt32(_encryptionService.Decrypt(oi)), portalConfig))
                        return RedirectToAction("", "Company");
                }

                var offer = OfferService.GetByPk(oi, portalConfig.PortalId, false);
                var company = CompanyService.GetByPK(GetCompanySearchById(portalConfig.PortalId, companyCredendentials.IdCompany));

                var dictionarySelected = portalConfig.NewDictionarySkillsActivate ? DictionaryEnum.COMMON_SKILLS : DictionaryEnum.OFFICE_SKILLS;

                var printOfferDataModel = new OfferPrintDataModel()
                {
                    Offer = Mapper.Map<OfferEntity, OfferDataModel>(offer),
                    Company = Mapper.Map<CompanyEntity, CompanyDataModel>(company),
                    Area = DictionaryService.GetDictionaryValue(DictionaryEnum.CATEGORY, offer.idcategory.ToString(), portalConfig.PortalId),
                    Country = DictionaryService.GetDictionaryValue(DictionaryEnum.COUNTRY, offer.idcountry.ToString(), portalConfig.PortalId),
                    Localization = DictionaryService.GetDictionaryValue(DictionaryEnum.LOCALIZATION_BY_COUNTRY, offer.idcountry, offer.idlocalization.ToString(), portalConfig.PortalId),
                    City = DictionaryService.GetDictionaryValue(DictionaryEnum.CITIES_BY_LOCALIZATION, offer.idlocalization, offer.idcity.ToString(), portalConfig.PortalId),
                    WorkingTime = DictionaryService.GetDictionaryValue(DictionaryEnum.EMPLOYMENT_TYPE, offer.idemploymenttype.ToString(), portalConfig.PortalId),
                    WorkPlaceType = DictionaryService.GetDictionaryValue(DictionaryEnum.WORKPLACES_TYPES, offer.IdWorkPlaceType.ToString(), portalConfig.PortalId),
                    ContractType = DictionaryService.GetDictionaryValue(DictionaryEnum.CONTRACT_TYPE, offer.idcontracttype.ToString(), portalConfig.PortalId),
                    MiniumStudies = DictionaryService.GetDictionaryValue(DictionaryEnum.EDUCATION_LEVEL, offer.idstudy.ToString(), portalConfig.PortalId),
                    Languages = string.Join(", ", offer.Languages.Select(l => DictionaryService.GetDictionaryValue(DictionaryEnum.LANGUAGE, l.ididiom.ToString(), portalConfig.PortalId)).ToList()),
                    Skills = string.Join(", ", offer.skills.Select(l => DictionaryService.GetDictionaryValue(dictionarySelected, l.idskill.ToString(), portalConfig.PortalId)).ToList()),
                    DriveLicenses = string.Join(", ", offer.drivelicenses.Select(l => DictionaryService.GetDictionaryValue(DictionaryEnum.DRIVER_LICENSE, l.iddrivelicense.ToString(), portalConfig.PortalId)).ToList()),
                };

                return View(printOfferDataModel);
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyOffersController Print {ex}");
                ExceptionPublisherService.Publish(ex, "CompanyOffersController", "Print");
                return RedirectToAction("Index", "Company");
            }
        }

        [Route]
        [HttpPost]
        [ValidateAntiForgeryToken()]
        public async Task<ActionResult> Index(HomeOfferDataModel homeOfferDataModel)
        {
            CompanyCredentials companycredentialsAux = null;
            try
            {
                var portalConfig = PortalConfigurationService.GetPortalConfiguration();
                var companyCredentials = SecurityHelper.GetCompanyCredentials();
                companycredentialsAux = companyCredentials;
                var companyProduct = CompanyProductService.GetByIdCompany(companyCredentials.IdCompany, companyCredentials.PortalId, OfferIntegratorEnum.CompuTrabajo);
                var company = CompanyService.GetByPK(GetCompanySearchById(portalConfig.PortalId, companyCredentials.IdCompany));

                homeOfferDataModel.HaveMembresyActive = companyProduct.GroupId == (short)ProductGroupsEnum.Membership;
                homeOfferDataModel.AlertDataModel = GetAlertInformation(company, companyProduct, portalConfig);
                homeOfferDataModel.ConvertToCompletePopUp = GetConvertToCompletePopUpDataModel(portalConfig);
                homeOfferDataModel.SelectOfferPopUp = GetSelectOfferPopUpDataModel(portalConfig);
                homeOfferDataModel.DeleteOfferPopUp = GetDeleteOfferPopUp(portalConfig);
                homeOfferDataModel.ActiveByUserCreditsPopUp = GetActiveByUserCreditsPopUp(portalConfig);
                homeOfferDataModel.ActionNotAllowedByFeature = GetActionNotAllowedPopUpByFeature(portalConfig);
                homeOfferDataModel.ActionNotAllowedByMembership = GetActionNotAllowedPopUpByMembership(portalConfig);
                homeOfferDataModel.NotFeaturePopUp = GetNotFeaturePopUp(portalConfig);
                homeOfferDataModel.BtnConvertToCompleteEncryptedName = _encryptionService.Encrypt(((int)TpvButtonOriginEnum.OfferListConvertToPack).ToString());
                homeOfferDataModel.HasCvsExtractions = companyProduct.Features.Any(f => f.AmbitId == (int)ProductAmbitEnum.CVsDownload);
                homeOfferDataModel.HasNewMatchesNotificationsFeature = companyProduct.GroupId != (short)ProductGroupsEnum.Membership
                    && _companyProductService.HasFeature(ProductAmbitEnum.NewMatchesPushNotifications, companyProduct) 
                    && portalConfig.NewMatchesPushNotifications 
                    && companyCredentials.TestAB == TestABEnum.A;

                FillOfferHighlightFeatureData(homeOfferDataModel, companyProduct, portalConfig);
                FillOfferUrgentFeatureData(homeOfferDataModel, companyProduct, portalConfig);

                if (homeOfferDataModel.SelectedProfesionalCategory != null) homeOfferDataModel.SelectedProfesionalCategories.Add((int)homeOfferDataModel.SelectedProfesionalCategory);
                if (homeOfferDataModel.SelectedOfferState != null) homeOfferDataModel.SelectedOfferStatus.Add((int)homeOfferDataModel.SelectedOfferState);

                List<UserEntity> usersVisible = GetUsersVisible(companyCredentials.IdCompany, companyCredentials);
                PopulateFilterList(homeOfferDataModel, portalConfig, usersVisible, companyProduct);
                await FillOffersAsync(homeOfferDataModel, portalConfig, companyCredentials, homeOfferDataModel.Pager.PageSelected, usersVisible, companyProduct);

                return View(homeOfferDataModel);
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyOffersController Index-Post {ex}");
                Dictionary<string, string> extradata = new Dictionary<string, string>();
                extradata.Add("Idcompany", companycredentialsAux?.IdCompany.ToString());
                extradata.Add("IdPortal", companycredentialsAux?.PortalId.ToString());
                extradata.Add("CompanyName", companycredentialsAux?.CompanyName.ToString());
                extradata.Add("UserRole", companycredentialsAux?.UserRole.ToString());
                extradata.Add("UserId", companycredentialsAux?.UserId.ToString());
                extradata.Add("UserName", companycredentialsAux?.Username.ToString());

                ExceptionPublisherService.Publish(ex, "CompanyOffersController", "Index-Post", false, extradata);
                return RedirectToAction("Index", "Company");
            }
        }

        [HttpPost]
        public string AutoCompleteOfferTitle(string returnAutoCompleteMethod)
        {
            HomeOfferSuggestFilterModel homeOfferSuggestFilterModel = GetHomeOfferSuggestFilterFromClient(returnAutoCompleteMethod);

            if (homeOfferSuggestFilterModel == null)
            {
                Response.StatusCode = 500;
                Response.StatusDescription = "Data invalid";
                return "false";
            }

            CompanyCredentials companyCredentials = SecurityHelper.GetCompanyCredentials();
            var offerSuggestFilter = GetOfferSuggestFilter(homeOfferSuggestFilterModel, companyCredentials.PortalId, companyCredentials.IdCompany, companyCredentials.UserId, companyCredentials.UserRole);

            return OfferService.ApiOffersEmpresaSuggest(offerSuggestFilter, _isNewSuggest);
        }

        #region "private method Rabbit Deletedoffer"

        private bool PublishToRabbitOffersEncryptedToDeleted(List<string> offerencryptedIds, short idPortal)
        {
            try
            {
                foreach (var offerEncrypted in offerencryptedIds)
                {
                    long.TryParse(_encryptionService.Decrypt(offerEncrypted), out var offerId);
                    var result = PublishToRabbitOfferDeleted(offerId, idPortal);
                }
                return true;
            }
            catch
            {
                return false;
            }
        }

        private (bool Error, string ErrorMessage) PublishToRabbitOfferDeleted(long idOffer, short idPortal)
        {

            try
            {
                //Assignation
                int idApp = 0;
                int idQueue = 0;
                if (ConfigurationManager.AppSettings["APP_ID"] != null) int.TryParse(ConfigurationManager.AppSettings["APP_ID"], out idApp);
                if (ConfigurationManager.AppSettings["RABBITMQ.PRODUCERID.OFFER_DELETED_EVENT"] != null) int.TryParse(ConfigurationManager.AppSettings["RABBITMQ.PRODUCERID.OFFER_DELETED_EVENT"], out idQueue);


                //Validate
                if (idQueue <= 0) return (true, $"valor recuperado de la idQueue {idQueue}");
                if (idApp <= 0) return (true, $"valor recuperado de la idApp {idApp}");
                if (idOffer <= 0) return (true, $"valor recuperado de la idOffer {idOffer}");
                if (idPortal <= 0) return (true, $"valor recuperado de la IdPortal {idPortal}");


                var @event = new OfferDeletedListenerEvent()
                {
                    IdApp = idApp,
                    IdPortal = idPortal,
                    IdOffer = idOffer
                };

                //Publish to rabbit queue
                _producerRabbitMQ.Publish(new ChatListenerItemQueue() { OfferDeletedEvent = @event }, new RabbitMQ.Model.ProducerDeliverEventArgs()
                {
                    AppId = idApp,
                    QueueId = idQueue,
                    Persistent = true,
                    NodeName = $"JOBADS_LISTENER_OFFER_DELETED_EVENT_{idApp.ToString()}",
                    Headers = new Dictionary<string, object>()
                    {
                        { "AppId",  idApp}
                    }
                });

                return (false, String.Empty);
            }
            catch (Exception ex)
            {
                ExceptionPublisherService.Publish(ex, "CompanyOfferController", "PublishToRabbitOfferDeleted", false,
                    new Dictionary<string, string>()
                    {
                        { "IdOffer", idOffer.ToString() }
                    }
                );

                return (true, ex.Message);
            }
        }

        #endregion

        [HttpPost]
        public string ExecuteAction(string idoffers, int ac, int? st, string btnConvertEnc)
        {
            try
            {
                var offersIds = idoffers.Split('|').Where(s => !string.IsNullOrEmpty(s)).ToList();
                var companyCredentials = SecurityHelper.GetCompanyCredentials();

                switch (ac)
                {
                    case (int)OfferActionEnum.Up:
                        return OfferService.UploadOffers(offersIds, companyCredentials.IdCompany, OfferIntegratorEnum.CompuTrabajo, companyCredentials.PortalId);
                    case (int)OfferActionEnum.ChangeStatus:
                        if (st == (short)OfferStatusEnum.Eliminada && !IsPermitedDeleteOffer(offersIds, companyCredentials))
                            return "0";

                        var result = OfferService.SetStatus(offersIds, (short)st, companyCredentials.IdCompany, (int)companyCredentials.UserId, companyCredentials.PortalId);

                        if (st == (short)OfferStatusEnum.Eliminada) PublishToRabbitOffersEncryptedToDeleted(offersIds, companyCredentials.PortalId);

                        return result;
                    case (int)OfferActionEnum.Renew:

                        return RenewOffers(companyCredentials, offersIds);


                    case (int)OfferActionEnum.Highlighted:
                    case (int)OfferActionEnum.Urgent:
                        var offerFromBBDD = OfferService.GetByPk(offersIds.FirstOrDefault(), companyCredentials.PortalId);
                        var offerUser = _userService.Get(offerFromBBDD.createdby, true, companyCredentials.PortalId);
                        return OfferService.ChangeOfferVisualization(offerFromBBDD, offerUser.ContactName, companyCredentials.UserId, (short)st, ac);
                }

                return "true";
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyOffersController ExecuteAction {ex}");
                ExceptionPublisherService.Publish(ex, "CompanyOffersController", "ExecuteAction");
                return "false";
            }
        }

        public ActionResult ConvertPack(string sg, string io, string btnConvertEnc)
        {
            var idOriginButton = 0;
            if (!string.IsNullOrEmpty(sg)
                && !string.IsNullOrEmpty(io))
            {
                short.TryParse(_encryptionService.Decrypt(sg), out var subGroupId);
                int.TryParse(_encryptionService.Decrypt(io), out var idOffer);

                if (idOffer > 0)
                {
                    int.TryParse(_encryptionService.Decrypt(btnConvertEnc), out idOriginButton);
                    var companyCredentials = SecurityHelper.GetCompanyCredentials();
                    var portalConfig = PortalConfigurationService.GetPortalConfiguration();

                    OfferConvertTrace offerConvertTrace = new OfferConvertTrace()
                    {
                        IdPortal = companyCredentials.PortalId,
                        IdOffer = idOffer,
                        IsAutoConvertToComplete = false,
                        IdOriginButton = idOriginButton,
                        IdPage = _httpService.GetSessionSourcePage(),
                        OriginMethod = "CompanyOffersController.ConvertPack",
                        IsFromMobile = RequestOrigin.IsFromMobile(Request),
                        DeviceIsKnowed = true,
                        OfferConvertTraceMatchesMailDTO = GetOfferConvertTraceMatchesMailDTO(portalConfig, companyCredentials)
                    };
                    OfferService.ConvertToPack(idOffer, subGroupId, companyCredentials.UserId, offerConvertTrace);
                }
            }

            return RedirectToAction("Index", "Company");
        }

        private bool IsMy(CompanyCredentials companyCredentials, int idOffer, PortalConfig portalConfig)
        {
            return _securityOfferService.IsMy(new IsMyOfferDTO(companyCredentials.UserRole, companyCredentials.IdCompany, companyCredentials.PortalId, (int)companyCredentials.UserId, idOffer, portalConfig.EnabledNewIsMy));
        }

        private string GetOfferContactName(string offerEncryptedId, short idPortal)
        {
            OfferEntity offer;
            UserEntity userOfferCreated;
            var contactName = "";

            offer = OfferService.GetByPk(offerEncryptedId, idPortal);
            userOfferCreated = (offer == null || offer.idoffer <= 0) ? null : _userService.Get(offer.createdby, true, offer.idportal);
            contactName = (userOfferCreated == null) ? "" : userOfferCreated.ContactName;

            return contactName;
        }

        private string RenewOffers(CompanyCredentials companyCredentials, List<string> offersIds)
        {
            var companyProduct = CompanyProductService.GetByIdCompany(companyCredentials.IdCompany, companyCredentials.PortalId, OfferIntegratorEnum.CompuTrabajo, companyCredentials.UserId);
            var canPublish = _companyProductService.CanPublish(companyProduct, companyCredentials.UserId, OfferIntegratorEnum.CompuTrabajo);
            var contactName = (offersIds.Count > 0) ? GetOfferContactName(offersIds.FirstOrDefault(), companyCredentials.PortalId) : "";
            JavaScriptSerializer serializer = new JavaScriptSerializer();

            if (canPublish)
            {
                return OfferService.RenewOffers(offersIds, companyCredentials.IdCompany, OfferIntegratorEnum.CompuTrabajo, companyCredentials.PortalId);
            }
            else
            {
                return serializer.Serialize(new { isValid = false, ContactName = contactName, DefaultMessageError = false });
            }
        }

        private bool IsPermitedDeleteOffer(List<string> offersIds, CompanyCredentials companyCredentials)
        {
            if (SecurityHelper.IsActionPemitedByRole(SecurityActionEnum.CompanyOffersDeleteAllOnesCanSee) && AllOffersIdsBelongToCompany(offersIds, companyCredentials))
                return true;

            var offerByUser = OfferService.SearchByUser((int)companyCredentials.UserId, companyCredentials.IdCompany, 0, 0, string.Empty, 0, 0, 0, new List<int>(), OfferIntegratorEnum.CompuTrabajo, companyCredentials.PortalId);

            if (!offerByUser.Any())
                return false;

            if (!offerByUser.Select(c => c.idofferencrypted).Contains(offersIds.FirstOrDefault()))
                return false;

            return true;
        }

        private bool AllOffersIdsBelongToCompany(List<string> offerEncryptedIds, CompanyCredentials companyCredentials)
        {

            var offersCompanyIds = OfferService.GetOfferIds(companyCredentials.IdCompany, companyCredentials.PortalId, (short)OfferIntegratorEnum.CompuTrabajo);
            var offersIds = offerEncryptedIds.Select(o => _encryptionService.Decrypt(o).ToInt()).ToList();

            if (!offersIds.All(id => offersCompanyIds.Contains(id)))
                return false;

            return true;

        }


        private async Task FillOffersAsync(HomeOfferDataModel homeOfferDataModel, PortalConfig portalConfig, CompanyCredentials companyCredentials, int currentPage, List<UserEntity> usersVisible, CompanyProductEntity companyProduct)
        {
            var offerIds = new List<int>();

            if (!string.IsNullOrEmpty(homeOfferDataModel.SearchTitle))
            {
                offerIds = GetOffersIds(homeOfferDataModel, portalConfig, companyCredentials);
                if (!offerIds.Any())
                {
                    homeOfferDataModel.Pager.TotalRows = 0;
                    return;
                }
            }

            var filteredUsers = GetFileredUseres(homeOfferDataModel, companyCredentials.UserId, usersVisible);
            var countOnlyFolder1 = GetFoldersToCountByFeature(companyProduct);
            var offerEntities = OfferService.Search(companyCredentials.IdCompany, 0, currentPage, homeOfferDataModel.Pager.PageSizeSelected,
                                                    TranslateOrderByEnum(homeOfferDataModel.OfferOrderSelected),
                                                    new List<int>(), homeOfferDataModel.SelectedProfesionalCategories,
                                                    homeOfferDataModel.SelectedOfferStatus, filteredUsers, offerIds,
                                                    homeOfferDataModel.Highlighteds, homeOfferDataModel.Urgents,
                                                    homeOfferDataModel.KillerQuestions, homeOfferDataModel.HiddenName, countOnlyFolder1,
                                                    OfferIntegratorEnum.CompuTrabajo, companyCredentials.PortalId);

            if (!string.IsNullOrEmpty(homeOfferDataModel.SearchTitle))
            {
                offerEntities = offerEntities.ToList();
            }

            homeOfferDataModel.Pager.TotalRows = OfferService.GetTotalOffersByCompany(companyCredentials.IdCompany, 0,
                new List<int>(), homeOfferDataModel.SelectedProfesionalCategories, homeOfferDataModel.SelectedOfferStatus, filteredUsers, offerIds, homeOfferDataModel.Highlighteds, homeOfferDataModel.Urgents, homeOfferDataModel.KillerQuestions, homeOfferDataModel.HiddenName, OfferIntegratorEnum.CompuTrabajo, portalConfig.PortalId, companyCredentials.CleanCacheOffers);
            homeOfferDataModel.Offers = Mapper.Map<List<OfferEntity>, List<OfferDataModel>>(offerEntities);
            homeOfferDataModel.ShowTotalNotViewed = ConfigurationManager.AppSettings["ShowTotalNotViewed"] != null && ConfigurationManager.AppSettings["ShowTotalNotViewed"] == "true";
            await FillAditionalOffersInformationAsync(portalConfig, homeOfferDataModel.Offers, offerEntities, companyCredentials, homeOfferDataModel.CanDelete, companyProduct, homeOfferDataModel.NotHighlightOfferPopUp, homeOfferDataModel.NotUrgentOfferPopUp, homeOfferDataModel.NotOfferEditPopUp, homeOfferDataModel.CanManageOffers);

            if (portalConfig.AEPortalConfig.ShowOfferViews)
                await FillOfferViews(homeOfferDataModel, offerEntities, companyCredentials);
        }

        private async Task FillOfferViews(HomeOfferDataModel homeOfferDataModel, List<OfferEntity> offerEntities, CompanyCredentials companyCredentials)
        {
            var offersKpisQuery = new OfferKpisElasticSearchQueryDTO
            {
                OffersId = offerEntities.Select(x => x.idoffer).ToList(),
            };

            var offersKpis = await _offerKpisElasticService.GetTotalViewsOfferKpi(offersKpisQuery, companyCredentials);

            var date = DateTime.Now.Date;
            foreach (var offer in homeOfferDataModel.Offers)
            {
                var offerKpi = offersKpis?.FirstOrDefault(x => x.IdOffer == offer.idoffer);
                int views = offerKpi?.Views > 0 ? offerKpi.Views : offer.total_applied > 0 ? offer.total_applied : 0;

                offer.Views = views == 0 || offer.createdon.Date == date ? "-1" : StringToolsHelper.ChangeGroupPointForDecimalPoint(views.ToString("N0"), companyCredentials.PortalId);
                offer.HasOfferViews = true;
            }
        }


        private bool GetFoldersToCountByFeature(CompanyProductEntity companyProduct)
        {
            return !companyProduct.Features.Any(f => f.AmbitId == (int)ProductAmbitEnum.CVFolders);
        }

        private List<int> GetFileredUseres(HomeOfferDataModel homeOfferDataModel, long actualUserId, List<UserEntity> usersVisible)
        {
            List<int> filteredUser;

            if (SecurityHelper.IsActionPemitedByRole(SecurityActionEnum.CompanyOffersManageByAllUsers))
            {
                return homeOfferDataModel.SelectedManagers.Select(sm => Convert.ToInt32(_encryptionService.Decrypt(sm))).ToList();
            }
            else if (SecurityHelper.IsActionPemitedByRole(SecurityActionEnum.CompanyOffersManageLessThanYoursRol))
            {
                filteredUser = homeOfferDataModel.SelectedManagers.Select(sm => Convert.ToInt32(_encryptionService.Decrypt(sm))).ToList();

                if (filteredUser.Any())
                {
                    return filteredUser;
                }

                List<long> usersId = GetListUsersId(usersVisible);
                usersId.ForEach(userId => filteredUser.Add((int)userId));
                return filteredUser;
            }
            else
            {
                return new List<int> { (int)actualUserId };
            }
        }

        private OfferSuggestElasticFilter GetOfferSuggestFilter(HomeOfferSuggestFilterModel homeOfferSuggestFilterModel, short portalId, int companyId, long userId, UserRoleEnum userRoleId)
        {
            return new OfferSuggestElasticFilter()
            {
                IdCompany = companyId,
                IdPortal = portalId,
                Title = homeOfferSuggestFilterModel.TextToSearch,
                AssignedUser = new List<long>() { userId },
                IdUser = _encryptionService.Encrypt(userId.ToString()),
                IdRole = _encryptionService.Encrypt(((short)userRoleId).ToString())
                //Features = string.Join(",", homeOfferSuggestFilterModel.FeatureFacetId.Select(n => EncryptationHelper.Decrypt(n)).ToList()),
                //status = string.Join(",", homeOfferSuggestFilterModel.StatusId.Select(n => n.ToString()).ToList()),
                //idCategory = string.Join(",", homeOfferSuggestFilterModel.ProfessionId.Select(n => n.ToString()).ToList()),
                //IdCreator = string.Join(",", usersId.Select(n => _encryptionService.Encrypt(n.ToString())).ToList()),
                //idRole = string.Join(",", GetIdRolesToFilter(userRoleId).Select(n => _encryptionService.Encrypt(n.ToString())).ToList())
            };
        }

        //TODO Move to service
        private string TranslateOrderByEnum(int orderByOffers)
        {
            var result = string.Empty;

            switch (orderByOffers)
            {
                case (int)OrderOfferByEnum.Default:
                    result = "Id DESC";
                    break;
                case (int)OrderOfferByEnum.ByPublicationAsc:
                    result = "Id ASC";
                    break;
                case (int)OrderOfferByEnum.ByTitleAsc:
                    result = "trim(Title) ASC";
                    break;
                case (int)OrderOfferByEnum.ByTitleDesc:
                    result = "trim(Title) DESC";
                    break;
                case (int)OrderOfferByEnum.ByExpirationAsc:
                    result = "OfferIntegrators.ExpirationTime ASC";
                    break;
                case (int)OrderOfferByEnum.ByExpirationDesc:
                    result = "OfferIntegrators.ExpirationTime DESC";
                    break;
                case (int)OrderOfferByEnum.ByStatusAsc:
                    result = "OfferIntegrators.IdStatus ASC";
                    break;
                case (int)OrderOfferByEnum.ByStatusDesc:
                    result = "OfferIntegrators.IdStatus DESC";
                    break;
                case (int)OrderOfferByEnum.ByInscritosAsc:
                    result = "OfferIntegrators.TotalApplied ASC";
                    break;
                case (int)OrderOfferByEnum.ByInscritosDesc:
                    result = "OfferIntegrators.TotalApplied DESC";
                    break;
                case (int)OrderOfferByEnum.ByLastUpdateAsc:
                    result = "datelastup asc";
                    break;
                case (int)OrderOfferByEnum.ByLastUpdateDesc:
                    result = "datelastup desc";
                    break;
                case (int)OrderOfferByEnum.ByNotViewedAppliesAsc:
                    result = "TotalNewNotViewed asc";
                    break;
                case (int)OrderOfferByEnum.ByNotViewedAppliesDesc:
                    result = "TotalNewNotViewed desc";
                    break;
                case (int)OrderOfferByEnum.ByNewAppliesAsc:
                    result = "TotalNew asc";
                    break;
                case (int)OrderOfferByEnum.ByNewAppliesDesc:
                    result = "TotalNew desc";
                    break;
            }

            return result;
        }

        private async Task FillAditionalOffersInformationAsync(PortalConfig portalConfig, List<OfferDataModel> offers, List<OfferEntity> offersEntities, CompanyCredentials companyCredentials, bool canDelete, CompanyProductEntity companyProduct, short notHighlightOfferPopUp, short notUrgentOfferPopUp, short notOfferEditPopUp, bool canManageOffers)
        {
            var productSubGroups = ProductSubGroupsService.Get(companyCredentials.PortalId);
            var conversations = new List<Master.Entities.Message.ConversationEntity>();
            var prodCWConvertToComplete = _landingProductsService.ManageCWFeatures((int)ProductAmbitEnum.CVsFreemium, companyProduct.IdCompany, portalConfig);

            if (portalConfig.has_chat == (int)PortalConfigStatusEnum.Activated && companyProduct.Features.Any(f => f.AmbitId == (int)ProductAmbitEnum.MessagesMailing))
            {
                conversations = await _messageJobdsChatService.GetFirstConversationByOffersListWithCacheAsync(new ConversationFilter() { IdCompany = companyCredentials.IdCompany, IdPortal = portalConfig.PortalId, IdUserCompany = companyCredentials.UserId }, offers.Select(o => o.idoffer).ToList(), portalConfig.idapp);
            }

            List<OfferMatchesSummarySubscriptionEntity> currentActiveMatchSummaryNotificationFromUser = null;
            List<SelectListItem> matchSummarySelect = SetMatchSummarySelectgList(portalConfig);
            List<SelectListItem> matchOfferMailSettingsStatusSelect = SetMatchOfferMailSettingsStatusSelectList(portalConfig);
            List<SelectListItem> matchOfferMailSettingsStatusExpiredSelect = SetMatchOfferMailSettingsStatusExpiredSelectList(portalConfig);
            List<OfferCvCounterEntity> cvCountersByOffers = new List<OfferCvCounterEntity>();
            if (portalConfig.AEPortalConfig.ActivateCompanyCvsConsumible &&
               companyProduct.GroupId != (short)ProductGroupsEnum.Membership)
            {
                cvCountersByOffers = _offerCvCountersService.GetCounterByIdOffers(string.Join(",", offers.Select(x => x.idofferCt).ToList()), companyProduct.PortalId);
            }

            var companyActiveProducts = CompanyProductService.GetAllCompanyActiveProducts(companyCredentials.IdCompany, companyCredentials.PortalId);

            offers.ForEach(o =>
            {
                CompanyProductEntity companyProductOffer;

                if (companyProduct.GroupId != (short)ProductGroupsEnum.Membership)
                    companyProductOffer = CompanyProductService.GetByCompanyProductId(o.idcompanyproduct, portalConfig.PortalId, companyCredentials.IdCompany);
                else
                {
                    companyProductOffer = companyProduct;
                    o.HasConversations = conversations.Any(c => c.IdOfferCT == o.idofferCt);
                }

                o.NotHighlightOfferPopUp = notHighlightOfferPopUp;
                o.NotUrgentOfferPopUp = notUrgentOfferPopUp;
                o.NotOfferEditPopUp = notOfferEditPopUp;

                var offerHighLightedFeature = companyProductOffer.Features.FirstOrDefault(f => f.AmbitId == (int)ProductAmbitEnum.OfferHighlighted);
                var offerUrgentFeature = companyProductOffer.Features.FirstOrDefault(f => f.AmbitId == (int)ProductAmbitEnum.OfferUrgent);

                o.CanDuplicate = CanDuplicateOffer(companyActiveProducts, companyCredentials, canManageOffers, o);

                o.CanEdit = CompanyProductService.CanEdit(new IsAllowedToEditOfferDTO()
                {
                    OfferExpirationTime = o.expirationtime,
                    IdOfferStatus = o.idofferstatus,
                    CompanyProductOfferGroupId = companyProductOffer.GroupId,
                    CompanyProductGroupId = companyProduct.GroupId,
                    CanEditPermitedByRol = canManageOffers,
                    BlockOldMembership = portalConfig.blockOldMembership
                });

                o.CompanyProductOfferDataModel = new CompanyProductOfferDataModel()
                {
                    Id = companyProductOffer.Id,
                    GroupId = companyProductOffer.GroupId,
                    SubGroupId = companyProductOffer.SubGroupId,
                    Title = productSubGroups.Find(x => x.Id == companyProductOffer.SubGroupId)?.Name ?? PageLiteralsHelper.GetLiteral("LIT_BORRADOR", (int)PageEnum.OfferList, portalConfig).ToUpper()
                };

                o.CanDelete = o.createdby == (int)companyCredentials.UserId ? true : canDelete;
                o.ShowPopUpConfirmConvertComplete = companyProduct.GroupId == (short)ProductGroupsEnum.Packs
                                                    && companyProduct.Features.Exists(f => f.AmbitId == (short)ProductAmbitEnum.Offer)
                                                    && (companyProduct.Features.FirstOrDefault(f => f.AmbitId == (short)ProductAmbitEnum.Offer)?.AvailableUnits ?? 0) > 0;
                o.HasHighLightedOfferFeature = offerHighLightedFeature != null;
                o.IsRecurrentHighLightedOfferFeature = offerHighLightedFeature != null && offerHighLightedFeature.IsRecurrent;
                o.HasUrgentOfferFeature = offerUrgentFeature != null;
                o.IsRecurrentUrgentOfferFeature = offerUrgentFeature != null && offerUrgentFeature.IsRecurrent;
                o.CreatedByUser = _userService.GetContactNameByUserId(o.createdby, portalConfig.PortalId);
                o.FrequencyRenewDays = companyProductOffer.Features.FirstOrDefault(x => x.AmbitId == (short)ProductAmbitEnum.OfferUp)?.FrequencyRenewDays ?? 0;
                o.RenewOfferActionSpan = OfferService.GetRenewOfferSpan(offersEntities.FirstOrDefault(oe => oe.idoffer == o.idoffer), portalConfig, companyProductOffer, PageId, OfferIntegratorEnum.CompuTrabajo);
                o.CanManageOffers = canManageOffers;
                o.HasQRCode = IsQrProductFeatureActive(companyProductOffer);
                o.HasOfferMatchesNotification = o.idstatus != (int)OfferStatusEnum.Borrador ? IsOfferMatchesNotificationActive(companyProductOffer) : false;
                o.HasFeatureMatchesMailForOffer = o.idstatus != (int)OfferStatusEnum.Borrador ? IsMatchesOfferMailSettings(companyProductOffer, companyCredentials, portalConfig) : false;
                o.MatchSummaryNotificationTypes = matchSummarySelect;
                o.MatchesOfferMailSettingsStatus = o.IdStatusMatchesOfferMailSettings == (short)MatchesOfferMailSettingsStatusEnum.Expired ? matchOfferMailSettingsStatusExpiredSelect : matchOfferMailSettingsStatusSelect;

                if (currentActiveMatchSummaryNotificationFromUser == null && CompanyProductService.HasFeature(ProductAmbitEnum.OfferMatchesNotification, companyProductOffer))
                {
                    currentActiveMatchSummaryNotificationFromUser = _companyTrackingService.GetMatchSummaryNotificationsFromUserAndCompany((int)companyCredentials.UserId, companyCredentials.IdCompany, portalConfig.PortalId);
                }

                var matchSummaryOffer = currentActiveMatchSummaryNotificationFromUser?.FirstOrDefault(x => x.IdOffer == o.idoffer);

                if (matchSummaryOffer != null)
                {
                    o.IdMatchSummaryNotificationType = matchSummaryOffer.IdStatus == StatusEnum.Deactivated
                    ? (short)0
                    : (short)matchSummaryOffer.IdType;
                }

                if (o.HasFeatureMatchesMailForOffer)
                {
                    o.IdStatusMatchesOfferMailSettings = GetIdStatusMatchesOfferMailSettings(o.idoffer, companyCredentials.IdCompany, portalConfig.PortalId);
                }

                if (o.CompanyProductOfferDataModel.GroupId == (short)ProductGroupsEnum.Freemium)
                {
                    o.ProdCWConvertToComplete = prodCWConvertToComplete;
                    o.BtnConvertToCompleteEncryptedName = _encryptionService.Encrypt(((int)TpvButtonOriginEnum.OfferListConvertToPack).ToString());
                }

                if (!portalConfig.AEPortalConfig.UseBBDDcvProductForOffers &&
                    portalConfig.AEPortalConfig.ActivateCompanyCvsConsumible &&
                    o.CompanyProductOfferDataModel.GroupId != (short)ProductGroupsEnum.Membership)
                {
                    var canFindBBDDcvs = companyProductOffer.Features.FirstOrDefault(f => f.AmbitId == (int)ProductAmbitEnum.CvBBDD) != null;
                    if (canFindBBDDcvs)
                    {
                        o.OfferPackAndBasicDataModel.CanFindBBDDcvs = canFindBBDDcvs;
                        var bbddcvConsumedUnits = (short)(cvCountersByOffers.FirstOrDefault(x => x.IdOffer == o.idofferCt)?.UnitsConsumed ?? 0);
                        o.OfferPackAndBasicDataModel.BBDDcvAvailableUnits = (companyProductOffer.Features.FirstOrDefault(f => f.AmbitId == (short)ProductAmbitEnum.CvBBDD)?.InitialUnits ?? 0) - bbddcvConsumedUnits;
                        o.OfferPackAndBasicDataModel.BBDDcvConsumedUnits = bbddcvConsumedUnits;
                        o.OfferPackAndBasicDataModel.BBDDcvFolderEncrypted = _encryptionService.Encrypt(((short)CompanyOfferFolderEnum.BBDD).ToString());
                        o.OfferPackAndBasicDataModel.BBDDcvInitialUnits = companyProductOffer.Features.FirstOrDefault(f => f.AmbitId == (short)ProductAmbitEnum.CvBBDD)?.InitialUnits ?? 0;
                    }
                }

                o.QrCodeName = string.Join("_", new[] { "QR", o.title, companyCredentials.CompanyName, o.createdon.Year.ToString() }).Replace(" ", "_");

                var offer = OfferService.GetByPk(o.idoffer, portalConfig.PortalId);
                o.OfferPublicUrl = portalConfig.url_web + offer.Integrations.Single(x => x.IdIntegrator == (int)OfferIntegratorEnum.CompuTrabajo).urlrewrite;
            });
        }

        private short CanDuplicateOffer(List<CompanyProductEntity> companyActiveProducts, CompanyCredentials companyCredentials, bool canManageOffers, OfferDataModel offer)
        {
            var activeProductsWithDuplicateOffers = companyActiveProducts.Any(x => x.Features.Any(f => f.AmbitId == (int)ProductAmbitEnum.TemplateOffer));

            if (companyActiveProducts.Any(x => x.GroupId == (short)ProductGroupsEnum.Membership))
            {
                if (!activeProductsWithDuplicateOffers || !canManageOffers)
                    return (short)CanDuplicateOfferEnum.NotHaveFeature;
                else if (!CompanyProductService.CanDoActionByFeature(companyCredentials.IdCompany, companyCredentials.PortalId, (short)ProductAmbitEnum.Offer, 1, companyCredentials.UserId, OfferIntegratorEnum.CompuTrabajo) || offer.idofferstatus == (int)OfferStatusEnum.Rechazada)
                    return (short)CanDuplicateOfferEnum.NotHaveUnits;
                else
                    return (short)CanDuplicateOfferEnum.Yes;
            }
            else
            {
                var productWithDuplicateOffers = companyActiveProducts
                    .Any(x => x.Features.Any(f => f.AmbitId == (int)ProductAmbitEnum.TemplateOffer) &&
                         x.Features.Any(f => f.AmbitId == (int)ProductAmbitEnum.Offer && f.AvailableUnits > 0));

                if (!productWithDuplicateOffers || !canManageOffers || offer.idofferstatus == (int)OfferStatusEnum.Rechazada)
                    return (short)CanDuplicateOfferEnum.NotHaveFeature;
                else
                    return (short)CanDuplicateOfferEnum.Yes;
            }
        }

        private List<SelectListItem> SetMatchSummarySelectgList(PortalConfig portalConfig)
        {
            return new List<SelectListItem>
            {
                new SelectListItem() { Value = ((short)MatchSummaryNotificationType.None).ToString(), Text = PageLiteralsHelper.GetLiteral("LIT_NONE", PageId, portalConfig) },
                new SelectListItem() { Value = ((short)MatchSummaryNotificationType.Daily).ToString(), Text = PageLiteralsHelper.GetLiteral("LIT_DAILY", PageId, portalConfig) },
                new SelectListItem() { Value = ((short)MatchSummaryNotificationType.Weekly).ToString(), Text = PageLiteralsHelper.GetLiteral("LIT_WEEKLY", PageId, portalConfig) },
            };
        }

        private List<SelectListItem> SetMatchOfferMailSettingsStatusSelectList(PortalConfig portalConfig)
        {
            return new List<SelectListItem>
            {
                new SelectListItem() { Value = ((short)MatchesOfferMailSettingsStatusEnum.Disabled).ToString(), Text = PageLiteralsHelper.GetLiteral("LIT_DISABLED", PageId, portalConfig) },
                new SelectListItem() { Value = ((short)MatchesOfferMailSettingsStatusEnum.Enabled).ToString(), Text = PageLiteralsHelper.GetLiteral("LIT_ENABLED", PageId, portalConfig) },
            };
        }

        private List<SelectListItem> SetMatchOfferMailSettingsStatusExpiredSelectList(PortalConfig portalConfig)
        {
            return new List<SelectListItem>
            {
                new SelectListItem() { Value = ((short)MatchesOfferMailSettingsStatusEnum.Expired).ToString(), Text = PageLiteralsHelper.GetLiteral("LIT_DISABLED", PageId, portalConfig) },
            };
        }

        private short GetIdStatusMatchesOfferMailSettings(int idOffer, int idCompany, short idPortal)
        {
            var matchesOfferMailSettingsDTO = _companyTrackingService.GetMatchesOfferMailSettings(idOffer, idCompany, idPortal);
            if (matchesOfferMailSettingsDTO != null && matchesOfferMailSettingsDTO.Id > 0)
            {
                return matchesOfferMailSettingsDTO.Status;
            }
            return (short)MatchesOfferMailSettingsStatusEnum.Disabled;
        }

        private bool IsQrProductFeatureActive(CompanyProductEntity companyProduct)
        {
            return _companyProductService.HasFeature(ProductAmbitEnum.QR, companyProduct);
        }

        private bool IsOfferMatchesNotificationActive(CompanyProductEntity companyProduct)
        {
            return _companyProductService.HasFeature(ProductAmbitEnum.OfferMatchesNotification, companyProduct);
        }

        private bool IsMatchesOfferMailSettings(CompanyProductEntity companyProductOffer, CompanyCredentials companyCredentials, PortalConfig portalConfig)
        {
            return _companyProductService.HasFeature(ProductAmbitEnum.MatchesMailForOffer, companyProductOffer)
                && portalConfig.SendMailsMatchesForOffers
                && companyCredentials.TestAB == TestABEnum.A;
        }

        private void PopulateFilterList(HomeOfferDataModel homeOfferDataModel, PortalConfig portalConfig, List<UserEntity> usersVisible, CompanyProductEntity companyProduct)
        {
            homeOfferDataModel.OfferActions = GetOfferActions(portalConfig, companyProduct.GroupId, homeOfferDataModel.CanDelete);
            homeOfferDataModel.OfferOrders = GetOfferOrders(portalConfig, homeOfferDataModel.HasNewMatchesNotificationsFeature);
            homeOfferDataModel.Pager.PageSizes = GetFromDictionaryDependantKey(portalConfig, DictionaryEnum.DIC_DROPDOWN_LIST, dependantKey: (short)DropDownListEnum.ddlOffersByPage);
            homeOfferDataModel.OfferStatus = GetOfferStatus(portalConfig);
            homeOfferDataModel.ShowManagers = companyProduct.GroupId == (short)ProductGroupsEnum.Membership
                                              && (SecurityHelper.IsActionPemitedByRole(SecurityActionEnum.CompanyOffersManageByAllUsers) || SecurityHelper.IsActionPemitedByRole(SecurityActionEnum.CompanyOffersManageLessThanYoursRol));
            homeOfferDataModel.Managers = companyProduct.GroupId == (short)ProductGroupsEnum.Membership ? GetCompanyManagers(usersVisible, homeOfferDataModel) : new List<SelectListItem>();
            var dicCateogries = GetFromDictionary(portalConfig, DictionaryEnum.CATEGORY);
            homeOfferDataModel.ProfesionalCategories = dicCateogries.OrderByDescending(s => homeOfferDataModel.SelectedProfesionalCategories.Contains(s.Value.ToInt()))
                .ToList();
        }

        private List<SelectListItem> GetCompanyManagers(List<UserEntity> usersVisible, HomeOfferDataModel homeOfferDataModel)
        {
            List<SelectListItem> companyManagers = new List<SelectListItem>();
            usersVisible.Where(u => homeOfferDataModel.SelectedManagers.Contains(u.IdEncrypt))
                .ToList()
                .ForEach(m => companyManagers.Add(new SelectListItem() { Value = m.IdEncrypt, Text = m.ContactName }));
            usersVisible.Where(u => !homeOfferDataModel.SelectedManagers.Contains(u.IdEncrypt))
                .ToList()
                .ForEach(m => companyManagers.Add(new SelectListItem() { Value = m.IdEncrypt, Text = m.ContactName }));

            return companyManagers;
        }

        private List<long> GetListUsersId(List<UserEntity> usersEntity)
        {
            List<long> usersId = new List<long>();
            usersEntity.ForEach(m => usersId.Add(m.Id));

            return usersId;
        }

        private List<UserEntity> GetUsersVisible(int companyId, CompanyCredentials companyCredentials)
        {
            if (SecurityHelper.IsActionPemitedByRole(SecurityActionEnum.CompanyOffersManageByAllUsers))
                return _userService.GetManagersByIdCompany(companyId, companyCredentials.PortalId).OrderBy(n => n.ContactName).ToList();

            if (SecurityHelper.IsActionPemitedByRole(SecurityActionEnum.CompanyOffersManageLessThanYoursRol))
                return _userService.GetManagersByDescendentRol(companyId, companyCredentials.UserRole, companyCredentials.PortalId);

            return new List<UserEntity>();
        }

        private List<SelectListItem> GetOfferStatus(PortalConfig portalConfig)
        {
            var offerStates = new List<SelectListItem>
            {
                new SelectListItem() { Value = ((int)OfferStatusEnum.Activa).ToString(), Text = PageLiteralsHelper.GetLiteral("DD_FILTRAR_ACTIVAS", PageId, portalConfig) },
                new SelectListItem() { Value = ((int)OfferStatusEnum.Vencida).ToString(), Text = PageLiteralsHelper.GetLiteral("DD_FILTRAR_VENCIDAS", PageId, portalConfig) },
                new SelectListItem() { Value = ((int)OfferStatusEnum.Desactivada).ToString(), Text = PageLiteralsHelper.GetLiteral("DD_FILTRAR_ARCHIVADAS", PageId, portalConfig) },
                new SelectListItem() { Value = ((int)OfferStatusEnum.Rechazada).ToString(), Text = PageLiteralsHelper.GetLiteral("DD_FILTRAR_RECHAZADAS", PageId, portalConfig) }
            };

            return offerStates;
        }

        private List<SelectListItem> GetOfferActions(PortalConfig portalConfig, short groupId, bool CanDelete)
        {
            var offerStates = new List<SelectListItem>
            {
                new SelectListItem() { Value = "0", Text = PageLiteralsHelper.GetLiteral("DD_SELECCIONA_ACCION", PageId, portalConfig) },
            };

            if (groupId == (short)ProductGroupsEnum.Membership)
            {
                offerStates.Add(new SelectListItem() { Value = "2", Text = PageLiteralsHelper.GetLiteral("DD_ACTIVAR", PageId, portalConfig) });
                offerStates.Add(new SelectListItem() { Value = "6", Text = PageLiteralsHelper.GetLiteral("DD_ARCHIVAR", PageId, portalConfig) });
                offerStates.Add(new SelectListItem() { Value = "1", Text = PageLiteralsHelper.GetLiteral("DD_ACTUALIAR", PageId, portalConfig) });
            }

            if (CanDelete)
                offerStates.Add(new SelectListItem() { Value = "4", Text = PageLiteralsHelper.GetLiteral("DD_ELIMINAR", PageId, portalConfig) });

            return offerStates;
        }

        private List<SelectListItem> GetOfferOrders(PortalConfig portalConfig, bool hasNewMatchesNotificationsFeature)
        {
            var offerOrders = new List<SelectListItem>
            {
                new SelectListItem() { Value = ((int)OrderOfferByEnum.Default).ToString(), Text = PageLiteralsHelper.GetLiteral("DD_NEW_FIRST", PageId, portalConfig) },
                new SelectListItem() { Value = ((int)OrderOfferByEnum.ByPublicationAsc).ToString(), Text = PageLiteralsHelper.GetLiteral("DD_OLD_FIRST", PageId, portalConfig)  },
                new SelectListItem() { Value = ((int)OrderOfferByEnum.ByTitleAsc).ToString(), Text = PageLiteralsHelper.GetLiteral("DD_TITLE_ASC", PageId, portalConfig) },
                new SelectListItem() { Value = ((int)OrderOfferByEnum.ByTitleDesc).ToString(), Text = PageLiteralsHelper.GetLiteral("DD_TITLE_DESC", PageId, portalConfig)  },
                new SelectListItem() { Value = ((int)OrderOfferByEnum.ByExpirationAsc).ToString(), Text = PageLiteralsHelper.GetLiteral("DD_DATEEXPIRATION_ASC", PageId, portalConfig)  },
                new SelectListItem() { Value = ((int)OrderOfferByEnum.ByExpirationDesc).ToString(), Text = PageLiteralsHelper.GetLiteral("DD_DATEEXPIRATION_DESC", PageId, portalConfig) },
                new SelectListItem() { Value = ((int)OrderOfferByEnum.ByInscritosAsc).ToString(), Text = PageLiteralsHelper.GetLiteral("DD_APPLIES_ASC", PageId, portalConfig) },
                new SelectListItem() { Value = ((int)OrderOfferByEnum.ByInscritosDesc).ToString(), Text = PageLiteralsHelper.GetLiteral("DD_APPLIES_DESC", PageId, portalConfig)  },
                new SelectListItem() { Value = ((int)OrderOfferByEnum.ByLastUpdateAsc).ToString(), Text = PageLiteralsHelper.GetLiteral("DD_LASTUPDATE_ASC", PageId, portalConfig)  },
                new SelectListItem() { Value = ((int)OrderOfferByEnum.ByLastUpdateDesc).ToString(), Text = PageLiteralsHelper.GetLiteral("DD_LASTUPDATE_DESC", PageId, portalConfig)  }

            };

            if (ConfigurationManager.AppSettings["ShowTotalNotViewed"] != null && ConfigurationManager.AppSettings["ShowTotalNotViewed"] == "true")
            {
                offerOrders.Add(new SelectListItem() { Value = ((int)OrderOfferByEnum.ByNotViewedAppliesAsc).ToString(), Text = PageLiteralsHelper.GetLiteral("DD_NOTVIEWED_ASC", PageId, portalConfig) });
                offerOrders.Add(new SelectListItem() { Value = ((int)OrderOfferByEnum.ByNotViewedAppliesDesc).ToString(), Text = PageLiteralsHelper.GetLiteral("DD_NOTVIEWED_DESC", PageId, portalConfig) });
            }

            if(hasNewMatchesNotificationsFeature)
            {
                offerOrders.Add(new SelectListItem() { Value = ((int)OrderOfferByEnum.ByNewAppliesAsc).ToString(), Text = PageLiteralsHelper.GetLiteral("DD_NEWAPPLIES_ASC", PageId, portalConfig) });
                offerOrders.Add(new SelectListItem() { Value = ((int)OrderOfferByEnum.ByNewAppliesDesc).ToString(), Text = PageLiteralsHelper.GetLiteral("DD_NEWAPPLIES_DESC", PageId, portalConfig) });
            }

            return offerOrders;
        }

        private List<int> GetOffersIds(HomeOfferDataModel homeOfferDataModel, PortalConfig portalConfig, CompanyCredentials companyCredentials)
        {
            var userIds = new List<long>();

            if (SecurityHelper.IsActionPemitedByRole(SecurityActionEnum.CompanyOffersManageByAllUsers) ||
                SecurityHelper.IsActionPemitedByRole(SecurityActionEnum.CompanyOffersManageLessThanYoursRol))
            {
                homeOfferDataModel.SelectedManagers.ForEach(m =>
                {
                    userIds.Add(_encryptionService.Decrypt(m).ToLong());
                });
            }
            else
            {
                userIds.Add(companyCredentials.UserId);
            }

            var offerIds = new List<int>();

            if (ConfigurationManager.AppSettings["ACTIVE_FIX_SEARCH_OFFER"] != null && ConfigurationManager.AppSettings["ACTIVE_FIX_SEARCH_OFFER"].ToLower() == "true")
            {
                offerIds = OfferService.GetApiOffersIds(new OfferElasticFilter()
                {
                    IdCompany = companyCredentials.IdCompany,
                    IdPortal = companyCredentials.PortalId,
                    TitleKeyWord = homeOfferDataModel.SearchTitle,
                    CurrentUser = companyCredentials.UserId,
                    IdRolCurrentUser = (int)companyCredentials.UserRole,
                    QuerySize = QUERY_SIZE,
                    PageNumber = 1
                }, portalConfig);
            }
            else
            {
                offerIds = OfferService.ApiOffersEmpresaGetIds(new OfferSuggestElasticFilter()
                {
                    IdCompany = companyCredentials.IdCompany,
                    IdPortal = companyCredentials.PortalId,
                    Title = homeOfferDataModel.SearchTitle,
                    MaxSuggesterResults = 9999,
                    IdUser = EncryptationHelper.Encrypt(companyCredentials.UserId.ToString()),
                    IdRole = EncryptationHelper.Encrypt(((short)companyCredentials.UserRole).ToString())
                }, _isNewSuggest);
            }
            return offerIds;
        }

        private PopUpDataModel GetSelectOfferPopUpDataModel(PortalConfig portalConfig)
        {
            return new PopUpDataModel()
            {
                Title = PageLiteralsHelper.GetLiteral("LIT_INFO", PageId, portalConfig),
                Message = PageLiteralsHelper.GetLiteral("LIT_SELECT_OFFER_ALERT", PageId, portalConfig),
                TitleBtnOk = string.Empty,
                TitleBtnKo = PageLiteralsHelper.GetLiteral("BTN_CANCELAR", PageId, portalConfig),
                LiteralProcessing = string.Empty,
                LiteralError = string.Empty,
                HasInput = false,
                HasButtonOk = false,

            };
        }

        private PopUpDataModel GetDeleteOfferPopUp(PortalConfig portalConfig)
        {
            return new PopUpDataModel()
            {
                Title = PageLiteralsHelper.GetLiteral("H3_ELIMINAR", PageId, portalConfig),
                Message = PageLiteralsHelper.GetLiteral("P_PREGUNTA_ELIMINAR", PageId, portalConfig),
                TitleBtnOk = PageLiteralsHelper.GetLiteral("BTN_ELIMINAR", PageId, portalConfig),
                TitleBtnKo = PageLiteralsHelper.GetLiteral("BTN_CANCELAR", PageId, portalConfig),
                HasInput = false,
                HasButtonOk = true
            };
        }

        private OfferConvertTraceMatchesMailDTO GetOfferConvertTraceMatchesMailDTO(PortalConfig portalConfig, CompanyCredentials companyCredentials)
        {
            OfferConvertTraceMatchesMailDTO offerConvertTraceMatchesMailDTO = new OfferConvertTraceMatchesMailDTO();
            if (portalConfig.SendMailsMatchesForOffers)
            {
                var companyActiveProducts = CompanyProductService.GetAllCompanyActiveProducts(companyCredentials.IdCompany, companyCredentials.PortalId);
                var companyProductFreemium = companyActiveProducts.Find(x => x.GroupId == (short)ProductGroupsEnum.Freemium && x.SubGroupId == (short)ProductSubGroupsEnum.Freemium);

                offerConvertTraceMatchesMailDTO.TestABEnum = TestABEnum.A;
                offerConvertTraceMatchesMailDTO.HasMatchesMailForOffer = companyProductFreemium == null ? false : companyProductFreemium.Features.Where(y => y.AmbitId == (int)ProductAmbitEnum.MatchesMailForOffer)?.Any() ?? false;
                offerConvertTraceMatchesMailDTO.ConsumedMails = _companyProductService.GetAvailableUnitsMailMatchForOffer(companyProductFreemium);
            }

            return offerConvertTraceMatchesMailDTO;
        }

        private HomeOfferSuggestFilterModel GetHomeOfferSuggestFilterFromClient(string homeOfferSuggestFilterModelJson)
        {
            JavaScriptSerializer js = new JavaScriptSerializer();
            HomeOfferSuggestFilterModel homeOfferSuggestFilterModel = js.Deserialize<HomeOfferSuggestFilterModel>(homeOfferSuggestFilterModelJson);

            return homeOfferSuggestFilterModel;
        }

        [HttpPost]
        public JsonResult ajaxManagePopUpConvertToCompleteBtt()
        {
            string returnVal = string.Empty;

            var companyCredentials = SecurityHelper.GetCompanyCredentials();
            var portalConfig = PortalConfigurationService.GetPortalConfiguration();

            var companyActiveProducts = CompanyProductService.GetAllCompanyActiveProducts(companyCredentials.IdCompany, companyCredentials.PortalId);
            var productsWithFeature = companyActiveProducts.Where(x => x.Features.Exists(y => y.AmbitId == (int)ProductAmbitEnum.Offer) && x.GroupId == (short)ProductGroupsEnum.Packs).ToList();
            var availableUnits = productsWithFeature.Sum(x => x.Features.Find(y => y.AmbitId == (int)ProductAmbitEnum.Offer).AvailableUnits);

            if (availableUnits > 0)
            {
                if (!portalConfig.AEPortalConfig.PostPublishWithConsumables)
                {
                    return Json(3);
                }
                else
                {
                    if (portalConfig.AEPortalConfig.PostPublishWithConsumables)
                    {
                        return Json(1);
                    }
                    else
                    {
                        return Json(4);
                    }
                }
            }
            else
            {
                return Json(2);
            }
        }

        [HttpPost]
        public JsonResult ManageChangeOfferVisibility(string idOffer, int idAmbit, int isCheck)
        {
            if (isCheck == 0)
            {
                return Json((short)ChangeOfferVisibilityPopUpEnum.ShowPopUpUncheckVisibility);
            }
            string returnValue = ChangeOfferVisibilityPopUpEnum.ShowPopUpGoToCart.ToString();
            var companyCredentials = SecurityHelper.GetCompanyCredentials();

            var offerBBDD = OfferService.GetByPk(idOffer, companyCredentials.PortalId);
            var offerProduct = _companyProductService.GetByCompanyProductId(offerBBDD.Integrations.First().idcompanyproduct, companyCredentials.PortalId, companyCredentials.IdCompany);

            if (offerProduct.Features.Exists(x => x.AmbitId == idAmbit))
            {
                if (offerProduct.GroupId == (short)ProductGroupsEnum.Membership)
                {
                    return Json((short)ChangeOfferVisibilityPopUpEnum.ShowPopUpChangeConsumeCredit);
                }
                if (offerProduct.GroupId == (short)ProductGroupsEnum.Packs)
                {
                    return Json((short)ChangeOfferVisibilityPopUpEnum.ShowPopUChangeWithoutConsumeCredit);
                }
            }
            else
            {
                if (offerProduct.GroupId == (short)ProductGroupsEnum.Membership ||
                    offerProduct.SubGroupId == (short)ProductSubGroupsEnum.Standard ||
                    offerProduct.SubGroupId == (short)ProductSubGroupsEnum.Advanced ||
                    offerProduct.SubGroupId == (short)ProductSubGroupsEnum.Premium)
                {
                    return Json((short)ChangeOfferVisibilityPopUpEnum.ShowPopUpDontHaveFeature);
                }

                var portalConfig = PortalConfigurationService.GetPortalConfiguration();
                if (portalConfig.AEPortalConfig.PostPublishWithConsumables)
                {
                    var companyActiveProducts = CompanyProductService.GetAllCompanyActiveProducts(companyCredentials.IdCompany, companyCredentials.PortalId);
                    var productsWithFeature = companyActiveProducts.Where(x => x.Features.Exists(y => y.AmbitId == idAmbit && x.GroupId == (short)ProductGroupsEnum.Consumibles)).ToList();
                    var availableUnits = productsWithFeature.Sum(x => x.Features.Find(y => y.AmbitId == idAmbit).AvailableUnits);

                    if (availableUnits > 0)
                    {
                        return Json((short)ChangeOfferVisibilityPopUpEnum.ShowPopUpChangeConsumeCredit);
                    }
                    else
                    {
                        return Json((short)ChangeOfferVisibilityPopUpEnum.ShowPopUpGoToCart);
                    }
                }
                else if (offerProduct.GroupId == (short)ProductGroupsEnum.Freemium)
                {
                    return Json((short)ChangeOfferVisibilityPopUpEnum.ShowPopUpGoToCart);
                }
                else if (offerProduct.GroupId == (short)ProductGroupsEnum.Packs)
                {
                    return Json((short)ChangeOfferVisibilityPopUpEnum.ShowPopUpDontHaveFeature);
                }
            }

            return Json(returnValue);
        }

        [HttpPost]
        public JsonResult InsertOrUpdateMatchSummaryNotification(string idOffer, MatchSummaryNotificationType type, bool isDisabled)
        {
            var companyCredentials = SecurityHelper.GetCompanyCredentials();
            try
            {
                //Prevent unxepected calls
                var offer = OfferService.GetByPk(idOffer, companyCredentials.PortalId);
                var offerProduct = _companyProductService.GetByCompanyProductId(offer.Integrations.First().idcompanyproduct, offer.idportal, offer.idcompany);
                var productClass = _companyProductService.SetProductClassByIdGroup(offerProduct.GroupId);
                var offerMatchesNotification = _companyProductService.GetByCompanyProductId(offer.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).idcompanyproduct, offer.idportal, offer.idcompany);
                if (!IsOfferMatchesNotificationActive(offerMatchesNotification))
                    return Json(false);


                _companyTrackingService.AeUpsertMatchesSummarySubscription(offer.idoffer, companyCredentials.IdCompany, companyCredentials.PortalId, companyCredentials.UserId, type, isDisabled);

                _offerMatchesNotificationKpiService.AddOfferMatchNotificationKpi(offer, companyCredentials.UserId, (ProductClassEnum)productClass, type, isDisabled);

            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyOffersController InsertOrUpdateMatchSummaryNotification {ex}");
                Dictionary<string, string> extradata = new Dictionary<string, string>
                {
                    { "Idcompany", companyCredentials?.IdCompany.ToString() },
                    { "IdPortal", companyCredentials?.PortalId.ToString() },
                    { "CompanyName", companyCredentials?.CompanyName.ToString() },
                    { "UserRole", companyCredentials?.UserRole.ToString() },
                    { "UserId", companyCredentials?.UserId.ToString() },
                    { "UserName", companyCredentials?.Username.ToString() }
                };

                ExceptionPublisherService.Publish(ex, "CompanyOffersController", "InsertOrUpdateMatchSummaryNotification", false, extradata);
                return Json(false);
            }
            return Json(true);

        }

        [HttpPost]
        public JsonResult UpdateMatchesOfferMailSettings(string idOffer, short idStatus)
        {
            var companyCredentials = SecurityHelper.GetCompanyCredentials();
            var portalConfig = PortalConfigurationService.GetPortalConfiguration();
            try
            {
                //Prevent unxepected calls
                var offer = OfferService.GetByPk(idOffer, portalConfig.PortalId);
                var offerProduct = _companyProductService.GetByCompanyProductId(offer.Integrations.First().idcompanyproduct, offer.idportal, offer.idcompany);

                if (!IsMatchesOfferMailSettings(offerProduct, companyCredentials, portalConfig))
                {
                    return Json(false, JsonRequestBehavior.AllowGet);
                }

                var matchesOfferMailSettingsDTO = _companyTrackingService.GetMatchesOfferMailSettings(offer.idoffer, offer.idcompany, offer.idportal);
                if (matchesOfferMailSettingsDTO != null && matchesOfferMailSettingsDTO.Id > 0)
                {
                    matchesOfferMailSettingsDTO.Status = idStatus;
                    matchesOfferMailSettingsDTO.IdUser = companyCredentials.UserId;
                    if (_companyTrackingService.UpdateStatusByUserMatchesOfferMailSettings(matchesOfferMailSettingsDTO))
                    {
                        var kpistatus = matchesOfferMailSettingsDTO.Status == (short)MatchesOfferMailSettingsStatusEnum.Disabled ? (short)KpiEnum.CT_AE_MATCHES_MAIL_DISABLED_FROM_OFFER_LIST : (short)KpiEnum.CT_AE_MATCHES_MAIL_ENABLED_FROM_OFFER_LIST;
                        _kpiService.Add(kpistatus, portalConfig.PortalId);
                    }
                }
                else
                {
                    MatchesOfferMailSettingsDTO matchesOfferMailSettingsDTOAdd = new MatchesOfferMailSettingsDTO()
                    {
                        IdOffer = offer.idoffer,
                        IdCompany = offer.idcompany,
                        IdPortal = offer.idportal,
                        IdUser = companyCredentials.UserId,
                        Status = idStatus,
                        AvailableMails = _companyProductService.GetAvailableUnitsMailMatchForOffer(offerProduct)
                    };
                    _companyTrackingService.AddMatchesOfferMailSettings(matchesOfferMailSettingsDTOAdd);
                    _kpiService.Add((short)KpiEnum.CT_AE_MATCHES_MAIL_ENABLED_FROM_OFFER_LIST, portalConfig.PortalId);
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyOffersController UpdateMatchesOfferMailSettings {ex}");
                Dictionary<string, string> extradata = new Dictionary<string, string>
                {
                    { "Idcompany", companyCredentials?.IdCompany.ToString() },
                    { "IdPortal", companyCredentials?.PortalId.ToString() },
                    { "CompanyName", companyCredentials?.CompanyName.ToString() },
                    { "UserRole", companyCredentials?.UserRole.ToString() },
                    { "UserId", companyCredentials?.UserId.ToString() },
                    { "UserName", companyCredentials?.Username.ToString() }
                };

                ExceptionPublisherService.Publish(ex, "CompanyOffersController", "UpdateMatchesOfferMailSettings", false, extradata);
                return Json(false, JsonRequestBehavior.AllowGet);
            }
            return Json(true, JsonRequestBehavior.AllowGet);
        }

        [HttpPost]
        public JsonResult IncreaseKpiRSS()
        {
            var portalConfig = PortalConfigurationService.GetPortalConfiguration();
            try
            {
                _kpiService.Add((short)KpiEnum.CT_OFFER_RSS_BUTTON_CLICK_EVENT, portalConfig.PortalId);
            }
            catch (Exception ex)
            {
                var companyCredentials = SecurityHelper.GetCompanyCredentials();
                Trace.TraceError($"CompanyOffersController IncreaseKpiRSS {ex}");
                Dictionary<string, string> extradata = new Dictionary<string, string>
                {
                    { "Idcompany", companyCredentials?.IdCompany.ToString() },
                    { "IdPortal", companyCredentials?.PortalId.ToString() },
                    { "CompanyName", companyCredentials?.CompanyName.ToString() },
                    { "UserRole", companyCredentials?.UserRole.ToString() },
                    { "UserId", companyCredentials?.UserId.ToString() },
                    { "UserName", companyCredentials?.Username.ToString() }
                };

                ExceptionPublisherService.Publish(ex, "CompanyOffersController", "IncreaseKpiRSS", false, extradata);
                return Json(false, JsonRequestBehavior.AllowGet);
            }
            return Json(true, JsonRequestBehavior.AllowGet);
        }
    }
}