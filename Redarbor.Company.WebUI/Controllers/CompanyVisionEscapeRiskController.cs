using Redarbor.Company.Contracts.ServiceLibrary;
using Redarbor.Company.WebUI.Helpers;
using Redarbor.Company.WebUI.Models.Company.Vision;
using Redarbor.Company.WebUI.Security;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Master.Contracts.ServiceLibrary.DTO;
using Redarbor.Master.Entities.Enums;
using Redarbor.Master.Entities.Searchs;
using Redarbor.Master.Entities.Vision;
using Redarbor.Products.Contracts.ServiceLibrary;
using Redarbor.Vision.Contracts.ServiceLibrary;
using Redarbor.Web.UI.Library.Controllers;
using Redarbor.Web.UI.Library.Exceptions;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Web.Mvc;

namespace Redarbor.Company.WebUI.Controllers
{
    [RedarborAuthorize]
    public class CompanyVisionEscapeRiskController : RedarborController
    {
        private readonly IEscapeRiskService _escapeRiskService;
        private readonly IExceptionPublisherService _exceptionPublisherService;
        private readonly IPortalConfigurationService _portalConfigurationService;
        private readonly ICompanyService _companyService;
        private readonly ICompanyProductService _companyProductService;

        public CompanyVisionEscapeRiskController(IEscapeRiskService escapeRiskService,
            IExceptionPublisherService exceptionPublisherService,
            IPortalConfigurationService portalConfigurationService,
            ICompanyService companyService,
            ICompanyProductService companyProductService)
        {
            this._escapeRiskService = escapeRiskService;
            _exceptionPublisherService = exceptionPublisherService;
            _portalConfigurationService = portalConfigurationService;
            _companyService = companyService;
            _companyProductService = companyProductService;
        }

        [Route("Company/EmployerBranding/EscapeRisk")]
        [Route("Company/EmproyerBranding/EscapeRisk")]
        public ActionResult Index()
        {
            try
            {
                var companyCredentials = SecurityHelper.GetCompanyCredentials();
                var portalConfig = _portalConfigurationService.GetPortalConfiguration();
                var company = _companyService.GetByPK(new CompanySearchSpecifications(companyCredentials.PortalId) { Id = companyCredentials.IdCompany });

                if (checkBlocked(portalConfig.company_block, company.Blocked, company.LastDateBlockedUpdate, portalConfig.DaysBlock))
                    return RedirectToAction("Blocked", "Account");

                if (!CompanyHelper.IsVisionAccesPermited(companyCredentials))
                {
                    return RedirectToAction("Index", "Company");
                }

                return View(this.LoadModel(companyCredentials));
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyVisionEscapeRiskController Index {ex}");
                _exceptionPublisherService.Publish(ex, "CompanyVisionEscapeRiskController", "Index");
                return RedirectToAction("Index", "Company");
            }
        }

        private VisionEscapeRiskModel LoadModel(CompanyCredentials companyCredentials)
        {
            string currentCSSClass = string.Empty;
            string currentMarkIndex = string.Empty;
            string escapeRiskPercentageDescription = string.Empty;
            int escapeRiskPercentage = 0;
            int canvasEscapeRiskYAxeStepSize = 0;
            string canvasEscapeRiskHistoryLabel = string.Empty;
            string canvasEscapeRiskHistoryData = string.Empty;
            bool phEscapeRiskEvolution = true;
            bool phEscapeRiskEvoVisible = false;
            bool phEscapeRiskEvoVisibleBLoqued = false;
            bool phEscapeRisk = false;
            bool phEscapeRiskBloqued = true;
            bool haveVisionFeature = false;
            bool isActionPermitedByRole = false;

            CompanySearchSpecifications specificationsCompany = new CompanySearchSpecifications(companyCredentials.PortalId);
            List<EscapeRiskEntity> escapeRiskEntities;


            specificationsCompany.Id = companyCredentials.IdCompany;

            escapeRiskEntities = GetEscapeRiskHistory(companyCredentials.PortalId, companyCredentials.IdCompany);
            if (!escapeRiskEntities.Any())
            {
                phEscapeRiskEvolution = false;
            }
            else
            {
                escapeRiskPercentage = this.GetEscapeRiskIndex(companyCredentials.PortalId, companyCredentials.IdCompany);
                escapeRiskPercentageDescription = $"{escapeRiskPercentage}.1%";

                if (escapeRiskEntities.Count > 1)
                {
                    string grid_escape_risk_history_labels = string.Empty;
                    string grid_escape_risk_history_data = string.Empty;

                    foreach (var escapeRisk in escapeRiskEntities)
                    {
                        grid_escape_risk_history_labels = string.IsNullOrEmpty(grid_escape_risk_history_labels)
                            ? $"{GetDateTimeFromInt(escapeRisk.Date).ToString("m")}"
                            : $"{grid_escape_risk_history_labels},{GetDateTimeFromInt(escapeRisk.Date).ToString("m")}";

                        grid_escape_risk_history_data = string.IsNullOrEmpty(grid_escape_risk_history_data)
                            ? string.Format("{0}", escapeRisk.EscapeRiskPercentage)
                            : string.Format("{0},{1}", grid_escape_risk_history_data, escapeRisk.EscapeRiskPercentage);
                    }


                    canvasEscapeRiskYAxeStepSize = 100;
                    canvasEscapeRiskHistoryLabel = grid_escape_risk_history_labels;
                    canvasEscapeRiskHistoryData = grid_escape_risk_history_data;
                }
            }

            haveVisionFeature = _companyProductService.HasFeature(ProductAmbitEnum.Vision, companyCredentials.IdCompany, companyCredentials.PortalId);
            isActionPermitedByRole = SecurityHelper.IsActionPemitedByRole(Master.Entities.Enums.SecurityActionEnum.AllVisionAcces);

            if (haveVisionFeature)
            {
                phEscapeRiskEvoVisible = true;
                phEscapeRiskEvoVisibleBLoqued = false;
                phEscapeRisk = true;
                phEscapeRiskBloqued = false;
            }
            else
            {
                phEscapeRiskEvoVisible = false;
                phEscapeRiskEvoVisibleBLoqued = true;
                phEscapeRisk = false;
                phEscapeRiskBloqued = true;
            }



            return new VisionEscapeRiskModel(
                    this.GetCurrentCSSClass(escapeRiskPercentage),
                    this.GetCurrentMarkIndex(escapeRiskPercentage),
                    escapeRiskPercentageDescription,
                    canvasEscapeRiskYAxeStepSize,
                    canvasEscapeRiskHistoryLabel,
                    canvasEscapeRiskHistoryData,
                    phEscapeRiskEvolution,
                    phEscapeRiskEvoVisibleBLoqued,
                    phEscapeRiskEvoVisible,
                    phEscapeRisk,
                    phEscapeRiskBloqued,
                    this.GetPhAllGraphics(escapeRiskEntities.Count()),
                    this.GetPhWithoutData(escapeRiskEntities.Count()),
                    haveVisionFeature,
                    isActionPermitedByRole
                );
        }

        private List<EscapeRiskEntity> GetEscapeRiskHistory(short portalId, int companyId)
        {
            return _escapeRiskService
                .GetEscapeRiskBetweenDates(companyId, portalId,
                    Convert.ToInt32(DateTime.Now.AddDays(-30).ToString("yyyyMMdd")),
                    Convert.ToInt32(DateTime.Now.ToString("yyyyMMdd")));
        }

        private int GetEscapeRiskIndex(short portalId, int companyId)
        {
            return _escapeRiskService.GetEscapeRiskPercentage(companyId, portalId, DateTime.Now);
        }

        private DateTime GetDateTimeFromInt(int date)
        {
            var dt = DateTime.Now;

            DateTime.TryParseExact(date.ToString(), "yyyyMMdd",
                              CultureInfo.InvariantCulture,
                              DateTimeStyles.None, out dt);

            return dt;
        }

        private string GetCurrentCSSClass(int escapeRiskPercentage)
        {
            if (escapeRiskPercentage >= 67)
                return " hig";
            else if (escapeRiskPercentage >= 34)
                return " med";
            else
                return " low";
        }

        private string GetCurrentMarkIndex(int escapeRiskPercentage)
        {
            var escapeRiskIndexMark = escapeRiskPercentage;

            if (escapeRiskIndexMark == 100)
                escapeRiskIndexMark = escapeRiskIndexMark - 1;

            return $"{escapeRiskIndexMark}.1%";
        }

        private bool GetPhAllGraphics(int counterEscapeRiskEnties)
        {
            if (counterEscapeRiskEnties == 0)
            {
                return false;
            }

            return true;
        }

        private bool GetPhWithoutData(int counterEscapeRiskEnties)
        {
            if (counterEscapeRiskEnties == 0)
            {
                return true;
            }

            return false;
        }
    }
}