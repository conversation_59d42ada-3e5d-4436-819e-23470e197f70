using Redarbor.Candidate.Contracts.ServiceLibrary;
using Redarbor.Candidate.Contracts.ServiceLibrary.DTO;
using Redarbor.CommentCv.Contracts.ServiceLibrary;
using Redarbor.Common.Entities;
using Redarbor.Common.Entities.Configuration;
using Redarbor.Common.Entities.Constants;
using Redarbor.Common.Entities.Enums;
using Redarbor.Company.Contracts.ServiceLibrary;
using Redarbor.Company.WebUI.Enums;
using Redarbor.Company.WebUI.Helpers;
using Redarbor.Company.WebUI.Helpers.Mappings;
using Redarbor.Company.WebUI.Models;
using Redarbor.Company.WebUI.Models.Company;
using Redarbor.Company.WebUI.Models.Company.Home;
using Redarbor.Company.WebUI.Models.Company.Match;
using Redarbor.Company.WebUI.Models.Company.Shared;
using Redarbor.Company.WebUI.Security;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Core.Contracts.ServiceLibrary;
using Redarbor.Core.Contracts.ServiceLibrary.Enums;
using Redarbor.Core.Counter.Contracts.ServiceLibrary;
using Redarbor.Core.Elastic.Library;
using Redarbor.Core.Kpi.Contracts.ServiceLibrary;
using Redarbor.Core.Kpi.Contracts.ServiceLibrary.Enums;
using Redarbor.Core.Shared.Contracts.ServiceLibrary;
using Redarbor.Core.Stack.Contracts.ServiceLibrary;
using Redarbor.Curriculum.Contracts.ServiceLibrary;
using Redarbor.CustomFolders.Contracts.ServiceLibrary;
using Redarbor.DataAnalytics.ServiceLibrary.DTO;
using Redarbor.Dictionaries.Consumer.Enums;
using Redarbor.Elastic.Entities.Candidate;
using Redarbor.Extensions.Library.Extensions;
using Redarbor.Folders.Contracts.ServiceLibrary;
using Redarbor.Geolocation.Contracts.ServiceLibrary;
using Redarbor.Geolocation.Contracts.ServiceLibrary.DTO;
using Redarbor.Master.Contracts.ServiceLibrary;
using Redarbor.Master.Contracts.ServiceLibrary.DTO;
using Redarbor.Master.Entities.Company;
using Redarbor.Master.Entities.CustomFolder;
using Redarbor.Master.Entities.Cv;
using Redarbor.Master.Entities.Enums;
using Redarbor.Master.Entities.Match;
using Redarbor.Master.Entities.Message;
using Redarbor.Master.Entities.Offers;
using Redarbor.Master.Entities.Product;
using Redarbor.Master.Entities.Searchs;
using Redarbor.Master.Entities.Stack;
using Redarbor.Match.Contracts.ServiceLibrary;
using Redarbor.Match.Contracts.ServiceLibrary.DTO;
using Redarbor.Match.Contracts.ServiceLibrary.Entities;
using Redarbor.Message.Jobads.Chat.Impl.ServiceLibrary;
using Redarbor.Offer.Contracts.ServiceLibrary;
using Redarbor.Offer.Contracts.ServiceLibrary.DTO;
using Redarbor.Procs.Domain.Enums;
using Redarbor.Products.Contracts.ServiceLibrary;
using Redarbor.Products.Contracts.ServiceLibrary.DTO;
using Redarbor.ProductSubGroups.Contracts.ServiceLibrary;
using Redarbor.Professions.Contracts.ServiceLibrary;
using Redarbor.RatingCv.Contracts.ServiceLibrary;
using Redarbor.Web.UI.Library;
using Redarbor.Web.UI.Library.Exceptions;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Diagnostics;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using System.Web.Mvc;
using SearchCandidate = Redarbor.Elastic.Entities.Candidate;

namespace Redarbor.Company.WebUI.Controllers
{
    [RoutePrefix("Company/Offers/Match")]
    [RedarborAuthorize]
    public class CompanyMatchesController : CompanyBaseController
    {
        const int NO_FREEMIUM_VIEWS_RESULTS = -1;
        short PageId = (short)PageEnum.MatchOffer;
        private const string SEPARATOR_SPLIT = ",";
        private const int LIMIT_CANDIDATES_TO_COMPARE = 3;
        CompanyCredentials _companyCredentials = new CompanyCredentials();
        CompanyProductEntity _companyProduct = new CompanyProductEntity();
        OfferEntity _offer = new OfferEntity();
        CompanyProductEntity _offerProduct = new CompanyProductEntity();
        List<CompanyFilterEntity> _companyFilters = new List<CompanyFilterEntity>();
        List<FoldersDataModel> _folders = new List<FoldersDataModel>();

        private bool _offerProductHasCVBBDD = false;
        private bool _hasFolders = false;
        private bool _hasCustomFolders = false;
        private bool _hasRatings = false;
        private bool _hasComents = false;
        private bool _hasChat = false;
        private bool _hasCvDownload = false;
        private bool _hasCandidateComparator = false;
        private bool _hasSaveFilters = false;
        private bool _hasSearchNit = false;
        private bool _hasExclusion = false;
        private bool _hasKillerQuestions = false;
        private bool _hasAdequacy = false;
        private bool _haveMembresyActive = false;
        private bool _haveOfferCompletOrMembership = false;
        private bool _showMatchesData = true;
        private bool _canCreateChat = false;
        private bool _hasOfferFlash = false;
        private bool _hasCVDownloadMatchesReceived = false;
        private bool _hasCandidateSuggester = false;
        private SearchResultDTO<MatchElasticEntity> _unlimitedMatchesResultElastic;
        private List<long> _candidatesSuggested;

        private int _maxCvAppliedToSee = 0;
        private bool _isProgressiveFreemiumChat = false;
        int _publishedOffers = 0;
        int _totalMatchesInOffer = 0;
        int _folderId = (Int32)CompanyOfferFolderEnum.Recibidos;
        string _contactEmail = string.Empty;
        private const int PAGE_SIZE_CONVERSATION_DEFAULT = 50;
        private const int METERS_IN_KILOMETER = 1000;

        private readonly IOfferCvCountersService _offerCvCountersService;
        private readonly IEncryptionService _encryptionService;
        private readonly IMatchService _matchService;
        private readonly IStackPublicatorMatchesService _stackPublicatorMatchesService;
        private readonly ICompanyFiltersService _companyFiltersService;
        private readonly ICustomFolderService _customFoldersService;
        private readonly IKillerQuestionsService _killerQuestionService;
        private readonly IFoldersService _foldersService;
        private readonly IMatchDownloaderService _matchDownloaderService;
        private readonly ICommentCvService _commentCvService;
        private readonly IRatingCvService _ratingCvService;
        private readonly ICandidateService _candidateService;
        private readonly ICurriculumService _curriculumService;
        private readonly ISessionService _sessionService;
        private readonly IProfessionService _professionService;
        private Dictionary<string, List<FacetResult>> _facets = new Dictionary<string, List<FacetResult>>();
        private readonly IFreemiumOfferViewsService _freemiumOfferViewsService;
        private readonly IElasticCandidateService _elasticSearchCandidateService;
        private readonly ILandingProductsService _landingProductsService;
        private readonly ICompanyTrackingService _companyTrackingService;
        private readonly IKpiService _kpiService;
        private readonly IMessageJobadsChatService _messageJobdsChatService;
        private readonly ISecurityOfferService _securityOfferService;
        private readonly IFreemiumOfferContactsService _freemiumOfferContactsService;
        private readonly IGeoLocationService _geolocationService;

        public CompanyMatchesController
            (ICandidateService canidateService,
           ICompanyFiltersService companyFiltersService,
           IProductService productService,
           IProductSubGroupsService productSubGroupsService,
           ICustomFolderService customFoldersService,
           IOfferService offerService,
           ICompanyProductService companyProductService,
           ICompanyService companyService,
           IPortalConfigurationService portalConfigurationService,
           IDictionaryService dictionaryService,
           IStackPublicatorMatchesService stackPublicatorMatchesService,
           IEncryptionService encryptionService,
           IMatchService matchService,
           IKillerQuestionsService killerQuestionService,
           IFoldersService foldersService,
           IMatchDownloaderService matchDownloaderService,
           ISecurityService securityService,
           ICommentCvService commentCvService,
           IRatingCvService ratingCvService,
           ICurriculumService curriculumService,
           ISessionService sessionService,
           IExceptionPublisherService exceptionPublisherService,
           IOfferCvCountersService offerCvCountersService,
           IProfessionService professionService,
           IFreemiumOfferViewsService freemiumOfferViewsService,
           ILandingProductsService landingProductsService,
           IElasticCandidateService elasticsearchCandidateService,
           ICompanyTrackingService companyTrackingService,
           IKpiService kpiService, IMessageJobadsChatService messageJobdsChatService, ISecurityOfferService securityOfferService,
           IFreemiumOfferContactsService freemiumOfferContactsService,
           IGeoLocationService geolocationService) : base(productService, productSubGroupsService, offerService, companyProductService, companyService, portalConfigurationService, dictionaryService, securityService, exceptionPublisherService)
        {
            _candidateService = canidateService;
            _encryptionService = encryptionService;
            _stackPublicatorMatchesService = stackPublicatorMatchesService;
            _matchService = matchService;
            _companyFiltersService = companyFiltersService;
            _customFoldersService = customFoldersService;
            _killerQuestionService = killerQuestionService;
            _foldersService = foldersService;
            _commentCvService = commentCvService;
            _ratingCvService = ratingCvService;
            _matchDownloaderService = matchDownloaderService;
            _curriculumService = curriculumService;
            _sessionService = sessionService;
            _offerCvCountersService = offerCvCountersService;
            _professionService = professionService;
            _freemiumOfferViewsService = freemiumOfferViewsService;
            _landingProductsService = landingProductsService;
            _elasticSearchCandidateService = elasticsearchCandidateService;
            _kpiService = kpiService;
            _companyTrackingService = companyTrackingService;
            _messageJobdsChatService = messageJobdsChatService;
            _securityOfferService = securityOfferService;
            _freemiumOfferContactsService = freemiumOfferContactsService;
            _geolocationService = geolocationService;
        }

        public string AutoCompleteExperienceProfession(string q)
        {
            var portalConfig = PortalConfigurationService.GetPortalConfiguration();

            return _professionService.GetResultSuggest(q, DictionaryByPortalEnum.ProfessionsActive, portalConfig.PortalId);
        }

        [Route]
        public ActionResult Index(string oi, string cf, string by, string o, string ft, string ms, string undoFilter, string filterExclusion = null, bool newApplies = false)
        {
            try
            {
                _companyCredentials = SecurityHelper.GetCompanyCredentials();
                var portalConfig = PortalConfigurationService.GetPortalConfiguration();
                var company = CompanyService.GetByPK(new CompanySearchSpecifications(_companyCredentials.PortalId) { Id = _companyCredentials.IdCompany });
                var companyProductEntity = CompanyProductService.GetByIdCompany(_companyCredentials.IdCompany, _companyCredentials.PortalId, OfferIntegratorEnum.CompuTrabajo);

                if (checkBlocked(portalConfig.company_block, company.Blocked, company.LastDateBlockedUpdate, portalConfig.DaysBlock))
                    return RedirectToAction("Blocked", "Account");

                if (!string.IsNullOrEmpty(oi))
                {
                    Int32.TryParse(_encryptionService.Decrypt(oi), out Int32 offerId);
                    if (offerId > 0)
                    {
                        _companyTrackingService.AeInvalidateMatchesForNotification(offerId, _companyCredentials.IdCompany, _companyCredentials.PortalId);

                        if (!IsMy(_companyCredentials, offerId, portalConfig))
                        {
                            var extradata = new Dictionary<string, string>();
                            extradata.Add("UrlReferrer", Request.UrlReferrer != null ? Request.UrlReferrer.ToString() : string.Empty);
                            ExceptionPublisherService.PublishWarning(new Exception("Is not mine (hot fix) - idoffer:" + offerId + "-idcompany:" + _companyCredentials.IdCompany), "CompanyMatchesController", "Index", extradata);
                            return RedirectToAction("", "Company");
                        }

                        TempData[PianoEvents.OFFER_ID_KEY] = offerId;
                    }
                    else
                    {
                        var extradata = new Dictionary<string, string>();
                        extradata.Add("UrlReferrer", Request.UrlReferrer != null ? Request.UrlReferrer.ToString() : string.Empty);
                        ExceptionPublisherService.Publish(new Exception("Idoffer is 0 -idcompany:" + _companyCredentials.IdCompany), "CompanyMatchesController", "Index", false, extradata);
                        return RedirectToAction("", "Company");
                    }
                }

                if (companyProductEntity.IsBlockedByOldMembership && portalConfig.blockOldMembership)
                {
                    return RedirectToAction("", "Company");
                }

                var rememberModel = false;

                var homeMatchesDataModel = (HomeMatchesDataModel)_sessionService.Get(typeof(HomeMatchesDataModel)) ??
                   new HomeMatchesDataModel()
                   {
                       MultifiltersDataModel = new MultifiltersDataModel()
                       {
                           Direction = o ?? "",
                           FieldSearch = by ?? "",
                           IdSaveFilterSelected = ft ?? "",
                           IsMySelection = (ms != null && ms == "true"),
                           SeeFilterExclusionMessage = filterExclusion ?? string.Empty
                       }
                   };

                if (string.IsNullOrEmpty(ms)) ms = "false";

                if ((HttpContext.Request.UrlReferrer?.AbsolutePath.Contains(Url.Action("Index", "CompanyMatchCvDetail") ?? string.Empty) ?? false) ||
                    ((HttpContext.Request.UrlReferrer?.AbsolutePath.Contains(Url.Action("Index", "CompanyMatches") ?? string.Empty) ?? false) &&
                    (homeMatchesDataModel.FolderSelectedEncrypted == cf && string.IsNullOrWhiteSpace(ft) && (ms.ToLower() == homeMatchesDataModel.MultifiltersDataModel.IsMySelection.ToString().ToLower()) && string.IsNullOrWhiteSpace(by) && string.IsNullOrWhiteSpace(undoFilter))))
                {
                    if ((HomeMatchesDataModel)_sessionService.Get(typeof(HomeMatchesDataModel)) == null)
                        rememberModel = true;
                    else
                        rememberModel = false;
                }
                else
                {
                    _sessionService.Forget(typeof(HomeMatchesDataModel));
                    if (!string.IsNullOrWhiteSpace(by))
                    {
                        homeMatchesDataModel.MultifiltersDataModel.Direction = !string.IsNullOrEmpty(o) ? o : homeMatchesDataModel.MultifiltersDataModel.Direction;
                        homeMatchesDataModel.MultifiltersDataModel.FieldSearch = !string.IsNullOrEmpty(by) ? by : homeMatchesDataModel.MultifiltersDataModel.FieldSearch;
                        homeMatchesDataModel.MultifiltersDataModel.IdSaveFilterSelected = !string.IsNullOrEmpty(ft) ? ft : homeMatchesDataModel.MultifiltersDataModel.IdSaveFilterSelected;
                        homeMatchesDataModel.MultifiltersDataModel.IsMySelection = (ms != null && ms == "true") ? true : homeMatchesDataModel.MultifiltersDataModel.IsMySelection;
                        homeMatchesDataModel.MultifiltersDataModel.SeeFilterExclusionMessage = !string.IsNullOrEmpty(filterExclusion) ? filterExclusion : homeMatchesDataModel.MultifiltersDataModel.SeeFilterExclusionMessage;
                    }
                    else
                    {
                        homeMatchesDataModel = new HomeMatchesDataModel()
                        {
                            MultifiltersDataModel = new MultifiltersDataModel()
                            {
                                Direction = o ?? "",
                                FieldSearch = by ?? "",
                                IdSaveFilterSelected = ft ?? "",
                                IsMySelection = (ms != null && ms == "true"),
                                SeeFilterExclusionMessage = filterExclusion ?? string.Empty
                            }
                        };
                    }
                    rememberModel = true;
                }

                Int32.TryParse(_encryptionService.Decrypt(cf), out var _currentFolder);
                if (_currentFolder == (short)CompanyOfferFolderEnum.BBDD)
                {
                    homeMatchesDataModel.MultifiltersDataModel.Order = (short)CvOrderEnum.Relevance;
                }

                homeMatchesDataModel.ProdCWConvertToComplete = _landingProductsService.ManageCWFeatures((int)ProductAmbitEnum.CVsFreemium, _companyCredentials.IdCompany, portalConfig);
                homeMatchesDataModel.ProdCWCVBBDD = _landingProductsService.ManageCWFeatures((int)ProductAmbitEnum.CvBBDD, _companyCredentials.IdCompany, portalConfig);

                return GetHomeModel(oi, cf = cf ?? _encryptionService.Encrypt(_folderId.ToString()),
                    homeMatchesDataModel, rememberModel, portalConfig, !string.IsNullOrEmpty(ft), newApplies);
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyMatchesController Index-Get {ex}");
                Dictionary<string, string> extradata = new Dictionary<string, string>();
                extradata.Add("Idcompany", _companyCredentials?.IdCompany.ToString());
                extradata.Add("IdPortal", _companyCredentials?.PortalId.ToString());
                extradata.Add("CompanyName", _companyCredentials?.CompanyName.ToString());
                extradata.Add("UserRole", _companyCredentials?.UserRole.ToString());
                extradata.Add("UserId", _companyCredentials?.UserId.ToString());
                extradata.Add("UserName", _companyCredentials?.Username.ToString());
                extradata.Add("IdOffer", oi);
                ExceptionPublisherService.Publish(ex, "CompanyMatchesController", "Index-Get", false, extradata);
                return RedirectToAction("Index", "Home");
            }
        }

        [Route]
        [HttpPost]
        [ValidateAntiForgeryToken()]
        public ActionResult Index(string oi, string cf, HomeMatchesDataModel homeMatchesDataModel)
        {
            homeMatchesDataModel.ActivelySentByUser = true;
            try
            {
                _companyCredentials = SecurityHelper.GetCompanyCredentials();
                var portalConfig = PortalConfigurationService.GetPortalConfiguration();

                if (!string.IsNullOrEmpty(homeMatchesDataModel.MultifiltersDataModel.NameNewSaveFilter) && homeMatchesDataModel.MultifiltersDataModel.IsFiltered)
                    return CreateNewSaveFilterAndRedirect(homeMatchesDataModel.MultifiltersDataModel, oi, homeMatchesDataModel.OfferIdCtEncrypted, cf, portalConfig);
                else
                    return GetHomeModel(oi, cf = cf ?? _encryptionService.Encrypt(_folderId.ToString()), homeMatchesDataModel, true, portalConfig);
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyMatchesController Index-Post {ex}");
                Dictionary<string, string> extradata = new Dictionary<string, string>();
                extradata.Add("Idcompany", _companyCredentials?.IdCompany.ToString());
                extradata.Add("IdPortal", _companyCredentials?.PortalId.ToString());
                extradata.Add("CompanyName", _companyCredentials?.CompanyName.ToString());
                extradata.Add("UserRole", _companyCredentials?.UserRole.ToString());
                extradata.Add("UserId", _companyCredentials?.UserId.ToString());
                extradata.Add("UserName", _companyCredentials?.Username.ToString());
                extradata.Add("IdOffer", oi);
                ExceptionPublisherService.Publish(ex, "CompanyMatchesController", "Index-Post", false, extradata);
                return RedirectToAction("Index", "Home");
            }
        }

        public async Task<ActionResult> LogicalDeleteCVsFolder(string oi, string ids, string cf, string cfo)
        {
            try
            {
                var portalConfig = PortalConfigurationService.GetPortalConfiguration();

                Int32.TryParse(_encryptionService.Decrypt(oi), out int offerId);
                Int32.TryParse(cf, out int folderDestiny);
                Int32.TryParse(cfo, out int folderOrigin);

                LoadData(offerId, portalConfig);

                var error = CheckData(offerId, ids, folderDestiny, folderOrigin, portalConfig);
                if (!string.IsNullOrEmpty(error))
                    return new HttpStatusCodeResult(HttpStatusCode.Forbidden, error);

                var offerProduct = GetCompanyOfferProduct(_companyCredentials, offerId);
                var offer = OfferService.GetByPk(offerId, portalConfig.PortalId, true);
                var matches = BuildListMatches(ids, _offer.idofferCT);

                _foldersService.DecreaseTotalCandidatesInFolder(_offer.idofferCT, matches, folderDestiny, folderOrigin, portalConfig.PortalId, _companyCredentials.IdCompany);

                foreach (var match in matches)
                {
                    _matchService.LogicalDeleteMatch(match);
                    await DeleteChatAsync(match, portalConfig, offerProduct);
                }

                _matchService.DeleteCacheFolderFacet(new SearchFilterDTO()
                {
                    Facets = new List<string>
                                       (new string[] { FacetsByMatchEnum.folderId.ToString() }),
                    IdPortal = portalConfig.PortalId,
                    IdOffer = _offer.idofferCT
                }, portalConfig);

                return new HttpStatusCodeResult(HttpStatusCode.OK, "Success");
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyMatchesController LogicalDeleteCVsFolder {ex}");
                ExceptionPublisherService.Publish(ex, "CompanyMatchesController", "LogicalDeleteCVsFolder");
                return new HttpStatusCodeResult(HttpStatusCode.InternalServerError, "Error");
            }
        }

        public ActionResult MoveCVsFolder(string oi, string ids, string cf, string cfo, int? idp)
        {
            try
            {
                int idpInt = idp ?? 0;
                var portalConfig = PortalConfigurationService.GetPortalConfiguration();
                int.TryParse(_encryptionService.Decrypt(oi), out int offerId);
                int.TryParse(cf, out int folderDestiny);
                int.TryParse(cfo, out int folderOrigin);

                LoadData(offerId, portalConfig);

                var error = CheckData(offerId, ids, folderDestiny, folderOrigin, portalConfig);
                if (!string.IsNullOrEmpty(error))
                    return new HttpStatusCodeResult(HttpStatusCode.Forbidden, error);

                var matches = BuildListMatches(ids, _offer.idofferCT);


                if (portalConfig.AEPortalConfig.CTHROnBoardingEnabled && folderDestiny == (int)CompanyOfferFolderEnum.Contratados && matches.Count() > 0)
                {
                    bool processResult = _candidateService.ProcessCandidateHrOnboarding(matches, portalConfig, _companyCredentials.IdCompany, _offer.title);
                    if (!processResult)
                    {
                        throw new InvalidOperationException("The candidate HR onboarding process failed.");
                    }
                }

                bool increaseInDiscardFolder = true;
                if (_offerProduct.GroupId == (int)ProductGroupsEnum.Freemium && folderDestiny == (int)CompanyOfferFolderEnum.Descartados)
                    increaseInDiscardFolder = false;

                _foldersService.MoveCvsFromFolder(_offer.idofferCT, matches, folderDestiny, folderOrigin, _companyCredentials.IdCompany, increaseInDiscardFolder);

                _foldersService.PrepareKPIFromFolder(matches.Count, _companyCredentials.IdCompany, _offerProduct.GroupId, folderDestiny, portalConfig.PortalId, idpInt, _companyCredentials.UserId);

                foreach (var match in matches)
                {
                    if (match.Id > -1)
                    {
                        if (!match.IsSeen)
                        {
                            _matchService.UpdateShowedMatch(match);
                        }

                        _matchService.SetProcessMatchStatus(_companyCredentials.IdCompany, _offer.idofferCT, (int)MatchProccesOfferStatusEnum.Reading, portalConfig.PortalId);
                        _matchService.NotifyChangeFolder(match, folderOrigin, folderDestiny, _offer);
                        ChangeCandidatesMatchProcessStatus(match, folderDestiny, portalConfig);
                    }
                }

                if (matches != null && matches.Any())
                {
                    _matchService.IndexMatchesByIds(matches.Select(s => s.Id).ToList(), portalConfig.PortalId);
                }

                _matchService.DeleteCacheFolderFacet(new SearchFilterDTO()
                {
                    Facets = new List<string>
                                    (new string[] { FacetsByMatchEnum.folderId.ToString() }),
                    IdPortal = portalConfig.PortalId,
                    IdOffer = _offer.idofferCT
                }, portalConfig);

                return new HttpStatusCodeResult(HttpStatusCode.OK, "Success");
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyMatchesController MoveCVsFolder {ex}");
                ExceptionPublisherService.Publish(ex, "CompanyMatchesController", "MoveCVsFolder");
                return new HttpStatusCodeResult(HttpStatusCode.InternalServerError, "Error");
            }
        }

        public string Extract(string oi, string ids, string cf)
        {
            var portalConfig = PortalConfigurationService.GetPortalConfiguration();
            var offer = OfferService.GetByPk(oi, portalConfig.PortalId);
            if (offer == null || offer.idoffer == 0)
                return string.Empty;

            List<string> listIds = new List<string>();

            if (!string.IsNullOrEmpty(ids))
            {
                var list = ids.ToString().Split('|');
                list.Where(q => !string.IsNullOrEmpty(q)).ToList().ForEach(id => { listIds.Add(id); });
            }
            else if (!string.IsNullOrEmpty(cf))
            {
                int.TryParse(EncryptationHelper.Decrypt(cf), out var folder);
                var list = _matchService.GetCandidateMatchesByOfferIdAndFolder(offer.idoffer, folder);
                list.Where(q => q != 0).ToList().ForEach(id => { listIds.Add(EncryptationHelper.Encrypt(id.ToString())); });
            }

            int idDownload = 0;
            var companyCredentials = SecurityHelper.GetCompanyCredentials();

            CvDownloaderEntity entity = new CvDownloaderEntity()
            {
                Filter = string.Empty,
                IdOffer = offer.idoffer,
                IdCompany = offer.idcompany,
                OfferTitle = offer.title,
                OfferDate = offer.Integrations.FirstOrDefault()?.publicationtime ?? DateTime.MinValue,
                IdLocation = offer.idlocalization,
                DownloadAllCvs = listIds.Any() ? (short)0 : (short)1,
                IdStatus = (int)StatusEnum.Pending,
                IdPortal = portalConfig.PortalId,
                UserId = companyCredentials.UserId,
                CountCvs = listIds.Any() ? listIds.Count() : 0
            };

            idDownload = _matchDownloaderService.AddCvDownload(entity);

            if (idDownload > 0)
            {
                if (listIds.Any())
                {
                    foreach (var id in listIds)
                    {
                        long.TryParse(_encryptionService.Decrypt(id), out long Id);
                        if (Id > 0)
                        {
                            var match = _matchService.GetByPk(Id);
                            var cvsOffer = new OfferCvDownloaderEntity()
                            {
                                Id = idDownload,
                                IdOffer = offer.idoffer,
                                IdCompany = companyCredentials.IdCompany,
                                IdCv = match.CurriculumId,
                                DateAdd = match.AppliedOn
                            };

                            _matchDownloaderService.AddCvDownloadOffer(cvsOffer);
                        }
                    }
                }
            }
            return _encryptionService.Encrypt(idDownload.ToString());
        }

        public void TryAsyncChangeMatchProcessStatus(List<MatchElasticEntity> listMatches, OfferEntity Offer, int newProcessStatus, CompanyCredentials companyCredentials, PortalConfig portalConfig)
        {
            try
            {
                AsyncChangeMatchProcessOneByOne(listMatches, Offer, newProcessStatus, companyCredentials, portalConfig);
                List<long> idMatches = listMatches.Select(m => m.Id).ToList();

                if (listMatches.Count > 0)
                {
                    AsyncNotifyProcessChangedToCandidate(idMatches, newProcessStatus, Offer);
                }

            }
            catch (Exception ex)
            {
                Trace.TraceInformation("TryAsyncChangeMatchProcessStatus - CompanyMatchesController: ", ex);
            }
        }

        public int AddCustomFolders(string namefd, string oi, string p)
        {
            try
            {
                if (string.IsNullOrEmpty(namefd))
                    return (int)FolderCustomStatusResponseEnum.NO_NAME;

                namefd = namefd.TruncateWithFinalPoints(_customFoldersService.GetMaxLenghtNameFolder());
                Int32.TryParse(_encryptionService.Decrypt(oi), out int offerId);
                Int32.TryParse(p, out int portalId);

                CustomFoldersFilterEntity customFoldersFilter = new CustomFoldersFilterEntity()
                {
                    Environment = (short)CustomFoldersEnvironmentEnum.Matches,
                    IdOffer = offerId,
                    IdPortal = (short)portalId
                };

                var customFolders = _customFoldersService.GetCustomFolders(customFoldersFilter);

                if (customFolders.Count == 2)
                    return (int)FolderCustomStatusResponseEnum.PASS_LIMIT;

                if (customFolders.Count == 0)
                    customFoldersFilter.Position = 6;

                customFoldersFilter.Name = namefd;
                customFoldersFilter.IdUser = 0;

                switch (customFolders.Select(n => n.Position).FirstOrDefault())
                {
                    case (short)CompanyCustomFoldersMatchEnum.Folder1:
                        customFoldersFilter.Position = (short)CompanyCustomFoldersMatchEnum.Folder2;
                        break;
                    case (short)CompanyCustomFoldersMatchEnum.Folder2:
                        customFoldersFilter.Position = (short)CompanyCustomFoldersMatchEnum.Folder1;
                        break;
                }

                if (_customFoldersService.AddCustomFolder(customFoldersFilter))
                {
                    return 1;
                }

                return (int)FolderCustomStatusResponseEnum.PASS_LIMIT;
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyMatchesController AddCustomFolders {ex}");
                ExceptionPublisherService.Publish(ex, "CompanyMatchesController", "AddCustomFolders");
                return (int)FolderCustomStatusResponseEnum.PASS_LIMIT;
            }
        }

        public int UpdateCustomFolders(string namefd, string oi, string p, string idf)
        {
            try
            {
                Int32.TryParse(_encryptionService.Decrypt(idf), out int folderId);
                Int32.TryParse(_encryptionService.Decrypt(oi), out int offerId);
                Int32.TryParse(p, out int portalId);

                if (string.IsNullOrEmpty(namefd) || folderId <= 0 || offerId <= 0)
                    return (int)FolderCustomStatusResponseEnum.NO_NAME;

                _offer = OfferService.GetByPk(offerId, (short)portalId);

                CustomFoldersFilterEntity customFoldersFilter = new CustomFoldersFilterEntity()
                {
                    Environment = (short)CustomFoldersEnvironmentEnum.Matches,
                    IdOffer = offerId,
                    IdPortal = (short)portalId,
                    Name = namefd,
                    IdUser = 0,
                    Id = folderId
                };

                _customFoldersService.UpdateCustomFolder(customFoldersFilter);

                return 1;
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyMatchesController UpdateCustomFolders {ex}");
                ExceptionPublisherService.Publish(ex, "CompanyMatchesController", "UpdateCustomFolders");
                return 0;
            }
        }

        public int DeleteCustomFolders(string oi, string p, string idf, short f)
        {
            try
            {
                Int32.TryParse(_encryptionService.Decrypt(idf), out int folderId);
                var idFolderGeneric = f;

                if (folderId <= 0
                    && (idFolderGeneric == (short)CompanyCustomFoldersMatchEnum.Folder1 || idFolderGeneric == (short)CompanyCustomFoldersMatchEnum.Folder2))
                    return (int)StatusEnum.Bloqued;

                Int32.TryParse(_encryptionService.Decrypt(oi), out int offerId);
                Int32.TryParse(p, out int portalId);

                CustomFoldersFilterEntity customFoldersFilter = new CustomFoldersFilterEntity()
                {
                    Environment = (short)CustomFoldersEnvironmentEnum.Matches,
                    IdOffer = offerId,
                    IdPortal = (short)portalId,
                    IdUser = 0,
                    Id = folderId
                };

                _customFoldersService.DeleteCustomFolder(customFoldersFilter);

                if (_offer == null || _offer.idoffer == 0)
                    _offer = OfferService.GetByPk(offerId, (short)portalId);

                return 1;
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyMatchesController DeleteCustomFolders {ex}");
                ExceptionPublisherService.Publish(ex, "CompanyMatchesController", "DeleteCustomFolders");
                return 0;
            }
        }

        public bool UpdateGeolocalizationOffer(string oi, string ipc, string pi)
        {
            try
            {
                _companyCredentials = SecurityHelper.GetCompanyCredentials();
                var portalConfig = PortalConfigurationService.GetPortalConfiguration();

                Int16.TryParse(pi, out short portalId);
                Int32.TryParse(ipc, out int postalCodeId);
                Int32.TryParse(_encryptionService.Decrypt(oi), out int offerId);

                if (postalCodeId <= 0 || offerId <= 0)
                    return false;

                var result = OfferService.UpdatePostalCode(offerId, portalId, postalCodeId);
                if (result)
                {
                    _kpiService.Add((short)CompanyKpiEnum.CT_COMPANY_SET_POSTAL_CODE_OFFER_IN_MATCHES_GRID, portalConfig.PortalId);
                    return true;
                }
                return false;
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyMatchesController UpdateGeolocalizationOffer {ex}");
                ExceptionPublisherService.Publish(ex, "CompanyMatchesController", "UpdateGeolocalizationOffer");
                return false;
            }
        }

        private bool IsMy(CompanyCredentials companyCredentials, int idOffer, PortalConfig portalConfig)
        {
            return _securityOfferService.IsMy(new IsMyOfferDTO(companyCredentials.UserRole, companyCredentials.IdCompany, companyCredentials.PortalId, (int)companyCredentials.UserId, idOffer, portalConfig.EnabledNewIsMy));
        }

        private bool HasNewFreemiumChat(PortalConfig portalConfig, CompanyProductEntity offerProduct)
        {
            return portalConfig.AEPortalConfig.EnableProgressiveChatFreemium
             && CompanyProductService.HasActiveFeatureByProduct(new SearchCompanyProductsByCompanyDTO(offerProduct.IdCompany, ProductAmbitEnum.ChatContactFreemium, portalConfig.PortalId, ProductEnum.FreemiumService));
        }

        private CompanyProductEntity GetCompanyOfferProduct(CompanyCredentials companyCredentials, int idOffer)
        {
            var companyProduct = CompanyProductService.GetByIdCompany(companyCredentials.IdCompany, companyCredentials.PortalId, OfferIntegratorEnum.CompuTrabajo);
            var offer = OfferService.GetByPk(idOffer, companyCredentials.PortalId);
            CompanyProductEntity offerProduct;

            if (companyProduct.GroupId != (short)ProductGroupsEnum.Membership)
            {
                offerProduct = CompanyProductService.GetByCompanyProductId(offer.Integrations.FirstOrDefault()?.idcompanyproduct ?? 0, offer.idportal, offer.idcompany);
            }
            else
            {
                offerProduct = companyProduct;
            }

            return offerProduct;
        }

        private async Task DeleteChatAsync(MatchEntity match, PortalConfig portalConfig, CompanyProductEntity offerProduct)
        {
            var deleteChat = false;

            if ((portalConfig.has_chat > 0) && (offerProduct.Features.Exists(f => f.AmbitId == (int)ProductAmbitEnum.MessagesMailing))) deleteChat = true;

            if ((portalConfig.has_chat > 0) && HasNewFreemiumChat(portalConfig, offerProduct)) deleteChat = true;

            if (!deleteChat) return;

            List<ConversationEntity> listConversations = await GetIdConversationAsync(match, portalConfig);

            if (listConversations.Any())
                await TryDeleteConversationAsync(listConversations, portalConfig);
        }

        private async Task TryDeleteConversationAsync(List<ConversationEntity> listConversations, PortalConfig portalConfig)
        {
            //TODO En vez de eliminar directo se podría poner en una cola de rabbit para que se elimine
            if (listConversations.FirstOrDefault().IdConversation != 0)
            {
                var companyCredentials = SecurityHelper.GetCompanyCredentials();

                ConversationFilter conversation = new ConversationFilter();
                conversation.IdPortal = portalConfig.PortalId;
                conversation.IdConversation = listConversations.FirstOrDefault().IdConversation;
                conversation.IdCompany = companyCredentials.IdCompany;
                conversation.IdUserCompany = companyCredentials.UserId;
                conversation.ConversationFilterEnum = ConversationFilterEnum.CompanyMessages; //TODO MQC Cuando candidato tengo lo de archivar lo modificaremos ahora eliminara a 4

                await _messageJobdsChatService.DeleteConversationAsync(conversation, portalConfig.idapp);
            }
        }

        private async Task<List<ConversationEntity>> GetIdConversationAsync(MatchEntity match, PortalConfig portalConfig)
        {

            var idOffer = OfferService.GetIdOfferByIdOfferCT(match.JobOfferId, portalConfig.PortalId);

            ConversationFilter filter = new ConversationFilter();
            filter.IdCandidate = match.CandidateId;
            filter.IdPortal = portalConfig.PortalId;
            filter.IdCompany = _companyCredentials.IdCompany;
            filter.IdUserCompany = _companyCredentials.UserId;
            filter.UserType = (int)UserTypeEnum.Company;
            filter.IdOffer = idOffer;
            filter.IdUserCandidate = _candidateService.GetIdUserCandidateById(match.CandidateId, match.PortalId);

            return await _messageJobdsChatService.GetConversationsAsync(filter, portalConfig.idapp, new PagerFilter() { PageNumber = 1, PageSize = PAGE_SIZE_CONVERSATION_DEFAULT });
        }

        private List<MatchEntity> BuildListMatches(string ids, int offerId)
        {
            char[] delimiterChars = { '|' };
            var matchEncryptedList = ids.ToString().Split(delimiterChars);

            List<MatchEntity> matches = new List<MatchEntity>();

            foreach (var match in matchEncryptedList)
            {
                long.TryParse(_encryptionService.Decrypt(match), out long id);
                if (id > 0)
                {
                    var matchEnt = _matchService.GetByPk(id);
                    if (matchEnt.Id > 0)
                        matches.Add(matchEnt);
                }
            }

            return matches;
        }

        private string CheckData(int offerId, string ids, int folderDestiny, int folderOrigin, PortalConfig portalConfig)
        {
            if (!IsMy(_companyCredentials, offerId, portalConfig))
                return "BadOffer";

            if (folderDestiny < 0 || folderOrigin < 0)
                return "BadDestinyOrOrigin";

            if (string.IsNullOrEmpty(ids))
                return "WithOutMatches";

            return string.Empty;
        }

        private int GetMatchCandidateProcessStatusByFolder(int folderDestiny)
        {
            var newIdCandidateProcessStatus = (short)MatchCandidateProcessStatusEnum.InProcess;

            switch (folderDestiny)
            {
                case (int)CompanyOfferFolderEnum.Finalistas:
                    newIdCandidateProcessStatus = (int)MatchCandidateProcessStatusEnum.Finalist;
                    break;
                case (int)CompanyOfferFolderEnum.Descartados:
                    newIdCandidateProcessStatus = (int)MatchCandidateProcessStatusEnum.Discarded;
                    break;
            }

            return newIdCandidateProcessStatus;
        }

        private void ChangeCandidatesMatchProcessStatus(MatchEntity match, int folderDestiny, PortalConfig portalConfig)
        {
            int newMatchProcessStatusId = GetMatchCandidateProcessStatusByFolder(folderDestiny);

            _matchService.ChangeCandidateProcessStatus(new CandidateProcessStatusDTO(_offer.idoffer, match.CurriculumId,
                newMatchProcessStatusId, portalConfig.PortalId, _offer.idcompany, match.Id, match.CandidateId),
                new TimelineExtraParamsChangeMatchDTO(_offer.title, _companyCredentials.CompanyName, portalConfig.idapp,
                    portalConfig.CurrentIntPortal, portalConfig.PortalId, DictionaryService.GetDictionaryValue(DictionaryEnum.CITY, _offer.idcity.ToString(), portalConfig.PortalId)));
        }

        private void FillMatchesData(HomeMatchesDataModel homeMatchesDataModel, SearchResultDTO<MatchElasticEntity> matchesResultElastic, PortalConfig portalConfig)
        {
            homeMatchesDataModel.Matches = MatchElasticEntityToMatchDataModelMapping(matchesResultElastic.Matches, portalConfig, homeMatchesDataModel);
            homeMatchesDataModel.Pager.PageSizeSelected = portalConfig.AEPortalConfig.MatchesPageSize;
            homeMatchesDataModel.Pager.TotalRows = matchesResultElastic.Total;
            var secondPartTotalCandidates = homeMatchesDataModel.TotalMatches != 1 ? PageLiteralsHelper.GetLiteral("CANDIDATE_COUNTER", PageId, portalConfig) : PageLiteralsHelper.GetLiteral("CANDIDATE_COUNTER1", PageId, portalConfig);
            homeMatchesDataModel.TotalCandidates = $"{homeMatchesDataModel.TotalMatches} {secondPartTotalCandidates}";
        }

        private int GetAgeFromDateBirth(DateTime dateBirth)
        {
            if (dateBirth == DateTime.MinValue) return 0;
            var year = DateTime.Now.Year - dateBirth.Year;
            var month = DateTime.Now.Month - dateBirth.Month;
            if (month < 0) year--;
            else if (month == 0)
            {
                var day = DateTime.Now.Day - dateBirth.Day;
                if (day < 0) year--;
            }
            return year;
        }

        private List<MatchDataModel> MatchElasticEntityToMatchDataModelMapping(List<MatchElasticEntity> matches, PortalConfig portalConfig, HomeMatchesDataModel homeMatchesDataModel)
        {
            var offerIntegrator = _offer.Integrations.FirstOrDefault();

            return matches.Select(m => new MatchDataModel()
            {
                AdequacyPoints = (short)m.adequacy_points,
                Age = GetAgeFromDateBirth(m.DateBirth),
                AgeRangeId = m.AgeRangeId,
                AppliedOn = m.appliedOn,
                AppliedOnToShow = AppliedOnToText(m.appliedOn, portalConfig),
                AutoFilterStatus = m.autoFiltersStatus,
                CandidateFileId = m.fileId,
                CandidateFileIdEncrypted = _encryptionService.Encrypt(m.fileId.ToString()),
                CandidateId = (int)m.candidateId,
                CandidateProcessId = m.processStatusId,
                CandidateStatusId = m.CandidateStatus,
                CategoryIds = m.categoryIds,
                CityId = m.cityId,
                CompanyFolderId = m.folderId,
                CommentCount = m.CommentCount,
                CompanyFolderIdEncrypted = _encryptionService.Encrypt(m.folderId.ToString()),
                CompanyFolderPreviousId = m.PreviousFolderId,
                CompanyId = m.CompanyId,
                CompletName = m.completeName,
                CurriculumId = (int)m.curriculumId,
                CurriculumIdEncrypted = _encryptionService.Encrypt(m.curriculumId.ToString()),
                DocumentNumber = m.documentNumber,
                EducationLevelId = m.educationLevelId,
                Excluding = m.excluding,
                FileExtensionId = m.fileId > 0 ? _candidateService.GetCandidateFileById(m.fileId, (int)m.candidateId, (short)m.portalId).ExtensionId : 0,
                Gender = m.genderId,
                HasOfferAdequacy = _hasAdequacy,
                HasPhoto = m.hasPhoto,
                HasRating = m.HasRating,
                Id = m.Id,
                IdEncrtypted = _encryptionService.Encrypt(m.Id.ToString()),
                IsExclusionFilter = _hasExclusion && m.autoFiltersStatus == 1,
                HasNewFreemiumChat = homeMatchesDataModel.HasNewFreemiumChat,
                IsLimitedCV = _offerProduct.GroupId == (int)ProductGroupsEnum.Freemium ? _unlimitedMatchesResultElastic.Matches.Find(x => x.Id == m.Id) == null : false,
                IsMatchFromOfferFlash = offerIntegrator != null && offerIntegrator.Isflash == 1 && m.offerFlashMatch == 1 && homeMatchesDataModel.IsOfferFlash,
                IsPremium = m.IsPremium == 0 ? false : portalConfig.AEPortalConfig.IsPremiumActive,
                IsSeen = m.viewed,
                JobOfferId = _offer.idoffer != 0 ? _offer.idoffer : m.offerId,
                JobOfferIdCt = _offer.idofferCT,
                KillerQuestionsPuntiation = _offer.has_killer_questions == 1 && m.excluding != 1 ? GetPuntuation(m) : 1,
                KQExcluding = m.excluding,
                KQScoreAvg = m.KQScoreAvg,
                KQScoreMax = m.KQAvgMax,
                LanguageIds = m.LanguageIds,
                LanguagesAndLevels = m.LanguagesAndLevels,
                LastEducation = GetLastEducationDescription(m.educationLevelId, portalConfig),
                LastEsperienceProfession = m.LastExperienceProfession,
                Name = m.Name,
                Nationality = m.nationality,
                OfferFlashMatch = m.offerFlashMatch,
                PortalId = m.portalId,
                ProcessStatusId = m.processStatusId,
                RegionId = m.regionId,
                ShowCoverLetter = m.HasCoverLetter == 0 ? false : portalConfig.AEPortalConfig.IsPremiumActive || portalConfig.AEPortalConfig.HasFreeFeatureCardPresentation,
                ShowPhone1Verification = m.Phone1VerificationStatus == 0 ? false : portalConfig.AEPortalConfig.IsPremiumActive || portalConfig.AEPortalConfig.HasFreeFeaturePhone1Verification,
                ShowVideoPresentation = m.HasCodePresentationVideo == 0 ? false : portalConfig.AEPortalConfig.IsPremiumActive || portalConfig.AEPortalConfig.HasFreeFeatureVideoPresentation,
                Surname = m.Surname,
                UpdatedOn = m.UpdatedOn,
                UrlPhoto = m.uRLPhoto,
                ViewDate = m.ViewDate,
                WhisedProfession = m.WishedProfession,
                WhisedSalary = m.wishedSalary,
                IsCandidateSuggested = _candidatesSuggested != null && _candidatesSuggested.Contains(m.candidateId),
                DistanceToOfferLocation = _geolocationService.GetDistance(homeMatchesDataModel.OfferGeoPoint, m.Location),
                HasTalentViewCompleted = m.HasTalentViewCompleted
            }).ToList();
        }

        private int GetPuntuation(MatchElasticEntity match)
        {
            if (match.KQScoreAvg == 0 || match.KQAvgMax == 0)
                return 0;

            return (int)Math.Round(match.KQScoreAvg * (double)10 / match.KQAvgMax);
        }

        private string GetLastEducationDescription(int educationLevelId, PortalConfig portalConfig)
        {
            string educationLevel = string.Empty;
            if (educationLevelId != 0)
            {
                educationLevel = DictionaryService.GetDictionaryValue(DictionaryEnum.EDUCATION_LEVEL, educationLevelId.ToString(), portalConfig.PortalId).ToString();
            }
            return string.IsNullOrWhiteSpace(educationLevel) ? PageLiteralsHelper.GetLiteral("LIT_SIN_ESPECIFICAR", PageId, portalConfig) : educationLevel;
        }

        private string AppliedOnToText(DateTime appliedOn, PortalConfig portalConfig)
        {
            String textDate = string.Empty;
            TimeSpan t = portalConfig.CurrentDateTimePortal.Date - appliedOn.Date;
            int days = Convert.ToInt32(t.TotalDays);

            if (days > 31)
            {
                textDate = PageLiteralsHelper.GetLiteral("LIT_MAS_1_MES", PageId, portalConfig);
            }

            if (days == 0)
            {
                textDate = PageLiteralsHelper.GetLiteral("LIT_HOY", PageId, portalConfig);
            }
            else if (days == 1)
            {
                textDate = PageLiteralsHelper.GetLiteral("LIT_AYER", PageId, portalConfig);
            }
            else
            {
                textDate = $"{PageLiteralsHelper.GetLiteral("LIT_HACE", PageId, portalConfig)}{days.ToString()}{PageLiteralsHelper.GetLiteral("LIT_DIAS", PageId, portalConfig)}";
            }

            return textDate;
        }

        private ActionResult GetHomeModel(string oi, string cf, HomeMatchesDataModel homeMatchesDataModel, bool remember, PortalConfig portalConfig, bool applyFilterSaved = false, bool newApplies = false)
        {
            ModelState.Clear();
            if (string.IsNullOrEmpty(oi) && string.IsNullOrEmpty(cf))
                return RedirectToAction("Index", "Company");

            Int32.TryParse(_encryptionService.Decrypt(oi), out var _idOffer);
            Int32.TryParse(_encryptionService.Decrypt(cf), out var _currentFolder);

            LoadData(_idOffer, portalConfig);
            LoadFeatures(portalConfig);

            var failInSecurity = LoadSecurity(_idOffer, portalConfig, _currentFolder);
            if (failInSecurity.Any())
                return RedirectToAction(failInSecurity.FirstOrDefault().Key, failInSecurity.FirstOrDefault().Value);

            LoadSecurityExpiredOfferComplete(portalConfig);
            var companyCredentials = SecurityHelper.GetCompanyCredentials();

            GetFacetFolders(portalConfig);

            homeMatchesDataModel.IsFromBasicOffer = _offerProduct.GroupId == (short)ProductGroupsEnum.Freemium;
            homeMatchesDataModel.EncryptedOfferSubGroupId = EncryptationHelper.Encrypt(_offerProduct.SubGroupId.ToString());
            homeMatchesDataModel.MultifiltersDataModel.IsAOfferPack = ProductService.GetProductGroupIdByProductId(companyCredentials.PortalId, _offer.Integrations.FirstOrDefault()?.idproduct ?? 0) == (short)ProductGroupsEnum.Packs;
            homeMatchesDataModel.HasFolders = _hasFolders;
            homeMatchesDataModel.OfferIdCtEncrypted = _offer.idofferCTencrypted;
            homeMatchesDataModel.ActionNotAllowedByMembership = GetActionNotAllowedPopUpByMembership(portalConfig);
            homeMatchesDataModel.ProdCWConvertToCompleteFolders = _landingProductsService.ManageCWFeatures((int)ProductAmbitEnum.CVFolders, companyCredentials.IdCompany, portalConfig);
            homeMatchesDataModel.ProdCWConvertToCompleteSaveFilters = _landingProductsService.ManageCWFeatures((int)ProductAmbitEnum.SaveFilter, companyCredentials.IdCompany, portalConfig);
            homeMatchesDataModel.ProdCWConvertToCompleteCVsFreemium = _landingProductsService.ManageCWFeatures((int)ProductAmbitEnum.CVsFreemium, companyCredentials.IdCompany, portalConfig);
            homeMatchesDataModel.ProdCWConvertToCompleteFilters = _landingProductsService.ManageCWFeatures((int)ProductAmbitEnum.FiltersCVInscritos, companyCredentials.IdCompany, portalConfig);
            homeMatchesDataModel.ProdCWConvertToCompleteCVSearch = _landingProductsService.ManageCWFeatures((int)ProductAmbitEnum.CvBBDD, companyCredentials.IdCompany, portalConfig);
            homeMatchesDataModel.BtnConvertToCompleteEncryptedName = _encryptionService.Encrypt(((int)TpvButtonOriginEnum.OfferMatchesListConvertToPack).ToString());
            homeMatchesDataModel.HasNewFreemiumChat = HasNewFreemiumChat(portalConfig, companyCredentials.IdCompany);

            var companyProductEntity = CompanyProductService.GetByIdCompany(_companyCredentials.IdCompany, _companyCredentials.PortalId, OfferIntegratorEnum.CompuTrabajo);

            homeMatchesDataModel.IsMemberShip = (companyProductEntity.GroupId == (short)ProductGroupsEnum.Membership);

            if (_currentFolder == (short)CompanyOfferFolderEnum.BBDD)
            {
                homeMatchesDataModel.IsNuggetCv = ConfigurationManager.AppSettings["SEARCH_CANDIDATES_BY_API"] != null && ConfigurationManager.AppSettings["SEARCH_CANDIDATES_BY_API"].ToLower() == "true";
                homeMatchesDataModel.IsNewFiltersActive = ConfigurationManager.AppSettings["NEW_FILTERS_CVS"] != null && ConfigurationManager.AppSettings["NEW_FILTERS_CVS"].ToLower() == "true";
                homeMatchesDataModel.MultifiltersDataModel.OrderDropDownList = GetOrderDropDownList(portalConfig);
            }

            DeleteMultifilterTags(homeMatchesDataModel.MultifiltersDataModel, homeMatchesDataModel.IsNuggetCv, portalConfig);
            FillMultifilterPropertiesForCvDetail(homeMatchesDataModel.MultifiltersDataModel);

            if (homeMatchesDataModel.MultifiltersDataModel.IsAOfferPack)
            {
                homeMatchesDataModel.FolderBBDDEncrypted = _encryptionService.Encrypt(((short)CompanyOfferFolderEnum.BBDD).ToString());

                if (_currentFolder == (short)CompanyOfferFolderEnum.BBDD)
                    SetCvsDataModel(homeMatchesDataModel, _offer, _currentFolder, companyCredentials, portalConfig);
            }

            homeMatchesDataModel.HasNewMatchesNotificationsFeature = (!homeMatchesDataModel.IsMemberShip)
                && CompanyProductService.HasActiveFeatureByProduct(new SearchCompanyProductsByCompanyDTO(companyCredentials.IdCompany, ProductAmbitEnum.NewMatchesPushNotifications, portalConfig.PortalId, ProductEnum.FreemiumService))
                && portalConfig.NewMatchesPushNotifications
                && companyCredentials.TestAB == TestABEnum.A;

            SetMatchesDataModel(homeMatchesDataModel, applyFilterSaved, _idOffer, _currentFolder, companyCredentials, portalConfig, newApplies);

            if (ConfigurationManager.AppSettings["RECALCULATE_MATCH_COUNTERS_MULTIMOVE"] == "true"
                && !homeMatchesDataModel.MultifiltersDataModel.IsFiltered)
                RecalculateCounters(_idOffer, homeMatchesDataModel.Folders, homeMatchesDataModel.TotalMatches, portalConfig);

            if (ConfigurationManager.AppSettings["RECALCULATE_MATCH_COUNTERS_MULTIMOVE"] == "true"
                && homeMatchesDataModel.MultifiltersDataModel.IsMatchesVisualitzed == 0
                && !homeMatchesDataModel.MultifiltersDataModel.FilterVisualizatedIsFilteredWithOtherFilters
                && homeMatchesDataModel.FolderSelected == (short)CompanyOfferFolderEnum.Recibidos)
            {
                var offer = OfferService.GetParentAndIntegratorOfferByPk(_offer.idofferCT, _offer.idportal);

                if (homeMatchesDataModel.MultifiltersDataModel.TotalFilterNumber != offer.TotalNewNotViewed)
                {
                    var newTotal = _matchService.GetTotalNotViewedByIdOffer(_offer.idofferCT);
                    OfferService.UpdateOfferNewTotalsNotViewedByNumber(_offer.idofferCT, _offer.idportal, newTotal);
                }
            }

            homeMatchesDataModel.MultifiltersDataModel.SearchText = string.Empty;
            homeMatchesDataModel.MultifiltersDataModel.SearchNit = string.Empty;
            homeMatchesDataModel.MultifiltersDataModel.SearchName = string.Empty;

            homeMatchesDataModel.MultifiltersDataModel.OfferIdEncrypted = oi;
            homeMatchesDataModel.MultifiltersDataModel.FolderSelectedEncrypted = cf;
            homeMatchesDataModel.MultifiltersDataModel.HasSaveFilters = _hasSaveFilters;

            homeMatchesDataModel.MultifiltersDataModel.IsFromBasicOffer = homeMatchesDataModel.IsFromBasicOffer;

            SetCompetenceTestUsageData(homeMatchesDataModel, portalConfig);
            //TODO SUPONGO QUE SE DEBERA HACER CMO EL TEST DE COMPETENCIAS Y CREAR KPIS

            MapToDataLayer(homeMatchesDataModel, companyCredentials, portalConfig.PortalId.ToString());

            if (remember)
                _sessionService.Remember(homeMatchesDataModel);
            return View(homeMatchesDataModel);
        }

        private bool HasNewFreemiumChat(PortalConfig portalConfig, int idCompany)
        {
            return portalConfig.AEPortalConfig.EnableProgressiveChatFreemium
                && CompanyProductService.HasActiveFeatureByProduct(new SearchCompanyProductsByCompanyDTO(idCompany, ProductAmbitEnum.ChatContactFreemium, portalConfig.PortalId, ProductEnum.FreemiumService));
        }

        private void SetCompetenceTestUsageData(HomeMatchesDataModel homeMatchesDataModel, PortalConfig portalConfig)
        {
            if (!homeMatchesDataModel.ActivelySentByUser) return;

            var hasCompetenceTest = homeMatchesDataModel.MultifiltersDataModel.HasCompetenceTest == 1
                ? true
                : homeMatchesDataModel.MultifiltersDataModel.HasCompetenceTest == 0
                    ? false
                    : default(bool?);

            var searchedByCompetence = !string.IsNullOrEmpty(homeMatchesDataModel.MultifiltersDataModel.CompetenceTestKey);

            if (hasCompetenceTest == null)
            {
                _kpiService.Add((short)KpiEnum.MATCHES_FILTERED_BY_COMPETENCES_TEST_TAKEN_OR_NOT, portalConfig.PortalId);
            }
            else if (hasCompetenceTest == true)
            {
                _kpiService.Add((short)KpiEnum.MATCHES_FILTERED_BY_COMPETENCES_TEST_TAKEN, portalConfig.PortalId);

                if (searchedByCompetence)
                {
                    _kpiService.Add((short)KpiEnum.MATCHES_FILTERED_BY_COMPETENCES_TEST_KEY, portalConfig.PortalId);
                }
            }
            else
            {
                _kpiService.Add((short)KpiEnum.MATCHES_FILTERED_BY_COMPETENCES_TEST_NOT_TAKEN, portalConfig.PortalId);
            }
        }

        private void RecalculateCounters(int idOffer, List<FoldersDataModel> folders, int totalMatches, PortalConfig portalConfig)
        {
            var offer = OfferService.GetByPk(idOffer, portalConfig.PortalId);

            if (offer != null
                && offer.Integrations.Any()
                && offer.Integrations.Exists(f => f.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo)
                && offer.Integrations.FirstOrDefault(f => f.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).total_applied != totalMatches)
            {
                if (totalMatches != 0)
                {
                    OfferService.RecalculateTotalApplies(new List<OfferStageDeltaEntity>() {
                        new OfferStageDeltaEntity(){
                            IdIntegrator = (short) OfferIntegratorEnum.CompuTrabajo,
                            IdOffer = offer.idofferCT,
                            IdPortal = offer.idportal,
                            Total = totalMatches
                        }
                    });

                    if (_hasFolders && folders.Any())
                    {
                        _foldersService.RecalculateFolders(offer.idofferCT, offer.idportal, offer.idcompany, totalMatches,
                            folders.Exists(e => e.Id == (short)CompanyOfferFolderEnum.Recibidos) ? folders.FirstOrDefault(e => e.Id == (short)CompanyOfferFolderEnum.Recibidos).Count : 0,
                            folders.Exists(e => e.Id == (short)CompanyOfferFolderEnum.Seleccionados) ? folders.FirstOrDefault(e => e.Id == (short)CompanyOfferFolderEnum.Seleccionados).Count : 0,
                            folders.Exists(e => e.Id == (short)CompanyOfferFolderEnum.Finalistas) ? folders.FirstOrDefault(e => e.Id == (short)CompanyOfferFolderEnum.Finalistas).Count : 0,
                            folders.Exists(e => e.Id == (short)CompanyOfferFolderEnum.Descartados) ? folders.FirstOrDefault(e => e.Id == (short)CompanyOfferFolderEnum.Descartados).Count : 0,
                            folders.Exists(e => e.Id == (short)CompanyCustomFoldersMatchEnum.Folder1) ? folders.FirstOrDefault(e => e.Id == (short)CompanyCustomFoldersMatchEnum.Folder1).Count : 0,
                            folders.Exists(e => e.Id == (short)CompanyCustomFoldersMatchEnum.Folder2) ? folders.FirstOrDefault(e => e.Id == (short)CompanyCustomFoldersMatchEnum.Folder2).Count : 0
                            );
                    }
                }
                else if (portalConfig.AEPortalConfig.TraceDifferentTotalMatchesExceptions)
                {
                    ExceptionPublisherService.Publish(new Exception($"El total de matches en elastic es 0 mientras que en base de datos de ofertas no. (OfferId:{offer.idoffer}, CompanyId: {offer.idcompany}, PortalId: {offer.idportal})"), "CompanyMatchesController", "RecalculateCounters", false, null, offer.idportal);
                }
            }
        }

        private List<SelectListItem> GetOrderDropDownList(PortalConfig portalConfig)
        {
            var list = new List<SelectListItem>();

            list.Add(new SelectListItem() { Text = PageLiteralsHelper.GetLiteral("RELEVANCY", PageId, portalConfig), Value = ((short)CvOrderEnum.Relevance).ToString() });
            if (portalConfig.AEPortalConfig.IsPremiumActive) list.Add(new SelectListItem() { Text = PageLiteralsHelper.GetLiteral("DATELASTUP", PageId, portalConfig), Value = ((short)CvOrderEnum.DateLastUp).ToString() });
            list.Add(new SelectListItem() { Text = PageLiteralsHelper.GetLiteral("DATECREATION", PageId, portalConfig), Value = ((short)CvOrderEnum.UpdateDate).ToString() });

            return list;

        }

        private void GetFacetFolders(PortalConfig portalConfig)
        {
            _facets = _matchService.GetFacetsFolderByElastic(new SearchFilterDTO()
            {
                Facets = new List<string>
                (new string[] { FacetsByMatchEnum.folderId.ToString() }),
                IdPortal = portalConfig.PortalId,
                IdOffer = _offer.idofferCT,
                IdCompany = _offer.idcompany
            });
        }

        private void SetCvsDataModel(HomeMatchesDataModel homeMatchesDataModel, OfferEntity offer, int currentFolder, CompanyCredentials companyCredentials, PortalConfig portalConfig)
        {
            var searchFilter = GetCandidateElasticFilter(homeMatchesDataModel.MultifiltersDataModel, portalConfig, currentFolder, companyCredentials, homeMatchesDataModel.Pager.PageSelected, homeMatchesDataModel.IsNuggetCv, homeMatchesDataModel.IsNewFiltersActive);
            homeMatchesDataModel.MultifiltersDataModel.IsFolderBBDD = true;
            var candidates = homeMatchesDataModel.MultifiltersDataModel.IsMySelection && !searchFilter.CandidatesIds.Any() ? new SearchCandidate.SearchResult<SearchCandidate.CandidateReadSearch>() : GetElasticCandidatesItems(searchFilter, homeMatchesDataModel);
            homeMatchesDataModel.Cvs = CandidateReadSearchDataModelMapping.MappingCandidateReadSearchDTOToCandidateReadSearchDataModel(candidates.Candidates, portalConfig);
            homeMatchesDataModel.Pager.TotalRows = candidates.Total;
            homeMatchesDataModel.Pager.PageSizeSelected = portalConfig.AEPortalConfig.MatchesPageSize;
            var secondPartTotalCandidates = candidates.Total != 1 ? PageLiteralsHelper.GetLiteral("CANDIDATE_COUNTER_CVS", PageId, portalConfig) : PageLiteralsHelper.GetLiteral("CANDIDATE_COUNTER_CV", PageId, portalConfig);
            homeMatchesDataModel.TotalCandidates = $"{StringToolsHelper.ChangeGroupPointForDecimalPoint(candidates.Total.ToString("N0"), portalConfig.PortalId)} {secondPartTotalCandidates}";

            var BBDDcvProductToConsume = CompanyProductService.GetBBDDcvProductToConsume(offer, companyCredentials, portalConfig);
            if (BBDDcvProductToConsume.ProductId > 0)
            {
                var cvBBDDFeature = BBDDcvProductToConsume.Features.Find(x => x.AmbitId == (short)ProductAmbitEnum.CvBBDD);

                if (cvBBDDFeature != null)
                {
                    if (BBDDcvProductToConsume.GroupId == (short)ProductGroupEnum.Packs)
                    {
                        var availabeUnits = cvBBDDFeature.IsUnlimited ? int.MaxValue : cvBBDDFeature.InitialUnits - GetCvsByOffer(offer.idofferCT, portalConfig.PortalId);
                        homeMatchesDataModel.NumberCvsAvailable = availabeUnits >= 0 ? availabeUnits : 0;
                    }
                    else
                    {
                        homeMatchesDataModel.NumberCvsAvailable = cvBBDDFeature.AvailableUnits;
                    }
                }
                homeMatchesDataModel.NumberCvsInMySelections = GetCvsByOffer().Count();
            }

            PopulateFilterListByCandidates(homeMatchesDataModel.MultifiltersDataModel, portalConfig, companyCredentials, currentFolder, searchFilter, homeMatchesDataModel.Pager, candidates, homeMatchesDataModel.IsNuggetCv, homeMatchesDataModel.IsNewFiltersActive);
        }

        private SearchCandidate.SearchResult<SearchCandidate.CandidateReadSearch> GetElasticCandidatesItems(SearchFilter searchFilter, HomeMatchesDataModel homeMatchesDataModel)
        {
            if (homeMatchesDataModel.IsNuggetCv)
            {
                var searchResult = _elasticSearchCandidateService.GetCandidates(searchFilter);

                if (searchResult != null
                    && searchResult.Candidates.Any())
                {
                    return searchResult;
                }
            }

            return new SearchResult<CandidateReadSearch>();
        }

        private void DeleteMultifilterTags(MultifiltersDataModel multifiltersDataModel, bool isNuggetCv, PortalConfig portalConfig)
        {

            int idToDelete = 0;
            if (!string.IsNullOrEmpty(multifiltersDataModel.SelectedCitiesDelete))
            {
                int.TryParse(multifiltersDataModel.SelectedCitiesDelete, out idToDelete);
                multifiltersDataModel.SelectedCities.Remove(idToDelete);
                multifiltersDataModel.SelectedCitiesDelete = string.Empty;
            }

            if (!string.IsNullOrEmpty(multifiltersDataModel.HasPhotoDelete))
            {
                multifiltersDataModel.HasPhoto = 0;
                multifiltersDataModel.HasPhotoDelete = string.Empty;
            }

            if (!string.IsNullOrEmpty(multifiltersDataModel.RatingDelete))
            {
                multifiltersDataModel.Rating = null;
                multifiltersDataModel.RatingDelete = string.Empty;
            }

            if (!string.IsNullOrEmpty(multifiltersDataModel.CommentsDelete))
            {
                multifiltersDataModel.Comments = 0;
                multifiltersDataModel.CommentsDelete = string.Empty;
            }

            if (!string.IsNullOrEmpty(multifiltersDataModel.MaxSalaryDelete))
            {
                multifiltersDataModel.MaxSalary = null;
                multifiltersDataModel.MaxSalaryDelete = string.Empty;
            }

            if (!string.IsNullOrEmpty(multifiltersDataModel.MinSalaryDelete))
            {
                multifiltersDataModel.MinSalary = null;
                multifiltersDataModel.MinSalaryDelete = string.Empty;
            }

            if (!string.IsNullOrEmpty(multifiltersDataModel.SelectedLanguageDelete))
            {
                multifiltersDataModel.SelectedLanguage = null;
                multifiltersDataModel.SelectedLanguageLevel = null;
                multifiltersDataModel.SelectedLanguageDelete = string.Empty;
            }

            if (!string.IsNullOrEmpty(multifiltersDataModel.GenderDelete))
            {
                multifiltersDataModel.Gender = 0;
                multifiltersDataModel.GenderDelete = string.Empty;
            }

            if (!string.IsNullOrEmpty(multifiltersDataModel.MinAgeDelete))
            {
                multifiltersDataModel.MinAge = null;
                multifiltersDataModel.MinAgeDelete = string.Empty;
            }

            if (!string.IsNullOrEmpty(multifiltersDataModel.MaxAgeDelete))
            {
                multifiltersDataModel.MaxAge = null;
                multifiltersDataModel.MaxAgeDelete = string.Empty;
            }

            if (!string.IsNullOrEmpty(multifiltersDataModel.IsMatchesVisualitzedDelete))
            {
                multifiltersDataModel.IsMatchesVisualitzed = -1;
                multifiltersDataModel.IsMatchesVisualitzedDelete = string.Empty;
            }

            if (!string.IsNullOrEmpty(multifiltersDataModel.ExclusionDelete))
            {
                multifiltersDataModel.Exclusion = -1;
                multifiltersDataModel.ExclusionDelete = string.Empty;
            }

            if (!string.IsNullOrEmpty(multifiltersDataModel.SelectedStudyLevelDelete))
            {
                int.TryParse(multifiltersDataModel.SelectedStudyLevelDelete, out idToDelete);
                multifiltersDataModel.SelectedStudyLevel.Remove(idToDelete);
                multifiltersDataModel.SelectedStudyLevelDelete = string.Empty;
            }

            if (!string.IsNullOrEmpty(multifiltersDataModel.SelectedStudyAndStatusByLevelDelete))
            {
                int.TryParse(multifiltersDataModel.SelectedStudyAndStatusByLevelDelete, out idToDelete);
                multifiltersDataModel.SelectedStudyAndStatusByLevel.Remove(idToDelete);
                multifiltersDataModel.SelectedStudyAndStatusByLevelDelete = string.Empty;
            }

            if (!string.IsNullOrEmpty(multifiltersDataModel.SelectedStudyAndStatusByStatusDelete))
            {
                int.TryParse(multifiltersDataModel.SelectedStudyAndStatusByStatusDelete, out idToDelete);
                multifiltersDataModel.SelectedStudyAndStatusByStatus.Remove(idToDelete);
                multifiltersDataModel.SelectedStudyAndStatusByStatusDelete = string.Empty;
            }

            if (!string.IsNullOrEmpty(multifiltersDataModel.SelectedNationatilityDelete))
            {
                int.TryParse(multifiltersDataModel.SelectedNationatilityDelete, out idToDelete);
                multifiltersDataModel.SelectedNationatility.Remove(idToDelete);
                multifiltersDataModel.SelectedNationatilityDelete = string.Empty;
            }

            if (!string.IsNullOrEmpty(multifiltersDataModel.SelectedLocalizationDelete))
            {
                int.TryParse(multifiltersDataModel.SelectedLocalizationDelete, out idToDelete);
                multifiltersDataModel.SelectedLocalization.Remove(idToDelete);
                multifiltersDataModel.SelectedLocalizationDelete = string.Empty;
            }

            if (!string.IsNullOrEmpty(multifiltersDataModel.SelectedProfesionalCategoriesDelete))
            {
                int.TryParse(multifiltersDataModel.SelectedProfesionalCategoriesDelete, out idToDelete);
                multifiltersDataModel.SelectedProfesionalCategories.Remove(idToDelete);
                multifiltersDataModel.SelectedProfesionalCategoriesDelete = string.Empty;
            }

            if (!string.IsNullOrEmpty(multifiltersDataModel.SelectedProfesionalCategoriesDelete))
            {
                int.TryParse(multifiltersDataModel.SelectedProfesionalCategoriesDelete, out idToDelete);
                multifiltersDataModel.SelectedProfesionalCategories.Remove(idToDelete);
                multifiltersDataModel.SelectedProfesionalCategoriesDelete = string.Empty;
            }

            if (!string.IsNullOrEmpty(multifiltersDataModel.HasDisabilityStatusDelete))
            {

                multifiltersDataModel.HasDisabilityStatusDelete = string.Empty;
                multifiltersDataModel.HasDisability = (short)HasDisabilityStatusEnum.None;
            }

            if (!string.IsNullOrEmpty(multifiltersDataModel.ApplicationDateFromDelete))
            {

                multifiltersDataModel.ApplicationDateFromDelete = string.Empty;
                multifiltersDataModel.ApplicationDateFrom = (short)ApplicationDateFromEnum.None;
            }


            if (!string.IsNullOrEmpty(multifiltersDataModel.ExperienceProfessionDelete))
            {
                multifiltersDataModel.ExperienceProfessionDelete = string.Empty;
                multifiltersDataModel.ExperienceProfession = string.Empty;
            }

            if (!string.IsNullOrEmpty(multifiltersDataModel.CareerDelete))
            {
                multifiltersDataModel.CareerDelete = string.Empty;
                multifiltersDataModel.Career = string.Empty;
            }

            if (!string.IsNullOrEmpty(multifiltersDataModel.SelectedStudyAndStatusByStatusDelete))
            {
                int.TryParse(multifiltersDataModel.SelectedStudyAndStatusByStatusDelete, out idToDelete);
                multifiltersDataModel.SelectedStudyAndStatusByStatus.Remove(idToDelete);
                multifiltersDataModel.SelectedStudyAndStatusByStatusDelete = string.Empty;
            }

            if (!string.IsNullOrEmpty(multifiltersDataModel.SelectedStudyAndStatusByLevelDelete))
            {
                int.TryParse(multifiltersDataModel.SelectedStudyAndStatusByLevelDelete, out idToDelete);
                multifiltersDataModel.SelectedStudyAndStatusByLevel.Remove(idToDelete);
                multifiltersDataModel.SelectedStudyAndStatusByLevelDelete = string.Empty;
            }

            if (!string.IsNullOrEmpty(multifiltersDataModel.HasCompetenceTestDelete))
            {

                multifiltersDataModel.HasCompetenceTestDelete = string.Empty;
                multifiltersDataModel.HasCompetenceTest = (short)HasCompetenceTestEnum.None;
            }

            if (!string.IsNullOrEmpty(multifiltersDataModel.CompetenceTestKeyDelete))
            {
                multifiltersDataModel.CompetenceTestKeyDelete = string.Empty;
                multifiltersDataModel.CompetenceTestKey = string.Empty;
                multifiltersDataModel.CompetenceTestMin = 0;
                multifiltersDataModel.CompetenceTestMax = 100;
            }

            if (!string.IsNullOrEmpty(multifiltersDataModel.HasAdequacyDelete))
            {

                multifiltersDataModel.HasAdequacyDelete = string.Empty;
                multifiltersDataModel.HasAdequacy = (short)HasAdequacyEnum.None;
                multifiltersDataModel.AdequacyMin = 0;
                multifiltersDataModel.AdequacyMax = 100;
            }

            if (!string.IsNullOrEmpty(multifiltersDataModel.HasTalentViewTestDelete))
            {
                multifiltersDataModel.HasTalentViewTest = (short)HasTalentViewTestEnum.None;
                multifiltersDataModel.HasTalentViewTestDelete = string.Empty;
            }

            if (!string.IsNullOrEmpty(multifiltersDataModel.DistanceDelete))
            {
                multifiltersDataModel.Distance = 0;
            }

            if (isNuggetCv)
            {
                if (!string.IsNullOrEmpty(multifiltersDataModel.IsWorkingDelete))
                {

                    multifiltersDataModel.IsWorkingDelete = string.Empty;
                    multifiltersDataModel.IsWorking = (short)WorkingStatusEnum.None;
                }

                if (!string.IsNullOrEmpty(multifiltersDataModel.NameUniversityDelete))
                {

                    multifiltersDataModel.NameUniversityDelete = string.Empty;
                    multifiltersDataModel.NameUniversity = string.Empty;
                }

                if (!string.IsNullOrEmpty(multifiltersDataModel.MinYearsExperienceDelete))
                {
                    multifiltersDataModel.MinYearsExperienceDelete = string.Empty;
                    multifiltersDataModel.MinYearsExperience = null;
                }

                if (!string.IsNullOrEmpty(multifiltersDataModel.MaxYearsExperienceDelete))
                {
                    multifiltersDataModel.MaxYearsExperienceDelete = string.Empty;
                    multifiltersDataModel.MaxYearsExperience = null;
                }

                if (!string.IsNullOrEmpty(multifiltersDataModel.CompanyNameDelete))
                {

                    multifiltersDataModel.CompanyNameDelete = string.Empty;
                    multifiltersDataModel.CompanyName = string.Empty;
                }

                if (!string.IsNullOrEmpty(multifiltersDataModel.StudyingStatusDelete))
                {

                    multifiltersDataModel.StudyingStatusDelete = string.Empty;
                    multifiltersDataModel.IsStudying = (short)StudyingStatusEnum.None;
                }

                if (!string.IsNullOrEmpty(multifiltersDataModel.StudyStatusDelete))
                {

                    multifiltersDataModel.StudyStatusDelete = string.Empty;
                    multifiltersDataModel.StudyStatus = (short)StudyingStatusEnum.None;
                }

            }

            List<string> multiSearch = new List<string>();
            if (!string.IsNullOrEmpty(multifiltersDataModel.MultiSearchText))
                multiSearch.Add(multifiltersDataModel.MultiSearchText);
            if (!string.IsNullOrEmpty(multifiltersDataModel.MultiSearchDelete))
            {
                if (multifiltersDataModel.MultiSearchText != null)
                {
                    multiSearch = multifiltersDataModel.MultiSearchText.Split(new string[] { " AND " }, StringSplitOptions.None).ToList<string>();
                    multiSearch.Remove(multifiltersDataModel.MultiSearchDelete);
                }
            }
            multifiltersDataModel.MultiSearchDelete = string.Empty;

            if (!string.IsNullOrEmpty(multifiltersDataModel.SearchText))
                multiSearch.Add(multifiltersDataModel.SearchText);

            multifiltersDataModel.SearchText = string.Join(" AND ", multiSearch);
            multifiltersDataModel.MultiSearchText = string.Join(" AND ", multiSearch);

            List<string> multiSearchNit = new List<string>();
            if (!string.IsNullOrEmpty(multifiltersDataModel.MultiSearchNitText))
                multiSearchNit.Add(multifiltersDataModel.MultiSearchNitText);
            if (!string.IsNullOrEmpty(multifiltersDataModel.MultiSearchNitDelete))
            {
                if (multifiltersDataModel.MultiSearchNitText != null)
                {
                    multiSearchNit = multifiltersDataModel.MultiSearchNitText.Split(new string[] { " OR " }, StringSplitOptions.None).ToList<string>();
                    multiSearchNit.Remove(multifiltersDataModel.MultiSearchNitDelete);
                }
            }
            multifiltersDataModel.MultiSearchNitDelete = string.Empty;

            if (!string.IsNullOrEmpty(multifiltersDataModel.SearchNit))
                multiSearchNit.Add(multifiltersDataModel.SearchNit);

            multifiltersDataModel.SearchNit = string.Join(" OR ", multiSearchNit);
            multifiltersDataModel.MultiSearchNitText = string.Join(" OR ", multiSearchNit);

            List<string> multiSearchName = new List<string>();
            if (!string.IsNullOrEmpty(multifiltersDataModel.MultiSearchNameText))
                multiSearchName.Add(multifiltersDataModel.MultiSearchNameText);
            if (!string.IsNullOrEmpty(multifiltersDataModel.MultiSearchNameDelete))
            {
                if (multifiltersDataModel.MultiSearchNameText != null)
                {
                    multiSearchName = multifiltersDataModel.MultiSearchNameText.Split(new string[] { " OR " }, StringSplitOptions.None).ToList<string>();
                    multiSearchName.Remove(multifiltersDataModel.MultiSearchNameDelete);
                }
            }
            multifiltersDataModel.MultiSearchNameDelete = string.Empty;

            if (!string.IsNullOrEmpty(multifiltersDataModel.SearchName))
                multiSearchName.Add(multifiltersDataModel.SearchName);

            var formatedCompleteNameList = new List<string>();

            if (portalConfig.AEPortalConfig.IsNewMatchElasticSearch)
            {
                formatedCompleteNameList = multiSearchName.Select(s => $"(*{s}*)").ToList();
            }
            else
            {
                formatedCompleteNameList = multiSearchName.Select(s => $"({s})").ToList();
            }

            multifiltersDataModel.SearchName = string.Join(" OR ", formatedCompleteNameList);
            multifiltersDataModel.MultiSearchNameText = string.Join(" OR ", multiSearchName);
        }

        private void FillMultifilterPropertiesForCvDetail(MultifiltersDataModel multifiltersDataModel)
        {

            multifiltersDataModel.ListSearchName = !string.IsNullOrEmpty(multifiltersDataModel.SearchText) ? _encryptionService.Encrypt(multifiltersDataModel.SearchText.Replace(" AND ", ","))
                                                                                                          : string.Empty;
            multifiltersDataModel.IdsProfesionalCategoriesSelected = multifiltersDataModel.SelectedProfesionalCategories.Any()
                                                                                        ? _encryptionService.Encrypt(string.Join(SEPARATOR_SPLIT, multifiltersDataModel.SelectedProfesionalCategories))
                                                                                        : string.Empty;
            multifiltersDataModel.IdsLocalizationsSelected = multifiltersDataModel.SelectedLocalization.Any() ? _encryptionService.Encrypt(string.Join(SEPARATOR_SPLIT, multifiltersDataModel.SelectedLocalization))
                                                                                                    : string.Empty;
        }

        private void SetMatchesDataModel(HomeMatchesDataModel homeMatchesDataModel, bool applyFilterSaved, int idOffer, int _currentFolder, CompanyCredentials companyCredentials, PortalConfig portalConfig, bool newApplies = false)
        {
            bool hasFilters = false;

            if (_offerProduct.GroupId == (int)ProductGroupsEnum.Freemium)
            {
                homeMatchesDataModel.Pager.PageSizeSelected = portalConfig.AEPortalConfig.MatchesPageSize;
            }
            else
            {
                homeMatchesDataModel.Pager.PageSizeSelected = _haveOfferCompletOrMembership ? portalConfig.AEPortalConfig.MatchesPageSize : _maxCvAppliedToSee;
            }
            homeMatchesDataModel.HaveOfferCompletOrMembership = _haveOfferCompletOrMembership;

            List<CompanyFilterEntity> saveFilters = new List<CompanyFilterEntity>();

            if (_hasSaveFilters)
                saveFilters = GetCompanyFiltersFilterMatches(_offer.idofferCT);

            if (applyFilterSaved)
                SetSaveFilterToDataModel(homeMatchesDataModel.MultifiltersDataModel, saveFilters, portalConfig);

            if (_offerProduct.GroupId == (int)ProductGroupsEnum.Freemium)
            {
                SearchFilterDTO filterSearch = GetUnlimitedMatchesElasticFilter(homeMatchesDataModel.MultifiltersDataModel, portalConfig, _offer, homeMatchesDataModel.Pager, newApplies);
                _unlimitedMatchesResultElastic = LoadMatches(filterSearch);
            }

            SearchFilterDTO searchFilter = GetMatchesElasticFilter(homeMatchesDataModel.MultifiltersDataModel, portalConfig, _currentFolder, _offer, homeMatchesDataModel.Pager, newApplies);
            SearchResultDTO<MatchElasticEntity> matchesResultElastic = LoadMatches(searchFilter);

            if (_hasCandidateSuggester &&
                _currentFolder == (int)CompanyOfferFolderEnum.Recibidos &&
                portalConfig.AEPortalConfig.MinAdequacyCVsSuggested > 0 &&
                portalConfig.AEPortalConfig.MaxCVsSuggested > 0 &&
                searchFilter.OrderSearch.FirstOrDefault().Field == "adequacy_points" &&
                searchFilter.OrderSearch.FirstOrDefault().Direction == OrderSearchDirectionEnum.desc)
            {
                _candidatesSuggested = matchesResultElastic.Matches.Where(m => m.adequacy_points >= portalConfig.AEPortalConfig.MinAdequacyCVsSuggested).Take(portalConfig.AEPortalConfig.MaxCVsSuggested).Select(m => m.candidateId).ToList();
            }

            hasFilters = HasFiltersSearchFilter(searchFilter);

            homeMatchesDataModel.TotalMatchesByFolder = matchesResultElastic.Matches.Count;
            homeMatchesDataModel.ScenaryTapsType = LoadScenary();
            homeMatchesDataModel.HaveMembresyActive = _haveMembresyActive;
            homeMatchesDataModel.Folders = LoadFolders(matchesResultElastic.Total, _currentFolder, hasFilters, portalConfig);
            homeMatchesDataModel.TotalMatches = _totalMatchesInOffer;

            homeMatchesDataModel.CanSeeMatches = _showMatchesData;
            homeMatchesDataModel.OfferName = _offer.title;
            homeMatchesDataModel.OfferIdCity = _offer.idcity;
            homeMatchesDataModel.IsForeign = _offer.idcountry != portalConfig.countryId;
            homeMatchesDataModel.OfferIdEncrypted = _offer.idofferencrypted;
            homeMatchesDataModel.OfferIdCtEncrypted = _offer.idofferCTencrypted;
            homeMatchesDataModel.OfferIdPostalCode = _offer.IdPostalCode;
            homeMatchesDataModel.OfferIdCompanyProduct = _offer.Integrations.FirstOrDefault()?.idcompanyproduct ?? 0;
            homeMatchesDataModel.FolderSelected = _currentFolder;
            homeMatchesDataModel.FolderSelectedEncrypted = _encryptionService.Encrypt(_currentFolder.ToString());
            homeMatchesDataModel.MaxAppliesToSee = _maxCvAppliedToSee;
            homeMatchesDataModel.PublisedOffers = _publishedOffers;
            homeMatchesDataModel.ContactMail = _contactEmail;

            if (portalConfig.AEPortalConfig.ShowDistanceMatchesFilter
                && _offerProduct.Features.Any(f => f.AmbitId == (int)ProductAmbitEnum.OfferFilterAppliesByProximity)
                && (portalConfig.AEPortalConfig.EnablePostalCode == "1"))
            {
                homeMatchesDataModel.OfferGeoPoint = searchFilter.GeoPoint != null ? new GeoPointDTO() { Lat = searchFilter.GeoPoint.Lat, Lon = searchFilter.GeoPoint.Lon } : null;
            }

            if (homeMatchesDataModel.MultifiltersDataModel.IsFiltered)
            {
                if (!homeMatchesDataModel.MultifiltersDataModel.IsFolderBBDD)
                {
                    homeMatchesDataModel.MultifiltersDataModel.TotalFilterNumber = matchesResultElastic.Total;
                    homeMatchesDataModel.MultifiltersDataModel.TotalFilterText = matchesResultElastic.Total == 1
                        ? PageLiteralsHelper.GetLiteral("ONE_CANDIDATE_FILTRATED", PageId, portalConfig)
                        : @PageLiteralsHelper.GetLiteral("MORE_CANDIDATE_FILTRATED", PageId, portalConfig);

                    homeMatchesDataModel.MultifiltersDataModel.TotalFilter = string.Format("{0} {1}",
                                    matchesResultElastic.Total > 0 ? StringToolsHelper.ChangeGroupPointForDecimalPoint(matchesResultElastic.Total.ToString("N0"), portalConfig.PortalId) : "0",
                                    matchesResultElastic.Total == 1 ? PageLiteralsHelper.GetLiteral("ONE_CANDIDATE_FILTRATED", PageId, portalConfig) : @PageLiteralsHelper.GetLiteral("MORE_CANDIDATE_FILTRATED", PageId, portalConfig));
                }
                else
                {
                    homeMatchesDataModel.MultifiltersDataModel.TotalFilterNumber = homeMatchesDataModel.Pager.TotalRows;
                    homeMatchesDataModel.MultifiltersDataModel.TotalFilterText = homeMatchesDataModel.Pager.TotalRows == 1
                        ? PageLiteralsHelper.GetLiteral("ONE_CANDIDATE_FILTRATED", PageId, portalConfig)
                        : @PageLiteralsHelper.GetLiteral("MORE_CANDIDATE_FILTRATED", PageId, portalConfig);

                    homeMatchesDataModel.MultifiltersDataModel.TotalFilter = string.Format("{0} {1}",
                                    homeMatchesDataModel.Pager.TotalRows > 0 ? StringToolsHelper.ChangeGroupPointForDecimalPoint(homeMatchesDataModel.Pager.TotalRows.ToString("N0"), portalConfig.PortalId) : "0",
                                    homeMatchesDataModel.Pager.TotalRows == 1 ? PageLiteralsHelper.GetLiteral("ONE_CANDIDATE_FILTRATED", PageId, portalConfig) : @PageLiteralsHelper.GetLiteral("MORE_CANDIDATE_FILTRATED", PageId, portalConfig));

                }
            }

            if (!homeMatchesDataModel.Cvs.Any() && !homeMatchesDataModel.MultifiltersDataModel.IsFolderBBDD)
            {
                var offerFeature = _companyProduct.Features.FirstOrDefault(f => f.AmbitId == (int)ProductAmbitEnum.Offer);
                homeMatchesDataModel.CanConvertComplete = _companyProduct.GroupId == (int)ProductGroupsEnum.Packs && offerFeature != null && offerFeature.AvailableUnits > 0;

                if (matchesResultElastic.Matches.Any())
                {
                    ChangeMatchProcessStatus(matchesResultElastic, companyCredentials, portalConfig);
                    if (homeMatchesDataModel.HasNewMatchesNotificationsFeature)
                    {
                        SendTotalNewDecreaserToRabbit(portalConfig, idOffer, matchesResultElastic);
                    }

                    CanCreateChat(_currentFolder, portalConfig);
                }

                homeMatchesDataModel.HasAdequacy = _hasAdequacy;
                homeMatchesDataModel.LimitCandidatesToCompare = LIMIT_CANDIDATES_TO_COMPARE;

                if (_haveOfferCompletOrMembership)
                {
                    homeMatchesDataModel.IsOfferFlash = ((_offer.Integrations.FirstOrDefault()?.Isflash == 1 ? true : false) && _hasOfferFlash);
                    homeMatchesDataModel.HasCustomFolders = _hasCustomFolders;
                    homeMatchesDataModel.HasCvsExtractions = _hasCvDownload;
                    homeMatchesDataModel.HasChatMatches = HasChat(portalConfig);
                    homeMatchesDataModel.CanCreteChat = _canCreateChat;
                    homeMatchesDataModel.HasSaveFilters = _hasSaveFilters;
                    homeMatchesDataModel.HasCVDownloadMatchesReceived = _hasCVDownloadMatchesReceived;
                    homeMatchesDataModel.FiltersSaved = GetFiltersSavedForDataModel(saveFilters);
                    homeMatchesDataModel.MultifiltersDataModel.HasExclusion = _hasExclusion;
                    homeMatchesDataModel.MultifiltersDataModel.HasKillerQuestions = ChecKillerQuestions(idOffer, portalConfig);
                    homeMatchesDataModel.MultifiltersDataModel.ShowCommentFilter = _hasComents;
                    homeMatchesDataModel.MultifiltersDataModel.ShowRatingFilter = _hasRatings;
                    homeMatchesDataModel.MultifiltersDataModel.ShowNitFilter = _hasSearchNit;
                    homeMatchesDataModel.SaveFilterLimitPopUp = FillPopUpLimitSaveFilters(portalConfig);
                    homeMatchesDataModel.SaveFilterPopUp = FillPopUpSaveFilters(portalConfig);
                    homeMatchesDataModel.RemoveSaveFilterPopUp = FillPopUpRemoveSaveFilter(portalConfig);
                    FillPopUpPaymentOffers(homeMatchesDataModel, portalConfig);
                    FillPopUpFolders(homeMatchesDataModel, portalConfig);
                    FillPopUpChat(homeMatchesDataModel, portalConfig);
                }

                FillPopUpCompleteOffer(homeMatchesDataModel, portalConfig);

                FillMatchesData(homeMatchesDataModel, matchesResultElastic, portalConfig);

                FillPopUpLostSession(homeMatchesDataModel, portalConfig);

                FillPopUpNoIdsCandidateComparator(homeMatchesDataModel, portalConfig);

                FillPopUpLimitIdsCandidateComparator(homeMatchesDataModel, portalConfig);

                PopulateFilterList(homeMatchesDataModel, portalConfig, _companyCredentials, _currentFolder, searchFilter);

                homeMatchesDataModel.ShowPunctuationColumn = homeMatchesDataModel.Matches.Any(x => x.KQScoreMax > 0);
                homeMatchesDataModel.HasCandidateComparator = _hasCandidateComparator;

            }
        }

        private bool HasChat(PortalConfig portalConfig)
        {
            return _hasChat
                && (_companyProduct.GroupId == (short)ProductGroupEnum.Membership
                || _companyProduct.GroupId == (short)ProductGroupEnum.Packs && !HasNewFreemiumChat(portalConfig, _companyProduct.IdCompany));
        }

        private bool HasFiltersSearchFilter(SearchFilterDTO filter)
        {
            return !string.IsNullOrEmpty(filter.Nit)
                    || filter.Nationalities.Any()
                    || filter.LocalizationIds.Any()
                    || filter.CityIds.Any()
                    || filter.CategoryIds.Any()
                    || filter.LevelStudyIds.Any()
                    || filter.AgeMin != 0
                    || filter.AgeMax != 0
                    || filter.MinSalary != 0
                    || filter.MaxSalary != 0
                    || !string.IsNullOrEmpty(filter.CompleteName)
                    || filter.Showed != -1
                    || filter.Gender.Any()
                    || filter.HasPhoto != 0
                    || filter.LanguagesIds.Any()
                    || filter.Excluding != -1
                    || filter.CandidatesIds.Any()
                    || filter.NotCandidatesIds.Any()
                    || filter.HasDisability > -1
                    || filter.ApplicationDateFrom > -1
                    || !string.IsNullOrEmpty(filter.ExperienceProfession)
                    || filter.StudyAndStatus.Any()
                    || !string.IsNullOrEmpty(filter.Career)
                    || filter.HasCompetenceTest > -1
                    || !string.IsNullOrEmpty(filter.CompetenceTestKey)
                    || filter.HasAdequacy > -1
                    || !string.IsNullOrEmpty(filter.Query)
                    || filter.Distance > 0
                    || filter.HasTalentViewTest > (short)HasTalentViewTestEnum.None;
        }

        private SearchCandidate.SearchFilter GetCandidateElasticFilter(MultifiltersDataModel m, PortalConfig portalConfig, int currentFolder, CompanyCredentials companyCredentials, int pageNumber, bool isNuggetCv, bool IsNewFiltersActive)
        {
            var searchFilter = new SearchCandidate.SearchFilter
            {
                PortalId = portalConfig.PortalId,
                Facets = GetFacetsToElastic(isNuggetCv, IsNewFiltersActive),
                PageSize = portalConfig.AEPortalConfig.MatchesPageSize,
                PageNumber = pageNumber,
                OrderSearch = new List<BaseOrderSearch>()
                {
                    _elasticSearchCandidateService.GetOrderSearch(new MultiFiltersDTO(m.Order, m.MultiSearchText, m.NameUniversity, m.CompanyName,
                                                                               m.OrderDirectionSelected, portalConfig.AEPortalConfig.IsPremiumActive))
                }
            };

            if (_haveOfferCompletOrMembership)
            {

                searchFilter.Nit = m.SearchNit;
                searchFilter.Nationalities = m.SelectedNationatility;
                searchFilter.LocalizationIds = m.IsMySelection ? new List<int>() : new List<int>() { _offer.idlocalization };
                searchFilter.CategoryIds = m.IsMySelection ? new List<int>() : new List<int>() { _offer.idcategory };
                searchFilter.LevelStudyIds = m.SelectedStudyLevel;
                searchFilter.AgeMin = m.MinAge ?? 0;
                searchFilter.AgeMax = m.MaxAge ?? 0;
                searchFilter.MinSalary = m.MinSalary ?? 0;
                searchFilter.MaxSalary = m.MaxSalary ?? 0;
                searchFilter.Gender = m.Gender != 0
                    ? new List<int>() { m.Gender }
                    : new List<int>();
                searchFilter.HasPhoto = m.HasPhoto;
                searchFilter.LanguagesIds = TransformLanguageDataModelToLanguageSearchFilter(m.SelectedLanguage, m.SelectedLanguageLevel);
                searchFilter.CandidatesIds = m.IsMySelection ? GetCvsByOffer() : new List<int>();
                searchFilter.Exact = m.ExactSearchText;
                searchFilter.Query = m.ExactSearchText
                    ? $"'\'{m.MultiSearchText}'\'"
                    : m.MultiSearchText;
                searchFilter.IsWorking = isNuggetCv && IsNewFiltersActive ? m.IsWorking : (short)0;
                searchFilter.MaxYearsExperience = isNuggetCv && IsNewFiltersActive && m.MaxYearsExperience != null ? (short)m.MaxYearsExperience : (short)0;
                searchFilter.MinYearsExperience = isNuggetCv && IsNewFiltersActive && m.MinYearsExperience != null ? (short)m.MinYearsExperience : (short)0;
                searchFilter.University = isNuggetCv && IsNewFiltersActive ? m.NameUniversity : string.Empty;
                searchFilter.Companies = isNuggetCv
                           && m.IsCvVisualitzed != 0
                           ? new List<int>() { companyCredentials.IdCompany }
                           : new List<int>();
                searchFilter.CompanyExperience = isNuggetCv && IsNewFiltersActive ? m.CompanyName : string.Empty;
                searchFilter.IsStudying = isNuggetCv && IsNewFiltersActive ? m.IsStudying : (short)StudyingStatusEnum.None;
                searchFilter.StudyStatus = isNuggetCv && IsNewFiltersActive ? m.StudyStatus : (short)StudyStatusEnum.None;
                searchFilter.HasDisability = isNuggetCv && IsNewFiltersActive ? m.HasDisability : (short)HasDisabilityStatusEnum.None;
                searchFilter.Distance = m.Distance;

                if (portalConfig.AEPortalConfig.ShowNewMatchesFilterDesign)
                {
                    searchFilter.ApplicationDateFrom = m.ApplicationDateFrom;
                    searchFilter.ExperienceProfession = m.ExperienceProfession;
                    searchFilter.StudyAndStatus = TransformStudyAndStatusDataModelToStudyAndStatusSearchFilter(m.SelectedStudyAndStatusByLevel,
                                                                                                                m.SelectedStudyAndStatusByStatus.FirstOrDefault(),
                                                                                                                GetFromDictionary(portalConfig, DictionaryEnum.STUDY_STATUS));
                    searchFilter.Career = m.Career;
                    searchFilter.HasCompetenceTest = m.HasCompetenceTest;
                    searchFilter.CompetenceTestMin = m.CompetenceTestMin;
                    searchFilter.CompetenceTestMax = m.CompetenceTestMax;
                    searchFilter.CompetenceTestKey = m.CompetenceTestKey;
                    searchFilter.HasAdequacy = m.HasAdequacy;
                    searchFilter.AdequacyMin = m.AdequacyMin;
                    searchFilter.AdequacyMax = m.AdequacyMax;
                    searchFilter.HasTalentViewTest = m.HasTalentViewTest;
                }

                searchFilter.OrderByScore = m.Order == (short)CvOrderEnum.Relevance;
            }
            return searchFilter;
        }

        private List<string> GetFacetsToElastic(bool isNuggetCv, bool isNewFiltersActive)
        {
            if (isNuggetCv && isNewFiltersActive)
            {
                return new List<string>() {
                    FacetsByCvEnum.nationality.ToString()
                };
            }
            else
            {
                return new List<string>()
                {
                    FacetsByCvEnum.categories_search.ToString(),
                    FacetsByCvEnum.idlocalization.ToString(),
                    FacetsByCvEnum.idemploymenttype.ToString(),
                    FacetsByCvEnum.idcity.ToString(),
                    FacetsByCvEnum.last_formation_idstudy.ToString(),
                    FacetsByCvEnum.idsalaryread.ToString(),
                    FacetsByCvEnum.hasphoto.ToString(),
                    FacetsByCvEnum.idioms_search.ToString(),
                    FacetsByCvEnum.nationality.ToString()
                };
            }
        }

        private Tuple<List<int>, List<int>> GetCandidatesByCommentsAndRatings(short? rating, short comments)
        {
            var ratingsCandidatesIds = new List<int>();
            var commentsCandidatesIds = new List<int>();
            var result = new Tuple<List<int>, List<int>>(new List<int>(), new List<int>());

            if (rating != null && rating != 0)
                ratingsCandidatesIds = _ratingCvService.GetIdsByRatingCompanyAndOffer((short)rating, _offer.idofferCT, _offer.idcompany);

            if (comments > 0)
                commentsCandidatesIds = _commentCvService.GetIdCandidatesByCompanyAndOffer(_offer.idofferCT, _offer.idcompany);

            if (rating != null && rating != 0 && comments > 0)
                result = mergeDataRatingsAndComments(ratingsCandidatesIds, commentsCandidatesIds, rating, comments);
            else if (rating != null && rating != 0)
                result = new Tuple<List<int>, List<int>>(rating == -1 ? new List<int>() : ratingsCandidatesIds, rating == -1 ? ratingsCandidatesIds : new List<int>());
            else if (comments > 0)
                result = new Tuple<List<int>, List<int>>(comments == 1 ? new List<int>() : commentsCandidatesIds, comments == 1 ? commentsCandidatesIds : new List<int>());

            return result;
        }

        private Tuple<List<int>, List<int>> mergeDataRatingsAndComments(List<int> ratingsCandidatesIds, List<int> commentsCandidatesIds, short? rating, short comments)
        {
            var result = new Tuple<List<int>, List<int>>(new List<int>(), new List<int>());
            var toAdd = new List<int>();
            var toRemove = new List<int>();

            foreach (var itemRating in ratingsCandidatesIds)
            {
                if (commentsCandidatesIds.Contains(itemRating)
                    && rating > 0
                    && comments == 2)
                {
                    toAdd.Add(itemRating);
                }

                if (rating == -1)
                {
                    toRemove.Add(itemRating);
                }
            }

            foreach (var itemComment in commentsCandidatesIds)
            {
                if (comments == 1)
                    toRemove.Add(itemComment);
            }

            result = new Tuple<List<int>, List<int>>(toAdd, toRemove);


            return result;
        }

        private List<int> GetCvsByOffer()
        {
            var cvs = _curriculumService.GetCVsByOffer(_offer.idofferCT, _offer.idcompany, _offer.idportal);
            return cvs.Any() ? cvs.Select(x => x.IdCandidate).ToList() : new List<int>();
        }

        private int GetCvsByOffer(int idOffer, short portalId)
        {
            var cvCountersByOffers = _offerCvCountersService.GetCounterByIdOffers(idOffer.ToString(), portalId);

            int CVUnitsConsumed = 0;
            if (cvCountersByOffers.Count > 0)
                CVUnitsConsumed = cvCountersByOffers.First().UnitsConsumed;

            return CVUnitsConsumed;
        }

        private void SetSaveFilterToDataModel(MultifiltersDataModel multifiltersDataModel, List<CompanyFilterEntity> saveFilters, PortalConfig portalConfig)
        {
            int.TryParse(_encryptionService.Decrypt(multifiltersDataModel.IdSaveFilterSelected), out var idSaveFilterSelected);
            var filterSaved = saveFilters.Exists(e => e.FilterId == idSaveFilterSelected)
                              ? saveFilters.FirstOrDefault(f => f.FilterId == idSaveFilterSelected)
                              : new CompanyFilterEntity();

            if (filterSaved.FilterId > 0)
            {
                multifiltersDataModel.SelectedCities = filterSaved.Cities;
                multifiltersDataModel.Comments = filterSaved.Comment;
                multifiltersDataModel.IsMatchesVisualitzed = filterSaved.CvVisualitzed;
                multifiltersDataModel.Exclusion = filterSaved.Exclusion;
                multifiltersDataModel.SelectedLanguage = filterSaved.Language;
                multifiltersDataModel.SelectedLanguageLevel = filterSaved.LanguageLevel;
                multifiltersDataModel.SelectedLocalization = filterSaved.Localizations;
                multifiltersDataModel.MaxAge = filterSaved.MaxAge;
                multifiltersDataModel.MinAge = filterSaved.MinAge;
                multifiltersDataModel.MaxSalary = filterSaved.MaxSalary;
                multifiltersDataModel.MinSalary = filterSaved.MinSalary;
                multifiltersDataModel.SelectedNationatility = filterSaved.Nationalities;
                multifiltersDataModel.HasPhoto = filterSaved.Photo;
                multifiltersDataModel.Gender = filterSaved.Gender;
                multifiltersDataModel.SelectedProfesionalCategories = filterSaved.ProfesionalCategories;
                multifiltersDataModel.Rating = filterSaved.Rating;
                multifiltersDataModel.SearchName = filterSaved.SearchName;
                multifiltersDataModel.SearchNit = filterSaved.SearchNit;
                multifiltersDataModel.SelectedStudyLevel = filterSaved.StudyLevels;
                multifiltersDataModel.HasDisability = filterSaved.HasDisability;
                multifiltersDataModel.Distance = filterSaved.Distance;

                if (portalConfig.AEPortalConfig.ShowNewMatchesFilterDesign)
                {
                    multifiltersDataModel.ApplicationDateFrom = filterSaved.ApplicationDateFrom;
                    multifiltersDataModel.Career = filterSaved.Career;
                    multifiltersDataModel.ExperienceProfession = filterSaved.ExperienceProfession;
                    multifiltersDataModel.StudyAndStatus = filterSaved.StudyAndStatus;
                    multifiltersDataModel.SelectedStudyAndStatusByLevel = GetListOfLevels(filterSaved.StudyAndStatus);
                    multifiltersDataModel.SelectedStudyAndStatusByStatus = GetListOfStatus(filterSaved.StudyAndStatus);
                    multifiltersDataModel.HasCompetenceTest = filterSaved.HasCompetenceTest;
                    multifiltersDataModel.CompetenceTestMin = filterSaved.CompetenceTestMin;
                    multifiltersDataModel.CompetenceTestMax = filterSaved.CompetenceTestMax;
                    multifiltersDataModel.CompetenceTestKey = filterSaved.CompetenceTestKey;
                    multifiltersDataModel.HasAdequacy = filterSaved.HasAdequacy;
                    multifiltersDataModel.AdequacyMin = filterSaved.AdequacyMin;
                    multifiltersDataModel.AdequacyMax = filterSaved.AdequacyMax;
                    multifiltersDataModel.MultiSearchText = filterSaved.SearchText;
                    multifiltersDataModel.ExactSearchText = filterSaved.ExactSearchText;
                    multifiltersDataModel.HasTalentViewTest = filterSaved.HasTalentViewTest;
                }
            }
        }

        private List<int> GetListOfLevels(List<string> studyAndStatus)
        {
            var list = new List<int>();
            foreach (var item in studyAndStatus)
            {
                var level = item.Substring(0, item.IndexOf("_"));
                if (!list.Any(l => l.ToString() == level)) list.Add(Convert.ToInt32(level));
            }
            return list;
        }

        private List<int> GetListOfStatus(List<string> studyAndStatus)
        {
            var list = new List<int>();
            if (studyAndStatus.Count() < 1)
            {
                list.Add(0);
            }
            else
            {
                var item = studyAndStatus[0];
                var status = item.Substring(item.IndexOf("_") + 1, item.Length - item.IndexOf("_") - 1);
                list.Add(Convert.ToInt32(status));
            }
            return list;
        }

        private List<SelectListItem> GetFiltersSavedForDataModel(List<CompanyFilterEntity> saveFilters)
        {
            var result = new List<SelectListItem>();

            if (saveFilters.Any())
            {
                foreach (var item in saveFilters)
                    result.Add(new SelectListItem() { Text = item.FilterName, Value = _encryptionService.Encrypt(item.FilterId.ToString()) });
            }

            return result;
        }

        private void FillPopUpCompleteOffer(HomeMatchesDataModel homeMatchesDataModel, PortalConfig portalConfig)
        {
            homeMatchesDataModel.RedirectToCartPopUp = SetRedirectToCartPopUp(portalConfig);
        }

        private void FillPopUpChat(HomeMatchesDataModel homeMatchesDataModel, PortalConfig portalConfig)
        {
            homeMatchesDataModel.NoFolderToMakeChatPopUp = NoFolderToMakeChatPopUp(portalConfig);
            homeMatchesDataModel.ChatUniPopUp = ChatUni(portalConfig);
        }

        private void FillPopUpFolders(HomeMatchesDataModel homeMatchesDataModel, PortalConfig portalConfig)
        {
            homeMatchesDataModel.AddFolderPopUp = AddFolderPopUp(portalConfig);
            homeMatchesDataModel.EditFolderPopUp = EditFolderPopUp(portalConfig);
            homeMatchesDataModel.DeleteFolderPopUp = DeleteFolderPopUp(portalConfig);
        }

        private void FillPopUpPaymentOffers(HomeMatchesDataModel homeMatchesDataModel, PortalConfig portalConfig)
        {
            homeMatchesDataModel.NoIdsSelectedPopUp = NoIdsSelectedPopUp(portalConfig);
            homeMatchesDataModel.NoIdsSelectedPrintPopUp = NoIdsSelectedPrintPopUp(portalConfig);
            homeMatchesDataModel.NoIdsSelectedChatPopUp = NoIdsSelectedChatPopUp(portalConfig);
            homeMatchesDataModel.NoIdsSelectedDeletePopUp = NoIdsSelectedDeletePopUp(portalConfig);
            homeMatchesDataModel.SureToLogicDelete = SureToLogicDelete(portalConfig);
            homeMatchesDataModel.SureToDiscardCandidate = SureToDiscardCandidate(portalConfig);
        }

        private void FillPopUpLostSession(HomeMatchesDataModel homeMatchesDataModel, PortalConfig portalConfig)
        {
            homeMatchesDataModel.LostSessionPopUp = LostSessionPopUp(portalConfig);
        }

        private void FillPopUpNoIdsCandidateComparator(HomeMatchesDataModel homeMatchesDataModel, PortalConfig portalConfig)
        {
            homeMatchesDataModel.NoIdsCandidateComparatorPopUp = NoIdsCandidateComparatorPopUp(portalConfig);
        }

        private void FillPopUpLimitIdsCandidateComparator(HomeMatchesDataModel homeMatchesDataModel, PortalConfig portalConfig)
        {
            homeMatchesDataModel.LimitIdsCandidateComparatorPopUp = LimitIdsCandidateComparatorPopUp(portalConfig);
        }

        private SearchFilterDTO GetUnlimitedMatchesElasticFilter(MultifiltersDataModel homeMatchesDataModel, PortalConfig portalConfig, OfferEntity offer, PagerDataModel pagerDataModel, bool newApplies = false)
        {
            var searchFilter = new SearchFilterDTO
            {
                IdPortal = portalConfig.PortalId,
                Facets = new List<string>(),
                IdOffer = offer.idofferCT,
                IdCompany = offer.idcompany,
                PageSize = _maxCvAppliedToSee,
                PageNumber = 1,
                OrderSearch = GetBaseOrderSearch(homeMatchesDataModel)
            };

            if (newApplies)
            {
                searchFilter.Showed = 0;
                homeMatchesDataModel.IsMatchesVisualitzed = 0;
            }

            return searchFilter;
        }

        private SearchFilterDTO GetMatchesElasticFilter(MultifiltersDataModel homeMatchesDataModel, PortalConfig portalConfig, int _currentFolder, OfferEntity offer, PagerDataModel pagerDataModel, bool newApplies = false)
        {
            var searchFilter = new SearchFilterDTO
            {
                IdPortal = portalConfig.PortalId,
                Facets = new List<string>(),
                IdOffer = offer.idofferCT,
                IdCompany = offer.idcompany,
                PageSize = pagerDataModel.PageSizeSelected,
                PageNumber = pagerDataModel.PageSelected,
                OrderSearch = GetBaseOrderSearch(homeMatchesDataModel)
            };

            searchFilter.IdCompanyFolder = _currentFolder > (int)CompanyOfferFolderEnum.Recibidos
                ? new List<int>() { _currentFolder }
                : new List<int>() { (int)CompanyOfferFolderEnum.Recibidos };


            var CandidatesToAddAnRemove = GetCandidatesByCommentsAndRatings(homeMatchesDataModel.Rating, homeMatchesDataModel.Comments);

            searchFilter.IsRating = homeMatchesDataModel.Rating ?? 0;
            searchFilter.IsCommented = homeMatchesDataModel.Comments;
            searchFilter.Nit = homeMatchesDataModel.SearchNit;
            searchFilter.Nationalities = homeMatchesDataModel.SelectedNationatility;
            searchFilter.LocalizationIds = homeMatchesDataModel.SelectedLocalization;
            searchFilter.CityIds = homeMatchesDataModel.SelectedCities;
            searchFilter.CategoryIds = homeMatchesDataModel.SelectedProfesionalCategories;
            searchFilter.LevelStudyIds = homeMatchesDataModel.SelectedStudyLevel;
            searchFilter.AgeMin = homeMatchesDataModel.MinAge ?? 0;
            searchFilter.AgeMax = homeMatchesDataModel.MaxAge ?? 0;
            searchFilter.MinSalary = homeMatchesDataModel.MinSalary ?? 0;
            searchFilter.MaxSalary = homeMatchesDataModel.MaxSalary ?? 0;
            searchFilter.CompleteName = homeMatchesDataModel.SearchName;
            searchFilter.Showed = homeMatchesDataModel.IsMatchesVisualitzed;
            searchFilter.Gender = homeMatchesDataModel.Gender != 0
                ? new List<int>() { homeMatchesDataModel.Gender }
                : new List<int>();
            searchFilter.HasPhoto = homeMatchesDataModel.HasPhoto;
            searchFilter.LanguagesIds = TransformLanguageDataModelToLanguageSearchFilter(homeMatchesDataModel.SelectedLanguage, homeMatchesDataModel.SelectedLanguageLevel);
            searchFilter.Excluding = homeMatchesDataModel.Exclusion;
            searchFilter.CandidatesIds = CandidatesToAddAnRemove.Item1;
            searchFilter.NotCandidatesIds = CandidatesToAddAnRemove.Item2;
            searchFilter.HasDisability = homeMatchesDataModel.HasDisability;
            searchFilter.Distance = homeMatchesDataModel.Distance;
            if (portalConfig.AEPortalConfig.ShowDistanceMatchesFilter
                && _offerProduct.Features.Any(f => f.AmbitId == (int)ProductAmbitEnum.OfferFilterAppliesByProximity)
                && (portalConfig.AEPortalConfig.EnablePostalCode == "1"))
            {

                searchFilter.GeoPoint = offer.IdPostalCode > 0 ? _geolocationService.GetGeoPoint(portalConfig.PortalId, offer.IdPostalCode, offer.idcity) : null;
            }
            if (portalConfig.AEPortalConfig.ShowNewMatchesFilterDesign)
            {
                searchFilter.ExactSearchText = homeMatchesDataModel.ExactSearchText;
                searchFilter.Query = homeMatchesDataModel.ExactSearchText
                    ? $"'\'{homeMatchesDataModel.MultiSearchText}'\'"
                    : homeMatchesDataModel.MultiSearchText;

                searchFilter.ApplicationDateFrom = homeMatchesDataModel.ApplicationDateFrom;
                searchFilter.ExperienceProfession = homeMatchesDataModel.ExperienceProfession;
                searchFilter.Career = homeMatchesDataModel.Career;
                if (!homeMatchesDataModel.StudyAndStatus.Any())
                    searchFilter.StudyAndStatus = TransformStudyAndStatusDataModelToStudyAndStatusSearchFilter(homeMatchesDataModel.SelectedStudyAndStatusByLevel,
                                                                                                            homeMatchesDataModel.SelectedStudyAndStatusByStatus.FirstOrDefault(),
                                                                                                            GetFromDictionary(portalConfig, DictionaryEnum.STUDY_STATUS));
                else
                    searchFilter.StudyAndStatus = homeMatchesDataModel.StudyAndStatus;

                searchFilter.HasCompetenceTest = homeMatchesDataModel.HasCompetenceTest;
                searchFilter.CompetenceTestMin = homeMatchesDataModel.CompetenceTestMin;
                searchFilter.CompetenceTestMax = homeMatchesDataModel.CompetenceTestMax;
                searchFilter.CompetenceTestKey = homeMatchesDataModel.CompetenceTestKey;
                searchFilter.HasAdequacy = homeMatchesDataModel.HasAdequacy;
                searchFilter.AdequacyMin = homeMatchesDataModel.AdequacyMin;
                searchFilter.AdequacyMax = homeMatchesDataModel.AdequacyMax;
                searchFilter.HasTalentViewTest = homeMatchesDataModel.HasTalentViewTest;
            }

            if (newApplies)
            {
                searchFilter.Showed = 0;
                homeMatchesDataModel.IsMatchesVisualitzed = 0;
            }

            return searchFilter;
        }

        private List<BaseOrderSearch> GetBaseOrderSearch(MultifiltersDataModel multifiltersDataModel)
        {
            List<BaseOrderSearch> list = new List<BaseOrderSearch>();

            if (_hasCandidateSuggester)
            {
                list.Add(new BaseOrderSearch()
                {
                    Field = string.IsNullOrEmpty(multifiltersDataModel.FieldSearch) ? "adequacy_points" : multifiltersDataModel.FieldSearch,
                    Direction = OrderField(multifiltersDataModel)
                });
            }
            else
            {
                if (_haveOfferCompletOrMembership)
                {
                    list.Add(new BaseOrderSearch()
                    {
                        Field = string.IsNullOrEmpty(multifiltersDataModel.FieldSearch) ? "appliedOn" : multifiltersDataModel.FieldSearch,
                        Direction = OrderField(multifiltersDataModel)
                    });
                }
                else
                {
                    list.Add(new BaseOrderSearch()
                    {
                        Field = "id",
                        Direction = OrderField(multifiltersDataModel)
                    });
                }
            }
            return list;
        }

        private OrderSearchDirectionEnum OrderField(MultifiltersDataModel multifiltersDataModel)
        {
            if (_haveOfferCompletOrMembership)
            {
                if (multifiltersDataModel.Direction == "a" && !string.IsNullOrEmpty(multifiltersDataModel.FieldSearch))
                    return OrderSearchDirectionEnum.asc;
                else
                    return OrderSearchDirectionEnum.desc;
            }

            return OrderSearchDirectionEnum.asc;
        }

        private SearchResultDTO<MatchElasticEntity> LoadMatches(SearchFilterDTO filters)
        {
            if ((filters.IsRating > 0 || filters.IsCommented == 2) && !filters.CandidatesIds.Any())
                return new SearchResultDTO<MatchElasticEntity>();

            var searchResult = _matchService.SearchElastic(filters);

            if (searchResult.Matches.Any())
                SetCommentsAndRatingsInMatches(filters.IsRating, searchResult.Matches);

            return searchResult;
        }

        private void SetCommentsAndRatingsInMatches(short isRating, List<MatchElasticEntity> matches)
        {
            var commentsCount = new Dictionary<long, int>();
            var ratingsCount = new Dictionary<long, int>();

            if (_hasComents)
            {
                var idsCandidate = matches.Select(m => (int)m.candidateId).ToList();

                commentsCount = _commentCvService.GetCountersWithCommentsByCandidates(idsCandidate, SecurityHelper.GetCompanyCredentials().IdCompany);

                if (_hasRatings)
                {
                    ratingsCount = _ratingCvService.GetHighRatingByCandidatesList(idsCandidate, SecurityHelper.GetCompanyCredentials().IdCompany, isRating);
                }
            }

            foreach (var match in matches)
            {
                if (commentsCount.ContainsKey(match.candidateId))
                    match.CommentCount = commentsCount[match.candidateId];

                if (ratingsCount.ContainsKey(match.candidateId))
                    match.HasRating = ratingsCount[match.candidateId] > 0;
            }
        }

        private void ChangeMatchProcessStatus(SearchResultDTO<MatchElasticEntity> matchesResultElastic, CompanyCredentials companyCredentials, PortalConfig portalConfig)
        {
            var candidatesToUpdateMatchProcess = matchesResultElastic.Matches.Where(candi => candi.processStatusId < (int)MatchCandidateProcessStatusEnum.Viewed).ToList();
            if (candidatesToUpdateMatchProcess != null && candidatesToUpdateMatchProcess.Count > 0)
                TryAsyncChangeMatchProcessStatus(candidatesToUpdateMatchProcess, _offer, (int)MatchCandidateProcessStatusEnum.Viewed, companyCredentials, portalConfig);
        }

        private void AsyncNotifyProcessChangedToCandidate(List<long> idMatches, int newProcessStatus, OfferEntity offer)
        {
            Task.Factory.StartNew((Action)(() =>
            {
                foreach (long idMatch in idMatches)
                {
                    _stackPublicatorMatchesService.Insert(new MatchStackEntity() { ObjectId = idMatch }, offer.idportal);
                    MatchEntity match = _matchService.GetByPk(idMatch, offer.idportal);
                    _matchService.NotifyProcessChangedToCandidate(match, (MatchCandidateProcessStatusEnum)newProcessStatus, offer);
                }
            }));
        }

        private void AsyncChangeMatchProcessOneByOne(List<MatchElasticEntity> matches, OfferEntity offer, int newProcessStatusId, CompanyCredentials companyCredentials, PortalConfig portalConfig)
        {
            Task.Factory.StartNew(() =>
            {
                foreach (var match in matches)
                {
                    _matchService.ChangeCandidateProcessStatus(new CandidateProcessStatusDTO(match.offerId, (int)match.curriculumId,
                                  newProcessStatusId, match.portalId, match.CompanyId, match.Id, match.candidateId),
                                  new TimelineExtraParamsChangeMatchDTO(offer.title, companyCredentials.CompanyName, portalConfig.idapp, portalConfig.CurrentIntPortal, portalConfig.PortalId, DictionaryService.GetDictionaryValue(DictionaryEnum.CITY, _offer.idcity.ToString(), portalConfig.PortalId)));
                }
            });
        }

        private bool ChecKillerQuestions(int idOffer, PortalConfig portalConfig)
        {
            if (!_hasKillerQuestions)
                return false;

            if (portalConfig.include_killer_questions == 0)
                return false;

            if (_offer.has_killer_questions == 0)
                return false;

            return OfferService.HasKillerQuestions(idOffer);
        }

        private List<CompanyFilterEntity> GetCompanyFiltersFilterMatches(int idOffer)
        {
            _companyFilters = _companyFiltersService.GetCompanyFiltersMatchesWithTranslateQuerystring(_companyCredentials.IdCompany, _companyCredentials.UserId, _companyCredentials.PortalId, idOffer);

            _companyFilters = _companyFilters.Where(x => x.IdStatus == (short)StatusEnum.Active).ToList();

            if (_companyFilters != null
                && _companyFilters.Any())
                return _companyFilters;

            return new List<CompanyFilterEntity>();
        }

        private void PopulateFilterList(HomeMatchesDataModel homeMatchesDataModel, PortalConfig portalConfig, CompanyCredentials companyCredentials, int currentFolder,
            SearchFilterDTO searchFilter)
        {
            homeMatchesDataModel.MatchActions = GetMatchActions(portalConfig, currentFolder);
            homeMatchesDataModel.MatchFolders = SetFolderActions(portalConfig, currentFolder);

            SearchFilterDTO facetsOther = GetFacetsFilter(portalConfig, searchFilter);
            var facets = GetFacets(facetsOther);

            if (facets.ContainsKey(FacetsByMatchEnum.categoryIds.ToString()) && string.IsNullOrEmpty(homeMatchesDataModel.MultifiltersDataModel.SearchNit))
            {
                homeMatchesDataModel.MultifiltersDataModel.ProfesionalCategories = GetSelectListItemsFromFacets(facets, homeMatchesDataModel.MultifiltersDataModel.SelectedProfesionalCategories, DictionaryEnum.CATEGORY,
                    FacetsByMatchEnum.categoryIds.ToString(), portalConfig);
            }

            if (facets.ContainsKey(FacetsByMatchEnum.regionId.ToString()) && string.IsNullOrEmpty(homeMatchesDataModel.MultifiltersDataModel.SearchNit))
            {
                homeMatchesDataModel.MultifiltersDataModel.Localizations = GetSelectListItemsFromFacetsDependant(facets, homeMatchesDataModel.MultifiltersDataModel.SelectedLocalization,
                    DictionaryEnum.LOCALIZATION_BY_COUNTRY, FacetsByMatchEnum.regionId.ToString(),
                    portalConfig);
            }

            if (homeMatchesDataModel.MultifiltersDataModel.SelectedLocalization.Any())
            {
                homeMatchesDataModel.MultifiltersDataModel.Cities = GetCitiesSelectListItems(homeMatchesDataModel.MultifiltersDataModel.SelectedLocalization, homeMatchesDataModel.MultifiltersDataModel.SelectedCities, portalConfig.PortalId);
            }

            if (facets.ContainsKey(FacetsByMatchEnum.nationality.ToString()) && string.IsNullOrEmpty(homeMatchesDataModel.MultifiltersDataModel.SearchNit))
            {
                homeMatchesDataModel.MultifiltersDataModel.Nationatilities = GetSelectListItemsFromFacetsDependant(facets, homeMatchesDataModel.MultifiltersDataModel.SelectedNationatility,
                    DictionaryEnum.COUNTRY, FacetsByMatchEnum.nationality.ToString(),
                    portalConfig);
            }

            if (facets.ContainsKey(FacetsByMatchEnum.educationLevelId.ToString()) && string.IsNullOrEmpty(homeMatchesDataModel.MultifiltersDataModel.SearchNit))
            {
                homeMatchesDataModel.MultifiltersDataModel.StudyLevels = GetSelectListItemsFromFacets(facets, homeMatchesDataModel.MultifiltersDataModel.SelectedStudyLevel,
                    DictionaryEnum.EDUCATION_LEVEL, FacetsByMatchEnum.educationLevelId.ToString(),
                    portalConfig);
            }

            homeMatchesDataModel.Pager.PageSizes = GetFromDictionaryDependantKey(portalConfig, DictionaryEnum.DIC_DROPDOWN_LIST, dependantKey: (short)DropDownListEnum.ddlOffersByPage);
            homeMatchesDataModel.MultifiltersDataModel.Languages = GetFromDictionary(portalConfig, DictionaryEnum.LANGUAGE, PageEnum.MatchOffer);
            homeMatchesDataModel.MultifiltersDataModel.LanguageLevels = GetFromDictionary(portalConfig, DictionaryEnum.LANGUAGE_LEVEL, PageEnum.MatchOffer);
            homeMatchesDataModel.MultifiltersDataModel.StudyStatusLevels = GetFromDictionary(portalConfig, DictionaryEnum.STUDY_STATUS);
            homeMatchesDataModel.MultifiltersDataModel.CompetenceTests = GetCompetenceTestsWithValue(GetFromDictionary(portalConfig, DictionaryEnum.COMPETENCES));
            homeMatchesDataModel.MultifiltersDataModel.Distances = GetFromDictionary(portalConfig, DictionaryEnum.DISTANCES, PageEnum.MatchOffer, true, "LIT_SIN_LIMITE").OrderBy(d => d.Value.ToInt()).ToList();

        }

        private List<SelectListItem> GetCompetenceTestsWithValue(List<SelectListItem> selectListItems)
        {
            foreach (var item in selectListItems)
            {
                item.Value = GetCompetenceTestKeyEnumByValue(item.Value);
            }
            return selectListItems;
        }

        private string GetCompetenceTestKeyEnumByValue(string value)
        {
            switch (Convert.ToInt16(value))
            {
                case (short)CompetenceTestKeyEnum.AR:
                    return CompetenceTestKeyEnum.AR.ToString();
                case (short)CompetenceTestKeyEnum.RE:
                    return CompetenceTestKeyEnum.RE.ToString();
                case (short)CompetenceTestKeyEnum.LI:
                    return CompetenceTestKeyEnum.LI.ToString();
                case (short)CompetenceTestKeyEnum.ML:
                    return CompetenceTestKeyEnum.ML.ToString();
                case (short)CompetenceTestKeyEnum.IS:
                    return CompetenceTestKeyEnum.IS.ToString();
                case (short)CompetenceTestKeyEnum.AU:
                    return CompetenceTestKeyEnum.AU.ToString();
                case (short)CompetenceTestKeyEnum.GE:
                    return CompetenceTestKeyEnum.GE.ToString();
                case (short)CompetenceTestKeyEnum.CO:
                    return CompetenceTestKeyEnum.CO.ToString();
                default:
                    return string.Empty;
            }

        }

        private static SearchFilterDTO GetFacetsFilter(PortalConfig portalConfig, SearchFilterDTO searchFilter)
        {
            return new SearchFilterDTO
            {
                IdPortal = portalConfig.PortalId,
                Facets = new List<string>
                    {
                        FacetsByMatchEnum.categoryIds.ToString(),
                        FacetsByMatchEnum.educationLevelId.ToString(),
                        FacetsByMatchEnum.cityId.ToString(),
                        FacetsByMatchEnum.regionId.ToString(),
                        FacetsByMatchEnum.nationality.ToString()
                    },
                IdOffer = searchFilter.IdOffer,
                IdCompany = searchFilter.IdCompany,
                IdCompanyFolder = searchFilter.IdCompanyFolder
            };
        }

        private void PopulateFilterListByCandidates(MultifiltersDataModel multifilterDataModel, PortalConfig portalConfig, CompanyCredentials companyCredentials, int currentFolder, SearchCandidate.SearchFilter searchFilter, PagerDataModel pagerDataModel,
                                                    SearchCandidate.SearchResult<SearchCandidate.CandidateReadSearch> candidates, bool isNuggetCv, bool isNewFilter)
        {
            if (isNuggetCv && isNewFilter)
            {
                GetAndSetNewFacets(multifilterDataModel, portalConfig, candidates.Facets);
                multifilterDataModel.StudyLevels = SetSelectedsToDictionary(multifilterDataModel.SelectedStudyLevel, GetFromDictionary(portalConfig, DictionaryEnum.EDUCATION_LEVEL));
            }

            pagerDataModel.PageSizes = GetFromDictionaryDependantKey(portalConfig, DictionaryEnum.DIC_DROPDOWN_LIST, dependantKey: (short)DropDownListEnum.ddlOffersByPage);
            multifilterDataModel.Languages = GetFromDictionary(portalConfig, DictionaryEnum.LANGUAGE, PageEnum.MatchOffer);
            multifilterDataModel.LanguageLevels = GetFromDictionary(portalConfig, DictionaryEnum.LANGUAGE_LEVEL, PageEnum.MatchOffer);
        }

        private void GetAndSetNewFacets(MultifiltersDataModel multifiltersDataModel, PortalConfig portalConfig, Dictionary<string, List<FacetResult>> facets)
        {
            if (facets != null &&
                facets.ContainsKey(FacetsByCvEnum.nationality.ToString()))
                multifiltersDataModel.Nationatilities = GetSelectListItemsFromNewFacets(facets, multifiltersDataModel.SelectedNationatility, GetFromDictionary(portalConfig, DictionaryEnum.COUNTRY), FacetsByCvEnum.nationality.ToString(), portalConfig);
        }

        private List<SelectListItem> GetSelectListItemsFromNewFacets(Dictionary<string, List<FacetResult>> facets, List<int> selecteds, List<SelectListItem> dictionary, string facet, PortalConfig portalConfig)
        {
            var result = new List<SelectListItem>();

            if (!facets.ContainsKey(facet)) return result;

            foreach (var item in facets[facet])
            {
                var dic = dictionary.FirstOrDefault(c => c.Value == item.Key);
                if (dic is null) continue;

                int.TryParse(dic.Value, out var id);

                result.Add(new SelectListItem()
                {
                    Value = dic.Value,
                    Text = dic.Text,
                    Selected = selecteds.Exists(e => e == id)
                });
            }

            if (result.Any())
            {
                if (result.Exists(e => e.Selected))
                    result = result.OrderByDescending(o => o.Selected).ToList();
                else if (result.Exists(e => e.Value == portalConfig.countryId.ToString()))
                {
                    var firstItem = result.Find(f => f.Value == portalConfig.countryId.ToString());
                    result.Remove(firstItem);
                    result.Insert(0, firstItem);
                }
            }
            return result;
        }

        private static List<SelectListItem> SetSelectedsToDictionary(List<int> selecteds, List<SelectListItem> dictionary)
        {
            var result = new List<SelectListItem>();

            foreach (var item in dictionary)
            {
                int.TryParse(item.Value, out var id);

                result.Add(new SelectListItem()
                {
                    Value = item.Value,
                    Text = item.Text,
                    Selected = selecteds.Exists(e => e == id)
                });
            }

            if (result.Any())
                result = result.OrderByDescending(o => o.Selected).ToList();

            return result;
        }

        private Dictionary<string, List<FacetResult>> GetFacets(SearchFilterDTO searchFilter)
        {
            return _matchService.GetCountFacetsByElastic(searchFilter);
        }


        private List<SelectListItem> GetSelectListItemsFromFacetsDependant(Dictionary<string, List<FacetResult>> facets, List<int> Selecteds, DictionaryEnum dict, string facet, PortalConfig portalConfig)
        {
            return GetSelectListItemsFromFacets(facets, Selecteds, GetFromDictionaryDependantKey(portalConfig, dict, dependantKey: portalConfig.countryId), facet, portalConfig);
        }

        private List<SelectListItem> GetSelectListItemsFromFacets(Dictionary<string, List<FacetResult>> facets, List<int> Selecteds, DictionaryEnum dict, string facet, PortalConfig portalConfig)
        {
            return GetSelectListItemsFromFacets(facets, Selecteds, GetFromDictionary(portalConfig, dict), facet, portalConfig);
        }

        private List<SelectListItem> GetSelectListItemsFromFacets(Dictionary<string, List<FacetResult>> facets, List<int> Selecteds, List<SelectListItem> dictionary, string facet, PortalConfig portalConfig)
        {
            var result = new List<SelectListItem>();

            if (!facets.ContainsKey(facet)) return result;

            foreach (var item in facets[facet])
            {
                var dic = dictionary.FirstOrDefault(c => c.Value == item.Key);
                if (dic is null) continue;

                int.TryParse(dic.Value, out var id);

                result.Add(new SelectListItem()
                {
                    Value = dic.Value,
                    Text = dic.Text,
                    Selected = Selecteds.Exists(e => e == id)
                });
            }

            if (result.Any())
                result = result.OrderByDescending(o => o.Selected).ToList();

            return result;
        }

        private List<SelectListItem> GetCitiesSelectListItems(List<int> selectedLocalizations, List<int> seletedCities, short portalId)
        {
            var result = new List<SelectListItem>();
            foreach (var localizationId in selectedLocalizations)
            {
                var dicCity = DictionaryService.GetDictionary(DictionaryEnum.CITIES_BY_LOCALIZATION, localizationId, portalId);
                foreach (var city in dicCity)
                {
                    int.TryParse(city.Value, out var id);

                    if (!result.Exists(r => r.Value == city.Value))
                    {
                        result.Add(new SelectListItem()
                        {
                            Value = city.Key,
                            Text = city.Value,
                            Selected = seletedCities.Exists(c => c == id)
                        });
                    }
                }
            }

            if (result.Any())
                result = result.OrderByDescending(o => o.Selected).ToList();

            return result;
        }

        private List<SelectListItem> SetFolderActions(PortalConfig portalConfig, int currentFolder)
        {
            var foldersDDL = new List<SelectListItem>
            {
                new SelectListItem() { Value = "0", Text = PageLiteralsHelper.GetLiteral("ACTION_OPTION_0", PageId, portalConfig) }
            };

            var foldersText = new List<SelectListItem>
            {
                 new SelectListItem() { Value = ((short)CompanyOfferFolderEnum.Recibidos).ToString(), Text = PageLiteralsHelper.GetLiteral("LIT_FOLDER_1", PageId, portalConfig) },
                 new SelectListItem() { Value = ((short)CompanyOfferFolderEnum.Seleccionados).ToString(), Text = PageLiteralsHelper.GetLiteral("LIT_FOLDER_2", PageId, portalConfig) },
                 new SelectListItem() { Value = ((short)CompanyOfferFolderEnum.Finalistas).ToString(), Text = PageLiteralsHelper.GetLiteral("LIT_FOLDER_3", PageId, portalConfig) },
                 new SelectListItem() { Value = ((short)CompanyOfferFolderEnum.Descartados).ToString(), Text = PageLiteralsHelper.GetLiteral("LIT_FOLDER_4", PageId, portalConfig) }

            };

            if (CompanyHelper.HasCTHROnboardingOption(_companyProduct, portalConfig))
                foldersText.Add(new SelectListItem() { Value = ((short)CompanyOfferFolderEnum.Contratados).ToString(), Text = PageLiteralsHelper.GetLiteral("LIT_FOLDER_8", PageId, portalConfig) });

            foreach (var folder in _folders)
            {
                if (folder.Position == (short)CompanyCustomFoldersMatchEnum.Folder1 || folder.Position == (short)CompanyCustomFoldersMatchEnum.Folder2)
                    foldersDDL.Add(new SelectListItem() { Value = folder.Position.ToString(), Text = folder.NameFolder });
                else if (folder.Position != (short)CompanyOfferFolderEnum.Recibidos && folder.Position != (short)CompanyOfferFolderEnum.BBDD && folder.Position != (short)CompanyOfferFolderEnum.Contratados)
                    foldersDDL.Add(new SelectListItem() { Value = folder.Position.ToString(), Text = foldersText.Where(n => n.Value == folder.Position.ToString()).FirstOrDefault().Text });
                else if (folder.Position == (short)CompanyOfferFolderEnum.Contratados)
                {
                    int index = foldersDDL.FindIndex(n => n.Value == CompanyOfferFolderEnum.Finalistas.GetHashCode().ToString());
                    if (index != -1)
                    {
                        foldersDDL.Insert(index + 1, new SelectListItem() { Value = folder.Position.ToString(), Text = foldersText.Where(n => n.Value == folder.Position.ToString()).FirstOrDefault().Text });
                    }
                }
            }

            var currentItem = foldersDDL.SingleOrDefault(r => r.Value == currentFolder.ToString());

            if (currentItem != null)
                foldersDDL.Remove(currentItem);

            return foldersDDL;
        }

        private List<SelectListItem> GetMatchActions(PortalConfig portalConfig, int currentFolder)
        {
            var matchActions = new List<SelectListItem>();

            matchActions.Add(new SelectListItem() { Value = "0", Text = PageLiteralsHelper.GetLiteral("ACTION_OPTION_0", PageId, portalConfig) });
            matchActions.Add(new SelectListItem() { Value = "1", Text = PageLiteralsHelper.GetLiteral("ACTION_OPTION_1", PageId, portalConfig) });

            if (currentFolder != (int)CompanyOfferFolderEnum.Descartados)
                matchActions.Add(new SelectListItem() { Value = "2", Text = PageLiteralsHelper.GetLiteral("ACTION_OPTION_3", PageId, portalConfig) });
            else
                matchActions.Add(new SelectListItem() { Value = "2", Text = PageLiteralsHelper.GetLiteral("ACTION_OPTION_2", PageId, portalConfig) });

            if (_hasCvDownload)
            {
                matchActions.Add(new SelectListItem() { Value = "3", Text = PageLiteralsHelper.GetLiteral("ACTION_OPTION_4", PageId, portalConfig) });
            }


            return matchActions;
        }

        private int LoadScenary()
        {
            var cvFoldersFeature = _offerProduct.Features.Find(feat => feat.AmbitId == (int)ProductAmbitEnum.CVFolders);
            var cvBBDDFeature = _offerProduct.Features.Find(feat => feat.AmbitId == (int)ProductAmbitEnum.CvBBDD);
            return CompanyService.GetScenaryType(cvFoldersFeature, cvBBDDFeature, _offerProduct.GroupId);
        }

        private List<FoldersDataModel> LoadWithoutTabs(int total)
        {
            List<FoldersDataModel> ListFolderDM = new List<FoldersDataModel>();
            ListFolderDM.Add(new FoldersDataModel()
            {
                Id = (short)CompanyOfferFolderEnum.Recibidos,
                IdEncrypted = _encryptionService.Encrypt(((short)CompanyOfferFolderEnum.Recibidos).ToString()),
                Count = total,
                Position = (short)CompanyOfferFolderEnum.Recibidos
            });

            _totalMatchesInOffer = total;

            return ListFolderDM;
        }

        private List<FoldersDataModel> LoadFolders(int totalInSearch, int currentFolder, bool hasFilters, PortalConfig portalConfig)
        {
            _folders = LoadTabParameters(totalInSearch, currentFolder, hasFilters, portalConfig);
            return _folders;
        }

        private List<FoldersDataModel> LoadTabParameters(int totalInSearch, int currentFolder, bool hasFilters, PortalConfig portalConfig)
        {
            List<FoldersDataModel> ListFolderDM = new List<FoldersDataModel>();
            FoldersDataModel FolderDM = new FoldersDataModel();

            InitialiteFolders(ListFolderDM, portalConfig);

            return FillFolders(ListFolderDM, totalInSearch, currentFolder, hasFilters, portalConfig);
        }

        private List<FoldersDataModel> FillFolders(List<FoldersDataModel> ListFolderDM, int totalInSearch, int currentFolder, bool hasFilters, PortalConfig portalConfig)
        {
            var folderFacet = _facets.ContainsKey(FacetsByMatchEnum.folderId.ToString()) ? _facets[FacetsByMatchEnum.folderId.ToString()] : new List<FacetResult>();

            if (folderFacet == null)
                return ListFolderDM;

            ListFolderDM.ForEach(n =>
                {
                    folderFacet.ForEach(o =>
                    {
                        Int32.TryParse(o.Key, out var _key);
                        if (n.Position == _key)
                        {
                            n.Count = o.Count;

                            if (currentFolder == n.Position)
                            {
                                if (!hasFilters
                                && o.Count != totalInSearch)
                                {
                                    _matchService.DeleteCacheFolderFacet(new SearchFilterDTO()
                                    {
                                        Facets = new List<string>(new string[] { FacetsByMatchEnum.folderId.ToString() }),
                                        IdPortal = portalConfig.PortalId,
                                        IdOffer = _offer.idofferCT
                                    }, portalConfig);
                                }
                            }
                        }
                    });
                });

            _totalMatchesInOffer = ListFolderDM.Sum(n => n.Count);

            return ListFolderDM;
        }

        private void InitialiteFolders(List<FoldersDataModel> ListFolderDM, PortalConfig portalConfig)
        {
            foreach (CompanyOfferFolderEnum position in Enum.GetValues(typeof(CompanyOfferFolderEnum)))
            {
                if ((int)position > 0)
                {
                    ListFolderDM.Add(new FoldersDataModel()
                    {
                        Position = (int)position,
                        PositionEncrypted = _encryptionService.Encrypt(((int)position).ToString()),
                        Count = 0,
                        LiName = $"li_{position}",
                        AnchorName = $"link_{position}",
                        NameFolder = fillName((int)position, portalConfig),
                        Id = (int)position
                    });
                }
            }

            if (_offerProduct.GroupId != (int)ProductGroupsEnum.Packs)
            {
                ListFolderDM.Remove(ListFolderDM.Single(r => r.Position == (int)CompanyOfferFolderEnum.BBDD));
            }
            if (!CompanyHelper.HasCTHROnboardingOption(_companyProduct, portalConfig))
            {
                ListFolderDM.Remove(ListFolderDM.Single(r => r.Position == (int)CompanyOfferFolderEnum.Contratados));
            }


            if (_hasCustomFolders)
            {
                var cf = _customFoldersService.GetCustomFolders(new CustomFoldersFilterEntity()
                {
                    Environment = (short)CustomFoldersEnvironmentEnum.Matches,
                    IdOffer = _offer.idofferCT,
                    IdPortal = _companyCredentials.PortalId
                });

                foreach (var folder in cf)
                {
                    if (folder.Position == (int)CompanyCustomFoldersMatchEnum.Folder1 || folder.Position == (int)CompanyCustomFoldersMatchEnum.Folder2)
                        ListFolderDM.Add(new FoldersDataModel()
                        {
                            Position = folder.Position,
                            PositionEncrypted = _encryptionService.Encrypt((folder.Position).ToString()),
                            Count = 0,
                            NameFolder = folder.Name,
                            Id = folder.Id,
                            IdEncrypted = folder.IdEncrypted
                        });
                }
            }
        }

        private string fillName(int foo, PortalConfig portalConfig)
        {
            string name = string.Empty;
            switch (foo)
            {
                case (int)CompanyOfferFolderEnum.Recibidos:
                    name = PageLiteralsHelper.GetLiteral("LIT_INSCRITOS", PageId, portalConfig);
                    break;
                case (int)CompanyOfferFolderEnum.Seleccionados:
                    name = PageLiteralsHelper.GetLiteral("LIT_ENPROCESO", PageId, portalConfig);
                    break;
                case (int)CompanyOfferFolderEnum.Finalistas:
                    name = PageLiteralsHelper.GetLiteral("LIT_FINALISTAS", PageId, portalConfig);
                    break;
                case (int)CompanyOfferFolderEnum.Descartados:
                    name = PageLiteralsHelper.GetLiteral("LIT_DESCARTADOS", PageId, portalConfig);
                    break;
                case (int)CompanyOfferFolderEnum.BBDD:
                    name = PageLiteralsHelper.GetLiteral("LIT_BBDD_HDV", PageId, portalConfig);
                    break;
                case (int)CompanyOfferFolderEnum.Contratados:
                    name = PageLiteralsHelper.GetLiteral("LIT_CONTRATADOS", PageId, portalConfig);
                    break;
            }
            return name;
        }

        private void CanCreateChat(int currentFolder, PortalConfig portalConfig)
        {
            if (_haveMembresyActive)
            {
                _canCreateChat = true;

                if (!_hasChat)
                    _canCreateChat = false;

                if (portalConfig.has_chat != (int)PortalConfigStatusEnum.Activated)
                    _canCreateChat = false;

                if (currentFolder != (short)CompanyOfferFolderEnum.Finalistas
                    || currentFolder != (short)CompanyOfferFolderEnum.Seleccionados)
                    _canCreateChat = false;
            }
        }

        private void LoadSecurityExpiredOfferComplete(PortalConfig portalConfig)
        {
            _publishedOffers = OfferService.GetTotalOffersByCompanyLastDays(_companyCredentials.IdCompany, _companyCredentials.PortalId, OfferIntegratorEnum.CompuTrabajo);
            _contactEmail = PageLiteralsHelper.GetLiteral("EMAIL_CONTACTO_COMERCIAL_GENERICO", PageId, portalConfig);

            int calculateDaysToExpired = GetPassDaysExpiratedOffer(_offer.Integrations.FirstOrDefault()?.expirationtime ?? DateTime.MinValue);
            var checkDaysToExpired = 0;

            if (_companyProduct.Features.Any(f => f.AmbitId == (short)ProductAmbitEnum.ExpiredTimeOfferManagement))
            {
                checkDaysToExpired = _companyProduct.Features.FirstOrDefault(f => f.AmbitId == (short)ProductAmbitEnum.ExpiredTimeOfferManagement).InitialUnits;
            }
            else
            {
                switch (_offerProduct.GroupId)
                {
                    case (short)ProductGroupsEnum.Freemium:
                        checkDaysToExpired = portalConfig.ExpiredDatysOffersCompleted;
                        break;
                    case (short)ProductGroupsEnum.Packs:
                        checkDaysToExpired = portalConfig.ExpiredDatysOffersCompleted;
                        break;
                    default:
                        checkDaysToExpired = portalConfig.ExpiredDatysOffersMembership;
                        break;
                }
            }

            if (_offer.Integrations.FirstOrDefault()?.IsPayment == 1 && _offer.Integrations.FirstOrDefault()?.idstatus == (short)OfferStatusEnum.Vencida
                && portalConfig.BlockBasicOffers
                && calculateDaysToExpired > checkDaysToExpired)
            {
                _showMatchesData = false;
            }
        }

        private int GetPassDaysExpiratedOffer(DateTime expiration)
        {
            int differenceInDays = 0;
            if (DateTime.Now > expiration)
            {
                TimeSpan ts = DateTime.Now - expiration;
                differenceInDays = ts.Days;
            }
            return differenceInDays;
        }

        private void LoadData(int idOffer, PortalConfig portalConfig)
        {
            _companyCredentials = SecurityHelper.GetCompanyCredentials();
            _companyProduct = CompanyProductService.GetByIdCompany(_companyCredentials.IdCompany, _companyCredentials.PortalId, OfferIntegratorEnum.CompuTrabajo);
            _offer = OfferService.GetByPk(idOffer, portalConfig.PortalId);
            _offerProduct = CompanyProductService.GetByCompanyProductId(_offer.Integrations.FirstOrDefault()?.idcompanyproduct ?? 0, _offer.idportal, _offer.idcompany);
            SetMaxCvAppliedToSee(_offerProduct, portalConfig, idOffer);
            isCompletMatchesPage(portalConfig);
            isMembershipActive();
        }

        private void SetMaxCvAppliedToSee(CompanyProductEntity companyProduct, PortalConfig portalConfig, int idOffer)
        {
            var chatFreemiumFeature = companyProduct.Features.Find(x => x.AmbitId == (int)ProductAmbitEnum.ChatContactFreemium);

            if (portalConfig.AEPortalConfig.ViewMatchesByNumberBasicOffer
                && (!portalConfig.AEPortalConfig.EnableProgressiveChatFreemium || chatFreemiumFeature == null))
            {
                SetMaxCvAppliedToSeeByContent(portalConfig, idOffer);
            }
            else if (portalConfig.AEPortalConfig.EnableProgressiveChatFreemium && chatFreemiumFeature != null)
            {
                SetMaxCvAppliedToSeeByContact(companyProduct, portalConfig, idOffer, chatFreemiumFeature);
            }

            if (_maxCvAppliedToSee == 0)
            {
                _maxCvAppliedToSee = companyProduct.Features.Find(x => x.AmbitId == (int)ProductAmbitEnum.CVsFreemium)?.AvailableUnits ?? 0;
            }
        }

        private void SetMaxCvAppliedToSeeByContact(CompanyProductEntity companyProduct, PortalConfig portalConfig, int idOffer, CompanyProductFeatureEntity chatFreemiumFeature)
        {
            var chatContacts = _freemiumOfferContactsService.GetContactsByOffer(new FreemiumOfferMatchesContactsDTO() { OfferId = idOffer, PortalId = portalConfig.PortalId });

            if (chatContacts > NO_FREEMIUM_VIEWS_RESULTS)
            {
                _maxCvAppliedToSee = chatContacts;
            }

            if (_maxCvAppliedToSee == 0)
            {
                _maxCvAppliedToSee = chatFreemiumFeature?.AvailableUnits ?? 0;
            }

            _isProgressiveFreemiumChat = companyProduct.GroupId == (short)ProductGroupsEnum.Freemium;
        }

        private void SetMaxCvAppliedToSeeByContent(PortalConfig portalConfig, int idOffer)
        {
            var views = _freemiumOfferViewsService.GetViewsByOffer(new FreemiumOfferMatchesViewsDTO() { OfferId = idOffer, PortalId = portalConfig.PortalId });

            if (views > NO_FREEMIUM_VIEWS_RESULTS)
            {
                _maxCvAppliedToSee = views;
            }
        }

        private void LoadFeatures(PortalConfig portalConfig)
        {
            _hasSearchNit = portalConfig.search_by_nit && _haveMembresyActive;

            if (_haveMembresyActive)
            {
                _hasFolders = _offerProduct.Features.Any(f => f.AmbitId == (int)ProductAmbitEnum.CVFolders);
            }
            else
            {
                _hasFolders = CompanyProductService.OfferPackHasFeature(_offerProduct.Id, (int)ProductAmbitEnum.CVFolders, portalConfig.PortalId);
            }

            _hasCustomFolders = _offerProduct.Features.Any(f => f.AmbitId == (int)ProductAmbitEnum.CustomFolders);
            _hasOfferFlash = _offerProduct.Features.Any(f => f.AmbitId == (int)ProductAmbitEnum.OfferFlash);
            _hasRatings = _offerProduct.Features.Any(f => f.AmbitId == (int)ProductAmbitEnum.RatingsCvs);
            _hasComents = _offerProduct.Features.Any(f => f.AmbitId == (int)ProductAmbitEnum.CommentsCV);
            _hasChat = _offerProduct.Features.Any(f => f.AmbitId == (int)ProductAmbitEnum.MessagesMailing);
            _hasCvDownload = _offerProduct.Features.Any(f => f.AmbitId == (int)ProductAmbitEnum.CVsDownload);
            _hasCandidateComparator = _offerProduct.Features.Any(f => f.AmbitId == (int)ProductAmbitEnum.CandidateComparator);
            _hasSaveFilters = _offerProduct.Features.Any(f => f.AmbitId == (int)ProductAmbitEnum.SaveFilter);
            _hasExclusion = _offerProduct.Features.Any(f => f.AmbitId == (int)ProductAmbitEnum.ExclusionMatches);
            _offerProductHasCVBBDD = _offerProduct.Features.Any(f => f.AmbitId == (int)ProductAmbitEnum.CvBBDD);
            _hasAdequacy = _offerProduct.Features.Any(f => f.AmbitId == (int)ProductAmbitEnum.CandidateAdequacy);
            _hasKillerQuestions = (_offerProduct.Features.Any(f => f.AmbitId == (int)ProductAmbitEnum.KillerQuestions) || _offer.has_killer_questions == 1);
            _hasCVDownloadMatchesReceived = _offerProduct.Features.Any(f => f.AmbitId == (int)ProductAmbitEnum.CVDownloadMatchesReceived);
            _hasCandidateSuggester = _offerProduct.Features.Any(f => f.AmbitId == (int)ProductAmbitEnum.CandidateSuggester);
        }

        private void isMembershipActive()
        {
            _haveMembresyActive = _offerProduct.GroupId == (short)ProductGroupsEnum.Membership;
        }

        private void isCompletMatchesPage(PortalConfig portalConfig)
        {
            switch (_offerProduct.GroupId)
            {
                case (int)ProductGroupsEnum.Membership:
                    _haveOfferCompletOrMembership = true;
                    break;

                default:
                    switch (_offerProduct.GroupId)
                    {
                        case (int)ProductGroupsEnum.Freemium:
                            _haveOfferCompletOrMembership = false;
                            break;
                        case (int)ProductGroupsEnum.Packs:
                            if (_offer.idstatus == (short)OfferStatusEnum.Vencida && _offer.Integrations.FirstOrDefault()?.idproduct == (short)ProductEnum.FreemiumMigration && portalConfig.BlockBasicOffers)
                                _haveOfferCompletOrMembership = false;
                            else
                                _haveOfferCompletOrMembership = true;
                            break;
                        case (int)ProductGroupsEnum.Membership:
                            _haveOfferCompletOrMembership = true;
                            break;
                        default:
                            _haveOfferCompletOrMembership = false;
                            break;
                    }
                    break;
            }
        }

        private bool IsBlockedExpiredMembership(PortalConfig portalConfig)
        {
            return (
                portalConfig.blockOldMembership
                && _companyProduct.GroupId != (short)ProductGroupsEnum.Membership
                && _offerProduct.GroupId == (short)ProductGroupsEnum.Membership
                && _offer.Integrations.FirstOrDefault()?.idstatus == (short)OfferStatusEnum.Vencida
                );
        }

        private Dictionary<string, string> LoadSecurity(int idOffer, PortalConfig portalConfig, int currentFolder = 1)
        {
            var returnTo = new Dictionary<string, string>();

            if (!IsMy(_companyCredentials, idOffer, portalConfig))
            {
                returnTo.Add("Index", "Company");
                return returnTo;
            }
            if (IsBlockedExpiredMembership(portalConfig))
            {
                returnTo.Add("ExpiredMembership", "CompanyLanding");
                return returnTo;
            }
            if (_offer.Integrations.FirstOrDefault()?.idstatus == (short)OfferStatusEnum.Vencida && _offer.Integrations.FirstOrDefault()?.idproduct == (short)ProductEnum.FreemiumService)
            {
                returnTo.Add("Index", "Company");
                return returnTo;
            }

            return returnTo;
        }

        private PopUpDataModel FillPopUpLimitSaveFilters(PortalConfig portalConfig)
        {
            return new PopUpDataModel()
            {
                Title = PageLiteralsHelper.GetLiteral("LIT_SAVE_FILTER_LIMIT_TITLE", (int)PageEnum.MatchOffer, portalConfig),
                Message = $"{PageLiteralsHelper.GetLiteral("LIT_SAVE_FILTER_LIMIT_INFO_1", (int)PageEnum.MatchOffer, portalConfig)} {PageLiteralsHelper.GetLiteral("LIT_SAVE_FILTER_LIMIT_INFO_2", (int)PageEnum.MatchOffer, portalConfig)}",
                HasButtonOk = false
            };
        }

        private PopUpDataModel FillPopUpSaveFilters(PortalConfig portalConfig)
        {
            return new PopUpDataModel()
            {
                Title = PageLiteralsHelper.GetLiteral("LIT_SAVE_FILTER", (int)PageEnum.MatchOffer, portalConfig),
                Message = PageLiteralsHelper.GetLiteral("LIT_SAVE_FILTER_OFFER", (int)PageEnum.MatchOffer, portalConfig),
                ExtraDynamicMessage = PageLiteralsHelper.GetLiteral("LIT_SAVE_FILTER_ALL", (int)PageEnum.MatchOffer, portalConfig),
                DescriptionCheckbox = PageLiteralsHelper.GetLiteral("LIT_SAVE_FILTER_FOR_ALL", (int)PageEnum.MatchOffer, portalConfig),
                HasInput = true,
                HasCheckBox = true,
                TitleBtnOk = PageLiteralsHelper.GetLiteral("LIT_SAVE_FILTER", (int)PageEnum.MatchOffer, portalConfig),
                LiteralProcessing = PageLiteralsHelper.GetLiteral("LIT_PROCESSING", (int)PageEnum.MatchOffer, portalConfig),
                PlaceHolderInput = PageLiteralsHelper.GetLiteral("LIT_WRITE_FILTER_NAME", (int)PageEnum.MatchOffer, portalConfig),
                LiteralError = "errorNameSaveFilter"
            };
        }

        private PopUpDataModel FillPopUpRemoveSaveFilter(PortalConfig portalConfig)
        {
            return new PopUpDataModel()
            {
                Title = PageLiteralsHelper.GetLiteral("LIT_DELETE_SAVED_FILTER", (int)PageEnum.MatchOffer, portalConfig),
                Message = PageLiteralsHelper.GetLiteral("LIT_DELETE_SAVED_FILTER_CONFIRM", (int)PageEnum.MatchOffer, portalConfig),
                ExtraDynamicMessage = PageLiteralsHelper.GetLiteral("LIT_DELETE_SAVED_FILTER_CONFIRM_2", (int)PageEnum.MatchOffer, portalConfig),
                TitleBtnOk = PageLiteralsHelper.GetLiteral("LIT_ELIMINAR", (int)PageEnum.MatchOffer, portalConfig),
                LiteralProcessing = PageLiteralsHelper.GetLiteral("LIT_PROCESSING", (int)PageEnum.MatchOffer, portalConfig)
            };
        }

        private PopUpDataModel NoFolderToMakeChatPopUp(PortalConfig portalConfig)
        {
            return new PopUpDataModel()
            {
                Title = PageLiteralsHelper.GetLiteral("LIT_SEND_MESSAGE", (int)PageEnum.MatchOffer, portalConfig),
                Message = PageLiteralsHelper.GetLiteral("LIT_INFO_CHAT_UNI", (int)PageEnum.MatchOffer, portalConfig),
                TitleBtnOk = PageLiteralsHelper.GetLiteral("LIT_ADD", (int)PageEnum.MatchOffer, portalConfig),
                TitleBtnKo = PageLiteralsHelper.GetLiteral("LIT_CERRAR", (int)PageEnum.MatchOffer, portalConfig),
                LiteralProcessing = PageLiteralsHelper.GetLiteral("LIT_PROCESSING", (int)PageEnum.MatchOffer, portalConfig),
                LiteralError = string.Empty,
                HasTextArea = false,
                HasInput = false,
                HasButtonOk = false
            };
        }

        private PopUpDataModel ChatUni(PortalConfig portalConfig)
        {
            return new PopUpDataModel()
            {
                Title = PageLiteralsHelper.GetLiteral("LIT_SEND_MESSAGE", (int)PageEnum.MatchOffer, portalConfig),
                Message = PageLiteralsHelper.GetLiteral("LIT_INFO_CREATE_CHAT_UNI", (int)PageEnum.MatchOffer, portalConfig),
                TitleBtnOk = PageLiteralsHelper.GetLiteral("LIT_ADD", (int)PageEnum.MatchOffer, portalConfig),
                TitleBtnKo = PageLiteralsHelper.GetLiteral("LIT_SEND", (int)PageEnum.MatchOffer, portalConfig),
                LiteralProcessing = PageLiteralsHelper.GetLiteral("LIT_PROCESSING", (int)PageEnum.MatchOffer, portalConfig),
                LiteralError = "errChatUnidireccional",
                HasInput = true,
                HasTextArea = true,
                HasButtonOk = true
            };
        }

        private PopUpDataModel DeleteFolderPopUp(PortalConfig portalConfig)
        {
            return new PopUpDataModel()
            {
                Title = PageLiteralsHelper.GetLiteral("LIT_DELETE_FOLDER", (int)PageEnum.MatchOffer, portalConfig),
                Message = PageLiteralsHelper.GetLiteral("LIT_DELETE_DESCRIPTION", (int)PageEnum.MatchOffer, portalConfig),
                TitleBtnOk = PageLiteralsHelper.GetLiteral("LIT_DELETE", (int)PageEnum.MatchOffer, portalConfig),
                TitleBtnKo = PageLiteralsHelper.GetLiteral("LIT_CERRAR", (int)PageEnum.MatchOffer, portalConfig),
                LiteralProcessing = PageLiteralsHelper.GetLiteral("LIT_PROCESSING", (int)PageEnum.MatchOffer, portalConfig),
                LiteralError = "errDeleteFolder",
                HasInput = false,
                HasButtonOk = true
            };
        }

        private PopUpDataModel EditFolderPopUp(PortalConfig portalConfig)
        {
            return new PopUpDataModel()
            {
                Title = PageLiteralsHelper.GetLiteral("LIT_UPDATE_FOLDER", (int)PageEnum.MatchOffer, portalConfig),
                Message = PageLiteralsHelper.GetLiteral("LIT_NAME_FOLDER", (int)PageEnum.MatchOffer, portalConfig),
                TitleBtnOk = PageLiteralsHelper.GetLiteral("LIT_UPDATE", (int)PageEnum.MatchOffer, portalConfig),
                TitleBtnKo = PageLiteralsHelper.GetLiteral("LIT_CERRAR", (int)PageEnum.MatchOffer, portalConfig),
                LiteralProcessing = PageLiteralsHelper.GetLiteral("LIT_PROCESSING", (int)PageEnum.MatchOffer, portalConfig),
                LiteralError = "errUpdateFolder",
                HasInput = true,
                HasButtonOk = true
            };
        }

        private PopUpDataModel AddFolderPopUp(PortalConfig portalConfig)
        {
            return new PopUpDataModel()
            {
                Title = PageLiteralsHelper.GetLiteral("LIT_ADD_FODLER", (int)PageEnum.MatchOffer, portalConfig),
                Message = PageLiteralsHelper.GetLiteral("LIT_NAME_FOLDER", (int)PageEnum.MatchOffer, portalConfig),
                TitleBtnOk = PageLiteralsHelper.GetLiteral("LIT_ADD", (int)PageEnum.MatchOffer, portalConfig),
                TitleBtnKo = PageLiteralsHelper.GetLiteral("LIT_CERRAR", (int)PageEnum.MatchOffer, portalConfig),
                LiteralProcessing = PageLiteralsHelper.GetLiteral("LIT_PROCESSING", (int)PageEnum.MatchOffer, portalConfig),
                LiteralError = "errAddFolder",
                HasInput = true,
                HasButtonOk = true
            };
        }

        private PopUpDataModel NoIdsSelectedPopUp(PortalConfig portalConfig)
        {
            return new PopUpDataModel()
            {
                Title = PageLiteralsHelper.GetLiteral("LIT_NO_SPECIFIED", (int)PageEnum.MatchOffer, portalConfig),
                Message = PageLiteralsHelper.GetLiteral("LIT_INFO_NOT_CANDIDATES_SELECTED", (int)PageEnum.MatchOffer, portalConfig),
                TitleBtnOk = string.Empty,
                TitleBtnKo = PageLiteralsHelper.GetLiteral("LIT_CERRAR", (int)PageEnum.MatchOffer, portalConfig),
                LiteralProcessing = string.Empty,
                LiteralError = string.Empty,
                HasInput = false,
                HasButtonOk = false
            };
        }

        private PopUpDataModel NoIdsSelectedPrintPopUp(PortalConfig portalConfig)
        {
            return new PopUpDataModel()
            {
                Title = PageLiteralsHelper.GetLiteral("LIT_PRINT_APPLICATIONS", (int)PageEnum.MatchOffer, portalConfig),
                Message = PageLiteralsHelper.GetLiteral("LIT_SELECT_PROFILES_TO_PRINT", (int)PageEnum.MatchOffer, portalConfig),
                TitleBtnOk = string.Empty,
                TitleBtnKo = PageLiteralsHelper.GetLiteral("LIT_CERRAR", (int)PageEnum.MatchOffer, portalConfig),
                LiteralProcessing = string.Empty,
                LiteralError = string.Empty,
                HasInput = false,
                HasButtonOk = false
            };
        }

        private PopUpDataModel NoIdsSelectedChatPopUp(PortalConfig portalConfig)
        {
            return new PopUpDataModel()
            {
                Title = PageLiteralsHelper.GetLiteral("LIT_SEND_MESSAGE", (int)PageEnum.MatchOffer, portalConfig),
                Message = PageLiteralsHelper.GetLiteral("LIT_ONLY_SEND_MSG_TO_CANDIDATES_SELECTED", (int)PageEnum.MatchOffer, portalConfig),
                TitleBtnOk = string.Empty,
                TitleBtnKo = PageLiteralsHelper.GetLiteral("LIT_CERRAR", (int)PageEnum.MatchOffer, portalConfig),
                LiteralProcessing = string.Empty,
                LiteralError = string.Empty,
                HasInput = false,
                HasButtonOk = false
            };
        }

        private PopUpDataModel NoIdsSelectedDeletePopUp(PortalConfig portalConfig)
        {
            return new PopUpDataModel()
            {
                Title = PageLiteralsHelper.GetLiteral("LIT_DISCARD_APPLICATIONS", (int)PageEnum.MatchOffer, portalConfig),
                Message = PageLiteralsHelper.GetLiteral("LIT_SELECT_HDV_TO_DISCARTED", (int)PageEnum.MatchOffer, portalConfig),
                TitleBtnOk = string.Empty,
                TitleBtnKo = PageLiteralsHelper.GetLiteral("LIT_CERRAR", (int)PageEnum.MatchOffer, portalConfig),
                LiteralProcessing = string.Empty,
                LiteralError = string.Empty,
                HasInput = false,
                HasButtonOk = false
            };
        }

        private PopUpDataModel SureToLogicDelete(PortalConfig portalConfig)
        {
            return new PopUpDataModel()
            {
                Title = PageLiteralsHelper.GetLiteral("ACTION_OPTION_2", (int)PageEnum.MatchOffer, portalConfig),
                Message = PageLiteralsHelper.GetLiteral("LIT_INFO_DELETE_CANDIDATE", (int)PageEnum.MatchOffer, portalConfig),
                TitleBtnOk = PageLiteralsHelper.GetLiteral("LIT_ELIMINAR", (int)PageEnum.MatchOffer, portalConfig),
                TitleBtnKo = PageLiteralsHelper.GetLiteral("LIT_CERRAR", (int)PageEnum.MatchOffer, portalConfig),
                LiteralProcessing = PageLiteralsHelper.GetLiteral("LIT_PROCESSING", (int)PageEnum.MatchOffer, portalConfig),
                LiteralError = string.Empty,
                HasInput = false,
                HasButtonOk = true,
            };
        }

        private PopUpDataModel SureToDiscardCandidate(PortalConfig portalConfig)
        {
            return new PopUpDataModel()
            {
                Title = PageLiteralsHelper.GetLiteral("LIT_DISCARD_APPLICATIONS", (int)PageEnum.MatchOffer, portalConfig),
                Message = PageLiteralsHelper.GetLiteral("LIT_MOVE_TO_DISCARD", (int)PageEnum.MatchOffer, portalConfig),
                TitleBtnOk = PageLiteralsHelper.GetLiteral("LIT_DISCARD", (int)PageEnum.MatchOffer, portalConfig),
                TitleBtnKo = PageLiteralsHelper.GetLiteral("LIT_CERRAR", (int)PageEnum.MatchOffer, portalConfig),
                LiteralProcessing = PageLiteralsHelper.GetLiteral("LIT_PROCESSING", (int)PageEnum.MatchOffer, portalConfig),
                LiteralError = string.Empty,
                HasInput = false,
                HasButtonOk = true,
            };
        }

        private PopUpDataModel SetRedirectToCartPopUp(PortalConfig portalConfig)
        {
            return new PopUpDataModel()
            {
                Title = PageLiteralsHelper.GetLiteral("H3_CONTRATE_PACK_MEMBRESIA", (int)PageEnum.MatchOffer, portalConfig),
                Message = PageLiteralsHelper.GetLiteral("P_DESEA_ACTUALIZAR_MEMBRESIA", (int)PageEnum.MatchOffer, portalConfig),
                TitleBtnOk = PageLiteralsHelper.GetLiteral("LIT_DESCUBRA_MAS", (int)PageEnum.MatchOffer, portalConfig),
                TitleBtnKo = PageLiteralsHelper.GetLiteral("LIT_CANCELAR", (int)PageEnum.MatchOffer, portalConfig),
                HasInput = false,
                HasButtonOk = true
            };
        }

        private PopUpDataModel LostSessionPopUp(PortalConfig portalConfig)
        {
            return new PopUpDataModel()
            {
                Title = PageLiteralsHelper.GetLiteral("TITLE_LOST_SESSION_POP_UP", (int)PageEnum.MatchOffer, portalConfig),
                Message = PageLiteralsHelper.GetLiteral("MSG_LOST_SESSION_POP_UP", (int)PageEnum.MatchOffer, portalConfig),
                TitleBtnKo = PageLiteralsHelper.GetLiteral("LIT_CERRAR", (int)PageEnum.MatchOffer, portalConfig),
                HasButtonOk = false
            };
        }

        private PopUpDataModel NoIdsCandidateComparatorPopUp(PortalConfig portalConfig)
        {
            return new PopUpDataModel()
            {
                Title = PageLiteralsHelper.GetLiteral("LIT_COMPARE_APPLICATIONS", (int)PageEnum.MatchOffer, portalConfig),
                Message = PageLiteralsHelper.GetLiteral("LIT_NO_CANDIDATES_TO_COMPARE", (int)PageEnum.MatchOffer, portalConfig),
                TitleBtnOk = string.Empty,
                TitleBtnKo = PageLiteralsHelper.GetLiteral("LIT_CERRAR", (int)PageEnum.MatchOffer, portalConfig),
                LiteralProcessing = string.Empty,
                LiteralError = string.Empty,
                HasInput = false,
                HasButtonOk = false
            };
        }

        private PopUpDataModel LimitIdsCandidateComparatorPopUp(PortalConfig portalConfig)
        {
            return new PopUpDataModel()
            {
                Title = PageLiteralsHelper.GetLiteral("LIT_CANDIDATES_TO_COMPARE", (int)PageEnum.MatchOffer, portalConfig),
                Message = string.Format(PageLiteralsHelper.GetLiteral("LIT_MAX_CANDIDATES_TO_COMPARE", (int)PageEnum.MatchOffer, portalConfig), LIMIT_CANDIDATES_TO_COMPARE),
                TitleBtnOk = string.Empty,
                TitleBtnKo = PageLiteralsHelper.GetLiteral("LIT_CERRAR", (int)PageEnum.MatchOffer, portalConfig),
                LiteralProcessing = string.Empty,
                LiteralError = string.Empty,
                HasInput = false,
                HasButtonOk = false
            };
        }

        private List<string> TransformLanguageDataModelToLanguageSearchFilter(int? selectedLanguage, int? selectedLanguageLevel)
        {
            var result = new List<string>();

            if (selectedLanguage != null && selectedLanguage > 0)
            {
                var stringBuilder = string.Empty;
                stringBuilder = selectedLanguage.ToString();

                if (selectedLanguageLevel != null)
                    stringBuilder = $"{stringBuilder}_{selectedLanguageLevel.ToString()}";

                result.Add(stringBuilder);
            }

            return result;
        }

        private List<string> TransformStudyAndStatusDataModelToStudyAndStatusSearchFilter(List<int> selectedStudies, int selectedStatus, List<SelectListItem> studiesStatus)
        {
            var result = new List<string>();
            if (selectedStudies.Any())
            {
                foreach (var study in selectedStudies)
                {
                    if (selectedStatus == 0)
                    {
                        foreach (var status in studiesStatus)
                        {
                            result.Add($"{study}_{status.Value}");
                        }
                    }
                    else
                    {
                        result.Add($"{study}_{selectedStatus}");
                    }
                }
            }
            else if (selectedStatus > 0)
            {
                result.Add($"*_{selectedStatus}");
            }
            return result;
        }

        private ActionResult CreateNewSaveFilterAndRedirect(MultifiltersDataModel multifiltersDataModel, string idofferEncrypted, string idofferCTEncrypted, string idFolderEncrypted, PortalConfig portalConfig)
        {
            var companyProduct = CompanyProductService.GetByIdCompany(_companyCredentials.IdCompany, _companyCredentials.PortalId, OfferIntegratorEnum.CompuTrabajo);
            int.TryParse(_encryptionService.Decrypt(idofferEncrypted), out var idOffer);
            int.TryParse(_encryptionService.Decrypt(idofferCTEncrypted), out var idOfferCT);
            int.TryParse(_encryptionService.Decrypt(idFolderEncrypted), out var idFolder);

            var failInSecurity = LoadSecurity(idOffer, portalConfig, idFolder);
            if (failInSecurity.Any())
                return RedirectToAction(failInSecurity.FirstOrDefault().Key, failInSecurity.FirstOrDefault().Value);

            if (CanCreateNewSaveFilter(companyProduct, idOfferCT))
            {
                if (_offer.idoffer == 0)
                    _offer = OfferService.GetByPk(idOffer, portalConfig.PortalId);

                if (multifiltersDataModel.AppliedNewSaveFilterToAllOffers)
                {
                    idOfferCT = 0;
                    idOffer = 0;
                }


                var companyFilter = new CompanyFilterEntity()
                {
                    Cities = multifiltersDataModel.SelectedCities,
                    Comment = multifiltersDataModel.Comments,
                    CvVisualitzed = multifiltersDataModel.IsMatchesVisualitzed,
                    Exclusion = multifiltersDataModel.Exclusion,
                    FilterName = multifiltersDataModel.NameNewSaveFilter,
                    Gender = multifiltersDataModel.Gender,
                    Language = multifiltersDataModel.SelectedLanguage,
                    LanguageLevel = multifiltersDataModel.SelectedLanguageLevel,
                    Localizations = multifiltersDataModel.SelectedLocalization,
                    MaxAge = multifiltersDataModel.MaxAge,
                    MinAge = multifiltersDataModel.MinAge,
                    MaxSalary = multifiltersDataModel.MaxSalary,
                    MinSalary = multifiltersDataModel.MinSalary,
                    Nationalities = multifiltersDataModel.SelectedNationatility,
                    Photo = multifiltersDataModel.HasPhoto,
                    ProfesionalCategories = multifiltersDataModel.SelectedProfesionalCategories,
                    Rating = multifiltersDataModel.Rating,
                    SearchName = multifiltersDataModel.SearchName,
                    SearchNit = multifiltersDataModel.SearchNit,
                    StudyLevels = multifiltersDataModel.SelectedStudyLevel,
                    HasDisability = multifiltersDataModel.HasDisability,
                    ApplicationDateFrom = multifiltersDataModel.ApplicationDateFrom,
                    Career = multifiltersDataModel.Career,
                    ExperienceProfession = multifiltersDataModel.ExperienceProfession,
                    StudyAndStatus = TransformStudyAndStatusDataModelToStudyAndStatusSearchFilter(multifiltersDataModel.SelectedStudyAndStatusByLevel,
                                                                                                            multifiltersDataModel.SelectedStudyAndStatusByStatus.FirstOrDefault(),
                                                                                                            GetFromDictionary(portalConfig, DictionaryEnum.STUDY_STATUS)),
                    HasCompetenceTest = multifiltersDataModel.HasCompetenceTest,
                    CompetenceTestMin = multifiltersDataModel.CompetenceTestMin,
                    CompetenceTestMax = multifiltersDataModel.CompetenceTestMax,
                    CompetenceTestKey = multifiltersDataModel.CompetenceTestKey,
                    HasAdequacy = multifiltersDataModel.HasAdequacy,
                    AdequacyMin = multifiltersDataModel.AdequacyMin,
                    AdequacyMax = multifiltersDataModel.AdequacyMax,
                    ExactSearchText = multifiltersDataModel.ExactSearchText,
                    SearchText = multifiltersDataModel.MultiSearchText,
                    Distance = multifiltersDataModel.Distance,
                    HasTalentViewTest = multifiltersDataModel.HasTalentViewTest,
                };

                var idNewSaveFolder = _companyFiltersService.AddFilterFromCompanyFilterEntity(companyFilter, _companyCredentials.IdCompany, _companyCredentials.UserId, idOfferCT, _companyCredentials.PortalId);

                if (!string.IsNullOrEmpty(idNewSaveFolder))
                    return RedirectToAction("Index", "CompanyMatches", new { oi = idofferEncrypted, cf = idFolderEncrypted, ft = idNewSaveFolder });
            }

            return RedirectToAction("Index", "CompanyMatches", new { oi = idofferEncrypted, cf = idFolderEncrypted });
        }

        private bool CanCreateNewSaveFilter(CompanyProductEntity companyProduct, int idOfferCT)
        {
            if (companyProduct.Features.Any(f => f.AmbitId == (int)ProductAmbitEnum.SaveFilter)
                && SecurityHelper.IsActionPemitedByRoleAndIsNotAdvisorRole(SecurityActionEnum.CompanyOffersManageFilters)
                && idOfferCT > 0)
                return GetCompanyFiltersFilterMatches(idOfferCT).Count < 20;

            return false;
        }

        [HttpPost]
        public JsonResult DeleteSaveFilter(string oi, string ft)
        {
            try
            {
                var response = false;

                if (!string.IsNullOrEmpty(ft))
                {
                    var companyCredentials = SecurityHelper.GetCompanyCredentials();

                    if (SecurityHelper.IsActionPemitedByRoleAndIsNotAdvisorRole(SecurityActionEnum.CompanyOffersManageFilters))
                        response = _companyFiltersService.DeleteFilterEncrypted("0", ft, companyCredentials.IdCompany, (int)companyCredentials.UserId, companyCredentials.PortalId);
                }

                return Json(response, JsonRequestBehavior.AllowGet);
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyMatchesController DeleteSaveFilter {ex}");
                ExceptionPublisherService.Publish(ex, "CompanyMatchesController", "DeleteSaveFilter");
                return new JsonResult();
            }
        }

        private PopUpDataModel GetActionNotAllowedPopUpByMembership(PortalConfig portalConfig)
        {
            int pageId = (short)PageEnum.MatchOffer;

            return new PopUpDataModel()
            {
                Title = PageLiteralsHelper.GetLiteral("LIT_ACTION_NOT_ALLOWED_MEMBERSHIP_TITLE", pageId, portalConfig),
                Message = PageLiteralsHelper.GetLiteral("LIT_ACTION_NOT_ALLOWED_MEMBERSHIP", pageId, portalConfig),
                TitleBtnOk = PageLiteralsHelper.GetLiteral("LIT_BTN_CONTACTAR", pageId, portalConfig),
                HasButtonOk = true,
            };
        }

        private bool SendTotalNewDecreaserToRabbit(PortalConfig portalConfig, int offerId, SearchResultDTO<MatchElasticEntity> matchesResultElastic)
        {
            var candidatesToUpdateMatchProcess = matchesResultElastic.Matches.Where(candi => candi.processStatusId < (int)MatchCandidateProcessStatusEnum.Viewed).Select(e => e.Id).ToList();
            if (!candidatesToUpdateMatchProcess.Any()) return false;

            return _matchService.PublishInRabbit(new PublishTotalNewMatchesDecreaserInRabbitDTO()
            {
                idApp = portalConfig.idapp,
                idQueue = portalConfig.AEPortalConfig.IdQueueRabbitTotalNewDecreaser,
                matchId = candidatesToUpdateMatchProcess,
                offerId = offerId,
                portalId = portalConfig.PortalId
            });
        }

        private void MapToDataLayer(HomeMatchesDataModel homeDataModel, CompanyCredentials companyCredentials, string portalId)
        {
            MultifiltersDataModel multifiltersDataModel = homeDataModel.MultifiltersDataModel;
            GTMDataLayerCVFilters dataLayerCVFilters = new GTMDataLayerCVFilters()
            {
                IdPortal = portalId,
                CompanyId = _encryptionService.Encrypt(companyCredentials.IdCompany.ToString()),
                UserId = companyCredentials.UserId > 0 ? "computrabajo-" + companyCredentials.PortalId.ToString() + "-" + _encryptionService.Encrypt(companyCredentials.UserId.ToString()) : "0",
                ise_ct_visualizacioncvs = multifiltersDataModel.IsMatchesVisualitzed > -1,
                ise_ct_categoriaprofesional = multifiltersDataModel.SelectedProfesionalCategories.Any(),
                ise_ct_edad = multifiltersDataModel.MinAge > 0 || multifiltersDataModel.MaxAge > 0,
                ise_ct_genero = multifiltersDataModel.Gender > 0,
                ise_ct_estado = multifiltersDataModel.SelectedLocalization.Any(),
                ise_ct_nacionalidad = multifiltersDataModel.SelectedNationatility.Any(),
                ise_ct_idiomas = multifiltersDataModel.SelectedLanguage > 0 ,
                ise_ct_pretensionsalarial = multifiltersDataModel.MinSalary > 0 || multifiltersDataModel.MaxSalary > 0,
                ise_ct_discapacidad = multifiltersDataModel.HasDisability > -1,
                ise_ct_nombrecompleto = !string.IsNullOrWhiteSpace(multifiltersDataModel.MultiSearchNameText),
                ise_ct_fotografiacv = multifiltersDataModel.HasPhoto > 0,
                ise_page = homeDataModel.Pager.PageSelected
            };

            TempData[PianoEvents.FILTER_DATALAYER_KEY] = dataLayerCVFilters;
        }
    }
}

