using Redarbor.Common.Entities.Configuration;
using Redarbor.Common.Entities.Enums;
using Redarbor.Company.Contracts.ServiceLibrary;
using Redarbor.Company.WebUI.Helpers;
using Redarbor.Company.WebUI.Models.Company.Vision;
using Redarbor.Company.WebUI.Security;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Core.Kpi.Contracts.ServiceLibrary;
using Redarbor.Core.Kpi.Contracts.ServiceLibrary.Enums;
using Redarbor.Core.Shared.Contracts.ServiceLibrary;
using Redarbor.Core.Stack.Contracts.ServiceLibrary;
using Redarbor.Master.Contracts.ServiceLibrary.DTO;
using Redarbor.Master.Entities.Company;
using Redarbor.Master.Entities.Enums;
using Redarbor.Master.Entities.Searchs;
using Redarbor.Master.Entities.Vision;
using Redarbor.Products.Contracts.ServiceLibrary;
using Redarbor.Vision.Contracts.ServiceLibrary;
using Redarbor.Web.UI.Library.Controllers;
using Redarbor.Web.UI.Library.Exceptions;
using System;
using System.Diagnostics;
using System.Linq;
using System.Web.Mvc;

namespace Redarbor.Company.WebUI.Controllers
{
    [RedarborAuthorize]
    public class CompanyVisionValuationRequestController : RedarborController
    {
        private readonly ICompanyService _companyService;
        private readonly ICompanyProductService _companyProductService;
        private readonly IPortalConfigurationService _portalConfigService;
        private readonly IGlobalVisionService _globalVisionService;
        private readonly IStackModService _stackModService;
        private readonly IExceptionPublisherService _exceptionPublisherService;
        private readonly IKpiService _kpiService;
        private readonly IEncryptionService _encrytionService;

        private const short PAGE_ID = (short)PageEnum.VisionValuationRequest;

        public CompanyVisionValuationRequestController(ICompanyService companyService,
            IPortalConfigurationService portalConfigService,
            IGlobalVisionService globalVisionService,
            IStackModService stackModService,
            ICompanyProductService companyProductService,
            IExceptionPublisherService exceptionPublisherService,
            IKpiService kpiservice,
            IEncryptionService encryptionService)
        {
            _companyService = companyService;
            _portalConfigService = portalConfigService;
            _globalVisionService = globalVisionService;
            _stackModService = stackModService;
            _exceptionPublisherService = exceptionPublisherService;
            _companyProductService = companyProductService;
            _kpiService = kpiservice;
            _encrytionService = encryptionService;
        }

        [Route("Company/EmployerBranding/ValuationRequest")]
        [Route("Company/EmproyerBranding/ValuationRequest")]
        public ActionResult Index(string afb, string afm, string boxGreen)
        {
            try
            {
                var companyCredentials = SecurityHelper.GetCompanyCredentials();
                var companyProduct = _companyProductService.GetByIdCompany(companyCredentials.IdCompany, companyCredentials.PortalId, OfferIntegratorEnum.CompuTrabajo);

                if (!CompanyHelper.IsVisionAccesPermited(companyCredentials) 
                    || !companyProduct.Features.Exists(f => f.AmbitId == (short)ProductAmbitEnum.VisionNumberGetReviews) 
                    || !SecurityHelper.IsActionPemitedByRole(Master.Entities.Enums.SecurityActionEnum.AllVisionAcces))
                {
                    return RedirectToAction("Index", "Company");
                }

                var featureGetReviews = companyProduct.Features.FirstOrDefault(f => f.AmbitId == (short)ProductAmbitEnum.VisionNumberGetReviews);

                var portalConfig = _portalConfigService.GetPortalConfiguration();
               


                AddedKpis(portalConfig, afb, afm);

                var company = _companyService.GetByPK(new CompanySearchSpecifications(companyCredentials.PortalId) { Id = companyCredentials.IdCompany });

                if (checkBlocked(portalConfig.company_block, company.Blocked, company.LastDateBlockedUpdate, portalConfig.DaysBlock))
                    return RedirectToAction("Blocked", "Account");

                var visionRequestValuationdataModel = new VisionRequestValuationDataModel()
                {
                    CompanyMaxSendEmailDaily = featureGetReviews.InitialUnits,
                    TextMessage = string.Format(PageLiteralsHelper.GetLiteral("LIT_DEFAUT_MESSAGE", PAGE_ID, portalConfig), company.ComercialName),
                    LimitDailySend = featureGetReviews.AvailableUnits <= 0,
                    NumRequestSend = featureGetReviews.AvailableUnits,
                    ShowBoxGreen = !string.IsNullOrEmpty(boxGreen) && boxGreen.ToLower() == "true"
                };

                return View(visionRequestValuationdataModel);
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyVisionValuationRequestController Index {ex}");
                _exceptionPublisherService.Publish(ex, "CompanyVisionValuationRequestController", "Index");
                return RedirectToAction("Index", "Company");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [Route("Company/EmployerBranding/ValuationRequest")]
        [Route("Company/EmproyerBranding/ValuationRequest")]
        public ActionResult Index(VisionRequestValuationDataModel visionRequestValuationDataModel)
        {
            try
            {
                var companyCredentials = SecurityHelper.GetCompanyCredentials();
                var companyProduct = _companyProductService.GetByIdCompany(companyCredentials.IdCompany, companyCredentials.PortalId, OfferIntegratorEnum.CompuTrabajo);

                if (!CompanyHelper.IsVisionAccesPermited(companyCredentials) || !companyProduct.Features.Exists(f => f.AmbitId == (short)ProductAmbitEnum.VisionNumberGetReviews))
                {
                    return RedirectToAction("Index", "Company");
                }

                var portalConfig = _portalConfigService.GetPortalConfiguration();
                var featureGetReviews = companyProduct.Features.FirstOrDefault(f => f.AmbitId == (short)ProductAmbitEnum.VisionNumberGetReviews);

                if (featureGetReviews.AvailableUnits > 0 && ModelState.IsValid)
                {
                    var company = _companyService.GetByPK(new CompanySearchSpecifications(companyCredentials.PortalId) { Id = companyCredentials.IdCompany });
                    var listEmail = visionRequestValuationDataModel.TextTo.Split(',').Where(email => email != "").ToList();

                    if (listEmail.Count <= featureGetReviews.AvailableUnits)
                    {
                        visionRequestValuationDataModel.TextSubject = visionRequestValuationDataModel.TextSubject ?? string.Empty;

                        foreach (string email in listEmail)
                        {
                            if (InsertSend(email, companyCredentials, portalConfig, visionRequestValuationDataModel, company)
                                && _companyProductService.ConsumeUnits(companyCredentials.IdCompany, companyCredentials.PortalId, (short)ProductAmbitEnum.VisionNumberGetReviews, 1, companyCredentials.UserId, DateTime.MinValue, OfferIntegratorEnum.CompuTrabajo))
                            {
                                visionRequestValuationDataModel.ShowBoxGreen = true;
                                featureGetReviews.AvailableUnits--;
                            }
                        }

                        if (visionRequestValuationDataModel.ShowBoxGreen)
                        {
                            AddedKpisToSend(portalConfig, listEmail.Count());
                            return RedirectToAction("index", "CompanyVisionValuationRequest", new { afb = string.Empty, afm = string.Empty, boxGreen = "true" });
                        }
                    }
                    else
                    {
                        visionRequestValuationDataModel.ShowBoxRed = true;
                    }

                    visionRequestValuationDataModel.TextTo = string.Empty;
                    visionRequestValuationDataModel.TextMessage = string.Format(PageLiteralsHelper.GetLiteral("LIT_DEFAUT_MESSAGE", PAGE_ID, portalConfig), company.ComercialName);
                    visionRequestValuationDataModel.TextSubject = string.Empty;
                    visionRequestValuationDataModel.LimitDailySend = featureGetReviews.AvailableUnits <= 0;
                    visionRequestValuationDataModel.NumRequestSend = featureGetReviews.AvailableUnits;
                }
                else
                {
                    visionRequestValuationDataModel.ShowBoxRed = true;
                }

                return View(visionRequestValuationDataModel);
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyVisionValuationRequestController Index-Post {ex}");
                _exceptionPublisherService.Publish(ex, "CompanyVisionValuationRequestController", "Index-Post");
                return RedirectToAction("Index", "Company");
            }
        }

        private void AddedKpis(PortalConfig portalConfig, string accessFromButtonCompanyHome, string accessFromMenu)
        {
            if (_encrytionService.Decrypt(accessFromButtonCompanyHome) == "1")
                _kpiService.Add((short)KpiEnum.ADVISOR_COMPANY_GET_REVIEWS_PAGE_SHOWED_BY_BUTTON_RIGTH, portalConfig.PortalId);
            if (_encrytionService.Decrypt(accessFromMenu) == "1")
                _kpiService.Add((short)KpiEnum.ADVISOR_COMPANY_GET_REVIEWS_PAGE_SHOWED_BY_MENU, portalConfig.PortalId);
        }

        private void AddedKpisToSend(PortalConfig portalConfig, int totalMails)
        {
            _kpiService.Add((short)KpiEnum.ADVISOR_COMPANY_GET_REVIEWS_TOTAL_SENDED, portalConfig.PortalId);
            _kpiService.AddSumBlock((short)KpiEnum.ADVISOR_COMPANY_GET_REVIEWS_TOTAL_MAILS, portalConfig.PortalId, totalMails);
        }

        private bool InsertSend(string email, CompanyCredentials companyCredentials, PortalConfig portalConfig, VisionRequestValuationDataModel visionRequestValuationDataModel, CompanyEntity company)
        {
            SendToModerationIfCompanyStatusIsRejected(company);

            return _globalVisionService.InsertValuationRequestMail(new CompanyValuationRequestMailEntity
            {
                To = email,
                From = "<EMAIL>",
                Message = visionRequestValuationDataModel.TextMessage.Replace(Environment.NewLine, "<br>"),
                Subject = visionRequestValuationDataModel.TextSubject,
                IdApp = portalConfig.idapp,
                IdCompany = companyCredentials.IdCompany,
                IdUser = (int)companyCredentials.UserId,
                IdPortal = companyCredentials.PortalId,
                IdStatus = (int)NotificationSendStatusEnum.PENDING_PROCESSING,
                PetitionDate = DateTime.Now
            });
        }

        private void SendToModerationIfCompanyStatusIsRejected(CompanyEntity company)
        {
            if (company.CompanyStatusId != (int)CompanyStatusEnum.Discarted) return;
            company.CompanyStatusId = (int)CompanyStatusEnum.Pending;
            company.StatusId = (int)StatusEnum.Active;

            _stackModService.Add(new Master.Entities.Stack.StackModEntity()
            {
                objectid = company.Id,
                typeid = (short)StackObjectTypeEnum.Company,
                idportal = company.PortalId
            });
        }

        public JsonResult ValidateTo()
        {
            var emails = Request.QueryString["TextTo"] ?? string.Empty;
            var result = false;

            if (!string.IsNullOrEmpty(emails))
            {
                var emailsTo = emails.Split(',').Where(e => !string.IsNullOrEmpty(e)).ToList();

                if (emailsTo.Any())
                {
                    result = true;

                    foreach (string email in emailsTo)
                        if (!StringToolsHelper.IsAValidEmail(email))
                            result = false;
                }
            }

            return Json(result, JsonRequestBehavior.AllowGet);
        }
    }
}