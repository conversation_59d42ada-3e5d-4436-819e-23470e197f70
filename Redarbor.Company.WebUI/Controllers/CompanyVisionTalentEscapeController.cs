using Redarbor.Common.Entities.Enums;
using Redarbor.Company.Contracts.ServiceLibrary;
using Redarbor.Company.WebUI.Helpers;
using Redarbor.Company.WebUI.Models.Company.Vision;
using Redarbor.Company.WebUI.Security;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Master.Contracts.ServiceLibrary.DTO;
using Redarbor.Master.Entities.Enums;
using Redarbor.Master.Entities.Searchs;
using Redarbor.Master.Entities.Vision;
using Redarbor.Products.Contracts.ServiceLibrary;
using Redarbor.Vision.Contracts.ServiceLibrary;
using Redarbor.Web.UI.Library.Controllers;
using Redarbor.Web.UI.Library.Exceptions;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Web.Mvc;

namespace Redarbor.Company.WebUI.Controllers
{
    [RedarborAuthorize]
    public class CompanyVisionTalentEscapeController : RedarborController
    {
        private readonly ITalentEscapeService _talentEscapeService;
        private readonly IExceptionPublisherService _exceptionPublisherService;
        private readonly IPortalConfigurationService _portalConfigurationService;
        private readonly ICompanyService _companyService;
        private readonly ICompanyProductService _companyProductService;

        public CompanyVisionTalentEscapeController(ITalentEscapeService talentEscapeService,
            IExceptionPublisherService exceptionPublisherService,
            IPortalConfigurationService portalConfigurationService,
            ICompanyService companyService,
            ICompanyProductService companyProductService)
        {
            this._talentEscapeService = talentEscapeService;
            _exceptionPublisherService = exceptionPublisherService;
            _portalConfigurationService = portalConfigurationService;
            _companyService = companyService;
            _companyProductService = companyProductService;
        }

        [Route("Company/EmployerBranding/TalentEscape")]
        [Route("Company/EmproyerBranding/TalentEscape")]
        public ActionResult Index()
        {
            try
            {
                var companyCredentials = SecurityHelper.GetCompanyCredentials();
                var portalConfig = _portalConfigurationService.GetPortalConfiguration();
                var company = _companyService.GetByPK(new CompanySearchSpecifications(companyCredentials.PortalId) { Id = companyCredentials.IdCompany });

                if (checkBlocked(portalConfig.company_block, company.Blocked, company.LastDateBlockedUpdate, portalConfig.DaysBlock))
                    return RedirectToAction("Blocked", "Account");

                int maxMovements = 10;

                if (!CompanyHelper.IsVisionAccesPermited(companyCredentials))
                {
                    return RedirectToAction("Index", "Company");
                }

                return View(this.LoadModel(companyCredentials, maxMovements));
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyVisionTalentEscapeController Index {ex}");
                _exceptionPublisherService.Publish(ex, "CompanyVisionTalentEscapeController", "Index");
                return RedirectToAction("Index", "Company");
            }
        }

        private VisionTalentEscapeModel LoadModel(CompanyCredentials companyCredentials, int maxMovements)
        {
            List<TalentEscapeEntity> companyTalentEscape = this._talentEscapeService.GetCompanyTalentScape(companyCredentials.PortalId, companyCredentials.IdMaster);
            bool phWithoutData = false;
            bool phWithData = false;
            bool haveVisionFeature = _companyProductService.HasFeature(ProductAmbitEnum.Vision, companyCredentials.IdCompany, companyCredentials.PortalId);
            bool isActionPermitedByRole = SecurityHelper.IsActionPemitedByRole(Master.Entities.Enums.SecurityActionEnum.AllVisionAcces);
            string monthLastCalculation = "";

            if (companyTalentEscape.Count == 0)
            {
                return new VisionTalentEscapeModel(haveVisionFeature);
            }

            monthLastCalculation = GetMonthCalculation(companyTalentEscape);
            phWithData = true;

            return new VisionTalentEscapeModel(
                    phWithoutData,
                    phWithData,
                    haveVisionFeature,
                    monthLastCalculation,
                    this.GetVisionTalentScapeFromModel(companyTalentEscape, companyCredentials.IdCompany, maxMovements, haveVisionFeature),
                    this.GetVisionTalentScapeToModel(companyTalentEscape, companyCredentials.IdCompany, maxMovements, haveVisionFeature)
                    );
        }

        private List<VisionTalentEscapeDetailModel> GetVisionTalentScapeModel(List<TalentEscapeEntity> companyTalentEscape, int companyId, int maxMovements, bool haveVisionDesbloqued, CompanyVisionTalentEscapeTypeEnum type)
        {
            List<VisionTalentEscapeDetailModel> visionTalentScapeDetailsModel = new List<VisionTalentEscapeDetailModel>();
            List<TalentEscapeEntity> companyTalentScapeTo = companyTalentEscape.FindAll(x => x.Type == type);
            int totalCountTo = companyTalentScapeTo.Sum(x => x.CandidatesCounter);
            bool isNameVisible;
            bool isBlurVisible;
            string companyName = string.Empty;
            string title;
            int counterItems = 0;
            int percentaje;

            if (companyTalentScapeTo.Count > maxMovements)
            {
                companyTalentScapeTo = companyTalentScapeTo.OrderByDescending(x => x.CandidatesCounter).Take(maxMovements).ToList();
            }

            if (haveVisionDesbloqued)
            {
                isNameVisible = true;
                isBlurVisible = false;
                title = "";
            }
            else
            {
                isNameVisible = false;
                isBlurVisible = true;
                title = GetLiteral("LIT_TITLE_CONTACT_CA");
                companyName = GetLiteral("LIT_INFO_NAME");
            }
                
            foreach (TalentEscapeEntity talentEscape in companyTalentScapeTo)
            {
                if (haveVisionDesbloqued)
                {
                    companyName = UpperCaseFirst(talentEscape.MasterMovingName);
                }

                counterItems++;
                percentaje = ((talentEscape.CandidatesCounter * 100) / totalCountTo);

                visionTalentScapeDetailsModel.Add(new VisionTalentEscapeDetailModel(
                    counterItems,
                    title,
                    companyName,
                    percentaje,
                    isNameVisible,
                    isBlurVisible
                    ));
            }

            return visionTalentScapeDetailsModel;
        }

        private List<VisionTalentEscapeDetailModel> GetVisionTalentScapeFromModel(List<TalentEscapeEntity> companyTalentEscape, int companyId, int maxMovements, bool haveVisionDesbloqued)
        {
            return this.GetVisionTalentScapeModel(companyTalentEscape, companyId, maxMovements, haveVisionDesbloqued, CompanyVisionTalentEscapeTypeEnum.From);
        }

        private List<VisionTalentEscapeDetailModel> GetVisionTalentScapeToModel(List<TalentEscapeEntity> companyTalentEscape, int companyId, int maxMovements, bool haveVisionDesbloqued)
        {
            return this.GetVisionTalentScapeModel(companyTalentEscape, companyId, maxMovements, haveVisionDesbloqued, CompanyVisionTalentEscapeTypeEnum.To);
        }

        private string GetMonthCalculation(List<TalentEscapeEntity> companyTalentScape)
        {
            var monthLastCalculation = GetMonthOfDateTime(companyTalentScape.FirstOrDefault().DateLastCalculation);
            return UpperCaseFirst(monthLastCalculation);
        }

        private string UpperCaseFirst(string input)
        {
            return System.Globalization.CultureInfo.InvariantCulture.TextInfo.ToTitleCase(input.ToLower());
        }

        private static string GetMonthOfDateTime(DateTime date)
        {
            return System.Globalization.CultureInfo.CreateSpecificCulture("es-ES").DateTimeFormat.GetMonthName(date.Month);
        }

        private string GetLiteral(string keyLiteral)
        {
            return PageLiteralsHelper.GetLiteral(keyLiteral, (short)PageEnum.CompanyTalentEscape, PortalConfigHelper.GetPortalConfiguration());
        }
    }
}