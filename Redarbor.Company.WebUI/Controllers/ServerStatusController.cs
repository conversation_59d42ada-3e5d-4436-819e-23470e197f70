using Redarbor.Company.WebUI.Models;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Core.Resolver.Contracts.ServiceLibrary;
using Redarbor.Core.Shared.Contracts.ServiceLibrary;
using Redarbor.Master.Contracts.ServiceLibrary;
using Redarbor.Web.UI.Library.Controllers;
using System;
using System.Web.Mvc;

namespace Redarbor.Company.WebUI.Controllers
{
    public class ServerStatusController : RedarborController
    {
        private readonly IClientIpAddressResolverService _clientIpAddressResolverService;
        private readonly IPortalConfigurationService _portalConfigurationService;
        private readonly ILoginCompanyService _loginCompanyService;
        private readonly IEncryptionService _encryptionService;
        public ServerStatusController(IClientIpAddressResolverService clientIpAddressResolverService, 
            IPortalConfigurationService portalConfigurationService, 
            ILoginCompanyService loginCompanyService,
            IEncryptionService encryptionService)
        {
            _clientIpAddressResolverService = clientIpAddressResolverService;
            _portalConfigurationService = portalConfigurationService;
            _loginCompanyService = loginCompanyService;
            _encryptionService = encryptionService;
        }

        [Route("Company_ui")]
        public ActionResult Index()
        {
            var idUser = _loginCompanyService.GetCredentialsFromSession()?.UserId ?? 0;

            var serverStatusDataModel = new ServerStatusDataModel() {
                IdUserEncrypted = _encryptionService.Encrypt(idUser.ToString()),
                UserAgent = _clientIpAddressResolverService.GetUserAgent() ?? string.Empty,
                ServerName = GetLastTwoCharectersServerName(),
                PortalConfig = _portalConfigurationService.GetPortalConfiguration()
            };
            return View(serverStatusDataModel);
        }

        private string GetLastTwoCharectersServerName()
        {
            var serverName = Environment.MachineName.ToString();
            if (!string.IsNullOrEmpty(serverName))
                return serverName.Substring(serverName.Length - 2, 2) ?? string.Empty;
            
            return string.Empty;
        }
    }
}