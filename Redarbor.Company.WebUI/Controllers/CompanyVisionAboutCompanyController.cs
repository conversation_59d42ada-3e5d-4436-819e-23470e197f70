using AutoMapper;
using Redarbor.Company.Contracts.ServiceLibrary;
using Redarbor.Company.WebUI.Helpers;
using Redarbor.Company.WebUI.Models.Company.Vision;
using Redarbor.Company.WebUI.Security;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Master.Entities.Company;
using Redarbor.Master.Entities.Enums;
using Redarbor.Master.Entities.Searchs;
using Redarbor.Vision.Contracts.ServiceLibrary;
using Redarbor.Web.UI.Library.Controllers;
using Redarbor.Web.UI.Library.Exceptions;
using System;
using System.Diagnostics;
using System.Web.Mvc;

namespace Redarbor.Company.WebUI.Controllers
{
    [RedarborAuthorize]
    public class CompanyVisionAboutCompanyController : RedarborController
    {
        private const int MAX_LENGTH_DESCRIPTION = 2000;
        private const int MAX_LENGTH_MISION = 500;
        private const int MAX_LENGTH_VALUES = 500;

        private readonly ICompanyService _companyService;
        private readonly IExceptionPublisherService _exceptionPublisherService;
        private readonly IStackService _stackService;
        private readonly IPortalConfigurationService _portalConfigService;

        public CompanyVisionAboutCompanyController(ICompanyService companyService,
            IExceptionPublisherService exceptionPublisherService,
            IStackService stackService,
            IPortalConfigurationService portalConfigService)
        {
            _companyService = companyService;
            _exceptionPublisherService = exceptionPublisherService;
            _stackService = stackService;
            _portalConfigService = portalConfigService;
        }

        [Route("Company/EmployerBranding/AboutCompany")]
        [Route("Company/EmproyerBranding/AboutCompany")]
        public ActionResult Index()
        {
            try
            {
                var companyCredentials = SecurityHelper.GetCompanyCredentials();
                var portalConfig = _portalConfigService.GetPortalConfiguration();
                var company = _companyService.GetByPK(new CompanySearchSpecifications(companyCredentials.PortalId) { Id = companyCredentials.IdCompany });

                if (checkBlocked(portalConfig.company_block, company.Blocked, company.LastDateBlockedUpdate, portalConfig.DaysBlock))
                    return RedirectToAction("Blocked", "Account");

                var visionAboutCompanyDataModel = Mapper.Map<CompanyAboutEntity, VisionAboutCompanyDataModel>(_companyService.GetAboutCompany(companyCredentials.IdCompany, companyCredentials.PortalId));
                visionAboutCompanyDataModel.CompanyCredentials = companyCredentials;
                visionAboutCompanyDataModel.CanUseFunction = CompanyHelper.IsVisionAccesPermited(companyCredentials) && SecurityHelper.IsActionPemitedByRole(SecurityActionEnum.CompanyDataEdit);
                FillVisionAboutCompanyDataModel(visionAboutCompanyDataModel);

                return View(visionAboutCompanyDataModel);
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyVisionAboutCompanyController Index-Get {ex}");
                _exceptionPublisherService.Publish(ex, "CompanyVisionAboutCompanyController", "Index-Get");
                return RedirectToAction("Index", "Company");
            }
        }

        [Route("Company/EmployerBranding/AboutCompany")]
        [Route("Company/EmproyerBranding/AboutCompany")]
        [HttpPost]
        [ValidateAntiForgeryToken()]
        public ActionResult Index(VisionAboutCompanyDataModel visionAboutCompanyDataModel)
        {
            try
            {
                var companyCredentials = SecurityHelper.GetCompanyCredentials();

                if (!CompanyHelper.IsVisionAccesPermited(companyCredentials))
                {
                    return RedirectToAction("Index", "Company");
                }

                visionAboutCompanyDataModel.IdCompany = companyCredentials.IdCompany;
                visionAboutCompanyDataModel.PortalId = companyCredentials.PortalId;
                FillVisionAboutCompanyDataModel(visionAboutCompanyDataModel);

                var companyDetailEntity = Mapper.Map<VisionAboutCompanyDataModel, CompanyAboutEntity>(visionAboutCompanyDataModel);
                visionAboutCompanyDataModel.IsUpdated = _companyService.UpdateAboutCompany(companyDetailEntity);
                _stackService.CompanyValuationStackInsert(companyCredentials.PortalId, companyCredentials.IdMaster, companyCredentials.IdCompany);

                return View(visionAboutCompanyDataModel);
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyVisionAboutCompanyController Index-Post {ex}");
                _exceptionPublisherService.Publish(ex, "CompanyVisionAboutCompanyController", "Index-Post");
                return RedirectToAction("Index", "Company");
            }
        }

        private void FillVisionAboutCompanyDataModel(VisionAboutCompanyDataModel visionAboutCompanyDataModel)
        {
            visionAboutCompanyDataModel.MaximumLengthDescription = MAX_LENGTH_DESCRIPTION;
            visionAboutCompanyDataModel.MaximumLengthMision = MAX_LENGTH_MISION;
            visionAboutCompanyDataModel.MaximumLengthValues = MAX_LENGTH_VALUES;
        }
    }
}