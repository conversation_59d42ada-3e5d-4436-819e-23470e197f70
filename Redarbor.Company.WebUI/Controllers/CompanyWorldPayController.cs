using Redarbor.Common.Entities.Configuration;
using Redarbor.Common.Entities.Enums;
using Redarbor.Company.Contracts.ServiceLibrary;
using Redarbor.Company.WebUI.Helpers;
using Redarbor.Company.WebUI.Models.Company.Cart;
using Redarbor.Company.WebUI.Security;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Core.Contracts.ServiceLibrary;
using Redarbor.Core.Kpi.Contracts.ServiceLibrary;
using Redarbor.Core.Kpi.Contracts.ServiceLibrary.Enums;
using Redarbor.Core.Shared.Contracts.ServiceLibrary;
using Redarbor.Core.Stack.Contracts.ServiceLibrary;
using Redarbor.Invoice.Contracts.ServiceLibrary;
using Redarbor.Master.Contracts.ServiceLibrary;
using Redarbor.Master.Entities.Company;
using Redarbor.Master.Entities.Enums;
using Redarbor.Master.Entities.Invoice;
using Redarbor.Master.Entities.Payment.Tokens;
using Redarbor.Master.Entities.Product;
using Redarbor.Master.Entities.PurchaseOperation;
using Redarbor.Master.Entities.Searchs;
using Redarbor.Master.Entities.Stack;
using Redarbor.Offer.Contracts.ServiceLibrary;
using Redarbor.PaymentCredentials.Contracts.ServiceLibrary;
using Redarbor.Products.Contracts.ServiceLibrary;
using Redarbor.ProductSubGroups.Contracts.ServiceLibrary;
using Redarbor.ProProduct.Contracts.ServiceLibrary;
using Redarbor.PurchaseOperation.Contracts.ServiceLibrary;
using Redarbor.PurchaseOperation.Impl.ServiceLibrary.CT.Payment.Utils;
using Redarbor.TokenPayments.Contracts.ServiceLibrary;
using System;
using System.Configuration;
using System.Linq;
using System.Web.Mvc;
using WorldPayExtension = Common.PaymentLibrary;

namespace Redarbor.Company.WebUI.Controllers
{
    //TODO MQC 2024-07-31 Este controlador quitar en proximos tickets, ya no existe esta cuenta de WorldPay
    //mirar si en realidad quitamos todo lo relacionado con pagos que no tenga que ver con Payments
    //ya que no se usan ya. Proximo ticket
    //He modificado el retorno de CompanyCartMessages por MultiPurchaseCartMessages para poder eliminar el controlador
    [RedarborAuthorize]
    [RoutePrefix("Payment")]
    public class CompanyWorldPayController : CompanyBaseController
    {
        private readonly IEncryptionService _encryptionService;
        private readonly IPurchaseOperationService _purchaseOperationService;
        private readonly IWorldPayTokenService _worldPayTokenService;
        private readonly IEnumToolsService _enumToolsService;
        private readonly IKpiService _kpiService;
        private readonly IStackService _stackService;
        private readonly IDtInvoiceService _dtInvoiceService;
        private readonly IPaymentCredentialsService _paymentCredentialsService;
        private readonly IProductsService _productsService;

        public CompanyWorldPayController(IProductService productService,
                 IProductSubGroupsService productSubGroupsService,
                 IOfferService offerService,
                 ICompanyProductService companyProductService,
                 ICompanyService companyService,
                 IPortalConfigurationService portalConfigurationService,
                 IDictionaryService dictionaryService,
                 ISecurityService securityService,
                 IExceptionPublisherService exceptionPublisherService,
                 IEncryptionService encryptionService,
                 IPurchaseOperationService purchaseOperationService,
                 IWorldPayTokenService worldPayTokenService,
                 IEnumToolsService enumToolsService,
                 IKpiService kpiService,
                 IStackService stackService,
                 IDtInvoiceService dtInvoiceService,
                 IPaymentCredentialsService paymentCredentialsService, IProductsService productsService) : base
                (productService,
                 productSubGroupsService,
                 offerService,
                 companyProductService,
                 companyService,
                 portalConfigurationService,
                 dictionaryService,
                 securityService,
                 exceptionPublisherService)
        {
            _encryptionService = encryptionService;
            _purchaseOperationService = purchaseOperationService;
            _worldPayTokenService = worldPayTokenService;
            _enumToolsService = enumToolsService;
            _kpiService = kpiService;
            _stackService = stackService;
            _dtInvoiceService = dtInvoiceService;
            _paymentCredentialsService = paymentCredentialsService;
            _productsService = productsService;
        }

        [Route("Card")]
        public ActionResult Index(string Ds_order, string p)
        {
            try
            {
                var model = new WorldPayCardModel();

                if (!string.IsNullOrEmpty(Ds_order) && !string.IsNullOrEmpty(p))
                {
                    int.TryParse(_encryptionService.Decrypt(Ds_order), out var idOperacionCompra);
                    int.TryParse(_encryptionService.Decrypt(p), out var idPage);

                    if (idOperacionCompra > 0)
                    {
                        var purchaseOperation = _purchaseOperationService.GetByPK(idOperacionCompra);

                        if (string.IsNullOrEmpty(purchaseOperation.Transactionid)
                            && purchaseOperation.IdStatus == (int)PurchaseOperationEnum.InProcess
                            && purchaseOperation.EndOperationDate == DateTime.MinValue)
                        {
                            var portalConfig = PortalConfigurationService.GetPortalConfiguration();
                            model.IsTest = !string.IsNullOrEmpty(ConfigurationManager.AppSettings["TEST_NEW_WORLDPAY"]) ? ConfigurationManager.AppSettings["TEST_NEW_WORLDPAY"].ToLower() == "true" : false;
                            model.IdOperacionCompraEncrypted = Ds_order;
                            model.ProductPrice = ProductService.SetCurrency(purchaseOperation.Price, portalConfig);
                            model.IdPageEncrypted = p;
                            model.HasReturn = idPage != 0;
                            model.IdproductEncrypted = _encryptionService.Encrypt(purchaseOperation.Idproduct.ToString());

                            model.ProductName = _productsService.Get(
                                                    new ProductSearchSpecifications(portalConfig.PortalId)
                                                    {
                                                        TemporalityId = (int)TemporalityEnum.Year,
                                                        Id = purchaseOperation.Idproduct
                                                    }).ComercialName;

                            FillExtraFields(model, purchaseOperation, portalConfig);
                        }
                    }
                }

                if (string.IsNullOrEmpty(model.IdOperacionCompraEncrypted))

                {
                    return RedirectToAction("Index", "MultiPurchaseCartMessages",
                                   new
                                   {
                                       hasError = 1,
                                       wp = 1
                                   });
                }


                return View(model);
            }
            catch (Exception e)
            {
                ExceptionPublisherService.Publish(e, "CompanyWorldPayController", "Index");
                return RedirectToAction("", "Company");
            }
        }

        private void FillExtraFields(WorldPayCardModel model, PurchaseOperationEntity purchaseOperation, PortalConfig portalConfig)
        {
            if (purchaseOperation.Tax > 0
                && portalConfig.EnableBreakDownPrices)
            {
                model.LitVatNumber = purchaseOperation.Tax.ToString();
                model.HasTax = true;
                model.LiteralVat = CompanyService.GetRatedPricesByPrice(purchaseOperation.PriceBase, portalConfig.countryId).VATLiteral;
                model.ProductPriceBase = ProductService.SetCurrency(purchaseOperation.PriceBase, portalConfig);
            }

            var products = ProductService.GetProductsByIdsProducts(portalConfig.PortalId, purchaseOperation.Idproduct.ToString());

            if (products.Any())
            {
                var product = products.FirstOrDefault();

                model.ProductPriceUnit = product.LiteralShowUnit;

                if (product.Saving > 0)
                {
                    model.HasSavingNumber = true;
                    model.SavingNumber = product.Saving.ToString();
                }

                var productFeatures = ProductService.GetProductFeatures(new ProductSearchSpecifications(portalConfig.PortalId) { Id = purchaseOperation.Idproduct });

                if (productFeatures.Any()
                    && productFeatures.Exists(e => e.AmbitId == (short)ProductAmbitEnum.Offer))
                {
                    var FeatureOffer = productFeatures.FirstOrDefault(f => f.AmbitId == (short)ProductAmbitEnum.Offer);
                    model.NumberOffers = FeatureOffer.InitialUnits.ToString();
                }
            }

            var credentialsWorldPay = GetWorldPayPaymentCredentials(portalConfig);

            model.ClientWorldPayKey = credentialsWorldPay.ClientKey;
            model.IsReusable = credentialsWorldPay.IsReusable;
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [Route("Card")]
        public ActionResult Index(WorldPayCardModel cardModel)
        {
            if (cardModel != null
                && !string.IsNullOrEmpty(cardModel.IdOperacionCompraEncrypted)
                && !string.IsNullOrEmpty(cardModel.TokenCardSelected))
            {
                int.TryParse(_encryptionService.Decrypt(cardModel.IdOperacionCompraEncrypted), out var idOperacionCompra);

                if (idOperacionCompra > 0)
                {
                    var purchaseOperation = _purchaseOperationService.GetByPK(idOperacionCompra);

                    if (string.IsNullOrEmpty(purchaseOperation.Transactionid)
                        && purchaseOperation.IdStatus == (int)PurchaseOperationEnum.InProcess
                        && purchaseOperation.EndOperationDate == DateTime.MinValue)
                    {
                        var portalConfig = PortalConfigurationService.GetPortalConfiguration();

                        var orderRequest = new WorldPayExtension.Models.OrderRequest()
                        {

                            Token = cardModel.TokenCardSelected,
                            DecimalAmount = purchaseOperation.Price,
                            CurrencyCode = portalConfig.currency,
                            OrderDescription = !string.IsNullOrEmpty(cardModel.ProductName)
                                                    ? cardModel.ProductName
                                                    : _productsService.Get(
                                                        new ProductSearchSpecifications(portalConfig.PortalId)
                                                        {
                                                            TemporalityId = (int)TemporalityEnum.Year,
                                                            Id = purchaseOperation.Idproduct
                                                        }).ComercialName,
                            Reusable = false,
                            CustomerOrderCode = idOperacionCompra.ToString(),
                        };

                        orderRequest.CustomerIdentifiers.IdCustomer = purchaseOperation.Idcompany;
                        orderRequest.CustomerIdentifiers.CustomerType = WorldPayExtension.Enums.CustomerType.COMPANY;

                        var credentialsWorldPay = GetWorldPayPaymentCredentials(portalConfig);
                        WorldPayExtension.Models.WorldpayConfiguration configurationWorldPay = GetWorldPayCredentialsToLibrary(credentialsWorldPay, portalConfig);

                        var paymentService = new WorldPayExtension.Services.PaymentService(WorldPayExtension.Enums.BusinessId.Sherlock, portalConfig.PortalId, configurationWorldPay);

                        try
                        {
                            var orderResponse = paymentService.SendOrder(orderRequest);

                            if (orderResponse.PaymentStatus == WorldPayExtension.Enums.OrderStatus.SUCCESS)
                            {
                                if (!_worldPayTokenService.Exist(purchaseOperation.Idcompany, purchaseOperation.Idportal, orderResponse.Token))
                                {
                                    _worldPayTokenService.Save(new WorldPayTokenEntity()
                                    {
                                        IdCompany = purchaseOperation.Idcompany,
                                        IdPortal = purchaseOperation.Idportal,
                                        Token = cardModel.TokenCardSelected
                                    });
                                }

                                ExecutePostPayOK(orderResponse, purchaseOperation, portalConfig, credentialsWorldPay.IsLive);

                                return RedirectToAction("Index", "MultiPurchaseCartMessages", new
                                {
                                    wp = 1,
                                    Ds_order = cardModel.IdOperacionCompraEncrypted,
                                    transactionId = orderResponse.OrderCode
                                });
                            }
                            else
                            {
                                ExecutePostPayKO(orderResponse, purchaseOperation, portalConfig, credentialsWorldPay.IsLive);
                                _kpiService.AddSumBlock((int)KpiEnum.ERROR_WORLDPAY_PROCESS_PAYMENT, PortalConfigurationService.GetPortalConfiguration().PortalId, 1);
                                return RedirectToAction("Index", "MultiPurchaseCartMessages", new
                                {
                                    haserror = 1,
                                    wp = 1,
                                    Ds_order = cardModel.IdOperacionCompraEncrypted,
                                    transactionId = orderResponse.OrderCode
                                });
                            }
                        }
                        catch (Exception e)
                        {
                            ExceptionPublisherService.Publish(e, "CompanyWorldPayController", "Index-Post");
                            _kpiService.AddSumBlock((int)KpiEnum.ERROR_WORLDPAY_PROCESS_PAYMENT, PortalConfigurationService.GetPortalConfiguration().PortalId, 1);
                        }
                    }
                }
            }

            return RedirectToAction("Index", "MultiPurchaseCartMessages",
                    new
                    {
                        hasError = 1,
                        wp = 1
                    });
        }

        private WorldPayExtension.Models.WorldpayConfiguration GetWorldPayCredentialsToLibrary(PaymentCredential credentialsWorldPay, PortalConfig portalConfig)
        {
            return new WorldPayExtension.Models.WorldpayConfiguration()
            {
                WolrdpayApiUrl = credentialsWorldPay.UrlApi,
                WorldpayCredentials = new WorldPayExtension.Models.WorldpayCredentials()
                {
                    ClientKey = credentialsWorldPay.ClientKey,
                    ServiceKey = credentialsWorldPay.ServiceKey,
                    MerchantId = credentialsWorldPay.IdMerchant,
                }
            };
        }

        [HttpPost]
        public void AddKPIErrorWorldPayData()
        {
            var companyCredentials = SecurityHelper.GetCompanyCredentials();

            _kpiService.AddSumBlock((int)KpiEnum.ERROR_WORLDPAY_INTRODUCE_DATA, PortalConfigurationService.GetPortalConfiguration().PortalId, 1);
        }

        private void ExecutePostPayOK(WorldPayExtension.Models.OrderResponse orderResponse, PurchaseOperationEntity purchaseOperation, PortalConfig portalConfig, bool isLive)
        {
            var product = _productsService.Get(new ProductSearchSpecifications(portalConfig.PortalId) { Id = purchaseOperation.Idproduct, TemporalityId = purchaseOperation.Idtemporality });

            if (purchaseOperation == null || purchaseOperation.IdPurchaseOperation <= 0 || purchaseOperation.IdPurchaseOperationstatus == (int)PurchaseOperationEnum.BuyOk)
            {
                return;
            }

            if (purchaseOperation.CrmActivityId > 0)
            {
                var countOpCompraByActivity = _purchaseOperationService.CountByActivity(purchaseOperation.CrmActivityId, purchaseOperation.Idcompany, purchaseOperation.IdPurchaseOperation);
                if (countOpCompraByActivity > 0)
                    return;
            }

            product.Price = (float)purchaseOperation.Price;

            ProduceProduct(purchaseOperation, portalConfig, product);
            UpdateOperacionCompra($"{CommonUtils.GetPrefixTransaction(_enumToolsService.NumToEnum<PaymentOriginEnum>(purchaseOperation.IdOrigin))}{orderResponse.OrderCode}", BuildResultMessage(orderResponse, isLive), purchaseOperation, portalConfig, (int)PurchaseOperationEnum.BuyOk);
            GenerateInvoiceAndStack(portalConfig, purchaseOperation, product);

            var promoAutomatic = ProductService.GetAutomaticPacksPromotions(GetPromotionSearchActive(purchaseOperation.Idportal, purchaseOperation.Idcompany));
            var promotionSearchActive = GetPromotionSearchActive(purchaseOperation.Idportal, purchaseOperation.Idcompany);

            if (promoAutomatic != null
                && promoAutomatic.Count > 0)
                ProductService.ExpiredAutomaticPromotions(purchaseOperation.Idportal, purchaseOperation.Idcompany);

            if (purchaseOperation.PromotionId > 0)
            {
                _kpiService.AddSumBlock((int)KpiEnum.PURCHASE_END_WITH_PROMOTION, portalConfig.PortalId, 1);
                if (promotionSearchActive.TypePromotion == (short)PromotionTypeEnum.VoucherPacks)
                    _kpiService.AddSumBlock((int)KpiEnum.TOTAL_PRICE_OPERATIONS_PROMOTION_VOUCHER, portalConfig.PortalId, (int)purchaseOperation.Price);
            }
        }


        private void ExecutePostPayKO(WorldPayExtension.Models.OrderResponse orderResponse, PurchaseOperationEntity purchaseOperation, PortalConfig portalConfig, bool isLive)
        {
            UpdateOperacionCompra(orderResponse.OrderCode, BuildResultMessage(orderResponse, isLive), purchaseOperation, portalConfig, (int)PurchaseOperationEnum.BuyKo);
        }

        protected string BuildResultMessage(WorldPayExtension.Models.OrderResponse orderResponse, bool isLive)
        {
            var TestDescription = isLive ? string.Empty : "IsTest";

            return $@"{TestDescription}callbackResult.transId:{orderResponse.OrderCode}|
                      callbackResult.transStatus:{orderResponse.PaymentStatus}|
                      callbackResult.name:{orderResponse.PaymentResponse.Name.Replace("#", string.Empty)}";
        }

        private void GenerateInvoiceAndStack(PortalConfig portalConfig, PurchaseOperationEntity purchaseOperation, ProductEntity product)
        {
            GenerateInvoice(purchaseOperation, portalConfig, product);
            _stackService.Save(new StackEntity(portalConfig.PortalId) { TypeId = (int)StackObjectTypeEnum.Company, ObjectId = purchaseOperation.Idcompany });
        }

        private PromotionSearchSpecifications GetPromotionSearchActive(short portalId, int idCompany)
        {
            return new PromotionSearchSpecifications(portalId)
            {
                StatusId = (int)PromotionStatusEnum.Active,
                CompanyId = idCompany
            };
        }

        private void GenerateInvoice(PurchaseOperationEntity purchaseOperation, PortalConfig portalConfig, ProductEntity product)
        {
            var newInvoice = new DtInvoiceEntity
            {
                IdPurchaseOperation = purchaseOperation.IdPurchaseOperation,
                IdCompany = purchaseOperation.Idcompany,
                IdInvoiceentity = purchaseOperation.Idinvoiceentity,
                TaxDate = DateTime.Now.AddDays(-1),
                NetGbp = (float)purchaseOperation.Price,
                VatGbp = purchaseOperation.Tax,
                IdStatus = (int)StatusEnum.Active,
                PacksId = purchaseOperation.Idproduct.ToString(),
                ProductsId = purchaseOperation.Idproduct.ToString(),
                IdPortal = portalConfig.PortalId,
                CreateDon = portalConfig.CurrentDateTimePortal
            };
            newInvoice.IdInvoice = _dtInvoiceService.Add(newInvoice);
            newInvoice.InvoiceUnified = new InvoiceUnifiedEntity
            {
                IdDtinvoice = newInvoice.IdInvoice,
                TypeId = (int)InvoiceTypeEnum.Navision,
                StatusId = (int)InvoiceStatusEnum.Pending,
                Price = (decimal)newInvoice.NetGbp,
                PdfPath = string.Empty,
                IdSource = (int)ProductSourceEnum.Web,
                IdPortal = (short)newInvoice.IdPortal,
                IdCompany = newInvoice.IdCompany,
                IdActivity = 0,
                Description = product.ComercialName
            };

            if (purchaseOperation.CrmActivityId == 0)
                _dtInvoiceService.SendToNavision(newInvoice, (short)SalesType.Job);

        }

        private void UpdateOperacionCompra(string transactionId, string tpvMessage, PurchaseOperationEntity purchaseOperation, PortalConfig portalConfig, int statusId)
        {
            purchaseOperation.IdPurchaseOperationstatus = statusId;
            purchaseOperation.EndOperationDate = DateTime.Now;
            purchaseOperation.TpvMessageText = tpvMessage;
            purchaseOperation.Transactionid = transactionId;


            _purchaseOperationService.Update(purchaseOperation, new CompanyEntity() { Id = purchaseOperation.Idcompany }, portalConfig.PortalId);
        }

        private void ProduceProduct(PurchaseOperationEntity purchaseOperation, PortalConfig portalConfig, ProductEntity product)
        {
            int companyProductId = CompanyProductService.AddProduct(portalConfig,
                                                                         product,
                                                                         purchaseOperation.Idcompany,
                                                                         purchaseOperation.IdOrigin,
                                                                         purchaseOperation.Iduser,
                                                                         purchaseOperation.SourceId);
            if (companyProductId > 0)
            {
                purchaseOperation.Idproductcontract = companyProductId;
            }

        }

        private PaymentCredential GetWorldPayPaymentCredentials(PortalConfig portalConfig)
        {
            var isNotTest = !string.IsNullOrEmpty(ConfigurationManager.AppSettings["TEST_NEW_WORLDPAY"]) ? ConfigurationManager.AppSettings["TEST_NEW_WORLDPAY"].ToLower() != "true" : true;
            return _paymentCredentialsService.GetPaymentCredentials(portalConfig.PortalId, (short)PaymentEnum.WorldPay, isNotTest);
        }
    }
}