using AutoMapper;
using Newtonsoft.Json;
using Redarbor.Common.Entities.Configuration;
using Redarbor.Common.Entities.Enums;
using Redarbor.Company.Contracts.ServiceLibrary;
using Redarbor.Company.WebUI.Enums;
using Redarbor.Company.WebUI.Helpers;
using Redarbor.Company.WebUI.Models.Company.Home;
using Redarbor.Company.WebUI.Models.Company.Offer;
using Redarbor.Company.WebUI.Models.Company.Product;
using Redarbor.Company.WebUI.Models.Company.Shared;
using Redarbor.Company.WebUI.Models.Company.Vision;
using Redarbor.Company.WebUI.Security;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Core.Contracts.ServiceLibrary;
using Redarbor.Core.Counter.Contracts.ServiceLibrary;
using Redarbor.Core.Kpi.Contracts.ServiceLibrary;
using Redarbor.Core.Kpi.Contracts.ServiceLibrary.Enums;
using Redarbor.Core.Mailing.Contracts.ServiceLibrary;
using Redarbor.Core.Shared.Contracts.ServiceLibrary;
using Redarbor.Dictionaries.Consumer.Enums;
using Redarbor.Extensions.Library.Extensions;
using Redarbor.Master.Contracts.ServiceLibrary;
using Redarbor.Master.Contracts.ServiceLibrary.DTO;
using Redarbor.Master.Entities.Company;
using Redarbor.Master.Entities.Counters;
using Redarbor.Master.Entities.Enums;
using Redarbor.Master.Entities.Mailing.Queue;
using Redarbor.Master.Entities.Mailing.ZendeskTicket;
using Redarbor.Master.Entities.Offers;
using Redarbor.Master.Entities.Product;
using Redarbor.Master.Entities.Searchs;
using Redarbor.Master.Entities.Vision;
using Redarbor.Message.Jobads.Chat.Impl.ServiceLibrary;
using Redarbor.Offer.Contracts.ServiceLibrary;
using Redarbor.Offer.Contracts.ServiceLibrary.DTO;
using Redarbor.Procs.Domain.Enums;
using Redarbor.Products.Contracts.ServiceLibrary;
using Redarbor.Products.Contracts.ServiceLibrary.DTO;
using Redarbor.ProductSubGroups.Contracts.ServiceLibrary;
using Redarbor.RabbitMQ.Abstractions;
using Redarbor.Surveys.Contracts.ServiceLibrary;
using Redarbor.Surveys.Contracts.ServiceLibrary.Models;
using Redarbor.User.Contracts.ServiceLibrary;
using Redarbor.User.Contracts.ServiceLibrary.DTO;
using Redarbor.Users.Library.Enums;
using Redarbor.Vision.Contracts.ServiceLibrary;
using Redarbor.Vision.Contracts.ServiceLibrary.DTO;
using Redarbor.Web.UI.Library.Exceptions;
using Redarbor.WebChannel.Contracts.ServiceLibrary;
using Redarbor.WebChannel.Entities.BannerHome;
using Redarbor.WebChannel.Entities.Enums;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.Mvc;
using CompanyImportOfferStatusEnum = Redarbor.Master.Entities.Enums.CompanyImportOfferStatusEnum;

namespace Redarbor.Company.WebUI.Controllers
{
    [RedarborAuthorize]
    public class CompanyController : CompanyBaseController
    {
        private readonly IMailingCountersService _mailingCountersService;
        private readonly ICompanyProductFeatureService _companyProductFeatureService;
        private readonly IEncryptionService _encryptionService;
        private readonly IGlobalVisionService _globalVisionService;
        private readonly IKpiService _kpiService;
        private readonly ILandingProductsService _landingProductsService;
        private readonly IMailingZendeskTicketService _mailingZendeskTicketService;
        private readonly IProducerRabbitMQ _producerRabbitMQ;
        private readonly IUserService _userService;
        private readonly IOfferKpisElasticService _offerKpisElasticService;
        private readonly IBannerHomeService _bannerHomeService;
        private readonly IPageProductsService _pageProductService;
        private readonly IOfferPreRegisterService _offerPreRegisterService;
        private readonly IMessageJobadsChatService _messageJobdsChatService;
        private readonly ISurveysService _surveyService;

        private const int NUMBER_LIMIT_EVALUATIONS = 50;
        private const int VERIFICATION_CODE_LIFETIME_MINUTES = 60;
        private const string VIEW_HOME_MEMBRESHIP = "MembershipHome";
        private const string VIEW_HOME_FREEMIUM_V2 = "FreemiumHomeV2";
        private const string VIEW_HOME_SUBSCRIPTION = "SubscriptionHome";
        private const string MESSAGES_CONTROLLER = "CompanyMessages";
        private const string CONVERSATIONS_CONTROLLER = "CompanyConversations";

        const string DATE_FORMAT = "yyyyMMdd";

        public CompanyController(IProductService productService,
            IProductSubGroupsService productSubGroupsService,
            IOfferService offerService,
            ICompanyProductService companyProductService,
            ICompanyService companyService,
            IPortalConfigurationService portalConfigurationService,
            IDictionaryService dictionaryService,
            IMailingCountersService mailingCountersService,
            ICompanyProductFeatureService companyProductFeatureService,
            ISecurityService securityService,
            IEncryptionService encryptionService,
            IGlobalVisionService globalVisionService,
            IExceptionPublisherService exceptionPublisherService,
            IKpiService kpiService,
            ILandingProductsService landingProductsService,
            IMailingZendeskTicketService mailingZendeskTicketService,
            IProducerRabbitMQ producerRabbitMQ,
            IUserService userService,
            IOfferKpisElasticService offerKpisElasticService,
            IBannerHomeService bannerHomeService,
            IPageProductsService pageProductService,
            IOfferPreRegisterService offerPreRegisterService,
            IMessageJobadsChatService messageJobdsChatService,
            ISurveysService surveyService) : base(productService, productSubGroupsService, offerService, companyProductService, companyService, portalConfigurationService, dictionaryService, securityService, exceptionPublisherService
            )
        {
            _mailingCountersService = mailingCountersService;
            _companyProductFeatureService = companyProductFeatureService;
            _encryptionService = encryptionService;
            _globalVisionService = globalVisionService;
            _kpiService = kpiService;
            _landingProductsService = landingProductsService;
            _mailingZendeskTicketService = mailingZendeskTicketService;
            _producerRabbitMQ = producerRabbitMQ;
            _userService = userService;
            _offerKpisElasticService = offerKpisElasticService;
            _bannerHomeService = bannerHomeService;
            _pageProductService = pageProductService;
            _offerPreRegisterService = offerPreRegisterService;
            _messageJobdsChatService = messageJobdsChatService;
            _surveyService = surveyService;
        }

        public async Task<ActionResult> Index(bool isUserValidated = false)
        {
            try
            {                
                var portalConfig = PortalConfigurationService.GetPortalConfiguration();
                var companyCredentials = SecurityHelper.GetCompanyCredentials();

                var company = CompanyService.GetByPK(GetCompanySearchById(portalConfig.PortalId, companyCredentials.IdCompany, isUserValidated));

                if (checkBlocked(portalConfig.company_block, company.Blocked, company.LastDateBlockedUpdate, portalConfig.DaysBlock))
                    return RedirectToAction("Blocked", "Account");

                var companyProduct = CompanyProductService.GetByIdCompany(companyCredentials.IdCompany, companyCredentials.PortalId, OfferIntegratorEnum.CompuTrabajo);

                var companyData = await GetCompanyHomeDataModel(company, companyProduct, portalConfig, companyCredentials, isUserValidated);

                var viewName = GetViewHomeName(
                    companyProduct.GroupId, 
                    portalConfig);

                return View(viewName, companyData);

            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyController Index {ex}");
                ExceptionPublisherService.Publish(ex, "CompanyController", "Index");
                return RedirectToAction("Index", "Home");
            }
        }

        public ActionResult Sherlock()
        {
            var portalConfig = PortalConfigurationService.GetPortalConfiguration();
            var companyCredentials = SecurityHelper.GetCompanyCredentials();
            var companyProduct = CompanyProductService.GetByIdCompany(companyCredentials.IdCompany, companyCredentials.PortalId, OfferIntegratorEnum.CompuTrabajo);

            if (portalConfig.AEPortalConfig.ShowBannerSherlock && (short)ProductGroupsEnum.Membership == companyProduct.GroupId)
            {
                return RedirectToAction("Index", "Company");
            }

            return View("Sherlock");
        }

        public ActionResult AuthorizationRememberActive()
        {
            var portalConfig = PortalConfigurationService.GetPortalConfiguration();
            var companyCredentials = SecurityHelper.GetCompanyCredentials();

            if (_userService.MarkMailAsVerified(new MarkMailDTO(companyCredentials.UserId, portalConfig.PortalId, UserEmailVerificationSource.VerificationByCoreStep2)))
            {
                _kpiService.Add(
                    (int)KpiEnum.MAIL_COMPANY_AUTHORIZATION_WELCOME_VERIFIED_OK,
                    portalConfig.PortalId);
            }

            return RedirectToAction("Index", "Company", new { isUserValidated = true });
        }

        [HttpPost]
        public string ContactSherlock()
        {
            return SendZendeskTicketSherlock(PortalConfigurationService.GetPortalConfiguration(),
                SecurityHelper.GetCompanyCredentials()).ToString().ToLower();
        }


        [HttpPost]
        public bool SendValidateUserMail()
        {
            var portalConfig = PortalConfigurationService.GetPortalConfiguration();
            var companyCredentials = SecurityHelper.GetCompanyCredentials();
            var company = CompanyService.GetByPK(new CompanySearchSpecifications(companyCredentials.PortalId, companyCredentials.IdCompany));

            if (portalConfig.AEPortalConfig.ShowCodeVerificationPopup
                && portalConfig.AEPortalConfig.ValidateUserByCodeDate < Convert.ToInt32(company.CreatedOn.ToString(DATE_FORMAT))
                && company.User.VerifiedMail == 0)
            {
                //code for resend email active user
                return !PublishToRabbit(portalConfig, company).Error;

            }
            return false;
        }

        [HttpPost]
        public string ExecuteAddKpi(short typeKpi)
        {
            try
            {
                var portalConfig = PortalConfigurationService.GetPortalConfiguration();
                var companyCredentials = SecurityHelper.GetCompanyCredentials();
                _kpiService.Add(typeKpi, portalConfig.PortalId);
                return "true";
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyController ExecuteAction {ex}");
                ExceptionPublisherService.Publish(ex, "CompanyController", "ExecuteAddKpi");
                return "false";
            }
        }

        public string ReloadMailingKpiLineChart(int companyId, short portalId, string typeIds)
        {
            if (typeIds == null) return JsonConvert.SerializeObject(null);

            var result = new List<LineChartViewModel>();
            foreach (var item in typeIds.Split(','))
            {
                if (!short.TryParse(item, out var parsedItem)) continue;

                var mailingKpi = Mapper.Map<List<MailingCountersEntity>, List<KpiDataModel>>(
                    _mailingCountersService.MailingCounterByCompany(companyId, portalId,
                        parsedItem));

                result.Add(new LineChartViewModel()
                {
                    Data = mailingKpi.Select(m => m.total).ToList(),
                    Labels = mailingKpi.Select(m =>
                        StringToolsHelper.GetLocalizedDate(m.date, DateFormatEnum.ShortDate)),
                    Total = StringToolsHelper.ChangeGroupPointForDecimalPoint(mailingKpi.Sum(m => m.total).ToString("N0"), portalId)
                });
            }
            return JsonConvert.SerializeObject(result);

        }

        private HomeCounterDataModel GetCompanyHomeCounters(CompanyProductEntity companyProduct, List<ControlFeatureUnitEntity> counters, short ambitFeature, string limitRedActive, bool isXN, CompanyCredentials companyCredentials)
        {
            var companyHomeCounters = new HomeCounterDataModel
            {
                Link = Url.Action("Index", "CompanyOffersPublish"),
                ShowPublishButton = SecurityHelper.IsActionPemitedByRole(SecurityActionEnum.ManageOffers)
            };

            if (companyProduct.Features.Any(feature => feature.AmbitId == ambitFeature) && counters.Exists(c => c.AmbitId == ambitFeature))
            {
                var feature = companyProduct.Features.Where(f => f.AmbitId == ambitFeature).First();

                var counter = counters.FirstOrDefault(c => c.AmbitId == ambitFeature) ?? new ControlFeatureUnitEntity();
                short.TryParse(ConfigurationManager.AppSettings[limitRedActive] != null ? ConfigurationManager.AppSettings[limitRedActive] : string.Empty, out var numberActiveRed);

                if (counter.TypeConsum == (short)TypeConsumEnum.GlobalFeature)
                    companyHomeCounters.ActiveRed = counter.Initial - counter.Consumed <= numberActiveRed;

                companyHomeCounters.TotalInitialByFeature = counter.Initial;
                companyHomeCounters.IsUnlimited = counter.IsUnLimited;
                companyHomeCounters.ConsumedOffers = counter.Consumed;
                companyHomeCounters.ConsumedTotalOffersSimultaneous = counter.TotalOffersSimultaneous;
                companyHomeCounters.IsSimultaneous = feature.IsSimultaneous;

                if (companyHomeCounters.IsUnlimited)
                {
                    companyHomeCounters.FeatureUnlimited = true;
                }

                if (companyHomeCounters.ConsumedOffers >= companyHomeCounters.TotalInitialByFeature)
                {
                    companyHomeCounters.ProgressbarColor = "color3";

                    if (SecurityService.IsActionPemitedByRole((short)companyCredentials.UserRole, companyCredentials.PortalId, SecurityActionEnum.CompanyCreditsAssign))
                    {
                        var unitsGlobalsByFeature = CompanyProductService.GetGlobalConsumUnitsByListFeatures(companyProduct.IdCompany, companyProduct.PortalId, new List<short> { counter.AmbitId }, (int)companyProduct.IdUser, OfferIntegratorEnum.CompuTrabajo, isXN);

                        if (unitsGlobalsByFeature != null
                            && unitsGlobalsByFeature.Exists(f => f.AmbitId == counter.AmbitId)
                            && unitsGlobalsByFeature.FirstOrDefault(f => f.AmbitId == counter.AmbitId)?.Available > 0)
                        {
                            companyHomeCounters.LiteralButton = "LIT_ASIGN_CREDITS";
                            companyHomeCounters.Link = Url.Action("Index", "CompanyConfigurationEditCredits");

                            companyHomeCounters.UnitsExceded = true;
                            companyHomeCounters.DivClass = "deshabilitado";
                        }
                        else
                        {
                            companyHomeCounters.LiteralButton = "BTN_PROMO";
                            companyHomeCounters.Link = Url.Action("Membership", "CompanyLanding");
                            companyHomeCounters.UnitsExceded = true;
                            companyHomeCounters.DivClass = "deshabilitado";
                        }
                    }
                    else
                    {
                        companyHomeCounters.LiteralButton = "BTN_PUBLICAR_OFERTA";
                        companyHomeCounters.UnitsExceded = true;
                        companyHomeCounters.DivClass = "deshabilitado";

                        if (counter.AmbitId == (short)ProductAmbitEnum.Offer && companyProduct.Features.Exists(cf => cf.AmbitId == (short)ProductAmbitEnum.AssignCredits))
                            companyHomeCounters.HiddenButton = true;
                    }
                }
                else
                {
                    companyHomeCounters.ProgressbarColor = "color4";
                    companyHomeCounters.LiteralButton = "BTN_PUBLICAR_OFERTA";
                }
            }
            else
            {
                companyHomeCounters.LiteralButton = "BTN_PROMO";
                companyHomeCounters.Link = Url.Action("Membership", "CompanyLanding");
                companyHomeCounters.DivClass = "deshabilitado";
            }

            companyHomeCounters.PercentageOffers = GetPercentageOffers(companyHomeCounters.ConsumedOffers, companyHomeCounters.TotalInitialByFeature);

            return companyHomeCounters;
        }


        private int GetPercentageOffers(int currentValue, int maxValue)
        {
            if (currentValue == 0 || maxValue == 0)
                return 0;

            int percentageOffers = (int)Math.Round((double)currentValue / maxValue * 100);

            return Math.Min(percentageOffers, 100);
        }

        private async Task FillOffersInformation(CompanyProductEntity companyProductEntity, HomeDataModel homeDataModel, PortalConfig portalConfig, CompanyCredentials companyCredentials)
        {
            var lastOffers = SecurityHelper.IsActionPemitedByRole(SecurityActionEnum.CompanyOffersManageByAllUsers)
                ? OfferService.Search(companyProductEntity, 0, 1, 8, "TotalNew DESC, Id DESC", 0, 0, 0, 0, new List<int>(), OfferIntegratorEnum.CompuTrabajo, portalConfig.PortalId)
                : OfferService.SearchByUser((int)companyCredentials.UserId, companyCredentials.IdCompany, 1, 8, "TotalNew DESC, Id DESC", 0, 0, 0, new List<int>(), OfferIntegratorEnum.CompuTrabajo, portalConfig.PortalId);

            homeDataModel.LastOfferList = Mapper.Map<List<OfferEntity>, List<OfferDataModel>>(lastOffers);

            if (homeDataModel.LastOfferList.Any())
            {
                homeDataModel.HasNewMatchesNotificationsFeature = companyProductEntity.GroupId != (short)ProductGroupsEnum.Membership
                && CompanyProductService.HasActiveFeatureByProduct(new SearchCompanyProductsByCompanyDTO(companyCredentials.IdCompany, ProductAmbitEnum.NewMatchesPushNotifications, portalConfig.PortalId, ProductEnum.FreemiumService)) 
                && portalConfig.NewMatchesPushNotifications 
                && companyCredentials.TestAB == TestABEnum.A;

                homeDataModel.HasNewApplies = homeDataModel.HasNewMatchesNotificationsFeature ? homeDataModel.LastOfferList.Where(p => p.TotalNew > 0).Any() : false;
                
                string prodCWConvertToComplete = string.Empty;
                var productSubGroups = ProductSubGroupsService.Get(companyCredentials.PortalId);

                if (homeDataModel.LastOfferList.Exists(x => x.IsBasic))
                {
                    prodCWConvertToComplete = _landingProductsService.ManageCWFeatures((int)ProductAmbitEnum.CVsFreemium, companyProductEntity.IdCompany, portalConfig);
                }

                homeDataModel.ProdCWConvertToComplete = prodCWConvertToComplete;
                var canManageOffer = SecurityHelper.IsActionPemitedByRoleAndIsNotAdvisorRole(SecurityActionEnum.ManageOffers);
                var canEditPermitedByRol = SecurityHelper.IsActionPemitedByRole(SecurityActionEnum.ManageOffers);

                homeDataModel.LastOfferList.ForEach(o =>
                {
                    if (o.IsBasic)
                    {
                        o.ProdCWConvertToComplete = prodCWConvertToComplete;
                        o.BtnConvertToCompleteEncryptedName = _encryptionService.Encrypt(((int)TpvButtonOriginEnum.PrivateHomeOfferListConvertToPack).ToString());
                    }
                    var offerProduct = CompanyProductService.GetByCompanyProductId(o.idcompanyproduct, portalConfig.PortalId, companyCredentials.IdCompany);
                    o.CanManageOffers = canManageOffer;
                    o.CanEdit = CompanyProductService.CanEdit(new IsAllowedToEditOfferDTO()
                    {
                        IdOfferStatus = o.idofferstatus,
                        OfferExpirationTime = o.expirationtime,
                        CompanyProductGroupId = companyProductEntity.GroupId,
                        CompanyProductOfferGroupId = offerProduct.GroupId,
                        CanEditPermitedByRol = canEditPermitedByRol,
                        BlockOldMembership = portalConfig.blockOldMembership
                    });
                    o.CompanyProductOfferDataModel.SubGroupId = offerProduct.SubGroupId;
                    o.CompanyProductOfferDataModel.Title = productSubGroups.Find(x => x.Id == offerProduct.SubGroupId)?.Name ?? PageLiteralsHelper.GetLiteral("LIT_BORRADOR", (int)PageEnum.HomePrivada, portalConfig).ToUpper();
                    o.CompanyProductOfferDataModel.GroupId = offerProduct.GroupId;
                });

                if (portalConfig.AEPortalConfig.ShowOfferViews)
                    await FillOfferViews(homeDataModel, lastOffers, companyCredentials);

            }
        }

        private LandingProductsDataModel GetLandingPopUpPacksComparative(PortalConfig portalConfig, int idCompany)
        {
            int idPage = portalConfig.AEPortalConfig.PostPublishWithConsumables ? (int)PageEnum.PopUpPacksComparativeWithConsumables : (int)PageEnum.PopUpPacksComparative;
            var landingProducts = _landingProductsService.GetLandingProducts(idPage, (short)PlaceLandingEnum.PopUpCompareProducts, portalConfig, idCompany);
            return new LandingProductsDataModel
            {
                LandingProductsContentDataModel = Mapper.Map<List<LandingProductsEntity>, List<LandingProductsContentDataModel>>(landingProducts)
            };
        }

        private bool HasNewFreemiumChat(PortalConfig portalConfig, int idCompany)
        {
            return portalConfig.AEPortalConfig.EnableProgressiveChatFreemium
                && CompanyProductService.HasActiveFeatureByProduct(new SearchCompanyProductsByCompanyDTO(idCompany, ProductAmbitEnum.ChatContactFreemium, portalConfig.PortalId, ProductEnum.FreemiumService));
        }

        private void FillMembresyHeader(CompanyEntity company, CompanyProductEntity companyProductEntity, HomeDataModel homeDataModel, PortalConfig portalConfig, CompanyCredentials companyCredentials)
        {
            FillRenewLinkInfo(company, homeDataModel);

            var unitsConsume = CompanyProductService.GetConsumeUnitsByFeatures(company.Id, portalConfig.PortalId, new List<short>()
            {
                (short) ProductAmbitEnum.Offer,
                (short) ProductAmbitEnum.OfferHighlighted,
                (short) ProductAmbitEnum.OfferUrgent
            }, companyCredentials.UserId, OfferIntegratorEnum.CompuTrabajo, true);

            homeDataModel.HomeMembresyResumDataModel = new HomeMembresyResumDataModel
            {
                ActiveOffersCounter = GetCompanyHomeCounters(companyProductEntity, unitsConsume, (short)ProductAmbitEnum.Offer, "ACTIVEOFFERS_NUMBER_ACTIVE_RED", false, companyCredentials),
                HighlightedOffersCounter = GetCompanyHomeCounters(companyProductEntity, unitsConsume, (short)ProductAmbitEnum.OfferHighlighted, "HIGHLIGHTOFFERS_NUMBER_ACTIVE_RED", false, companyCredentials),
                UrgentOffersCounter = GetCompanyHomeCounters(companyProductEntity, unitsConsume, (short)ProductAmbitEnum.OfferUrgent, "HIGHLIGHTOFFERS_NUMBER_ACTIVE_RED", false, companyCredentials)
            };

            if (companyProductEntity.Features.Exists(e => e.AmbitId == (short)ProductAmbitEnum.Offer && e.IsSimultaneous))
            {
                var unitsConsume2N = CompanyProductService.GetConsumeUnitsBy2N(company.Id, portalConfig.PortalId, OfferIntegratorEnum.CompuTrabajo, companyCredentials.UserId);

                if (unitsConsume2N.IsSimultaneous)
                    homeDataModel.HomeMembresyResumDataModel.PublishOffersCounter = GetCompanyHomeCounters(companyProductEntity, new List<ControlFeatureUnitEntity>() { unitsConsume2N },
                    (short)ProductAmbitEnum.Offer, "ACTIVEOFFERS_NUMBER_ACTIVE_RED", true, companyCredentials);
            }
        }

        private void FillRenewLinkInfo(CompanyEntity company, HomeDataModel homeDataModel)
        {
            var productDateExpiration = CompanyService.CompanyMaxProductExpirationDate(company.Id);
            if (productDateExpiration != DateTime.MinValue)
            {
                homeDataModel.DaysToExpire = productDateExpiration.Subtract(DateTime.Now).Days;
                homeDataModel.ShowRenewLink = homeDataModel.DaysToExpire >= 0 && homeDataModel.DaysToExpire < 31;
            }
        }

        private void FillProductsInformation(CompanyProductEntity companyProductEntity, HomeDataModel homeDataModel, PortalConfig portalConfig, CompanyCredentials companyCredentials)
        {
            var productsByPage = GetProductsByPage(companyProductEntity, portalConfig, companyCredentials);

            if (productsByPage.Any())
            {
                homeDataModel.Products = GetPageProductsDataModel(productsByPage, companyProductEntity, portalConfig, companyCredentials);
                homeDataModel.HasPromotionByProductByPage = homeDataModel.Products.Any() && homeDataModel.Products.Exists(e => e.HasPromotion);
            }
        }

        private List<ProductEntity> GetProductsByPage(CompanyProductEntity companyProductEntity, PortalConfig portalConfig, CompanyCredentials companyCredentials)
        {
            var pageId = PageEnum.CompanyNewProductsHome;

            if (portalConfig.AEPortalConfig.TestABPrivateHomePrices && IsCompanyTestAB(companyProductEntity.IdCompany))
            {
                pageId = PageEnum.PrivateHomeTestABPrices;
            }
            else if (portalConfig.AEPortalConfig.PostPublishWithConsumables)
            {
                pageId = PageEnum.PrivateHomeWithConsumables;
            }

            return _pageProductService.SearchByPage(GetPageProductsSearchFilter(companyCredentials.IdCompany, companyCredentials.TestAB, portalConfig, pageId));
        }

        private void SetOptionServiceBasic(HomeDataModel homeDataModel, CompanyProductEntity companyProductEntity)
        {
            if (companyProductEntity.GroupId == (short)ProductGroupsEnum.Freemium
                && companyProductEntity.Features.Exists(e => e.AmbitId == (short)ProductAmbitEnum.Offer && e.AvailableUnits > 0))
            {
                homeDataModel.HasAvailablesServiceBasic = true;
                homeDataModel.AvailablesServiceBasic = companyProductEntity.Features.FirstOrDefault(e => e.AmbitId == (short)ProductAmbitEnum.Offer).AvailableUnits;
            }
        }

        private List<ProductDataModel> GetPageProductsDataModel(List<ProductEntity> productsByPage, CompanyProductEntity companyProductMain, PortalConfig portalConfig, CompanyCredentials companyCredentials)
        {
            var result = new List<ProductDataModel>();
            var positionButton = 1;
            var productSubGroups = ProductSubGroupsService.Get(companyCredentials.PortalId);

            var packsList = CompanyProductService.GetPackAndFeaturesByCompany(companyCredentials.IdCompany, companyCredentials.PortalId);
            if (companyProductMain.GroupId == (short)ProductGroupsEnum.Freemium)
                packsList.Add(companyProductMain);

            foreach (var productPage in productsByPage)
            {
                var promotion = new PromotionEntity();
                var productToAdd = Mapper.Map<ProductEntity, ProductDataModel>(productPage);

                if (productPage.Promotions.Any())
                    promotion = productPage.Promotions.FirstOrDefault();

                if (promotion.Id > 0
                    && portalConfig.active_promotions
                    && productPage.HasPromotion)
                {
                    productToAdd.LiteralShowTotalPromotion = promotion.LiteralPriceUnity;

                    productToAdd.LiteralShowUnit = promotion.LiteralPriceUnity;
                    decimal.TryParse(promotion.LiteralPercentDiscount, out decimal percentSaving);
                    productToAdd.Saving = percentSaving;

                    decimal.TryParse(promotion.LiteralTotalSaveUpPrice, out var saveUpPrice);
                    productToAdd.LiteralTotalSaveUpPrice = ProductService.SetCurrency(saveUpPrice, portalConfig);

                    productToAdd.HasPromotionByProduct = true;
                    productToAdd.TotalPriceWithoutPromotion = promotion.LiteralOldPrice;
                    productToAdd.UnitPriceWithoutPromotion = productPage.LiteralShowUnit;
                }
                else
                    productToAdd.HasPromotion = false;

                productToAdd.ShowMembershipLanding = productPage.GroupId == (short)ProductGroupsEnum.Membership;
                productToAdd.ShowExpiredMembershipLanding = productPage.GroupId == (short)ProductGroupsEnum.Membership && companyProductMain.IsBlockedByOldMembership && portalConfig.blockOldMembership;
                productToAdd.EncryptedId = _encryptionService.Encrypt(productPage.Id.ToString());
                productToAdd.ShowTemporality = productPage.GroupId == (short)ProductGroupsEnum.Membership;

                foreach (var productWithFeatures in packsList.Where(x => x.SubGroupId == productPage.SubGroupId))
                {
                    var featuresAmbitOffer = productWithFeatures.Features?.FirstOrDefault(x => x.AmbitId == (int)ProductAmbitEnum.Offer && x.AvailableUnits > 0);

                    if (featuresAmbitOffer != null && featuresAmbitOffer.AvailableUnits > 0)
                    {
                        productToAdd.AvailabeUnits += featuresAmbitOffer.AvailableUnits;
                        productToAdd.TotalUnits += featuresAmbitOffer.InitialUnits;
                        productToAdd.UsedUnits += featuresAmbitOffer.ConsumedUnits;

                        var percentUnits = (int)Math.Round((double)(100 * productToAdd.UsedUnits) / productToAdd.TotalUnits);
                        productToAdd.PercentUnits = Math.Min(percentUnits, 100);
                    }
                }

                productToAdd.Recommended = productPage.Recommended;
                productToAdd.ShowMoreLiteral = PageLiteralsHelper.GetLiteral("SHOW_MORE_OFFERS", (int)PageEnum.HomePrivada, portalConfig);
                productToAdd.CompanyProductOfferDataModel = new CompanyProductOfferDataModel()
                {
                    SubGroupId = productPage.SubGroupId,
                    Title = (productSubGroups.Any() ? productSubGroups.Exists(p => p.Id == productPage.SubGroupId) ? productSubGroups.FirstOrDefault(p => p.Id == productPage.SubGroupId).Name : productToAdd.ComercialName : string.Empty),
                    GroupId = (short)productPage.GroupId
                };

                var productFeaturesDescriptions = ProductService.GetSubGroupFeaturesDescriptions(productPage.SubGroupId, portalConfig.PortalId).Where(x => x.LandingId == (short)PlaceLandingEnum.CommonLanding).ToList();
                productToAdd.FeaturesDescriptions = Mapper.Map<List<ProductFeatureDescriptionEntity>, List<ProductFeatureDescriptionDataModel>>(productFeaturesDescriptions);

                productToAdd.LandingProductSubtitle = GetLandingProductSubtitle(productPage.SubGroupId, portalConfig);
                productToAdd.BtnOriginId = GetButtonOriginEnumByPosition(positionButton);

                productToAdd.ProductIsPrime = portalConfig.AEPortalConfig.ActivatePrimeProduct && companyCredentials.TestAB == TestABEnum.A && productPage.GroupId == (short)ProductGroupsEnum.Consumibles && productPage.SubGroupId == (short)ProductSubGroupsEnum.Prime;

                if (portalConfig.AEPortalConfig.ActivateCompanyCvsConsumible &&
                    productToAdd.GroupId == (int)ProductGroupEnum.Consumibles &&
                    productToAdd.SubGroupId == (short)ProductSubGroupsEnum.BBDDcv)
                {
                    productToAdd = FillBBDDcvProductCounters(productToAdd, companyCredentials.IdCompany, portalConfig);
                }

                positionButton += 1;

                result.Add(productToAdd);
            }

            return result;
        }

        private string GetLandingProductSubtitle(short subGroupId, PortalConfig portalConfig)
        {
            return subGroupId switch
            {
                (short)ProductSubGroupsEnum.Standard => PageLiteralsHelper.GetLiteral("GET_CANDIDATES_WITH_STANDARD", (int)PageEnum.HomePrivada, portalConfig),
                (short)ProductSubGroupsEnum.Advanced => PageLiteralsHelper.GetLiteral("GET_CANDIDATES_WITH_ADVANCED", (int)PageEnum.HomePrivada, portalConfig),
                (short)ProductSubGroupsEnum.Express => PageLiteralsHelper.GetLiteral("GET_CANDIDATES_WITH_EXPRESS", (int)PageEnum.HomePrivada, portalConfig),
                (short)ProductSubGroupsEnum.Premium => PageLiteralsHelper.GetLiteral("LIT_MORE_CANDIDATES", (int)PageEnum.HomePrivada, portalConfig),
                _ => string.Empty
            };
        }

        private int GetButtonOriginEnumByPosition(int positionButton)
        {
            return positionButton switch
            {
                1 => (int)TpvButtonOriginEnum.PrivateHome_1,
                2 => (int)TpvButtonOriginEnum.PrivateHome_2,
                3 => (int)TpvButtonOriginEnum.PrivateHome_3,
                4 => (int)TpvButtonOriginEnum.PrivateHome_4,
                _ => 0,
            };
        }

        private PopUpDataModel GetFunctionOnlyWithFeatureDataModel(PortalConfig portalConfig)
        {
            return new PopUpDataModel()
            {
                Title = PageLiteralsHelper.GetLiteral("LIT_ONLY_WITH_FEATURE_TITLE", (int)PageEnum.HomePrivada, portalConfig),
                Message = PageLiteralsHelper.GetLiteral("LIT_ONLY_WITH_FEATURE_MESSAGE", (int)PageEnum.HomePrivada, portalConfig),
                TitleBtnOk = PageLiteralsHelper.GetLiteral("LIT_BTN_CONTRATAR", (int)PageEnum.HomePrivada, portalConfig),
                TitleBtnKo = PageLiteralsHelper.GetLiteral("LIT_CANCELAR", (int)PageEnum.HomePrivada, portalConfig),
                LiteralProcessing = string.Empty,
                LiteralError = string.Empty,
                HasInput = false,
                HasButtonOk = true
            };
        }

        private async Task<HomeDataModel> GetCompanyHomeDataModel(CompanyEntity company, CompanyProductEntity companyProduct, PortalConfig portalConfig, CompanyCredentials companyCredentials, bool isUserValidated = false)
        {
            var homeDataModel = new HomeDataModel
            {
                CompanyProduct = companyProduct,
                CanManageOffers = SecurityHelper.IsActionPemitedByRoleAndIsNotAdvisorRole(SecurityActionEnum.ManageOffers),
                RoleName = DictionaryService.GetDictionaryValue(DictionaryEnum.USER_ROLES_CT, ((short)companyCredentials.UserRole).ToString(), portalConfig.PortalId),
                UserName = companyCredentials.Name
            };

            var hasNewFreemiumChat = HasNewFreemiumChat(portalConfig, companyCredentials.IdCompany);

            if (companyProduct.GroupId == (short)ProductGroupsEnum.Membership)
            {
                homeDataModel.HaveMembresyActive = true;
                FillMembresyHeader(company, companyProduct, homeDataModel, portalConfig, companyCredentials);

                if (portalConfig.HaveVision
                    && companyCredentials.IdMaster > 0
                    && CompanyProductService.HasFeature(ProductAmbitEnum.Vision, companyProduct))
                {
                    homeDataModel.HasVision = true;
                    homeDataModel.VisionSummaryDataModel = GetVisionSummaryDataModel(companyProduct, portalConfig, companyCredentials);
                    GetValuesVision(homeDataModel, company.Id, portalConfig);
                }

                homeDataModel.HasQrCode = IsQrProductFeatureActive(companyCredentials);
                homeDataModel.ProductComercialName = ProductService.GetProductDescriptionByProductId(companyCredentials.PortalId, companyProduct.ProductId);
            }
            else
            {
                homeDataModel.OffersIsUnlimited = _companyProductFeatureService.IsUnlimited(companyProduct, (int)ProductAmbitEnum.Offer);
                homeDataModel.BtnConvertToCompleteEncryptedName = _encryptionService.Encrypt(((int)TpvButtonOriginEnum.PrivateHomeOfferListConvertToPack).ToString());

                var haveMembresyExpired = companyProduct.IsBlockedByOldMembership && portalConfig.blockOldMembership
                || checkBlocked(portalConfig.company_block, company.Blocked, company.LastDateBlockedUpdate, portalConfig.DaysBlock);
                homeDataModel.HaveMembresyExpired = haveMembresyExpired;

                SetOptionServiceBasic(homeDataModel, companyProduct);
                var allCompanyActiveProducts = CompanyProductService.GetAllCompanyActiveProducts(companyCredentials.IdCompany, companyCredentials.PortalId);

                if (portalConfig.AEPortalConfig.DisableFreemiumAfterPurchaseActivity && IsCompanyTestAB(companyCredentials.IdCompany))
                {
                    homeDataModel.FreeOffersAvailable = CompanyProductService.CompanyHasMoreThanOneContractedProduct(companyCredentials.IdCompany, (short)companyCredentials.PortalId) ? 0 :
                                                        allCompanyActiveProducts.Where(x => x.GroupId == (short)ProductGroupsEnum.Freemium)?.Sum(y => y.Features.Find(z => z.AmbitId == (int)ProductAmbitEnum.Offer)?.AvailableUnits) ?? 0;
                }
                else
                {
                    homeDataModel.FreeOffersAvailable = allCompanyActiveProducts.Where(x => x.GroupId == (short)ProductGroupsEnum.Freemium)?.Sum(y => y.Features.Find(z => z.AmbitId == (int)ProductAmbitEnum.Offer)?.AvailableUnits) ?? 0;
                }

                homeDataModel.CompleteOffersAvailable = allCompanyActiveProducts.Where(x => x.GroupId == (short)ProductGroupsEnum.Packs)?.Sum(y => y.Features.Find(z => z.AmbitId == (int)ProductAmbitEnum.Offer)?.AvailableUnits) ?? 0;
                homeDataModel.CompanyAvailableUnitsPurchasedDataModel = FillCompanyAvailableUnitsPurchasedDataModel(allCompanyActiveProducts);
                homeDataModel.IsTestABPrivateHomePricesActive = portalConfig.AEPortalConfig.TestABPrivateHomePrices && IsCompanyTestAB(companyCredentials.IdCompany);

                FillProductsInformation(companyProduct, homeDataModel, portalConfig, companyCredentials);

                homeDataModel.LandingProductsPopUpDataModel = GetLandingPopUpPacksComparative(portalConfig, company.Id);
                homeDataModel.CanBuyPacks = CanBuyPacks(haveMembresyExpired, portalConfig, companyCredentials);
                homeDataModel.MinimPercentPromoVoucher = portalConfig.active_promotions && portalConfig.AEPortalConfig.DaysPromotion > 0 ? ProductService.GetMinimPercentPromoVoucher(companyProduct.IdCompany, portalConfig.PortalId) : 0;
                homeDataModel.HaveVoucherPromotion = homeDataModel.MinimPercentPromoVoucher > 0;

                homeDataModel.TextAvailableOffers = GetBannerTextAvailableOffers(homeDataModel, portalConfig);
                homeDataModel.HasOnePlusOnePack = HasOnePlusOnePack(portalConfig, companyCredentials.IdCompany);
                homeDataModel.HasPromoOffers = HasPromoOffers(allCompanyActiveProducts, portalConfig);

                if (portalConfig.AEPortalConfig.ActivatePrimeProduct && companyCredentials.TestAB == TestABEnum.A)
                {
                    homeDataModel.HasPrime = allCompanyActiveProducts.Exists(x => x.GroupId == (short)ProductGroupsEnum.Consumibles && x.SubGroupId == (short)ProductSubGroupsEnum.Prime);
                    homeDataModel.PrimeDateActivation = homeDataModel.HasPrime ? PrimeDateActivation(allCompanyActiveProducts) : string.Empty;
                }

                homeDataModel.ComercialName = CompanyService.GetCompanyComercialName(companyCredentials.IdCompany, companyCredentials.PortalId);

                //TODO MQC Add a specific class
                //Ini Data dtCompany StatusImportOffer status depend on OfferPreRegister
                homeDataModel.ShowBannerImportOffer = ShowBannerImportOfferAndRefreshStatus(companyCredentials.IdCompany, companyCredentials.PortalId);
                homeDataModel.UrlImportOffer = homeDataModel.ShowBannerImportOffer ? Url.Action("ImportOffers", "PreRegister") : string.Empty;
                homeDataModel.ShowActionCheckImportOffer = ShowActionCheckImportOffer(companyCredentials.IdCompany, companyCredentials.PortalId);
                homeDataModel.UrlAjaxPostCheckImportOffer = homeDataModel.ShowActionCheckImportOffer ? Url.Action("CheckActionImportingOffer", "Company") : string.Empty;
                //Ini Data dtCompany StatusImportOffer status depend on OfferPreRegister

                if (portalConfig.has_chat == 1 && HasChatActivate(companyCredentials.IdCompany, portalConfig))
                {
                    var conversationFilter = new ConversationFilter
                    {
                        IdCompany = companyCredentials.IdCompany,
                        IdPortal = portalConfig.PortalId,
                        IdUserCompany = companyCredentials.UserId,
                        UserType = (short)UserTypeEnum.Company
                    };

                    if (await _messageJobdsChatService.GetTotalCountConversationsAsync(conversationFilter, portalConfig.idapp) > 0)
                    {
                        homeDataModel.ShowMessagesDiv = true;
                        homeDataModel.TotalPendingMessagescompany = await _messageJobdsChatService.GetCountPendingMessagesByCompanyWithCacheAsync(conversationFilter, portalConfig.idapp);
                    }

                    homeDataModel.UrlIndexConversations = GetUrlIndexConversations(hasNewFreemiumChat, new CompanyProductEntity());
                }
                
                var productSubGroups = ProductSubGroupsService.Get(companyCredentials.PortalId);

                string title = productSubGroups.Find(x => x.Id == (int)ProductSubGroupsEnum.Standard)?.Name ?? "";
                homeDataModel.ProdSubGroup3AvailableUnits = allCompanyActiveProducts
                    .Where(x => x.SubGroupId == (int)ProductSubGroupsEnum.Standard)
                    .Sum(x => x.Features.Find(f => f.AmbitId == (int)ProductAmbitEnum.Offer)?.AvailableUnits ?? 0);                    
                homeDataModel.ProdSubGroup3Title = $"{char.ToUpper(title[0])}{title.Substring(1).ToLower()}";

                title = productSubGroups.Find(x => x.Id == (int)ProductSubGroupsEnum.Advanced)?.Name ?? "";
                homeDataModel.ProdSubGroup4AvailableUnits = allCompanyActiveProducts
                    .Where(x => x.SubGroupId == (int)ProductSubGroupsEnum.Advanced)
                    .Sum(x => x.Features.Find(f => f.AmbitId == (int)ProductAmbitEnum.Offer)?.AvailableUnits ?? 0);
                homeDataModel.ProdSubGroup4Title = $"{char.ToUpper(title[0])}{title.Substring(1).ToLower()}";

                title = productSubGroups.Find(x => x.Id == (int)ProductSubGroupsEnum.Premium)?.Name ?? "";
                homeDataModel.ProdSubGroup5AvailableUnits = allCompanyActiveProducts
                    .Where(x => x.SubGroupId == (int)ProductSubGroupsEnum.Premium)
                    .Sum(x => x.Features.Find(f => f.AmbitId == (int)ProductAmbitEnum.Offer)?.AvailableUnits ?? 0);
                homeDataModel.ProdSubGroup5Title = $"{char.ToUpper(title[0])}{title.Substring(1).ToLower()}";

                title = productSubGroups.Find(x => x.Id == (int)ProductSubGroupsEnum.Express)?.Name ?? "";
                homeDataModel.ProdSubGroup9AvailableUnits = allCompanyActiveProducts
                    .Where(x => x.SubGroupId == (int)ProductSubGroupsEnum.Express)
                    .Sum(x => x.Features.Find(f => f.AmbitId == (int)ProductAmbitEnum.Offer)?.AvailableUnits ?? 0);
                homeDataModel.ProdSubGroup9Title = $"{char.ToUpper(title[0])}{title.Substring(1).ToLower()}";                
            }

            homeDataModel.ActionNotAllowedByMembership = GetActionNotAllowedPopUpByMembership(portalConfig);
            homeDataModel.AlertDataModel = GetAlertInformation(company, companyProduct, portalConfig);
            homeDataModel.FunctionOnlyWithFeaturePopUp = GetFunctionOnlyWithFeatureDataModel(portalConfig);
            homeDataModel.ShowTotalNotViewed = ConfigurationManager.AppSettings["ShowTotalNotViewed"] != null && ConfigurationManager.AppSettings["ShowTotalNotViewed"] == "true";
            homeDataModel.ViewMatchesByNumberBasicOffer = portalConfig.AEPortalConfig.ViewMatchesByNumberBasicOffer;
            homeDataModel.ShowRememberAuthorizationMail = portalConfig.AEPortalConfig.ShowCodeVerificationPopup &&
                portalConfig.AEPortalConfig.ValidateUserByCodeDate < Convert.ToInt32(company.CreatedOn.ToString(DATE_FORMAT)) &&
                company.User.VerifiedMail == 0;
            homeDataModel.CanSendRememberAuthorizationMail = DateTime.UtcNow.AddMinutes(-portalConfig.AEPortalConfig.MinimumTimeShowRememberActiveUser) > Session["LAST_REMEMBER_SEND"].ToDateTime();
            homeDataModel.IsUserValidated = isUserValidated;
            homeDataModel.HasCompanyDescription = !string.IsNullOrEmpty(company.Description);
            homeDataModel.IsEditConfigPemitedByRoleAndIsNotAdvisorRole = SecurityHelper.IsActionPemitedByRoleAndIsNotAdvisorRole(SecurityActionEnum.CompanyDataEdit);
            homeDataModel.JustLoggedIn = ((bool?)Session[JustLoggedInSessionKey]) ?? false;
            homeDataModel.HasNewFreemiumChat = hasNewFreemiumChat;
            homeDataModel.UrlSurveyHome = String.IsNullOrEmpty(portalConfig.AEPortalConfig.UrlSurveyHome) ? string.Empty : portalConfig.AEPortalConfig.UrlSurveyHome.Replace("email", companyCredentials.Email);
            homeDataModel.ShowDescriptionPopup = !homeDataModel.HasCompanyDescription && homeDataModel.IsEditConfigPemitedByRoleAndIsNotAdvisorRole && homeDataModel.JustLoggedIn;
            Session[JustLoggedInSessionKey] = false;

            await FillOffersInformation(companyProduct, homeDataModel, portalConfig, companyCredentials);
            homeDataModel.SerializedListBanners = portalConfig.AEPortalConfig.ShowBannerPromoHomePaymentInstallments ? JsonConvert.SerializeObject(GetBannersHome(portalConfig, homeDataModel.CompanyProduct.GroupId)) : string.Empty;

            homeDataModel.UserMisuseMessage = FillUserMisuseMessage(companyCredentials.UserId, companyCredentials.IdCompany, companyCredentials.UserRole, portalConfig);
            homeDataModel.ErrorAlertsDataModel = GetLiteralsPopUpErrorAlerts(company, portalConfig);            

            return homeDataModel;
        }

        private string FillUserMisuseMessage(long userId, int idCompany, UserRoleEnum userRole, PortalConfig portalConfig)
        {
            var userMisuseList = _userService.GetUserMisuseList(idCompany, portalConfig.PortalId);

            if (userMisuseList.Any())
            {
                string legalNoticeLink = $"<a href=\"{portalConfig.url_web}/avisolegal/\" target=\"_blank\" class=\"tdY\">{PageLiteralsHelper.GetLiteral("LIT_CONSULT_LEGAL_BASES", (int)PageEnum.HomePrivada, portalConfig)}</a>";
                string contactLink = $"<a href=\"{@Url.Action("Index", "Contact", new { idc = EncryptationHelper.Encrypt(idCompany.ToString()) })}\" target=\"_blank\" class=\"tdY\">{PageLiteralsHelper.GetLiteral("LIT_CONTACT_SUPPORT", (int)PageEnum.HomePrivada, portalConfig)}</a>";

                if (userMisuseList.Contains(userId))                
                {
                    return $"{PageLiteralsHelper.GetLiteral("LIT_MISUSE_MSG_USER", (int)PageEnum.HomePrivada, portalConfig)}{legalNoticeLink} o {contactLink}.";
                }
                else if (userRole == UserRoleEnum.ADMINISTRADOR || userRole == UserRoleEnum.ADMINISTRADOR_PRINCIPAL)
                {
                    return $"{PageLiteralsHelper.GetLiteral("LIT_MISUSE_MSG_OTHER", (int)PageEnum.HomePrivada, portalConfig)}{legalNoticeLink} o {contactLink}.";
                }
            }

            return string.Empty;
        }

        [HttpPost]
        [RedarborAuthorize]
        public async Task<JsonResult> CheckActionImportingOffer()
        {
            try
            {
                var companyCredentials = SecurityHelper.GetCompanyCredentials();

                var company = await CompanyService.GetByPKAsync(new CompanySearchSpecifications(companyCredentials.PortalId, companyCredentials.IdCompany));

                if (company.StatusImportOffer == (short)CompanyImportOfferStatusEnum.StartMigration)
                {
                    return Json(new { stopConsulting = false }, JsonRequestBehavior.AllowGet);
                }

                if (company.StatusImportOffer == (short)CompanyImportOfferStatusEnum.Migrated)
                {
                    //TODO MQC Cuando tengamos un proceso podemos quitar esta llamada temporal que es como un worker en la vista
                    var emailSended = await _offerPreRegisterService.SendEmailImportOffersCompleted(company.Id, company.Nit, company.PortalId);
                    if (emailSended) UpdateStatusImportOffer(companyCredentials.IdCompany, companyCredentials.PortalId, CompanyImportOfferStatusEnum.Finished);
                }

                return Json(new { stopConsulting = true }, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                ExceptionPublisherService.Publish(ex, "CompanyController", "CheckActionImportingOffer");
                return Json(new { stopConsulting = true }, JsonRequestBehavior.AllowGet);
            }
        }

        private List<BannerHomeDataModel> GetBannersHome(PortalConfig portalConfig, short groupId)
        {
            var typeHomeAE = (groupId == (short)ProductGroupsEnum.Membership) ? TypeHomeAEEnum.Membership : TypeHomeAEEnum.PackAndBasic;
            return Mapper.Map<List<BannerHomeEntity>, List<BannerHomeDataModel>>(_bannerHomeService.GetBannersHome(portalConfig.PortalId, typeHomeAE));
        }

        private string PrimeDateActivation(List<CompanyProductEntity> allCompanyActiveProducts)
        {
            var primeProduct = allCompanyActiveProducts.FirstOrDefault(x => x.GroupId == (short)ProductGroupEnum.Consumibles && x.SubGroupId == (short)ProductSubGroupsEnum.Prime && x.StatusId == (short)StatusEnum.Active);

            return primeProduct == null
                ? string.Empty
                : StringToolsHelper.GetLocalizedDate(primeProduct.DateActivation, DateFormatEnum.DayMonthYear);
        }

        private async Task FillOfferViews(HomeDataModel homeDataModel, List<OfferEntity> offerEntities, CompanyCredentials companyCredentials)
        {
            var offersKpisQuery = new OfferKpisElasticSearchQueryDTO
            {
                OffersId = offerEntities.Select(x => x.idoffer).ToList(),
            };

            var offersKpis = await _offerKpisElasticService.GetTotalViewsOfferKpi(offersKpisQuery, companyCredentials);

            var date = DateTime.Now.Date;
            foreach (var offer in homeDataModel.LastOfferList)
            {
                var offerKpi = offersKpis?.FirstOrDefault(x => x.IdOffer == offer.idoffer);
                int views = offerKpi?.Views > 0 ? offerKpi.Views : offer.total_applied > 0 ? offer.total_applied : 0;

                offer.Views = views == 0 || offer.createdon.Date == date ? "-1" : StringToolsHelper.ChangeGroupPointForDecimalPoint(views.ToString("N0"), companyCredentials.PortalId);
                offer.HasOfferViews = true;
            }
        }


        private bool IsQrProductFeatureActive(CompanyCredentials companyCredentials)
        {
            var companyProduct = CompanyProductService.GetByIdCompany(companyCredentials.IdCompany, companyCredentials.PortalId, OfferIntegratorEnum.CompuTrabajo);
            return CompanyProductService.HasFeature(ProductAmbitEnum.QR, companyProduct);
        }

        private bool HasPromoOffers(List<CompanyProductEntity> allCompanyActiveProducts, PortalConfig portalConfig)
        {
            var havePromoFeature = allCompanyActiveProducts.Find(x => x.GroupId == (short)ProductGroupsEnum.Freemium && x.SubGroupId == (short)ProductSubGroupsEnum.Freemium)?.Features.Find(y => y.AmbitId == (int)ProductAmbitEnum.PromoOffer);
            return portalConfig.AEPortalConfig.PromoOffers && havePromoFeature != null;
        }

        private bool HasOnePlusOnePack(PortalConfig portalConfig, int idCompany)
        {
            return portalConfig.AEPortalConfig.HasOnePlusOnePack && !CompanyProductService.CompanyHasMoreThanOneContractedProduct(idCompany, portalConfig.PortalId);
        }

        private string GetBannerTextAvailableOffers(HomeDataModel homeDataModel, PortalConfig portalConfig)
        {
            var returnText = new StringBuilder();
            if ((portalConfig.AEPortalConfig.PostPublishWithConsumables) && homeDataModel.CompleteOffersAvailable > 0)
            {
                returnText.Append("<span class=\"fwB\">");
                returnText.Append(homeDataModel.CompleteOffersAvailable + " ");
                returnText.Append(homeDataModel.CompleteOffersAvailable == 1 ? PageLiteralsHelper.GetLiteral("LIT_OFFER", (int)PageEnum.HomePrivada, portalConfig) : PageLiteralsHelper.GetLiteral("LIT_OFFERS", (int)PageEnum.HomePrivada, portalConfig));
                returnText.Append(" ");
                returnText.Append(PageLiteralsHelper.GetLiteral("LIT_MULTIPACKS", (int)PageEnum.HomePrivada, portalConfig));
                returnText.Append("</span>");
                returnText.Append(homeDataModel.CompleteOffersAvailable == 1 ? PageLiteralsHelper.GetLiteral("LIT_AVAILABLE", (int)PageEnum.HomePrivada, portalConfig) : PageLiteralsHelper.GetLiteral("LIT_AVAILABLES", (int)PageEnum.HomePrivada, portalConfig));
            }
            else if (homeDataModel.CompleteOffersAvailable == 0 && (homeDataModel.FreeOffersAvailable > 0 || portalConfig.AEPortalConfig.PostPublishWithConsumables))
            {
                if (homeDataModel.OffersIsUnlimited)
                {
                    returnText.Append($" {PageLiteralsHelper.GetLiteral("LIT_BASIC_OFFERS", (int)PageEnum.HomePrivada, portalConfig)}");
                    returnText.Append("<span class=\"fwB\">");
                    returnText.Append($" {PageLiteralsHelper.GetLiteral("LIT_FREE_UNLIMITED", (int)PageEnum.HomePrivada, portalConfig)}");
                    returnText.Append("</span> ");
                }
                else
                {
                    if (portalConfig.AEPortalConfig.HideBasicOffersNum)
                    {
                        returnText.Append("<span class=\"fwB\">");
                        returnText.Append(PageLiteralsHelper.GetLiteral("LIT_OFFER_FREE_CAPITAL", (int)PageEnum.HomePrivada, portalConfig));
                        returnText.Append("</span> ");
                        returnText.Append(PageLiteralsHelper.GetLiteral("LIT_AVAILABLES", (int)PageEnum.HomePrivada, portalConfig));
                    }
                    else
                    {
                        returnText.Append("<span class=\"fwB\">");
                        if (homeDataModel.FreeOffersAvailable < 1000)
                        {
                            returnText.Append(homeDataModel.FreeOffersAvailable + " ");
                        }
                        returnText.Append(homeDataModel.FreeOffersAvailable == 1 ? PageLiteralsHelper.GetLiteral("LIT_OFFER_FREE", (int)PageEnum.HomePrivada, portalConfig) : PageLiteralsHelper.GetLiteral("LIT_OFFERS_FREE", (int)PageEnum.HomePrivada, portalConfig));
                        returnText.Append("</span> ");
                        returnText.Append(homeDataModel.FreeOffersAvailable == 1 ? PageLiteralsHelper.GetLiteral("LIT_AVAILABLE", (int)PageEnum.HomePrivada, portalConfig) : PageLiteralsHelper.GetLiteral("LIT_AVAILABLES", (int)PageEnum.HomePrivada, portalConfig));
                    }
                }
            }

            return returnText.ToString();
        }

        private CompanyAvailableUnitsPurchasedDataModel FillCompanyAvailableUnitsPurchasedDataModel(List<CompanyProductEntity> allCompanyActiveProducts)
        {
            var companyAvailableUnitsPurchasedDataModel = new CompanyAvailableUnitsPurchasedDataModel();
            var consumibles = allCompanyActiveProducts.Where(x => x.GroupId == (short)ProductGroupsEnum.Consumibles);
            var fremium = allCompanyActiveProducts.Where(x => x.GroupId == (short)ProductGroupsEnum.Freemium);

            companyAvailableUnitsPurchasedDataModel.Urgent = consumibles.Sum(y => y.Features.Find(z => z.AmbitId == (int)ProductAmbitEnum.OfferUrgent)?.AvailableUnits) ?? 0;
            companyAvailableUnitsPurchasedDataModel.Highlighted = consumibles.Sum(y => y.Features.Find(z => z.AmbitId == (int)ProductAmbitEnum.OfferHighlighted)?.AvailableUnits) ?? 0;
            companyAvailableUnitsPurchasedDataModel.KillerQuestions = consumibles.Sum(y => y.Features.Find(z => z.AmbitId == (int)ProductAmbitEnum.KillerQuestions)?.AvailableUnits) ?? 0;
            companyAvailableUnitsPurchasedDataModel.HiddenCompany = consumibles.Sum(y => y.Features.Find(z => z.AmbitId == (int)ProductAmbitEnum.OfferHiddenName)?.AvailableUnits) ?? 0;
            companyAvailableUnitsPurchasedDataModel.BasicOffers = fremium?.Sum(y => y.Features.Find(z => z.AmbitId == (int)ProductAmbitEnum.Offer)?.AvailableUnits) ?? 0;
            companyAvailableUnitsPurchasedDataModel.CompleteOffers = allCompanyActiveProducts.Where(x => x.GroupId == (short)ProductGroupsEnum.Packs)?.Sum(y => y.Features.Find(z => z.AmbitId == (int)ProductAmbitEnum.Offer)?.AvailableUnits) ?? 0;
            companyAvailableUnitsPurchasedDataModel.HasUnlimitedFreeBasicOffers = fremium.Any(x => x.Features.Any(x => x.IsUnlimited));
            companyAvailableUnitsPurchasedDataModel.SubgroupStandard = allCompanyActiveProducts.Where(x => x.SubGroupId == (short)ProductSubGroupsEnum.Standard)?.Sum(y => y.Features.Find(z => z.AmbitId == (int)ProductAmbitEnum.Offer)?.AvailableUnits) ?? 0;
            companyAvailableUnitsPurchasedDataModel.SubgroupAdvanced = allCompanyActiveProducts.Where(x => x.SubGroupId == (short)ProductSubGroupsEnum.Advanced)?.Sum(y => y.Features.Find(z => z.AmbitId == (int)ProductAmbitEnum.Offer)?.AvailableUnits) ?? 0;
            companyAvailableUnitsPurchasedDataModel.SubgroupPremium = allCompanyActiveProducts.Where(x => x.SubGroupId == (short)ProductSubGroupsEnum.Premium)?.Sum(y => y.Features.Find(z => z.AmbitId == (int)ProductAmbitEnum.Offer)?.AvailableUnits) ?? 0;
            companyAvailableUnitsPurchasedDataModel.SubgroupCvBBDD = consumibles.Sum(y => y.Features.Find(z => z.AmbitId == (int)ProductAmbitEnum.CvBBDD)?.AvailableUnits) ?? 0;

            if (companyAvailableUnitsPurchasedDataModel.SubgroupStandard > 0 ||
                companyAvailableUnitsPurchasedDataModel.SubgroupAdvanced > 0 ||
                companyAvailableUnitsPurchasedDataModel.SubgroupPremium > 0 ||
                companyAvailableUnitsPurchasedDataModel.SubgroupCvBBDD > 0)
            {
                companyAvailableUnitsPurchasedDataModel.ShowAvailableUnitsInMobile = true;
            }

            return companyAvailableUnitsPurchasedDataModel;
        }

        private bool CanBuyPacks(bool haveMembresyExpired, PortalConfig portalConfig, CompanyCredentials companyCredentials)
        {
            if (haveMembresyExpired)
            {
                int lastMembresyExpiredId = CompanyProductService.GetCompanyProductIdLastMembresyExpired(companyCredentials.IdCompany, portalConfig.PortalId);
                if (lastMembresyExpiredId > 0)
                {
                    var lastMembresyExpiredCompanyProduct = CompanyProductService.GetByCompanyProductId(lastMembresyExpiredId, portalConfig.PortalId, companyCredentials.IdCompany);
                    if (lastMembresyExpiredCompanyProduct.ServiceTypeId == (int)ProductServiceTypeEnum.Courtesy ||
                        lastMembresyExpiredCompanyProduct.DateExpiration < DateTime.Now.AddDays(-30))
                    {
                        return true;
                    }
                }
                return false;
            }
            return true;
        }

        private void GetValuesVision(HomeDataModel homeDataModel, int companyId, PortalConfig portalConfig)
        {
            var companyValuation = _globalVisionService.GetMyCompanyValuation(
               new CompanyValuationFilters
               {
                   IdPortal = portalConfig.PortalId,
                   IdCompany = companyId,
                   IdMaster = 0,
                   UrlRewrite = string.Empty
               },
               "companyvaluation/get");

            //Evaluacion general
            homeDataModel.GeneralValuationScoreStars = StringToolsHelper.ChangeGroupPointForDecimalPoint(companyValuation.RatingCompany.AverageDetail.ToString("N2"), portalConfig.PortalId);
            homeDataModel.GeneralStarsStyle = $"{((115 * companyValuation.RatingCompany.AverageDetail) / 5).ToString().Replace(",", ".")}px";
            homeDataModel.TotalValuations = $"{StringToolsHelper.ChangeGroupPointForDecimalPoint(companyValuation.TotalValuations.ToString("N0"), portalConfig.PortalId)} {PageLiteralsHelper.GetLiteral("LIT_EVALUATIONS", (int)PageEnum.HomePrivada, portalConfig)}";


            //DoughnutCircle
            if (companyValuation.RecommendedPercent > 0)
            {
                homeDataModel.RecommendationsNumber = StringToolsHelper.ChangeGroupPointForDecimalPoint(companyValuation.Recommended.ToString("N0"), portalConfig.PortalId);

                var percent = companyValuation.RecommendedPercent;
                homeDataModel.RecomendedPercent = percent.ToString();
                if (percent > 0)
                {
                    var degree = (360 * percent) / 100;
                    homeDataModel.DoughnutCircle = degree.ToString();

                    if (percent > 50)
                        homeDataModel.ClassDivDoughnut = "bDoughnut over50";
                    else
                        homeDataModel.ClassDivDoughnut = "bDoughnut";
                }
            }

            //Followers
            var totalFollewers = CompanyService.GetCompanyFollowersCount(companyId);
            homeDataModel.CompanyFollowers = StringToolsHelper.ChangeGroupPointForDecimalPoint(totalFollewers.ToString("N0"), portalConfig.PortalId);
        }

        private VisionSummaryDataModel GetVisionSummaryDataModel(CompanyProductEntity companyProduct, PortalConfig portalConfig, CompanyCredentials companyCredentials)
        {
            var visionSummaryDataModel = Mapper.Map<VisionSummaryDTO, VisionSummaryDataModel>(_globalVisionService.GetVisionSummaryDTO(companyCredentials, portalConfig));
            FillVisionSummaryDataModel(visionSummaryDataModel, companyProduct);
            return visionSummaryDataModel;
        }

        private void FillVisionSummaryDataModel(VisionSummaryDataModel visionSummaryDataModel, CompanyProductEntity companyProduct)
        {
            visionSummaryDataModel.IsUnblockedVision = CompanyProductService.HasFeature(ProductAmbitEnum.Vision, companyProduct);
            visionSummaryDataModel.ShowLinkEditCompany = SecurityHelper.IsActionPemitedByRole(Master.Entities.Enums.SecurityActionEnum.CompanyDataEdit);
            visionSummaryDataModel.NumberLimitEvaluations = NUMBER_LIMIT_EVALUATIONS;
            visionSummaryDataModel.HaReviewsFeature = companyProduct.Features.Exists(f => f.AmbitId == (short)ProductAmbitEnum.VisionNumberGetReviews);
        }

        private bool SendZendeskTicketSherlock(PortalConfig portalConfig, CompanyCredentials companyCredentials)
        {
            var company = CompanyService.GetByPK(GetCompanySearchById(portalConfig.PortalId, companyCredentials.IdCompany));

            var sb = new StringBuilder();
            sb.AppendLine(PageLiteralsHelper.GetLiteral("ZENDESCK_BODY_INFO", (int)PageEnum.Sherlock, portalConfig));
            sb.AppendLine("");
            sb.AppendLine($"{PageLiteralsHelper.GetLiteral("ZENDESCK_BODY_COUNTRY", (int)PageEnum.Sherlock, portalConfig)} {portalConfig.CountryName}");
            sb.AppendLine($"{PageLiteralsHelper.GetLiteral("ZENDESCK_BODY_IDCOMPANY", (int)PageEnum.Sherlock, portalConfig)} {company.Id}");
            sb.AppendLine($"{PageLiteralsHelper.GetLiteral("ZENDESCK_BODY_COMPNAYNAME", (int)PageEnum.Sherlock, portalConfig)} {company.ComercialName}");
            sb.AppendLine($"{PageLiteralsHelper.GetLiteral("ZENDESCK_BODY_USERNAME", (int)PageEnum.Sherlock, portalConfig)} {companyCredentials.Name}");
            sb.AppendLine($"{PageLiteralsHelper.GetLiteral("ZENDESCK_BODY_USERMAIL", (int)PageEnum.Sherlock, portalConfig)} {companyCredentials.Email}");
            sb.AppendLine($"{PageLiteralsHelper.GetLiteral("ZENDESCK_BODY_CURRENT_PRODUCT_CT", (int)PageEnum.Sherlock, portalConfig)} {CompanyHelper.GetCurrentProductDescription(companyCredentials)}");
            sb.AppendLine($"{PageLiteralsHelper.GetLiteral("ZENDESCK_BODY_COMERCIAL_ASSIGNED_NAME", (int)PageEnum.Sherlock, portalConfig)} {PageLiteralsHelper.GetLiteral("EMAIL_CONTACTO_COMERCIAL_GENERICO", (int)PageEnum.Sherlock, portalConfig)}");

            var zendeskTicket = new ZendeskTicketEntity();
            zendeskTicket.ticket.subject = PageLiteralsHelper.GetLiteral("ZENDESCK_SUBJECT", (int)PageEnum.Sherlock, portalConfig);
            zendeskTicket.ticket.requester.email = companyCredentials.Email;
            zendeskTicket.ticket.requester.name = companyCredentials.Name;
            zendeskTicket.ticket.comment = new CommentEntity()
            {
                body = sb.ToString()
            };

            return _mailingZendeskTicketService.Send(zendeskTicket, ZendeskPostbox.Sherlock);
        }

        private PopUpErrorAlertsDataModel GetLiteralsPopUpErrorAlerts(CompanyEntity company, PortalConfig portalConfig)
        {
            if (company.CompanyStatusId != (int)CompanyStatusEnum.Discarted)
            {
                return new PopUpErrorAlertsDataModel();
            }

            CompanyRejection rejection = null;
            if (company.CompanyRejectionId > 0)
            {
                rejection = CompanyHelper.GetRejection(company.CompanyRejectionId, portalConfig.PortalId);
            }

            var pageId = (int)PageEnum.SiteMasterCompany;

            var urlSubmitRequest = "";
            var existCompanyRejectionIdRequest = rejection != null;
            var exitRejectionTypeEditInfoOrRejection = false;
            if (existCompanyRejectionIdRequest)
            {
                exitRejectionTypeEditInfoOrRejection = rejection.Action == (short)CompanyRejectionIdEnum.Request || rejection.Action == (short)CompanyRejectionIdEnum.EditInCompany;
                if (rejection.Action == (short)CompanyRejectionIdEnum.Request)
                {
                    urlSubmitRequest = PageLiteralsHelper.GetLiteral("LIT_URL_SUBMIT_REQUEST", pageId, portalConfig);
                }
            }

            return new PopUpErrorAlertsDataModel()
            {
                Title = PageLiteralsHelper.GetLiteral("LIT_COMPANY_NOT_VALIDATED", pageId, portalConfig),
                Reason = existCompanyRejectionIdRequest ? rejection.Reason : "",
                Solution = existCompanyRejectionIdRequest ? rejection.Solution : string.Join("", new string[] { PageLiteralsHelper.GetLiteral("LIT_REVIEW_REJECTION", pageId, portalConfig), PageLiteralsHelper.GetLiteral("LIT_REVIEW_REJECTION_2", pageId, portalConfig) }),
                TextButtonClose = PageLiteralsHelper.GetLiteral("LIT_NOT_NOW", pageId, portalConfig),
                TextButtonRedirect = exitRejectionTypeEditInfoOrRejection ? PageLiteralsHelper.GetLiteral("LIT_EDIT_INFORMATION", pageId, portalConfig) : PageLiteralsHelper.GetLiteral("LIT_CONTACT", pageId, portalConfig),
                UrlToRedirecto = existCompanyRejectionIdRequest ? GetUrlAction(rejection.Action, urlSubmitRequest) : $"{portalConfig.url_web}/avisolegal/",
                IsVisible = true
            };
        }

        private string GetUrlAction(short companyRejectionAction, string urlSubmitRequest)
        {
            if (companyRejectionAction == (short)CompanyRejectionIdEnum.Request)
            {
                return urlSubmitRequest;
            }
            else if (companyRejectionAction == (short)CompanyRejectionIdEnum.EditInCompany)
            {
                return @Url.Action("Index", "CompanyConfigurationEditCompany");
            }
            else
            {
                return @Url.Action("Index", "Contact", new { idc = EncryptationHelper.Encrypt(SecurityHelper.GetCompanyCredentials().IdCompany.ToString()) });
            }
        }

        private PopUpDataModel GetActionNotAllowedPopUpByMembership(PortalConfig portalConfig)
        {
            int pageId = (int)PageEnum.HomePrivada;

            return new PopUpDataModel()
            {
                Title = PageLiteralsHelper.GetLiteral("LIT_ACTION_NOT_ALLOWED_MEMBERSHIP_TITLE", pageId, portalConfig),
                Message = PageLiteralsHelper.GetLiteral("LIT_ACTION_NOT_ALLOWED_MEMBERSHIP", pageId, portalConfig),
                TitleBtnOk = PageLiteralsHelper.GetLiteral("LIT_BTN_CONTACTAR", pageId, portalConfig),
                HasButtonOk = true,
            };
        }

        private (bool Error, string ErrorMessage) PublishToRabbit(PortalConfig portalConfig, CompanyEntity company)
        {
            var mailCompanyAuthorizationRemember = GetMailCompanyauthorizationRememberRabbitMessage(portalConfig, company);

            try
            {
                //Assignation
                int idApp = 0;
                if (ConfigurationManager.AppSettings["APP_ID"] != null) int.TryParse(ConfigurationManager.AppSettings["APP_ID"], out idApp);
                int idQueue = portalConfig.AEPortalConfig.IdQueueRabbitMailCompanyAuthorizationRemember;
                var idPortal = mailCompanyAuthorizationRemember.PortalId;

                //Validate
                if (idQueue <= 0) return (true, $"valor recuperado de la idQueue {idQueue}");
                if (idApp <= 0) return (true, $"valor recuperado de la idApp {idApp}");
                if (string.IsNullOrWhiteSpace(mailCompanyAuthorizationRemember.EmailTo)) return (true, $"valor recuperado del objeto MailCompanyProductProduced EmailTo {mailCompanyAuthorizationRemember.EmailTo}");

                //Publish to rabbit queue
                _producerRabbitMQ.Publish(mailCompanyAuthorizationRemember, new RabbitMQ.Model.ProducerDeliverEventArgs()
                {
                    AppId = idApp,
                    QueueId = idQueue,
                    Persistent = true,
                    NodeName = NewsLetterEnum.MailCompanyAuthorizationRemember.ToString(),
                    Headers = new Dictionary<string, object>()
                    {
                        { "ID_PORTAL", mailCompanyAuthorizationRemember.PortalId }
                    }
                });
                Session["LAST_REMEMBER_SEND"] = DateTime.UtcNow;

                return (false, String.Empty);
            }
            catch (Exception ex)
            {
                ExceptionPublisherService.Publish(
                    ex,
                    "CompanyController",
                    "PublishToRabbit",
                    false,
                    new Dictionary<string, string>()
                    {
                        { "IdCompany", mailCompanyAuthorizationRemember?.CompanyId.ToString() }
                    },
                    mailCompanyAuthorizationRemember.PortalId
                );

                return (true, ex.Message);
            }
        }

        private MailCompanyAuthorizationRemember GetMailCompanyauthorizationRememberRabbitMessage(PortalConfig portalConfig, CompanyEntity company)
        {
            return new MailCompanyAuthorizationRemember
            {
                CompanyId = company.Id,
                CompanyName = company.CompanyName,
                PortalId = portalConfig.PortalId,
                EmailTo = company.User.Email,
                UserId = company.User.Id,
                UserName = company.User.Username,
                NewsletterId = (int)NewsLetterEnum.MailCompanyAuthorizationRemember,
                UrlConfirmationMail = $"{portalConfig.url_empresa}{Url.Action("AuthorizationRememberActive", "Company")}",
                VerificationCode = BuildVerifyCode(),
                Expires = DateTime.Now.AddMinutes(VERIFICATION_CODE_LIFETIME_MINUTES),
            };
        }

        private static string BuildVerifyCode()
        {
            var random = new Random();
            return $"{random.Next(0, 9999):0000}";
        }

        private ProductDataModel FillBBDDcvProductCounters(ProductDataModel productToAdd, int idCompany, PortalConfig portalConfig)
        {
            productToAdd.IsBBDDcvConsumableProduct = true;
            productToAdd.ShowTagPromo = true;
            productToAdd.LandingProductSubtitle = PageLiteralsHelper.GetLiteral("LIT_BBDDCV_SUBTITLE", (int)PageEnum.HomePrivada, portalConfig);

            var BBDDcvProducts = CompanyProductService.GetBBDDsProductsEntitiy(idCompany, portalConfig.PortalId);

            if (BBDDcvProducts.AvailableUnits > 0)
            {
                productToAdd.AvailabeUnits += BBDDcvProducts.AvailableUnits;
                productToAdd.TotalUnits += BBDDcvProducts.InitialUnits;
                productToAdd.UsedUnits += BBDDcvProducts.InitialUnits - BBDDcvProducts.AvailableUnits;
                var percentUnits = (int)Math.Round((double)(100 * productToAdd.UsedUnits) / productToAdd.TotalUnits);
                productToAdd.PercentUnits = Math.Min(percentUnits, 100);
            }

            return productToAdd;
        }

        private string GetViewHomeName(short companyProductGroup, PortalConfig portalConfig)
        {
            if(1 == 2) //temporal a la espera de definici�n de producto
            {
                return VIEW_HOME_SUBSCRIPTION;
            }
            if (companyProductGroup == (short)ProductGroupsEnum.Membership)
            {
                return VIEW_HOME_MEMBRESHIP; 
            }
            
            return VIEW_HOME_FREEMIUM_V2;
        }

        private string GetUrlIndexConversations(bool hasNewFreemiumChat, CompanyProductEntity offerProduct)
        {
            return offerProduct.GroupId == (short)ProductGroupsEnum.Membership
                || offerProduct.GroupId != (short)ProductGroupsEnum.Membership && !hasNewFreemiumChat
                ? MESSAGES_CONTROLLER
                : CONVERSATIONS_CONTROLLER;
        }

        private bool HasChatActivate(int idCompany, PortalConfig portalConfig)
        {
            return portalConfig.AEPortalConfig.EnableProgressiveChatFreemium
                && CompanyProductService.HasActiveFeatureByProduct(new SearchCompanyProductsByCompanyDTO(idCompany, ProductAmbitEnum.ChatContactFreemium, portalConfig.PortalId, ProductEnum.FreemiumService));
        }

        private bool IsSurveyCompleted(int idCompany, short idPortal)
        {
            return _surveyService.GetLastResponseDateByCompany(idCompany, idPortal) >= DateTime.Now.AddDays(-15);           
        }

        [HttpPost]
        public JsonResult InsertCSATSurveyResponse()
        {
            var portalConfig = PortalConfigurationService.GetPortalConfiguration();
            var companyCredentials = SecurityHelper.GetCompanyCredentials();
            var companyProduct = CompanyProductService.GetByIdCompany(companyCredentials.IdCompany, companyCredentials.PortalId, OfferIntegratorEnum.CompuTrabajo);

            Request.InputStream.Position = 0;
            using (var reader = new StreamReader(Request.InputStream))
            {
                var body = reader.ReadToEnd();
                var surveyResponse = JsonConvert.DeserializeObject<SurveyResponseDTO>(body);

                if (surveyResponse == null || surveyResponse.IdSurvey <= 0)
                    return Json(new { success = false });

                SurveyResponse response = new SurveyResponse
                {
                    IdCompany = companyCredentials.IdCompany,
                    IdPortal = portalConfig.PortalId,
                    IsMembresy = companyProduct.GroupId == (short)ProductGroupsEnum.Membership,
                    IdSurvey = surveyResponse.IdSurvey.ToShort(),
                    SatisfactionGrade = surveyResponse.SatisfactionGrade.ToShort(),
                    Comment = surveyResponse.Comment
                };

                if(_surveyService.InsertSurveyResponse(response))
                {
                    return Json(new { success = true });
                }
                return Json(new { success = false });
            }

            
        }
    }
}