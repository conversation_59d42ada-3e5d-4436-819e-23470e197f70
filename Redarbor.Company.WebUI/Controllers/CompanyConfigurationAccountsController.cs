using AutoMapper;
using Newtonsoft.Json;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using Redarbor.Common.Entities.Configuration;
using Redarbor.Common.Entities.Constants;
using Redarbor.Common.Entities.Enums;
using Redarbor.Company.Contracts.ServiceLibrary;
using Redarbor.Company.WebUI.Helpers;
using Redarbor.Company.WebUI.Models.Company;
using Redarbor.Company.WebUI.Models.Company.Configuration;
using Redarbor.Company.WebUI.Security;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Core.Contracts.ServiceLibrary;
using Redarbor.Core.Mailing.Contracts.ServiceLibrary;
using Redarbor.Core.Shared.Contracts.ServiceLibrary;
using Redarbor.Master.Contracts.ServiceLibrary;
using Redarbor.Master.Contracts.ServiceLibrary.DTO;
using Redarbor.Master.Entities.Company;
using Redarbor.Master.Entities.Enums;
using Redarbor.Master.Entities.Product;
using Redarbor.Master.Entities.Searchs;
using Redarbor.Master.Entities.Security;
using Redarbor.Master.Entities.Users;
using Redarbor.Offer.Contracts.ServiceLibrary;
using Redarbor.Offer.Contracts.ServiceLibrary.Enums;
using Redarbor.Products.Contracts.ServiceLibrary;
using Redarbor.User.Contracts.ServiceLibrary;
using Redarbor.Web.UI.Library.Controllers;
using Redarbor.Web.UI.Library.Exceptions;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Web.Mvc;

namespace Redarbor.Company.WebUI.Controllers
{
    [RoutePrefix("Company/Configuration/Accounts")]
    [RedarborAuthorize]
    public class CompanyConfigurationAccountsController : RedarborController
    {
        private readonly short _pageId;
        private readonly IUserService _userService;
        private readonly IPortalConfigurationService _portalConfigurationService;
        private readonly ICompanyService _companyService;
        private readonly ICompanyProductService _companyProductService;
        private readonly IDictionaryService _dictionaryService;
        private readonly IUserRoleService _userRoleService;
        private readonly IEncryptionService _encrypService;
        private readonly IExceptionPublisherService _exceptionPublisherService;
        private readonly IMailingService _mailingService;
        private readonly IHashService _hashService;
        private readonly IOfferService _offerService;
        private readonly ISecurityService _securityService;

        public CompanyConfigurationAccountsController(IUserService userService, IPortalConfigurationService portalConfigurationService, ICompanyService companyService,
            ICompanyProductService companyProductService, IDictionaryService dictionaryService, IUserRoleService userRoleService,
            IEncryptionService encrypService,
            IExceptionPublisherService exceptionPublisherService,
            IMailingService mailingService,
            IHashService hashService,
            IOfferService offerService,
            ISecurityService securityService)
        {
            _userService = userService;
            _portalConfigurationService = portalConfigurationService;
            _companyService = companyService;
            _companyProductService = companyProductService;
            _dictionaryService = dictionaryService;
            _userRoleService = userRoleService;
            _encrypService = encrypService;
            _exceptionPublisherService = exceptionPublisherService;
            _mailingService = mailingService;
            _hashService = hashService;
            _offerService = offerService;
            _pageId = (int)PageEnum.CompanyAccounts;
            _securityService = securityService;
        }


        [Route]
        public ActionResult Index()
        {
            try
            {
                var portalConfig = _portalConfigurationService.GetPortalConfiguration();
                CompanyCredentials companyCredentials = SecurityHelper.GetCompanyCredentials();
                CompanyEntity company = this._companyService.GetByPK(new CompanySearchSpecifications(companyCredentials.PortalId) { Id = companyCredentials.IdCompany });

                if (base.checkBlocked(portalConfig.company_block, company.Blocked, company.LastDateBlockedUpdate, portalConfig.DaysBlock))
                    return RedirectToAction("Blocked", "Account");

                var companyProduct = _companyProductService.GetByIdCompany(companyCredentials.IdCompany, companyCredentials.PortalId, OfferIntegratorEnum.CompuTrabajo);
                int initialUsersFeature = this.GetInitialUsersFeature(companyCredentials, companyProduct);

                if (initialUsersFeature > 0
                    && !SecurityHelper.IsActionPemitedByRoleAndIsNotAdvisorRole(SecurityActionEnum.CompanyUsers))
                    return RedirectToAction("Index", "Company");

                AccountUserDataModel dataModel = this.GetAccountUserDataModel(companyCredentials, initialUsersFeature, new AccountUserDataModel(), portalConfig, companyProduct);
                return View(dataModel);
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"{nameof(CompanyConfigurationAccountsController)} -- {nameof(Index)}-Get {ex}");
                this._exceptionPublisherService.Publish(ex, nameof(CompanyConfigurationAccountsController), $"{nameof(Index)}-Get");
                return RedirectToAction("Index", "Home");
            }
        }

        [HttpPost]
        [Route("Filter")]
        [ValidateAntiForgeryToken()]
        public ActionResult FilterAccounts(AccountUserDataModel accountUserDataModel)
        {
            var portalConfig = _portalConfigurationService.GetPortalConfiguration();
            CompanyCredentials companyCredentials = SecurityHelper.GetCompanyCredentials();
            var companyProduct = _companyProductService.GetByIdCompany(companyCredentials.IdCompany, companyCredentials.PortalId, OfferIntegratorEnum.CompuTrabajo);

            int initialUsersFeature = this.GetInitialUsersFeature(companyCredentials, companyProduct);

            AccountUserDataModel dataModel = this.GetAccountUserDataModel(companyCredentials, initialUsersFeature, accountUserDataModel, portalConfig, companyProduct);
            return View("Index", dataModel);
        }

        private AccountUserDataModel GetAccountUserDataModel(CompanyCredentials companyCredentials, int initialUsersFeature, AccountUserDataModel accountUserDataModelGet, PortalConfig portalConfig, CompanyProductEntity companyProduct)
        {
            List<UserEntity> usersList = this._userService.GetManagersByIdCompany(companyCredentials.IdCompany, portalConfig.PortalId);
            List<UserEntity> allUsersList = usersList;

            if (usersList.Any())
            {
                usersList = FilterAndSortList(usersList, accountUserDataModelGet);
            }            

            if (usersList == null || initialUsersFeature == 0) throw new ArgumentNullException();
            AccountUserDataModel accountUserDataModel = new AccountUserDataModel();
            accountUserDataModel.Users = Mapper.Map<List<UserEntity>, List<UserDataModel>>(usersList);
            accountUserDataModel.TotalUsers = usersList.Count;
            accountUserDataModel.Role = SecurityHelper.GetUserRole();
            accountUserDataModel.LimitUsers = initialUsersFeature;
            accountUserDataModel.ExceededLimitUsers = allUsersList.Count >= initialUsersFeature;
            accountUserDataModel.ShowContactPhone = accountUserDataModel.ExceededLimitUsers ? this.ShowContactPhone(portalConfig) : false;
            accountUserDataModel.HasFeature = initialUsersFeature > 0;
            accountUserDataModel.UsersOrders = GetUsersOrders(portalConfig);
            accountUserDataModel.RoleFilter = GetRolsItems(portalConfig);
            accountUserDataModel.UsersItems = GetUsersItems(allUsersList);
            accountUserDataModel.IsCompanyMembership = companyProduct.GroupId == (short)ProductGroupsEnum.Membership;

            accountUserDataModel.AllUsersFeatDecript = PageLiteralsHelper.GetLiteral("LIT_ALLFEATDESC", _pageId, portalConfig);

            List<SecurityByRoleEntity> allRoleFeaturesList = _securityService.GetAllRoleFeaturesList();
            accountUserDataModel.RoleAdministratorPrincFeatures = ConstructRoleFeatureDescriptions((short)UserRoleEnum.ADMINISTRADOR_PRINCIPAL, allRoleFeaturesList, portalConfig);
            accountUserDataModel.RoleGestorFeatures = ConstructRoleFeatureDescriptions((short)UserRoleEnum.GESTOR, allRoleFeaturesList, portalConfig);
            accountUserDataModel.RoleAdministradorFeatures = ConstructRoleFeatureDescriptions((short)UserRoleEnum.ADMINISTRADOR, allRoleFeaturesList, portalConfig);
            accountUserDataModel.RoleGestorSeniorFeatures = ConstructRoleFeatureDescriptions((short)UserRoleEnum.GESTOR_SENIOR, allRoleFeaturesList, portalConfig);
            accountUserDataModel.RoleSupervisorFeatures = ConstructRoleFeatureDescriptions((short)UserRoleEnum.SUPERVISOR, allRoleFeaturesList, portalConfig);
            accountUserDataModel.RoleCompuadvisorFeatures = ConstructRoleFeatureDescriptions((short)UserRoleEnum.COMPUADVISOR, allRoleFeaturesList, portalConfig);
            accountUserDataModel.RoleEntrevistadorFeatures = ConstructRoleFeatureDescriptions((short)UserRoleEnum.ENTREVISTADOR, allRoleFeaturesList, portalConfig);
            accountUserDataModel.RoleSeleccionadorFeatures = ConstructRoleFeatureDescriptions((short)UserRoleEnum.SELECCIONADOR, allRoleFeaturesList, portalConfig);

            return accountUserDataModel;
        }

        private string ConstructRoleFeatureDescriptions(short userRole, List<SecurityByRoleEntity> allRoleFeaturesList, PortalConfig portalConfig)
        {
            StringBuilder description = new StringBuilder();
            List<SecurityByRoleEntity> roleFeatureList = new List<SecurityByRoleEntity>();

            switch (userRole)
            {
                case (short)UserRoleEnum.ADMINISTRADOR_PRINCIPAL:
                    description.Append(PageLiteralsHelper.GetLiteral("LIT_GENERIC_ROLE_ACCES", _pageId, portalConfig)).Replace("#ROLE#", PageLiteralsHelper.GetLiteral("LIT_ADMINISTRADOR_PRINCIPAL", _pageId, portalConfig));
                    roleFeatureList = allRoleFeaturesList.Where(x => x.IdRole == (short)UserRoleEnum.ADMINISTRADOR_PRINCIPAL).ToList();
                    break;
                case (short)UserRoleEnum.GESTOR:
                    description.Append(PageLiteralsHelper.GetLiteral("LIT_GENERIC_ROLE_ACCES", _pageId, portalConfig)).Replace("#ROLE#", PageLiteralsHelper.GetLiteral("LIT_GESTOR", _pageId, portalConfig));
                    roleFeatureList = allRoleFeaturesList.Where(x => x.IdRole == (short)UserRoleEnum.GESTOR).ToList();
                    break;
                case (short)UserRoleEnum.ADMINISTRADOR:
                    description.Append(PageLiteralsHelper.GetLiteral("LIT_GENERIC_ROLE_ACCES", _pageId, portalConfig)).Replace("#ROLE#", PageLiteralsHelper.GetLiteral("LIT_ADMINISTRADOR", _pageId, portalConfig));
                    roleFeatureList = allRoleFeaturesList.Where(x => x.IdRole == (short)UserRoleEnum.ADMINISTRADOR).ToList();
                    break;
                case (short)UserRoleEnum.GESTOR_SENIOR:
                    description.Append(PageLiteralsHelper.GetLiteral("LIT_GENERIC_ROLE_ACCES", _pageId, portalConfig)).Replace("#ROLE#", PageLiteralsHelper.GetLiteral("LIT_GESTOR_SENIOR", _pageId, portalConfig));
                    roleFeatureList = allRoleFeaturesList.Where(x => x.IdRole == (short)UserRoleEnum.GESTOR_SENIOR).ToList();
                    break;
                case (short)UserRoleEnum.SUPERVISOR:
                    description.Append(PageLiteralsHelper.GetLiteral("LIT_GENERIC_ROLE_ACCES", _pageId, portalConfig)).Replace("#ROLE#", PageLiteralsHelper.GetLiteral("LIT_SUPERVISOR", _pageId, portalConfig));
                    roleFeatureList = allRoleFeaturesList.Where(x => x.IdRole == (short)UserRoleEnum.SUPERVISOR).ToList();
                    break;
                case (short)UserRoleEnum.COMPUADVISOR:
                    description.Append(PageLiteralsHelper.GetLiteral("LIT_GENERIC_ROLE_ACCES", _pageId, portalConfig)).Replace("#ROLE#", PageLiteralsHelper.GetLiteral("LIT_COMPUADVISOR", _pageId, portalConfig));
                    roleFeatureList = allRoleFeaturesList.Where(x => x.IdRole == (short)UserRoleEnum.COMPUADVISOR).ToList();
                    break;
                case (short)UserRoleEnum.ENTREVISTADOR:
                    description.Append(PageLiteralsHelper.GetLiteral("LIT_GENERIC_ROLE_ACCES", _pageId, portalConfig)).Replace("#ROLE#", PageLiteralsHelper.GetLiteral("LIT_ENTREVISTADOR", _pageId, portalConfig));
                    roleFeatureList = allRoleFeaturesList.Where(x => x.IdRole == (short)UserRoleEnum.ENTREVISTADOR).ToList();
                    break;
                case (short)UserRoleEnum.SELECCIONADOR:
                    description.Append(PageLiteralsHelper.GetLiteral("LIT_GENERIC_ROLE_ACCES", _pageId, portalConfig)).Replace("#ROLE#", PageLiteralsHelper.GetLiteral("LIT_SELECCIONADOR", _pageId, portalConfig));
                    roleFeatureList = allRoleFeaturesList.Where(x => x.IdRole == (short)UserRoleEnum.SELECCIONADOR).ToList();
                    break;
            }

            return description.Append(PrintFeaturesDescription(roleFeatureList, portalConfig)).ToString();
        }

        private StringBuilder PrintFeaturesDescription(List<SecurityByRoleEntity> roleFeatureList, PortalConfig portalConfig)
        {
            StringBuilder description = new StringBuilder();
            description.Append("");

            if (roleFeatureList.Count > 0)
            {
                if (roleFeatureList.Find(x => x.IdReferenceType == (int)SecurityActionEnum.CompanyReportsAccess) != null)
                {
                    description.Append(PageLiteralsHelper.GetLiteral("LIT_COMPANYREPORTSACCESS", _pageId, portalConfig));
                }
                if (roleFeatureList.Find(x => x.IdReferenceType == (int)SecurityActionEnum.CompanyUsers) != null)
                {
                    description.Append(PageLiteralsHelper.GetLiteral("LIT_COMPANYUSERS", _pageId, portalConfig));
                }
                if (roleFeatureList.Find(x => x.IdReferenceType == (int)SecurityActionEnum.CompanyInvoice) != null)
                {
                    description.Append(PageLiteralsHelper.GetLiteral("LIT_COMPANYINVOICE", _pageId, portalConfig));
                }
                if (roleFeatureList.Find(x => x.IdReferenceType == (int)SecurityActionEnum.CompanyOffersManageByAllUsers) != null)
                {
                    description.Append(PageLiteralsHelper.GetLiteral("LIT_MANAGEBYALLUUSERS", _pageId, portalConfig));
                }
                if (roleFeatureList.Find(x => x.IdReferenceType == (int)SecurityActionEnum.CompanyOffersManageFilters) != null)
                {
                    description.Append(PageLiteralsHelper.GetLiteral("LIT_MANAGEFILTERS", _pageId, portalConfig));
                }
                if (roleFeatureList.Find(x => x.IdReferenceType == (int)SecurityActionEnum.CompanyDataEdit) != null)
                {
                    description.Append(PageLiteralsHelper.GetLiteral("LIT_COMPANYDATAEDIT", _pageId, portalConfig));
                }
                if (roleFeatureList.Find(x => x.IdReferenceType == (int)SecurityActionEnum.CompanyCvsDownload) != null)
                {
                    description.Append(PageLiteralsHelper.GetLiteral("LIT_CVSDOWNLOAD", _pageId, portalConfig));
                }
                if (roleFeatureList.Find(x => x.IdReferenceType == (int)SecurityActionEnum.CompanyOffersManageLessThanYoursRol) != null)
                {
                    description.Append(PageLiteralsHelper.GetLiteral("LIT_LESSTHAMYOURSROL", _pageId, portalConfig));
                }
                if (roleFeatureList.Find(x => x.IdReferenceType == (int)SecurityActionEnum.CompanyCreditsAssign) != null)
                {
                    description.Append(PageLiteralsHelper.GetLiteral("LIT_CREDITSASSIGN", _pageId, portalConfig));
                }
                if (roleFeatureList.Find(x => x.IdReferenceType == (int)SecurityActionEnum.CompanyCompuAdvisorAccess) != null)
                {
                    description.Append(PageLiteralsHelper.GetLiteral("LIT_ADVISORACCES", _pageId, portalConfig));
                }
                if (roleFeatureList.Find(x => x.IdReferenceType == (int)SecurityActionEnum.CompanyCharts) != null)
                {
                    description.Append(PageLiteralsHelper.GetLiteral("LIT_COMPANYCHARTS", _pageId, portalConfig));
                }
                if (roleFeatureList.Find(x => x.IdReferenceType == (int)SecurityActionEnum.ManageOffers) != null)
                {
                    description.Append(PageLiteralsHelper.GetLiteral("LIT_MANAGEOFFERS", _pageId, portalConfig));
                }
            }

            return description;
        }

        private List<UserEntity> FilterAndSortList(List<UserEntity> usersList, AccountUserDataModel accountUserDataModel)
        {
            string filterRole = _encrypService.Decrypt(accountUserDataModel.RoleFilterSelected);

            if (!string.IsNullOrEmpty(filterRole))
            {
                short filterRoleShort = 0;
                short.TryParse(filterRole, out filterRoleShort);
                usersList = usersList.Where(x => x.RoleId == filterRoleShort).ToList();
            }

            if (!string.IsNullOrEmpty(accountUserDataModel.FilterMail))
            {
                usersList = usersList.Where(x => x.Email.ToLower().Contains(accountUserDataModel.FilterMail.ToLower())).ToList();
            }

            if (!string.IsNullOrEmpty(accountUserDataModel.FilterUserName))
            {
                var compareInfo = CultureInfo.InvariantCulture.CompareInfo;
                usersList = usersList.Where(p => compareInfo.IndexOf(p.ContactName.ToLower(), accountUserDataModel.FilterUserName.ToLower(), CompareOptions.IgnoreNonSpace) > -1).ToList();
            }

            if (accountUserDataModel.UserOrderSelected > 0)
            {
                usersList = GetOrderedList(usersList, accountUserDataModel.UserOrderSelected);
            }

            return usersList;
        }

        private List<UserEntity> GetOrderedList(List<UserEntity> users, int orderBy)
        {
            switch (orderBy)
            {
                case (int)OrderUsersByEnum.ByNameAsc:
                    users = users.OrderBy(x => x.ContactName).ToList();
                    break;
                case (int)OrderUsersByEnum.ByNameDesc:
                    users = users.OrderByDescending(x => x.ContactName).ToList();
                    break;
                case (int)OrderUsersByEnum.ByMailAsc:
                    users = users.OrderBy(x => x.Email).ToList();
                    break;
                case (int)OrderUsersByEnum.ByMailDesc:
                    users = users.OrderByDescending(x => x.Email).ToList();
                    break;
                case (int)OrderUsersByEnum.ByRoleAsc:
                    users = users.OrderBy(x => x.RoleId).ToList();
                    break;
                case (int)OrderUsersByEnum.ByRoleDesc:
                    users = users.OrderByDescending(x => x.RoleId).ToList();
                    break;
                case (int)OrderUsersByEnum.ByLastLoginAsc:
                    users = users.OrderBy(x => x.LastLoginOn).ToList();
                    break;
                case (int)OrderUsersByEnum.ByLastLoginDesc:
                    users = users.OrderByDescending(x => x.LastLoginOn).ToList();
                    break;
                default:
                    users = users.OrderBy(x => x.ContactName).ToList();
                    break;
            }

            return users;
        }

        [HttpPost]
        [ValidateAntiForgeryToken()]
        [Route]
        public JsonResult EditOrInsert(UserDataModel userDataModel)
        {
            try
            {
                var portalConfig = _portalConfigurationService.GetPortalConfiguration();
                var companyCredentials = SecurityHelper.GetCompanyCredentials();

                ViewData["portalConfig"] = portalConfig;
                ViewData["loginRole"] = companyCredentials.UserRole;

                var isCorrectEditOrInsert = false;

                SetEditOrNewUser(userDataModel, portalConfig, companyCredentials);

                if (userDataModel.IsNew)
                {
                    SetNewUser(userDataModel, portalConfig);
                    var userEntity = AutoMapper.Mapper.Map<UserDataModel, UserEntity>(userDataModel);
                    var idUser = _userService.Add(userEntity);

                    var idUserEncrypted = EncryptationHelper.Encrypt(idUser.ToString());
                    if (!string.IsNullOrEmpty(idUserEncrypted))
                    {
                        userDataModel.Id = idUser;
                        userDataModel.IdEncrypt = idUserEncrypted;
                        userEntity.Id = idUser;
                        userEntity.IdEncrypt = idUserEncrypted;
                    }

                    isCorrectEditOrInsert = idUser > 0;

                    var company = _companyService.GetByPK(new CompanySearchSpecifications(userEntity.PortalId) { Id = userEntity.CompanyId });
                    _companyService.AddStackService(company);

                    if (isCorrectEditOrInsert && portalConfig.SendMailAddedCompanyUser)
                    {
                        if (company.Id > 0 && userEntity != null)
                        {
                            company.User = userEntity;
                            _mailingService.AddNewsLetterMail(company, (short)NewsLetterEnum.ConfirmationMailCreateCompanyUser, userEntity.PortalId, (int)NotificationTypeEnum.Company, (int)PriorityEnum.MEDIUM);
                        }
                    }
                }
                else
                {
                    SetUpdateUser(userDataModel, portalConfig);

                    long.TryParse(EncryptationHelper.Decrypt(userDataModel.IdEncrypt), out var idUser);

                    userDataModel.Id = idUser;

                    var userEntity = AutoMapper.Mapper.Map<UserDataModel, UserEntity>(userDataModel);

                    var company = _companyService.GetByPK(new CompanySearchSpecifications(userEntity.PortalId) { Id = userEntity.CompanyId });
                    _companyService.AddStackService(company);

                    if (portalConfig.SendMailAddedCompanyUser)
                    {
                        var userFound = _userService.Get(userDataModel.Id, true, portalConfig.PortalId);
                        if (userFound.Email != userDataModel.Email && userDataModel.RoleId != (short)UserRoleEnum.ADMINISTRADOR_PRINCIPAL)
                        {
                            userDataModel.StatusId = portalConfig.SendMailAddedCompanyUser ? (short)UserStatusEnum.Desactivado : (short)UserStatusEnum.Activo;
                            userEntity.StatusId = userDataModel.StatusId;
                            userEntity.Password = _hashService.Encrypt(userEntity.Password);

                            company.User = userEntity;
                            _mailingService.AddNewsLetterMail(company, (short)NewsLetterEnum.ConfirmationMailCreateCompanyUser, userEntity.PortalId, (int)NotificationTypeEnum.Company, (int)PriorityEnum.MEDIUM);
                        }
                    }

                    var needAddToStack = _userService.NeedAddToStack(userEntity);
                    isCorrectEditOrInsert = _userService.Update(userEntity);

                    if (isCorrectEditOrInsert)
                    {
                        if (userDataModel.RoleId == (short)UserRoleEnum.ADMINISTRADOR_PRINCIPAL)
                            _companyService.UpdateCompanyEmail(companyCredentials.IdCompany, userDataModel.Email, companyCredentials.PortalId);

                        if (needAddToStack)
                            AddStackAndUpdateRolInOffers(userEntity, portalConfig);
                    }
                }

                return Json(new
                {
                    view = RenderRazorViewToString(ControllerContext, "_UserEditRow", userDataModel),
                    isValid = isCorrectEditOrInsert
                });
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyConfigurationAccountsController -- EditOrInsert {ex}");
                _exceptionPublisherService.Publish(ex, "CompanyConfigurationAccountsController", "EditOrInsert");
                return Json(false);
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken()]
        public JsonResult Delete(UserDeleteDataModel userDeleteDataModel)
        {
            try
            {
                long.TryParse(EncryptationHelper.Decrypt(userDeleteDataModel.User.IdEncrypt), out var idUser);

                if (idUser <= 0)
                    return Json(false);

                if (userDeleteDataModel.User.HasContent)
                {
                    long idUserToAssignContent = 0;

                    if (userDeleteDataModel.AssignContent == 0)
                    {
                        long.TryParse(EncryptationHelper.Decrypt(userDeleteDataModel.IdEncryptAdministratorPrincipal),
                            out idUserToAssignContent);
                    }
                    else
                    {
                        long.TryParse(
                            EncryptationHelper.Decrypt(userDeleteDataModel.IdEncryptUserToAssingContentSelected),
                            out idUserToAssignContent);
                    }

                    return Json(_userService.DeleteUser((int)idUser, (int)idUserToAssignContent,
                        SecurityHelper.GetCompanyCredentials().IdCompany));
                }
                else
                    return Json(_userService.DeleteUser((int)idUser,
                        SecurityHelper.GetCompanyCredentials().IdCompany));
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyConfigurationAccountsController -- Delete {ex}");
                _exceptionPublisherService.Publish(ex, "CompanyConfigurationAccountsController", "Delete");
                return Json(false);
            }
        }

        [HttpPost]
        public PartialViewResult FormEdit(UserDataModel userDataModel)
        {
            try
            {
                var portalConfig = _portalConfigurationService.GetPortalConfiguration();
                ModelState.Clear();
                FillDropDowns(userDataModel, portalConfig);

                userDataModel.DomainCompanySelected = userDataModel.DomainEmail;
                userDataModel.RolSelected = _encrypService.Encrypt(userDataModel.RoleId.ToString());

                userDataModel.IsNew = false;

                return PartialView("_UserEditOrInsert", userDataModel);
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyConfigurationAccountsController -- Delete {ex}");
                _exceptionPublisherService.Publish(ex, "CompanyConfigurationAccountsController", "Delete");
                return PartialView("_UserEditOrInsert", userDataModel);
            }
        }

        [HttpPost]
        public PartialViewResult FormNew(UserDataModel userDataModel)
        {
            try
            {
                ModelState.Clear();

                userDataModel.IsNew = true;

                if (IsValidToAdd())
                {
                    var portalConfig = _portalConfigurationService.GetPortalConfiguration();
                    FillDropDowns(userDataModel, portalConfig);
                    userDataModel.IsValidToAdd = true;
                }
                else
                    userDataModel.IsValidToAdd = false;

                return PartialView("_UserEditOrInsert", userDataModel);
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyConfigurationAccountsController -- FormNew {ex}");
                _exceptionPublisherService.Publish(ex, "CompanyConfigurationAccountsController", "FormNew");
                return PartialView("_UserEditOrInsert", userDataModel);
            }
        }

        public JsonResult CheckEmailWithDomain(string domainCompanySelected, string prefixEmail, string email)
        {
            var newEmail = $"{prefixEmail}{domainCompanySelected}";

            if (newEmail != email)
            {
                var portalConfig = _portalConfigurationService.GetPortalConfiguration();

                Regex regexEmail = new Regex(RegularExpressions.EMAIL_REGEX);

                if (!regexEmail.Match(newEmail).Success)
                    return Json(PageLiteralsHelper.GetLiteral("CODE_EMAIL_FORMATO", (short)PageEnum.CompanyAccounts, portalConfig), JsonRequestBehavior.AllowGet);

                if (_userService.ExistsMail(newEmail, portalConfig.PortalId))
                    return Json(PageLiteralsHelper.GetLiteral("CODE_EMAIL_REPETIDO", (short)PageEnum.CompanyAccounts, portalConfig), JsonRequestBehavior.AllowGet);
            }
            return Json(true, JsonRequestBehavior.AllowGet);
        }

        public JsonResult CheckEmail(string idEncrypt, string email)
        {
            var portalConfig = _portalConfigurationService.GetPortalConfiguration();

            long.TryParse(EncryptationHelper.Decrypt(idEncrypt), out var idUser);

            if (idUser != 0)
            {
                if (_userService.ExistsMail(email, portalConfig.PortalId))
                {
                    var currentUser = _userService.Get(idUser, true, portalConfig.PortalId);
                    return currentUser.Email == email ? Json(true, JsonRequestBehavior.AllowGet)
                                                : Json(PageLiteralsHelper.GetLiteral("CODE_EMAIL_REPETIDO", (short)PageEnum.CompanyAccounts, portalConfig), JsonRequestBehavior.AllowGet);
                }
            }
            else
            {
                if (_userService.ExistsMail(email, portalConfig.PortalId))
                    return Json(PageLiteralsHelper.GetLiteral("CODE_EMAIL_REPETIDO", (short)PageEnum.CompanyAccounts, portalConfig), JsonRequestBehavior.AllowGet);
            }


            return Json(true, JsonRequestBehavior.AllowGet);
        }

        private void FillDropDowns(UserDataModel userDataModel, PortalConfig portalConfig)
        {
            userDataModel.DomainsCompany = GetDomainsCompanyItems();
            userDataModel.Rols = GetRolsItems(portalConfig);
        }

        private List<SelectListItem> GetDomainsCompanyItems()
        {
            var domainsCompanyItems = new List<SelectListItem>();

            var domainsCompany = _companyService.GetCompanyDomains(SecurityHelper.GetCompanyCredentials().IdCompany);

            foreach (var domain in domainsCompany)
            {
                domainsCompanyItems.Add(new SelectListItem { Text = domain, Value = domain });
            }

            return domainsCompanyItems;
        }

        private List<SelectListItem> GetUsersItems(List<UserEntity> allUsersList)
        {
            List<SelectListItem> usersItems = new List<SelectListItem>();

            foreach (var user in allUsersList)
            {
                if (user.RoleId != (short)UserRoleEnum.SELECCIONADOR &&
                    user.RoleId != (short)UserRoleEnum.COMPUADVISOR)
                {
                    usersItems.Add(new SelectListItem { Text = user.ContactName, Value = user.IdEncrypt });
                }
            }

            return usersItems;
        }

        private List<SelectListItem> GetRolsItems(PortalConfig portalConfig)
        {
            var rolItems = new List<SelectListItem>();
            var dictionaryRoles = _dictionaryService.GetDictionary(Dictionaries.Consumer.Enums.DictionaryEnum.USER_ROLES_CT, portalConfig.PortalId);
            var existFeautureRole = GetFeature(ProductAmbitEnum.ExtraRoles).Id != 0;
            var existFeauturecompuAdvisor = GetFeature(ProductAmbitEnum.Vision).Id != 0;

            var listUserRoles = _userRoleService.GetUserRolesPermitedByAmbitExtraRole(existFeautureRole, existFeauturecompuAdvisor).ToList();
            var userAccountDataModel = new AccountUserDataModel();
            userAccountDataModel.Role = SecurityHelper.GetUserRole();

            rolItems.Add(new SelectListItem() { Value = "", Text = PageLiteralsHelper.GetLiteral("LIT_ROLE_TYPE", _pageId, portalConfig) });

            foreach (UserRoleEnum userRolesEnum in listUserRoles)
            {
                rolItems.Add(new SelectListItem { Text = GetNameByRoleEnum(dictionaryRoles, userRolesEnum), Value = _encrypService.Encrypt(((short)userRolesEnum).ToString()) });
            }

            return rolItems;
        }

        private string GetNameByRoleEnum(Dictionary<string, string> dictionaryRoles, UserRoleEnum userRolesEnum)
        {
            string keyRole = ((short)userRolesEnum).ToString();

            if (!dictionaryRoles.ContainsKey(keyRole))
            {
                return userRolesEnum.ToString();
            }

            return dictionaryRoles[keyRole];
        }

        private void SetNewUser(UserDataModel userDataModel, PortalConfig portalConfig)
        {
            userDataModel.StatusId = portalConfig.SendMailAddedCompanyUser && userDataModel.RoleId != (short)UserRoleEnum.ADMINISTRADOR_PRINCIPAL ? (short)UserStatusEnum.Desactivado : (short)UserStatusEnum.Activo;
            userDataModel.Username = userDataModel.Email;
            userDataModel.OriginId = (short)UserOriginEnum.WEB;
            userDataModel.TypeId = (short)UserTypeEnum.Company;
            userDataModel.CreatedOn = portalConfig.CurrentDateTimePortal;
            userDataModel.CreatedBy = SecurityHelper.GetCompanyCredentials().UserId;
            userDataModel.ParentCompanyId = SecurityHelper.GetCompanyCredentials().IdCompany;
            userDataModel.NdrStatusId = 0;
        }


        private void SetUpdateUser(UserDataModel userDataModel, PortalConfig portalConfig)
        {
            userDataModel.UpdatedOn = portalConfig.CurrentDateTimePortal;
        }

        private void SetEditOrNewUser(UserDataModel userDataModel, PortalConfig portalConfig, CompanyCredentials companyCredentials)
        {
            userDataModel.PortalId = portalConfig.PortalId;

            userDataModel.Email = !string.IsNullOrWhiteSpace(userDataModel.PrefixEmail) && !string.IsNullOrWhiteSpace(userDataModel.DomainCompanySelected)
                                    ? $"{userDataModel.PrefixEmail}{userDataModel.DomainCompanySelected}"
                                    : userDataModel.Email;

            short.TryParse(_encrypService.Decrypt(userDataModel.RolSelected), out var roleid);
            userDataModel.RoleId = roleid;
            userDataModel.DomainEmail = userDataModel.DomainCompanySelected;
            userDataModel.CompanyId = companyCredentials.IdCompany;

            userDataModel.Password = userDataModel.Password.Trim();
        }

        private bool IsValidToAdd()
        {
            CompanyCredentials companyCredentials = SecurityHelper.GetCompanyCredentials();

            var userAccounts = _userService.GetTotalManagersByIdCompany(companyCredentials.IdCompany);

            if (userAccounts == 0)
                return false;

            var companyProduct = _companyProductService.GetByIdCompany(companyCredentials.IdCompany, companyCredentials.PortalId, OfferIntegratorEnum.CompuTrabajo);
            return userAccounts < GetInitialUsersFeature(companyCredentials, companyProduct);
        }

        private CompanyProductFeatureEntity GetFeature(ProductAmbitEnum ambit)
        {
            var companyProduct = _companyProductService.GetByIdCompany(SecurityHelper.GetCompanyCredentials().IdCompany, SecurityHelper.GetCompanyCredentials().PortalId, OfferIntegratorEnum.CompuTrabajo);
            var feature = companyProduct.Features.Where(feat => feat.AmbitId == (short)ambit).FirstOrDefault();
            return feature ?? new CompanyProductFeatureEntity();
        }
        
        private int GetInitialUsersFeature(CompanyCredentials companyCredentials, CompanyProductEntity companyProduct)
        {
            if (companyProduct.GroupId == (short)ProductGroupsEnum.Membership)
            {
                var usersFeature = companyProduct.Features.Where(feat => feat.AmbitId == (short)ProductAmbitEnum.Users).FirstOrDefault();
                return usersFeature != null ? usersFeature.InitialUnits : 0;
            }
            else
            {
                var allCompanyActiveProducts = _companyProductService.GetAllCompanyActiveProducts(companyCredentials.IdCompany, companyCredentials.PortalId).Where(p => p.GroupId != (short)ProductGroupsEnum.Membership);
                if (allCompanyActiveProducts != null)
                {
                    return allCompanyActiveProducts.Where(x => x.Features.Exists(n => n.AmbitId == (short)ProductAmbitEnum.Users)).Sum(x => x.Features.First(n => n.AmbitId == (short)ProductAmbitEnum.Users).InitialUnits);
                }
            }

            return 0;
        }

        private bool ShowContactPhone(PortalConfig portalConfig)
        {
            short idPortal = portalConfig.PortalId;
            EnviromentEnum environment = portalConfig.Enviroment;

            return idPortal != (short)PortalEnum.ComputrabajoMexico &&
                idPortal != (short)PortalEnum.ComputrabajoArgentina &&
                idPortal != (int)PortalEnum.ComputrabajoCostaRica &&
                idPortal != (int)PortalEnum.ComputrabajoGuatemala &&
                idPortal != (int)PortalEnum.ComputrabajoEcuador &&
                idPortal != (int)PortalEnum.ComputrabajoElSalvador;
        }

        private List<SelectListItem> GetUsersOrders(PortalConfig portalConfig)
        {
            var offerOrders = new List<SelectListItem>
            {
                new SelectListItem() { Value = ((int)OrderUsersByEnum.ByNameAsc).ToString(), Text = PageLiteralsHelper.GetLiteral("LIT_NAME_ASC", _pageId, portalConfig) },
                new SelectListItem() { Value = ((int)OrderUsersByEnum.ByNameDesc).ToString(), Text = PageLiteralsHelper.GetLiteral("LIT_NAME_DESC", _pageId, portalConfig)  },
                new SelectListItem() { Value = ((int)OrderUsersByEnum.ByMailAsc).ToString(), Text = PageLiteralsHelper.GetLiteral("LIT_MAIL_ASC", _pageId, portalConfig)  },
                new SelectListItem() { Value = ((int)OrderUsersByEnum.ByMailDesc).ToString(), Text = PageLiteralsHelper.GetLiteral("LIT_MAIL_DESC", _pageId, portalConfig)  },
                new SelectListItem() { Value = ((int)OrderUsersByEnum.ByRoleAsc).ToString(), Text = PageLiteralsHelper.GetLiteral("LIT_ROLE_ASC", _pageId, portalConfig)  },
                new SelectListItem() { Value = ((int)OrderUsersByEnum.ByRoleDesc).ToString(), Text = PageLiteralsHelper.GetLiteral("LIT_ROLE_DESC", _pageId, portalConfig)  },
                new SelectListItem() { Value = ((int)OrderUsersByEnum.ByLastLoginAsc).ToString(), Text = PageLiteralsHelper.GetLiteral("LIT_LASTLOGIN_ASC", _pageId, portalConfig)  },
                new SelectListItem() { Value = ((int)OrderUsersByEnum.ByLastLoginDesc).ToString(), Text = PageLiteralsHelper.GetLiteral("LIT_LASTLOGIN_DESC", _pageId, portalConfig)  }
            };

            return offerOrders;
        }

        [HttpPost]
        public void DeleteMarkedUsers(string utd, string ida)
        {
            try
            {
                if (!SecurityHelper.IsActionPemitedByRoleAndIsNotAdvisorRole(SecurityActionEnum.CompanyUsers)
                    || !int.TryParse(_encrypService.Decrypt(ida), out var idUserToAssignContent))
                {
                    return;
                }

                var users = utd.Split('|');

                foreach (var idUser in users)
                {
                    if (int.TryParse(_encrypService.Decrypt(idUser), out var idUserParsed))
                    {
                        var companyCredentials = SecurityHelper.GetCompanyCredentials();
                        var user = _userService.Get(idUserParsed, true, companyCredentials.PortalId);

                        if (user.RoleId == (int)UserRoleEnum.ADMINISTRADOR_PRINCIPAL)
                        {
                            continue; // El administrador principal no se debe poder eliminar [confirmar]
                        }

                        _userService.DeleteUser(idUserParsed, idUserToAssignContent, companyCredentials.IdCompany);
                    }
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyConfigurationAccountsController -- DeleteMarkedUsers {ex}");
            }
        }

        [HttpPost]
        public JsonResult ExportToExcel(string jsonUsersItems)
        {
            try
            {
                if (!SecurityHelper.IsActionPemitedByRoleAndIsNotAdvisorRole(SecurityActionEnum.CompanyUsers))
                    return new JsonResult();

                var usersListItems = JsonConvert.DeserializeObject<List<UserDataModel>>(jsonUsersItems);
                var portalConfig = _portalConfigurationService.GetPortalConfiguration();

                using (var excelPackage = GetExcelPackage(usersListItems, portalConfig))
                {
                    var id = Guid.NewGuid().ToString();

                    using (MemoryStream memoryStream = new MemoryStream())
                    {
                        excelPackage.SaveAs(memoryStream);
                        memoryStream.Position = 0;
                        TempData[id] = memoryStream.ToArray();
                    }
                    return new JsonResult()
                    {
                        Data = new { FileGuid = id, FileName = "UsersList.xlsx" }
                    };
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyConfigurationAccountsController -- ExportToExcel {ex}");
                return new JsonResult();
            }
        }

        public ActionResult Download(string fileGuid, string fileName)
        {
            try
            {
                if (TempData[fileGuid] != null)
                {
                    byte[] data = TempData[fileGuid] as byte[];
                    return File(data, "application/vnd.ms-excel", fileName);
                }
                else
                    return new EmptyResult();
            }
            catch (Exception ex)
            {
                Trace.TraceError($"SherlockAnalyticsReportsTeamController Download {ex}");
                return new EmptyResult();
            }
        }

        private ExcelPackage GetExcelPackage(List<UserDataModel> usersListItems, PortalConfig portalConfig)
        {
            var excelPackage = new ExcelPackage();

            ExcelWorksheet excelWorkSheet = excelPackage.Workbook.Worksheets.Add("Hoja");

            excelWorkSheet.Cells[1, 1].Value = PageLiteralsHelper.GetLiteral("LIT_EXCELHEADER_CONTACTNAME", _pageId, portalConfig);
            excelWorkSheet.Cells[1, 2].Value = PageLiteralsHelper.GetLiteral("LIT_EXCELHEADER_EMAIL", _pageId, portalConfig);
            excelWorkSheet.Cells[1, 3].Value = PageLiteralsHelper.GetLiteral("LIT_EXCELHEADER_ROL", _pageId, portalConfig);
            excelWorkSheet.Cells[1, 4].Value = PageLiteralsHelper.GetLiteral("LIT_EXCELHEADER_CREATEDON", _pageId, portalConfig);
            excelWorkSheet.Cells[1, 5].Value = PageLiteralsHelper.GetLiteral("LIT_EXCELHEADER_LASTLOGIN", _pageId, portalConfig);

            var dictionaryRoles = _dictionaryService.GetDictionary(Dictionaries.Consumer.Enums.DictionaryEnum.USER_ROLES_CT, portalConfig.PortalId);

            List<Tuple<string, string, string, string, string>> exportFormatedList = new List<Tuple<string, string, string, string, string>>();
            foreach (var user in usersListItems)
            {
                string roleLiteral = string.Empty;
                if (dictionaryRoles.ContainsKey(user.RoleId.ToString()))
                {
                    roleLiteral = dictionaryRoles[user.RoleId.ToString()];
                }

                exportFormatedList.Add(new Tuple<string, string, string, string, string>(user.ContactName, user.Email, roleLiteral, user.CreatedOn.ToString("dd/MM/yyyy"), user.LastLoginOn.ToString("dd/MM/yyyy")));
            }

            excelWorkSheet.Cells["A2"].LoadFromCollection(exportFormatedList
                .Select(r => new
                {
                    r.Item1,
                    r.Item2,
                    r.Item3,
                    r.Item4,
                    r.Item5
                }));

            using (ExcelRange excelRange = excelWorkSheet.Cells["A1:E1"])
            {
                excelRange.Style.Font.Bold = true;
                excelRange.Style.Fill.PatternType = ExcelFillStyle.Solid;
                excelRange.Style.Fill.BackgroundColor.SetColor(Color.FromArgb(79, 129, 189));
                excelRange.Style.Font.Color.SetColor(Color.White);
            }

            return excelPackage;
        }

        private void AddStackAndUpdateRolInOffers(UserEntity userEntity, PortalConfig portalConfig)
        {
            var index = 0;
            var blocksize = portalConfig.BlockSizeDefault;
            var offersByIduserInBlock = _userService.FindAllIdOffersByUserBulk((int)userEntity.Id, userEntity.PortalId, index, blocksize);
            while (offersByIduserInBlock.Any())
            {
                _userService.PublicOffersToStack(offersByIduserInBlock, userEntity.PortalId);
                _offerService.UpdateIdRolCreatedBy(offersByIduserInBlock, userEntity.PortalId, userEntity.RoleId);
                index = index + blocksize;
                offersByIduserInBlock = _userService.FindAllIdOffersByUserBulk((int)userEntity.Id, userEntity.PortalId, index, blocksize);
            };
        }

        private int GetInitialUsers(int basicFeatureInitialUsers)
        {
            CompanyCredentials companyCredentials = SecurityHelper.GetCompanyCredentials();
            var portalConfig = _portalConfigurationService.GetPortalConfiguration();

            var initialUsers = basicFeatureInitialUsers;

            if (portalConfig.AEPortalConfig.ActivatePrimeProduct && companyCredentials.TestAB == TestABEnum.A)
            {
                var allCompanyActiveProducts = _companyProductService.GetAllCompanyActiveProducts(companyCredentials.IdCompany, companyCredentials.PortalId);
                if (!allCompanyActiveProducts.Exists(x => x.GroupId == (short)ProductGroupsEnum.Membership) &&
                    allCompanyActiveProducts.Exists(x => x.SubGroupId == (short)ProductSubGroupsEnum.Prime))
                {
                    var primeProduct = allCompanyActiveProducts.Find(x => x.SubGroupId == (short)ProductSubGroupsEnum.Prime);
                    if (primeProduct != null)
                    {
                        var primeProductUsersFeature = primeProduct.Features.Find(x => x.AmbitId == (short)ProductAmbitEnum.Users);
                        if (primeProductUsersFeature != null)
                        {
                            initialUsers += primeProductUsersFeature.InitialUnits;
                        }
                    }
                }
            }
            return initialUsers;
        }
    }
}