using AutoMapper;
using Redarbor.Company.Contracts.ServiceLibrary;
using Redarbor.Company.WebUI.Helpers;
using Redarbor.Company.WebUI.Models.Company.Configuration;
using Redarbor.Company.WebUI.Models.Company.Product;
using Redarbor.Company.WebUI.Security;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Core.Contracts.ServiceLibrary;
using Redarbor.Core.Shared.Contracts.ServiceLibrary;
using Redarbor.Invoice.Contracts.ServiceLibrary;
using Redarbor.Master.Contracts.ServiceLibrary;
using Redarbor.Master.Entities.Enums;
using Redarbor.Master.Entities.Invoice;
using Redarbor.Master.Entities.Product;
using Redarbor.Master.Entities.PurchaseOperation;
using Redarbor.Master.Entities.Searchs;
using Redarbor.Offer.Contracts.ServiceLibrary;
using Redarbor.Products.Contracts.ServiceLibrary;
using Redarbor.ProductSubGroups.Contracts.ServiceLibrary;
using Redarbor.ProProduct.Contracts.ServiceLibrary;
using Redarbor.PurchaseOperation.Contracts.ServiceLibrary;
using Redarbor.Web.UI.Library.Exceptions;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Web.Mvc;

namespace Redarbor.Company.WebUI.Controllers
{
    [RoutePrefix("Company/Invoices")]
    [RedarborAuthorize]
    public class CompanyInvoicesController : CompanyBaseController
    {
        private readonly IDtInvoiceService DtInvoiceService;
        private readonly IDtInvoiceService IDtInvoiceService;
        private readonly IEncryptionService EncryptionService;
        private readonly IInvoiceUnifiedService InvoiceService;
        private readonly IAmazonFilesService AmazonFilesService;
        private readonly IInvoiceEntityService InvoiceEntityService;
        private readonly IPurchaseOperationService PurchaseOperationService;
        private readonly IProductsService _productsService;

        public CompanyInvoicesController(
            IProductService productService,
            IProductSubGroupsService productSubGroupsService,
            IOfferService offerService,
            ICompanyProductService companyProductService,
            ICompanyService companyService,
            IPortalConfigurationService portalConfigurationService,
            IDictionaryService dictionaryService,
            ISecurityService securityService,
            IExceptionPublisherService exceptionPublisherService,
            IInvoiceUnifiedService invoiceService,
            IDtInvoiceService dtInvoiceService,
            IAmazonFilesService amazonFilesService,
            IEncryptionService encryptionService,
            IPurchaseOperationService purchaseOperationService,
            IInvoiceEntityService invoiceEntityService,
            IDtInvoiceService iDtinvoiceService, IProductsService productsService) : base(productService, productSubGroupsService, offerService, companyProductService, companyService, portalConfigurationService, dictionaryService, securityService, exceptionPublisherService)
        {
            InvoiceService = invoiceService;
            DtInvoiceService = dtInvoiceService;
            AmazonFilesService = amazonFilesService;
            EncryptionService = encryptionService;
            PurchaseOperationService = purchaseOperationService;
            InvoiceEntityService = invoiceEntityService;
            IDtInvoiceService = iDtinvoiceService;
            _productsService = productsService;
        }

        [Route]
        public ActionResult Index()
        {
            if (!SecurityHelper.IsActionPemitedByRoleAndIsNotAdvisorRole(SecurityActionEnum.CompanyInvoice))
                return RedirectToAction("Index", "Company");

            var portalConfig = PortalConfigurationService.GetPortalConfiguration();
            var companyCredentials = SecurityHelper.GetCompanyCredentials();
            var company = CompanyService.GetByPK(new CompanySearchSpecifications(companyCredentials.PortalId) { Id = companyCredentials.IdCompany });

            if (checkBlocked(portalConfig.company_block, company.Blocked, company.LastDateBlockedUpdate, portalConfig.DaysBlock))
                return RedirectToAction("Blocked", "Account");

            var invoiceDataModel = Mapper.Map<List<InvoiceUnifiedEntity>, List<InvoiceUnifiedDataModel>>(InvoiceService.GetUnifiedInvoices(company.Id, portalConfig.PortalId));

            FillInvoiceAddtionalInfo(invoiceDataModel);

            return View(invoiceDataModel);
        }

        [Route("DownloadInvoice")]
        public ActionResult DownloadInvoice(string inv, string invactv, string tpi, string pathinv)
        {
            try
            {
                var invoicePath = EncryptionService.Decrypt(pathinv);
                var fileName = invoicePath.Split('/').Last();
                var companyCredentials = SecurityHelper.GetCompanyCredentials();
                Int32.TryParse(EncryptionService.Decrypt(inv), out var idDtInvoice);

                if (idDtInvoice != 0)
                {
                    var invoiceDt = DtInvoiceService.GetByPK(idDtInvoice, companyCredentials.PortalId);
                    if (invoiceDt.IdCompany != companyCredentials.IdCompany)
                        return RedirectToAction("Index");
                }

                using (var fileContent = AmazonFilesService.GetInvoiceFile(invoicePath))
                {
                    return fileContent == null
                        ? (ActionResult)RedirectToAction("Index")
                        : File(fileContent.ToArray(), System.Net.Mime.MediaTypeNames.Application.Octet, fileName);
                }
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyInvoicesController DownloadInvoice {ex}");
                ExceptionPublisherService.Publish(ex, "CompanyInvoicesController", "DownloadInvoice");
                return null;
            }
        }

        [Route("Invoice")]
        public ActionResult Invoice(string inv)
        {
            try
            {
                if (!SecurityHelper.IsActionPemitedByRoleAndIsNotAdvisorRole(SecurityActionEnum.CompanyInvoice))
                    return RedirectToAction("Index", "Company");

                var portalConfig = PortalConfigurationService.GetPortalConfiguration();
                var companyCredentials = SecurityHelper.GetCompanyCredentials();
                var company = CompanyService.GetByPK(new CompanySearchSpecifications(companyCredentials.PortalId) { Id = companyCredentials.IdCompany });

                if (checkBlocked(portalConfig.company_block, company.Blocked, company.LastDateBlockedUpdate, portalConfig.DaysBlock))
                    return RedirectToAction("Blocked", "Account");

                var dtInvoiceDataModel = Mapper.Map<DtInvoiceEntity, DtInvoiceDataModel>(
                    IDtInvoiceService.GetByPK(inv, portalConfig.PortalId));
                var purchaseOperationDataModel = Mapper.Map<PurchaseOperationEntity, PurchaseOperationDataModel>(
                    PurchaseOperationService.GetByPK(dtInvoiceDataModel.IdPurchaseOperation));

                if (companyCredentials.IdCompany != dtInvoiceDataModel.IdCompany)
                    return RedirectToAction("Index", "CompanyConfiguration");

                var configurationInvoiceDataModel = string.IsNullOrEmpty(dtInvoiceDataModel.GeneratedInvoice)
                    ? new ConfigurationInvoiceDataModel()
                    {
                        DtInvoiceDataModel = dtInvoiceDataModel,
                        InvoiceDataModel = Mapper.Map<InvoiceEntity, InvoiceDataModel>(InvoiceEntityService.GetByPK(dtInvoiceDataModel.IdInvoiceentity, dtInvoiceDataModel.IdPortal)),
                        PurchaseOperationDataModel = purchaseOperationDataModel,
                        ProductDataModel = Mapper.Map<ProductEntity, ProductDataModel>(
                            _productsService.Get(new ProductSearchSpecifications(portalConfig.PortalId)
                            {
                                Id = purchaseOperationDataModel.Idproduct,
                                TemporalityId = purchaseOperationDataModel.Idtemporality
                            })),
                        CompanyProductDataModel = Mapper.Map<CompanyProductEntity, CompanyProductDataModel>(
                            CompanyProductService.GetByCompanyProductId(purchaseOperationDataModel.Idproductcontract, portalConfig.PortalId, dtInvoiceDataModel.IdCompany))
                    }
                    : new ConfigurationInvoiceDataModel()
                    {
                        DtInvoiceDataModel = dtInvoiceDataModel
                    };

                return View(configurationInvoiceDataModel);
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyInvoicesController Index {ex}");
                ExceptionPublisherService.Publish(ex, "CompanyInvoicesController", "Index");
                return RedirectToAction("Index", "Home");
            }
        }

        private void FillInvoiceAddtionalInfo(List<InvoiceUnifiedDataModel> invoices)
        {
            foreach (var invoice in invoices)
            {
                if (!invoice.IsNavisionCRM)
                {
                    invoice.InvoiceUrl = Url.Action("Invoice", new { inv = EncryptionService.Encrypt(invoice.iddtinvoice.ToString()) });
                }
                else
                {
                    invoice.InvoiceUrl = Url.Action("DownloadInvoice",
                        new
                        {
                            inv = EncryptionService.Encrypt(invoice.iddtinvoice.ToString()),
                            invactv = EncryptionService.Encrypt(invoice.idactivity.ToString()),
                            tpi = EncryptionService.Encrypt(invoice.typeid.ToString()),
                            pathinv = EncryptionService.Encrypt(invoice.pdfpath)
                        });
                }
            }
        }
    }
}