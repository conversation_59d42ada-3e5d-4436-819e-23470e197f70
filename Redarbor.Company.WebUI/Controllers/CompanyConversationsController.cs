using Amazon.S3.Model;
using AutoMapper;
using Common.PaymentLibrary.Enums;
using Redarbor.Candidate.Contracts.ServiceLibrary;
using Redarbor.Common.Entities.Configuration;
using Redarbor.Common.Entities.Constants;
using Redarbor.Common.Entities.Enums;
using Redarbor.Company.Contracts.ServiceLibrary;
using Redarbor.Company.WebUI.Enums;
using Redarbor.Company.WebUI.Helpers;
using Redarbor.Company.WebUI.Models.Company.Candidate;
using Redarbor.Company.WebUI.Models.Company.Conversations;
using Redarbor.Company.WebUI.Models.Company.Home;
using Redarbor.Company.WebUI.Models.Company.Match;
using Redarbor.Company.WebUI.Models.Company.Offer;
using Redarbor.Company.WebUI.Models.Company.Shared;
using Redarbor.Company.WebUI.Security;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Core.Contracts.ServiceLibrary;
using Redarbor.Core.Contracts.ServiceLibrary.Enums;
using Redarbor.Core.Counter.Contracts.ServiceLibrary;
using Redarbor.Core.Kpi.Contracts.ServiceLibrary;
using Redarbor.Core.Mailing.Contracts.ServiceLibrary;
using Redarbor.Core.Shared.Contracts.ServiceLibrary;
using Redarbor.Dictionaries.Consumer.Enums;
using Redarbor.Extensions.Library.Extensions;
using Redarbor.Master.Contracts.ServiceLibrary;
using Redarbor.Master.Contracts.ServiceLibrary.DTO;
using Redarbor.Master.Entities.Candidate;
using Redarbor.Master.Entities.Enums;
using Redarbor.Master.Entities.Mailing;
using Redarbor.Master.Entities.Match;
using Redarbor.Master.Entities.Message;
using Redarbor.Master.Entities.Offers;
using Redarbor.Master.Entities.Product;
using Redarbor.Master.Entities.PurchaseOperation;
using Redarbor.Master.Entities.Searchs;
using Redarbor.Match.Contracts.ServiceLibrary;
using Redarbor.Message.Jobads.Chat.Impl.ServiceLibrary;
using Redarbor.Offer.Contracts.ServiceLibrary;
using Redarbor.Offer.Contracts.ServiceLibrary.DTO;
using Redarbor.Products.Contracts.ServiceLibrary;
using Redarbor.Products.Contracts.ServiceLibrary.DTO;
using Redarbor.ProductSubGroups.Contracts.ServiceLibrary;
using Redarbor.PurchaseOperation.Contracts.ServiceLibrary;
using Redarbor.Web.UI.Library.Exceptions;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing.Printing;
using System.Linq;
using System.Net;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Web.Mvc;

namespace Redarbor.Company.WebUI.Controllers
{
    [RoutePrefix("Company/Conversations")]
    [RedarborAuthorize]
    public class CompanyConversationsController : CompanyBaseController
    {
        short PageId = (short)PageEnum.Messages;

        private readonly IEncryptionService _encryptionService;
        private readonly ICandidateService _candidateService;
        private readonly IMatchService _matchService;
        private readonly IRequestDeviceRecorder _requestDeviceRecorder;
        private readonly ICompanyCountersService _companyCountersService;
        private readonly IMessageJobadsChatService _messageJobdsChatService;
        private readonly IMailingService _mailingService;
        private readonly ISecurityOfferService _securityOfferService;
        private const int PAGE_SIZE_CONVERSATION_DEFAULT = 10;
        private const int PAGE_SIZE_CONVERSATION_HEADER_DEFAULT = 5;
        private const int PAGE_SIZE_MESSAGE_DEFAULT = 25;
        const int NO_FREEMIUM_VIEWS_RESULTS = -1;
        const int PAGE_PACK_CART = (int)PageEnum.PackCart;
        const string KEY_BACK_URL = "KEY_BACK_URL_CHAT_FREEMIUM";
        const string PARTIAL_CONVERSATION_BOX_VIEW_NAME = "Conversation/_ConversationBoxMessages";
        const string PARTIAL_CONVERSATION_POP_UP_VIEW_NAME = "PopUps/Conversation/_ConversationBoxMessages";
        const string PARTIAL_MESSAGES_VIEW_NAME = "Conversation/_ConversationMessages";
        const string PARTIAL_MESSAGES_POP_UP_VIEW_NAME = "PopUps/Conversation/_ConversationMessages";
        const string PARTIAL_MESSAGE_PAGE_VIEW_NAME = "Conversation/_ConversationBoxMessagesByPage";
        const string PARTIAL_MESSAGE_PAGE_POP_UP_VIEW_NAME = "PopUps/Conversation/_ConversationBoxMessagesByPage";
        
        const string END_CONVERSATION = "end";
        const int MINIMUM_PAGE_SCROLL_UP = 2;

        private readonly IFreemiumOfferContactsService _freemiumOfferContactsService;
        private readonly IMultiPurchaseOperationService _multipurchaseOperationService;

        public CompanyConversationsController(IProductService productService,
            IProductSubGroupsService productSubGroupsService,
            IOfferService offerService,
            ICompanyProductService companyProductService,
            ICompanyService companyService,
            IPortalConfigurationService portalConfigurationService,
            IDictionaryService dictionaryService,
            IEncryptionService encryptionService,
            ICandidateService candidateService,
            IMatchService matchService,
            IRequestDeviceRecorder requestDeviceRecorder,
            ICompanyCountersService companyCountersService,
            ISecurityService securityService,
            IExceptionPublisherService exceptionPublisherService,
            IMailingService mailingService, IMessageJobadsChatService messageJobdsChatService, ISecurityOfferService securityOfferService,
            IFreemiumOfferContactsService freemiumOfferContactsService,
            IMultiPurchaseOperationService multipurchaseOperationService
            ) : base(productService, productSubGroupsService, offerService, companyProductService, companyService, portalConfigurationService, dictionaryService, securityService, exceptionPublisherService)
        {
            _encryptionService = encryptionService;
            _candidateService = candidateService;
            _matchService = matchService;
            _requestDeviceRecorder = requestDeviceRecorder;
            _companyCountersService = companyCountersService;
            _mailingService = mailingService;
            _messageJobdsChatService = messageJobdsChatService;
            _securityOfferService = securityOfferService;
            _freemiumOfferContactsService = freemiumOfferContactsService;
            _multipurchaseOperationService = multipurchaseOperationService;
        }

        #region "private methods"

        private async Task<List<ConversationDataModel>> GetConversationsAsync(CompanyCredentials companyCredentials, PortalConfig portalConfig)
        {
            return Mapper.Map<List<ConversationEntity>, List<ConversationDataModel>>(await _messageJobdsChatService.GetConversationsAsync(new ConversationFilter
            {
                IdCompany = companyCredentials.IdCompany,
                IdPortal = portalConfig.PortalId,
                IdUserCompany = companyCredentials.UserId,
                UserType = (short)UserTypeEnum.Company,
                ConversationFilterEnum = ConversationFilterEnum.CompanyConversations
            }, portalConfig.idapp, new PagerFilter() { PageNumber = 1, PageSize = PAGE_SIZE_CONVERSATION_DEFAULT }));
        }

        private async Task<List<ConversationDataModel>> HeaderConversationsAsync(CompanyCredentials companyCredentials, PortalConfig portalConfig, bool hasNewMessages)
        {
            return Mapper.Map<List<ConversationEntity>, List<ConversationDataModel>>(await _messageJobdsChatService.GetConversationsWithCacheAsync(new ConversationFilter
            {
                IdCompany = companyCredentials.IdCompany,
                IdPortal = portalConfig.PortalId,
                IdUserCompany = companyCredentials.UserId,
                UserType = (short)UserTypeEnum.Company,
                ConversationFilterEnum = ConversationFilterEnum.CompanyConversations
            }, portalConfig.idapp, new PagerFilter() { PageNumber = 1, PageSize = PAGE_SIZE_CONVERSATION_HEADER_DEFAULT }, hasNewMessages));
        }

        private List<MatchDataModel> FillDataMatches(List<ConversationDataModel> conversations)
        {
            //TODO MQC W25484 - Review, si es necesario porque se debería enviar a la cola si no existe el match o esta eliminado.. OJO
            List<MatchDataModel> list = new List<MatchDataModel>();
            Dictionary<int, int> toDelete = new Dictionary<int, int>();

            foreach (var item in conversations.Select(n => new { n.IdOfferCT, n.IdCv }).Distinct())
            {
                var match = Mapper.Map<MatchEntity, MatchDataModel>((MatchEntity)_matchService.GetByIdOfferAndIdCv(item.IdOfferCT, item.IdCv));
                if (match.Id != -1)
                    list.Add(match);
                else if (!toDelete.ContainsKey(item.IdOfferCT))
                    toDelete.Add(item.IdOfferCT, item.IdCv);
            }

            if (toDelete.Any())
            {
                foreach (var item in toDelete)
                {
                    conversations.RemoveAll(n => n.IdOfferCT == item.Key && n.IdCv == item.Value);
                }
            }

            return list;
        }

        private List<CandidateDataModel> FillDataDandidates(List<ConversationDataModel> conversations, short portalId)
        {
            List<CandidateDataModel> list = new List<CandidateDataModel>();
            foreach (var candidateId in conversations.Select(n => n.IdCandidate).Distinct().ToList())
            {
                list.Add(Mapper.Map<CandidateEntity, CandidateDataModel>(_candidateService.GetCandidateById(candidateId, portalId)));
            }
            return list;
        }

        //TODO MQC W25484 revisar esta función se puede eliminar cuando todo este actualizado al nuevo chat jobads
        private List<OfferDataModel> FillDataOffer(List<ConversationDataModel> conversations)
        {
            List<OfferDataModel> list = new List<OfferDataModel>();
            var portalId = SecurityHelper.GetCompanyCredentials().PortalId;
            //TODO MQC 2024 - OfferService.GetByPk obtienes todo tipo de oferta, activa o no, por el GetByPK si da un Id.
            foreach (var offerId in conversations.Select(n => n.IdOffer).Distinct().ToList())
            {
                var offer = Mapper.Map<OfferEntity, OfferDataModel>(OfferService.GetByPk(offerId, portalId));
                if (offer != null)
                {
                    if (offer.idofferCt > 0)
                        list.Add(offer);
                }
            }
            return list;
        }

        private void GenerateNotificationPendingsMessages(int idCurrentCv, int idCandidate, short idPortal, int idCurrentOffer, int idCompany, int idUser)
        {
            var candidate = _candidateService.GetCandidateRead(idCurrentCv, idPortal, idCandidate, idCompany);

            var emailNotification = new MailingNotification
            {
                PortalId = idPortal,
                OfferId = idCurrentOffer,
                CompanyId = idCompany,
                UserId = idUser,
                Name = candidate.User.ContactName,
                CandidateId = idCandidate,
                EmailTo = candidate.User.Email,
                NotificationTypeId = (int)NotificationTypeEnum.Company,
                NotificationStatus = (int)StateNotificationEnum.PENDIENTE_PROCESAR,
                NewsletterId = (int)NewsLetterEnum.NotificationChat
            };

            _mailingService.Add(emailNotification);
        }

        private async Task<List<ConversationEntity>> GetConversationsActiveAsync(short portalId, int companyId, long userId, int idApp)
        {
            ConversationFilter filter = new ConversationFilter();
            filter.IdPortal = portalId;
            filter.IdCompany = companyId;
            filter.IdUserCompany = userId;
            filter.UserType = (int)UserTypeEnum.Company;


            return await _messageJobdsChatService.GetConversationsStatusActiveAsync(filter, idApp, new PagerFilter() { PageNumber = 1, PageSize = PAGE_SIZE_CONVERSATION_DEFAULT });
        }

        private async Task CreateMessageForConversationAsync(CompanyCredentials companyCredentials, PortalConfig portalConfig, MatchEntity match, int idOffer, long idConver, string message, List<long> Convers)
        {
            var messageEntity = new MessageEntity()
            {
                IdConversation = idConver,
                IdPortal = portalConfig.PortalId,
                IdUserCompany = (int)companyCredentials.UserId,
                MessageBody = message,
                UserType = (int)UserTypeEnum.Company
            };


            if (await _messageJobdsChatService.AddMessageAsync(messageEntity, portalConfig.idapp))
            {
                Convers.Remove(idConver);
            }
        }

        private async Task<long> CreateOrGetConversationAsync(CompanyCredentials companyCredentials, PortalConfig portalConfig, MatchEntity Match, int offerId)
        {
            ConversationEntity conversation = new ConversationEntity
            {
                IdCompany = companyCredentials.IdCompany,
                IdPortal = portalConfig.PortalId,
                IdApp = portalConfig.idapp,
                IdOffer = offerId,
                IdCandidate = Match.CandidateId,
                IdCv = Match.CurriculumId,
                IdUserCompany = companyCredentials.UserId,
                CountPendingCandidate = 0,
                CountPendingCompany = 0,
                StatusConversation = (short)StatusEnum.Pending,
                UserType = (short)UserTypeEnum.Company,
                IdConversationType = (short)TypeConverEnum.Bidireccional
            };

            conversation.ConversationName = OfferService.GetOfferTitle(offerId, portalConfig.PortalId);
            conversation.IdUserCandidate = _candidateService.GetIdUserCandidateById(Match.CandidateId, Match.PortalId);

            var converEncrypted = await _messageJobdsChatService.AddConversationAsync(conversation);

            if (!string.IsNullOrEmpty(converEncrypted))
            {
                long.TryParse(_encryptionService.Decrypt(converEncrypted), out var idConver);               
                return idConver;
            }
            return 0;
        }

        private async Task<int> CreateConversationUnidirectionalAsync(CompanyCredentials companyCredentials, PortalConfig portalConfig, List<long> idsMatchesList, MatchEntity Match, int idOffer)
        {
            ConversationEntity conversation = new ConversationEntity
            {
                IdCompany = companyCredentials.IdCompany,
                IdPortal = portalConfig.PortalId,
                IdApp = portalConfig.idapp,
                IdOffer = idOffer,
                IdCandidate = Match.CandidateId,
                IdCv = Match.CurriculumId,
                IdUserCompany = companyCredentials.UserId,
                CountPendingCandidate = 0,
                CountPendingCompany = 0,
                StatusConversation = (short)StatusEnum.Pending,
                UserType = (short)UserTypeEnum.Company,
                IdConversationType = (short)TypeConverEnum.Unidireccional
            };

            conversation.ConversationName = OfferService.GetOfferTitle(idOffer, portalConfig.PortalId);
            conversation.IdUserCandidate = _candidateService.GetIdUserCandidateById(Match.CandidateId, Match.PortalId);

            var converEncrypted = await _messageJobdsChatService.AddConversationAsync(conversation);

            if (!string.IsNullOrEmpty(converEncrypted))
            {
                int.TryParse(_encryptionService.Decrypt(converEncrypted), out var idConver);
                idsMatchesList.Add(idConver);
                return idConver;
            }

            return 0;
        }

        private async Task CheckAndUpdateNewMessagesAsync(PortalConfig portalConfig, ConversationDataModel conversation)
        {
            if (conversation is null) return;
            if (conversation.CountPendingCompany <= 0) return;
            if (conversation.IdConversation <= 0) return;

            await _messageJobdsChatService.UpdateMessagesToReadAsync(new ConversationFilter()
            {
                UserType = (int)UserTypeEnum.Company,
                IdConversation = conversation.IdConversation,
                IdCompany = conversation.IdCompany,
                IdPortal = portalConfig.PortalId,
                IdUserCompany = conversation.IdUserCompany,
            }, portalConfig.idapp);

            conversation.CountPendingCompany = 0;
        }

        private async Task<List<MessageDataModel>> FillConversationMessagesAsync(List<ConversationDataModel> conversations, CompanyCredentials companyCredentials, PortalConfig portalConfig, string oi, string idconver)
        {
            var firstConver = conversations.FirstOrDefault()?.IdConversation ?? 0;
            var idOffer = 0;

            if (!string.IsNullOrEmpty(oi))
            {
                int.TryParse(_encryptionService.Decrypt(oi), out idOffer);
                firstConver = conversations.FirstOrDefault(n => n.IdOffer == idOffer)?.IdConversation ?? 0;
            }

            if (!string.IsNullOrEmpty(idconver))
            {
                int.TryParse(_encryptionService.Decrypt(idconver), out firstConver);
            }

            //Hay paginación ordenado por los ultimos mensajes
            var messages = Mapper.Map<List<MessageEntity>, List<MessageDataModel>>(await _messageJobdsChatService.GetMessagesAsync(new MessageFilter
            {
                IdPortal = portalConfig.PortalId,
                IdConversation = firstConver,
                ConversationFilterEnum = ConversationFilterEnum.CompanyConversations,
            }, portalConfig.idapp));

            messages = GetMessageImages(messages, companyCredentials, portalConfig);

            return !messages.Any()
                ? new List<MessageDataModel>()
                : messages;
        }

        private PopUpDataModel GetDeleteConversationPopUp(PortalConfig portalConfig)
        {
            return new PopUpDataModel()
            {
                Title = PageLiteralsHelper.GetLiteral("REMOVING_CONVERSATION", PageId, portalConfig),
                Message = PageLiteralsHelper.GetLiteral("REMOVING_CONVERSATION_QUESTION", PageId, portalConfig),
                TitleBtnOk = PageLiteralsHelper.GetLiteral("BTN_ACEPTAR", PageId, portalConfig),
                TitleBtnKo = PageLiteralsHelper.GetLiteral("BTN_CANCELAR", PageId, portalConfig),
                HasInput = false,
                HasButtonOk = true
            };
        }

        private void FillOfferDataByConversationName(HomeConversationsDataModel HomeConversationsDataModel, List<ConversationDataModel> conversationsModel)
        {
            conversationsModel.ForEach(conversation =>
            {
                HomeConversationsDataModel.OfferNames.Add(new SelectListItem()
                {
                    Value = conversation.IdOfferEncrypted,
                    Text = conversation.ConversationName,
                    Selected = HomeConversationsDataModel.OfferIdEncryptedParameter == conversation.IdOfferEncrypted
                });
                HomeConversationsDataModel.SelectedOfferName = HomeConversationsDataModel.OfferIdEncryptedParameter;
            });
        }

        private void FillConverData(List<ConversationDataModel> conversations, List<OfferDataModel> listOffers, List<CandidateDataModel> listCandidates, List<MatchDataModel> listMatches)
        {
            //TODO MQC W25484 - Revisar si es necesario, ya que no debería haber conversaciones sin candidato o oferta
            var excludedConversationsList = new List<ConversationDataModel>();

            foreach (var conversation in conversations)
            {
                var candidate = listCandidates.FirstOrDefault(c => c.IdCandidate == conversation.IdCandidate);
                var offer = listOffers.FirstOrDefault(c => c.idoffer == conversation.IdOffer);
                var match = listMatches.FirstOrDefault(c => c.CandidateId == conversation.IdCandidate && c.JobOfferId == conversation.IdOfferCT);

                if (candidate == null)
                    excludedConversationsList.Add(conversations.Single(r => r.IdConversation == conversation.IdConversation));

                conversation.Candidate = candidate ?? new CandidateDataModel();
                conversation.Offer = offer ?? new OfferDataModel();
                conversation.Match = match ?? new MatchDataModel();
            }

            conversations.RemoveAll(c => excludedConversationsList.Contains(c));
        }


        private List<MessageDataModel> GetMessageImages(List<MessageDataModel> messages, CompanyCredentials companyCredentials, PortalConfig portalConfig)
        {
            foreach (var message in messages)
            {
                switch (message.UserType)
                {
                    case (short)UserTypeEnum.Company:
                        message.Image = CompanyHelper.GetLogoPath(companyCredentials);
                        break;

                    case (short)UserTypeEnum.Candidate:
                        message.Image = _candidateService.GetCandidatePhoto(message.IdCandidate, portalConfig);
                        break;
                }
            }

            return messages;
        }

        private CompanyProductEntity GetCompanyOfferProduct(CompanyCredentials companyCredentials, string idOfferEncrypted)
        {
            var companyProduct = CompanyProductService.GetByIdCompany(companyCredentials.IdCompany, companyCredentials.PortalId, OfferIntegratorEnum.CompuTrabajo);
            var offer = OfferService.GetByPk(idOfferEncrypted, companyCredentials.PortalId);
            CompanyProductEntity offerProduct;

            if (companyProduct.GroupId != (short)ProductGroupsEnum.Membership)
            {
                offerProduct = CompanyProductService.GetByCompanyProductId(offer.Integrations.FirstOrDefault()?.idcompanyproduct ?? 0, offer.idportal, offer.idcompany);
            }
            else
            {
                offerProduct = companyProduct;
            }

            return offerProduct;
        }

        private bool HasNewFreemiumChat(PortalConfig portalConfig, CompanyProductEntity offerProduct)
        {
            return portalConfig.AEPortalConfig.EnableProgressiveChatFreemium
             && CompanyProductService.HasActiveFeatureByProduct(new SearchCompanyProductsByCompanyDTO(offerProduct.IdCompany, ProductAmbitEnum.ChatContactFreemium, portalConfig.PortalId, ProductEnum.FreemiumService));
        }

        private bool ShowPromoFreemium(PortalConfig portalConfig, CompanyCredentials companyCredentials, string idOfferEncrypted)
        {
            var offerProduct = GetCompanyOfferProduct(companyCredentials, idOfferEncrypted);
            return HasNewFreemiumChat(portalConfig, offerProduct) && (offerProduct.GroupId == (int)ProductGroupsEnum.Freemium);
        }

        private bool CandSendMessages(CompanyCredentials companyCredentials, ConversationDataModel conversation)
        {
            if (conversation.IdConversation <= 0) return false;

            var offerProduct = GetCompanyOfferProduct(companyCredentials, conversation.IdOfferEncrypted);
            //var featureLimitMessages = offerProduct.FeaturestAny(n => n.AmbitId == (short)ProductAmbitEnum.MessageLimit);

            //El estado 3 en Jobads es Archivada -Concepto de Finalizada, resultado que no se puede enviar mensajes
            if (conversation.StatusConversation == (short)ConversationStatusEnum.Expired) return false;

            return true;
        }

        private string MessageInformacion(PortalConfig portalConfig, int PageId)
        {
            //TODO MQC W25546 - Debería haber casuísticas de reasons
            return PageLiteralsHelper.GetLiteral("LIT_CONVER_FINISHED", PageId, portalConfig);
            //TODO MQC W25546 - Add literal
            //Este texto que hay debajo nunca existira, porque será una partial fake
            //Cuando esté tirado PageLiteralsHelper.GetLiteral("CHAT_FREEMIUM_MESSAGE", PageId, portalConfig), o ver que tipo de literal
            //return "Alcanzaste el límite de contactos <strong>vía chat</strong> (limitado a las primeras 15 personas inscritas). \r\n        Mejora la oferta para poder contactar por teléfono y email con este candidato y todos los demás inscritos en la oferta.";
        }

        private ConversationDataModel GetCurrentConversation(List<ConversationDataModel> conversations, string idconver, ChatNavigationPointEnum pointEnum)
        {
            if (pointEnum.Equals(ChatNavigationPointEnum.OfferList) || pointEnum.Equals(ChatNavigationPointEnum.None))
            {
                return new ConversationDataModel();
            }

            var conversation = conversations.Any(c => c.IdConversationEncrypted == idconver) ?
                                             conversations.FirstOrDefault(c => c.IdConversationEncrypted == idconver) : null;


            if (conversation != null && conversation.IdConversation > 0 && conversation.IdOffer > 0)
            {
                conversation.OfferConversationDetail = GetOfferConversationDetail(conversation.IdOffer);
            }

            return conversation ?? new ConversationDataModel();
        }

        #endregion "private methods"

        [Route]
        public async Task<ActionResult> Index(IndexConversationsRequestDataModel inputModel)
        {
            inputModel = inputModel == null ? new IndexConversationsRequestDataModel() { idconver = "" } : inputModel;

            try
            {
                var companyCredentials = SecurityHelper.GetCompanyCredentials();
                var companyProduct = CompanyProductService.GetByIdCompany(companyCredentials.IdCompany, companyCredentials.PortalId, OfferIntegratorEnum.CompuTrabajo);
                var portalConfig = PortalConfigurationService.GetPortalConfiguration();
                var company = CompanyService.GetByPK(new CompanySearchSpecifications(companyCredentials.PortalId) { Id = companyCredentials.IdCompany });
                var idConverDecrypt = _encryptionService.Decrypt(inputModel.idconver.ToString());
                var idConversation = long.TryParse(idConverDecrypt, out var idConverDecryptLong) ? idConverDecryptLong : 0;
                var searchByOffer = string.IsNullOrWhiteSpace(inputModel.SearchBy) ? string.Empty : inputModel.SearchBy;
                bool isFromMobileView = RequestOrigin.IsFromMobile(Request);

                if (checkBlocked(portalConfig.company_block, company.Blocked, company.LastDateBlockedUpdate, portalConfig.DaysBlock))
                    return RedirectToAction("Blocked", "Account");

                if (!HasChatActivate(companyCredentials.IdCompany, portalConfig))
                    return RedirectToAction("Index", "Company");

                if (!string.IsNullOrEmpty(inputModel.oi)
                    && !IsMy(companyCredentials, Convert.ToInt32(_encryptionService.Decrypt(inputModel.oi)), portalConfig))
                {
                    return RedirectToAction("Index", "Company");
                }

                var pagerFilter = new PagerFilter
                {
                    PageNumber = inputModel.IdPage > 0 && (inputModel.ChatNavigationPoint == ChatNavigationPointEnum.CandidateList || inputModel.ChatNavigationPoint == ChatNavigationPointEnum.SpecificConversation) ? inputModel.IdPage : 1,
                    PageSize = inputModel.PageSize > 0 && (inputModel.ChatNavigationPoint == ChatNavigationPointEnum.CandidateList || inputModel.ChatNavigationPoint == ChatNavigationPointEnum.SpecificConversation) ? inputModel.PageSize : PAGE_SIZE_CONVERSATION_DEFAULT
                };

                var pagerFilterOffer = new PagerFilter
                {
                    PageNumber = inputModel.IdPage > 0 && inputModel.ChatNavigationPoint == ChatNavigationPointEnum.OfferList ? inputModel.IdPage : 1,
                    PageSize = inputModel.PageSize > 0 && inputModel.ChatNavigationPoint == ChatNavigationPointEnum.OfferList ? inputModel.PageSize + 1 : PAGE_SIZE_CONVERSATION_DEFAULT + 1
                };

                var converOfferList = await GetConverOfferList(companyCredentials, portalConfig, searchByOffer, pagerFilterOffer);
                var totalOffers = FindOutTotalOfferAndRemoveLastElement(converOfferList, pagerFilterOffer);

                var offerListConversationDataModel = new List<OfferListConversationsDataModel>();
                converOfferList.ForEach(conver => MapConverByOffer(conver, offerListConversationDataModel, portalConfig.PortalId));

                var filter = GetFilterByConversations(inputModel, companyCredentials, portalConfig);
                List<ConversationDataModel> conversations = await GetConversations(portalConfig, pagerFilter, filter, idConversation, inputModel.ChatNavigationPoint);

                if (!conversations.Any())
                    return RedirectToAction("Index", "Company");

                int totalCountConversations = await _messageJobdsChatService.GetTotalCountConversationsAsync(filter, portalConfig.idapp);
                SetIdOfferCT(portalConfig, conversations);

                var ListOffers = FillDataOffer(conversations);
                var ListCandidates = FillDataDandidates(conversations, portalConfig.PortalId);
                var ListMatches = FillDataMatches(conversations);
                FillConverData(conversations, ListOffers, ListCandidates, ListMatches); //revisado - todo esto no pasara porque ya no existiran conversaciones activas sin un candidato o una oferta. (hay un proceso de listener)

                ConversationDataModel currentConversation = GetCurrentConversation(conversations, inputModel.idconver, inputModel.ChatNavigationPoint);
                var messages = currentConversation?.IdConversation <= 0 ? new List<MessageDataModel>() : await FillConversationMessagesAsync(conversations, companyCredentials, portalConfig, inputModel.oi, inputModel.idconver);



                bool canSendMessages = CandSendMessages(companyCredentials, currentConversation);
                bool showPromoFreemium = ShowPromoFreemium(portalConfig, companyCredentials, currentConversation.IdOfferEncrypted);
                var totalPendingMessages = await _messageJobdsChatService.GetCountPendingMessagesByCompanyAsync(new ConversationFilter()
                {
                    IdCompany = companyCredentials.IdCompany,
                    IdPortal = portalConfig.PortalId,
                    IdUserCompany = companyCredentials.UserId,
                    UserType = (short)UserTypeEnum.Company,
                    ConversationFilterEnum = ConversationFilterEnum.CompanyConversations
                }, portalConfig.idapp);

                HomeConversationsDataModel homeConversation = GetHomeConversationModel(inputModel, companyCredentials, portalConfig, searchByOffer, pagerFilter, pagerFilterOffer, totalOffers, offerListConversationDataModel, filter, conversations, totalCountConversations, currentConversation, messages, canSendMessages, showPromoFreemium, totalPendingMessages);
                FillOfferDataByConversationName(homeConversation, conversations);

                await CheckAndUpdateNewMessagesAsync(portalConfig, currentConversation); //para que no se repitan los mensajes en la vista

                return View("Index", homeConversation);
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyConversationsController Index-Get {ex}");
                ExceptionPublisherService.Publish(ex, "CompanyConversationsController", "Index-Get");
                return RedirectToAction("Index", "Company");
            }
        }

        private async Task<List<ConversationDataModel>> GetConversations(PortalConfig portalConfig, PagerFilter pagerFilter, ConversationFilter filter, long idConversation, ChatNavigationPointEnum chatNavigationPoint)
        {
            List<ConversationEntity> conversations = new List<ConversationEntity>();

            if (idConversation > 0 && chatNavigationPoint == ChatNavigationPointEnum.SpecificConversation)
            {
                var conversation = await _messageJobdsChatService.GetConversationByIdAsync(idConversation, portalConfig.idapp);
                conversations.Add(conversation);
            }
            else
            {
                conversations = await _messageJobdsChatService.GetConversationsAsync(filter, portalConfig.idapp, pagerFilter);
            }
            return Mapper.Map<List<ConversationEntity>, List<ConversationDataModel>>(conversations);
        }

        private int FindFeatureFreemiumAndGetApplicationLimit(bool showPromoFreemium, PortalConfig portalConfig, ConversationDataModel currentConversation, CompanyCredentials companyCredentials)
        {
            var result = 0;

            if (showPromoFreemium)
            {
                var chatFreemiumFeature = GetCompanyOfferProduct(companyCredentials, currentConversation.IdOfferEncrypted)?.Features?.Find(x => x.AmbitId == (int)ProductAmbitEnum.ChatContactFreemium) ?? null;
                result = GetApplicationLimit(portalConfig, currentConversation.IdOffer, chatFreemiumFeature);
            }

            return result;
        }

        private HomeConversationsDataModel GetHomeConversationModel(IndexConversationsRequestDataModel inputModel, CompanyCredentials companyCredentials, PortalConfig portalConfig, string searchByOffer, PagerFilter pagerFilter, PagerFilter pagerFilterOffer, int totalOffers, List<OfferListConversationsDataModel> offerListConversationDataModel, ConversationFilter filter, List<ConversationDataModel> conversations, int totalCountConversations, ConversationDataModel currentConversation, List<MessageDataModel> messages, bool canSendMessages, bool showPromoFreemium, int totalPendingMessages)
        {
            return new HomeConversationsDataModel
            {
                BackUrl = GetBackUrl(),
                Conversations = conversations,
                ContentMessages = new ContentMessageDataModel
                {
                    CanDelete = CanDelete(companyCredentials, currentConversation),
                    CanViewCV = CanViewCV(currentConversation),
                    CurrentConversation = currentConversation,
                    Messages = messages,
                    ShowPromoFreemium = showPromoFreemium,                  
                    CandSendMessages = canSendMessages,
                    MessageInformation = canSendMessages ? string.Empty : MessageInformacion(portalConfig, PageId),
                    ApplicationsLimit = FindFeatureFreemiumAndGetApplicationLimit(showPromoFreemium, portalConfig, currentConversation, companyCredentials)
                },
                OfferIdEncryptedParameter = inputModel.oi,
                ConversationIdEncryptedParameter = inputModel.idconver,
                DeleteConversationPopUp = GetDeleteConversationPopUp(portalConfig),
                OfferList = offerListConversationDataModel,
                OfferSelected = GetOfferSelectedByIdOffer((int)filter.IdOffer),
                TotalPendingMessagescompany = totalPendingMessages,
                NavigationControl = GetNavigationControlDataModel(inputModel, totalCountConversations, pagerFilter, pagerFilterOffer, totalOffers),
                SearchByOffer = string.IsNullOrWhiteSpace(searchByOffer) ? string.Empty : searchByOffer
            };
        }

        private bool CanViewCV(ConversationDataModel currentConversation)
        {
            return (currentConversation != null || currentConversation.IdConversation > 0) ? currentConversation.StatusConversation == (short)ConversationStatusEnum.Active : false;
        }

        private string GetBackUrl()
        {
            var oldUrlReferrer = GetUrlReferrer(KEY_BACK_URL);
            var urlReferrer = Request?.UrlReferrer?.ToString() ?? string.Empty;

            if (urlReferrer.ToLower().Contains("conversations"))
            {
                if (!string.IsNullOrEmpty(oldUrlReferrer))
                {
                    SetUrlReferrer(oldUrlReferrer, KEY_BACK_URL);
                    return oldUrlReferrer;
                }
            }
            else if (!string.IsNullOrEmpty(urlReferrer))
            {
                SetUrlReferrer(urlReferrer, KEY_BACK_URL);
                return urlReferrer;
            }
            else if (!string.IsNullOrEmpty(oldUrlReferrer))
            {
                SetUrlReferrer(oldUrlReferrer, KEY_BACK_URL);
                return oldUrlReferrer;
            }

            return Url.Action("Index", "Company");
        }

        private void SetUrlReferrer(string urlReferrer, string key)
        {
            TempData[key] = urlReferrer;
        }
        private string GetUrlReferrer(string key)
        {
            if (TempData[key] == null) { return string.Empty; }
            return TempData[key].ToString();
        }

        private bool CanDelete(CompanyCredentials companyCredentials, ConversationDataModel currentConversation)
        {
            if (string.IsNullOrEmpty(currentConversation.IdOfferEncrypted))
                return false;

            var groupId = GetCompanyOfferProduct(companyCredentials, currentConversation.IdOfferEncrypted).GroupId;
            return groupId == (short)ProductGroupsEnum.Packs || groupId == (short)ProductGroupsEnum.Membership;
        }

        private OfferListConversationsDataModel GetOfferSelectedByIdOffer(int idOffer)
        {
            var result = new OfferListConversationsDataModel();

            if (idOffer > 0)
            {
                var portalConfig = PortalConfigurationService.GetPortalConfiguration();
                var offer = OfferService.GetByPk(idOffer,portalConfig.PortalId);

                if (offer != null)
                {
                    result.IdOfferEncrypted = EncryptationHelper.Encrypt(offer.idoffer.ToString());
                    result.Title = offer.title ?? "";
                    result.CityName = offer.Location;
                    result.LocalizationName = GetLocalizationName(offer.idcountry, offer.idlocalization, offer.idportal);
                }
            }

            return result;
        }

        private OfferConversationDetailDataModel GetOfferConversationDetail(int idOffer)
        {
            var result = new OfferConversationDetailDataModel();

            if (idOffer > 0)
            {
                var portalConfig = PortalConfigurationService.GetPortalConfiguration();
                var offer = Mapper.Map<OfferEntity, OfferDataModel>(OfferService.GetByPk(idOffer, portalConfig.PortalId));

                if (offer != null)
                {
                    result.IdOfferEncrypted = EncryptationHelper.Encrypt(offer.idoffer.ToString());
                    result.Title = offer.title ?? "";
                    result.CityName = offer.Location;
                    result.LocalizationName = GetLocalizationName(offer.idcountry ?? 0,  offer.idlocalization ?? 0, offer.idportal);
                    result.IsActive = offer.IsActive;
                }
            }

            return result;
        }

        private void SetIdOfferCT(PortalConfig portalConfig, List<ConversationDataModel> conversations)
        {
            //INI quitamos cuando todo este actualizado, no haya IdOfferCT en la MATCH i OFFER                  
            foreach (var conver in conversations)
            {
                conver.IdOfferCT = OfferService.GetOfferCtIdByOfferId(conver.IdOffer, portalConfig.PortalId);
                conver.IdOfferCTEncrypted = EncryptationHelper.Encrypt(conver.IdOfferCT.ToString());
            }
            //FIN quitamos cuando todo este actualizado MATCH i OFFER         
        }

        private ConversationFilter GetFilterByConversations(IndexConversationsRequestDataModel inputModel, CompanyCredentials companyCredentials, PortalConfig portalConfig)
        {
            var filter = new ConversationFilter
            {
                IdCompany = companyCredentials.IdCompany,
                IdPortal = portalConfig.PortalId,
                IdUserCompany = companyCredentials.UserId,
                UserType = (short)UserTypeEnum.Company,
                ConversationFilterEnum = ConversationFilterEnum.CompanyConversations
            };

            if (!string.IsNullOrEmpty(inputModel.oi))
            {
                long.TryParse(_encryptionService.Decrypt(inputModel.oi), out var idOffer);
                filter.IdOffer = idOffer;
            }

            return filter;
        }

        private async Task<List<ConversationOffer>> GetConverOfferList(CompanyCredentials companyCredentials, PortalConfig portalConfig, string searchByOffer, PagerFilter pagerFilterOffer)
        {
            return await _messageJobdsChatService.GetOffersInConversationByCompanyAsync(new ConversationFilter
            {
                IdCompany = companyCredentials.IdCompany,
                IdPortal = portalConfig.PortalId,
                IdUserCompany = companyCredentials.UserId,
                UserType = (short)UserTypeEnum.Company,
                ConversationFilterEnum = ConversationFilterEnum.CompanyConversations
            }, searchByOffer, pagerFilterOffer, portalConfig.idapp);
        }

        private static int FindOutTotalOfferAndRemoveLastElement(List<ConversationOffer> converOfferList, PagerFilter pagerFilter)
        {
            var totalConverOffer = converOfferList?.Count ?? 0;

            if (totalConverOffer > 0 && totalConverOffer == pagerFilter.PageSize)
            {
                converOfferList.Remove(converOfferList.LastOrDefault());
            }

            pagerFilter.PageSize -= 1;

            return totalConverOffer;
        }

        private ConversationNavigationControlDataModel GetNavigationControlDataModel(IndexConversationsRequestDataModel inputModel, int totalCount, PagerFilter pagerFilter, PagerFilter pagerFilterOffer, int totalOffers)
        {
            var hasIdOffer = !string.IsNullOrEmpty(inputModel.oi);

            return new ConversationNavigationControlDataModel()
            {
                CandidatePageControl = (inputModel.ChatNavigationPoint == ChatNavigationPointEnum.SpecificConversation) ? new ConversationPageControlDataModel() { HasPagination = false } : GetNavigationControlByCandidatePoint(inputModel, totalCount, pagerFilter, ChatNavigationPointEnum.CandidateList),
                OfferPageControl = GetNavigationControlByOfferPoint(inputModel, totalOffers, pagerFilterOffer, ChatNavigationPointEnum.OfferList),
                ChatNavigationPoint = GetChatNavigationPoint(inputModel.ChatNavigationPoint, inputModel),
                CandidatesByOfferPageControl = new ConversationPageControlDataModel()
                {
                    BeforeActive = false,
                    HasPagination = hasIdOffer && inputModel.ChatNavigationPoint == ChatNavigationPointEnum.CandidatesByOffer
                                    ? totalCount > pagerFilter.PageSize : false,
                    NextActive = totalCount > pagerFilter.PageSize,
                    PageNumber = 1,
                    PageSize = hasIdOffer && inputModel.ChatNavigationPoint == ChatNavigationPointEnum.CandidatesByOffer
                                    ? pagerFilter.PageSize : PAGE_SIZE_CONVERSATION_DEFAULT
                },
            };
        }

        private ChatNavigationPointEnum GetChatNavigationPoint(ChatNavigationPointEnum chatNavigationPoint, IndexConversationsRequestDataModel inputModel)
        {
            var hasIdOffer = !string.IsNullOrEmpty(inputModel.oi);

            switch (chatNavigationPoint)
            {
                case ChatNavigationPointEnum.CandidateList:
                    return ChatNavigationPointEnum.CandidateList;
                case ChatNavigationPointEnum.OfferList:
                    return ChatNavigationPointEnum.OfferList;
                case ChatNavigationPointEnum.CandidatesByOffer:
                    if (hasIdOffer)
                        return ChatNavigationPointEnum.CandidatesByOffer;
                    break;
                case ChatNavigationPointEnum.SpecificConversation:
                    if (!string.IsNullOrEmpty(inputModel.idconver))
                        return ChatNavigationPointEnum.SpecificConversation;
                    break;
            }

            return ChatNavigationPointEnum.OfferList;
        }

        private static ConversationPageControlDataModel GetNavigationControlByCandidatePoint(IndexConversationsRequestDataModel inputModel, int totalCount, PagerFilter pagerFilter, ChatNavigationPointEnum navigationPoint)
        {
            return new ConversationPageControlDataModel()
            {
                BeforeActive = inputModel.ChatNavigationPoint == navigationPoint ? pagerFilter.PageNumber > 1 : false,
                HasPagination = totalCount > pagerFilter.PageSize,
                NextActive = inputModel.ChatNavigationPoint == navigationPoint ? totalCount > pagerFilter.PageSize * pagerFilter.PageNumber : totalCount > pagerFilter.PageSize,
                PageNumber = inputModel.ChatNavigationPoint == navigationPoint ? pagerFilter.PageNumber : 1,
                PageSize = inputModel.ChatNavigationPoint == navigationPoint ? pagerFilter.PageSize : PAGE_SIZE_CONVERSATION_DEFAULT
            };
        }

        private static ConversationPageControlDataModel GetNavigationControlByOfferPoint(IndexConversationsRequestDataModel inputModel, int totalCount, PagerFilter pagerFilter, ChatNavigationPointEnum navigationPoint)
        {
            return new ConversationPageControlDataModel()
            {
                BeforeActive = inputModel.ChatNavigationPoint == navigationPoint ? pagerFilter.PageNumber > 1 : false,
                HasPagination = pagerFilter.PageNumber <= 1 ? totalCount > pagerFilter.PageSize : true,
                NextActive = inputModel.ChatNavigationPoint == navigationPoint ? totalCount > pagerFilter.PageSize * pagerFilter.PageNumber : totalCount > pagerFilter.PageSize,
                PageNumber = inputModel.ChatNavigationPoint == navigationPoint ? pagerFilter.PageNumber : 1,
                PageSize = inputModel.ChatNavigationPoint == navigationPoint ? pagerFilter.PageSize : PAGE_SIZE_CONVERSATION_DEFAULT
            };
        }

        private string GetLocalizationName(int idCountry, int idLocalization, short idPortal)
        {
            if (idLocalization > 1 && idCountry > 0)
            {
                return DictionaryService.GetDictionaryValue(DictionaryEnum.LOCALIZATION_BY_COUNTRY, idCountry, idLocalization.ToString(), idPortal);
            }
            else if (idCountry > 0)
            {
                return DictionaryService.GetDictionaryValue(DictionaryEnum.COUNTRY, idCountry.ToString(), idPortal);
            }
            else
            {
                return "Sin especificar";
            }
        }

        private void MapConverByOffer(ConversationOffer conver, List<OfferListConversationsDataModel> conversationByOffer, short idPortal)
        {
            var offer = OfferService.GetByPk(conver.IdOfferEncrypted, idPortal);
            conversationByOffer.Add(new OfferListConversationsDataModel()
            {
                IdOfferEncrypted = conver.IdOfferEncrypted,
                Title = conver.ConversationName,
                Total = conver.TotalMessagesPendingToRead,
                CityName = offer.Location,
                LocalizationName = GetLocalizationName(offer.idcountry, offer.idlocalization, idPortal)
        });
        }

        [HttpPost]
        [Route("GetTotalCountConversationsByOffer")]
        public async Task<ActionResult> GetTotalCountConversationsByOffer(CandidatesByOfferParamsDataModel candidatesByOfferParams)
        {
            //return Json(new { total = 20 }, JsonRequestBehavior.AllowGet);
            try
            {
                var resultTotal = 0;
                var portalConfig = PortalConfigurationService.GetPortalConfiguration();
                var companyCredentials = SecurityHelper.GetCompanyCredentials();

                if (!string.IsNullOrEmpty(candidatesByOfferParams.oi))
                {
                    int.TryParse(_encryptionService.Decrypt(candidatesByOfferParams.oi), out var idOffer);
                    resultTotal = await _messageJobdsChatService.GetTotalCountConversationsAsync(new ConversationFilter()
                    {
                        IdCompany = companyCredentials.IdCompany,
                        IdPortal = portalConfig.PortalId,
                        IdUserCompany = companyCredentials.UserId,
                        UserType = (short)UserTypeEnum.Company,
                        IdOffer = idOffer,
                        ConversationFilterEnum = ConversationFilterEnum.CompanyConversations
                    }, portalConfig.idapp);
                }


                return Json(new { total = resultTotal }, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyConversationsController GetTotalCountConversationsByOffer {ex}");
                ExceptionPublisherService.Publish(ex, "CompanyConversationsController", "GetTotalCountConversationsByOffer");
                return PartialView("Conversation/_ConversationCandidatesByOffer", new List<ConversationDataModel>());
            }
        }


        [HttpPost]
        [Route("GetCandidatesByOffer")]
        public async Task<ActionResult> GetCandidatesByOffer(CandidatesByOfferParamsDataModel candidatesByOfferParams)
        {
            try
            {
                var companyCredentials = SecurityHelper.GetCompanyCredentials();
                var companyProduct = CompanyProductService.GetByIdCompany(companyCredentials.IdCompany, companyCredentials.PortalId, OfferIntegratorEnum.CompuTrabajo);
                var portalConfig = PortalConfigurationService.GetPortalConfiguration();

                if (!HasChatActivate(companyCredentials.IdCompany, portalConfig))
                    return RedirectToAction("Index", "Company");

                List<ConversationDataModel> conversations = new List<ConversationDataModel>();

                if (!string.IsNullOrEmpty(candidatesByOfferParams.oi))
                {
                    int.TryParse(_encryptionService.Decrypt(candidatesByOfferParams.oi), out var idOffer);

                    var filter = new ConversationFilter
                    {
                        IdCompany = companyCredentials.IdCompany,
                        IdPortal = portalConfig.PortalId,
                        IdUserCompany = companyCredentials.UserId,
                        UserType = (short)UserTypeEnum.Company,
                        IdOffer = idOffer,
                        ConversationFilterEnum = ConversationFilterEnum.CompanyConversations
                    };
                    conversations = Mapper.Map<List<ConversationEntity>, List<ConversationDataModel>>(await _messageJobdsChatService.GetConversationsAsync(filter, portalConfig.idapp, new PagerFilter() { PageNumber = candidatesByOfferParams.IdPage, PageSize = PAGE_SIZE_CONVERSATION_DEFAULT }));

                    //INI quitamos cuando todo este actualizado, no haya IdOfferCT en la MATCH i OFFER
                    var idOfferCt = OfferService.GetOfferCtIdByOfferId(idOffer, portalConfig.PortalId);
                    foreach (var conver in conversations)
                    {
                        conver.IdOfferCT = idOfferCt;
                        conver.IdOfferCTEncrypted = EncryptationHelper.Encrypt(idOfferCt.ToString());
                    }
                    //FIN quitamos cuando todo este actualizado MATCH i OFFER

                }
                else
                {
                    conversations = await GetConversationsAsync(companyCredentials, portalConfig);

                    //INI quitamos cuando todo este actualizado, no haya IdOfferCT en la MATCH i OFFER                      
                    foreach (var conver in conversations)
                    {
                        conver.IdOfferCT = OfferService.GetOfferCtIdByOfferId(conver.IdOffer, portalConfig.PortalId);
                        conver.IdOfferCTEncrypted = EncryptationHelper.Encrypt(conver.IdOfferCT.ToString());
                    }
                    //FIN quitamos cuando todo este actualizado MATCH i OFFER                  
                }

                List<OfferDataModel> ListOffers = FillDataOffer(conversations);

                if (ListOffers.Any())
                {
                    List<CandidateDataModel> ListCandidates = FillDataDandidates(conversations, portalConfig.PortalId);
                    List<MatchDataModel> ListMatches = FillDataMatches(conversations);

                    FillConverData(conversations, ListOffers, ListCandidates, ListMatches);

                    ViewBag.portalConfig = portalConfig;
                    ViewBag.idConverSelected = candidatesByOfferParams.oi;
                }

                return PartialView("Conversation/_ConversationCandidatesByOffer", conversations);
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyConversationsController GetCandidatesByOffer {ex}");
                ExceptionPublisherService.Publish(ex, "CompanyConversationsController", "GetCandidatesByOffer");
                return PartialView("Conversation/_ConversationCandidatesByOffer", new List<ConversationDataModel>());
            }
        }

        [HttpPost]
        public async Task<ActionResult> GetConversations(string idConversationEncrypted, string idOfferEncrypted, bool isFromHeader)
        {
            try
            {
                var companyCredentials = SecurityHelper.GetCompanyCredentials();
                var companyProduct = CompanyProductService.GetByIdCompany(companyCredentials.IdCompany, companyCredentials.PortalId, OfferIntegratorEnum.CompuTrabajo);
                var portalConfig = PortalConfigurationService.GetPortalConfiguration();

                if (!HasChatActivate(companyCredentials.IdCompany, portalConfig))
                    return RedirectToAction("Index", "Company");

                List<ConversationDataModel> conversations = new List<ConversationDataModel>();

                if (!string.IsNullOrEmpty(idOfferEncrypted))
                {
                    int.TryParse(_encryptionService.Decrypt(idOfferEncrypted), out var idOffer);

                    var filter = new ConversationFilter
                    {
                        IdCompany = companyCredentials.IdCompany,
                        IdPortal = portalConfig.PortalId,
                        IdUserCompany = companyCredentials.UserId,
                        UserType = (short)UserTypeEnum.Company,
                        IdOffer = idOffer,
                        ConversationFilterEnum = ConversationFilterEnum.CompanyConversations
                    };
                    conversations = Mapper.Map<List<ConversationEntity>, List<ConversationDataModel>>(await _messageJobdsChatService.GetConversationsAsync(filter, portalConfig.idapp, new PagerFilter() { PageNumber = 1, PageSize = PAGE_SIZE_CONVERSATION_DEFAULT }));

                    //INI quitamos cuando todo este actualizado, no haya IdOfferCT en la MATCH i OFFER
                    var idOfferCt = OfferService.GetOfferCtIdByOfferId(idOffer, portalConfig.PortalId);
                    foreach (var conver in conversations)
                    {
                        conver.IdOfferCT = idOfferCt;
                        conver.IdOfferCTEncrypted = EncryptationHelper.Encrypt(idOfferCt.ToString());
                    }
                    //FIN quitamos cuando todo este actualizado MATCH i OFFER

                }
                else
                {
                    conversations = await GetConversationsAsync(companyCredentials, portalConfig);

                    //INI quitamos cuando todo este actualizado, no haya IdOfferCT en la MATCH i OFFER                      
                    foreach (var conver in conversations)
                    {
                        conver.IdOfferCT = OfferService.GetOfferCtIdByOfferId(conver.IdOffer, portalConfig.PortalId);
                        conver.IdOfferCTEncrypted = EncryptationHelper.Encrypt(conver.IdOfferCT.ToString());
                    }
                    //FIN quitamos cuando todo este actualizado MATCH i OFFER                  
                }

                List<OfferDataModel> ListOffers = FillDataOffer(conversations);

                if (ListOffers.Any())
                {
                    List<CandidateDataModel> ListCandidates = FillDataDandidates(conversations, portalConfig.PortalId);
                    List<MatchDataModel> ListMatches = FillDataMatches(conversations);

                    FillConverData(conversations, ListOffers, ListCandidates, ListMatches);

                    ViewBag.isFromHeader = isFromHeader;
                    ViewBag.portalConfig = portalConfig;
                    ViewBag.idConverSelected = idConversationEncrypted;
                }

                return PartialView("Conversation/_ConversationCandidates", conversations);
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyConversationsController GetConversations {ex}");
                ExceptionPublisherService.Publish(ex, "CompanyConversationsController", "GetConversations");
                return PartialView("Conversation/_ConversationCandidates", new List<ConversationDataModel>());
            }
        }

        [HttpPost]
        public async Task<ActionResult> GetConversationsHeader()
        {
            try
            {
                var companyCredentials = SecurityHelper.GetCompanyCredentials();
                var companyProduct = CompanyProductService.GetByIdCompany(companyCredentials.IdCompany, companyCredentials.PortalId, OfferIntegratorEnum.CompuTrabajo);
                var portalConfig = PortalConfigurationService.GetPortalConfiguration();

                if (!HasChatActivate(companyCredentials.IdCompany, portalConfig))
                    return RedirectToAction("Index", "Company");

                HomeConversationsDataModel headerChatDTM = new HomeConversationsDataModel();

                List<ConversationDataModel> conversations;

                headerChatDTM.TotalPendingMessagescompany = await _messageJobdsChatService.GetCountPendingMessagesByCompanyWithCacheAsync(new ConversationFilter
                {
                    IdCompany = companyCredentials.IdCompany,
                    IdPortal = portalConfig.PortalId,
                    IdUserCompany = companyCredentials.UserId,
                    UserType = (short)UserTypeEnum.Company,
                    ConversationFilterEnum = ConversationFilterEnum.CompanyConversations
                }, portalConfig.idapp);

                conversations = await HeaderConversationsAsync(companyCredentials, portalConfig, headerChatDTM.TotalPendingMessagescompany > 0);

                //INI quitamos cuando todo este actualizado, no haya IdOfferCT en la MATCH i OFFER                  
                foreach (var conver in conversations)
                {
                    conver.IdOfferCT = OfferService.GetOfferCtIdByOfferId(conver.IdOffer, portalConfig.PortalId);
                    conver.IdOfferCTEncrypted = EncryptationHelper.Encrypt(conver.IdOfferCT.ToString());
                }
                //FIN quitamos cuando todo este actualizado MATCH i OFFER

                List<OfferDataModel> ListOffers = FillDataOffer(conversations);

                if (ListOffers.Any())
                {
                    List<CandidateDataModel> ListCandidates = FillDataDandidates(conversations, portalConfig.PortalId);
                    List<MatchDataModel> ListMatches = FillDataMatches(conversations);

                    FillConverData(conversations, ListOffers, ListCandidates, ListMatches);
                }

                headerChatDTM.Conversations = conversations;

                return PartialView("Conversation/_MessageConversationHeader", headerChatDTM);
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyConversationsController GetConversationsHeader {ex}");
                ExceptionPublisherService.Publish(ex, "CompanyConversationsController", "GetConversationsHeader");
                return PartialView("Conversation/_MessageConversationHeader", new HomeConversationsDataModel());
            }
        }

        private bool HasChatActivate(int idCompany, PortalConfig portalConfig)
        {
            return portalConfig.AEPortalConfig.EnableProgressiveChatFreemium
                && CompanyProductService.HasActiveFeatureByProduct(new SearchCompanyProductsByCompanyDTO(idCompany, ProductAmbitEnum.ChatContactFreemium, portalConfig.PortalId, ProductEnum.FreemiumService));
        }

        [HttpPost]
        public async Task<int> GetCountMessagesPendingHeader()
        {
            try
            {
                var companyCredentials = SecurityHelper.GetCompanyCredentials();
                var portalConfig = PortalConfigurationService.GetPortalConfiguration();

                return await _messageJobdsChatService.GetCountPendingMessagesByCompanyWithCacheAsync(new ConversationFilter
                {
                    IdCompany = companyCredentials.IdCompany,
                    IdPortal = portalConfig.PortalId,
                    IdUserCompany = companyCredentials.UserId,
                    ConversationFilterEnum = ConversationFilterEnum.CompanyConversations
                }, portalConfig.idapp);

            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyConversationsController GetCountMessagesPendingHeader {ex}");
                ExceptionPublisherService.Publish(ex, "CompanyConversationsController", "GetCountMessagesPendingHeader");
                return 0;
            }
        }

        [HttpPost]
        public async Task<ActionResult> GetMessagesByIdConversation(string idConversationEncrypted, bool isPopUpMatch = false)
        {
            var partialViewName = isPopUpMatch ? PARTIAL_CONVERSATION_POP_UP_VIEW_NAME : PARTIAL_CONVERSATION_BOX_VIEW_NAME;

            try
            {            
                int.TryParse(_encryptionService.Decrypt(idConversationEncrypted), out var idCurrentConversation);

                if (idCurrentConversation <= 0)
                {
                    return PartialView(partialViewName, new ContentMessageDataModel { });
                }                

                var companyCredentials = SecurityHelper.GetCompanyCredentials();
                var portalConfig = PortalConfigurationService.GetPortalConfiguration();

                ConversationDataModel conversation = new ConversationDataModel();
                List<MessageDataModel> messages = new List<MessageDataModel>();

                var conversationMessages = await _messageJobdsChatService.GetMessagesInConversationAsync(new MessageFilter()
                {
                    IdConversation = idCurrentConversation,
                    IdPortal = portalConfig.PortalId,
                    ConversationFilterEnum = ConversationFilterEnum.CompanyConversations
                }, portalConfig.idapp, new PagerFilter() { PageNumber = 1, PageSize = PAGE_SIZE_MESSAGE_DEFAULT });

                conversation = Mapper.Map<ConversationEntity, ConversationDataModel>(conversationMessages.Conversation);
                messages = Mapper.Map<List<MessageEntity>, List<MessageDataModel>>(conversationMessages.Messages);

                //INI quitamos cuando todo este actualizado, no haya IdOfferCT en la MATCH i OFFER               
                conversation.IdOfferCT = OfferService.GetOfferCtIdByOfferId(conversation.IdOffer, portalConfig.PortalId);
                conversation.IdOfferCTEncrypted = EncryptationHelper.Encrypt(conversation.IdOfferCT.ToString());
                //FIN quitamos cuando todo este actualizado MATCH i OFFER

                conversation.OfferConversationDetail = GetOfferConversationDetail(conversation.IdOffer);

                messages = GetMessageImages(messages, companyCredentials, portalConfig);

                await CheckAndUpdateNewMessagesAsync(portalConfig, conversation);

                var contentMessasgeDataModel = new ContentMessageDataModel
                {
                    CurrentConversation = conversation,
                    Messages = messages
                };
                List<ConversationDataModel> conversations = new List<ConversationDataModel>();
                conversations.Add(conversation);

                List<OfferDataModel> ListOffers = FillDataOffer(conversations);
                List<CandidateDataModel> ListCandidates = FillDataDandidates(conversations, portalConfig.PortalId);
                List<MatchDataModel> ListMatches = FillDataMatches(conversations);

                FillConverData(conversations, ListOffers, ListCandidates, ListMatches);
                contentMessasgeDataModel.CurrentConversation = conversations.FirstOrDefault();

                bool canSendMessages = CandSendMessages(companyCredentials, contentMessasgeDataModel.CurrentConversation);
                bool showPromoFreemium = ShowPromoFreemium(portalConfig, companyCredentials, contentMessasgeDataModel.CurrentConversation.IdOfferEncrypted);

                contentMessasgeDataModel.ShowPromoFreemium = showPromoFreemium;
                contentMessasgeDataModel.CandSendMessages = canSendMessages;               
                contentMessasgeDataModel.MessageInformation = canSendMessages ? string.Empty : MessageInformacion(portalConfig, PageId);
                contentMessasgeDataModel.ApplicationsLimit = FindFeatureFreemiumAndGetApplicationLimit(showPromoFreemium, portalConfig, contentMessasgeDataModel.CurrentConversation, companyCredentials);
                contentMessasgeDataModel.CanDelete = CanDelete(companyCredentials, conversation) ;
                contentMessasgeDataModel.CanViewCV = CanViewCV(contentMessasgeDataModel.CurrentConversation);

                return PartialView(partialViewName, contentMessasgeDataModel);
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyConversationsController GetCountMessagesPendingHeader {ex}");
                ExceptionPublisherService.Publish(ex, "CompanyConversationsController", "GetCountMessagesPendingHeader");
                return PartialView(partialViewName, new ContentMessageDataModel());
            }
        }

        [HttpPost]
        [Route("GetMessagesActiveConverByPageAsync")]
        public async Task<ActionResult> GetMessagesActiveConverByPageAsync(MessagesByPageParamsDataModel model, bool isPopUpMatch = false)
        {

            var partialViewName = isPopUpMatch ? PARTIAL_MESSAGE_PAGE_POP_UP_VIEW_NAME : PARTIAL_MESSAGE_PAGE_VIEW_NAME;

            if (model == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest, "empty model");
            }

            if (model.IdPage < MINIMUM_PAGE_SCROLL_UP || !int.TryParse(_encryptionService.Decrypt(model.IdConversationEncrypted), out var idCurrentConversation)
                || model.IdFirstMessageByPage <= 0)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest, "incorrect params");
            }

            var portalConfig = PortalConfigurationService.GetPortalConfiguration();

            var messages = Mapper.Map<List<MessageEntity>, List<MessageDataModel>>(await _messageJobdsChatService.GetMessagesAsync(new MessageFilter
            {
                IdConversation = idCurrentConversation,
                UserType = (short)UserTypeEnum.Company,
                IdPortal = portalConfig.PortalId,
                ConversationFilterEnum = ConversationFilterEnum.CompanyConversations
            }, portalConfig.idapp, new PagerFilter() { PageNumber = model.IdPage, PageSize = PAGE_SIZE_MESSAGE_DEFAULT }));

            if (!messages.Any())
            {
                return Json(END_CONVERSATION);
            }
       
            messages.RemoveAll(m => m.IdMessage >= model.IdFirstMessageByPage);

            return PartialView(partialViewName, messages);
        }
         
        public async Task<ActionResult> GetMessagesActiveConverAsync(string idConversationEncrypted,bool isPopUpMatch = false)
        {
            var partialViewName = isPopUpMatch ? PARTIAL_MESSAGES_POP_UP_VIEW_NAME : PARTIAL_MESSAGES_VIEW_NAME;

            try
            {
                int.TryParse(_encryptionService.Decrypt(idConversationEncrypted), out var idCurrentConversation);
                var portalConfig = PortalConfigurationService.GetPortalConfiguration();

                var messages = Mapper.Map<List<MessageEntity>, List<MessageDataModel>>(await _messageJobdsChatService.GetMessagesAsync(new MessageFilter
                {
                    IdConversation = idCurrentConversation,
                    UserType = (short)UserTypeEnum.Company,
                    IdPortal = portalConfig.PortalId,
                    ConversationFilterEnum = ConversationFilterEnum.CompanyConversations
                }, portalConfig.idapp));

                var lastMessage = new MessageDataModel();

                if (messages.Any(n => n.StatusMessage == (int)1 && n.UserType == (short)UserTypeEnum.Candidate))
                {
                    var companyCredentials = SecurityHelper.GetCompanyCredentials();

                    lastMessage = messages.FirstOrDefault(n => n.StatusMessage == (int)1 && n.UserType == (short)UserTypeEnum.Candidate);

                    await _messageJobdsChatService.UpdateMessagesToReadAsync(new ConversationFilter()
                    {
                        UserType = (int)UserTypeEnum.Company,
                        IdConversation = idCurrentConversation,
                        IdCompany = companyCredentials.IdCompany,
                        IdPortal = portalConfig.PortalId,
                        IdUserCompany = (int)lastMessage.IdUserCompany,
                    }, portalConfig.idapp);

                    switch (lastMessage.UserType)
                    {
                        case (short)UserTypeEnum.Company:
                            lastMessage.Image = CompanyHelper.GetLogoPath(companyCredentials);
                            break;

                        case (short)UserTypeEnum.Candidate:
                            lastMessage.Image = _candidateService.GetCandidatePhoto(lastMessage.IdCandidate, PortalConfigurationService.GetPortalConfiguration());
                            break;
                    }
                }
                else
                {
                    return new HttpStatusCodeResult(HttpStatusCode.Accepted);
                }

                if (messages.Count >= 2)
                {
                    var lastMessages = messages[messages.Count - 1];
                    var penultimateMessages = messages[messages.Count - 2];
                    var sameDescriptionDate = lastMessages.DateShowHead(portalConfig) == penultimateMessages.DateShowHead(portalConfig);

                    ViewData["isSameMessageOwner"] = lastMessages.UserType == penultimateMessages.UserType && sameDescriptionDate;
                    ViewData["showDateShowHead"] = !sameDescriptionDate;
                }

                return PartialView(partialViewName, lastMessage);
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyConversationsController GetMessagesActiveConverAsync {ex}");
                ExceptionPublisherService.Publish(ex, "CompanyConversationsController", "GetMessagesActiveConverAsync");
                return PartialView(partialViewName, new MessageDataModel());
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> InsertMessage(string idConversationEncrypted, string idTypeEncrypted, string message, bool isPopUpMatch = false)
        {
            var partialViewName = isPopUpMatch ? PARTIAL_MESSAGES_POP_UP_VIEW_NAME : PARTIAL_MESSAGES_VIEW_NAME;

            try
            {
                Int32.TryParse(_encryptionService.Decrypt(idConversationEncrypted), out var idCurrentConversation);
                Int32.TryParse(_encryptionService.Decrypt(idTypeEncrypted), out var idType);                

                var companyCredentials = SecurityHelper.GetCompanyCredentials();
                var portalConfig = PortalConfigurationService.GetPortalConfiguration();                

                if (idCurrentConversation <= 0 || String.IsNullOrEmpty(message))
                {
                    return new HttpStatusCodeResult(HttpStatusCode.BadRequest, PageLiteralsHelper.GetLiteral("ERROR_LOAD_DATA", PageId, portalConfig));
                }


                if (Regex.IsMatch(message, RegularExpressions.CONTAINS_ICON_CHARCODES))
                {
                    return new HttpStatusCodeResult(HttpStatusCode.BadRequest, PageLiteralsHelper.GetLiteral("LIT_ERROR_INSERT_EMOJIS", PageId, portalConfig));
                }
                else if (message.IsSurrogateText())
                {
                    return new HttpStatusCodeResult(HttpStatusCode.BadRequest, PageLiteralsHelper.GetLiteral("LIT_TEXT_STYLE_ERROR", PageId, portalConfig));
                }

                List<MessageEntity> currentMessages = new List<MessageEntity>();
                MessageEntity messageEntity = new MessageEntity();

                messageEntity = new MessageEntity()
                {
                    IdConversation = idCurrentConversation,
                    IdPortal = portalConfig.PortalId,
                    IdUserCompany = (int)companyCredentials.UserId,
                    MessageBody = message,
                    UserType = (int)UserTypeEnum.Company
                };

                if (!await _messageJobdsChatService.AddMessageAsync(messageEntity, portalConfig.idapp))
                    return new HttpStatusCodeResult(HttpStatusCode.BadRequest, PageLiteralsHelper.GetLiteral("ERROR_ADD_MESSAGE", PageId, portalConfig));

                currentMessages = await _messageJobdsChatService.GetMessagesAsync(new MessageFilter
                {
                    IdConversation = idCurrentConversation,
                    UserType = (short)UserTypeEnum.Company,
                    IdPortal = portalConfig.PortalId,
                    ConversationFilterEnum = ConversationFilterEnum.CompanyConversations
                }, portalConfig.idapp, new PagerFilter() { PageNumber = 1, PageSize = PAGE_SIZE_MESSAGE_DEFAULT });

                if (currentMessages.Count() == 1)
                {                    
                    var messageFirst = currentMessages.FirstOrDefault();
                    GenerateNotificationPendingsMessages(messageFirst.IdCv, messageFirst.IdCandidate, portalConfig.PortalId, messageFirst.IdOffer, companyCredentials.IdCompany, (int)companyCredentials.UserId);

                    var offerProduct = GetCompanyOfferProduct(companyCredentials, _encryptionService.Encrypt(messageFirst.IdOffer.ToString()));
                    _requestDeviceRecorder.RecordChatFreemiumFirstMessageDeviceKpi(offerProduct.GroupId, portalConfig.PortalId);                    
                }

                if (idType != (short)TypeConverEnum.Bidireccional)
                {
                    //Haremos lo mismo pero desaparecerá .. si no es bidireccional le cambiamos                    
                    await _messageJobdsChatService.ChangeTypeConverAsync(new ConversationFilter
                    {
                        IdConversation = idCurrentConversation,
                        IdConversationType = (short)TypeConverEnum.Bidireccional,
                        IdPortal = portalConfig.PortalId
                    }, portalConfig.idapp);
                }

                if (currentMessages.Count >= 2)
                {
                    var lastMessages = currentMessages[currentMessages.Count - 1];
                    var penultimateMessages = currentMessages[currentMessages.Count - 2];
                    var sameDay = lastMessages.DateAdd.Day == penultimateMessages.DateAdd.Day
                                    && lastMessages.DateAdd.Month == penultimateMessages.DateAdd.Month
                                    && lastMessages.DateAdd.Year == penultimateMessages.DateAdd.Year;

                    ViewData["isSameMessageOwner"] = lastMessages.UserType == penultimateMessages.UserType && sameDay;
                    ViewData["showDateShowHead"] = !sameDay;

                    if (lastMessages.UserType != penultimateMessages.UserType)
                    {                      
                        var offerProduct = GetCompanyOfferProduct(companyCredentials, _encryptionService.Encrypt(lastMessages.IdOffer.ToString()));
                        _requestDeviceRecorder.RecordChatFreemiumResponseMessageDeviceKpi(offerProduct.GroupId, portalConfig.PortalId);
                    }
                }

                return PartialView(partialViewName, Mapper.Map<MessageEntity, MessageDataModel>(currentMessages.LastOrDefault()));
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyConversationsController InsertMessage {ex}");
                ExceptionPublisherService.Publish(ex, "CompanyConversationsController", "InsertMessage");
                return PartialView(partialViewName, new MessageDataModel());
            }
        }
              

        //TODO MQC W24604 - mirar tema de eliminar si falla
        [HttpPost]
        public async Task<ActionResult> DeleteConversationAsync(string idConversationEncrypted)
        {
            try
            {
                int.TryParse(_encryptionService.Decrypt(idConversationEncrypted), out var idCurrentConversation);

                var portalConfig = PortalConfigurationService.GetPortalConfiguration();
                var companyCredentials = SecurityHelper.GetCompanyCredentials();

                var result = await _messageJobdsChatService.DeleteConversationAsync(new ConversationFilter()
                {
                    IdConversation = idCurrentConversation,
                    IdPortal = portalConfig.PortalId,
                    IdCompany = companyCredentials.IdCompany,
                    IdUserCompany = companyCredentials.UserId,
                    ConversationFilterEnum = ConversationFilterEnum.CompanyConversations

                }, portalConfig.idapp);

                if (result) return new HttpStatusCodeResult(HttpStatusCode.OK, "Deleted Conversation");
                else return new HttpStatusCodeResult(HttpStatusCode.BadRequest, "Fallo al eliminar conversación");
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyConversationsController DeleteConversationAsync {ex}");
                ExceptionPublisherService.Publish(ex, "CompanyConversationsController", "DeleteConversationAsync");
                //TODO MQC 2023 para Eliminar dar error si falla. PageLiteralsHelper.GetLiteral("ERROR_ADD_MESSAGE", PageId, portalConfig) 
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest, "Fallo al eliminar conversación");
            }
        }

        [Route("SendToBuyProduct")]
        public ActionResult SendToBuyProduct(string oi)
        {
            var companyCredentials = SecurityHelper.GetCompanyCredentials();
            var portalConfig = PortalConfigurationService.GetPortalConfiguration();
            var productToBuy = GetProductCartIdByPage(PAGE_PACK_CART, (short)ProductSubGroupsEnum.Standard, portalConfig, companyCredentials.IdCompany);
            int.TryParse(_encryptionService.Decrypt(oi), out var idOffer);
            var buttonId = (int)TpvButtonOriginEnum.ChatFreemiumMessageDetail;
            var buttonIdEncry = _encryptionService.Encrypt(((int)TpvButtonOriginEnum.ChatFreemiumMessageDetail).ToString());

            if (idOffer > 0 && _securityOfferService.IsMy(new IsMyOfferDTO(companyCredentials.UserRole, companyCredentials.IdCompany, companyCredentials.PortalId, (int)companyCredentials.UserId, idOffer, portalConfig.EnabledNewIsMy)))
            {
                var companyActiveProducts = CompanyProductService.GetAllCompanyActiveProducts(companyCredentials.IdCompany, companyCredentials.PortalId);
                var productsWithFeature = companyActiveProducts.Where(x => x.Features.Exists(y => y.AmbitId == (int)ProductAmbitEnum.Offer) && x.GroupId == (short)ProductGroupsEnum.Packs).ToList();
                var availableUnits = productsWithFeature.Sum(x => x.Features.Find(y => y.AmbitId == (int)ProductAmbitEnum.Offer).AvailableUnits);

                if (availableUnits > 0
                    && !portalConfig.AEPortalConfig.PostPublishWithConsumables)
                {
                    return RedirectToAction("landing-convert-packs", "Company/Landings", new { io = oi, cc = EncryptationHelper.Encrypt("1"), btnConvertEnc = buttonIdEncry });
                }

                PurchaseProductsListEntity purchaseProductsListEntity = new PurchaseProductsListEntity
                {
                    IdOffer = idOffer,
                    IdCompany = companyCredentials.IdCompany,
                    IdPortal = portalConfig.PortalId,
                    OfferProductId = productToBuy.Id,
                    OfferGroup = (short)productToBuy.GroupId
                };

                if (_multipurchaseOperationService.SavePurchaseProductsList(purchaseProductsListEntity))
                {
                    Session["sessionSourceButton"] = buttonId;
                    Session["sessionSourcePage"] = PageId;

                    return RedirectToAction("Index", "MultiPurchaseCart", new { oi = oi, p = EncryptationHelper.Encrypt(PAGE_PACK_CART.ToString()), btn = buttonIdEncry, prod = _encryptionService.Encrypt(productToBuy.Id.ToString()) });
                }
            }

            return RedirectToAction("Index", "MultiPurchaseCart", new { p = EncryptationHelper.Encrypt(PAGE_PACK_CART.ToString()), btn = buttonIdEncry, prod = _encryptionService.Encrypt(productToBuy.Id.ToString()) });
        }

        [HttpPost]
        public async Task<int> InsertMessageUnidirectionalAsync(string ids, string oi, string p, string idu, string env, string kpi, string text)
        {
            try
            {
                int.TryParse(_encryptionService.Decrypt(oi), out var offerId);

                int.TryParse(p, out var portalId);

                int.TryParse(_encryptionService.Decrypt(idu), out var userId);

                int.TryParse(_encryptionService.Decrypt(env), out var enviorementId);

                int.TryParse(_encryptionService.Decrypt(kpi), out var kpiId);

                var companyCredentials = SecurityHelper.GetCompanyCredentials();
                var portalConfig = PortalConfigurationService.GetPortalConfiguration();

                if (offerId <= 0 ||
                   portalId <= 0 ||
                   userId <= 0 ||
                   enviorementId <= 0 ||
                   text.Length <= 0 ||
                   ids.Length <= 0)
                {
                    return 0;
                }

                var idsMtches = ids.Split('|');
                List<long> Convers = new List<long>();

                List<ConversationEntity> ConversationsActive = await GetConversationsActiveAsync(portalConfig.PortalId, companyCredentials.IdCompany, companyCredentials.UserId, portalConfig.idapp);

                foreach (var item in idsMtches)
                {
                    long.TryParse(_encryptionService.Decrypt(item), out var id);

                    if (id != 0)
                    {
                        //IdOfferCT es IdOffer guardado en nuevo CHAT 2023
                        MatchEntity Match = _matchService.GetByPk(id);
                        ConversationEntity ConversationActive = ConversationsActive.Find(n => n.IdCandidate == Match.CandidateId && n.IdOfferCT == offerId && n.IdUserCompany == companyCredentials.UserId);

                        if (ConversationActive == null || ConversationActive.IdConversation == 0)
                        {
                            int idConver = await CreateConversationUnidirectionalAsync(companyCredentials, portalConfig, Convers, Match, offerId);

                            if (idConver > 0)
                                await CreateMessageForConversationAsync(companyCredentials, portalConfig, Match, offerId, idConver, text, Convers);
                        }
                        else
                        {
                            await CreateMessageForConversationAsync(companyCredentials, portalConfig, Match, offerId, ConversationActive.IdConversation, text, Convers);
                        }
                    }
                }
                if (Convers.Count() == 0)
                    return 1;

                return 0;
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyConversationsController InsertMessageUnidirectionalAsync {ex}");
                ExceptionPublisherService.Publish(ex, "CompanyConversationsController", "InsertMessageUnidirectionalAsync");
                return 0;
            }
        }

        public async Task<string> OpenOrCreateConverAsync(string ims, string oi)
        {
            try
            {
                int.TryParse(_encryptionService.Decrypt(oi), out var offerId);

                long.TryParse(_encryptionService.Decrypt(ims), out var matchId);

                var companyCredentials = SecurityHelper.GetCompanyCredentials();
                var portalConfig = PortalConfigurationService.GetPortalConfiguration();

                if (offerId <= 0 ||
                   matchId <= 0)
                {
                    return string.Empty;
                }

                MatchEntity Match = _matchService.GetByPk(matchId);
           
                long idConver = await CreateOrGetConversationAsync(companyCredentials, portalConfig,  Match, offerId);
            
                if (idConver == 0)
                    return string.Empty;

                return _encryptionService.Encrypt(idConver.ToString());
            }
            catch (FormsAuthenticationExpiredException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyConversationsController OpenOrCreateConver {ex}");
                ExceptionPublisherService.Publish(ex, "CompanyConversationsController", "OpenOrCreateConver");
                return string.Empty;
            }
        }

        [HttpPost]
        [Route("GetTotalPendingMessages")]
        public async Task<int> GetTotalPendingMessages()
        {
            try
            {
                var resultTotal = 0;
                var portalConfig = PortalConfigurationService.GetPortalConfiguration();
                var companyCredentials = SecurityHelper.GetCompanyCredentials();

                resultTotal = await _messageJobdsChatService.GetCountPendingMessagesByCompanyAsync(new ConversationFilter()
                {
                    IdCompany = companyCredentials.IdCompany,
                    IdPortal = portalConfig.PortalId,
                    IdUserCompany = companyCredentials.UserId,
                    UserType = (short)UserTypeEnum.Company,
                    ConversationFilterEnum = ConversationFilterEnum.CompanyConversations
                }, portalConfig.idapp);


                return resultTotal;
            }
            catch (Exception ex)
            {
                ExceptionPublisherService.Publish(ex, "CompanyConversationsController", "GetTotalPendingMessages");
                return 0;
            }
        }

        [HttpPost]
        [Route("GetTotalPendingMessagesByOffer")]
        public async Task<int> GetTotalPendingMessagesByOffer(string idoffer)
        {
            try
            {
                int.TryParse(_encryptionService.Decrypt(idoffer), out var offerId);
                var resultTotal = 0;
                var portalConfig = PortalConfigurationService.GetPortalConfiguration();
                var companyCredentials = SecurityHelper.GetCompanyCredentials();

                resultTotal = await _messageJobdsChatService.GetCountPendingMessagesByCompanyAsync(new ConversationFilter()
                {
                    IdCompany = companyCredentials.IdCompany,
                    IdPortal = portalConfig.PortalId,
                    IdUserCompany = companyCredentials.UserId,
                    UserType = (short)UserTypeEnum.Company,
                    IdOffer = offerId,
                    ConversationFilterEnum = ConversationFilterEnum.CompanyConversations
                }, portalConfig.idapp);


                return resultTotal;
            }
            catch (Exception ex)
            {
                ExceptionPublisherService.Publish(ex, "CompanyConversationsController", "GetTotalPendingMessagesByOffer");
                return 0;
            }
        }
        private bool IsMy(CompanyCredentials companyCredentials, int idOffer, PortalConfig portalConfig)
        {
            return _securityOfferService.IsMy(new IsMyOfferDTO(companyCredentials.UserRole, companyCredentials.IdCompany, companyCredentials.PortalId, (int)companyCredentials.UserId, idOffer, portalConfig.EnabledNewIsMy));
        }

        private int GetApplicationLimit(PortalConfig portalConfig, int idOffer, CompanyProductFeatureEntity chatFreemiumFeature)
        {
            var chatContacts = _freemiumOfferContactsService.GetContactsByOffer(new FreemiumOfferMatchesContactsDTO() { OfferId = idOffer, PortalId = portalConfig.PortalId });

            if (chatContacts > NO_FREEMIUM_VIEWS_RESULTS)
            {
                return chatContacts;
            }

            return chatFreemiumFeature?.AvailableUnits ?? 0;
        }

        private ProductEntity GetProductCartIdByPage(int idPage, short subGroup, PortalConfig portalConfig, int idCompany)
        {
            List<ProductEntity> productsPage = ProductService.SearchByPage(new ProductSearchSpecifications(portalConfig.PortalId) { PageId = idPage, CompanyId = idCompany, SubGroup = subGroup, LoadPromotions = portalConfig.active_promotions, DisableFreemium = IsDisableFreemiumOffers(idCompany, portalConfig) }, true).ToList();

            if (productsPage == null || !productsPage.Any())
            {
                productsPage = ProductService.SearchByPage(new ProductSearchSpecifications(portalConfig.PortalId) { PageId = (int)PageEnum.DefaultCart, CompanyId = idCompany, SubGroup = subGroup, LoadPromotions = portalConfig.active_promotions, DisableFreemium = IsDisableFreemiumOffers(idCompany, portalConfig) }, true).ToList();

                if (productsPage == null || !productsPage.Any())
                {
                    productsPage = ProductService.SearchByPage(new ProductSearchSpecifications(portalConfig.PortalId) { PageId = (int)PageEnum.DefaultCart, CompanyId = idCompany, LoadPromotions = portalConfig.active_promotions, DisableFreemium = IsDisableFreemiumOffers(idCompany, portalConfig) }, true).ToList();
                }
            }

            return productsPage?.FirstOrDefault() ?? new ProductEntity();
        }
    }
}