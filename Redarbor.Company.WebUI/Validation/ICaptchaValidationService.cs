using Redarbor.Master.Contracts.ServiceLibrary.DTO;

namespace Redarbor.Company.WebUI.Validation
{
    public interface ICaptchaValidationService
    {
        bool IsCaptchaValid(string captchaResponse, CompanyCredentials companyCredentials = null);

        public string GetControlKey(CompanyCredentials companyCredentials = null);

        bool IncrementShows(CompanyCredentials companyCredentials = null);
    }
}