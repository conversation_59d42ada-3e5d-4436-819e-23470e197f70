using Autofac.Integration.Mvc;
using Redarbor.Company.WebUI.Helpers;
using Redarbor.User.Contracts.ServiceLibrary;
using Redarbor.User.Impl.ServiceLibrary;
using System.ComponentModel.DataAnnotations;

namespace Redarbor.Company.WebUI.Validation.Attributes
{
    public class EmailCheckerAttribute : ValidationAttribute
    {
        public string CountryIdSelected { get; set; }
        private static IUserService _service;

        private static IUserService Service => _service ??= ((UserService)AutofacDependencyResolver.Current
                                                             .GetService(typeof(IUserService)));

        public EmailCheckerAttribute() { }

        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            var portalConfig = PortalConfigHelper.GetPortalConfiguration();

            if (value == null || string.IsNullOrEmpty(value.ToString()))
            {
                return new ValidationResult(ErrorMessage, new[] { validationContext.MemberName });
            }

            if (Service.ExistsMail(value.ToString(), portalConfig.PortalId))
            {
                return new ValidationResult(ErrorMessage, new[] { validationContext.MemberName });
            }

            return ValidationResult.Success;
        }
    }
}