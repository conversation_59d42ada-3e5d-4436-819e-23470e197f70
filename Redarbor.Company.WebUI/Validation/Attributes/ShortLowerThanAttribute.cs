using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Web.Mvc;

namespace Redarbor.Company.WebUI.Validation.Attributes
{
    public class ShortLowerThanAttribute : ValidationAttribute, IClientValidatable
    {
        private string _otherPropertyId;
        private string _otherPropertyName;

        public ShortLowerThanAttribute(string otherPropertyName, string otherPropertyId)
        {
            this._otherPropertyName = otherPropertyName;
            this._otherPropertyId = otherPropertyId;
        }

        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            ValidationResult validationResult = ValidationResult.Success;
            try
            {
                // Using reflection we can get a reference to the other date property, in this example the project start date
                var containerType = validationContext.ObjectInstance.GetType();
                var field = containerType.GetProperty(_otherPropertyName);

                if (field == null)
                {
                    return new ValidationResult(String.Format("Unknown property: {0}.", _otherPropertyName));
                }

                var extensionValue = field.GetValue(validationContext.ObjectInstance, null);
                if (extensionValue == null || value == null)
                {
                    //validationResult = new ValidationResult("Start Date is empty");
                    return validationResult;
                }

                // Let's check that otherProperty is of type int as we expect it to be
                if (field.PropertyType == typeof(short))
                {
                    var referenceProperty = Convert.ToInt16(field.GetValue(validationContext.ObjectInstance, null));
                    var toValidate = Convert.ToInt16(value);

                    if (referenceProperty < toValidate && referenceProperty > 0 && toValidate > 0) 
                    {
                        validationResult = new ValidationResult(ErrorMessage, new[] { validationContext.MemberName });
                    }
                }
                else
                {
                    validationResult = new ValidationResult("An error occurred while validating the property. OtherProperty is not of type DateTime", new[] { validationContext.MemberName });
                }
            }
            catch (Exception)
            {
                validationResult = new ValidationResult(ErrorMessage, new[] { validationContext.MemberName });
            }

            return validationResult;
        }


        public IEnumerable<ModelClientValidationRule> GetClientValidationRules(ModelMetadata metadata, ControllerContext context)
        {
            var rule = new ModelClientValidationRule
            {
                ErrorMessage = ErrorMessage,
                ValidationType = "islower",
            };
            rule.ValidationParameters.Add("otherproperty", _otherPropertyId);
            yield return rule;
        }

    }
}


