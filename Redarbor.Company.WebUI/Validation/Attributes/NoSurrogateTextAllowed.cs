using Redarbor.Extensions.Library.Extensions;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Web.Mvc;
using Redarbor.Common.Entities.Constants;

namespace Redarbor.Company.WebUI.Validation.Attributes
{
	public class NoSurrogateTextAllowed : ValidationAttribute, IClientValidatable
	{
		public NoSurrogateTextAllowed()
		{
		}
			
		protected override ValidationResult IsValid(object value, ValidationContext validationContext)
		{
			try
			{
				string textValidate = value == null ? string.Empty : value.ToString();
				if (string.IsNullOrEmpty(textValidate))
				{
					return ValidationResult.Success;
				}
				if (!textValidate.IsSurrogateText())
				{
					return ValidationResult.Success;
				}
				return new ValidationResult(ErrorMessage, new[] { validationContext.MemberName });
			}
			catch (Exception e)
			{
				return new ValidationResult(ErrorMessage, new[] { e.Message.ToString() });
			}
		}

		public IEnumerable<ModelClientValidationRule> GetClientValidationRules(ModelMetadata metadata, ControllerContext context)
		{
			var rule = new ModelClientValidationRule
			{
				ErrorMessage = ErrorMessage,
				ValidationType = "issurrogate",
			};

			rule.ValidationParameters.Add("minsurrogate", LimitSurrogate.MIN_HIGH_SURROGATE_UTF16);
            rule.ValidationParameters.Add("maxsurrogate", LimitSurrogate.MAX_HIGH_SURROGATE_UTF16);
			
            yield return rule;
		}
	}
}