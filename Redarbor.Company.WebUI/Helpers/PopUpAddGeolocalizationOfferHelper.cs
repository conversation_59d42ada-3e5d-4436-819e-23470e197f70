using Autofac.Integration.Mvc;
using Redarbor.Common.Entities.Configuration;
using Redarbor.Common.Entities.Enums;
using Redarbor.Core.Contracts.ServiceLibrary;
using Redarbor.Core.Contracts.ServiceLibrary.Enums;
using Redarbor.Core.Impl.ServiceLibrary;
using Redarbor.Dictionaries.Consumer.Enums;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;

namespace Redarbor.Company.WebUI.Helpers
{
    public static class PopUpAddGeolocalizationOfferHelper
    {
        static IDictionaryService _dictionaryService;

        private static IDictionaryService DictionaryService =>
            _dictionaryService ?? (_dictionaryService = ((DictionaryService)AutofacDependencyResolver.Current
                                                                       .GetService(typeof(IDictionaryService))));

        public static List<SelectListItem> GetGeolocalizationByIdCity(PortalConfig portalConfig, int idCity)
        {

            var dictionary = GetFromDictionaryDependantKey(portalConfig, DictionaryEnum.POSTAL_CODES, PageEnum.MatchOffer, dependantKey : idCity);

            if (dictionary == null || !dictionary.Any())
            {
                return new List<SelectListItem>();
            }
            return dictionary;
        }

        private static List<SelectListItem> GetFromDictionaryDependantKey(PortalConfig portalConfig, DictionaryEnum dictionaryEnum, PageEnum pageId = PageEnum.OfferList, bool appendDefaultValueWhenEmpty = false, string defaultLiteral = "LIT_FORM_SELECCIONE", int? dependantKey = null)
        {
            var dependantKeyaux = dependantKey ?? 0;
            var dictionary = DictionaryService.GetDictionaryPostalCodes(dictionaryEnum, dependantKeyaux, portalConfig.PortalId);

            var listItems = new List<SelectListItem>();

            if (appendDefaultValueWhenEmpty)
                listItems.Add(new SelectListItem() { Value = "0", Text = PageLiteralsHelper.GetLiteral(defaultLiteral, (short)pageId, portalConfig) });

            foreach (var item in dictionary)
                listItems.Add(new SelectListItem() { Value = item.Key, Text = item.Value });


            return listItems;
        }

    }
}