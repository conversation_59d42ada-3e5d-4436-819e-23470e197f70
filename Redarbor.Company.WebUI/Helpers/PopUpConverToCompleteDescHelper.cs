using Autofac.Integration.Mvc;
using Redarbor.Common.Entities.Configuration;
using Redarbor.Common.Entities.Enums;
using Redarbor.Master.Contracts.ServiceLibrary.DTO;
using Redarbor.Master.Entities.Enums;
using Redarbor.Master.Entities.Product;
using Redarbor.Products.Contracts.ServiceLibrary;
using Redarbor.Products.Contracts.ServiceLibrary.DTO;
using Redarbor.Products.Impl.ServiceLibrary;
using System.Collections.Generic;
using System.Linq;

namespace Redarbor.Company.WebUI.Helpers
{
    public static class PopUpConverToCompleteDescHelper
    {
        static IProductService _productService;
        static ICompanyProductService _companyProductService;

        private static IProductService ProductService =>
            _productService ?? (_productService = ((ProductService)AutofacDependencyResolver.Current
                                                                       .GetService(typeof(IProductService))));

        private static ICompanyProductService CompanyProductService =>
            _companyProductService ?? (_companyProductService = ((CompanyProductService)AutofacDependencyResolver.Current
                                                                       .GetService(typeof(ICompanyProductService))));


        public static short GetSubgroupId(string idProductStr, PortalConfig portalConfig)
        {
            if(short.TryParse(idProductStr, out short idProduct))
            {
				return ProductService.GetSubGroupIdByProduct(idProduct, portalConfig);
			}
            return 0;
        }
		public static List<ProductFeatureDescriptionEntity> GetConvertToCompleteDesc(PortalConfig portalConfig, short idSubGroup, CompanyCredentials companyCredentials)
        {
			List<ProductFeatureDescriptionEntity> listFeature = new List<ProductFeatureDescriptionEntity>();

            if (companyCredentials != null && portalConfig != null)
            {
                var landing = GetConvertToCompletePopUp(portalConfig, companyCredentials.IdCompany);
                listFeature = ProductService.GetSubGroupFeaturesDescriptions(idSubGroup, portalConfig.PortalId).Where(x => x.LandingId == (short)landing && x.ProductId == 0).ToList();
                
                if (idSubGroup != (short)ProductSubGroupsEnum.None && !listFeature.Any())
                {
                    listFeature = ProductService.GetSubGroupFeaturesDescriptions((short)ProductSubGroupsEnum.None, portalConfig.PortalId).Where(x => x.LandingId == (short)landing && x.ProductId == 0).ToList();
                }
            }
            return listFeature;
		}

        private static PlaceLandingEnum GetConvertToCompletePopUp(PortalConfig portalConfig, int idCompany)
        {
            return portalConfig.AEPortalConfig.EnableProgressiveChatFreemium
                && CompanyProductService.HasActiveFeatureByProduct(new SearchCompanyProductsByCompanyDTO(idCompany, ProductAmbitEnum.ChatContactFreemium, portalConfig.PortalId, ProductEnum.FreemiumService))
            ? PlaceLandingEnum.ConvertToCompletePopUpNewChat
            : PlaceLandingEnum.ConvertToCompletePopUp;
        }

        public static string GetTitleProductId(PortalConfig portalConfig, string idProductStr, short subgroup)
        {
			if (short.TryParse(idProductStr, out short idProduct))
			{
                return $"{PageLiteralsHelper.GetLiteral("LIT_OFFER", (int)PageEnum.HomePrivada, portalConfig)} {ProductService.GetProductDescriptionByGroupAndSubGroup(idProduct, subgroup, portalConfig.PortalId)}";
			}
			return string.Empty;
		}
    }
}