using System;

namespace Redarbor.Company.WebUI.Models.Company.Candidate.Cv
{
    public class CommentCvDataModel
    {
        public int Id { get; set; }

        public string Comment { get; set; } = string.Empty;

        public DateTime CreatedOn { get; set; } = DateTime.MinValue;

        public string TextDate { get; set; } = string.Empty;

        public string NameAuthor { get; set; } = string.Empty;

        public int OfferId { get; set; }

        public int CandidateId { get; set; }

        public int CvId { get; set; }

        public int CompanyId { get; set; }

        public int UserId { get; set; }

        public short PortalId { get; set; }

        public bool CanRemoving { get; set; }
    }
}