using System;
using System.Collections.Generic;

namespace Redarbor.Company.WebUI.Models.Company.Candidate
{
    public class CandidateDataModel
    {
        public string ClientIdAdd { get; set; } = string.Empty;
        public string ClientIdMod { get; set; } = string.Empty;
        public int IdCandidate { get; set; }
        public int IdUser { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Surname { get; set; } = string.Empty;
        public short Age { get; set; }
        public short IdPortal { get; set; }
        public short IdRace { get; set; }
        public short IdGender { get; set; }
        public short IdCity { get; set; }
        public short IdLocalization { get; set; }
        public short IdCountry { get; set; }
        public string Address { get; set; } = string.Empty;
        public string Phone1 { get; set; } = string.Empty;
        public string Phone1ToWhatsApp
        {
            get
            {
                return Phone1.Trim().Replace("-", "");
            }
        }
        public short IdPhoneType1 { get; set; }
        public string Phone2 { get; set; } = string.Empty;
        public string Phone2ToWhatsApp
        {
            get
            {
                return Phone2.Trim().Replace("-", "");
            }
        }
        public short IdPhoneType2 { get; set; }
        public short IdMaritalStatus { get; set; }
        public short Nationality { get; set; }
        public short IdLegalSituation { get; set; }
        public short DriveLicence { get; set; }
        public string Nit { get; set; } = string.Empty;
        public short IdIdentificationType { get; set; }
        public short Disability { get; set; }
        public short ExperienceYears { get; set; }
        public string Photo { get; set; } = string.Empty;
        public short Car { get; set; }
        public short IdemploymentStatus { get; set; }
        public int MinimumSalary { get; set; }
        public short ResidenceChange { get; set; }
        public short Travel { get; set; }
        public string WishedJob { get; set; } = string.Empty;
        public DateTime CreatedOn { get; set; } = DateTime.MinValue;
        public int CreatedBy { get; set; }
        public DateTime UpdatedOn { get; set; } = DateTime.MinValue;
        public DateTime DeletedOn { get; set; } = DateTime.MinValue;
        public short IdStatus { get; set; }
        public List<CandidateByCategoryDataModel> Categories { get; set; } = new List<CandidateByCategoryDataModel>();
        public List<CandidateByLocalizationDataModel> Localizations { get; set; } = new List<CandidateByLocalizationDataModel>();
        public List<CandidateByEmploymentTypeDataModel> EmploymentTypes { get; set; } = new List<CandidateByEmploymentTypeDataModel>();
        public int IdCargo { get; set; }
        public string SkypeName { get; set; } = string.Empty;


        public bool Phone1VerificationStatus { get; set; }
        public int ProductId { get; set; }
        public string CodePresentationVideo { get; set; } = string.Empty;
        public bool HasCodePresentationVideo { get; set; }        
        public bool IsPremium { get; set; }

        public bool TestCompetences { get; set; }

        public bool TalentView3D { get; set; }
        public bool TestCompetencesTalentViewVisible { get; set; }

        public bool HasVideoPresentationPreAssigned { get; set; }

    }
}