using Redarbor.Company.WebUI.Models.Company.Candidate.Cv;
using Redarbor.Company.WebUI.Models.Company.Offer;
using System.Collections.Generic;

namespace Redarbor.Company.WebUI.Models.Company.Candidate
{
    public class CandidateReadDataModel
    {
        public UserDataModel User { get; set; } = new UserDataModel();
        public CandidateDataModel Candidate { get; set; } = new CandidateDataModel();
        public CvDataModel Cv { get; set; } = new CvDataModel();

        public bool IsFromOfferFlash { get; set; } = false;
        public bool IsAutoExcluding { get; set; } = false;
        public bool HasTestCompetences { get; set; } = false;

        public int IdFolder { get; set; }

        public string UserLastLoginCultureInfo { get; set; } = string.Empty;
        public string UserCreatedOnCultureInfo { get; set; } = string.Empty;
        public string CandidateUpdatedOnCultureInfo { get; set; } = string.Empty;
      
        public string CandidateRaceString { get; set; } = string.Empty;
        public string NationlaityString { get; set; } = string.Empty;
        public string ProvinceString { get; set; } = string.Empty;
        public string CityString { get; set; } = string.Empty;
        public string LegalSituationString { get; set; } = string.Empty;
        public string CivilianStatus { get; set; } = string.Empty;
        public string ActualWork { get; set; } = string.Empty;

        public string CommentTextArea { get; set; } = string.Empty;


        public string Salary { get; set; } = string.Empty;
        public RatingCvDataModel RatingTop { get; set; } = new RatingCvDataModel();
        public List<RatingCvDataModel> ListRating { get; set; } = new List<RatingCvDataModel>();
        public List<CommentCvDataModel> ListComments { get; set; } = new List<CommentCvDataModel>();
        public List<KillerQuestionDataModel> KillerQuestions { get; set; } = new List<KillerQuestionDataModel>();
        public List<OfferCandidateDataModel> HistoryOffers = new List<OfferCandidateDataModel>();


        public bool DriveLicense { get; set; } = false;
        public bool Vehicles { get; set; } = false;
        public bool Travel { get; set; } = false;
        public bool ChangeResidence { get; set; } = false;
        public bool CanShowNit { get; set; } = false;
        public bool CanShowRace { get; set; } = false;
        public bool HasDriverLicense { get; set; } = false;
        public bool HasVehicles { get; set; } = false;
        public bool IsYourNationality { get; set; } = false;
        public bool ShowMinSalary { get; set; } = false;
        public bool HasRatings { get; set; } = false;
        public bool HasComments { get; set; } = false;
        public bool HasKillerQuestions { get; set; } = false;
        public bool HasExperiences { get; set; } = false;
        public bool HasFormation { get; set; } = false;
        public bool HasTravel { get; set; } = false;
        public bool HasChangeResidence { get; set; } = false;
        public bool HasTalentView3D { get; set; } = false;  

        public string IdentificationType { get; set; } = string.Empty;
    }
}