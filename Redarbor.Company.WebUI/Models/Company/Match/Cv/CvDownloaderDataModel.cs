using Redarbor.Master.Entities.Product;
using System;

namespace Redarbor.Company.WebUI.Models.Company.Match.Cv
{
    public class CvDownloaderDataModel
    {
        public int Id { get; set; }
        public string IdEncrypt { get; set; }
        public int IdOffer { get; set; }
        public string IdOfferEncrypted { get; set; }        
        public int IdCompany { get; set; }
        public string OfferTitle { get; set; }
        public DateTime OfferDate { get; set; }
        public short DownloadAllCvs { get; set; }
        public string Filter { get; set; }
        public string Url { get; set; }
        public string FileName { get; set; }
        public short IdExtension { get; set; }
        public int CountCvs { get; set; }
        public short IdStatus { get; set; }
        public short IdPortal { get; set; }
        public DateTime DateAdd { get; set; }
        public long UserId { get; set; }
        public string LocalizationDescription { get; set; } = string.Empty;
        public string MonthOfferDateDescription { get; set; } = string.Empty;
        public string MonthDateAddDescription { get; set; } = string.Empty;
        public bool TypeExtract { get; set; } = false;
        public CVDownloaderConfigDataModel CVDownloaderConfig { get; set; } = new CVDownloaderConfigDataModel();
        public bool ShowNitByConfigPortal { get; set; } = false;
        public bool ShowTestCompetences { get; set; } = false;
        public bool ShowCVDownloadMatchesReceived { get; set; } = false;
        public bool ShowCVDownloadCSVXLS { get; set; } = false;
        public bool CanExtractCV { get; set; } = false;
        public bool IsActionSuported { get; set; } = false;
        public bool CVsDownload { get; set; } = false;
        public CompanyProductFeatureEntity CVsDownloadLimit { get; set; } = new CompanyProductFeatureEntity();
    }
}