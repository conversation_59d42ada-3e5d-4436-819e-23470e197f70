using Redarbor.Common.Entities.Enums;
using Redarbor.Company.WebUI.Models.Company.Shared;
using Redarbor.Company.WebUI.Models.Company.Vision.Valuations;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Redarbor.Company.WebUI.Models.Company.Vision
{
    public class VisionCommentsDataModel
    {
        public string CompanyComercialName { get; set; } = string.Empty;
        public short FilterBy { get; set; } = (short) RatingCommentStatus.AcceptedOrDenounced;
        public short GetUnanswered => FilterBy != (short) RatingCommentStatus.Unanswered ? (short)0 : (short)1;
        public List<VisionRatingsDataModel> CompanyValuationRattings { get; set; } = new List<VisionRatingsDataModel>();
        public bool BlockVisionSections { get; set; } = false;
        public bool IsActionPermitedByRole { get; set; } = false;
        public bool HaveReviewsModeratedByCompanyFeature { get; set; }        
        public int ManageActions { get; set; }

        [UIHint("Pager")]
        public PagerDataModel Pager { get; set; } = new PagerDataModel();

        [UIHint("PopUp")]
        public PopUpDataModel DenounceCommentPopUp { get; set; } = new PopUpDataModel();
        [UIHint("PopUp")]
        public PopUpDataModel AcceptCommentPopUp { get; set; } = new PopUpDataModel();
        [UIHint("PopUp")]
        public PopUpDataModel DeclineCommentPopUp { get; set; } = new PopUpDataModel();
        [UIHint("PopUp")]
        public PopUpDataModel CommentWithURLPopUp { get; set; } = new PopUpDataModel();
        [UIHint("PopUp")]
        public PopUpDataModel CommentWithMailPopUp { get; set; } = new PopUpDataModel();
        [UIHint("PopUp")]
        public PopUpDataModel GenericActionPopUp { get; set; } = new PopUpDataModel();        
    }
}