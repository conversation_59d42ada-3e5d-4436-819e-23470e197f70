using Redarbor.Company.WebUI.Validation.Attributes;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Web.Mvc;

namespace Redarbor.Company.WebUI.Models.Company.Vision
{
    public class VisionCompanyDetailDataModel
    {
        public int IdCompany { get; set; }
        public short PortalId { get; set; }
        [Required(ErrorMessage = "LIT_ERR_COMMERCIAL_NAME_REQUIRED")]
        [StringLength(50, ErrorMessage = "LIT_ERR_LENGTH_MAX_50")]
		[NoEmojisAllowed(ErrorMessage = "LIT_ERROR_INSERT_EMOJIS")]
		[NoSurrogateTextAllowed(ErrorMessage = "LIT_TEXT_STYLE_ERROR")]
		public string ComercialName { get; set; } = string.Empty;
        [Required(ErrorMessage = "LIT_ERR_COMPANY_NAME_REQUIRED")]
        [StringLength(50, ErrorMessage = "LIT_ERR_LENGTH_MAX_50")]
		[NoEmojisAllowed(ErrorMessage = "LIT_ERROR_INSERT_EMOJIS")]
		[NoSurrogateTextAllowed(ErrorMessage = "LIT_TEXT_STYLE_ERROR")]
		public string CompanyName { get; set; } = string.Empty;
        public string Nit { get; set; } = string.Empty;
        public short IdCountry { get; set; }
        public short IdCountrySelected { get; set; }
        public List<SelectListItem> Countries { get; set; } = new List<SelectListItem>();
        public short? IdLocalization { get; set; }
        public List<SelectListItem> Localizations { get; set; } = new List<SelectListItem>();
        public short? IdCity { get; set; }
        public List<SelectListItem> Cities { get; set; } = new List<SelectListItem>();
        [Required(ErrorMessage = "LIT_ERR_ADDRESS_REQUIRED")]
        [StringLength(200, ErrorMessage = "LIT_ERR_LENGTH_MAX_200")]
		[NoEmojisAllowed(ErrorMessage = "LIT_ERROR_INSERT_EMOJIS")]
		[NoSurrogateTextAllowed(ErrorMessage = "LIT_TEXT_STYLE_ERROR")]
		public string Address { get; set; } = string.Empty;
        public short IdIndustry { get; set; }
        public List<SelectListItem> Industries { get; set; } = new List<SelectListItem>();
        [Required(ErrorMessage = "LIT_ERR_EMPLOYEES_NUMBER_REQUIRED")]
        public short IdEmploymentNumber { get; set; }
        public List<SelectListItem> NumberEmployees { get; set; } = new List<SelectListItem>();
        [Required(ErrorMessage = "LIT_ERR_POSTAL_CODE_REQUIRED")]
        [StringLength(10, ErrorMessage = "LIT_ERR_LENGTH_MAX_10")]
		[NoEmojisAllowed(ErrorMessage = "LIT_ERROR_INSERT_EMOJIS")]
		[NoSurrogateTextAllowed(ErrorMessage = "LIT_TEXT_STYLE_ERROR")]
		public string Postalcode { get; set; } = string.Empty;
        public bool IsUpdated { get; set; }
    }
}