namespace Redarbor.Company.WebUI.Models.Company.Vision
{
    public class VisionEscapeRiskModel
    {
        public string CurrentCSSClass { get; } = string.Empty;
        public string CurrentMarkIndex { get; } = string.Empty;
        public string EscapeRiskPercentageDescription { get; } = string.Empty;
        public int CanvasEscapeRiskYAxeStepSize { get; }
        public string CanvasEscapeRiskHistoryLabel { get; } = string.Empty;
        public string CanvasEscapeRiskHistoryData { get; } = string.Empty;
        public bool PhEscapeRiskEvolution { get; }
        public bool PhEscapeRiskEvoVisibleBLoqued { get; }
        public bool PhEscapeRiskEvoVisible { get; }
        public bool PhEscapeRisk { get; }
        public bool PhEscapeRiskBloqued { get; }
        public bool PhAllGraphics { get; }
        public bool PhWithoutData { get; }
        public bool HaveVisionFeature { get; }
        public bool isActionPermitedByRole { get; set; }

        public VisionEscapeRiskModel(
            string currentCSSClass,
            string currentMarkIndex,
            string escapeRiskPercentageDescription,
            int canvasEscapeRiskYAxeStepSize,
            string canvasEscapeRiskHistoryLabel,
            string canvasEscapeRiskHistoryData,
            bool phEscapeRiskEvolution,
            bool phEscapeRiskEvoVisibleBLoqued,
            bool phEscapeRiskEvoVisible,
            bool phEscapeRisk,
            bool phEscapeRiskBloqued,
            bool phAllGraphics,
            bool phWithoutData,
            bool haveVisionFeature,
            bool isActionPermitedByRole)
        {
            this.CurrentCSSClass = currentCSSClass;
            this.CurrentMarkIndex = currentMarkIndex;
            this.EscapeRiskPercentageDescription = escapeRiskPercentageDescription;
            this.CanvasEscapeRiskYAxeStepSize = canvasEscapeRiskYAxeStepSize;
            this.CanvasEscapeRiskHistoryLabel = canvasEscapeRiskHistoryLabel;
            this.CanvasEscapeRiskHistoryData = canvasEscapeRiskHistoryData;
            this.PhEscapeRiskEvolution = phEscapeRiskEvolution;
            this.PhEscapeRiskEvoVisibleBLoqued = phEscapeRiskEvoVisibleBLoqued;
            this.PhEscapeRiskEvoVisible = phEscapeRiskEvoVisible;
            this.PhEscapeRisk = phEscapeRisk;
            this.PhEscapeRiskBloqued = phEscapeRiskBloqued;
            this.PhAllGraphics = phAllGraphics;
            this.PhWithoutData = phWithoutData;
            this.HaveVisionFeature = haveVisionFeature;
            this.isActionPermitedByRole = isActionPermitedByRole;
        }
    }
}