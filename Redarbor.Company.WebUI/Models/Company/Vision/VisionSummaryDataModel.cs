using Redarbor.Extensions.Library.Extensions;
using System.Collections.Generic;

namespace Redarbor.Company.WebUI.Models.Company.Vision
{
    public class VisionSummaryDataModel
    {
        public string CompanyComercialName { get; set; } = string.Empty;
        public bool HaReviewsFeature { get; set; }
        public string GeneralValuationScoreStars { get; set; } = string.Empty;
        public string RecomendedPercent { get; set; } = string.Empty;
        public string CompanyFollowers { get; set; } = string.Empty;
        public string TotalValuations { get; set; } = string.Empty;
        public string CEOScoreValuation { get; set; } = string.Empty;
        public string TotalCEOValuations { get; set; } = string.Empty;
        public string RecommendationsNumber { get; set; } = string.Empty;
        public string CurrentPosition { get; set; } = string.Empty;
        public string CompanyLastUpdate { get; set; } = string.Empty;
        public string CompanyPhotosLit { get; set; } = string.Empty;
        public string CompanyBenefitsLit { get; set; } = string.Empty;
        public string TotalBenefitsLit { get; set; } = string.Empty;
        public string TotalAnsweredComments { get; set; } = string.Empty;
        public string TotalCommentsLit { get; set; } = string.Empty;
        public string UnansweredCommentsLit { get; set; } = string.Empty;
        public string GeneralStarsWidth { get; set; } = string.Empty;
        public string CeoStarsWidth { get; set; } = string.Empty;
        public List<string> LblGraf = new List<string>();
        public List<string> ValuesGraf = new List<string>();
        public string AboutCompanyGrafHClass { get; set; } = string.Empty;
        public string PhotosGrafHClass { get; set; } = string.Empty;
        public string BenefitsGrafHClass { get; set; } = string.Empty;
        public string CommentsGrafHClass { get; set; } = string.Empty;
        public bool ShowLinkEditCompany { get; set; } = false;

        //Refactorizar nombres
        public bool IsUnblockedVision { get; set; }
        public bool PhProgressCompanyInfoGreen { get; set; }
        public bool PhProgressCompanyInfoGray { get; set; }
        public bool PhProgressCompanyPhotosGreen { get; set; }
        public bool PhProgressCompanyPhotosGray { get; set; }
        public bool PhBenefitsIconValGreenOk { get; set; }
        public bool PhBenefitsIconValGrayOk { get; set; }
        public bool PhUnansweredComments { get; set; }
        public bool PhCommentIconValGrayOk { get; set; }
        public bool PhCommentIconValGreenOk { get; set; }
        public string InfoPercentWith { get; set; } = string.Empty;
        public int MiniumPhotosCompanyMonth { get; set; }
        public int NumberLimitEvaluations { get; set; } = 0;
        public bool IsExceededLimitEvaluations => GetIsExceededLimitEvaluations(); 
        public string PhotosPercentWith { get; set; } = string.Empty;
        public string BenefitsPercentWith { get; set; } = string.Empty;
        public string CommentPercentWith { get; set; } = string.Empty;
        private bool GetIsExceededLimitEvaluations()
        {
            var recommendationsNumber = 0;

            if (RecommendationsNumber != "0")
            {
                if (RecommendationsNumber.Contains(","))
                {
                    recommendationsNumber = RecommendationsNumber.Replace(",","").ToInt();
                }
                else if (RecommendationsNumber.Contains("."))
                {
                    recommendationsNumber = RecommendationsNumber.Replace(".", "").ToInt();
                }
                else
                {
                    recommendationsNumber = RecommendationsNumber.ToInt();
                }
                return recommendationsNumber > NumberLimitEvaluations;
            }
            return false;
        }
    }
}