using Redarbor.Master.Entities.Enums;
using System;
using System.ComponentModel.DataAnnotations;

namespace Redarbor.Company.WebUI.Models.Company.Vision
{
    public class VisionAwardsItemModel
    {
        public string Id { get; set; }

        [Required(ErrorMessage = "YEAR_REQUIRED")]
        public int Year { get; set; }

        [Required(ErrorMessage = "NAME_REQUIRED_ATTR")]
        [StringLength(100, ErrorMessage = "MAX_100")]
        public string Name { get; set; }

        [Required(ErrorMessage = "AWARD_ORIGIN")]
        [StringLength(100, ErrorMessage = "MAX_100")]
        public string Source { get; set; }

        [Url]
        [StringLength(100, ErrorMessage = "MAX_255")]
        public string Web { get; set; }

        public bool IsAwardsEditable { get; set; }

        public CompanyAwardEnum AwardType { get; set; }

        public VisionAwardsItemModel()
        {
            Id = "";
            Year = DateTime.Now.Year;
            Name = "";
            Source = "";
            Web = "";
            AwardType = CompanyAwardEnum.Regular;
            IsAwardsEditable = false;
        }

        public VisionAwardsItemModel(
            bool isAwardsEditable
            )
        {
            Id = "";
            Year = DateTime.Now.Year;
            Name = "";
            Source = "";
            Web = "";
            AwardType = CompanyAwardEnum.Regular;
            IsAwardsEditable = isAwardsEditable;
        }

        public VisionAwardsItemModel(
            string id,
            int year,
            string name,
            string source,
            string web,
            CompanyAwardEnum companyAward,
            bool isAwardsEditable
            )
        {
            Id = id;
            Year = year;
            Name = name;
            Source = source;
            Web = web;
            AwardType = companyAward;
            IsAwardsEditable = isAwardsEditable;
        }
    }
}