using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Redarbor.Company.WebUI.Models.Company.Vision
{
    public class VisionRankingDataModel
    {
        public int CurrentIdCompany { get; set; }
        public bool FindMyCompany { get; set; } = false;
        public bool FilterByIndustry { get; set; } = false;
        public bool BlockVisionSections { get; set; } = false;
        public List<VisionCompanyValuationDataModel> CompanyValuations = new List<VisionCompanyValuationDataModel>();

        [UIHint("defaultPager")]
        public PagerDataModel Pager { get; set; } = new PagerDataModel();
    }
}