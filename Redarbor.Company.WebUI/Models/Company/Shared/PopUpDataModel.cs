using System.Collections.Generic;

namespace Redarbor.Company.WebUI.Models.Company.Shared
{
    public class PopUpDataModel
    {
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string ExtraDynamicMessage { get; set; } = string.Empty;
        public string TitleBtnOk { get; set; } = string.Empty;
        public string TitleBtnKo { get; set; } = string.Empty;
        public string LiteralProcessing { get; set; } = string.Empty;
        public string LiteralError { get; set; } = string.Empty;    
        public bool HasInput { get; set; }
        public bool HasCheckBox { get; set; }
        public string DescriptionCheckbox { get; set; } = string.Empty;
        public bool HasTextArea { get; set; } = false;
        public bool HasButtonOk { get; set; } = true;
        public bool HasButtonOkNoJsProcessing { get; set; } = false;
        public string PlaceHolderInput { get; set; } = string.Empty;
        public bool HasCross { get; set; } = true;
        public bool HasLoading { get; set; } = false;

        public List<string> ItemList { get; set; } = new List<string>();
    }
}