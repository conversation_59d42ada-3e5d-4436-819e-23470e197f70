using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace Redarbor.Company.WebUI.Models.Company.Cart.Payments
{
    public class CompanyCardTokenModel
    {
        public string IdTokenEncrypted { get; set; }
        //public int CompanyId { get; set; }
        //public short PortalId { get; set; }
        public string CardType { get; set; }
        public string MaskedCardNumber { get; set; }
        public string CardName { get; set; }

        private int expiryMonth = 0;
        public int ExpiryMonth { set { expiryMonth = value; } get { return expiryMonth; } }

        private int expiryYear = 0;
        public int ExpiryYear { set { expiryYear = value; } get { return expiryYear; } }

        public bool IsExpiredCard { get; set; }

        public bool Default { get; set; }

        public short IdStatus { get; set; }

        public string ExpiredCard
        {
            get
            {
                return $"{expiryMonth}/{expiryYear}";
            }
        }

    }
}