using System;

namespace Redarbor.Company.WebUI.Models.Company.Configuration
{
    public class InvoiceDataModel
    {
        public int Id { get; set; } = 0;
        public int IdCompany { get; set; } = 0;
        public string CompanyName { get; set; } = string.Empty;
        public string CompleteAddress { get; set; } = string.Empty;
        public string ShowCompleteAddress => CompleteAddress.Contains("|")
            ? CompleteAddress.Replace("|", "<br />")
            : CompleteAddress;
        public int IdCountry { get; set; } = 0;
        public string Nit { get; set; } = string.Empty;
        public string ContactFullName { get; set; } = string.Empty;
        public string ContactEmail { get; set; } = string.Empty;
        public DateTime CreateDon { get; set; } = DateTime.MinValue;
        public int CreatedBy { get; set; } = 0;
        public DateTime UpdateDon { get; set; } = DateTime.MinValue;
        public DateTime DeleteDon { get; set; } = DateTime.MinValue;
        public int IdStatus { get; set; } = 0;
        public short PortalId { get; set; } = 0;
    }
}