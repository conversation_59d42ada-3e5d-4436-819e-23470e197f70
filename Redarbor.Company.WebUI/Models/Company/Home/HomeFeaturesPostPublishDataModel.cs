using Redarbor.Company.WebUI.Models.Company.Offer;
using Redarbor.Company.WebUI.Models.Company.Shared;
using Redarbor.Master.Entities.Enums;
using Redarbor.Master.Entities.Product;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Web.Mvc;

namespace Redarbor.Company.WebUI.Models.Company.Home
{
    public class HomeFeaturesPostPublishDataModel
    {
        public short OfferGroupToConsume { get; set; }
        
        public string NameOffer { get; set; } = string.Empty;
        public bool ShowKillerQuestionsFeature { get; set; }
        public bool ShowOfferExpressCheck { get; set; }
        public OfferPostPublishDataModel Offer { get; set; }

        [UIHint("ConvertToCompletePopUp")]
        public PopUpConvertToCompleteDataModel ConvertToCompletePopUp { get; set; } = new PopUpConvertToCompleteDataModel();
        public string OfferIdEncrypted { get; set; } = string.Empty;
        public string SubGroupToConsume { get; set; } = string.Empty;
        public short GoToCart { get; set; }

        public PostPublishProductsBoxesClass CompleteOffersProduct = new PostPublishProductsBoxesClass();
        public PostPublishProductsBoxesClass BasicOffersProduct = new PostPublishProductsBoxesClass();

        public List<PostPublishProductsBoxesClass> PostPublishProductsBoxes { get; set; } = new List<PostPublishProductsBoxesClass>();
        public class PostPublishProductsBoxesClass
        {
            public short GroupId;
            public short SubGroup;
            public string ProductName;
            public int AvailableUnits;
            public string ClassName;
            public string ProductToBuy;
            public bool IsUnlimited;
        }

        [UIHint("defaultPopUp")]
        public PopUpDataModel PublishOfferPopUp { get; set; } = new PopUpDataModel();

        public string StandardProduct { get; set; } = string.Empty;
        public string AdvanceProduct { get; set; } = string.Empty;
        public string PremiumProduct { get; set; } = string.Empty;
        public string ExpressProduct { get; set; } = string.Empty;
        public bool ShowPostPublishProductsBoxes { get; set; } = false;
        public bool DisableKillerQuestionsFeature { get; set; } = false;
        public bool DisableHighlightFeature { get; set; } = false;
        public bool DisableExpressFeature { get; set; } = false;
        public bool DisableUrgentFeature { get; set; } = false;
        public bool DisableFlashFeature { get; set; } = false;
        public bool DisableHiddenCompanyFeature { get; set; } = false;
        public bool DisableShowEmailContactFeature { get; set; } = false;
        public bool DisableShowPhoneContactFeature { get; set; } = false;
        public bool DisableShowAddressContactFeature { get; set; } = false;        
        public bool IsUpdateOffer {get;set;} = false;
        public short OfferSubgroup { get; set; }
        public string FirstFooterInfo { get; set; } = string.Empty;
        public int OfferProductSelected { get; set; }
        public List<ProductEntity> OfferProductsForSale { get; set; } = new List<ProductEntity>();
        public bool IsHighlightChecked { get; set; }
        public int HighlightAvailableUnits { get; set; }
        public int HighlightProductSelected { get; set; }
        public List<ProductEntity> HighlightProductsForSale { get; set; } = new List<ProductEntity>();        
        public bool IsUrgentChecked { get; set; }
        public int UrgentAvailableUnits { get; set; }
        public short UrgentProductSelected { get; set; }
        public List<ProductEntity> UrgentProductsForSale { get; set; } = new List<ProductEntity>();        
        public bool IsHiddenCompanyChecked { get; set; }
        public int HiddenCompanyAvailableUnits { get; set; }
        public short HiddenCompanyProductSelected { get; set; }
        public List<ProductEntity> HiddenCompanyProductsForSale { get; set; } = new List<ProductEntity>();
        public bool IsKQChecked { get; set; }
        public int KQAvailableUnits { get; set; }
        public short KQProductSelected { get; set; }
        public List<ProductEntity> KQProductsForSale { get; set; } = new List<ProductEntity>();
        public bool IsFlashChecked { get; set; }
        public int FlashAvailableUnits { get; set; }
        public short FlashProductSelected { get; set; }
        public List<ProductEntity> FlashProductsForSale { get; set; } = new List<ProductEntity>();
        public bool IsShowContactDataChecked { get; set; }
        public int ShowContactDataAvailableUnits { get; set; }
        public short ShowContactDataProductSelected { get; set; }
        public List<ProductEntity> ShowContactDataProductsForSale { get; set; } = new List<ProductEntity>();
        public bool HavePromoOfferAvailable { get; set; }

        public bool HasOfferMatchesNotification { get; set; }

        public short IdMatchSummaryNotificationType { get; set; } = (short)MatchSummaryNotificationType.None;
        public List<SelectListItem> MatchSummaryNotificationTypes { get; set; }

        public bool IsEssentialChecked { get; set; }

        public bool IsNonEssentialDisabled { get; set; }
        public bool IsExpressChecked { get; set; }
        public string BtnConvertToCompleteEncryptedName { get; set; }
        public bool FreemiumProductHaveKQ { get; set; }
        public bool AnyCheckboxIsChecked { get; set; }
    }
}