using Redarbor.Common.Entities.Configuration;
using Redarbor.Common.Entities.Enums;
using Redarbor.Company.WebUI.Enums;
using Redarbor.Company.WebUI.Helpers;
using System;

namespace Redarbor.Company.WebUI.Models.Company.Conversations
{
    public class MessageDataModel
    {
        public long IdConversation { get; set; }
        public int IdPortal { get; set; }
        public int IdApp { get; set; }
        public long IdMessage { get; set; }
        public int IdCompany { get; set; }
        public int IdOffer { get; set; }
        public int IdCandidate { get; set; }
        public int IdCv { get; set; }
        public long IdUserCompany { get; set; }
        public short UserType { get; set; }
        public string MessageBody { get; set; } = string.Empty;
        public DateTime DateAdd { get; set; } = DateTime.MinValue;
        public int StatusMessage { get; set; }
        public string Image { get; set; }
        public long IdUserCandidate { get; set; }
        public string DateShowHead(PortalConfig portalConfig)
        {
            string date;
            DateTime now = portalConfig.CurrentDateTimePortal;
            int months = now.Month - DateAdd.Month;
            int years = now.Year - DateAdd.Year;

            int days = (years == 0 && months == 0) ? (int)(now.Day - DateAdd.Day) : -1;

            string dateDefault = StringToolsHelper.GetLocalizedDate(DateAdd.Date, DateFormatEnum.ShortDateYY);

            switch (days)
            {
                case 0:
                    date = PageLiteralsHelper.GetLiteral("LIT_CONVER_TODAY", (short)PageEnum.Messages, portalConfig);
                    break;
                case 1:
                    date = PageLiteralsHelper.GetLiteral("LIT_ONE_DAY_AGO", (short)PageEnum.Messages, portalConfig);
                    break;
                case 2:
                case 3:
                case 4:
                case 5:
                case 6:
                    date = GetDayOfWeek(portalConfig.CurrentDateTimePortal.AddDays(-days), dateDefault, portalConfig);
                    break;
                default:
                    date = dateDefault;
                    break;
            }
            return date;
        }

        private string GetDayOfWeek(DateTime date, string dateDefault, PortalConfig portalConfig)
        {
            switch (date.DayOfWeek)
            {
                case DayOfWeek.Monday:
                    return PageLiteralsHelper.GetLiteral("LIT_CONVER_MONDAY", (short)PageEnum.Messages, portalConfig);
                case DayOfWeek.Tuesday:
                    return PageLiteralsHelper.GetLiteral("LIT_CONVER_TUESDAY", (short)PageEnum.Messages, portalConfig);
                case DayOfWeek.Wednesday:
                    return PageLiteralsHelper.GetLiteral("LIT_CONVER_WEDNESDAY", (short)PageEnum.Messages, portalConfig);
                case DayOfWeek.Thursday:
                    return PageLiteralsHelper.GetLiteral("LIT_CONVER_THURSDAY", (short)PageEnum.Messages, portalConfig);
                case DayOfWeek.Friday:
                    return PageLiteralsHelper.GetLiteral("LIT_CONVER_FRIDAY", (short)PageEnum.Messages, portalConfig);
                case DayOfWeek.Saturday:
                    return PageLiteralsHelper.GetLiteral("LIT_CONVER_SATURDAY", (short)PageEnum.Messages, portalConfig);
                case DayOfWeek.Sunday:
                    return PageLiteralsHelper.GetLiteral("LIT_CONVER_SUNDAY", (short)PageEnum.Messages, portalConfig);
            }

            return dateDefault;
        }

        public string HourShow(PortalConfig portalConfig) => DateAdd.ToString("HH:mm");        
    }
}

