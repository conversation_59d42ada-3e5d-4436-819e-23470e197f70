using Redarbor.Common.Entities.Configuration;
using Redarbor.Company.WebUI.Models.Company.Product;
using Redarbor.Master.Entities.Enums;
using System.Collections.Generic;
using System.Linq;

namespace Redarbor.Company.WebUI.Models.Company.Offer
{
    public class PublishedDataModel
    {
        public string GeneralTitleLiteralKey { get; set; } = string.Empty;
        public bool ShowExclusionView { get; set; }
        public string OfferIdEncrypted { get; set; } = string.Empty;
        public string ProductIdEncrypted { get; set; } = string.Empty;
        public ProductDataModel PromotionFlash { get; set; } = new ProductDataModel();
        public int CompanyGroupId { get; set; }
        public bool HasFeatureAvailableUnitsPack { get; set; }
        public int MinimPercentPromoVoucher { get; set; }
        public string NamePackConsumed { get; set; }

        public int AvailableUnitsCv { get; set; }
        public string BBDDFolderEncrypted { get; set; } = string.Empty;

        public List<ProductDataModel> ListProductDataModel { get; set; }

        public bool CanShowLandingForFreemiumPublished(PortalConfig portalConfig)
            => (ProductGroupsEnum)CompanyGroupId == ProductGroupsEnum.Freemium
            && portalConfig.AEPortalConfig.ShowFreemiumPublishedLanding
            && ListProductDataModel.Any(x => x.SubGroupId == (int)ProductSubGroupsEnum.Standard)
            && ListProductDataModel.Any(x => x.SubGroupId == (int)ProductSubGroupsEnum.Premium);

        public  bool HasPrime { get; set; }
    }
}