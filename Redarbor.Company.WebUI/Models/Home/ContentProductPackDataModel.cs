using Redarbor.Common.Entities.Configuration;
using Redarbor.Common.Entities.Enums;
using Redarbor.Company.WebUI.Helpers;
using Redarbor.Company.WebUI.Models.Company.Product;
using System.Collections.Generic;

namespace Redarbor.Company.WebUI.Models.Home
{
    public class ContentProductPackDataModel
    {
        public ProductDataModel ProductDataModel { get; set; } = new ProductDataModel();
        public string ButtonText(PortalConfig portalConfig)
        {
            return ProductDataModel.Price != 0
                ? PageLiteralsHelper.GetLiteral("LIT_COMPRAR", (short)PageEnum.HomeMasterCompany, portalConfig)
                : PageLiteralsHelper.GetLiteral("LIT_PUBLICAR", (short)PageEnum.HomeMasterCompany, portalConfig);
        }

        public List<ContentProductPackFeatureDataModel> LstProductAmbit { get; set; } = new List<ContentProductPackFeatureDataModel>();
        
        
    }

    
}
