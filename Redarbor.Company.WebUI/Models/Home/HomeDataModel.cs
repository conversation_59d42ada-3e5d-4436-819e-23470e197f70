using Redarbor.Company.WebUI.Models.Company.Product;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Web.Mvc;

namespace Redarbor.Company.WebUI.Models.Home
{
    public class HomeDataModel
    {
        public List<SelectListItem> CountriesLocation { get; set; }
        public string CountryLocationId { get; set; }
        public List<SelectListItem> Categories { get; set; }
        public string CategoryId { get; set; }
        public CvsCountersDataModel CvsCountersDataModel { get; set; }
        [UIHint("ContentProductPack")]
        public int ActivesCompanies { get; set; }
        public int AppliesDaily { get; set; }
        public List<ContentProductPackDataModel> LstContentProductPack { get; set; }
        public string TextFilterCVs { get; set; } = string.Empty;
        public bool ShowBanner { get; set; }
        public string UrlBlog { get; set; }
        [UIHint("LandingProducts")]
        public LandingProductsDataModel LandingProductsDataModel { get; set; } = new LandingProductsDataModel();
        [UIHint("productContentGroupPopUp")]
        public LandingProductsDataModel LandingProductsPopUpDataModel { get; set; } = new LandingProductsDataModel();
        public bool ShowCompaniesTab { get; set; }
        public bool ShowSalariesTab { get; set; }
        public string UrlRecruitmentSherlockByPortal { get; set; }
        public LoginDataModel LoginDataModel { get; set; } = new LoginDataModel();
        public List<ProductDataModel> ProductsAndServicesProducts { get; set; } = new List<ProductDataModel>();
        public bool IsProductsAndServicesLanding { get; set; }
    }
}