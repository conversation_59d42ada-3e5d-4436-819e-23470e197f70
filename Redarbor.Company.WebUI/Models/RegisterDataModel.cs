using Redarbor.Company.WebUI.Models.Company;
using System.ComponentModel.DataAnnotations;

namespace Redarbor.Company.WebUI.Models
{
    public class RegisterDataModel
    {   
        public LoginDataModel LoginDataModel { get; set; }
        public CompanyDataModel CompanyDataModel { get; set; }        
        public bool IsCheckedAdditionalConditions { get; set; }
        [UIHint("tooltipConditions")]
        public string ToolTipConditions { get; set; }
	}
}