using Redarbor.Company.WebUI.Models.Company.Candidate.Competences;
using System.Collections.Generic;
using System.Web.Mvc;

namespace Redarbor.Company.WebUI.Models.CandidateComparator
{
    public class CandidateComparatorDataModel
    {
        public int FolderId { get; set; }
        public string FolderName { get; set; } = string.Empty;
        public string MatchIdEncrypted { get; set; } = string.Empty;
        public string Photo { get; set; } = string.Empty;
        public int AdequacyPoints { get; set; }
        public bool IsHigherAdequacy { get; set; }
        public bool IsCandidateRecommended { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Surname { get; set; } = string.Empty;
        public RichPropertyModel<int> Age { get; set; } = new RichPropertyModel<int>();
        public RichPropertyModel<string> Localization { get; set; } = new RichPropertyModel<string>();
        public RichPropertyModel<string> City { get; set; } = new RichPropertyModel<string>();
        public List<CompetenceDataModel> CompetenceDataModels { get; set; } = new List<CompetenceDataModel>();
        public bool HasCompetencesTest { get; set; }
        public int KillerQuestionsPunctuation { get; set; }
        public bool HasKillerQuestions { get; set; }
        public int KQExcluding { get; set; }
        public int KQScoreAvg { get; set; }
        public int KQScoreMax { get; set; }
        public RichPropertyModel<string> Salary { get; set; } = new RichPropertyModel<string>();
        public RichPropertyModel<string> YearsAndMonthsExperience { get; set; } = new RichPropertyModel<string>();
        public string LastCharge { get; set; } = string.Empty;
        public string LastCompany { get; set; } = string.Empty;
        public string MaxStudies { get; set; } = string.Empty;
        public string TitleStudies { get; set; } = string.Empty;
        public RichPropertyModel<string> Language { get; set; } = new RichPropertyModel<string>();
        public RichPropertyModel<string> ResidenceChange { get; set; } = new RichPropertyModel<string>();
        public RichPropertyModel<string> DisponibilityTravel { get; set; } = new RichPropertyModel<string>();
        public RichPropertyModel<string> DriveLicenses { get; set; } = new RichPropertyModel<string>();
        public RichPropertyModel<string> Disability { get; set; } = new RichPropertyModel<string>();
        public string LastUpdate { get; set; } = string.Empty;
        public List<SelectListItem> MatchFolders { get; set; } = new List<SelectListItem>(); 
        public int MatchFolderIdsSelected { get; set; }
        public bool HasFolderDetails { get; set; }
        public int DistanceToOfferLocation { get; set; }
    }
}