namespace Redarbor.Company.WebUI.Models
{
    public class HealthCheckReturnModel
    {

        private bool statusOk = true; 
        private const string STATUS_OK = "OK";
        private const string STATUS_KO = "KO";


        public HealthCheckReturnModel(string i_version, string i_environment, int i_app_id)
        {
            BuildVersion = i_version;
            Environment = i_environment;
            AppId = i_app_id;
        }

        public string Status { get { return statusOk ? STATUS_OK : STATUS_KO; } }
        public string BuildVersion { get; }
        public string Environment { get; }
        public int AppId { get; }
        public string PlainText()
        {
            return $"Status:{Status}\nBuildVersion:{BuildVersion}\nEnvironment:{Environment}\nAppId:{AppId}";
        }

    }
}