@model Redarbor.Company.WebUI.Models.Company.Home.HomeCvsDataModel
@using Redarbor.Company.WebUI.Helpers.Html
@using Redarbor.Company.WebUI.Helpers
@using Redarbor.Common.Entities.Enums
@using Redarbor.Master.Entities.Enums

@{
    Layout = "~/Views/Shared/Layouts/_LayoutMasterNewPem.cshtml";
    int pageId = Html.SetCurrentPage((int)PageEnum.PackCVSearch);
    var portalConfig = PortalConfigHelper.GetPortalConfiguration();
    var companyCredentials = SecurityHelper.GetCompanyCredentials();
    var candidateCardDesign = portalConfig.AEPortalConfig.FindCVsNewCard ? "_CandidateListItemBNewPem" : "_CandidateListItemNewPem";
}

@section TitleSection{
    <title>@PageLiteralsHelper.GetLiteral("META_DESC_INICIO", pageId, portalConfig)</title>
}

@section CustomScriptsFooterSection{
    @Scripts.Render("~/bundles/js/cvlistnewpem")

    <script type="text/javascript">

        $(document).ready(function () {
            $('#UserDate').val(getFormattedDate());
        });        

        var configCvList = {
            urlActionFormAjaxUpdateFolder: '@Url.Action("UpdateFolder","CompanyCvs")',
            urlActionFormAjaxAddFolder: '@Url.Action("AddFolder", "CompanyCvs")',
            urlActionFormAjaxRemoveCvs: '@Url.Action("DelecteCvsFromFolder","CompanyCvs")',
            urlActionFormAjaxRemoveFolder: '@Url.Action("DeleteFolder", "CompanyCvs")',
            urlActionFormAjaxDeleteFilter: '@Url.Action("DeleteSaveFilter", "CompanyCvs")',
            UrlActionWithoutFilter: '@Url.Action("Index","CompanyCvs", new { idFolder = Model.MultifiltersDataModel.FolderSelected })',
            isShowFolders: '@Model.MultifiltersDataModel.ShowCustomFoldersFilter',
            literalErrorInputNameFolder: '@PageLiteralsHelper.GetLiteral("LIT_PLS_INPUT_NAME", pageId, portalConfig)',
            urlActionFormAjaxAddCvsFolder: '@Url.Action("AddCvsToFolder","CompanyCvs")',
            idFolderEncrypted: '@Model.MultifiltersDataModel.FolderSelected',
            passLimitFolder: '@((short)FolderCustomStatusResponseEnum.PASS_LIMIT)',
            notCanAddFolder: '@((short)FolderCustomStatusResponseEnum.NOT_CAN_ADD_CVS)',
            notAllCvsFolder: '@((short)FolderCustomStatusResponseEnum.NOTALLCVS)',
            AllCvsFolder: '@((short)FolderCustomStatusResponseEnum.ALLCVS)',
            existOrNotViewedFolder: '@((short)FolderCustomStatusResponseEnum.EXIST_OR_NOT_VIEWED)',
            noNameFolder: '@((short)FolderCustomStatusResponseEnum.NO_NAME)',
            literalErrorInputName: '@PageLiteralsHelper.GetLiteral("LIT_PLS_INPUT_NAME",pageId, portalConfig)',
            literalErrorMaxFolder: '@PageLiteralsHelper.GetLiteral("LIT_MAX_FOLDERS", pageId,portalConfig)',
            literalErrorNotRemoveFolder: '@PageLiteralsHelper.GetLiteral("LIT_NOT_UPDATED", pageId, portalConfig)',
            doautoscroll: '@(Model.DoAutoScroll.ToString().ToLower())',
            limitSaveFilters: '@(Model.MultifiltersDataModel.SavedFilters.Count < 20)',
            errorSaveFilter: '@PageLiteralsHelper.GetLiteral("LIT_MANDATORY", pageId, portalConfig)',
            literalProcessing: '@PageLiteralsHelper.GetLiteral("LIT_PROCESSING", pageId, portalConfig)',
            idPortal: '@portalConfig.PortalId',
            idCompany: '@Model.IdCompany',
            isFiltered: '@Model.MultifiltersDataModel.IsFiltered'
        };

        InitializeCvList(configCvList);

        const cvList = new CvList(configCvList);

    </script>
}
<main>

    @if (Model.HomeCvsPackAndBasicDataModel.ItsBBDDcvProdActivated && Model.TotalAllowedCvVisualization == 0)
    {
        <div class="box_border bAll1_blue mbB pAllB">
            <div class="dFlex vm_fx">
                <div class="prB"><img src="@Url.Content(string.Format("{0}img/buscador-candidatos.svg", portalConfig.PrefixPathImage, portalConfig.countrycode.ToLower(), portalConfig.SufixPathImage))"></div>
                <div class="w100">
                    <p class="fc_aux fs13 mb5">@PageLiteralsHelper.GetLiteral("LIT_BBDDCV_NOT_ACCESS", pageId, portalConfig)</p>
                    <p>@Html.Raw(PageLiteralsHelper.GetLiteral("LIT_BBDDCV_BANNER", pageId, portalConfig)) </p>
                </div>
                <a class="b_primary" style="white-space:nowrap;" href="@Url.Action("Index", "MultiPurchaseCart" , new { prod=EncryptationHelper.Encrypt(Model.IdProductBBDDcv.ToString()),
                                        p = EncryptationHelper.Encrypt(((short)PageEnum.PackCart).ToString()),
                                        btn = EncryptationHelper.Encrypt(((short)TpvButtonOriginEnum.CompanyCVsBBDDcvList).ToString())
                                    })">@PageLiteralsHelper.GetLiteral("LIT_PURCHASE_BBDDCV_PROD", pageId, portalConfig)</a>
                                </div>
                            </div>
                        }


    @using (Html.BeginForm("Index", "CompanyCvs", FormMethod.Post, new { id = "searchCvs" }))
    {
        @Html.AntiForgeryToken()
        @Html.HiddenFor(m => m.HomeCvsPackAndBasicDataModel.ItsBBDDcvProdActivated)
        @Html.HiddenFor(m => m.UserDate)

        //if (Model.MultifiltersDataModel.IsFiltered ||
        //    (!string.IsNullOrWhiteSpace(Model.MultifiltersDataModel.FolderSelected) && Model.MultifiltersDataModel.FolderSelected != "0"))
        //{
            @Html.EditorFor(m => m.MultifiltersDataModel, "BBDDManageFoldersNewPem", new { Model.IsNuggetCv, Model.IsNewFiltersActive })
            @Html.HiddenFor(m => m.Pager.PageSelected)
            @Html.HiddenFor(m => m.MultifiltersDataModel.NameNewSaveFilter, new { id = "NameNewSaveFilter", name = "NameNewSaveFilter" })
            @Html.EditorFor(m => m.MultifiltersDataModel, "CompanyCvsFilters", new { Model.IsNuggetCv, Model.IsNewFiltersActive })
        @*}
        else
        {
            @Html.HiddenFor(m => m.MultifiltersDataModel.IsCvVisualitzed)

            <div class="box tc">
                <span class="icon i_search_CV button xxl mAuto"></span>
                <p class="fs18 mtB">@Html.Raw(PageLiteralsHelper.GetLiteral("LIT_BBDDCV_WELCOME_TIT", pageId, portalConfig))</p>
                <p class="fs16 mtB mbB">@Html.Raw(PageLiteralsHelper.GetLiteral("LIT_BBDDCV_WELCOME_SUB", pageId, portalConfig))</p>
                <div class="pAllB bg_brand_light mB_neg mbB_neg bl2_white bAll2_white border_radius_b">
                    <div class="field_group w80 mAuto mt25 w100_m mt0_m mb0_m dFlex">
                        <div class="field_input_icon fs16 w40 mlAuto mb10">
                            <div class="cont">
                                <span class="icon i_find fc_link"></span>
                                @Html.TextBoxFor(m => Model.MultifiltersDataModel.SearchText, new { placeholder = PageLiteralsHelper.GetLiteral("LIT_BBDDCV_WELCOME_PH1", pageId, portalConfig) })
                            </div>
                        </div>
                        <div class="field_select_icon w40">
                            <div class="cont">
                                <span class="icon i_address fc_link"></span>
                                @Html.DropDownListFor(m => m.MultifiltersDataModel.SelectedLocalization, Model.MultifiltersDataModel.Localizations, PageLiteralsHelper.GetLiteral("LIT_SELECT_LOCALIZATION", pageId, portalConfig))
                            </div>
                        </div>
                        <div class="mb10 w16 end w100_m mb0_m mrAuto">
                            <a href="#" id="js_cvsPreFilterButton" class="b_primary w100">@Html.Raw(PageLiteralsHelper.GetLiteral("LIT_BBDDCV_WELCOME_FIND", pageId, portalConfig))</a>
                        </div>
                    </div>
                    <div class="w80 mAuto tl">
                        <div id="divErrorSearch" class="box_error small pAll5 dIB tl hide">@PageLiteralsHelper.GetLiteral("LIT_ERROR_SEARCH", pageId, portalConfig)</div>
                    </div>
                </div>
            </div>
        }*@

        if (portalConfig.ShowMultifiltersTags)
        {
            @Html.EditorFor(m => m.MultifiltersDataModel, "CvsMultifilterTagsNewPem")
        }
    }

    @*@if (Model.MultifiltersDataModel.IsFiltered ||
            (!string.IsNullOrWhiteSpace(Model.MultifiltersDataModel.FolderSelected) && Model.MultifiltersDataModel.FolderSelected != "0"))
    {*@
        <div class="fr w75_mB">
            <header class="header_block full_m">
                <div class="mrAuto posRel title_m">
                    <h1 class="w70_m">
                        <span class="fs24 fwB mr5 fs16_m">@Model.TotalRowsFormatted</span>
                        @PageLiteralsHelper.GetLiteral("TITLE_PERSONAS", pageId, portalConfig)
                    </h1>
                </div>

                @if (Model.HomeCvsPackAndBasicDataModel.ItsBBDDcvProdActivated)
                {
                    if (Model.TotalAllowedCvVisualization > 0)
                    {
                        <div class="sub_box pAll10 mb0 mlAuto">
                            <p><strong>@PageLiteralsHelper.GetLiteral("LIT_BBDDCV_SEARCH", pageId, portalConfig)</strong> @PageLiteralsHelper.GetLiteral("LIT_AVAILABLE_ACCES", pageId, portalConfig) <strong>@(Model.TotalAllowedCvVisualizationFormatted)</strong>/@(Model.InitialBBDDCvVisualization) @PageLiteralsHelper.GetLiteral("LIT_HDV", pageId, portalConfig)</p>
                        </div>
                    }
                }
                else
                {
                    <div class="sub_box pAll10 mb0 mlAuto">
                        @if (Model.TotalAllowedCvVisualization != int.MaxValue)
                        {
                            <p>@string.Format(PageLiteralsHelper.GetLiteral("LIT_X_HDV_DISPONIBLES", pageId, portalConfig), Model.TotalAllowedCvVisualizationFormatted)</p>
                        }
                        else
                        {
                            <p>@Html.Raw(PageLiteralsHelper.GetLiteral("LIT_UNLIMITED_ACCESS", pageId, portalConfig))</p>
                        }
                    </div>
                }
            </header>

            <section class="cm-9 grid bgBlueMbl" id="js_sectFilter">

                @if (!Model.Candidates.Any())
                {
                    <div class="box_info dFlex vm_fx fs16">
                        <span class="icon i_info mr10"></span>
                        @PageLiteralsHelper.GetLiteral("H1_NO_CANDIDATOS", pageId, portalConfig)
                    </div>
                }

                @if (Model.Candidates.Any() && Model.MultifiltersDataModel.CustomFolders.Any())
                {
                    <div class="field_checkbox mb20 ml20 bb1_m bg_base_light_m mb0_m ml0_m pAll15_m mtB ">
                        <label class="checkbox mb0">
                            <input type="checkbox" id="chk_select_all" onclick="selectAllCandidates(this)">
                            <span class="input"></span>
                            <span>@PageLiteralsHelper.GetLiteral("LIT_SELECT_ALL", pageId, portalConfig)</span>
                        </label>
                    </div>
                }

                <div class="mbB mtB mb0_m">
                    @if (Model.Pager.PageSelected > portalConfig.max_pages_pagination_cvs)
                    {
                        <div class="box_info dFlex vm_fx fs16">
                            <span class="icon i_info mr10"></span>
                            <div>
                                <p>@PageLiteralsHelper.GetLiteral("MAX_PAGES_LIT_MAX_PAGES_VIEW", pageId, portalConfig)</p>
                                <p>@PageLiteralsHelper.GetLiteral("MAX_PAGES_LIT_COMMENT", pageId, portalConfig)</p>
                            </div>
                        </div>
                    }
                    else
                    {
                        foreach (var candidate in Model.Candidates)
                        {
                            Html.RenderPartial(candidateCardDesign, candidate, new ViewDataDictionary {
                                {"portalConfig", portalConfig},
                                {"showCommentFilter", Model.MultifiltersDataModel.ShowCommentFilter},
                                {"showRatingFilter", Model.MultifiltersDataModel.ShowRatingFilter},
                                {"CustomFolders", Model.MultifiltersDataModel.CustomFolders.Any()},
                                {"queryEncrypted", Model.MultifiltersDataModel.ListSearchName},
                                {"categoriesEncrypted", Model.MultifiltersDataModel.IdsProfesionalCategoriesSelected},
                                {"provincesEncrypted", Model.MultifiltersDataModel.IdsLocalizationsSelected}
                            });
                        }
                    }
                </div>

                @Html.DisplayFor(i => i.Pager, new { portalConfig, formIdToSubmit = "searchCvs" })

            </section>
        </div>
   @* }*@

    @Html.Partial("PopUps/_GenericInfoPopUp", new ViewDataDictionary { { "portalConfig", portalConfig },
                                                                       { "popupid", "selectcvpopup" },
                                                                       { "tit", PageLiteralsHelper.GetLiteral("LIT_SELECT_CANDIDATES", pageId, portalConfig)},
                                                                       { "desc", PageLiteralsHelper.GetLiteral("LIT_SELECT_CANDIDATES_DESC", pageId, portalConfig)} })

    @Html.Partial("PopUps/_GenericInfoPopUp", new ViewDataDictionary { { "portalConfig", portalConfig },
                                                                       { "popupid", "popupNotViewCv" },
                                                                       { "tit", PageLiteralsHelper.GetLiteral("LIT_CV_BLOQUED", pageId, portalConfig)},
                                                                       { "desc", PageLiteralsHelper.GetLiteral("LIT_CV_BLOQUED_DESCRIPTION", pageId, portalConfig)} })

    @Html.Partial("PopUps/_GenericInfoPopUp", new ViewDataDictionary { { "portalConfig", portalConfig },
                                                                   { "popupid", "popupNotAllCv" },
                                                                   { "tit", PageLiteralsHelper.GetLiteral("LIT_NOT_ALL_CV_TITLE", pageId, portalConfig)},
                                                                   { "desc", PageLiteralsHelper.GetLiteral("LIT_NOT_ALL_CV_DESCRIPTION", pageId, portalConfig)} })

    @Html.Partial("PopUps/_GenericInfoPopUp", new ViewDataDictionary { { "portalConfig", portalConfig },
                                                               { "popupid", "popupAdvert" },
                                                               { "tit", PageLiteralsHelper.GetLiteral("LIT_ADVERT", pageId, portalConfig)},
                                                               { "desc", PageLiteralsHelper.GetLiteral("LIT_ADVERT_DESCRIPTION", pageId, portalConfig)} })

    @Html.Partial("PopUps/_GenericInfoPopUp", new ViewDataDictionary { { "portalConfig", portalConfig },
                                                           { "popupid", "PopUpSelectCvs" },
                                                           { "tit", PageLiteralsHelper.GetLiteral("LIT_SELECT_CANDIDATES", pageId, portalConfig)},
                                                           { "desc", PageLiteralsHelper.GetLiteral("LIT_SELECT_CANDIDATES_DESC", pageId, portalConfig)} })



    @if (Model.MultifiltersDataModel.ShowCustomFoldersFilter || Model.MultifiltersDataModel.ShowSaveFilter)
    {
        @Html.Partial("PopUps/_DeleteCVsFilter", new ViewDataDictionary { { "portalConfig", portalConfig } })
        @Html.Partial("PopUps/_CreateCVsFilterPopUp", new ViewDataDictionary { { "portalConfig", portalConfig } })
    }
</main>
