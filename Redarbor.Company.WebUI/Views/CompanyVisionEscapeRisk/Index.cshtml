@model Redarbor.Company.WebUI.Models.Company.Vision.VisionEscapeRiskModel
@using Redarbor.Common.Entities.Enums
@using Redarbor.Company.WebUI.Helpers
@using Redarbor.Company.WebUI.Models.Company.Vision

@{
    Layout = "~/Views/Shared/Layouts/_LayoutMaster.cshtml";
    short pageId = (int)PageEnum.CompanyEscapeRisk;
    var portalConfig = PortalConfigHelper.GetPortalConfiguration();
}

@section TitleSection{
    <title>@PageLiteralsHelper.GetLiteral("LIT_ESCAPERISK", pageId, portalConfig)</title>
}

@section CustomScriptsFooterSection{
    @Scripts.Render("~/bundles/js/vision/escaperisk")
}

<div class="fdo_e clear pt0i">
    <div class="container">
        @if (!Model.HaveVisionFeature)
        {
            VisionBannerModel visionBannerModel = new VisionBannerModel(CompanyHelper.GetCompanyComercial().ComercialEmail);
            Html.RenderPartial("_VisionBanner", visionBannerModel, new ViewDataDictionary { { "portalConfig", portalConfig } });
        }
        <div class="pt20i gridOp bgBlueMbl">
            <div class="fl w_100 w_100r">
                <div class="boxWhite boxShadow p0 fl w_100 contenido p30 p15_r mb15_r">
                    <div class="fl100">
                        @if (Model.PhWithoutData)
                        {
                            <div class="fl100">
                                <div class="w100 val_title">@PageLiteralsHelper.GetLiteral("LIT_ESCAPERISK", pageId, portalConfig)</div>
                                <div class="hA orange mt15">
                                    <div class="imgA">
                                        <span class="icoI">
                                            <span class="icon icoInfo"></span>
                                        </span>
                                    </div>
                                    <div class="infoA plr15_r">
                                        <p class="fs16 fw_b">@PageLiteralsHelper.GetLiteral("LIT_ESCAPERISK_DATOS_INSU_HE", pageId, portalConfig)</p>
                                        <p class="fs14 fc80">@PageLiteralsHelper.GetLiteral("LIT_ESCAPERISK_DATOS_INSU", pageId, portalConfig)</p>
                                    </div>
                                </div>
                            </div>
                        }

                        @if (Model.PhAllGraphics)
                        {
                            <div class="fl100 p30 p15_r">
                                <div class="w100 val_title">@PageLiteralsHelper.GetLiteral("LIT_ESCAPERISK", pageId, portalConfig)</div>

                                <div class="w_100 dTable">
                                    @if (Model.PhEscapeRisk)
                                    {
                                        <div class="w_70 pt20 tablec vmid">
                                            <div class="grafH">
                                                <div class="barraF">
                                                    <div class="barraI" style="width:@Model.EscapeRiskPercentageDescription"></div>
                                                    <div class="barraO"></div>
                                                </div>
                                                <div class="@String.Format("mark{0}", Model.CurrentCSSClass)" style="left: @Model.CurrentMarkIndex"></div>
                                            </div>
                                            <div class="w_100 dTable mb20">
                                                <span class="tablec w_33 tc low">Bajo</span>
                                                <span class="tablec w_33 tc med">Medio</span>
                                                <span class="tablec w_33 tc hig">Alto</span>
                                            </div>
                                        </div>
                                        <div class="w_30 tablec p10 vmid fc_gray">
                                            <p>@PageLiteralsHelper.GetLiteral("LIT_ESCAPERISK_BAS", pageId, portalConfig)</p>
                                        </div>
                                    }

                                    @if (Model.PhEscapeRiskBloqued)
                                    {
                                        <img class="w_100 showPopUpVision cp" title="@PageLiteralsHelper.GetLiteral("LIT_TITLE_CONTACT_CA", pageId, portalConfig)" src="@Url.Content(String.Format("{0}img/grado-riesgo-desenfoc.jpg{1}", portalConfig.PrefixPathImage, portalConfig.SufixPathImage))">
                                    }
                                    else
                                    {
                                        if (!Model.isActionPermitedByRole)
                                        {
                                            <img class="w_100 showPopUpBS cp" title="@PageLiteralsHelper.GetLiteral("LIT_TITLE_CONTACT_CA", pageId, portalConfig)" src="@Url.Content(String.Format("{0}img/grado-riesgo-desenfoc.jpg{1}", portalConfig.PrefixPathImage, portalConfig.SufixPathImage))">
                                        }
                                    }
                                </div>
                            </div>

                            if (Model.PhEscapeRiskEvolution)
                            {
                                <div id="phEscapeRiskEvolution" class="fl100 bt1 p30 p15_r">
                                    <div class="w_100">@PageLiteralsHelper.GetLiteral("LIT_ESCAPERISK_EVO", pageId, portalConfig)</div>

                                    @if (Model.PhEscapeRiskEvoVisible)
                                    {
                                        <input type="hidden" id="canvas_escape_risk_history_data" value="@Model.CanvasEscapeRiskHistoryData" />
                                        <input type="hidden" id="canvas_escape_risk_history_label" value="@Model.CanvasEscapeRiskHistoryLabel" />
                                        <input type="hidden" id="canvas_escape_risk_y_axe_step_size" value="@Model.CanvasEscapeRiskYAxeStepSize" />
                                        <div class="fl100 mt15">
                                            <canvas id="canvas_escape_risk_history" class="w_100 h_auto"></canvas>
                                        </div>
                                    }

                                    @if (Model.PhEscapeRiskEvoVisibleBLoqued)
                                    {
                                        <img class="w_100 showPopUpVision cp" title="@PageLiteralsHelper.GetLiteral("LIT_TITLE_CONTACT_CA", pageId, portalConfig)" src="@Url.Content(String.Format("{0}img/riesgo-desenfoc.jpg{1}", portalConfig.PrefixPathImage, portalConfig.SufixPathImage))">
                                    }
                                    else
                                    {
                                        if (!Model.isActionPermitedByRole)
                                        {
                                            <img class="w_100 showPopUpBS cp" title="@PageLiteralsHelper.GetLiteral("LIT_TITLE_CONTACT_CA", pageId, portalConfig)" src="@Url.Content(String.Format("{0}img/riesgo-desenfoc.jpg{1}", portalConfig.PrefixPathImage, portalConfig.SufixPathImage))">
                                        }
                                    }
                                </div>
                            }
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
