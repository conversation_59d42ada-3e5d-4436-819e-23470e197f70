@using Redarbor.Company.WebUI.Helpers;
@using Redarbor.Common.Entities.Enums;
@using System.Web.Optimization;
@using Redarbor.Core.Kpi.Contracts.ServiceLibrary.Enums;
@model Redarbor.Company.WebUI.Models.SimplifiedRegister.ImportOfferModel

@{
    Layout = null;
    short pageId = (short)PageEnum.OffersPreregister;
    var portalConfig = PortalConfigHelper.GetPortalConfiguration();
    short kpiNotNow = (short)KpiEnum.CT_AE_COMPANIES_THAT_HAVE_NOT_IMPORTED_OFFERS;
}


<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="robots" content="noindex, nofollow" />
    <title>@PageLiteralsHelper.GetLiteral("HEAD_TITLE_PRE_REGISTER_COMPANY", pageId, portalConfig)</title>

    @Scripts.Render("~/bundles/js/jquery")
    @Scripts.Render("~/bundles/js/offerPreRegister")
    @Styles.Render(string.Format("~/bundles/css/pem_new{0}", portalConfig.staticVirtualBundle))

    <script type="text/javascript">
        $(document).ready(function () {
            InitializeGTM('@portalConfig.GoogleTagManager')
        });
    </script>
</head>
<body>
    <header>
        <!-- Menu tools -->
        <div class="menu_tools log">
            <div class="container">
                <!-- Logotipo -->
                <span class="logo">
                    <img srcset="@Url.Content(string.Format("{0}img/logoct_neg.svg{1}", portalConfig.PrefixPathImage, portalConfig.SufixPathImage))" alt="@PageLiteralsHelper.GetLiteral("ALT_IMG_LOGO_PORTAL", pageId, portalConfig)" class="cep_logo" />
                </span>
                <!-- Fin: Logotipo -->
            </div>
            <div class="overlay hide"></div>
        </div>
    </header>
    <main class="mb0">
        @Html.AntiForgeryToken()
        <div class="box mtB">
            <div class="dFlex vm_fx fx_wrap mbB">
                <div>
                    <p class="fs24 mb5"><strong>@PageLiteralsHelper.GetLiteral("LIT_PRE_OFFER_IMPORT_REVIEW", pageId, portalConfig)</strong></p>
                    <p class="fs16">@PageLiteralsHelper.GetLiteral("LIT_PRE_OFFER_IMPORT_CHOOSE", pageId, portalConfig)</p>
                </div>
                <button id="BtnGoHomeImportOffers" class="b_primary_inv big mlAuto mr15 w100_m mt10_m">@PageLiteralsHelper.GetLiteral("LIT_PRE_OFFER_CONSENT_NOT_NOW", pageId, portalConfig)</button>
                <button id="ImportOfferBtn" class="b_primary big w100_m mt10_m">@PageLiteralsHelper.GetLiteral("LIT_PRE_OFFER_IMPORT_BUTTON", pageId, portalConfig)</button>
            </div>
            <div class="tag fc_aux bg_brand_light mbB">
                <span id="tagCountOffers">

                </span>
            </div>
            <div id="ErrorGeneric" class="box_error small mbB hide">@PageLiteralsHelper.GetLiteral("LIT_PRE_OFFER_IMPORT_ERROR", pageId, portalConfig)</div>
            <div role="region" aria-labelledby="HeadersCol" tabindex="0" class="fixed_column_wrapper" id="table_offers">
                @{Html.RenderPartial("PreRegister/_ImportOffersTable", Model.ImportOffers, new ViewDataDictionary {
                        { "portalConfig", portalConfig }
                    }); }
            </div>
        </div>
    </main>

    @{Html.RenderPartial("PreRegister/_PopUpImportOfferDetail", new Redarbor.Company.WebUI.Models.SimplifiedRegister.PopUpImportOfferDetailModel(), new ViewDataDictionary {
                        { "portalConfig", portalConfig }
                    });}

    @{Html.RenderPartial("PreRegister/_PopUpImportEndMessage", null, new ViewDataDictionary {
                     { "portalConfig", portalConfig },{"urlReturn", Model.UrlReturn}
                 });}

    @{Html.RenderPartial("PreRegister/_PopUpImportOfferValidateDetail", null, new ViewDataDictionary {
                     { "portalConfig", portalConfig },{"urlReturn", Model.UrlReturn}
                 });}

    @Scripts.Render("~/bundles/js/required")
    <script type="text/javascript">
    document.addEventListener("DOMContentLoaded", function () {
        const elementsPopUp = {
            idEnc: document.getElementById('idEncryptedPopUp'),
            cargo: document.getElementById('PopupCargo'),
            description: document.getElementById('PopupDescription'),
            actionbtn: document.getElementById('PopupUpdateImportOffer'),
            popup: document.getElementById('PopUpImportOfferDetail')
        };

        const elemLastPopup = document.getElementById('PopUpImportOfferBtn');
        const elemErrorGeneric = document.getElementById('ErrorGeneric');
        const elemImportBtn = document.getElementById('ImportOfferBtn');
        const elemGoHomeBtn = document.getElementById('BtnGoHomeImportOffers');
        const elemenValidateDetail = document.getElementById('PopUpImportOfferValidateDetail');
        const elemenDetailOffer = document.getElementById('PopUpImportOfferDetail');
        const elementLitTagImportOffers = '@PageLiteralsHelper.GetLiteral("LIT_PRE_OFFER_COUNT_OFFERS", pageId, portalConfig)';
        const elemBtnImportOfferReturn = document.getElementById('BtnImportOfferReturn');
        const elemKpi = @kpiNotNow;

        const urlActions = {
            urlUpdateImportOffer: '@Url.Action("UpdateImportOffer", "PreRegister")',
            urlGetImportOffer: '@Url.Action("ImportOffers", "PreRegister")',
            urlPostImportOffer: '@Url.Action("ImportOffersPost", "PreRegister")',
            urlReturn: '@Model.UrlReturn',
            urlValidateOfferDetail: '@Url.Action("ValidateOfferDetail", "PreRegister")',
            urlUpdateSelectToImport: '@Url.Action("UpdateSelectToImportOfferById", "PreRegister")',
            urlAddKpi: '@Url.Action("AddKpi", "PreRegister")'
        };

        const configModel = {
            UrlActions: urlActions,
            ElementsPopUp: elementsPopUp,
            ElemImportBtn: elemImportBtn,
            ElemGoHomeBtn: elemGoHomeBtn,
            ElemLastPopup: elemLastPopup,
            ElemOfferDetailPopup: elemenDetailOffer,
            ElemValidatePopup: elemenValidateDetail,
            ElemErrorGeneric: elemErrorGeneric,
            ListIdsSelected: [],
            ElementLitTagImportOffers: elementLitTagImportOffers,
            ElemBtnImportOfferReturn: elemBtnImportOfferReturn,
            ElemKpi: elemKpi
        };

        InitializeOfferPreRegister(configModel);
        SetTagSelectElements();
        elemImportBtn.addEventListener('click',function(event) {
            ImportOfferSubmit();
        });
        const selectAllCheckbox = document.getElementById('js_select_all');
        const checkboxes = document.querySelectorAll('.js_select_item');
        LoadEventsCheck(selectAllCheckbox, checkboxes);
        if (!window.location.href.includes("encryptedChecked")) {
            LoadSelectAll(selectAllCheckbox, checkboxes);
        } else {
            LoadSelectAllPost(checkboxes);
        }



        var descriptions = document.querySelectorAll(".js_description");

        descriptions.forEach(function (description) {
            var fullText = description.querySelector(".fullDescription").textContent;
            var shortText = fullText.split(" ").reduce((acc, word) => {
                if (acc.length + word.length < 100) {
                    return acc + " " + word;
                }
                return acc;
            });
            description.querySelector(".shortDescription").textContent = shortText.trim();
        });

        var table_offers = document.getElementById('table_offers');
        var header_table = document.getElementById('header_table');

        table_offers.onscroll = () => {
            var scrollPos = table_offers.scrollTop;
            if (scrollPos >= 10) {
                header_table.classList.add('fixed_header');
            } else {
                header_table.classList.remove('fixed_header');
            }
        };

        const elemDetailOffer = document.querySelectorAll('.popUpImportOfferShow');

        elemDetailOffer.forEach(element => {
            element.addEventListener('click', function (event) {
                const elemSelected = event.currentTarget;
                var idRow = elemSelected.getAttribute('data-id-row');
                var elementRowCargo = document.getElementById('cargo_' + idRow);
                var elementRowDescription = document.getElementById('description_' + idRow);
                ChargePopUpOffer({ idenc: idRow, cargo: elementRowCargo.textContent.trim(), description: elementRowDescription.textContent.trim() });
            });
        });

    });

    </script>
</body>
</html>
