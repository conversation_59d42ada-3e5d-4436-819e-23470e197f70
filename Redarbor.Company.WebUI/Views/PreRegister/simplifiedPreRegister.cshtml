@using Redarbor.Company.WebUI.Helpers;
@using Redarbor.Common.Entities.Enums;
@using System.Web.Optimization;

@model Redarbor.Company.WebUI.Models.SimplifiedRegister.SimplifiedRegisterDataModel

@{
    Layout = null;
    short pageId = (short)PageEnum.CompanyPreRegister;
    var portalConfig = PortalConfigHelper.GetPortalConfiguration();
}

<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="robots" content="noindex, nofollow" />

    <link title="@PageLiteralsHelper.GetLiteral("LIT_COMPUTRABAJO", pageId, portalConfig)" rel="shortcut icon" type="image/x-icon" href="@Url.Content(string.Format("{0}img/favicon.ico{1}", portalConfig.PrefixPathImage, portalConfig.SufixPathImage))" />
    <title>@PageLiteralsHelper.GetLiteral("HEAD_TITLE_PRE_REGISTER_COMPANY", pageId, portalConfig)</title>
    <meta name="description" content="@PageLiteralsHelper.GetLiteral("META_DESCRIPTION_PRE_REGISTER_COMPANY", pageId, portalConfig)" />
    <meta name="keywords" content="@PageLiteralsHelper.GetLiteral("META_KEYWORDS_PRE_REGISTER_COMPANY", pageId, portalConfig)" />

    <script src="https://www.googleoptimize.com/optimize.js?id=OPT-T9XF9K8"></script>

    @Scripts.Render("~/bundles/js/jquery")
    @Scripts.Render("~/bundles/js/required")
    @Scripts.Render("~/bundles/js/simplifiedPreRegister")
    @Styles.Render(string.Format("~/bundles/css/landing_saleforce{0}", portalConfig.staticVirtualBundle))

    <script type="text/javascript">
        $(document).ready(function () {
            InitializeGTM('@portalConfig.GoogleTagManager')
        });
    </script>
</head>
<body>
    <header>
        <!-- Menu tools -->
        <div class="menu_tools log">
            <div class="container">
                <!-- Logotipo -->
                <span class="logo">
                    <img srcset="@Url.Content(string.Format("{0}img/logoct_neg.svg{1}", portalConfig.PrefixPathImage, portalConfig.SufixPathImage))" alt="@PageLiteralsHelper.GetLiteral("ALT_IMG_LOGO_PORTAL", pageId, portalConfig)" class="cep_logo" />
                </span>
                <!-- Fin: Logotipo -->
            </div>
            <div class="overlay hide"></div>
        </div>
    </header>
    <main>
        <div class="container">
            @{Html.RenderPartial("PreRegister/_PreRegister", Model); }
        </div>
    </main>

    @if (portalConfig.AEPortalConfig.ShowCookiesConsent)
    {
        Html.RenderPartial("_CookiesConsent", new ViewDataDictionary { { "portalConfig", portalConfig }, { "pageId", (int)pageId } });
    }

    @if (portalConfig.AEPortalConfig.IsActiveLogJsWithMuscula)
    {
        <script>
            window.Muscula = {
                logId: 'TMs4Ade0AxYwT',
                errors: [],
            };
            window.Muscula.onerrorOriginalHandler = window.onerror;
            window.onerror = function () {
                window.Muscula.errors.push(arguments);
                return false;
            };

            (function () {
                var m = document.createElement('script');
                m.async = true;
                m.src = 'https://cdn.muscula.com/m2v2.js';
                var s = document.getElementsByTagName('script')[0];
                s.parentNode.insertBefore(m, s);
            })();
        </script>
    }
</body>

</html>

