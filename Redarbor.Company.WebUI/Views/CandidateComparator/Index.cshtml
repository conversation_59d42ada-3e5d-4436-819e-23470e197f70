@model Redarbor.Company.WebUI.Models.CandidateComparator.OfferCandidateComparatorDataModel
@using Redarbor.Company.WebUI.Helpers.Html
@using Redarbor.Company.WebUI.Helpers
@using Redarbor.Common.Entities.Enums
@using Redarbor.Master.Entities.Enums
@using Redarbor.Core.Kpi.Contracts.ServiceLibrary.Enums


@{
    Layout = "~/Views/Shared/Layouts/_LayoutMasterNewPem.cshtml";
    int pageDetailCvId = Html.SetCurrentPage((int)PageEnum.DetailCVCompany);
    int pageId = Html.SetCurrentPage((int)PageEnum.CandidatecomparatorPage);
    var portalConfig = PortalConfigHelper.GetPortalConfiguration();
    var companyCredentials = SecurityHelper.GetCompanyCredentials();
}


@section TitleSection{
    <title>@PageLiteralsHelper.GetLiteral("CANDIDATE_COMPARATOR", pageId, portalConfig)</title>
}
<main>

    <header class="header_block w100">
        <div class="w100">
            <h1 class="pl15_m pr15_m pt15_m"><span class="fc_brand_aux fs24">@PageLiteralsHelper.GetLiteral("SUBTITLE", pageDetailCvId, portalConfig) <span class="fwB">@Model.OfferComparatorDataModel.OfferTitle</span></span></h1>
            <div class="mt10 dFlex vm_fx w100">
                <h2 class="fs17 fwB fs16_m title_m">@PageLiteralsHelper.GetLiteral("CANDIDATE_COMPARATOR", pageId, portalConfig)</h2>
                <div class="mlAuto tr w50_m pr15_m"><a href="@Url.Action("Index", "CompanyMatches", new { oi = Model.OfferComparatorDataModel.OfferIdEncrypted, cf = Model.FolderSelectedEncrypted })">@PageLiteralsHelper.GetLiteral("LIT_PAG_VOLVER_LISTADO", pageDetailCvId, portalConfig)</a></div>
            </div>
        </div>
    </header>

    @if (Model.CandidateComparatorDataModels.Any())
    {
        <div id="messageMoveFolder" class="tag save hide">
            <span id="nameCandidateChangeFolder"></span> @PageLiteralsHelper.GetLiteral("MOVE_TO_FOLDER", pageId, portalConfig)
        </div>

        <article class="box" id="box_compare">
            <div class="header_comparador">
                <div class="dFlex vb_fx">
                    <p class="fc_aux mb10">@PageLiteralsHelper.GetLiteral("OFFER_REQUIREMENTS", pageId, portalConfig)</p>
                </div>

                @{
                    var count = 0;

                    foreach (var candidateComparatorDataModel in Model.CandidateComparatorDataModels)
                    {
                        <div class="dFlex row_fx tj_fx plB">
                            <div class="dFlex dIB_m">
                                @if (!string.IsNullOrEmpty(candidateComparatorDataModel.Photo))
                                {
                                    if (candidateComparatorDataModel.Photo.StartsWith("http"))
                                    {
                                        <div class="img mr10"><img src="@candidateComparatorDataModel.Photo"></div>
                                    }
                                    else
                                    {
                                        <div class="img mr10"><img src="@<EMAIL>"></div>
                                    }
                                }
                                else
                                {
                                    <div class="img mr10"><img src="@Url.Content(string.Format("{0}img/user_empty.svg", portalConfig.PrefixPathImage, portalConfig.countrycode.ToLower(), portalConfig.SufixPathImage))"></div>
                                }
                                <div>
                                    <p class="fs18">
                                        <span id="candidateName_@count">@candidateComparatorDataModel.Name</span> @candidateComparatorDataModel.Surname
                                        @if (Model.OfferComparatorDataModel.ShowAdecuacy && candidateComparatorDataModel.IsCandidateRecommended)
                                        {
                                            <span class="tag bg_ok">@PageLiteralsHelper.GetLiteral("LIT_RECOMMENDED", pageId, portalConfig)</span>
                                        }
                                    </p>

                                    @if (Model.OfferComparatorDataModel.ShowAdecuacy)
                                    {
                                        <p class="@(candidateComparatorDataModel.AdequacyPoints < 50 ? "fc_info" : "fc_ok") mt5 mb5 @(candidateComparatorDataModel.IsHigherAdequacy ? "fwB" : "")"><span class="fs22">@candidateComparatorDataModel.AdequacyPoints</span><span class="fs18">%</span> @PageLiteralsHelper.GetLiteral("ADECUATION", pageId, portalConfig)</p>
                                    }
                                    @PageLiteralsHelper.GetLiteral("FOLDER", pageId, portalConfig) <span id="folderName_@count">@candidateComparatorDataModel.FolderName</span>
                                </div>
                            </div>

                            @if (candidateComparatorDataModel.HasFolderDetails)
                            {

                                var fieldId = "MatchFolderSelected_" + count;
                                <div class="dFlex vm_fx mt15">
                                    <label for="@fieldId" class="fx_none mr10 mb0 hide_m">@PageLiteralsHelper.GetLiteral("MOVE_CV_TO", pageId, portalConfig)</label>
                                    <div>
                                        @Html.DropDownListFor(m => candidateComparatorDataModel.MatchFolderIdsSelected, @candidateComparatorDataModel.MatchFolders, new { @id = fieldId, @data_match_selected_index = count, @data_previous_folder_id = @candidateComparatorDataModel.FolderId })
                                    </div>
                                    <input id="matchIdEncrypted_@count" type="hidden" value="@candidateComparatorDataModel.MatchIdEncrypted" />
                                </div>
                            }
                        </div>
                        count++;
                    }
                    <input id="countMaxCandidateComparator" type="hidden" value="@Model.CandidateComparatorDataModels.Count()" />
                }
            </div>

            <div class="dFlex equal_w_fx vm_fx pt15 pb15 bt1_dash">
                <div>
                    <p class="fs16">@PageLiteralsHelper.GetLiteral("LIT_AGE", pageDetailCvId, portalConfig)</p>
                    @if (Model.OfferComparatorDataModel.MinAge != 0 && Model.OfferComparatorDataModel.MaxAge != 0)
                    {
                        <p class="fc_ok">@string.Format(PageLiteralsHelper.GetLiteral("RANGE_YEARS", pageId, portalConfig), @Model.OfferComparatorDataModel.MinAge, @Model.OfferComparatorDataModel.MaxAge)</p>
                    }
                </div>
                @foreach (var candidateComparatorDataModel in Model.CandidateComparatorDataModels)
                {
                    <div class="plB">
                        <p class="@(candidateComparatorDataModel.Age.Success ? "fc_ok" : "")">@candidateComparatorDataModel.Age.Value @PageLiteralsHelper.GetLiteral("YEARS", pageId, portalConfig)</p>
                    </div>
                }
            </div>

            <div class="dFlex equal_w_fx vm_fx pt15 pb15 bt1_dash">
                <div>
                    <p class="fs16">@PageLiteralsHelper.GetLiteral("LOCALIZATION", pageId, portalConfig)</p>
                    <p class="fc_ok">@Model.OfferComparatorDataModel.Localization</p>
                </div>
                @foreach (var candidateComparatorDataModel in Model.CandidateComparatorDataModels)
                {
                    <div class="plB">
                        <p class="@(candidateComparatorDataModel.Localization.Success ? "fc_ok" : "")">@(string.IsNullOrEmpty(candidateComparatorDataModel.Localization.Value) ? "-" : candidateComparatorDataModel.Localization.Value)</p>
                    </div>
                }
            </div>

            <div class="dFlex equal_w_fx vm_fx pt15 pb15 bt1_dash">
                <div>
                    <p class="fs16">@PageLiteralsHelper.GetLiteral("CITY", pageId, portalConfig)</p>
                    <p class="fc_ok">@Model.OfferComparatorDataModel.City</p>
                </div>
                @foreach (var candidateComparatorDataModel in Model.CandidateComparatorDataModels)
                {
                    <div class="plB">
                        <p class="@(candidateComparatorDataModel.City.Success ? "fc_ok" : "")">@(string.IsNullOrEmpty(candidateComparatorDataModel.City.Value) ? "-" : candidateComparatorDataModel.City.Value)</p>
                    </div>
                }
            </div>

            <div class="dFlex equal_w_fx vm_fx pt15 pb15 bt1_dash">
                <div>
                    <p class="fs16">@PageLiteralsHelper.GetLiteral("LIT_DISTANCE", pageId, portalConfig)</p>
                </div>
                @foreach (var candidateComparatorDataModel in Model.CandidateComparatorDataModels)
                {
                    <div class="plB">
                        <p>@candidateComparatorDataModel.DistanceToOfferLocation&nbsp;@PageLiteralsHelper.GetLiteral("LIT_KM", pageId, portalConfig)</p>
                    </div>
                }
            </div>

            <div class="dFlex equal_w_fx pt15 pb15 bt1_dash">
                <div>
                    <p class="fs16">@PageLiteralsHelper.GetLiteral("LIT_TEST_COMPETENCIAS", pageDetailCvId, portalConfig)</p>
                </div>
                @{
                    int index = 1;

                    foreach (var candidateComparatorDataModel in Model.CandidateComparatorDataModels)
                    {
                        if (Model.CandidateComparatorDataModels.Any(c => c.HasCompetencesTest && c.CompetenceDataModels.Any()))
                        {
                            if (candidateComparatorDataModel.HasCompetencesTest)
                            {
                                <div class="plB">
                                    @foreach (var test in candidateComparatorDataModel.CompetenceDataModels.OrderByDescending(c => c.Result).Take(3).ToList())
                                    {
                                        <div class="dFlex vm_fx mb15">
                                            <div class="posRel">
                                                <div style="height:60px; width:60px">
                                                    <div class="chartjs-size-monitor" style="position: absolute; inset: 0px; overflow: hidden; pointer-events: none; visibility: hidden; z-index: -1;">
                                                        <div class="chartjs-size-monitor-expand" style="position:absolute;left:0;top:0;right:0;bottom:0;overflow:hidden;pointer-events:none;visibility:hidden;z-index:-1;">
                                                            <div style="position:absolute;width:1000000px;height:1000000px;left:0;top:0"></div>
                                                        </div>
                                                        <div class="chartjs-size-monitor-shrink" style="position:absolute;left:0;top:0;right:0;bottom:0;overflow:hidden;pointer-events:none;visibility:hidden;z-index:-1;">
                                                            <div style="position:absolute;width:200%;height:200%;left:0; top:0"></div>
                                                        </div>
                                                    </div>
                                                    <canvas id="js_donut_chart_@index" width="60" height="60" style="display: block; width: 60px; height: 60px;" class="chartjs-render-monitor"></canvas>
                                                </div>
                                                <div id="donut_result_@index" class="info_donut fc_ok fs21 fwB">@test.Result</div>
                                            </div>
                                            <p class="w100 ml15">@test.Title</p>
                                        </div>
                                        index++;
                                    }
                                </div>
                            }
                            else
                            {
                                <div class="plB">
                                    <p class="fc_info mt20">@PageLiteralsHelper.GetLiteral("NO_TEST", pageId, portalConfig)</p>
                                </div>
                            }
                        }
                        else
                        {
                            <div class="plB">
                                <p class="fc_info">@PageLiteralsHelper.GetLiteral("NO_TEST", pageId, portalConfig)</p>
                            </div>
                        }
                    }
                    <input id="countMaxJsdonutChart" type="hidden" value="@index" />
                }
            </div>

            @if (Model.CandidateComparatorDataModels.Any(c => c.HasKillerQuestions))
            {
                <div class="dFlex equal_w_fx vm_fx pt15 pb15 bt1_dash">
                    <div>
                        <p class="fs16">@PageLiteralsHelper.GetLiteral("LIT_PREGUNTAS_FILTRADO", pageDetailCvId, portalConfig)</p>
                    </div>
                    @foreach (var candidateComparatorDataModel in Model.CandidateComparatorDataModels)
                    {
                        if (candidateComparatorDataModel.KQExcluding == 1 || candidateComparatorDataModel.KillerQuestionsPunctuation == -1)
                        {
                            <div class="plB">
                                <span>@PageLiteralsHelper.GetLiteral("LIT_EXCLUYENTE", pageDetailCvId, portalConfig)</span>
                            </div>
                        }
                        else if (candidateComparatorDataModel.KillerQuestionsPunctuation == 0)
                        {
                            <div class="plB">
                                <span>@PageLiteralsHelper.GetLiteral("LIT_SIN_PUNTUACION", pageDetailCvId, portalConfig)</span>
                            </div>
                        }
                        else
                        {
                            <div class="plB">
                                <strong>@candidateComparatorDataModel.KQScoreAvg</strong> @PageLiteralsHelper.GetLiteral("LIT_DE", pageDetailCvId, portalConfig) <strong>@candidateComparatorDataModel.KQScoreMax</strong>
                                @{
                                    var numberPercentatge = @candidateComparatorDataModel.KillerQuestionsPunctuation * 10;

                                    <span class="rating_kq small">
                                        <span style="width:@numberPercentatge%;"></span>
                                    </span>
                                }
                            </div>
                        }
                    }
                </div>
            }

            @if (!string.IsNullOrEmpty(Model.OfferComparatorDataModel.SalaryWithFormat) || Model.CandidateComparatorDataModels.Any(c => !string.IsNullOrEmpty(c.Salary.Value)))
            {
                <div class="dFlex equal_w_fx vm_fx pt15 pb15 bt1_dash">
                    <div>
                        <p class="fs16">@PageLiteralsHelper.GetLiteral("PRETENSION_SALARY", pageId, portalConfig)</p>
                        <p class="fc_ok">@Model.OfferComparatorDataModel.SalaryWithFormat @string.Format("({0})", PageLiteralsHelper.GetLiteral("MONTHLY", pageId, portalConfig))</p>
                    </div>
                    @foreach (var candidateComparatorDataModel in Model.CandidateComparatorDataModels)
                    {
                        var value = string.Format("{0} ({1})", candidateComparatorDataModel.Salary.Value, PageLiteralsHelper.GetLiteral("MONTHLY", pageId, portalConfig));
                        <div class="plB">
                            <p class="@(candidateComparatorDataModel.Salary.Success ? "fc_ok" : "")">
                                @(string.IsNullOrEmpty(candidateComparatorDataModel.Salary.Value) ? "-" : value)
                            </p>
                        </div>
                    }
                </div>
            }

            @if (Model.OfferComparatorDataModel.YearsExperience != 0 || Model.CandidateComparatorDataModels.Any(c => !string.IsNullOrEmpty(c.YearsAndMonthsExperience.Value)))
            {
                <div class="dFlex equal_w_fx vm_fx pt15 pb15 bt1_dash">
                    <div>
                        <p class="fs16">@PageLiteralsHelper.GetLiteral("YEARS_EXPERIENCE", pageId, portalConfig)</p>
                        @if (Model.OfferComparatorDataModel.YearsExperience != 0)
                        {
                            <p class="fc_ok">@Model.OfferComparatorDataModel.YearsExperience @PageLiteralsHelper.GetLiteral("YEARS", pageId, portalConfig)</p>
                        }
                    </div>
                    @foreach (var candidateComparatorDataModel in Model.CandidateComparatorDataModels)
                    {
                        <div class="plB @(candidateComparatorDataModel.YearsAndMonthsExperience.Success ? "fc_ok" : "")">
                            <p>@(string.IsNullOrEmpty(candidateComparatorDataModel.YearsAndMonthsExperience.Value) ? "-" : candidateComparatorDataModel.YearsAndMonthsExperience.Value)</p>
                        </div>
                    }
                </div>
            }

            @if (Model.CandidateComparatorDataModels.Any(c => !string.IsNullOrEmpty(c.LastCharge)))
            {
                <div class="dFlex equal_w_fx vm_fx pt15 pb15 bt1_dash">
                    <div>
                        <p class="fs16">@PageLiteralsHelper.GetLiteral("LAST_CHARGE", pageId, portalConfig)</p>
                    </div>
                    @foreach (var candidateComparatorDataModel in Model.CandidateComparatorDataModels)
                    {
                        <div class="plB">
                            <p>@(string.IsNullOrEmpty(candidateComparatorDataModel.LastCharge) ? "-" : candidateComparatorDataModel.LastCharge)</p>
                        </div>
                    }
                </div>
            }

            @if (Model.CandidateComparatorDataModels.Any(c => !string.IsNullOrEmpty(c.LastCompany)))
            {
                <div class="dFlex equal_w_fx vm_fx pt15 pb15 bt1_dash">
                    <div>
                        <p class="fs16">@PageLiteralsHelper.GetLiteral("LAST_COMPANY", pageId, portalConfig)</p>
                    </div>
                    @foreach (var candidateComparatorDataModel in Model.CandidateComparatorDataModels)
                    {
                        <div class="plB">
                            <p>@(string.IsNullOrEmpty(candidateComparatorDataModel.LastCompany) ? "-" : candidateComparatorDataModel.LastCompany)</p>
                        </div>
                    }
                </div>
            }

            @if (!string.IsNullOrEmpty(Model.OfferComparatorDataModel.MinStudies) || Model.CandidateComparatorDataModels.Any(c => !string.IsNullOrEmpty(c.MaxStudies)))
            {
                <div class="dFlex equal_w_fx vm_fx pt15 pb15 bt1_dash">
                    <div>
                        <p class="fs16">@PageLiteralsHelper.GetLiteral("STUDIES_MIN", pageId, portalConfig)</p>
                        <p>@Model.OfferComparatorDataModel.MinStudies</p>
                    </div>
                    @foreach (var candidateComparatorDataModel in Model.CandidateComparatorDataModels)
                    {
                        <div class="plB">
                            <p>@(string.IsNullOrEmpty(candidateComparatorDataModel.MaxStudies) ? "-" : candidateComparatorDataModel.MaxStudies)</p>
                        </div>
                    }
                </div>
            }

            @if (Model.CandidateComparatorDataModels.Any(c => !string.IsNullOrEmpty(c.TitleStudies)))
            {
                <div class="dFlex equal_w_fx vm_fx pt15 pb15 bt1_dash">
                    <div>
                        <p class="fs16">@PageLiteralsHelper.GetLiteral("STUDIES_TITLE", pageId, portalConfig)</p>
                    </div>
                    @foreach (var candidateComparatorDataModel in Model.CandidateComparatorDataModels)
                    {
                        <div class="plB">
                            <p>@(string.IsNullOrEmpty(candidateComparatorDataModel.TitleStudies) ? "-" : candidateComparatorDataModel.TitleStudies)</p>
                        </div>
                    }
                </div>
            }

            @if (!string.IsNullOrEmpty(Model.OfferComparatorDataModel.EnglishLanguageName) && !string.IsNullOrEmpty(Model.OfferComparatorDataModel.EnglishLanguageLevel)
                || Model.CandidateComparatorDataModels.Any(c => !string.IsNullOrEmpty(c.Language.Value)))
            {
                <div class="dFlex equal_w_fx vm_fx pt15 pb15 bt1_dash">
                    <div>
                        <p class="fs16">@PageLiteralsHelper.GetLiteral("LIT_IDIOM", pageDetailCvId, portalConfig)</p>
                        <p class="fc_ok">@Model.OfferComparatorDataModel.EnglishLanguageName @Model.OfferComparatorDataModel.EnglishLanguageLevel</p>
                    </div>
                    @foreach (var candidateComparatorDataModel in Model.CandidateComparatorDataModels)
                    {
                        <div class="plB @(candidateComparatorDataModel.Language.Success ? "fc_ok" : "")">
                            <p>@(string.IsNullOrEmpty(candidateComparatorDataModel.Language.Value) ? "-" : candidateComparatorDataModel.Language.Value)</p>
                        </div>
                    }
                </div>
            }

            @if (!string.IsNullOrEmpty(Model.OfferComparatorDataModel.ResidenceChangeString) || Model.CandidateComparatorDataModels.Any(c => !string.IsNullOrEmpty(c.ResidenceChange.Value)))
            {
                <div class="dFlex equal_w_fx vm_fx pt15 pb15 bt1_dash">
                    <div>
                        <p class="fs16">@PageLiteralsHelper.GetLiteral("RESIDENCE_CHANGE", pageId, portalConfig)</p>
                        <p class="fc_ok">@Model.OfferComparatorDataModel.ResidenceChangeString</p>
                    </div>
                    @foreach (var candidateComparatorDataModel in Model.CandidateComparatorDataModels)
                    {
                        <div class="plB @(candidateComparatorDataModel.ResidenceChange.Success ? "fc_ok" : "")">
                            <p>@(string.IsNullOrEmpty(candidateComparatorDataModel.ResidenceChange.Value) ? "-" : candidateComparatorDataModel.ResidenceChange.Value)</p>
                        </div>
                    }
                </div>
            }

            @if (!string.IsNullOrEmpty(Model.OfferComparatorDataModel.DisponibilityTravelString) || Model.CandidateComparatorDataModels.Any(c => !string.IsNullOrEmpty(c.DisponibilityTravel.Value)))
            {
                <div class="dFlex equal_w_fx vm_fx pt15 pb15 bt1_dash">
                    <div>
                        <p class="fs16">@PageLiteralsHelper.GetLiteral("LIT_AVAILABILITY_TRAVEL", pageDetailCvId, portalConfig)</p>
                        <p class="fc_ok">@Model.OfferComparatorDataModel.DisponibilityTravelString</p>
                    </div>
                    @foreach (var candidateComparatorDataModel in Model.CandidateComparatorDataModels)
                    {
                        <div class="plB @(candidateComparatorDataModel.DisponibilityTravel.Success ? "fc_ok" : "")">
                            <p>@(string.IsNullOrEmpty(candidateComparatorDataModel.DisponibilityTravel.Value) ? "-" : candidateComparatorDataModel.DisponibilityTravel.Value)</p>
                        </div>
                    }
                </div>
            }

            @if (Model.CandidateComparatorDataModels.Any(c => !string.IsNullOrEmpty(c.DriveLicenses.Value)))
            {
                <div class="dFlex equal_w_fx vm_fx pt15 pb15 bt1_dash">
                    <div>
                        <p class="fs16">@PageLiteralsHelper.GetLiteral("DRIVE_LICENSES", pageId, portalConfig)</p>
                        <p class="fc_ok">@Model.OfferComparatorDataModel.DriveLicenses</p>
                    </div>
                    @foreach (var candidateComparatorDataModel in Model.CandidateComparatorDataModels)
                    {
                        <div class="plB @(candidateComparatorDataModel.DriveLicenses.Success ? "fc_ok" : "")">
                            <p>@(string.IsNullOrEmpty(candidateComparatorDataModel.DriveLicenses.Value) ? "-" : candidateComparatorDataModel.DriveLicenses.Value)</p>
                        </div>
                    }
                </div>
            }

            <div class="dFlex equal_w_fx vm_fx pt15 pb15 bt1_dash">
                <div>
                    <p class="fs16">@PageLiteralsHelper.GetLiteral("DISABILITY", pageId, portalConfig)</p>
                    <p class="fc_ok">@Model.OfferComparatorDataModel.DisabilityString</p>
                </div>
                @foreach (var candidateComparatorDataModel in Model.CandidateComparatorDataModels)
                {
                    <div class="plB @(candidateComparatorDataModel.Disability.Success ? "fc_ok" : "")">
                        <p>@candidateComparatorDataModel.Disability.Value</p>
                    </div>
                }
            </div>

            <div class="dFlex equal_w_fx vm_fx pt15 pb15 bt1_dash">
                <div>
                    <p class="fs16">@PageLiteralsHelper.GetLiteral("LAST_UPDATE", pageId, portalConfig)</p>
                </div>
                @foreach (var candidateComparatorDataModel in Model.CandidateComparatorDataModels)
                {
                    <div class="plB">
                        <p>@candidateComparatorDataModel.LastUpdate</p>
                    </div>
                }
            </div>
        </article>
    }
</main>

<div id="loaderCandidateComparatorId" class="popup flex hide">
    <div class="w40">
        <div>
            <div class="loading">
                <div>
                    <span></span><span></span><span></span><span></span>
                </div>
                <div>
                    <span></span><span></span><span></span><span></span>
                </div>
                <div>
                    <span></span><span></span><span></span><span></span>
                </div>
            </div>
        </div>
    </div>
</div>

@section CustomScriptsFooterSection{
    @Scripts.Render("~/bundles/js/company/CandidateComparator/candidateComparator")
    <script type="text/javascript">
        var configModelCandidatecomparator = {
            FolderSelected: "@Model.FolderSelected",
            OfferIdEncrypted: "@Model.OfferComparatorDataModel.OfferIdEncrypted",
            MoveCVsFolder: "@Url.Action("MoveCVsFolder", "CompanyMatches")",
            idp: @pageId,
            literals: {
                selected: '@PageLiteralsHelper.GetLiteral("LIT_FOLDER_2", (int)PageEnum.MatchOffer, portalConfig)',
                selectedKpi: '@EncryptationHelper.Encrypt(((int)KpiEnum.COMPANY_MOVE_FOLDER_SELECTED).ToString())',
                finalistKpi: '@EncryptationHelper.Encrypt(((int)KpiEnum.COMPANY_MOVE_FOLDER_FINALIST).ToString())',
                discartedKpi: '@EncryptationHelper.Encrypt(((int)KpiEnum.COMPANY_MOVE_FOLDER_DISCARDED).ToString())',
                customFolder1Kpi: '@EncryptationHelper.Encrypt(((int)KpiEnum.COMPANY_MOVE_CUSTOM_FOLDER_1).ToString())',
                customFolder2kpi: '@EncryptationHelper.Encrypt(((int)KpiEnum.COMPANY_MOVE_CUSTOM_FOLDER_2).ToString())',
                moveDefaultKpi: '@EncryptationHelper.Encrypt(((int)KpiEnum.COMPANY_MATCH_CV_WAS_MOVED_FROM_FOLDER).ToString())'
            }
        };
        InitializeCandidateComparator(configModelCandidatecomparator);
    </script>
}