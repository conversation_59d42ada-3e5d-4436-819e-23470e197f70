@using Redarbor.Common.Entities.Enums
@using System.Web.Optimization;
@using Redarbor.Company.WebUI.Helpers;
@using Redarbor.Company.WebUI.Enums;
@model Redarbor.Company.WebUI.Models.Company.Candidate.QuizTalentviewExportDataModel
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
@{
    short pageIdHomeMaster = (short)PageEnum.HomeMasterCompany;
    short pageIdDetailCv = (short)PageEnum.DetailCVCompany;
    short pageId = (short)PageEnum.TalentView;
    var portalConfig = PortalConfigHelper.GetPortalConfiguration();
}

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="robots" content="noindex, nofollow" />
    <script>
        // Contenido del archivo jQuery
        @Html.Raw(System.IO.File.ReadAllText(Server.MapPath("~/c/js/jquery-3.1.1.js")))
    </script>
    <script>
        // Contenido del archivo required.js
        @Html.Raw(System.IO.File.ReadAllText(Server.MapPath("~/c/js/chart.js")))
    </script>
    <link title="@PageLiteralsHelper.GetLiteral("LIT_COMPUTRABAJO", pageIdHomeMaster, portalConfig)" rel="shortcut icon" type="image/x-icon" href="@Url.Content(string.Format("{0}img/favicon.ico{1}", portalConfig.PrefixPathImage, portalConfig.SufixPathImage))" />
    <style>
        @Html.Raw(System.IO.File.ReadAllText(Server.MapPath("~/c/v2/css/results_talentview_3D.css")))
    </style>
</head>

<body>
    <div class="page_title">
        <div class="intro_content">
            <div class="logo">
                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="133" height="30" xmlns:v="https://vecta.io/nano" style="zoom:3;"><g fill-rule="evenodd"><g fill="#fff" fill-rule="nonzero"><path d="M0 8.502c0 5.239 1.789 8.502 6.675 8.502 1.491 0 2.821-.253 4.542-1.08l.229-2.482c-1.606.965-3.211 1.31-4.771 1.31-3.051 0-3.853-2.413-3.853-6.25s.757-6.25 3.899-6.25c1.491 0 2.89.345 4.496 1.172V1.08C9.496.253 8.212 0 6.721 0 1.72 0 0 3.263 0 8.502z" /><use xlink:href="#B" /><path d="M27.616 5.285h-2.477v11.49h2.592V8.02c.826-.506 1.514-.873 2.477-.873 1.078 0 1.651.597 1.651 1.723v7.905h2.615V8.112c.803-.529 1.514-.965 2.454-.965 1.078 0 1.651.597 1.651 1.723v7.905h2.615v-8.41c0-2.229-1.422-3.309-3.257-3.309-.757 0-1.583.207-2.385.643l-1.284 1.333h-.046c-.436-1.333-1.537-1.976-2.936-1.976-.757 0-1.583.207-2.385.643l-1.124 1.126h-.046l-.115-1.54zm18.556 0h-2.477V21.83h2.592V7.836c.55-.391 1.307-.896 2.271-.896 1.674 0 2.592 1.149 2.592 4.09 0 2.643-.734 4.205-2.638 4.205a3.1 3.1 0 0 1-1.537-.368v1.586a4.53 4.53 0 0 0 2.202.552c2.821 0 4.656-2.183 4.656-5.975 0-3.677-1.491-5.975-4.129-5.975-.734 0-1.422.184-2.018.529l-1.399 1.08-.115-1.379zm12.202 0h-2.615v8.411c0 2.229 1.422 3.309 3.257 3.309.757 0 1.583-.207 2.385-.643l1.124-1.126h.046l.115 1.54h2.477V5.285h-2.592v8.755c-.803.506-1.583.873-2.523.873-1.101 0-1.674-.597-1.674-1.723V5.285zm8.441 1.839h1.262v6.986c0 2.114 1.032 2.895 3.119 2.895.505 0 1.514-.138 2.11-.391V14.73c-.459.184-1.032.253-1.399.253-.872 0-1.239-.368-1.239-1.218V7.124h2.569V5.285h-2.569V2.826h-.986l-1.606 2.459-1.262.735v1.103zM77.48 5.285h-2.477v11.49h2.592V8.548c.688-.712 1.491-1.126 2.454-1.126a3.98 3.98 0 0 1 1.032.138l.849-2.321a4.78 4.78 0 0 0-1.284-.184c-.872 0-1.468.322-1.973.689l-1.032 1.54h-.046l-.115-1.999z" /><use xlink:href="#C" /><path d="M96.449 6.664V.23h-2.592v16.66h.94l.734-.896c.826.574 2.064 1.011 3.395 1.011 3.165 0 5.069-2.183 5.069-5.975 0-3.677-1.491-5.975-4.129-5.975-.734 0-1.422.184-2.018.529l-1.399 1.08zm0 1.172c.55-.391 1.307-.896 2.271-.896 1.674 0 2.592 1.149 2.592 4.09 0 2.643-.665 4.343-2.707 4.343-.895 0-1.56-.253-2.156-.597v-6.94z" /><use xlink:href="#C" x="23.074" /><path d="M116.839 2.229c0 .781.573 1.402 1.514 1.402.917 0 1.514-.62 1.514-1.402s-.596-1.402-1.514-1.402c-.94 0-1.514.62-1.514 1.402zm2.844 3.056h-2.592v11.329c0 1.999-.275 2.666-1.858 3.654l1.055 1.563c2.844-1.287 3.395-3.033 3.395-5.216V5.285z" /><use xlink:href="#B" x="109.041" /></g><path d="M69.457 23.212s-1.287-.554-1.382.969c-.138 1.315 1.245 1.814 1.727 2.007 7.6 3.045 35.235 8.725 62.274-7.337.276-.138.069-.554-.233-.342-23.075 12.182-54.096 8.718-62.386 4.703z" fill="#ff6f00" /></g><defs><path id="B" d="M17.868 17.005c3.509 0 5.276-2.252 5.276-5.975s-1.766-5.975-5.276-5.975c-3.486 0-5.253 2.252-5.253 5.975s1.766 5.975 5.253 5.975zm0-10.18c1.95 0 2.592 1.563 2.592 4.205 0 2.597-.596 4.205-2.592 4.205-1.973 0-2.592-1.609-2.592-4.205 0-2.643.665-4.205 2.592-4.205z" /><path id="C" d="M88.765 15.258l.115 1.517h2.5V8.273c0-2.068-1.72-3.217-4.243-3.217-1.17 0-2.225.161-3.486.529l-.688 1.861.161.115c1.284-.529 2.225-.712 3.441-.712 1.858 0 2.202 1.08 2.202 2.114v1.149l-3.441.735c-1.353.276-3.188 1.011-3.188 3.217 0 1.746 1.284 2.941 3.142 2.941a3.8 3.8 0 0 0 2.064-.597l1.422-1.149zm0-3.884v2.551c-.596.529-1.33 1.126-2.363 1.126-.849 0-1.537-.506-1.537-1.471 0-.942.459-1.241 1.445-1.517l2.454-.689z" /></defs></svg>
            </div>
            <div class="info">
                <h1>@PageLiteralsHelper.GetLiteral("LIT_INFORMES_DE_RESULTADOS", pageId, portalConfig) <span>Talentview 3D</span></h1>
            </div>
            <div class="info_left">
                <p class="fwB">
                    @PageLiteralsHelper.GetLiteral("LIT_VALUED_PERSON", pageId, portalConfig)
                </p>
                <p>
                    @Model.CandidateName
                </p>
            </div>
            @*<div class="info_right">
                    <p class="fwB">
                        @Html.Lit("EXPORT_VALUATE_DATE", Model.Page)
                    </p>
                    <p>
                        @Model.TestDateCompleted
                    </p>
                </div>*@
        </div>
        <svg class="intro_img" width="2480px" height="1134px" viewBox="0 0 2480 1134" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
            <title>1 copy 17@1x</title>
            <defs>
                <path d="M2697.23097,19 C2478.95826,19 2262.29505,63.0934745 2047.21836,151.257434 C2029.42177,159.119774 2010.56749,167.212007 1990.40262,175.603101 C1972.28412,183.695334 1882.17448,233.030369 1865.06768,243.4905 C1461.14933,490.418555 1510.30841,1273.93983 1573.03336,1464.9809 C1652.75015,1707.65593 1925.05646,1846.62624 2090.19231,1910.49051 C2281.70116,1982.81484 2484.03971,2019 2697.23097,2019 C2940.97996,2019 3108.36912,1933.54878 3199.42147,1762.69231 C3290.47382,1591.81286 3336,1343.91925 3336,1018.98851 C3336,694.057761 3290.47382,446.164155 3199.42147,275.284699 C3108.36912,104.428233 2940.97996,19 2697.23097,19 Z" id="path-1"></path>
                <filter x="-10.8%" y="-6.3%" width="119.4%" height="117.5%" filterUnits="objectBoundingBox" id="filter-2">
                    <feMorphology radius="25" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
                    <feOffset dx="-20" dy="50" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
                    <feGaussianBlur stdDeviation="25" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
                    <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.05 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
                </filter>
                <path d="M263.230975,225 C44.9582573,225 -171.704949,269.093474 -386.781636,357.257434 C-404.578232,365.119774 -423.432507,373.212007 -443.597383,381.603101 C-461.715881,389.695334 -551.825518,439.030369 -568.932323,449.4905 C-972.850665,696.418555 -923.691593,1479.93983 -860.966641,1670.9809 C-781.249849,1913.65593 -508.94354,2052.62624 -343.807686,2116.49051 C-152.298842,2188.81484 50.0397143,2225 263.230975,2225 C506.979955,2225 674.369125,2139.54878 765.421475,1968.69231 C856.473825,1797.81286 902,1549.91925 902,1224.98851 C902,900.057761 856.473825,652.164155 765.421475,481.284699 C674.369125,310.428233 506.979955,225 263.230975,225 Z" id="path-3"></path>
                <filter x="-8.6%" y="-11.2%" width="119.4%" height="117.5%" filterUnits="objectBoundingBox" id="filter-4">
                    <feMorphology radius="25" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
                    <feOffset dx="20" dy="-50" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
                    <feGaussianBlur stdDeviation="25" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
                    <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.05 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
                </filter>
            </defs>
            <g id="1-copy-17" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                <rect fill="#0D3878" x="0" y="0" width="2480" height="1134"></rect>
                <g id="Fill-30" transform="translate(2433.500000, 1019.000000) rotate(30.000000) translate(-2433.500000, -1019.000000) ">
                    <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                    <use fill="#005DA9" fill-rule="evenodd" xlink:href="#path-1"></use>
                </g>
                <g id="Fill-30-Copy" transform="translate(-0.500000, 1225.000000) rotate(-70.000000) translate(0.500000, -1225.000000) ">
                    <use fill="black" fill-opacity="1" filter="url(#filter-4)" xlink:href="#path-3"></use>
                    <use fill="#005DA9" fill-rule="evenodd" xlink:href="#path-3"></use>
                </g>
            </g>
        </svg>
    </div>
    <div class="page">
        <div class="header">
            <p>
                <span>@PageLiteralsHelper.GetLiteral("LIT_VALUED_PERSON", pageId, portalConfig):</span> @Model.CandidateName
            </p>
            <div class="mlAuto">
                <p>
                    <span>@PageLiteralsHelper.GetLiteral("LIT_EMAIL", pageId, portalConfig): </span> @Model.CandidateMail
                </p>
                @if (!string.IsNullOrEmpty(Model.CandidatePhone))
                {
                    <p>
                        <span>@PageLiteralsHelper.GetLiteral("LIT_PHONE", pageId, portalConfig):</span> @Model.CandidatePhone
                    </p>
                }
            </div>
        </div>
        <h2>
            @PageLiteralsHelper.GetLiteral("LIT_TALENT_VIEW_PERSONALITY", pageIdDetailCv, portalConfig)
        </h2>
        @if (Model.PersonalityList != null && Model.PersonalityList.Count > 0)
        {
            <p class="fwB fs18 mbB">
                @Model.PersonalityList.OrderByDescending(x => x.Result).FirstOrDefault().Title
            </p>
            <p>
                @Model.PersonalityList.OrderByDescending(x => x.Result).FirstOrDefault().Description
            </p>
        }
        <div class="mt60">
            <h2>
                @PageLiteralsHelper.GetLiteral("LIT_TEST_COMPETENCIAS", pageIdDetailCv, portalConfig)
            </h2>
            <div class="item grid">
                <p>
                    @PageLiteralsHelper.GetLiteral("LIT_RADAR_DESCRIPTION", pageId, portalConfig)
                </p>
                <div><canvas id="radar"></canvas></div>
            </div>
        </div>
        <div class="mt60">
            <h2>@PageLiteralsHelper.GetLiteral("LIT_TALENT_VIEW_VALUES", pageIdDetailCv, portalConfig)</h2>
            <div class="item grid">
                <p>
                    @PageLiteralsHelper.GetLiteral("LIT_MAP_DESCRPTION", pageId, portalConfig)</h2>
                </p>
                <div class="graph">
                    <div class="table_results">
                        @foreach (var group in Model.ValuesList)
                        {
                            <div>
                                <span class="@(group.ShortScoreGraph1Class)">
                                    @if (group.ShortScore1)
                                    {
                                        <span></span>
                                    }
                                </span>
                                <span class="@(group.ShortScoreGraph2Class)">
                                    @if (group.ShortScore2)
                                    {
                                        <span></span>
                                    }
                                </span>
                                <span class="@(group.ShortScoreGraph3Class)">
                                    @if (group.ShortScore3)
                                    {
                                        <span></span>
                                    }
                                </span>
                                <span class="@(group.ShortScoreGraph4Class)">
                                    @if (group.ShortScore4)
                                    {
                                        <span></span>
                                    }
                                </span>
                                <span class="@(group.ShortScoreGraph5Class)">
                                    @if (group.ShortScore5)
                                    {
                                        <span></span>
                                    }
                                </span>
                                <span class="legend">@group.Title</span>
                            </div>
                        }
                    </div>
                    <div class="legend">
                        <span>@PageLiteralsHelper.GetLiteral("LIT_TALENT_VIEW_NO_DOMINANT", pageIdDetailCv, portalConfig)</span>
                        <span>@PageLiteralsHelper.GetLiteral("LIT_TALENT_VIEW_DOMINANT", pageIdDetailCv, portalConfig)</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="page">
        <div class="header">
            <p>
                <span>@PageLiteralsHelper.GetLiteral("LIT_VALUED_PERSON", pageId, portalConfig):</span> @Model.CandidateName
            </p>
            <div class="mlAuto">
                <p>
                    <span>@PageLiteralsHelper.GetLiteral("LIT_EMAIL", pageId, portalConfig): </span> @Model.CandidateMail
                </p>
                @if (!string.IsNullOrEmpty(Model.CandidatePhone))
                {
                    <p>
                        <span>@PageLiteralsHelper.GetLiteral("LIT_PHONE", pageId, portalConfig): </span> @Model.CandidatePhone
                    </p>
                }
            </div>
        </div>
        <h2>@PageLiteralsHelper.GetLiteral("LIT_TEST_COMPETENCIAS", pageIdDetailCv, portalConfig)</h2>
        <p class="fs20 mb30">
            @PageLiteralsHelper.GetLiteral("LIT_EIGHT_DESCRIPTION", pageIdDetailCv, portalConfig)
        </p>
        @foreach (var group in Model.CompetenceList.OrderByDescending(x => x.Result))
        {
            <div class="item">
                <div class="result_doughnut mrB">
                    <svg viewBox="0 0 42 42" class="doughnut-chart">
                        <circle class="doughnut-hole" cx="21" cy="21" r="15.91549431" fill="#fff"></circle>
                        <circle class="doughnut-ring" cx="21" cy="21" r="15.91549431" fill="transparent" stroke="#eaf3fb" stroke-width="3"></circle>
                        <circle class="doughnut-segment" cx="21" cy="21" r="15.91549431" fill="transparent" stroke="@group.LongScoreRotateColor" stroke-width="3" stroke-dasharray="@group.LongScoreRotateDoughnut" stroke-dashoffset="25"></circle>
                        <g class="doughnut-text" transform="translate(21, 21)">
                            <text x="0" y="0" text-anchor="middle" dy="0.3em" fill="#313944">@group.Result</text>
                        </g>
                    </svg>
                </div>
                <div>
                    <p class="fc_link fs18 fwB mb5">@group.Title</p>
                    <p>@group.Description</p>
                </div>
            </div>
        }
    </div>
    <div class="page">
        <div class="header">
            <p>
                <span>@PageLiteralsHelper.GetLiteral("LIT_VALUED_PERSON", pageId, portalConfig):</span> @Model.CandidateName
            </p>
            <div class="mlAuto">
                <p>
                    <span>@PageLiteralsHelper.GetLiteral("LIT_EMAIL", pageId, portalConfig): </span> @Model.CandidateMail
                </p>
                @if (!string.IsNullOrEmpty(Model.CandidatePhone))
                {
                    <p>
                        <span>@PageLiteralsHelper.GetLiteral("LIT_PHONE", pageId, portalConfig): </span> @Model.CandidatePhone
                    </p>
                }
            </div>
        </div>
        <h2>
            @PageLiteralsHelper.GetLiteral("LIT_TALENT_VIEW_VALUES", pageIdDetailCv, portalConfig)
        </h2>
        <p class="fs20 mb30">
            @PageLiteralsHelper.GetLiteral("LIT_VALUES_DESCRIPTION", pageIdDetailCv, portalConfig)
        </p>
        @foreach (var group in Model.ValuesList.OrderByDescending(x => x.Result))
        {
           

            <div class="item">
                <div class="result_doughnut mrB">
                    <svg viewBox="0 0 42 21" class="half-doughnut-chart">

                        <circle class="doughnut-ring" cx="21" cy="21" r="15.91549431" fill="transparent" stroke="#D2D3D4" stroke-width="3"></circle>
                        <circle class="doughnut-segment" cx="21" cy="21" r="15.91549431" fill="transparent" stroke="@group.ShortScoreRotateColor" stroke-width="3" stroke-dasharray="@group.ShortScoreRotateDoughnut" stroke-dashoffset="50"></circle>
                        <text class="doughnut-text" x="21" y="19" text-anchor="middle" font-size="12" fill="#313944">@group.ShortScore</text>
                    </svg>
                    <span class="legend"><span>0</span><span>5</span></span>
                </div>
                <div>
                    <p class="fs16 fwB mb5">
                        @group.Title
                    </p>
                    <p>
                        @group.Description
                    </p>
                </div>
            </div>
        }
    </div>

    <script type="text/javascript">
        @{
            var enumValues = new[] {
                        CompentencesTestRuleKeysEnum.RC,
                        CompentencesTestRuleKeysEnum.TD,
                        CompentencesTestRuleKeysEnum.PC,
                        CompentencesTestRuleKeysEnum.RI,
                        CompentencesTestRuleKeysEnum.AD,
                        CompentencesTestRuleKeysEnum.CA,
                        CompentencesTestRuleKeysEnum.LI,
                        CompentencesTestRuleKeysEnum.TE,
                    };

            var radarChartLabels = "'" + PageLiteralsHelper.GetLiteral("RESOLUCION_TEST_COMPETENCES", pageIdDetailCv, portalConfig) + "'," +
                                         "'" + PageLiteralsHelper.GetLiteral("DECISIONES_TEST_COMPETENCES", pageIdDetailCv, portalConfig) + "'," +
                                         "'" + PageLiteralsHelper.GetLiteral("PENSAMIENTO_TEST_COMPETENCES", pageIdDetailCv, portalConfig) + "'," +
                                         "'" + PageLiteralsHelper.GetLiteral("RESILENCIA_TEST_COMPETENCES", pageIdDetailCv, portalConfig) + "'," +
                                         "'" + PageLiteralsHelper.GetLiteral("ADAPTACION_TEST_COMPETENCES", pageIdDetailCv, portalConfig) + "'," +
                                         "'" + PageLiteralsHelper.GetLiteral("CAPACIDAD_TEST_COMPETENCES", pageIdDetailCv, portalConfig) + "'," +
                                         "'" + PageLiteralsHelper.GetLiteral("LIDERAZGO_TEST_COMPETENCES", pageIdDetailCv, portalConfig) + "'," +
                                         "'" + PageLiteralsHelper.GetLiteral("TRABAJO_TEST_COMPETENCES", pageIdDetailCv, portalConfig) + "'";
        }
        var ctx = document.getElementById('radar').getContext('2d');
        var myChart = new Chart(ctx, {
            type: 'radar',
            data: {
                labels: [@Html.Raw(radarChartLabels)],
                datasets: [{
                    borderColor: '#005DA9',
                    borderWidth: '1px',
                    data: [
                                    @{

                                        var competencies = enumValues.Select(e =>
                                        {
                                            var competence = Model.CompetenceList.FirstOrDefault(x => string.Equals(x.Key, e.ToString()));
                                            return (competence == null ? 0 : competence.Result).ToString();
                                        }).Aggregate((a, b) => a + ", " + b);
                                     }
                                        @(competencies)
                                    ],
                    pointBackgroundColor: '#005DA9',
                    pointBorderColor: '#005DA9',
                    pointBorderWidth: '',
                }],
            },
            options: {
                legend: { display: false },
                maintainAspectRatio: false,
                scale: {
                    ticks: {
                        fontColor: '#a0a0a0',
                        fontSize: 11,
                        max: 100,
                        maxTicksLimit: 5,
                        min: 0,
                    },
                    pointLabels: {
                        fontSize: 11,
                    },
                },
                tooltips: {
                    displayColors: false,
                    enabled: true,
                },
            }
        });
    </script>
</body>
</html>