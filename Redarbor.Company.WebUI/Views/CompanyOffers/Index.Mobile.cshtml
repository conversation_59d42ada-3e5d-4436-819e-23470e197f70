@model Redarbor.Company.WebUI.Models.Company.Home.HomeOfferDataModel
@using Redarbor.Company.WebUI.Helpers.Html
@using Redarbor.Company.WebUI.Enums
@using Redarbor.Company.WebUI.Helpers
@using Redarbor.Common.Entities.Enums
@using System.Web.Optimization

@{
    Layout = "~/Views/Shared/Layouts/_LayoutMasterMobilePemMin.cshtml";
    int pageId = Html.SetCurrentPage((int)PageEnum.OfferList);
    var portalConfig = PortalConfigHelper.GetPortalConfiguration();
    var companyCredentials = SecurityHelper.GetCompanyCredentials();
    var goToMPCartHighlightOffer = (@Url.Action("Index", "MultiPurchaseCart", new { p = EncryptationHelper.Encrypt(((int)PageEnum.ConsumablesCart).ToString()), prod = Model.ProdCWHighlightOffer, btn = Model.BtnEnableHighlightOffer }));
    var goToMPCartUrgentOffer = (@Url.Action("Index", "MultiPurchaseCart", new { p = EncryptationHelper.Encrypt(((int)PageEnum.ConsumablesCart).ToString()), prod = Model.ProdCWUrgentOffer, btn = Model.BtnEnableUrgentOffer }));
    var goToMPCartEditOffer = (@Url.Action("Index", "MultiPurchaseCart", new { p = EncryptationHelper.Encrypt(((int)PageEnum.PackCart).ToString()), prod = Model.ProdCWEditOffer, btn = Model.BtnEnableEditOffer }));
    var buttonConvertToCompleteEncryptedName = Model.BtnConvertToCompleteEncryptedName;
    ViewData["selected"] = "reclutamiento";

    @Html.Partial("_PopUpConvertToCompleteCompMobile", new ViewDataDictionary { { "portalConfig", portalConfig }, { "baseProduct", Model.ProdCWConvertToComplete }, { "companyCredentials", companyCredentials }, { "HasNewFreemiumChat", Model.HasNewFreemiumChat } })
    @Html.Partial("_PopUpConvertToCompleteCompFromBasicMobile", new ViewDataDictionary { { "portalConfig", portalConfig }, { "baseProduct", Model.ProdCWConvertToComplete }, { "companyCredentials", companyCredentials }, { "HasNewFreemiumChat", Model.HasNewFreemiumChat } })
    @Html.Partial("PopUpsMobile/_ActionNotAllowedPopUpMobile", new ViewDataDictionary { { "portalConfig", portalConfig } })
    @Html.Partial("PopUpsMobile/_DeleteOfferPopUpMobile", new ViewDataDictionary { { "portalConfig", portalConfig } })
    @Html.Partial("PopUpsMobile/_InfoSelectAnyOfferPopUpMobile", new ViewDataDictionary { { "portalConfig", portalConfig } })
    @Html.Partial("PopUpsMobile/_FeatureActionPopUpMobile", new ViewDataDictionary { { "portalConfig", portalConfig } })
    @Html.Partial("PopUpsMobile/_UserNotCreditAvailablePopUpMobile", new ViewDataDictionary { { "portalConfig", portalConfig } })
}

@section TitleSection{
    <title>@PageLiteralsHelper.GetLiteral("HEAD_OFFERS_DEFAULT", pageId, portalConfig)</title>
}

@section CustomScriptsSection{
    @Scripts.Render("~/bundles/js/mobile/offerlistmobile")
    @Scripts.Render("~/bundles/js/mobile/convertToCompletePopupMobile")
}

@section CustomScriptsFooterSection{
    <script>
            var literalsOfferList = {
                LIT_OFFER_ACTION_UPDATING: '@PageLiteralsHelper.GetLiteral("LIT_OFFER_ACTION_UPDATING", pageId, portalConfig)',
                LIT_OFFER_ACTION_PROCESSING: '@PageLiteralsHelper.GetLiteral("LIT_OFFER_ACTION_PROCESSING", pageId, portalConfig)',
                LIT_OFFER_ACTION_ACTIVATING: '@PageLiteralsHelper.GetLiteral("LIT_OFFER_ACTION_ACTIVATING", pageId, portalConfig)',
                LIT_OFFER_ACTION_DEACTIVATING: '@PageLiteralsHelper.GetLiteral("LIT_OFFER_ACTION_DEACTIVATING", pageId, portalConfig)',
                LIT_OFFER_ACTION_DELETING: '@PageLiteralsHelper.GetLiteral("LIT_OFFER_ACTION_DELETING", pageId, portalConfig)',
                LIT_OFFER_ACTION_ARCHIVING: '@PageLiteralsHelper.GetLiteral("LIT_OFFER_ACTION_ARCHIVING", pageId, portalConfig)',
                LIT_OFFER_ACTION_RENEWING: '@PageLiteralsHelper.GetLiteral("LIT_OFFER_ACTION_RENEWING", pageId, portalConfig)',
                LIT_OFFER_ACTION_HIGHLIGHTING: '@PageLiteralsHelper.GetLiteral("LIT_OFFER_ACTION_HIGHLIGHTING", pageId, portalConfig)',
                LIT_OFFER_ACTION_SETTING_URGENT: '@PageLiteralsHelper.GetLiteral("LIT_OFFER_ACTION_SETTING_URGENT", pageId, portalConfig)',
                LIT_OFFER_ACTION_UPGRADING: '@PageLiteralsHelper.GetLiteral("LIT_OFFER_ACTION_UPGRADING", pageId, portalConfig)',
                LIT_OFFER_ACTION_RESULT_2: '@PageLiteralsHelper.GetLiteral("LIT_OFFER_ACTION_RESULT_2", pageId, portalConfig)',
                LIT_OFFER_ACTION_RESULT_6: '@PageLiteralsHelper.GetLiteral("LIT_OFFER_ACTION_RESULT_6", pageId, portalConfig)',
                LIT_OFFER_HIGHLIGHTED: '@PageLiteralsHelper.GetLiteral("LIT_OFFER_HIGHLIGHTED", pageId, portalConfig)',
                LIT_OFFER_URGENT: '@PageLiteralsHelper.GetLiteral("LIT_OFFER_URGENT", pageId, portalConfig)',
                LIT_OFFER_EDIT: '@PageLiteralsHelper.GetLiteral("LIT_OFFER_EDIT", pageId, portalConfig)',
                LIT_NOT_FEATURE_TEMPLATE: '@PageLiteralsHelper.GetLiteral("LIT_ACTION_NOT_ALLOWED_FEATURE", pageId, portalConfig)',
                LIT_OFFERS_DELETE_NOT_ALL_DESCRIPTION: '@PageLiteralsHelper.GetLiteral("LIT_OFFERS_DELETE_NOT_ALL_DESCRIPTION", pageId, portalConfig)',
                LIT_NOT_CONSUMIBLE: 'Para poder {0} debe contratar antes unidades',
                LIT_ACTION_NOT_ALLOWED_FEATURE_TITLE: '@PageLiteralsHelper.GetLiteral("LIT_ACTION_NOT_ALLOWED_FEATURE_TITLE", pageId, portalConfig)',
                LIT_DESC_POPUP_OFERT_DESTA: '@PageLiteralsHelper.GetLiteral("LIT_HIGHLIGHT_LIMIT", pageId, portalConfig)',
                LIT_DESC_POPUP_OFERT_URG: '@PageLiteralsHelper.GetLiteral("LIT_URGENT_LIMIT", pageId, portalConfig)',
                LIT_DESCRIPTION_POPUP_NOHIGHLIGHT_USERS: '@PageLiteralsHelper.GetLiteral("LIT_DESCRIPTION_POPUP_NOHIGHLIGHT_USERS", pageId, portalConfig)',
                LIT_DESCRIPTION_POPUP_NOURGENT_USERS: '@PageLiteralsHelper.GetLiteral("LIT_DESCRIPTION_POPUP_NOURGENT_USERS", pageId, portalConfig)',
                LIT_DESCRIPTION_POPUP_OFFER_USERS: '@PageLiteralsHelper.GetLiteral("LIT_DESCRIPTION_POPUP_OFFER_USERS", pageId, portalConfig)',
                LIT_OFFER_NOTIFICATION_NONE:'@PageLiteralsHelper.GetLiteral("LIT_NONE", pageId, portalConfig)',
                LIT_OFFER_NOTIFICATION_DAILY:'@PageLiteralsHelper.GetLiteral("LIT_DAILY", pageId, portalConfig)',
                LIT_OFFER_NOTIFICATION_WEEKLY: '@PageLiteralsHelper.GetLiteral("LIT_WEEKLY", pageId, portalConfig)',
                LIT_MATCHES_OFFER_MAIL_ENABLED: '@PageLiteralsHelper.GetLiteral("LIT_ENABLED", pageId, portalConfig)',
                LIT_MATCHES_OFFER_MAIL_DISABLED:'@PageLiteralsHelper.GetLiteral("LIT_DISABLED", pageId, portalConfig)',
            };
        InitializeOfferList({
            ajax: "@Url.Action("ExecuteAction", "CompanyOffers")",
            ajaxManageChangeOfferVisibility: "@Url.Action("ManageChangeOfferVisibility", "CompanyOffers")",
            ajaxManagePopUpConvertToCompleteBtt: "@Url.Action("ajaxManagePopUpConvertToCompleteBtt", "CompanyOffers")",
            ajaxMatchSummaryNotification: "@Url.Action("InsertOrUpdateMatchSummaryNotification", "CompanyOffers")",
            changeWithConsumeCreditLit: "@PageLiteralsHelper.GetLiteral("LIT_INFO_CHECK_VISUALIZATION", pageId, portalConfig)",
            changeWithoutConsumeCreditLit: "@PageLiteralsHelper.GetLiteral("LIT_CHANGE_OFFER_VISUALIZATION", pageId, portalConfig)",
            uncheckUrgentOfferLit: "@PageLiteralsHelper.GetLiteral("LIT_UNCHECK_URGENT_OFFER", pageId, portalConfig)",
            uncheckHighlightedOfferLit:"@PageLiteralsHelper.GetLiteral("LIT_UNCHECK_HIGHLIGHTED_OFFER", pageId, portalConfig)",
            infohighlighted: "@PageLiteralsHelper.GetLiteral("LIT_INFO_HIGHLIGHTED", pageId, portalConfig)",
            infourgent: "@PageLiteralsHelper.GetLiteral("LIT_INFO_URGENT", pageId, portalConfig)",
            infoLit: "@PageLiteralsHelper.GetLiteral("LIT_INFO", pageId, portalConfig)",
            infoLitCantChange: "@PageLiteralsHelper.GetLiteral("LIT_ACTION_NOT_ALLOWED", pageId, portalConfig)",
            urlKpis: "@Url.Action("ExecuteAddKpi", "Company")",
            literals: literalsOfferList,
            goToMPCartHighlightOffer: '@goToMPCartHighlightOffer',
            goToMPCartUrgentOffer: '@goToMPCartUrgentOffer',
            goToMPCartEditOffer: '@goToMPCartEditOffer',
            ajaxMatchesOfferMailSettings: "@Url.Action("UpdateMatchesOfferMailSettings", "CompanyOffers")",
            ajaxRssKpi: "@Url.Action("IncreaseKpiRSS", "CompanyOffers")",
        });
        InitializeConvertToCompletePopUp({
            goToPostPublishStepTwo: "@Url.Action("Index", "CompanyPostPublishStepTwo", new { oi = "#idofenc#", p = EncryptationHelper.Encrypt(((int)PageEnum.PostPublishStepTwo).ToString()), btn = Model.BtnConvertToCompleteEncryptedName })",
            goToMPCart: "@Url.Action("Index", "MultiPurchaseCart", new { oi = "#idofenc#", p = EncryptationHelper.Encrypt(((int)PageEnum.PackCart).ToString()), btn = Model.BtnConvertToCompleteEncryptedName, prod = "#product#" })",
            goToChooseMP:"@Url.Action("landing-convert-packs", "Company/Landings", new { io = "#idofenc#", cc= EncryptationHelper.Encrypt("1"), btnConvertEnc = Model.BtnConvertToCompleteEncryptedName })",
            ajaxManagePopUpConvertToCompleteBtt: "@Url.Action("ajaxManagePopUpConvertToCompleteBtt", "CompanyOffers")",
            idPage: @pageId,
            ajaxConvertToCompleteUrl: "@Url.Action("ExecuteAction", "CompanyOffers")",
            buttonConvertToCompleteEncryptedName : '@buttonConvertToCompleteEncryptedName'
        });
    </script>
}


@if (Model.AlertDataModel != null)
{
    Html.RenderPartial("_AlertNewPemMobile", Model.AlertDataModel, new ViewDataDictionary { { "portalConfig", portalConfig }, { "companyCredentials", companyCredentials } });
}


<main>
    <div class="pAllB">
        <p class="fs20 fwB tc">@PageLiteralsHelper.GetLiteral("TITLE_ADS_MANAGEMENT", pageId, portalConfig) <span class="fwN">(@StringToolsHelper.ChangeGroupPointForDecimalPoint(Model.Pager.TotalRows.ToString("N0"), portalConfig.PortalId) @PageLiteralsHelper.GetLiteral("TITLE_ADS", pageId, portalConfig))</span></p>
        @{Html.RenderPartial("_CompanyListOffersMobile", Model, new ViewDataDictionary { { "portalConfig", portalConfig } });}
    </div>
</main>


<div class="popup flex" id="popupGeneric">
    <div>
        <div>
            <p class="tc mbB" id="popupGenericDescription"></p>
            <div class="loading"><div><span></span><span></span><span></span><span></span></div><div><span></span><span></span><span></span><span></span></div><div><span></span><span></span><span></span><span></span></div></div>
        </div>
    </div>
</div>

<div class="popup draggable" id="frmActiveByUserCredits">
    <div>
        <div class="dragger">
            <div class="btn_click js_hide_popup">
                <span class="icon i_close"></span>
            </div>
        </div>
        <div class="content pt0">
            <p class="fs18 fwB tc mbB">@PageLiteralsHelper.GetLiteral("LIT_POPUP_OFERT_DESTA", pageId, portalConfig)</p>
            <p id="bodyMessageActive">@PageLiteralsHelper.GetLiteral("LIT_DESCRIPTION_POPUP_ACTIVE_USERS", pageId, portalConfig)</p>
            <button class="b_primary mtB" id="btnAcceptActiveByUserCredits"> @PageLiteralsHelper.GetLiteral("LIT_BTT_RENEW_OFFER", pageId, portalConfig)</button>
        </div>
    </div>
</div>

<input type="hidden" id="encryptedoffer" value="" />
<input type="hidden" id="savedcheckid" value="" />

<div class="popup draggable" id="js-recibiralertas">
    <div>
        <div class="dragger">
            <div class="btn_click js_hide_popup">
                <span class="icon i_close"></span>
            </div>
        </div>
        <div class="content pt0">
            <p class="tc mbB">@PageLiteralsHelper.GetLiteral("LIT_RENEW_OFFER_POPUP_TITLE", pageId, portalConfig)</p>

            <input type="button" class="b_primary" style="display: none" id="bttRenewOffer" value="@PageLiteralsHelper.GetLiteral("DD_RENOVAR", pageId, portalConfig)" onclick="javascript:renewOffer(document.getElementById('encryptedoffer').value);" />
            <input type="button" class="b_primary" style="display: none" id="bttConvertToComplete" value="@PageLiteralsHelper.GetLiteral("BTN_POP_CONSUMIR", pageId, portalConfig)" onclick="javascript: offersAction(document.getElementById('encryptedoffer').value,@((short)OfferActionEnum.Complete), '',this);" />
            <input type="button" class="b_primary" style="display: none" id="bttContact" value="@PageLiteralsHelper.GetLiteral("LIT_BTN_CONTACTAR", pageId, portalConfig)" onclick="window.location.href = '@Url.Action("Index", "Contact")'; loadingFullShow();" />
            <a class="b_transparent js_hide_popup" href="#" id="lnkCancelar" style="display: none">@PageLiteralsHelper.GetLiteral("LIT_CANCELAR", pageId, portalConfig)</a>
        </div>
    </div>
</div>

<input type="hidden" id="visualizationOffer" value="" />

@if (portalConfig.AEPortalConfig.ActivateCompanyCvsConsumible &&
    portalConfig.AEPortalConfig.ShowBBDDCvPromoTag)
{
    <script>
        $(document).ready(function () {
            var cookiekey = "SHOW_BBDDTAG_" + @companyCredentials.IdCompany + "_" + @companyCredentials.PortalId;
            var valor = clientStorage.inCookie().get(cookiekey);
            if (valor != "1") {
                $(".js_tag2_buscador_cv").removeClass("hide");
                clientStorage.inCookie().set(cookiekey, "1", 30, "/");
            }
        });
    </script>
}