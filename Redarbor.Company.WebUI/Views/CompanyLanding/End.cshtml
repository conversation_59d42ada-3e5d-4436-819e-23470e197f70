@model Redarbor.Company.WebUI.Models.Company.Landings.LandingNoMpDataModel
@using Redarbor.Company.WebUI.Helpers
@using Redarbor.Common.Entities.Enums

@{
    Layout = "~/Views/Shared/Layouts/_LayoutMasterNewPem.cshtml";
    var PageId = (int)PageEnum.LandingMembresiaAndPacks;
    var portalConfig = PortalConfigHelper.GetPortalConfiguration();
}

@section TitleSection{
    <title>@PageLiteralsHelper.GetLiteral("HEAD_NEW_LANDING", PageId, portalConfig)</title>
}

@section CustomScriptsFooterSection{
    @Scripts.Render("~/bundles/js/landing")
}

@Scripts.Render("~/bundles/js/analytics")

<script type="text/javascript">
    $(document).ready(function () {
        ga('send', 'event', 'Company Landing Welcome', 'Registro completado', 'Empresa registrada');
    });
</script>

<main>
    @Html.HiddenFor(model => model.GtmUniqueConversionId, new { @id = "gtm_unique_conversion_id" })
    <div class="tc pAllB">
        @if (Model.ShowWelcomeMessage)
        {
            <p class="fs30 fc_brand_aux fwB">@PageLiteralsHelper.GetLiteral("LIT_THANKS", PageId, portalConfig)</p>
            <p class="fs18 mtB mb10">@Html.Raw(PageLiteralsHelper.GetLiteral("LIT_WELCOME", PageId, portalConfig))</p>
        }
    </div>
    @{
        var totalProducts = Model.GroupProducts.Count();

        var gridClass = "grid" + totalProducts;
        var gridWidth = totalProducts == 1 ? "w40" : "w70";
    }
    <div id="seccio_landing" class="@gridClass @gridWidth mAuto mtB mbB full_m mb0_m w100_m">
        @foreach (var product in Model.GroupProducts)
        {
            Html.RenderPartial("_ProductGroupLanding", product);
        }
    </div>
    <div class="fr mb10 dB_m bg_white_m pAllB_m tc_m w100_m">
        @if (CompanyHelper.HasRates(portalConfig.countryId))
        {
            <span id="info_vat" class="fc_aux pl10 prB_m">@PageLiteralsHelper.GetLiteral("LIT_WITHOUT_VAT", PageId, portalConfig) @CompanyHelper.GetRateLiteral(portalConfig.countryId)</span>
        }
    </div>
</main>

@if (Model.VerificationByCodeModel.Enabled && Model.VerificationByCodeModel.Expires > 0 && !string.IsNullOrWhiteSpace(Model.VerificationByCodeModel.Code))
{
    Html.RenderPartial("Partials/PopupVerifyCode/_PopupVerifyCode", Model.VerificationByCodeModel);
}