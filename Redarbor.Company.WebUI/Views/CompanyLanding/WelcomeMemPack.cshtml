@model Redarbor.Company.WebUI.Models.Company.Landings.LandingWelcomeMemPackDataModel
@using Redarbor.Company.WebUI.Helpers
@using Redarbor.Common.Entities.Enums
@using Redarbor.Master.Entities.Enums

@{
    Layout = "~/Views/Shared/Layouts/_LayoutPaymentNewPem.cshtml";
    var PageId = (int)PageEnum.WelcomeCompany;
    var portalConfig = PortalConfigHelper.GetPortalConfiguration();
}

@section TitleSection{
    <title>@PageLiteralsHelper.GetLiteral("HEAD_NEW_LANDING", PageId, portalConfig)</title>
}

@section CustomScriptsFooterSection{
    @Scripts.Render("~/bundles/js/landing")
}

@Scripts.Render("~/bundles/js/analytics")

<script type="text/javascript">
    $(document).ready(function () {
        ga('send', 'event', 'Company Landing Welcome', 'Registro completado', 'Empresa registrada');
    });
</script>

<main>
    @Html.HiddenFor(model => model.GtmUniqueConversionId, new { @id = "gtm_unique_conversion_id" })
    <div class="tc pAllB">
        <p class="fs30 fc_brand_aux fwB">@PageLiteralsHelper.GetLiteral("LIT_THANKS", PageId, portalConfig)</p>
        <p class="fs18 mtB mb10">@Html.Raw(PageLiteralsHelper.GetLiteral("LIT_WELCOME", PageId, portalConfig))</p>
    </div>

    <div class="grid3 mb15 full_m mb0_m mt0_m">
        @{ Html.RenderPartial("Product/_LandingMemPackProducts", Model.ListLandingProductDataModel, new ViewDataDictionary { { "pageId", PageId } }); }
    </div>


    <div class="fr mb10 dB_m bg_white_m pAllB_m tc_m w100_m">
        <a href="@Url.Action("Index", "Contact")">@PageLiteralsHelper.GetLiteral("LIT_MORE_INFO", PageId, portalConfig)</a>
        @if (CompanyHelper.HasRates(portalConfig.countryId))
        {
            <span id="info_vat" class="fc_aux pl10 prB_m">@PageLiteralsHelper.GetLiteral("LIT_WITHOUT_VAT", PageId, portalConfig) @CompanyHelper.GetRateLiteral(portalConfig.countryId)</span>
        }
    </div>

    <div class="mt20 pAllB clear tc">
        @if (Model.AvailablesServiceBasic > 1)
        {
            <p class="fs16 pb20 pbB_m">@PageLiteralsHelper.GetLiteral("LIT_PUBLISH", PageId, portalConfig) @Model.AvailablesServiceBasic @PageLiteralsHelper.GetLiteral("LIT_FREE_OFFERS", PageId, portalConfig)</p>
        }
        else
        {
            <p class="fs16 pb20 pbB_m">@PageLiteralsHelper.GetLiteral("LIT_PUBLISH", PageId, portalConfig) @Model.AvailablesServiceBasic @PageLiteralsHelper.GetLiteral("LIT_FREE_OFFER", PageId, portalConfig)</p>
        }

        <a class="b_aux big" href="@Url.Action("Index","CompanyOffersPublish")">@PageLiteralsHelper.GetLiteral("PUBLISH_NOW", PageId, portalConfig)</a>
        <div class="mt10"> <a href="@Url.Action("", "Company")">@PageLiteralsHelper.GetLiteral("LIT_DONT_PUBLISH", PageId, portalConfig)</a></div>
    </div>
</main>

@if (Model.VerificationByCodeModel.Enabled && Model.VerificationByCodeModel.Expires > 0 && !string.IsNullOrWhiteSpace(Model.VerificationByCodeModel.Code))
{
    Html.RenderPartial("Partials/PopupVerifyCode/_PopupVerifyCode", Model.VerificationByCodeModel);
}

<script type="text/javascript">
    document.addEventListener('DOMContentLoaded', () => {
        $("#js-header_publishbtt").addClass("hide");
    })
</script>