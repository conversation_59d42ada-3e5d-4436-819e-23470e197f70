@model Redarbor.Company.WebUI.Models.Company.Landings.LandingWelcomeDataModel
@using Redarbor.Company.WebUI.Helpers
@using Redarbor.Common.Entities.Enums
@using Redarbor.Master.Entities.Enums

@{
    Layout = "~/Views/Shared/Layouts/_LayoutPaymentNewPem.cshtml";
    var PageId = (int)PageEnum.WelcomeCompany;
    var portalConfig = PortalConfigHelper.GetPortalConfiguration();
}

@section TitleSection{
    <title>@PageLiteralsHelper.GetLiteral("HEAD_NEW_LANDING", PageId, portalConfig)</title>
}

@Scripts.Render("~/bundles/js/analytics")

<script type="text/javascript">
    $(document).ready(function () {
        ga('send', 'event', 'Company Landing Welcome', 'Registro completado', 'Empresa registrada');
    });
</script>

@section CustomScriptsFooterSection{
    @Scripts.Render("~/bundles/js/landing")
}

<main>
    @Html.HiddenFor(model => model.GtmUniqueConversionId, new { @id = "gtm_unique_conversion_id" })
    <div class="box flowHid tc_m">
        <div class="w75 w100_m pAllB mAllB mAll0_m pAll0_m">
            <h1 class="fwB fs35 mbB fs30_m pb10">@PageLiteralsHelper.GetLiteral("LIT_WELCOME_CONGRATULATIONS", PageId, portalConfig)</h1>
            <p class="mbB fs21 fs18_m pbB">@PageLiteralsHelper.GetLiteral("LIT_WELCOME_SEARCH_TALENT", PageId, portalConfig)</p>

            @if (portalConfig.AEPortalConfig.PublishFromWelcomeSimplified)
            {
                <a class="b_primary mbB" href="@Url.Action("Index","CompanyOffersPublish", new { fws = EncryptationHelper.Encrypt("1") })">@PageLiteralsHelper.GetLiteral("LIT_WELCOME_FIRST_OFFER_BUTTON", PageId, portalConfig)</a>
            }
            else
            {
                <a class="b_primary mbB" href="@Url.Action("Index","CompanyOffersPublish")">@PageLiteralsHelper.GetLiteral("LIT_WELCOME_FIRST_OFFER_BUTTON", PageId, portalConfig)</a>
            }
            <p class="fc_aux fs13 pt15">@PageLiteralsHelper.GetLiteral("LIT_WELCOME_COMPANY_DATA_REVIEWED", PageId, portalConfig)</p>
            <img src="@Url.Content(string.Format("{0}img/landing/banner_promo_homeae-min.png{1}", portalConfig.PrefixPathImage, portalConfig.SufixPathImage))" class="hide_m posAbs top0" style="right: -100px; top: -10px;" />
        </div>
    </div>
</main>

@if (Model.VerificationByCodeModel.Enabled && Model.VerificationByCodeModel.Expires > 0 && !string.IsNullOrWhiteSpace(Model.VerificationByCodeModel.Code))
{
    Html.RenderPartial("Partials/PopupVerifyCode/_PopupVerifyCode", Model.VerificationByCodeModel);
}

<script type="text/javascript">
    document.addEventListener('DOMContentLoaded', () => {
        $("#js-header_publishbtt").addClass("hide");
    })
</script>