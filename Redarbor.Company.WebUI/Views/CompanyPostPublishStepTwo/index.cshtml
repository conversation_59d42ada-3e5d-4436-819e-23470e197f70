@model Redarbor.Company.WebUI.Models.Company.Offer.OfferPostPublishStepTwoDataModel
@using Redarbor.Master.Entities.Enums

@{
    Layout = "~/Views/Shared/Layouts/_LayoutMasterNewPem.cshtml";
    string guidJs = Guid.NewGuid().ToString();
}

@section TitleSection{
    <title>@Model.PageLiteralsDataModel.GetLiteral("LIT_OFFER_VISIBILITY")</title>
}

@section CustomScriptsSection{
    @Scripts.Render("~/bundles/js/postpublishsteptwo?v=" + guidJs)
}
    <main>
        @using (Html.BeginForm("Index", "CompanyPostPublishStepTwo", FormMethod.Post, new { id = "publishOffer", @class = "fl" }))
        {
            @Html.AntiForgeryToken()

            <nav class="breadcrumb hide_m">
                <a href="@Url.Action("Index", "Company")">@Model.PageLiteralsDataModel.GetLiteral("BREADCRUMB_INICIO")</a>
                @Html.Raw(Model.PageLiteralsDataModel.GetLiteral("BREADCRUMB_PUBL_OFERTA"))
            </nav>
            <header class="header_block full_m js_fixed small fl fn_m mb0_m">
                <div class="posRel title_m">
                    <h1 class="dIB mr10 fwB t_ellipsis">@(Model.IsUpdateOffer ? Model.PageLiteralsDataModel.GetLiteral("LIT_TITLE_EDIT") : Model.PageLiteralsDataModel.GetLiteral("LIT_TITLE_PUBLISH")) "@Model.NameOffer"</h1>
                    <span class="tag bg_white fc_base dB_m pl0_m mt5_m vtb">@Model.PageLiteralsDataModel.GetLiteral("LIT_STEP")</span>
                </div>
            </header>

            if (Model.ReturnToPreviousStep)
            {
                <a href="@Url.Action("Index", "CompanyOffersPostPublish", new { idEditOffer = Model.OfferIdEncrypted })" class="pl15_m dB fr fn_m mt10 mt0_m">@Model.PageLiteralsDataModel.GetLiteral("LIT_RETURN_STEP")</a>
            }

            <h2 class="fwB mb15 pAllB_m pb0_m clear">@Model.PageLiteralsDataModel.GetLiteral("LIT_OFFER_TYPE")</h2>

            <section class="w70 fl w100_m pAllB_m bb1_m">




                @*@if (Model.HavePromoOfferAvailable)
                {
                    <div class="box mb15 bAll1_m sel">
                        <div class="dFlex vm_fx dB_m">
                            <div class="field_radio mb0 fs18 lh1_3 dIB_m vt_m">
                                <label class="radio">
                                    @Html.RadioButtonFor(m => m.OfferGroupToConsume, @Model.CompleteOffersProduct.GroupId, new { Checked = "checked", @class = "managePublishButton", data_productname = @Model.GetLiteral("LIT_CUSTOM_OFFER") })
                                    <span class="input"></span>
                                    <span>@Model.GetLiteral("LIT_CUSTOM_OFFER")</span>
                                </label>
                            </div>
                        </div>
                        <p class="fc_aux">@Model.GetLiteral("LIT_CUSTOM_OFFER_DESC")</p>
                    </div>
                }
                else
                {*@
                    <div id="OfferFreemiumBox" class="box mb15 bAll1_m @(Model.BasicOffersProduct.AvailableUnits == 0 || Model.ItsPaymentOffer || Model.CompleteOffersProduct.AvailableUnits > 0?"disabled":"") @(Model.ShowBasicOffersProduct && Model.CompleteOffersProduct.AvailableUnits == 0 && !Model.HavePromoOfferAvailable?"":"hide")">
                        <div class="dFlex vm_fx dB_m">
                            <div class="field_radio mb0 fs18 lh1_3 dIB_m vt_m">
                                <label class="radio">
                                    @if (Model.BasicOffersProduct.AvailableUnits == 0 || Model.ItsPaymentOffer || Model.CompleteOffersProduct.AvailableUnits > 0)
                                    {
                                        @Html.RadioButtonFor(m => m.OfferGroupToConsume, @Model.BasicOffersProduct.GroupId, new { @disabled = "disabled" })
                                    }
                                    else
                                    {
                                        if (Model.CompleteOffersProduct.AvailableUnits > 0 && !Model.ItsPaymentOffer)
                                        {
                                            @Html.RadioButtonFor(m => m.OfferGroupToConsume, @Model.BasicOffersProduct.GroupId, new { @class = "managePublishButton", data_productname = @Model.PageLiteralsDataModel.GetLiteral("LIT_FREEMIUM_OFFER") })
                                        }
                                        else
                                        {
                                            @Html.RadioButtonFor(m => m.OfferGroupToConsume, @Model.BasicOffersProduct.GroupId, new { Checked = "checked", @class = "managePublishButton", data_productname = @Model.PageLiteralsDataModel.GetLiteral("LIT_FREEMIUM_OFFER") })
                                        }
                                    }
                                    <span class="input"></span>
                                </label>
                            </div>
                            <div class="hide_m"><span class="icon i_oferta_basica"></span></div>
                            <div class="pl15 dIB_m pl0_m">
                                <label for="js_oferta" class="fs16 fwB">@Model.PageLiteralsDataModel.GetLiteral("LIT_FREEMIUM_OFFER")</label>
                                <div class="info_tooltip">
                                    <div class="bubble_tooltip right hide">
                                        <p>@Model.PageLiteralsDataModel.GetLiteral("LIT_OFFER_FREEMIUM_DESC")</p>
                                    </div>
                                </div>
                                <ul>
                                    @Html.Raw(Model.PageLiteralsDataModel.GetLiteral("LIT_OFFER_FREEMIUM_SUBT"))
                                </ul>
                            </div>
                            @if (!Model.OfferIsPayment)
                            {
                                <div class="mlAuto tc tl_m mt15_m ml15_m pl15_m">
                                    <span class="tag fwB @(Model.BasicOffersProduct.AvailableUnits>0?"bg_ok":"") ml10">@Model.BasicOffersProduct.AvailableUnits @Model.PageLiteralsDataModel.GetLiteral("LIT_UNITS_AVAILABLE")</span>
                                </div>
                            }
                        </div>
                    </div>
               @* }*@



                <div id="OfferCompleteBox" class="box mbB sel bAll1_m sel @(Model.OfferProductsHasPromotions?"promo":"")">
                    @if (Model.OfferProductsHasPromotions)
                    {
                        <div class="promo_tag fs18">
                            <span class="fwB">@Model.OfferProductsPromotionName.</span> @Model.PageLiteralsDataModel.GetLiteral("LIT_PROMOTION_TEXT")
                        </div>
                    }

                    <div class="dFlex vm_fx dB_m">
                        <div class="field_radio mb0 lh1_3 vt_m dIB_m">
                            <label class="radio">
                                @if ((Model.CompleteOffersProduct.AvailableUnits == 0
                                    && Model.BasicOffersProduct.AvailableUnits > 0
                                    && !Model.ItsPaymentOffer)
                                    || Model.HavePromoOfferAvailable)
                                {
                                    @Html.RadioButtonFor(m => m.OfferGroupToConsume, @Model.CompleteOffersProduct.GroupId, new { @class = "managePublishButton", data_productname = @Model.PageLiteralsDataModel.GetLiteral("LIT_OFFER_COMPLETE") })
                                }
                                else
                                {
                                    @Html.RadioButtonFor(m => m.OfferGroupToConsume, @Model.CompleteOffersProduct.GroupId, new { Checked = "checked", @class = "managePublishButton", data_productname = @Model.PageLiteralsDataModel.GetLiteral("LIT_OFFER_COMPLETE") })
                                }
                                <span class="input"></span>
                            </label>
                        </div>
                        <div class="hide_m"><span class="icon i_oferta_completa"></span></div>
                        <div class="pl15 dIB_m pl0_m">
                            <label for="js_oferta" class="fs16 fwB mr5 vt">@Model.PageLiteralsDataModel.GetLiteral("LIT_OFFER_COMPLETE")</label>
                            <div class="info_tooltip">
                                <div class="bubble_tooltip right hide">
                                    <p>@Html.Raw(Model.PageLiteralsDataModel.GetLiteral("LIT_OFFER_COMPLETE_DESC"))</p>
                                </div>
                            </div>
                            <ul>
                                @Html.Raw(Model.PageLiteralsDataModel.GetLiteral("LIT_OFFER_COMPLETE_SUBT"))
                            </ul>
                        </div>
                        @if (Model.OfferIdStatus == (int)OfferStatusEnum.Borrador || !Model.OfferIsPayment)
                        {
                            <div class="mlAuto tc tl_m mt15_m ml15_m pl15_m">
                                <span class="tag @(Model.CompleteOffersProduct.AvailableUnits>0?"bg_ok":"") ml10">@Model.CompleteOffersProduct.AvailableUnits @Model.PageLiteralsDataModel.GetLiteral("LIT_UNITS_AVAILABLE")</span>
                            </div>
                        }
                    </div>


                    @if (Model.CompleteOffersProduct.AvailableUnits == 0 && !Model.ItsPaymentOffer)
                    {
                        <div id="OfferProductsList" @(Model.BasicOffersProduct.AvailableUnits > 0 ? "class=\"hide\"" : "")>

                            <div class="field_radio_box mb0 mtB">
                                <div class="group">
                                    @foreach (var offerProduct in Model.OfferProductsForSale)
                                    {
                                        if (Model.OfferProductsHasPromotions && offerProduct.Promotions.ToList().Count > 0)
                                        {
                                            var promotion = offerProduct.Promotions.ToList().First();

                                            <label class="radio w100">
                                                @if (offerProduct.Default)
                                                {
                                                    @Html.RadioButtonFor(m => m.OfferProductSelected, offerProduct.Id, new { Checked = "checked", @class = "managePublishButton", data_productname = offerProduct.ComercialName, data_productpricenum = promotion.NumericPrice, data_productprice = promotion.LiteralTotalPrice })
                                                }
                                                else
                                                {
                                                    @Html.RadioButtonFor(m => m.OfferProductSelected, offerProduct.Id, new { @class = "managePublishButton", data_productname = offerProduct.ComercialName, data_productpricenum = promotion.NumericPrice, data_productprice = promotion.LiteralTotalPrice })
                                                }
                                                <span class="input"></span>
                                                <span class="label_box">
                                                    <p class="mb5 lh1 dIB">
                                                        <span class="fwB fs18 mr5">@Html.Raw(offerProduct.ComercialName)</span>
                                                    </p>
                                                    <p class="fc_aux fs13 dIB fr">
                                                        <span class="mr15">
                                                            <span class="fc_promo fwB">@promotion.LiteralTotalPrice</span>
                                                            <span class="tdL">@Html.Raw(offerProduct.LiteralShowTotal)</span>
                                                        </span>
                                                        (<span class="fc_promo mr5">@promotion.LiteralPriceUnity</span>
                                                        <span class="tdL">@Html.Raw(offerProduct.LiteralShowUnit)</span> por unidad)
                                                    </p>
                                                </span>
                                            </label>
                                        }
                                        else
                                        {
                                            <label class="radio w100">
                                                @if (offerProduct.Default)
                                                {
                                                    @Html.RadioButtonFor(m => m.OfferProductSelected, offerProduct.Id, new { Checked = "checked", @class = "managePublishButton", data_productname = offerProduct.ComercialName, data_productpricenum = offerProduct.Price, data_productprice = offerProduct.LiteralShowTotal })
                                                }
                                                else
                                                {
                                                    @Html.RadioButtonFor(m => m.OfferProductSelected, offerProduct.Id, new { @class = "managePublishButton", data_productname = offerProduct.ComercialName, data_productpricenum = offerProduct.Price, data_productprice = offerProduct.LiteralShowTotal })
                                                }
                                                <span class="input"></span>
                                                <span class="label_box">
                                                    <p class="mb5 lh1 dIB">
                                                        <span class="fwB fs18">@Html.Raw(offerProduct.ComercialName)</span>
                                                    </p>
                                                    <p class="fc_aux fs13 dIB fr dIB">
                                                        <span class="mr15 fc_base">@Html.Raw(offerProduct.LiteralShowTotal)</span>
                                                        (@Html.Raw(offerProduct.LiteralShowUnit) por unidad)
                                                    </p>
                                                </span>
                                            </label>
                                        }
                                    }
                                </div>
                            </div>
                        </div>
                    }
                </div>


                <h2 class="mb15 fwB">@Model.PageLiteralsDataModel.GetLiteral("LIT_OFFER_FEATURES")</h2>







                @if (Model.ShowKillerQuestionsFeature)
                {
                    <div id="kqbox" class="box mb15 bAll1_m sel @(Model.KQProductsHasPromotions?"promo":"")">
                        @if (Model.KQProductsHasPromotions)
                        {
                            <div class="promo_tag fs18">
                                <span class="fwB">@Model.KQProductsPromotionName.</span> @Model.PageLiteralsDataModel.GetLiteral("LIT_PROMOTION_TEXT")
                            </div>
                        }

                        <div class="dFlex vm_fx dB_m">
                            <div class="field_checkbox mb0 fs16 vt_m dIB_m">
                                <label class="checkbox">
                                    @Html.CheckBoxFor(m => m.IsKQChecked, new { @class = "managePublishButton" })
                                    <span class="input"></span>
                                </label>
                                <span class="field-validation-valid"></span>
                            </div>
                            <div class="hide_m"><span class="icon i_tests"></span></div>

                            <div class="pl15 dIB_m pl0_m">
                                <label id="generickqname" for="js_kq" class="fs16 fwB mr5 vt">@Model.PageLiteralsDataModel.GetLiteral("LIT_KQ")</label>
                                <div class="info_tooltip">
                                    <div class="bubble_tooltip right hide">
                                        <p>@Html.Raw(Model.PageLiteralsDataModel.GetLiteral("LIT_KQ_DESC"))</p>
                                    </div>
                                </div>
                                <ul>
                                    @Html.Raw(Model.PageLiteralsDataModel.GetLiteral("LIT_KQ_SUBT"))
                                </ul>
                            </div>
                            @if (!Model.IsKQChecked || Model.OfferIdStatus == (int)OfferStatusEnum.Borrador)
                            { 
                                <div class="mlAuto tc tl_m mt15_m ml15_m pl15_m">
                                    <span class="tag @(Model.KQAvailableUnits>0?"bg_ok":"") fwB">@Model.KQAvailableUnits @Model.PageLiteralsDataModel.GetLiteral("LIT_UNITS_AVAILABLE")</span>
                                    @if ((!Model.IsKQChecked && Model.KQAvailableUnits == 0) || Model.OfferIdStatus == (int)OfferStatusEnum.Borrador)
                                    {
                                        <a id="addKQUnits" class="dB mtB">@Html.Raw(Model.PageLiteralsDataModel.GetLiteral("LIT_ADDUNITS"))</a>
                                    }
                                </div>
                            }
                        </div>

                        @if ((Model.KQAvailableUnits == 0 && !Model.IsKQChecked) || Model.OfferIdStatus == (int)OfferStatusEnum.Borrador)
                        {
                            <div id="KQProductsList" class="hide">
                                <div class="field_radio_box mb0 mtB">
                                    <div class="group">
                                        @foreach (var kqProduct in Model.KQProductsForSale)
                                        {
                                            if (Model.KQProductsHasPromotions && kqProduct.Promotions.ToList().Count > 0)
                                            {
                                                var promotion = kqProduct.Promotions.ToList().First();

                                                <label class="radio w100">
                                                    @if (kqProduct.Default)
                                                    {
                                                        @Html.RadioButtonFor(m => m.KQProductSelected, kqProduct.Id, new { Checked = "checked", @class = "managePublishButton", data_productname = kqProduct.ComercialName, data_productpricenum = promotion.NumericPrice, data_productprice = promotion.LiteralTotalPrice })
                                                    }
                                                    else
                                                    {
                                                        @Html.RadioButtonFor(m => m.KQProductSelected, kqProduct.Id, new { @class = "managePublishButton", data_productname = kqProduct.ComercialName, data_productpricenum = promotion.NumericPrice, data_productprice = promotion.LiteralTotalPrice })
                                                    }
                                                    <span class="input"></span>
                                                    <span class="label_box">
                                                        <p class="mb5 lh1 dIB">
                                                            <span class="fwB fs18 mr5">@Html.Raw(kqProduct.ComercialName)</span>
                                                        </p>
                                                        <p class="fc_aux fs13 dIB fr">
                                                            <span class="mr15">
                                                                <span class="fc_promo fwB">@promotion.LiteralTotalPrice</span>
                                                                <span class="tdL">@Html.Raw(kqProduct.LiteralShowTotal)</span>
                                                            </span>
                                                            (<span class="fc_promo mr5">@promotion.LiteralPriceUnity</span>
                                                            <span class="tdL">@Html.Raw(kqProduct.LiteralShowUnit)</span> por unidad)
                                                        </p>
                                                    </span>
                                                </label>
                                            }
                                            else
                                            {
                                                <label class="radio w100">
                                                    @if (kqProduct.Default)
                                                    {
                                                        @Html.RadioButtonFor(m => m.KQProductSelected, kqProduct.Id, new { Checked = "checked", @class = "managePublishButton", data_productname = kqProduct.ComercialName, data_productpricenum = kqProduct.Price, data_productprice = kqProduct.LiteralShowTotal })
                                                    }
                                                    else
                                                    {
                                                        @Html.RadioButtonFor(m => m.KQProductSelected, kqProduct.Id, new { @class = "managePublishButton", data_productname = kqProduct.ComercialName, data_productpricenum = kqProduct.Price, data_productprice = kqProduct.LiteralShowTotal })
                                                    }
                                                    <span class="input"></span>
                                                    <span class="label_box">
                                                        <p class="mb5 lh1 dIB">
                                                            <span class="fwB fs18">@Html.Raw(kqProduct.ComercialName)</span>
                                                        </p>
                                                        <p class="fc_aux fs13 fr dIB">
                                                            <span class="mr15 fc_base">@Html.Raw(kqProduct.LiteralShowTotal)</span>
                                                            (@Html.Raw(kqProduct.LiteralShowUnit) por unidad)
                                                        </p>
                                                    </span>
                                                </label>
                                            }
                                        }
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                }







                <div id="hiddenCompanyBox" class="box mb15 bAll1_m @(Model.HiddenCompanyProductsHasPromotions?"promo":"")">
                    @if (Model.HiddenCompanyProductsHasPromotions)
                    {
                        <div class="promo_tag fs18">
                            <span class="fwB">@Model.HiddenCompanyProductsPromotionName.</span> @Model.PageLiteralsDataModel.GetLiteral("LIT_PROMOTION_TEXT")
                        </div>
                    }

                    <div class="dFlex vm_fx dB_m">
                        <div class="field_checkbox mb0 fs16 vt_m dIB_m">
                            <label class="checkbox">
                                @Html.CheckBoxFor(m => m.IsHiddenCompanyChecked, new { @class = "managePublishButton" })
                                <span class="input"></span>
                            </label>
                            <span class="field-validation-valid"></span>
                        </div>
                        <div class="hide_m"><span class="icon i_confidencialidad"></span></div>
                        <div class="pl15 dIB_m pl0_m">
                            <label id="generichiddencompanyname" for="js_confidencialidad" class="fs16 fwB mr5 vt">@Model.PageLiteralsDataModel.GetLiteral("LIT_OFFER_CONFIDENTIALITY")</label>
                            <div class="info_tooltip">
                                <div class="bubble_tooltip right hide">
                                    <p>@Html.Raw(Model.PageLiteralsDataModel.GetLiteral("LIT_HIDDENNAME_DESC"))</p>
                                </div>
                            </div>
                            <ul>
                                @Html.Raw(Model.PageLiteralsDataModel.GetLiteral("LIT_HIDDENNAME_SUBT"))
                            </ul>
                        </div>
                        @if (!Model.IsHiddenCompanyChecked)
                        {
                            <div class="mlAuto tc tl_m mt15_m ml15_m pl15_m">
                                <span class="tag @(Model.HiddenCompanyAvailableUnits>0?"bg_ok":"") fwB">@Model.HiddenCompanyAvailableUnits @Model.PageLiteralsDataModel.GetLiteral("LIT_UNITS_AVAILABLE")</span>
                                @if (!Model.IsHiddenCompanyChecked && Model.HiddenCompanyAvailableUnits == 0)
                                {
                                    <a id="addHiddenCompanyUnits" class="dB mtB">@Html.Raw(Model.PageLiteralsDataModel.GetLiteral("LIT_ADDUNITS"))</a>
                                }
                            </div>
                        }
                    </div>

                    @if (Model.HiddenCompanyAvailableUnits == 0 && !Model.IsHiddenCompanyChecked)
                    {
                        <div id="hiddenCompanyProductsList" class="hide">
                            <div class="field_radio_box mb0 mtB">
                                <div class="group">

                                    @foreach (var hiddenCompanyProduct in Model.HiddenCompanyProductsForSale)
                                    {
                                        if (Model.HiddenCompanyProductsHasPromotions && hiddenCompanyProduct.Promotions.ToList().Count > 0)
                                        {
                                            var promotion = hiddenCompanyProduct.Promotions.ToList().First();

                                            <label class="radio w100">
                                                @if (hiddenCompanyProduct.Default)
                                                {
                                                    @Html.RadioButtonFor(m => m.HiddenCompanyProductSelected, hiddenCompanyProduct.Id, new { Checked = "checked", @class = "managePublishButton", data_productname = hiddenCompanyProduct.ComercialName, data_productpricenum = promotion.NumericPrice, data_productprice = promotion.LiteralTotalPrice })
                                                }
                                                else
                                                {
                                                    @Html.RadioButtonFor(m => m.HiddenCompanyProductSelected, hiddenCompanyProduct.Id, new { @class = "managePublishButton", data_productname = hiddenCompanyProduct.ComercialName, data_productpricenum = promotion.NumericPrice, data_productprice = promotion.LiteralTotalPrice })
                                                }
                                                <span class="input"></span>
                                                <span class="label_box">
                                                    <p class="mb5 lh1 dIB">
                                                        <span class="fwB fs18 mr5">@Html.Raw(hiddenCompanyProduct.ComercialName)</span>
                                                    </p>
                                                    <p class="fc_aux fs13 dIB fr">
                                                        <span class="mr15">
                                                            <span class="fc_promo fwB">@promotion.LiteralTotalPrice</span>
                                                            <span class="tdL">@Html.Raw(hiddenCompanyProduct.LiteralShowTotal)</span>
                                                        </span>
                                                        (<span class="fc_promo mr5">@promotion.LiteralPriceUnity</span>
                                                        <span class="tdL">@Html.Raw(hiddenCompanyProduct.LiteralShowUnit)</span> por unidad)
                                                    </p>
                                                </span>
                                            </label>
                                        }
                                        else
                                        {
                                            <label class="radio w100">
                                                @if (hiddenCompanyProduct.Default)
                                                {
                                                    @Html.RadioButtonFor(m => m.HiddenCompanyProductSelected, hiddenCompanyProduct.Id, new { Checked = "checked", @class = "managePublishButton", data_productname = hiddenCompanyProduct.ComercialName, data_productpricenum = hiddenCompanyProduct.Price, data_productprice = hiddenCompanyProduct.LiteralShowTotal })
                                                }
                                                else
                                                {
                                                    @Html.RadioButtonFor(m => m.HiddenCompanyProductSelected, hiddenCompanyProduct.Id, new { @class = "managePublishButton", data_productname = hiddenCompanyProduct.ComercialName, data_productpricenum = hiddenCompanyProduct.Price, data_productprice = hiddenCompanyProduct.LiteralShowTotal })
                                                }
                                                <span class="input"></span>
                                                <span class="label_box">
                                                    <p class="mb5 lh1 dIB">
                                                        <span class="fwB fs18">@Html.Raw(hiddenCompanyProduct.ComercialName)</span>
                                                    </p>
                                                    <p class="fc_aux fs13 fr dIB">
                                                        <span class="mr15 fc_base">@Html.Raw(hiddenCompanyProduct.LiteralShowTotal)</span>
                                                        (@Html.Raw(hiddenCompanyProduct.LiteralShowUnit) por unidad)
                                                    </p>
                                                </span>
                                            </label>
                                        }
                                    }

                                </div>
                            </div>
                        </div>
                    }
                    else
                    {
                        <div id="hiddenCompanyTextBox" class="hide">
                            <div class="field_input w50 w100_m">
                                <label>@Model.PageLiteralsDataModel.GetLiteral("FORM_NOMBRE_EMPRESA_SHOW")</label>
                                @Html.TextBoxFor(m => m.OfferHiddenCompanyName, new { @class = "cm-12" })
                                <span class="field-validation-valid">
                                    @Html.ValidationMessageFor(m => m.OfferHiddenCompanyName, "", new { @class = "pos_rel fl w_100 field-validation-valid" })
                                </span>
                            </div>
                        </div>
                    }
                </div>






                <div id="urgentBox" class="box mb15 bAll1_m @(Model.UrgentProductsHasPromotions?"promo":"")">
                    @if (Model.UrgentProductsHasPromotions)
                    {
                        <div class="promo_tag fs18">
                            <span class="fwB">@Model.UrgentProductsPromotionName.</span> @Model.PageLiteralsDataModel.GetLiteral("LIT_PROMOTION_TEXT")
                        </div>
                    }

                    <div class="dFlex vm_fx dB_m">
                        <div class="field_checkbox mb0 fs16 vt_m dIB_m">
                            <label class="checkbox">
                                @Html.CheckBoxFor(m => m.IsUrgentChecked, new { @class = "managePublishButton" })
                                <span class="input"></span>
                            </label>

                            <span class="field-validation-valid"></span>
                        </div>
                        <div class="hide_m"><span class="icon i_urgente"></span></div>
                        <div class="pl15 dIB_m pl0_m">
                            <label id="genericurgentname" for="js_urgencia" class="fs16 fwB mr5 vt">@Model.PageLiteralsDataModel.GetLiteral("LIT_URGENT")</label>
                            <div class="info_tooltip">
                                <div class="bubble_tooltip right hide">
                                    <p>@Html.Raw(Model.PageLiteralsDataModel.GetLiteral("LIT_URGENT_DESC"))</p>
                                </div>
                            </div>
                            <ul>
                                @Html.Raw(Model.PageLiteralsDataModel.GetLiteral("LIT_URGENT_SUBT"))
                            </ul>
                        </div>
                        @if (!Model.IsUrgentChecked)
                        {
                            <div class="mlAuto tc tl_m mt15_m ml15_m pl15_m">
                                <span class="tag @(Model.UrgentAvailableUnits > 0 ? "bg_ok" : "")  fwB">@Model.UrgentAvailableUnits @Model.PageLiteralsDataModel.GetLiteral("LIT_UNITS_AVAILABLE")</span>
                                @if (!Model.IsUrgentChecked && Model.UrgentAvailableUnits == 0)
                                {
                                    <a id="addUrgentUnits" class="dB mtB">@Html.Raw(Model.PageLiteralsDataModel.GetLiteral("LIT_ADDUNITS"))</a>
                                }
                            </div>
                        }
                    </div>

                    @if (Model.UrgentAvailableUnits == 0 && !Model.IsUrgentChecked)
                    {
                        <div id="urgentProductsList" class="hide">
                            <div class="field_radio_box mb0 mtB">
                                <div class="group">

                                    @foreach (var urgentProduct in Model.UrgentProductsForSale)
                                    {
                                        if (Model.UrgentProductsHasPromotions && urgentProduct.Promotions.ToList().Count > 0)
                                        {
                                            var promotion = urgentProduct.Promotions.ToList().First();

                                            <label class="radio w100">
                                                @if (urgentProduct.Default)
                                                {
                                                    @Html.RadioButtonFor(m => m.UrgentProductSelected, urgentProduct.Id, new { Checked = "checked", @class = "managePublishButton", data_productname = urgentProduct.ComercialName, data_productpricenum = promotion.NumericPrice, data_productprice = promotion.LiteralTotalPrice })
                                                }
                                                else
                                                {
                                                    @Html.RadioButtonFor(m => m.UrgentProductSelected, urgentProduct.Id, new { @class = "managePublishButton", data_productname = urgentProduct.ComercialName, data_productpricenum = promotion.NumericPrice, data_productprice = promotion.LiteralTotalPrice })
                                                }
                                                <span class="input"></span>
                                                <span class="label_box">
                                                    <p class="mb5 lh1 dIB">
                                                        <span class="fwB fs18 mr5">@Html.Raw(urgentProduct.ComercialName)</span>
                                                    </p>
                                                    <p class="fc_aux fs13 dIB fr">
                                                        <span class="mr15">
                                                            <span class="fc_promo fwB">@promotion.LiteralTotalPrice</span>
                                                            <span class="tdL">@Html.Raw(urgentProduct.LiteralShowTotal)</span>
                                                        </span>
                                                        (<span class="fc_promo mr5">@promotion.LiteralPriceUnity</span>
                                                        <span class="tdL">@Html.Raw(urgentProduct.LiteralShowUnit)</span> por unidad)
                                                    </p>
                                                </span>
                                            </label>
                                        }
                                        else
                                        {
                                            <label class="radio w100">
                                                @if (urgentProduct.Default)
                                                {
                                                    @Html.RadioButtonFor(m => m.UrgentProductSelected, urgentProduct.Id, new { Checked = "checked", @class = "managePublishButton", data_productname = urgentProduct.ComercialName, data_productpricenum = urgentProduct.Price, data_productprice = urgentProduct.LiteralShowTotal })
                                                }
                                                else
                                                {
                                                    @Html.RadioButtonFor(m => m.UrgentProductSelected, urgentProduct.Id, new { @class = "managePublishButton", data_productname = urgentProduct.ComercialName, data_productpricenum = urgentProduct.Price, data_productprice = urgentProduct.LiteralShowTotal })
                                                }
                                                <span class="input"></span>
                                                <span class="label_box">
                                                    <p class="mb5 lh1 dIB">
                                                        <span class="fwB fs18">@Html.Raw(urgentProduct.ComercialName)</span>
                                                    </p>
                                                    <p class="fc_aux fs13 fr dIB">
                                                        <span class="mr15 fc_base">@Html.Raw(urgentProduct.LiteralShowTotal)</span>
                                                        (@Html.Raw(urgentProduct.LiteralShowUnit) por unidad)
                                                    </p>
                                                </span>
                                            </label>
                                        }
                                    }

                                </div>
                            </div>
                        </div>
                    }
                </div>







                <div id="HighlightBox" class="box mb15 bAll1_m @(Model.HighlightProductsHasPromotions?"promo":"")">
                    @if (Model.HighlightProductsHasPromotions)
                    {
                        <div class="promo_tag fs18">
                            <span class="fwB">@Model.HighlightProductsPromotionName.</span> @Model.PageLiteralsDataModel.GetLiteral("LIT_PROMOTION_TEXT")
                        </div>
                    }

                    <div class="dFlex vm_fx dB_m">
                        <div class="field_checkbox mb0 fs16 vt_m dIB_m">
                            <label class="checkbox">
                                @Html.CheckBoxFor(m => m.IsHighlightChecked, new { @class = "managePublishButton" })
                                <span class="input"></span>
                            </label>
                            <span class="field-validation-valid"></span>
                        </div>
                        <div class="hide_m"><span class="icon i_destacado"></span></div>
                        <div class="pl15 dIB_m pl0_m">
                            <label id="generichighlightedname" for="js_destacado" class="fs16 fwB mr5 vt">@Model.PageLiteralsDataModel.GetLiteral("LIT_HIGHLIGHT")</label>
                            <div class="info_tooltip">
                                <div class="bubble_tooltip right hide">
                                    <p>@Html.Raw(Model.PageLiteralsDataModel.GetLiteral("LIT_HIGHLIGHT_DESC"))</p>
                                </div>
                            </div>
                            <ul>
                                @Html.Raw(Model.PageLiteralsDataModel.GetLiteral("LIT_HIGHLIGHT_SUBT"))
                            </ul>
                        </div>
                        @if (!Model.IsHighlightChecked)
                        {
                            <div class="mlAuto tc tl_m mt15_m ml15_m pl15_m">
                                <span class="tag @(Model.HighlightAvailableUnits > 0 ? "bg_ok" : "") fwB">@Html.Raw(Model.HighlightAvailableUnits) @Model.PageLiteralsDataModel.GetLiteral("LIT_UNITS_AVAILABLE")</span>
                                @if (!Model.IsHighlightChecked && Model.HighlightAvailableUnits == 0)
                                {
                                    <a id="addHighlightUnits" class="dB mtB">@Html.Raw(Model.PageLiteralsDataModel.GetLiteral("LIT_ADDUNITS"))</a>
                                }
                            </div>
                        }
                    </div>

                    @if (Model.HighlightAvailableUnits == 0 && !Model.IsHighlightChecked)
                    {
                        <div id="HighlightProductsList" class="hide">
                            <div class="field_radio_box mb0 mtB">
                                <div class="group">

                                    @foreach (var highlightProduct in Model.HighlightProductsForSale)
                                    {
                                        if (Model.HighlightProductsHasPromotions && highlightProduct.Promotions.ToList().Count > 0)
                                        {
                                            var promotion = highlightProduct.Promotions.ToList().First();

                                            <label class="radio w100">
                                                @if (highlightProduct.Default)
                                                {
                                                    @Html.RadioButtonFor(m => m.HighlightProductSelected, highlightProduct.Id, new { Checked = "checked", @class = "managePublishButton", data_productname = highlightProduct.ComercialName, data_productpricenum = promotion.NumericPrice, data_productprice = promotion.LiteralTotalPrice })
                                                }
                                                else
                                                {
                                                    @Html.RadioButtonFor(m => m.HighlightProductSelected, highlightProduct.Id, new { @class = "managePublishButton", data_productname = highlightProduct.ComercialName, data_productpricenum = promotion.NumericPrice, data_productprice = promotion.LiteralTotalPrice })
                                                }
                                                <span class="input"></span>
                                                <span class="label_box">
                                                    <p class="mb5 lh1 dIB">
                                                        <span class="fwB fs18 mr5">@Html.Raw(highlightProduct.ComercialName)</span>
                                                    </p>
                                                    <p class="fc_aux fs13 dIB fr">
                                                        <span class="mr15">
                                                            <span class="fc_promo fwB">@promotion.LiteralTotalPrice</span>
                                                            <span class="tdL">@Html.Raw(highlightProduct.LiteralShowTotal)</span>
                                                        </span>
                                                        (<span class="fc_promo mr5">@promotion.LiteralPriceUnity</span>
                                                        <span class="tdL">@Html.Raw(highlightProduct.LiteralShowUnit)</span> por unidad)
                                                    </p>
                                                </span>
                                            </label>
                                        }
                                        else
                                        {
                                            <label class="radio w100">
                                                @if (highlightProduct.Default)
                                                {
                                                    @Html.RadioButtonFor(m => m.HighlightProductSelected, highlightProduct.Id, new { Checked = "checked", @class = "managePublishButton", data_productname = highlightProduct.ComercialName, data_productpricenum = highlightProduct.Price, data_productprice = highlightProduct.LiteralShowTotal })
                                                }
                                                else
                                                {
                                                    @Html.RadioButtonFor(m => m.HighlightProductSelected, highlightProduct.Id, new { @class = "managePublishButton", data_productname = highlightProduct.ComercialName, data_productpricenum = highlightProduct.Price, data_productprice = highlightProduct.LiteralShowTotal })
                                                }
                                                <span class="input"></span>
                                                <span class="label_box">
                                                    <p class="mb5 lh1 dIB">
                                                        <span class="fwB fs18">@Html.Raw(highlightProduct.ComercialName)</span>
                                                    </p>
                                                    <p class="fc_aux fs13 fr dIB">
                                                        <span class="mr15 fc_base">@Html.Raw(highlightProduct.LiteralShowTotal)</span>
                                                        (@Html.Raw(highlightProduct.LiteralShowUnit) por unidad)
                                                    </p>
                                                </span>
                                            </label>
                                        }
                                    }

                                </div>
                            </div>
                        </div>
                    }
                </div>






                @if (Model.ShowContactDataProductsForSale.Any())
                {
                    <div id="FlashBox" class="box mb15 bAll1_m @(Model.FlashProductsHasPromotions ? "promo" : "")">
                        @if (Model.FlashProductsHasPromotions)
                        {
                            <div class="promo_tag fs18">
                                <span class="fwB">@Model.FlashProductsPromotionName.</span> @Model.PageLiteralsDataModel.GetLiteral("LIT_PROMOTION_TEXT")
                            </div>
                        }
                        <div class="dFlex">
                            <div class="field_checkbox mb15 mb0_m fs16">
                                <label class="checkbox">
                                    @Html.CheckBoxFor(m => m.IsFlashChecked, new { @class = "managePublishButton" })
                                    <span class="input"></span>
                                    <span id="genericflashname">@Model.PageLiteralsDataModel.GetLiteral("LIT_FLASH")</span>
                                </label>
                                <span class="field-validation-valid"></span>
                            </div>
                            @if (!Model.IsFlashChecked)
                            {
                                <div class="mlAuto tc tl_m mt15_m ml15_m pl15_m">
                                    <span class="tag @(Model.FlashAvailableUnits > 0 ? "bg_ok" : "") fwB">@Model.FlashAvailableUnits @Model.PageLiteralsDataModel.GetLiteral("LIT_UNITS_AVAILABLE")</span>
                                    @if (!Model.IsFlashChecked && Model.FlashAvailableUnits == 0)
                                    {
                                        <a id="addFlashUnits" class="dB mtB">@Html.Raw(Model.PageLiteralsDataModel.GetLiteral("LIT_ADDUNITS"))</a>
                                    }
                                </div>
                            }
                        </div>
                        <div class="dFlex vm_fx mb15">
                            <p class="fc_aux w100">@Html.Raw(Model.PageLiteralsDataModel.GetLiteral("LIT_FLASH_DESC"))</p>
                            @Html.Raw(Model.PageLiteralsDataModel.GetLiteral("LIT_FLASH_SUBT"))
                        </div>

                        @if (Model.FlashAvailableUnits == 0 && !Model.IsFlashChecked)
                        {
                            <div id="FlashProductsList" class="hide">
                                <div class="field_radio_box mb0 mtB">
                                    <div class="group">

                                        @foreach (var flashProduct in Model.FlashProductsForSale)
                                        {
                                            if (Model.FlashProductsHasPromotions && flashProduct.Promotions.ToList().Count > 0)
                                            {
                                                var promotion = flashProduct.Promotions.ToList().First();

                                                <label class="radio w100">
                                                    @if (flashProduct.Default)
                                                    {
                                                        @Html.RadioButtonFor(m => m.FlashProductSelected, flashProduct.Id, new { Checked = "checked", @class = "managePublishButton", data_productname = flashProduct.ComercialName, data_productpricenum = promotion.NumericPrice, data_productprice = promotion.LiteralTotalPrice })
                                                    }
                                                    else
                                                    {
                                                        @Html.RadioButtonFor(m => m.FlashProductSelected, flashProduct.Id, new { @class = "managePublishButton", data_productname = flashProduct.ComercialName, data_productpricenum = promotion.NumericPrice, data_productprice = promotion.LiteralTotalPrice })
                                                    }
                                                    <span class="input"></span>
                                                    <span class="label_box">
                                                        <p class="mb5 lh1 dIB">
                                                            <span class="fwB fs18 mr5">@Html.Raw(flashProduct.ComercialName)</span>
                                                        </p>
                                                        <p class="fc_aux fs13 dIB fr">
                                                            <span class="mr15">
                                                                <span class="fc_promo fwB">@promotion.LiteralTotalPrice</span>
                                                                <span class="tdL">@Html.Raw(flashProduct.LiteralShowTotal)</span>
                                                            </span>
                                                            (<span class="fc_promo mr5">@promotion.LiteralPriceUnity</span>
                                                            <span class="tdL">@Html.Raw(flashProduct.LiteralShowUnit)</span> por unidad)
                                                        </p>
                                                    </span>
                                                </label>
                                            }
                                            else
                                            {
                                                <label class="radio w100">

                                                    @if (flashProduct.Default)
                                                    {
                                                        @Html.RadioButtonFor(m => m.FlashProductSelected, flashProduct.Id, new { Checked = "checked", @class = "managePublishButton", data_productname = flashProduct.ComercialName, data_productpricenum = flashProduct.Price, data_productprice = flashProduct.LiteralShowTotal })
                                                    }
                                                    else
                                                    {
                                                        @Html.RadioButtonFor(m => m.FlashProductSelected, flashProduct.Id, new { @class = "managePublishButton", data_productname = flashProduct.ComercialName, data_productpricenum = flashProduct.Price, data_productprice = flashProduct.LiteralShowTotal })
                                                    }
                                                    <span class="input"></span>
                                                    <span class="label_box">
                                                        <p class="mb5 lh1 dIB">
                                                            <span class="fwB fs18">@Html.Raw(flashProduct.ComercialName)</span>
                                                        </p>
                                                        <p class="fc_aux fs13 fr dIB">
                                                            <span class="mr15 fc_base">@Html.Raw(flashProduct.LiteralShowTotal)</span>
                                                            (@Html.Raw(flashProduct.LiteralShowUnit) por unidad)
                                                        </p>
                                                    </span>
                                                </label>
                                            }
                                        }
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                }

                @if (Model.ShowContactDataProductsForSale.Any())
                {
                    <div id="ShowContactDataBox" class="box mb15 bAll1_m @(Model.ShowContactProductsHasPromotions?"promo":"")">
                        @if (Model.ShowContactProductsHasPromotions)
                        {
                            <div class="promo_tag fs18">
                                <span class="fwB">@Model.ShowContactDataProductsPromotionName.</span> @Model.PageLiteralsDataModel.GetLiteral("LIT_PROMOTION_TEXT")
                            </div>
                        }
                        <div class="dFlex">
                            <div class="field_checkbox mb15 mb0_m fs16">
                                <label class="checkbox">
                                    @if (Model.ShowOfferContactData)
                                    {
                                        @Html.CheckBoxFor(m => m.IsShowContactDataChecked, new { @class = "managePublishButton", @checked = "checked" })
                                    }
                                    else
                                    {
                                        @Html.CheckBoxFor(m => m.IsShowContactDataChecked, new { @class = "managePublishButton" })
                                    }
                                    <span class="input"></span>
                                    <span id="genericshname">@Model.PageLiteralsDataModel.GetLiteral("LIT_SHOW_CONTACT_DATA")</span>
                                </label>
                                <span class="field-validation-valid"></span>
                            </div>
                            @if (!Model.ShowOfferContactData)
                            {
                                <div class="mlAuto tc tl_m mt15_m ml15_m pl15_m">
                                    <span class="tag @(Model.ShowContactDataAvailableUnits>0?"bg_ok":"") fwB">@Model.ShowContactDataAvailableUnits @Model.PageLiteralsDataModel.GetLiteral("LIT_UNITS_AVAILABLE")</span>
                                    @if (!Model.IsShowContactDataChecked && Model.ShowContactDataAvailableUnits == 0 && !Model.ShowOfferContactData)
                                    {
                                        <a id="addShowContactDataUnits" class="dB mtB">@Html.Raw(Model.PageLiteralsDataModel.GetLiteral("LIT_ADDUNITS"))</a>
                                    }
                                </div>
                            }
                        </div>
                        <div class="dFlex vm_fx mb15">
                            <p class="fc_aux w100">@Html.Raw(Model.PageLiteralsDataModel.GetLiteral("LIT_SHOW_CONTACT_DATA_DESC"))</p>
                            @Html.Raw(Model.PageLiteralsDataModel.GetLiteral("LIT_SHOW_CONTACT_DATA_SUBT"))
                        </div>

                        @if (Model.ShowContactDataAvailableUnits == 0
                            && !Model.ShowOfferContactData)
                        {
                            <div id="ShowContactDataProductsList" class="hide">
                                <div class="field_radio_box mb0 mtB">
                                    <div class="group">

                                        @foreach (var showContactDataProduct in Model.ShowContactDataProductsForSale)
                                        {
                                            if (Model.ShowContactProductsHasPromotions && showContactDataProduct.Promotions.ToList().Count > 0)
                                            {
                                                var promotion = showContactDataProduct.Promotions.ToList().First();

                                                <label class="radio w100">
                                                    @if (showContactDataProduct.Default)
                                                    {
                                                        @Html.RadioButtonFor(m => m.ShowContactDataProductSelected, showContactDataProduct.Id, new { Checked = "checked", @class = "managePublishButton", data_productname = showContactDataProduct.ComercialName, data_productpricenum = promotion.NumericPrice, data_productprice = promotion.LiteralTotalPrice })
                                                    }
                                                    else
                                                    {
                                                        @Html.RadioButtonFor(m => m.ShowContactDataProductSelected, showContactDataProduct.Id, new { @class = "managePublishButton", data_productname = showContactDataProduct.ComercialName, data_productpricenum = promotion.NumericPrice, data_productprice = promotion.LiteralTotalPrice })
                                                    }
                                                    <span class="input"></span>
                                                    <span class="label_box">
                                                        <p class="mb5 lh1 dIB">
                                                            <span class="fwB fs18 mr5">@Html.Raw(showContactDataProduct.ComercialName)</span>
                                                        </p>
                                                        <p class="fc_aux fs13 dIB fr">
                                                            <span class="mr15">
                                                                <span class="fc_promo fwB">@promotion.LiteralTotalPrice</span>
                                                                <span class="tdL">@Html.Raw(showContactDataProduct.LiteralShowTotal)</span>
                                                            </span>
                                                            (<span class="fc_promo mr5">@promotion.LiteralPriceUnity</span>
                                                            <span class="tdL">@Html.Raw(showContactDataProduct.LiteralShowUnit)</span> por unidad)
                                                        </p>
                                                    </span>
                                                </label>
                                            }
                                            else
                                            {
                                                <label class="radio w100">
                                                    @if (showContactDataProduct.Default)
                                                    {
                                                        @Html.RadioButtonFor(m => m.ShowContactDataProductSelected, showContactDataProduct.Id, new { Checked = "checked", @class = "managePublishButton", data_productname = showContactDataProduct.ComercialName, data_productpricenum = showContactDataProduct.Price, data_productprice = showContactDataProduct.LiteralShowTotal })
                                                    }
                                                    else
                                                    {
                                                        @Html.RadioButtonFor(m => m.ShowContactDataProductSelected, showContactDataProduct.Id, new { @class = "managePublishButton", data_productname = showContactDataProduct.ComercialName, data_productpricenum = showContactDataProduct.Price, data_productprice = showContactDataProduct.LiteralShowTotal })
                                                    }
                                                    <span class="input"></span>
                                                    <span class="label_box">
                                                        <p class="mb5 lh1 dIB">
                                                            <span class="fwB fs18">@Html.Raw(showContactDataProduct.ComercialName)</span>
                                                        </p>
                                                        <p class="fc_aux fs13 fr dIB">
                                                            <span class="mr15 fc_base">@Html.Raw(showContactDataProduct.LiteralShowTotal)</span>
                                                            (@Html.Raw(showContactDataProduct.LiteralShowUnit) por unidad)
                                                        </p>
                                                    </span>
                                                </label>
                                            }
                                        }
                                    </div>
                                </div>
                            </div>
                        }
                        else
                        {
                            <div id="ShowContactDataTextBoxes">
                                <div id="form_contact_email" class="field_input w50 w100_m @(Model.OfferShowContactEmail ? "display:none" : "")">
                                    <label>@Model.PageLiteralsDataModel.GetLiteral("FORM_EMAIL_CONTACTO")</label>
                                    @Html.TextBoxFor(m => m.OfferContactEmail, new { @class = "cm-12" })
                                    <span class="field-validation-valid">
                                        @Html.ValidationMessageFor(m => m.OfferContactEmail, "", new { @class = "pos_rel fl w_100 field-validation-valid" })
                                    </span>
                                </div>

                                <div id="form_contact_phone" class="field_input w50 w100_m @(Model.OfferShowContactPhone ? "display:none" : "")">
                                    <label>@Model.PageLiteralsDataModel.GetLiteral("FORM_TELEFONO_CONTACTO")</label>
                                    @Html.TextBoxFor(m => m.OfferContactPhone, new { @class = "cm-12" })
                                    <span class="field-validation-valid">
                                        @Html.ValidationMessageFor(m => m.OfferContactPhone, "", new { @class = "pos_rel fl w_100 field-validation-valid" })
                                    </span>
                                </div>

                                <div id="form_contact_address" class="field_input w50 w100_m @(Model.OfferShowContactAdress ? "display:none" : "")">
                                    <label>@Model.PageLiteralsDataModel.GetLiteral("FORM_DIRECCION_CONTACTO")</label>
                                    @Html.TextBoxFor(m => m.OfferContactAddress, new { @class = "cm-12" })
                                    <span class="field-validation-valid">
                                        @Html.ValidationMessageFor(m => m.OfferContactAddress, "", new { @class = "pos_rel fl w_100 field-validation-valid" })
                                    </span>
                                </div>
                            </div>
                        }

                    </div>
                }
            </section>

            <section class="box fr w30_mB w100_m posSticky top65 pb0">
                <h3 class="fwB mb15">@Model.PageLiteralsDataModel.GetLiteral("LIT_ORDER_SUMMARY")</h3>
                <div id="summaryProducts" class="table tiny mb15"></div>

                <div id="goToCartBtt" class="info_button hide">
                    <div class="submitbtn">
                        <input type="submit" class="js-processing b_primary big w100" value="@Model.PageLiteralsDataModel.GetLiteral("LIT_BUY")" />
                    </div>
                    @if (Model.MaskedCardNumber != string.Empty)
                    {
                        <div class="cols fs12 mb10 mt15">
                            <span class="w100">@Model.PageLiteralsDataModel.GetLiteral("LIT_NIT_TXT")<span>@Model.MaskedCardNumber</span></span>
                            <a href="#" class="gotocart">@Model.PageLiteralsDataModel.GetLiteral("LIT_CHANGE")</a>
                        </div>

                        <div class="cols fs12 mb10">
                            <span class="w100">@Model.PageLiteralsDataModel.GetLiteral("LIT_CARD_TXT")<span> @Model.NIT</span></span>
                            <a href="#" class="gotocart">@Model.PageLiteralsDataModel.GetLiteral("LIT_CHANGE")</a>
                        </div>
                    }

                </div>

                <div id="updatePublishBtt" class="info_button">
                    <div class="submitbtn">
                        @if (Model.IsUpdateOffer)
                        {
                            <input type="submit" class="js-processing b_primary big w100" value="@Model.PageLiteralsDataModel.GetLiteral("LIT_UPDATE")" />
                        }
                        else
                        {
                            <input type="button" id="btCp" class="js-processing b_primary big w100" value="@Model.PageLiteralsDataModel.GetLiteral("LIT_PUBLISH")" />
                        }
                    </div>
                </div>

                <p class="fc_aux fs12 mt15 pb20">@Model.PageLiteralsDataModel.GetLiteral("LIT_ORDER_SUMMARY_FOOTER")</p>

                <div id="offerFeaturesDesc" class="bg_brand_light bt1 pAllB mB_neg hide">
                    <h4 class="fwB mb10">@Model.PageLiteralsDataModel.GetLiteral("LIT_PRODUCT_FEATURES")</h4>
                    <ul class="table tiny">
                        @foreach (var item in Model.OfferFeaturesDescription)
                        {
                            <li>
                                <div class="pr10">
                                    <span title="Contiene" class="icon @(item.Available == 1 ? "i_yes_small" : "i_no_small")"></span>
                                </div>
                                <span>@item.Literal</span>
                            </li>
                        }
                    </ul>
                </div>

            </section>

            @Html.HiddenFor(m => m.OfferIdEncrypted);
            @Html.HiddenFor(m => m.MakePayment);
            @Html.HiddenFor(m => m.NIT);
            @Html.HiddenFor(m => m.MaskedCardNumber);
        }
    </main>


    @Html.DisplayFor(p => p.PublishOfferPopUp, new
    {
        IdPopUp = "publishOfferPop",
        IdBtnOk = "SubmitPage",
        IdMessage = "bodyMessage",
    })


    @section CustomScriptsFooterSection{
        <script type="text/javascript">
        InitializePostPublishStepTwo({
            ajaxSumProducts: "@Url.Action("SumProducts", "CompanyPostPublishStepTwo")",
            litVatNumber: "@Model.LitVatNumber",
            litVatLiteral: "@Model.LitVatLiteral",
            litAddUnits: "@Html.Raw(Model.PageLiteralsDataModel.GetLiteral("LIT_ADDUNITS"))",
            litRemoveUnits: "@Model.PageLiteralsDataModel.GetLiteral("LIT_REMOVEUNITS")"
        });
        </script>
    }

