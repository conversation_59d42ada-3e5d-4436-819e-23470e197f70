@model Redarbor.Company.WebUI.Models.Company.Configuration.CompanyReportDataModel
@using Redarbor.Company.WebUI.Helpers.Html
@using Redarbor.Company.WebUI.Helpers
@using Redarbor.Common.Entities.Enums
@using Redarbor.Company.WebUI.Enums

@{
    Layout = "~/Views/Shared/Layouts/_LayoutMasterNewPem.cshtml";
    int PageId = Html.SetCurrentPage((int)PageEnum.ListReports);
    var portalConfig = PortalConfigHelper.GetPortalConfiguration();
}

@section TitleSection{
    <title>@PageLiteralsHelper.GetLiteral("HEAD_REPORT_COMPANY", PageId, portalConfig)</title>
}

@section CustomScriptsSection{
    @Scripts.Render("~/bundles/js/report")
<script type="text/javascript">
        var configReportModel = {
            urlActionFormIndexDelete: '@Url.Action("Delete","CompanyReport")',
            urlActionFormIndex:'@Url.Action("Index","CompanyReport")',
            UrlActionFormDownload: '@Url.Action("Download", "CompanyReport")',
            activeAjaxCheckDownload: false,
            idReportEncrypted: '',
            urlActionCheckCreate: '',
            dateFormatPortal: '@StringToolsHelper.GetFormatDatePortal(DateFormatEnum.ShortDateJS)',
            rangeDaysReport: @portalConfig.AEPortalConfig.RangeDaysReport
        };
        InitializeReport(configReportModel);
</script>
}

<main>

    <nav class="breadcrumb mbB hide_m">
        <a href="@Url.Action("Index","Company")">@PageLiteralsHelper.GetLiteral("LIT_INICIO", PageId, portalConfig)</a>
        @PageLiteralsHelper.GetLiteral("HEAD_REPORT_COMPANY", PageId, portalConfig)
    </nav>

    <header class="header_block mb5 full_m mb0_m">
        <h1 class="mrAuto title_m">@PageLiteralsHelper.GetLiteral("LIT_TITLE_COMPANY_REPORTS", PageId, portalConfig)</h1>
        <div id="divBtnExtractions" class="plB_m prB_m w100_m">
            <a id="linkExtractions" class="b_primary_inv">@PageLiteralsHelper.GetLiteral("LIT_CREATE_REPORT", PageId, portalConfig)</a>
        </div>
    </header>

    @using (Html.BeginForm("Create", "CompanyReport", FormMethod.Post, new { id = "create_report" }))
    {
        @Html.AntiForgeryToken()
        <section class="box mt15 mb15 hide bb0_m mt0_m mb0_m @(Model.Pager.TotalRows > 0 ? "hide" : "")" id="form_create_report">
            <h2 class="fs18 fwB mbB hide_m">@PageLiteralsHelper.GetLiteral("LIT_NEW_REPORT", PageId, portalConfig)<span class="fs14"> @PageLiteralsHelper.GetLiteral("LIT_NEW_REPORT_MAX_MONTHS", PageId, portalConfig)</span></h2>
            <div class="field_group_hor">
                <label>@PageLiteralsHelper.GetLiteral("LIT_PERIODO", PageId, portalConfig)</label>
                <div class="w45 w100_m">
                    <div class="field_calendar hor datepicker w46 mb0">
                        <div class="w100">
                            <div>
                                @Html.TextBoxFor(model => model.InitialDate, new
                           {
                               @Value = Model.InitialDate == null ? null : StringToolsHelper.GetLocalizedDate(Convert.ToDateTime(Model.InitialDate), DateFormatEnum.ShortDateJS),
                               @AutoComplete = "off",
                               placeholder = PageLiteralsHelper.GetLiteral("LIT_DATE_INIT", PageId, portalConfig)
                           })
                            </div>
                            @Html.ValidationMessageFor(model => model.InitialDate, "")
                        </div>
                    </div>
                    <span class="field_info tc w4">-</span>
                    <div class="field_calendar hor datepicker w46 end mb0">
                        <div class="w100">
                            <div>
                                @Html.TextBoxFor(model => model.EndDate, new
                           {
                               @Value = Model.EndDate == null ? null : StringToolsHelper.GetLocalizedDate(Convert.ToDateTime(Model.EndDate), DateFormatEnum.ShortDateJS),
                               @AutoComplete = "off",
                               placeholder = PageLiteralsHelper.GetLiteral("LIT_DATE_FINAL", PageId, portalConfig)
                           })
                            </div>
                            @Html.ValidationMessageFor(model => model.EndDate, "")
                        </div>
                    </div>
                </div>
            </div>
            <div class="field_radio_box hor">
                <label>@PageLiteralsHelper.GetLiteral("LIT_FORMAT_EXPORT", PageId, portalConfig)</label>
                <div class="group w45">
                    <div>
                        <label class="radio">
                            @Html.RadioButtonFor(r => r.TypeFile, ((short)FileTypeEnum.CSV), new { name = "format_export", @id = "rd_csv" })
                            <span class="input"></span>
                            <span class="label_box">@PageLiteralsHelper.GetLiteral("LIT_CSV", PageId, portalConfig)</span>
                        </label>
                        <label class="radio">
                            @Html.RadioButtonFor(r => r.TypeFile, ((short)FileTypeEnum.EXCEL), new { name = "format_export", @id = "rd_excel" })
                            <span class="input"></span>
                            <span class="label_box"> @PageLiteralsHelper.GetLiteral("LIT_EXCEL", PageId, portalConfig)</span>
                        </label>
                    </div>
                    @Html.ValidationMessageFor(model => model.TypeFile, "")
                </div>
            </div>
            <footer class="form_action bt0_m pt0_m">
                <input type="submit" id="btn_Report" class="b_primary big wAuto w100_m" value="@PageLiteralsHelper.GetLiteral("LIT_CREATE", PageId, portalConfig)" />
                <div><a href="#" id="linkCancelar" class="b_transparent">@PageLiteralsHelper.GetLiteral("LIT_CANCELAR", PageId, portalConfig)</a></div>
            </footer>
        </section>
    }
    @if (Model.CompanyFiles.Any())
    {
        Html.RenderPartial("_CompanyReport", Model, new ViewDataDictionary { { "portalConfig", portalConfig } });
    }
    @if (Model.Pager.TotalRows > Model.Pager.PageSizeSelected)
    {
        using (Html.BeginForm("Index", "CompanyReport", FormMethod.Post, new { id = "companyReportList" }))
        {
            @Html.AntiForgeryToken()
            @Html.HiddenFor(m => m.Pager.PageSelected);
        }
    }
    @Html.DisplayFor(i => i.Pager, new { portalConfig = portalConfig, formIdToSubmit = "companyReportList" })

</main>

@Html.DisplayFor(p => p.DeleteReportPopUp, new
{
    IdPopUp = "frmDeleteReportPopup",
    IdBtnOk = "btContinue",
    IdBtnKo = "btAhoraNo",
    IdInput = string.Empty,
})
@Html.DisplayFor(p => p.ErrorDeleteReportPopUp, new
{
    IdPopUp = "frmErrorDeleteReportPopup",
    IdBtnOk = "btAlertError",
    IdInput = string.Empty,
})
