@using Redarbor.Company.WebUI.Helpers;
@using Redarbor.Common.Entities.Enums;
@using System.Web.Optimization;

@model Redarbor.Company.WebUI.Models.SimplifiedRegister.SimplifiedRegisterDataModel

@{
    Layout = null;
    short pageId = (short)PageEnum.CompanyLandingRegister;
    short pageLiteralId = (short)PageEnum.LitCompanyLandingRegister;
    short pageIdHome = (short)PageEnum.HomePublica;
    short pageIdRegister2 = (short)PageEnum.CompanyRegister;
    var portalConfig = PortalConfigHelper.GetPortalConfiguration();
}

<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />

    <link rel="shortcut icon" type="image/x-icon" href="@Url.Content(string.Format("{0}img/favicon.ico{1}", portalConfig.PrefixPathImage, portalConfig.SufixPathImage))" />
    
    <link rel="apple-touch-icon" sizes="120x120" href="@Url.Content(string.Format("{0}img/apple-touch-icon-120x120.png{1}", portalConfig.PrefixPathImage, portalConfig.SufixPathImage))" />
    <link rel="apple-touch-icon" href="@Url.Content(string.Format("{0}img/apple-touch-icon.png{1}", portalConfig.PrefixPathImage, portalConfig.SufixPathImage))" />

    <title>@PageLiteralsHelper.GetLiteral("HEAD_TITLE_BOLSA_TRABAJO_OFERTAS_EMPLEO", pageIdHome, portalConfig)</title>
    <meta name="description" content="@PageLiteralsHelper.GetLiteral("META_DESCRIPTION_PUBLICACION_GRATUITA", pageIdHome, portalConfig)" />
    <meta name="keywords" content="@PageLiteralsHelper.GetLiteral("META_KEYWORDS_HOJAS_DE_VIDA", pageIdHome, portalConfig)" />
    <meta name="title" content="@PageLiteralsHelper.GetLiteral("HEAD_TITLE_BOLSA_TRABAJO_OFERTAS_EMPLEO", pageIdHome, portalConfig)" />

    @Scripts.Render("~/bundles/js/jquery")
    @Scripts.Render("~/bundles/js/required")

    @Styles.Render(string.Format("~/bundles/css/landing_inforegister{0}", portalConfig.staticVirtualBundle))

    @if (portalConfig.AEPortalConfig.HasHubSpot)
    {
        <!-- Start of HubSpot Embed Code -->
        <script type="text/javascript" id="hs-script-loader" async defer src="//js.hs-scripts.com/8889978.js?businessUnitId=660018"></script>
        <!-- End of HubSpot Embed Code -->
    }
</head>

<body>
    <header>
        <!-- Menu tools -->
        <div class="menu_tools log">
            <div class="container">
                <!-- Logotipo -->
                <a href="@portalConfig.url_web" class="logo">
                    <img srcset="@Url.Content(string.Format("{0}img/logoct_neg.svg{1}", portalConfig.PrefixPathImage, portalConfig.SufixPathImage))" alt="@PageLiteralsHelper.GetLiteral("ALT_IMG_LOGO_PORTAL", pageIdRegister2, portalConfig)" class="cep_logo" />
                </a>
                <!-- Fin: Logotipo -->
            </div>
            <div class="overlay hide"></div>
        </div>
        <!-- Fin: Menu tools -->
    </header>

    <main>
        <div class="franja hide_m"></div>
        <div class="container">
            <div class="dFlex dB_m">
                <div class="w50 z10 w100_m fs16">
                    <section class="pb40 ptB h457">
                        <h1 class="fs40 fwB ptB mbB">@Html.Raw(@PageLiteralsHelper.GetLiteral("LIT_INFO_REGISTER_H1_GROWTH", pageLiteralId, portalConfig))</h1>
                        <h2 class="pb40 pb20_m">@PageLiteralsHelper.GetLiteral("LIT_INFO_REGISTER_H2_JOIN", pageLiteralId, portalConfig)</h2>

                        <ul class="grid3 mb40 mt40 full_m mb0_m mt0_m tc_m w70_m mAuto">
                            <li class="posRel">
                                <svg class="posAbs hide_m" style="top:-50px; left:100px;" xmlns="http://www.w3.org/2000/svg" xmlns:v="https://vecta.io/nano" width="127" height="46"><path d="M2.209 44.969c10.241-10.595 20.83-21.55 33.346-29.76C45.3 8.815 60.47 1.492 77.401 2.908c18.372 1.536 35.409 14.047 42.394 31.132.02.05.046.095.073.139l-12.707-6.852a.9.9 0 0 0-1.222.365c-.235.439-.069.987.37 1.224l15.14 8.163a.9.9 0 0 0 .814.023.89.89 0 0 0 .241-.166.9.9 0 0 0 .258-.475l3.267-16.848c.095-.489-.225-.964-.715-1.061a.9.9 0 0 0-1.058.711l-2.759 14.229c-.011-.043-.014-.086-.031-.129-7.237-17.701-24.888-30.663-43.922-32.255C60.102-.351 44.542 7.148 34.56 13.697 21.877 22.017 11.217 33.045.908 43.711c-.346.358-.335.929.024 1.277s.938.341 1.277-.019h0z" fill="#418fde" fill-rule="evenodd"></path></svg>

                                <p class="fwB fs21">@PageLiteralsHelper.GetLiteral("LIT_INFO_REGISTER_PUBLISH", pageLiteralId, portalConfig)</p>
                                <p>@PageLiteralsHelper.GetLiteral("LIT_INFO_REGISTER_CREATE", pageLiteralId, portalConfig)</p>
                            </li>
                            <li class="posRel">
                                <svg class="posAbs hide_m" style="bottom:-40px; right:-20px;transform: rotateX(180deg);" xmlns="http://www.w3.org/2000/svg" xmlns:v="https://vecta.io/nano" width="127" height="46"><path d="M2.209 44.969c10.241-10.595 20.83-21.55 33.346-29.76C45.3 8.815 60.47 1.492 77.401 2.908c18.372 1.536 35.409 14.047 42.394 31.132.02.05.046.095.073.139l-12.707-6.852a.9.9 0 0 0-1.222.365c-.235.439-.069.987.37 1.224l15.14 8.163a.9.9 0 0 0 .814.023.89.89 0 0 0 .241-.166.9.9 0 0 0 .258-.475l3.267-16.848c.095-.489-.225-.964-.715-1.061a.9.9 0 0 0-1.058.711l-2.759 14.229c-.011-.043-.014-.086-.031-.129-7.237-17.701-24.888-30.663-43.922-32.255C60.102-.351 44.542 7.148 34.56 13.697 21.877 22.017 11.217 33.045.908 43.711c-.346.358-.335.929.024 1.277s.938.341 1.277-.019h0z" fill="#418fde" fill-rule="evenodd"></path></svg>

                                <p class="fwB fs21">@PageLiteralsHelper.GetLiteral("LIT_INFO_REGISTER_SELECT", pageLiteralId, portalConfig)</p>
                                <p>@PageLiteralsHelper.GetLiteral("LIT_INFO_REGISTER_ATTRACT", pageLiteralId, portalConfig)</p>
                            </li>
                            <li>
                                <p class="fwB fs21">@PageLiteralsHelper.GetLiteral("LIT_INFO_REGISTER_CONTRACT", pageLiteralId, portalConfig)</p>
                                <p>@PageLiteralsHelper.GetLiteral("LIT_INFO_REGISTER_FIND", pageLiteralId, portalConfig)</p>
                            </li>
                        </ul>
                    </section>
                    <section class="pb40 pt40 mbB bg_brand_light_m">
                        <p class="fs30 mbB">@PageLiteralsHelper.GetLiteral("LIT_INFO_REGISTER_BETTER", pageLiteralId, portalConfig)</p>
                        <div class="box mbB tc">
                            <p class="fwB fs24">+ @StringToolsHelper.ChangeGroupPointForDecimalPoint(Model.LandingInfoRegisterDataModel.ActivesCompanies, portalConfig.PortalId)</p>
                            <p class="mbB">@PageLiteralsHelper.GetLiteral("LIT_INFO_REGISTER_COMPANY_CONTRACT", pageLiteralId, portalConfig)</p>
                            @if (!string.IsNullOrEmpty(portalConfig.GoogleTagManager))
                            {
                                <div id='dfplogosh' class="tc" style="height:46px"></div>
                            }

                        </div>
                        <div class="grid3 full_m tc">
                            <div class="box">
                                <p class="fs24 fwB">+ @StringToolsHelper.ChangeGroupPointForDecimalPoint(Model.LandingInfoRegisterDataModel.ActivesCvs, portalConfig.PortalId) <span class="fs16">@PageLiteralsHelper.GetLiteral("LIT_INFO_REGISTER_MILIONS", pageLiteralId, portalConfig)</span></p>
                                <p>@PageLiteralsHelper.GetLiteral("LIT_INFO_REGISTER_USERS", pageLiteralId, portalConfig)</p>
                            </div>
                            <div class="box">
                                <p class="fs24 fwB">+ @StringToolsHelper.ChangeGroupPointForDecimalPoint(Model.LandingInfoRegisterDataModel.TotalCvs, portalConfig.PortalId) <span class="fs16">@PageLiteralsHelper.GetLiteral("LIT_INFO_REGISTER_MILIONS", pageLiteralId, portalConfig)</span></p>
                                <p>@PageLiteralsHelper.GetLiteral("LIT_INFO_REGISTER_CVS", pageLiteralId, portalConfig)</p>
                            </div>
                            <div class="box">
                                <p class="fs24 fwB">+ @StringToolsHelper.ChangeGroupPointForDecimalPoint(Model.LandingInfoRegisterDataModel.AppliesDaily, portalConfig.PortalId)</p>
                                <p>@PageLiteralsHelper.GetLiteral("LIT_INFO_REGISTER_APPLICATIONS", pageLiteralId, portalConfig)</p>
                            </div>
                        </div>
                    </section>
                    <section class="pt40 pb40 pt20_m pb20_m">
                        <p class="mbB fs30">@PageLiteralsHelper.GetLiteral("LIT_INFO_REGISTER_CONTRACT_PERSON", pageLiteralId, portalConfig)</p>
                        <p class="mb40">@PageLiteralsHelper.GetLiteral("LIT_INFO_REGISTER_SELECT_TALENT", pageLiteralId, portalConfig)</p>
                        <ul>
                            <li class="dFlex vm_fx mbB dB_m tc_m mbB_m">
                                <img src="~/c/v2/img/atrae.svg">
                                <div class="w100 plB">
                                    <p class="fs21 fwB mb10">@PageLiteralsHelper.GetLiteral("LIT_INFO_REGISTER_FIND_FAST", pageLiteralId, portalConfig)</p>
                                    <p>@PageLiteralsHelper.GetLiteral("LIT_INFO_REGISTER_ADD", pageLiteralId, portalConfig)</p>
                                </div>
                            </li>
                            <li class="dFlex vm_fx mbB dB_m tc_m mbB_m">
                                <img src="~/c/v2/img/verifica.svg">
                                <div class="w100 plB">
                                    <p class="fs21 fwB mb10">@PageLiteralsHelper.GetLiteral("LIT_INFO_REGISTER_VERIFY_HABILITES", pageLiteralId, portalConfig)</p>
                                    <p>@PageLiteralsHelper.GetLiteral("LIT_INFO_REGISTER_OPTIMIZE", pageLiteralId, portalConfig)</p>
                                </div>
                            </li>
                            <li class="dFlex vm_fx mbB dB_m tc_m mbB_m">
                                <img src="~/c/v2/img/organiza.svg">
                                <div class="w100 plB">
                                    <p class="fs21 fwB mb10">@PageLiteralsHelper.GetLiteral("LIT_INFO_REGISTER_MANAGE", pageLiteralId, portalConfig)</p>
                                    <p>@PageLiteralsHelper.GetLiteral("LIT_INFO_REGISTER_FILTER_CVS", pageLiteralId, portalConfig)</p>
                                </div>
                            </li>
                        </ul>
                    </section>
                </div>
                <div class="w50 pl40 w100_m pl0_m">
                    <div class="posSticky top20 mtB mAuto z10 box mbB pAll30">
                        <p class="fwB fs21 mb5 tc">@PageLiteralsHelper.GetLiteral("LIT_INFO_REGISTER_PUBLISH_FREE", pageLiteralId, portalConfig)</p>
                        <p class="mbB tc prB plB fs16">@PageLiteralsHelper.GetLiteral("LIT_INFO_REGISTER_COMPLETE_FORM", pageLiteralId, portalConfig)</p>
                        @{Html.RenderPartial("_Register", Model); }
                    </div>
                </div>
            </div>
        </div>
    </main>
    <footer>
        <div class="container">
            <p>
                @PageLiteralsHelper.GetLiteral("MASTER_FOOTER_COPYRIGHT", (short)PageEnum.HomeMasterCompany, portalConfig)
                @DateTime.Now.Year.ToString()
                @PageLiteralsHelper.GetLiteral("MASTER_FOOTER_DGNET_LTD", (short)PageEnum.HomeMasterCompany, portalConfig)
            </p>
            <p class="dB_m">
                <a href="@PageLiteralsHelper.GetLiteral("LIT_URL_SOPORTE_EMPRESAS",  (short)PageEnum.HomeMasterCompany, portalConfig)">@PageLiteralsHelper.GetLiteral("MASTER_FOOTER_AYUDA_PARA_EMPRESAS", (short)PageEnum.HomeMasterCompany, portalConfig)</a>
                <span class="pr5 pl5"> / </span>
                <a href="@Url.Action("Index","Contact")">@PageLiteralsHelper.GetLiteral("FOOT_CONT_EMPRESA", (short)PageEnum.HomeMasterCompany, portalConfig)</a>
            </p>
        </div>
        @if (portalConfig.AEPortalConfig.ShowCookiesConsent)
        {
            Html.RenderPartial("_CookiesConsent", new ViewDataDictionary { { "portalConfig", portalConfig }, { "pageId", (int)pageId } });
        }
    </footer>

    <script type="text/javascript">
        SendGTMDataLayer('@Url.Action("GetGTMDataLayerData", "GoogleTagManager")');
    </script>
    @{ Html.RenderPartial("_GTMandAnalytics"); }

    @Scripts.Render("~/bundles/js/simplifiedRegister")
    <script type="text/javascript">
    const config = { childList: true, subtree: true, characterData: true };
    const targetNodeEmailError = document.getElementById('EmailError');
    const targetNodeNitError = document.getElementById('NitError');

    //callback email error
    const callbackEmailError = function(mutationsList, observer) {
	    for(let mutation of mutationsList) {
		    if ($("#EmailError").text() == '@Html.Raw(PageLiteralsHelper.GetLiteral("LIT_ERR_EMAIL_EXISTS", pageIdRegister2, portalConfig))') {
			    $('#linkLogin1').removeClass('hide');
		    } else {
			    $('#linkLogin1').addClass('hide');
		    }
	    }
    };
    const observerEmailError = new MutationObserver(callbackEmailError);
    observerEmailError.observe(targetNodeEmailError, config);


    //callback nit error
    const callbackNitError = function(mutationsList, observer) {
	    for(let mutation of mutationsList) {
		    if ($("#NitError").text() == '@Html.Raw(PageLiteralsHelper.GetLiteral("CODE_NIT_DUPLICATE_v2", pageIdRegister2, portalConfig))') {
			    $('#linkLogin2').removeClass('hide');
		    }
		    else {
			    $('#linkLogin2').addClass('hide');
		    }
	    }
    };
    const observerNitError = new MutationObserver(callbackNitError);
    observerNitError.observe(targetNodeNitError, config);


    function SetLocationAndSpecificLocationIdByPostalCode() {
        var idPostalCode = $("#RegisterDataModel_IdPostalCode").val();

        if (idPostalCode > 0) {
            idLocalization = $('[data-postalcode-suggester] li[val="' + idPostalCode + '"]').attr('data-extradata-0');
            idCity = $('[data-postalcode-suggester] li[val="' + idPostalCode + '"]').attr('data-extradata-1');
            if (idLocalization === undefined || idLocalization === null) {
                $("#RegisterDataModel_LocalizationIdSelected").val("0");
            } else {
                $("#RegisterDataModel_LocalizationIdSelected").val(+idLocalization);
            }

            if (idCity === undefined || idCity === null) {
                $("#RegisterDataModel_CityIdSelected").val("0");
            } else {
                $("#RegisterDataModel_CityIdSelected").val(+idCity);
            }
        }

        $('#RegisterDataModel_PostalCodeSuggestText').valid();
    }

    var literals = {
        defaultCity: '@PageLiteralsHelper.GetLiteral("DEF_VALUE_DDCIUDAD", pageIdRegister2, portalConfig)',
        defaultDepartment: '@PageLiteralsHelper.GetLiteral("LIT_SELECCIONE_DEPARTAMENTO", pageIdRegister2, portalConfig)',
        literalCharRemaining: '@PageLiteralsHelper.GetLiteral("LIT_CND_CHR_REMAINING", pageIdRegister2, portalConfig)'
    };
    var urlActions = {
        localizationByCountry: '@Url.Action("GetLocalizationByCountryWithoutPortalIdForPostalCode", "Register2")',
        citiesByLocalization: '@Url.Action("GetCitiesByCountryLocation", "Register2")',
        warningInfo: '@Url.Action("WarningInfo", "Register2")'
    };
    var simplifiedRegisterConfigModel = {
        literals: literals,
        urlActions: urlActions
    };

    InitializeSimplifiedRegister(simplifiedRegisterConfigModel);

    var literalsValidationPortal = {
        LIT_VP_NIT_FIELD_NOT_EMPTY: '@PageLiteralsHelper.GetLiteral("LIT_CND_CHR_REMAINING", pageIdRegister2, portalConfig)',
        LIT_VP_REGEX_NIT_ALPHANUM: '@PageLiteralsHelper.GetLiteral("LIT_VP_REGEX_NIT_ALPHANUM", pageIdRegister2, portalConfig)'
    };

    var SuggesterMailDomainDTO = {
            "messageMailDomain": '@PageLiteralsHelper.GetLiteral("LIT_REGISTER_SIMPLIFIED_MAYBE", pageIdRegister2, portalConfig)'
    };

    const validationByPortal = new validationsByPortal(@portalConfig.PortalId,literalsValidationPortal);
    validationByPortal.nit();
    </script>



    @if (portalConfig.AEPortalConfig.IsActiveLogJsWithMuscula)
    {
        <script>
            window.Muscula = {
                logId: 'TMs4Ade0AxYwT',
                errors: [],
            };
            window.Muscula.onerrorOriginalHandler = window.onerror;
            window.onerror = function () {
                window.Muscula.errors.push(arguments);
                return false;
            };

            (function () {
                var m = document.createElement('script');
                m.async = true;
                m.src = 'https://cdn.muscula.com/m2v2.js';
                var s = document.getElementsByTagName('script')[0];
                s.parentNode.insertBefore(m, s);
            })();
        </script>
    }

    <script>$('.i_eye').click(function () { var input = $(this).closest('.field_input_button').find('input'); if (input.attr('type') == 'password') { input.attr('type', 'text'); } else { input.attr('type', 'password'); } });</script>

</body>
</html>

