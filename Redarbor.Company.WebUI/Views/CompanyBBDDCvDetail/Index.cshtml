@model Redarbor.Company.WebUI.Models.Company.Candidate.Cv.CvDetailDataModel
@using Redarbor.Company.WebUI.Helpers.Html
@using Redarbor.Company.WebUI.Helpers
@using Redarbor.Common.Entities.Enums
@using Redarbor.Master.Entities.Enums
@using Redarbor.Company.WebUI.Enums
@using Redarbor.Core.Kpi.Contracts.ServiceLibrary.Enums
@using Redarbor.Extensions.Library.Extensions
@using Redarbor.Core.Contracts.ServiceLibrary.Enums

@{
    Layout = "~/Views/Shared/Layouts/_LayoutMasterNewPem.cshtml";
    int PageId = Html.SetCurrentPage((int)PageEnum.DetailCVCompany);
    var portalConfig = PortalConfigHelper.GetPortalConfiguration();
    var companyCredentials = SecurityHelper.GetCompanyCredentials();
    var classIsPremium = (Model.IsPremium ? "premium" : string.Empty);
    var classPremiumVerifiedFound = Model.ShowPhone1Verification ? "tlf_whatsapp" : "tlf_whatsapp2";
    var setBttJsClass = Model.CvDetailPackAndBasicModel.ItsBBDDcvProdActivated && Model.TotalAllowedCvVisualization == 0 ? "js_showBBDDcvProd" : "js_bttUnblockBBDDCv";
    var buttonConvertToCompleteEncryptedName = Model.BtnConvertToCompleteEncryptedName;
    var classIsPremiumButton = Model.IsPremium ? "bg_premium" : "b_primary";

    @Html.Partial("_PopUpConvertToCompleteComp", new ViewDataDictionary { { "portalConfig", portalConfig }, { "baseProduct", Model.ProdCWConvertToComplete }, { "companyCredentials", companyCredentials }, { "HasNewFreemiumChat", Model.HasNewFreemiumChat } })
    @Html.Partial("_PopUpConvertToCompleteCompFromBasic", new ViewDataDictionary { { "portalConfig", portalConfig }, { "baseProduct", Model.ProdCWConvertToComplete }, { "companyCredentials", companyCredentials }, { "HasNewFreemiumChat", Model.HasNewFreemiumChat } })
    @Html.Partial("PopUps/_UnblockBBDDCvPopUp", new ViewDataDictionary { { "portalConfig", portalConfig } })
    @Html.Partial("PopUps/_UserNotCreditAvailablePopUp", new ViewDataDictionary { { "portalConfig", portalConfig } })

    if (portalConfig.AEPortalConfig.ActivateCompanyCvsConsumible)
    {
        @Html.Partial("PopUps/_BBDDcvProdPopUp", new ViewDataDictionary { { "portalConfig", portalConfig },
                                                                        { "IdProductBBDDcv", Model.CvDetailPackAndBasicModel.IdProductBBDDcv.ToString() },
                                                                        { "PageLiteralsDataModel", Model.PageLiteralsDataModel },
                                                                        { "ProductPriceBBDDcv", Model.CvDetailPackAndBasicModel.ProductPriceBBDDcv }})
    }

    if (Model.HasCustomFolders)
    {
        @Html.Partial("PopUps/_BBDDAddCVCustomFolderPopUp", new ViewDataDictionary { { "portalConfig", portalConfig } })
        @Html.Partial("PopUps/_BBDDEditCVCustomFolderPopUp", new ViewDataDictionary { { "portalConfig", portalConfig } })
        @Html.Partial("PopUps/_BBDDDelCVCustomFolderPopUp", new ViewDataDictionary { { "portalConfig", portalConfig } })

        if (!Model.IsBasicOffer && Model.IsViewed && !Model.IsMatch)
        {
            @Html.Partial("PopUps/_maxCvFolderPopUp", new ViewDataDictionary { { "portalConfig", portalConfig } })
        }
    }

    if (Model.Candidate.HasRatings)
    {
        @Html.Partial("PopUps/_DeleteRatingPopUp", new ViewDataDictionary { { "portalConfig", portalConfig } })
    }
    if (Model.Candidate.HasComments)
    {
        @Html.Partial("PopUps/_DeleteCVCommentPopUp", new ViewDataDictionary { { "portalConfig", portalConfig } })
    }
    if (Model.HasCustomFolders && !Model.HasFoldersDetail &&
        !string.IsNullOrEmpty(Model.FolderSelectedEncrypted) && Model.FolderSelected != 0)
    {
        @Html.Partial("PopUps/_DeleteCVBBDDPopUp", new ViewDataDictionary { { "portalConfig", portalConfig } })
    }
    if (portalConfig.AEPortalConfig.ReportCandidatePhoto)
    {
        @Html.Partial("PopUps/_CandidatePhotoReportedPopUp", new ViewDataDictionary { { "portalConfig", portalConfig } })
    }
}


@section TitleSection{
    <title>@PageLiteralsHelper.GetLiteral("HEAD_CV_DETAIL", PageId, portalConfig)</title>
}


<main>

    @if (Model.ShowVideoPresentation && Model.IsViewed)
    {
        if (portalConfig.PortalId == (short)PortalEnum.ComputrabajoColombia)
        {
            Html.RenderPartial("_VideoPopUpColombia", new ViewDataDictionary { { "idPopUp", "divPopupVideo" }, { "srcVideo", Model.UrlPresentationVideo } });
        }
        else
        {
            Html.RenderPartial("_VideoPopUp", new ViewDataDictionary { { "idPopUp", "divPopupVideo" } });
        }
    }


    @if (Model.HasFoldersDetail)
    {
        <header class="header_block">
            <div class="mrAuto w100_m">
                <h2 class="fs17 fwB fs16_m title_m">@PageLiteralsHelper.GetLiteral("TITLE_H1", PageId, portalConfig)</h2>
                <h1 class="pl15_m pr15_m pt15_m">
                    <span class="fc_brand_aux fs2">
                        @PageLiteralsHelper.GetLiteral("SUBTITLE", PageId, portalConfig)
                        <span class="fwB">@Model.OfferTitle</span>
                    </span>
                    @*<span class="dB_m fs16 fs14_m">(84 candidatos inscritos, este valor está hardcodeado)</span>*@
                </h1>
            </div>

            @if (Model.TotalAllowedCvVisualization != int.MaxValue)
            {
                <div class="bg_brand_light pAll10 mrB">@Html.Raw(string.Format(PageLiteralsHelper.GetLiteral("LIT_X_HDV_DISPONIBLES", (int)PageEnum.PackCVSearch, portalConfig), Model.TotalAllowedCvVisualizationFormatted))</div>
            }
            else
            {
                <div class="bg_brand_light pAll10 mrB">@Html.Raw(PageLiteralsHelper.GetLiteral("LIT_UNLIMITED_ACCESS", (int)PageEnum.PackCVSearch, portalConfig))</div>
            }

            <nav class="hide_m">
                <a id="js-listado" class="mr10" href="@(!string.IsNullOrEmpty(Model.OfferIdEncrypted) ? Url.Action("Index","CompanyMatches", new { oi=Model.OfferIdEncrypted, cf=Model.FolderSelectedEncrypted }) : Url.Action("Index", "CompanyCvs"))">@Html.Raw(PageLiteralsHelper.GetLiteral("LIT_PAG_VOLVER_LISTADO", PageId, portalConfig))</a>

                @if (!string.IsNullOrEmpty(Model.PreviousUrl))
                {
                    <a id="js-before" class="b_aux small" href="@Model.PreviousUrl">
                        <span class="icon i_prev"></span>
                    </a>
                }
                @if (!string.IsNullOrEmpty(Model.NextUrl))
                {
                    <a id="js-next" class="b_aux small" href="@Model.NextUrl">
                        <span class="icon i_next"></span>
                    </a>
                }
            </nav>
        </header>

        <ul class="menu_folders mAllB_m">
            @foreach (var folder in Model.Folders)
            {
                if (folder.Position != (int)CompanyOfferFolderEnum.Contratados)
                {
                    <li id="@folder.PositionEncrypted" class="@(Model.FolderSelected == folder.Position ? "sel" : "") @(folder.Position == (int)CompanyCustomFoldersMatchEnum.Folder1 || folder.Position == (int)CompanyCustomFoldersMatchEnum.Folder2 ? "custom_folder" : "")">

                        @if (folder.Position == (int)CompanyOfferFolderEnum.BBDD)
                        {
                            <a id="@folder.AnchorName" href="@Url.Action("Index", "CompanyMatches", new { oi = Model.OfferIdEncrypted, cf = folder.PositionEncrypted })">
                                @folder.NameFolder
                            </a>
                        }
                        else if (folder.Count > 0)
                        {
                            <a id="@folder.AnchorName" href="@Url.Action("Index", "CompanyMatches", new { oi = Model.OfferIdEncrypted, cf = folder.PositionEncrypted })">
                                @folder.NameFolder (@StringToolsHelper.ChangeGroupPointForDecimalPoint(folder.Count.ToString("N0"), portalConfig.PortalId))
                            </a>
                        }
                        else
                        {
                            <span class="@(folder.Position == (int)CompanyCustomFoldersMatchEnum.Folder1 || folder.Position == (int)CompanyCustomFoldersMatchEnum.Folder2 ? "w_auto nav-tab-lbl fl" : "nav-tab-lbl")">@folder.NameFolder (0)</span>
                        }

                        @if (Model.HasCustomFolders && (folder.Position == (int)CompanyCustomFoldersMatchEnum.Folder1 || folder.Position == (int)CompanyCustomFoldersMatchEnum.Folder2))
                        {
                            <div id="<EMAIL>" class="drop">
                            </div>
                        }
                    </li>
                }
            }
        </ul>
    }
    else
    {
        <header class="header_block full_m">

            @if (Model.CvDetailPackAndBasicModel.ItsBBDDcvProdActivated)
            {
                if (Model.TotalAllowedCvVisualization > 0)
                {
                    <div class="bg_brand_light pAll10 mlAuto">
                        <strong>@PageLiteralsHelper.GetLiteral("LIT_BBDDCV_SEARCH", PageId, portalConfig)</strong> @PageLiteralsHelper.GetLiteral("LIT_AVAILABLE_ACCES", PageId, portalConfig) <strong>@(Model.TotalAllowedCvVisualizationFormatted)</strong>/@(Model.InitialBBDDCvVisualization) @PageLiteralsHelper.GetLiteral("LIT_HDV", PageId, portalConfig)
                    </div>
                }
                else
                {
                    <div class="mrAuto">
                        <div class="box_border bAll1_blue radius_big">
                            <div class="dFlex vm_fx pAll5">
                                <div class="prB">
                                    <img src="@Url.Content(string.Format("{0}img/buscador-candidatos.svg", portalConfig.PrefixPathImage, portalConfig.countrycode.ToLower(), portalConfig.SufixPathImage))">
                                </div>
                                <div>
                                    @Html.Raw(PageLiteralsHelper.GetLiteral("LIT_BBDDCV_BANNER", PageId, portalConfig))
                                    <a href="@Url.Action("Index", "MultiPurchaseCart" , new { prod=EncryptationHelper.Encrypt(Model.CvDetailPackAndBasicModel.IdProductBBDDcv.ToString()),
                                            p = EncryptationHelper.Encrypt(((short)PageEnum.PackCart).ToString()),
                                            btn = EncryptationHelper.Encrypt(((short)TpvButtonOriginEnum.CompanyCVsBBDDcvDetail).ToString())
                                        })">
                                        @PageLiteralsHelper.GetLiteral("LIT_PURCHASE_BBDDCV_PROD", PageId, portalConfig)
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                }
            }
            else
            {
                <div class="bg_brand_light pAll10 mlAuto">
                    @if (Model.TotalAllowedCvVisualization != int.MaxValue)
                    {
                        <p>@string.Format(PageLiteralsHelper.GetLiteral("LIT_X_HDV_DISPONIBLES", PageId, portalConfig), Model.TotalAllowedCvVisualizationFormatted)</p>
                    }
                    else
                    {
                        <p>@Html.Raw(PageLiteralsHelper.GetLiteral("LIT_UNLIMITED_ACCESS", PageId, portalConfig))</p>
                    }
                </div>
            }

            <nav class=" ml20 hide_m">
                @if (!string.IsNullOrEmpty(Model.OfferIdEncrypted))
                {
                    <a id="js-listado" class="mr10" href="@(Url.Action("Index","CompanyMatches", new { oi= Model.OfferIdEncrypted, cf =  Model.FolderSelectedEncrypted }))">@Html.Raw(PageLiteralsHelper.GetLiteral("LIT_PAG_VOLVER_LISTADO", PageId, portalConfig))</a>
                }
                else
                {
                    <a id="js-listado" class="mr10" href="#" onclick="goBack();">@Html.Raw(PageLiteralsHelper.GetLiteral("LIT_PAG_VOLVER_LISTADO", PageId, portalConfig))</a>
                }

                @if (!string.IsNullOrEmpty(Model.PreviousUrl))
                {
                    <a id="js-before" class="b_aux small" href="@Model.PreviousUrl">
                        <span class="icon i_prev"></span>
                    </a>
                }
                @if (!string.IsNullOrEmpty(Model.NextUrl))
                {
                    <a id="js-next" class="b_aux small" href="@Model.NextUrl">
                        <span class="icon i_next"></span>
                    </a>
                }
            </nav>
        </header>
    }

    @if (Model.HasCustomFolders && !Model.HasFoldersDetail)
    {
        <ul class="menu_folders mAllB_m">
            @if (!string.IsNullOrEmpty(Model.FolderSelectedEncrypted) && Model.FolderSelected != 0)
            {
                <li><a href="@Url.Action("Index","CompanyCvs")">@PageLiteralsHelper.GetLiteral("LIT_CV_SEARCH", PageId, portalConfig)</a></li>
            }
            else
            {
                <li class="sel">
                    <a>@PageLiteralsHelper.GetLiteral("LIT_CV_SEARCH", PageId, portalConfig)</a>
                </li>
            }

            @if (Model.CustomFoldersBBDD.Any())
            {
                foreach (var item in Model.CustomFoldersBBDD)
                {
                    <li id="folder" class="@(Model.FolderSelectedEncrypted == item.IdEncrypted ? "sel" : "")">
                        <a id="linkFolder" href="@Url.Action("Index","CompanyCvs", new { idFolder = item.IdEncrypted})" title="@item.NameFolder">
                            <span id="@item.IdEncrypted"> @item.NameFolder</span>
                        </a>
                        @if (Model.FolderSelectedEncrypted != item.IdEncrypted && !string.IsNullOrEmpty(item.IdEncrypted))
                        {
                            <span>
                                <span class="icon i_drop_down"></span>
                            </span>
                            <div class="drop">
                                <div>
                                    <a class="edit_custom_folder" data-id-folder="@item.IdEncrypted">@PageLiteralsHelper.GetLiteral("LIT_EDIT_FOLDER", PageId, portalConfig)</a>
                                    <a data-id-folder="@item.IdEncrypted" class="delete_folder_action">@PageLiteralsHelper.GetLiteral("LIT_DELETE_FOLDER", PageId, portalConfig)</a>
                                </div>
                            </div>
                        }
                    </li>
                }
            }

            @if (Model.CanCreateNewFolder)
            {
                <li><a id="addfolder" href="#">+</a></li>
            }
        </ul>
        <div class="box_border dFlex fx_wrap mb20 pb10 pl15 pr0 pt10 bAll0_m bb1_m bt1_m mb0_m">
            <div class="field_select hor mb0 mrAuto small w30 w100_m">
                <label for="FolderSelected">@PageLiteralsHelper.GetLiteral("LIT_MOVERA", PageId, portalConfig)</label>
                <div>
                    <div>
                        @Html.DropDownListFor(d => d.FolderSelected, Model.CustomFoldersDropDownList, Model.IsViewed ? null : new { disabled = true, id = "FolderSelected" })
                    </div>
                </div>
            </div>
            @if (!string.IsNullOrEmpty(Model.FolderSelectedEncrypted) && Model.FolderSelected != 0)
            {
                <a href="#" id="deleteBBDDCVFromFolder" class="bl1 dFlex icon_tooltip small vm_fx pl15 pr15 b_aux_m no_tooltip_m mt15_m w100_m pAllB_m">
                    <span class="icon i_remove"></span>
                    <span class="ml5_m">@PageLiteralsHelper.GetLiteral("LIT_ELIMINAR", PageId, portalConfig)</span>
                </a>
            }
        </div>
    }


    <article class="box clearfix pAll0 @(Model.IsPremium? "bAllPremium" : "") bb0_m">
        @if (Model.IsPremium)
        {
            <div class="tag_premium">
                <p>Premium</p>
            </div>
        }

        <header class="header_block plB pr15 pt15 full_m mb0_m title_m">
            <h1 class="fwB mrAuto w100_m">@PageLiteralsHelper.GetLiteral("LIT_TITULO_CANDIDATE", PageId, portalConfig) @Model.Candidate.Candidate.Name @Model.Candidate.Candidate.Surname</h1>

            @if (Model.HasDocument && Model.IsViewed)
            {
                <a href="@Url.Action("DownloadCvAmazon", "CompanyCvDownloader", new { idcv = Model.IdCvEncrypted })" class="icon_tooltip pb10 pl15 pr15 pt10 bl0_m mt5_m pAll10_m  hide js_download_file" target="_blank">
                    <span class="@string.Format("icon {0}", Model.TypeDocument)"></span>
                    <span>@PageLiteralsHelper.GetLiteral("LIT_MOSTRAR_HDV_ADJUNTA", PageId, portalConfig)</span>
                </a>
            }

            @if (!Model.IsViewed)
            {
                <div class="fc_aux pb10 pr15 pt10 t_no_wrap fs13_m fwN_m pAll0_m">
                    @PageLiteralsHelper.GetLiteral("LIT_ACTUALIZADO", PageId, portalConfig)
                    @StringToolsHelper.NormalizeGetFormatDate(@Model.Candidate.Cv.UpdatedOn.GetFormattedDate(string.Empty, new System.Globalization.CultureInfo(portalConfig.cultura)),
                    PageLiteralsHelper.GetLiteral("LIT_AYER", PageId, portalConfig),
                    PageLiteralsHelper.GetLiteral("LIT_HOY", PageId, portalConfig),
                    PageLiteralsHelper.GetLiteral("LIT_DIAS", PageId, portalConfig),
                    string.Empty)
                    <span class="icon i_clock ml5 vt hide_m"></span>
                </div>

                if (Model.HasDocument)
                {
                    <div class="icon_tooltip pb10 pl15 pr15 pt5 bl0_m mt5_m pAll10_m">
                        <span class="icon @string.Format("icon {0}", Model.TypeDocument)"></span>
                        <span>@PageLiteralsHelper.GetLiteral("LIT_HAS_DV", PageId, portalConfig)</span>
                    </div>
                }
            }
        </header>

        <div class="fl plB w30 pAllB_m pb0_m w100_m">

            <div class="photo_cand pl0">
                <div class="mAuto">
                    @if (!string.IsNullOrEmpty(Model.Candidate.Candidate.Photo))
                    {
                        if (Model.Candidate.Candidate.Photo.StartsWith("http"))
                        {
                            <img src="@Model.Candidate.Candidate.Photo" />
                        }
                        else
                        {
                            <img src="@<EMAIL>" />
                        }
                    }
                    <span class="icon i_man_big"></span>
                </div>
            </div>

            @if (Model.ShowVideoPresentation && Model.IsViewed)
            {

                if (portalConfig.PortalId == (short)PortalEnum.ComputrabajoColombia)
                {
                    <div class="sub_box w65 pAll10 w100_m mAuto mtB" onclick="showPopupVideoPresentacion()">
                        <span class="tag new">Nuevo</span>
                        <p class="fwB">
                            <span class="icon i_video vm mr10"></span>
                            @PageLiteralsHelper.GetLiteral("VIDEO_INTERVIEW", PageId, portalConfig)
                        </p>
                    </div>
                }
                else
                {
                    <div class="sub_box w65 pAll10 w100_m mAuto mtB js_show_video cp" data-candidate-candidateId="@Model.Candidate.Candidate.IdCandidate">
                        <span class="tag new">Nuevo</span>
                        <p class="fwB">
                            <span class="icon i_video vm mr10"></span>
                            @PageLiteralsHelper.GetLiteral("VIDEO_INTERVIEW", PageId, portalConfig)
                        </p>
                    </div>
                }
            }


            @if (portalConfig.AEPortalConfig.ReportCandidatePhoto &&
                !string.IsNullOrEmpty(Model.Candidate.Candidate.Photo))
            {
                <div class="info_tooltip no_icon w100 mt10">
                    <p class="fc_aux tc">
                        <span class="icon i_report"></span>
                        @PageLiteralsHelper.GetLiteral("LIT_REPORT", PageId, portalConfig)
                    </p>
                    <div class="opt_bubble" style="top:25px;">
                        <a class="dB fs13" href="#" onclick="ReportPhoto('1');">@PageLiteralsHelper.GetLiteral("LIT_REPORT_1", PageId, portalConfig)</a>
                        <a class="dB fs13" href="#" onclick="ReportPhoto('2');">@PageLiteralsHelper.GetLiteral("LIT_REPORT_2", PageId, portalConfig)</a>
                    </div>
                </div>
            }

            @if (!Model.IsViewed)
            {
                <a class="b_primary mtB w100 @setBttJsClass">@PageLiteralsHelper.GetLiteral("BTN_INFO_CONTACTO", PageId, portalConfig)</a>
            }

            <ul class="mtB table small">
                @if (!string.IsNullOrEmpty(Model.Candidate.User.Email))
                {
                    <li>
                        <div class="vt"><span class="icon i_email mr10" title="@PageLiteralsHelper.GetLiteral("LIT_EMAIL", PageId, portalConfig)"></span></div>
                        <span class="w100">@Model.Candidate.User.Email</span>
                    </li>
                }
                @if (Model.Candidate.CanShowNit)
                {
                    if (!Model.IsViewed)
                    {
                        <li>
                            <div class="vt"><span class="icon i_card mr10" title="@PageLiteralsHelper.GetLiteral("LIT_IDENTIFICACION", PageId, portalConfig)"></span></div>
                            <span class="w100">
                                @Model.Candidate.IdentificationType
                                @(new string('*', Model.Candidate.Candidate.Nit.Length))
                            </span>
                        </li>
                    }
                    else
                    {
                        <li>
                            <div class="vt"><span class="icon i_card mr10" title="@PageLiteralsHelper.GetLiteral("LIT_IDENTIFICACION", PageId, portalConfig)"></span></div>
                            <span class="w100">@Model.Candidate.Candidate.Nit</span>
                        </li>
                    }
                }
                @if (!string.IsNullOrEmpty(Model.Candidate.Candidate.Phone1ToWhatsApp))
                {
                    <li class="sub_box">
                        <div class="vt pl10">
                            <span class="icon i_mobile mr10"></span>
                        </div>
                        @if (Model.ShowPhone1Verification)
                        {
                            <span class="w100 pl10">
                                @Model.Candidate.Candidate.Phone1
                                <span class="fc_ok ml10 mr10">@PageLiteralsHelper.GetLiteral("VERIFIED", PageId, portalConfig)</span>
                                <span class="icon i_verificada"></span>
                            </span>
                        }
                        else
                        {
                            <span class="w100 pl10">@Model.Candidate.Candidate.Phone1</span>
                        }
                    </li>

                    if (Model.IsViewed && Model.HasWhatsapp && Model.WhatsappPhone != string.Empty)
                    {
                        <li class="sub_box">
                            <div class="vt pl10">
                                <span class="icon i_whatsapp mr10"></span>
                            </div>
                            <span class="w100 pl10">
                                <a href="https://api.whatsapp.com/send?phone=@Model.WhatsappPhone&text=@Url.Encode(Model.WhatsappMessage)" target='_blank'>
                                    @PageLiteralsHelper.GetLiteral("SEND_WHATSAPP", PageId, portalConfig)
                                </a>
                            </span>
                        </li>
                    }
                }
                @if (!string.IsNullOrEmpty(Model.Candidate.Candidate.Phone2ToWhatsApp) && Model.IsViewed)
                {
                    <li class="sub_box">
                        <div class="vt pl10">
                            <span class="icon i_mobile mr10"></span>
                        </div>
                        <span class="w100 pl10">@Model.Candidate.Candidate.Phone2</span>
                    </li>
                    if (Model.HasWhatsapp && Model.WhatsappPhone2 != string.Empty)
                    {
                        <li class="sub_box">
                            <div class="vt pl10">
                                <span class="icon i_whatsapp mr10"></span>
                            </div>
                            <span class="w100 pl10">
                                <a href="https://api.whatsapp.com/send?phone=@Model.WhatsappPhone2&text=@Model.WhatsappMessage2" target='_blank'>
                                    @PageLiteralsHelper.GetLiteral("SEND_WHATSAPP", PageId, portalConfig)
                                </a>
                            </span>
                        </li>
                    }
                }
                @if (Model.IsViewed && portalConfig.AEPortalConfig.ShowSkypeContact && Model.HasSkype && !string.IsNullOrEmpty(Model.Candidate.Candidate.SkypeName))
                {
                    <li>
                        <div class="vt">
                            <span title="" class="icon i_skype mr10"></span>
                        </div>
                        <span class="w100">
                            <a class="js_show_skype">@Model.Candidate.Candidate.SkypeName</a>
                        </span>
                    </li>
                    <li class="hide js_box_skype">
                        <div></div>
                        <div>
                            <div class="cols mb10">
                                <span class="icon i_skype_msg"></span>
                                <a class="pl15" href="skype:@Model.Candidate.Candidate.SkypeName?chat">Enviar mensaje</a>
                            </div>
                            <div class="cols">
                                <span class="icon i_skype_call"></span>
                                <a class="pl15" href="skype:@Model.Candidate.Candidate.SkypeName?call">Llamar</a>
                            </div>
                        </div>
                    </li>

                }

                <li>
                    <div class="vt"><span class="icon i_flag mr10" title="@PageLiteralsHelper.GetLiteral("LIT_PROVINCE_CITY", PageId, portalConfig)"></span></div>
                    <span class="w100">
                        @string.Format("{0} {1} {2}", @Model.Candidate.ProvinceString != string.Empty ? @Model.Candidate.ProvinceString : string.Empty
                                    , @Model.Candidate.ProvinceString != string.Empty ? "/" : string.Empty
                                    , @Model.Candidate.CityString != string.Empty ? @Model.Candidate.CityString : string.Empty)
                    </span>
                </li>
                @if (Model.Candidate.CanShowRace)
                {
                    <li>
                        <div class="vt"><span class="icon i_card mr10"></span></div>
                        <span class="w100">@PageLiteralsHelper.GetLiteral("LIT_RACE", PageId, portalConfig) <span> @Model.Candidate.CandidateRaceString </span></span>
                    </li>
                }
                @if (!Model.IsViewed)
                {
                    <li>
                        <div class="vt"><span class="icon i_card mr10" title="@PageLiteralsHelper.GetLiteral("LIT_AGE", PageId, portalConfig)"></span></div>
                        <span class="w100">**</span>
                    </li>
                }
                else
                {
                    <li>
                        <div class="vt"><span class="icon i_card mr10" title="@PageLiteralsHelper.GetLiteral("LIT_AGE", PageId, portalConfig)"></span></div>
                        <span class="w100">@Model.Candidate.Candidate.Age @PageLiteralsHelper.GetLiteral("LIT_YEARS", PageId, portalConfig)</span>
                    </li>
                }
                @if (!string.IsNullOrEmpty(Model.Candidate.CivilianStatus))
                {
                    <li>
                        <div class="vt"><span class="icon i_partner mr10" title="@PageLiteralsHelper.GetLiteral("LIT_CIVIL_STATUS", PageId, portalConfig)"></span></div>

                        @if (Model.IsViewed)
                        {
                            <span class="w100">@Model.Candidate.CivilianStatus</span>
                        }
                        else
                        {
                            <span class="w100">**</span>
                        }
                    </li>
                }
                @if (Model.Candidate.Candidate.Disability > 0)
                {
                    <li>
                        <div class="vt"><span class="icon i_discapacity opacity_med mr10" title="@PageLiteralsHelper.GetLiteral("LIT_DISABILITY", PageId, portalConfig)"></span></div>
                        <span class="w100">@string.Format("{0} %", Model.Candidate.Candidate.Disability)</span>
                    </li>
                }

                <li>
                    <div class="vt"><span class="icon i_yes mr10" title="@PageLiteralsHelper.GetLiteral("LIT_YES", PageId, portalConfig)"></span></div>
                    <span class="w100">@Model.Candidate.ActualWork</span>
                </li>

                @if (Model.Candidate.HasDriverLicense)
                {
                    <li>
                        <div class="vt"><span class="icon mr10 @(Model.Candidate.DriveLicense ? "i_yes" : "i_no")" title="@PageLiteralsHelper.GetLiteral(@String.Format("LIT_{0}", (Model.Candidate.DriveLicense ? "YES" : "NO")), PageId, portalConfig)"></span></div>
                        <span class="w100">@PageLiteralsHelper.GetLiteral("LIT_DRIVE_LICENSE", PageId, portalConfig)</span>
                    </li>
                }

                @if (Model.Candidate.HasVehicles)
                {
                    <li>
                        <div class="vt"><span class="icon mr10 @(Model.Candidate.Vehicles ? "i_yes" : "i_no")" title="@PageLiteralsHelper.GetLiteral(@String.Format("LIT_{0}", (Model.Candidate.Vehicles ? "YES" : "NO")), PageId, portalConfig)"></span></div>
                        <span class="w100">@PageLiteralsHelper.GetLiteral("LIT_TRANSPORTATION", PageId, portalConfig)</span>
                    </li>
                }

                @if (Model.Candidate.HasTravel)
                {
                    <li>
                        <div class="vt"><span class="icon mr10 @(Model.Candidate.Travel ? "i_yes" : "i_no")" title="@PageLiteralsHelper.GetLiteral(@String.Format("LIT_{0}", (Model.Candidate.Travel ? "YES" : "NO")), PageId, portalConfig)"></span></div>
                        <span class="w100">@PageLiteralsHelper.GetLiteral("LIT_AVAILABILITY_TRAVEL", PageId, portalConfig)</span>
                    </li>
                }

                @if (Model.Candidate.HasChangeResidence)
                {
                    <li>
                        <div class="vt"><span class="icon mr10 @(Model.Candidate.ChangeResidence ? "i_yes" : "i_no")" title="@PageLiteralsHelper.GetLiteral(@String.Format("LIT_{0}", (Model.Candidate.ChangeResidence ? "YES" : "NO")), PageId, portalConfig)"></span></div>
                        <span class="w100">@PageLiteralsHelper.GetLiteral("LIT_AVAILABILITY_RESIDENCE", PageId, portalConfig)</span>
                    </li>
                }

                <li>
                    <div class="vt"><span class="icon i_money mr10" title="@PageLiteralsHelper.GetLiteral("LIT_TITLE_SALARY", PageId, portalConfig)"></span></div>
                    <span class="w100">@PageLiteralsHelper.GetLiteral("LIT_TITLE_SALARY", PageId, portalConfig) @Model.Candidate.Salary</span>
                </li>
            </ul>

            <ul class="bt1 mtB pt15 bt0_m pt0_m pbB">
                <li class="mt15 mt10_m">
                    <p>@PageLiteralsHelper.GetLiteral("LIT_ULTIMO_LOGIN", PageId, portalConfig)</p>
                    <p class="fc_aux fs12">@Model.Candidate.UserLastLoginCultureInfo</p>
                </li>
                <li class="mt15 mt10_m">
                    <p>@PageLiteralsHelper.GetLiteral("LIT_ULTIMA_MODIF", PageId, portalConfig)</p>
                    <p class="fc_aux fs12">@Model.Candidate.CandidateUpdatedOnCultureInfo</p>
                </li>
                <li class="mt15 mt10_m">
                    <p>@PageLiteralsHelper.GetLiteral("LIT_REGISTRADO", PageId, portalConfig)</p>
                    <p class="fc_aux fs12">@Model.Candidate.UserCreatedOnCultureInfo</p>
                </li>
            </ul>
        </div>
        <div class="fr pAllB pt0 w70_mB">
            <div class="bb1 pbB">
                <h2 class="fs20 mb20 fs16_m mb0_m mB_neg_m title_m">@Model.Candidate.Cv.TittleAdd</h2>
                <p>@Model.Candidate.Cv.ShortDescription</p>
            </div>
            @if (Model.HasTalentView3dFeature && Model.Candidate.HasTalentView3D)
            {
                <div class="bb1 ptB pbB">
                    <div class="dFlex vm_fx">
                        <h2 class="fs20 fs16_m mb0_m mB_neg_m title_m tc_m dIB_m w100_m">@PageLiteralsHelper.GetLiteral("LIT_TALENT_VIEW_RESULTS", PageId, portalConfig)</h2>
                        @if (Model.IsViewed)
                        {
                            <a class="fc_link cp mlAuto" onclick="GeneratePDF()">@PageLiteralsHelper.GetLiteral("LIT_DOWNLOAD_TALENT", PageId, portalConfig)</a>
                        }
                    </div>
                    <div class="grid3 big full_m">
                        @if (Model.Candidate.Cv.ValuesList != null && Model.Candidate.Cv.ValuesList.Any())
                        {
                            <div class="box_border hover dFlex row_fx mb0 cp tc bClick pAllB">
                                <p class="fs18">@PageLiteralsHelper.GetLiteral("LIT_TALENT_VIEW_VALUES", PageId, portalConfig)</p>
                                @foreach (var value in Model.Candidate.Cv.ValuesList.OrderByDescending(c => c.Result).OrderByDescending(x => x.Result).Take(3).ToList())
                                {
                                    // Mapear el valor de Result al rango de 0 a 5

                                    var color = value.ShortScore == 1 ? "border-color: #7D66A8!important; "
                                  : value.ShortScore == 2 ? "border-color: #7D66A820!important;"
                                  : value.ShortScore == 3 ? "border-color: #005DA920!important;"
                                  : value.ShortScore == 4 ? "border-color: #005DA950!important;"
                                  : "border-color: #005DA9!important;";

                                    <div class="mt15 mb15">
                                        <div class="doughnut half default">
                                            <div style="transform: rotate(@(value.ShortScoreRotate)deg);@color"></div>
                                            <span class="result">@value.ShortScore</span>
                                        </div>
                                        <span class="legend"><span>0</span><span>5</span></span>
                                        <p class="fs16 fwB">@value.Title</p>
                                    </div>
                                }
                                <a class="ptB mtAuto" href="#valores">@PageLiteralsHelper.GetLiteral("LIT_TALENT_VIEW_DISCOVER_VALUES", PageId, portalConfig)</a>
                            </div>
                        }
                        @if (Model.Candidate.Cv.PersonalityList != null && Model.Candidate.Cv.PersonalityList.Any())
                        {
                            var description = @Model.Candidate.Cv.PersonalityList.FirstOrDefault().Description;
                            var finalDescription = (@description != null && @description.Length > 300) ? @description.Substring(0, 300) + "..." : @description;

                            <div class="box_border hover dFlex row_fx mb0 cp tc bClick pAllB">
                                <p class="fs18">@PageLiteralsHelper.GetLiteral("LIT_TALENT_VIEW_PERSONALITY", PageId, portalConfig)</p>
                                <p class="mtB tl">@finalDescription</p>
                                <a class="ptB mtAuto" href="#personalidad">@PageLiteralsHelper.GetLiteral("LIT_TALENT_VIEW_DESCRIPTION", PageId, portalConfig)</a>
                            </div>
                        }
                        @if (Model.Candidate.Cv.PersonalityList != null && Model.Candidate.Cv.CompetenceList.Any())
                        {
                            <div class="box_border hover dFlex row_fx mb0 cp tc bClick pAllB">
                                <p class="fs18">@PageLiteralsHelper.GetLiteral("LIT_TALENT_VIEW_COMPETENCES", PageId, portalConfig)</p>
                                @foreach (var competence in Model.Candidate.Cv.CompetenceList.OrderByDescending(c => c.Result).OrderByDescending(x => x.Result).Take(3).ToList())
                                {
                                    <div class="mtB mb5">
                                        <div class="doughnut thin mAuto">

                                            <div class="portion" style="background-image: conic-gradient(#005DA9 0 calc((@competence.Result / 100) * 360deg), #eaf3fb 0);"></div>
                                            <p class="center fs20">@competence.Result</p>
                                        </div>
                                    </div>
                                    <p class="fs16 fwB">@competence.Title</p>
                                }
                                <a class="mtAuto ptB" href="#competencias">@PageLiteralsHelper.GetLiteral("LIT_TALENT_VIEW_DISCOVER_COMPETENCES", PageId, portalConfig)</a>
                            </div>
                        }
                    </div>
                </div>
            }
            else if (!Model.IsBasicOffer && Model.HasCompanyTestCompetencesFeature && Model.Candidate.HasTestCompetences)

            {
                <div class="bb1 ptB pbB">
                    <h2 class="fs20 mb5 fs16_m mB_neg_m title_m">@PageLiteralsHelper.GetLiteral("LIT_TEST_COMPETENCIAS", PageId, portalConfig)</h2>
                    <p class="fc_premium fs13_m mb20">@PageLiteralsHelper.GetLiteral("LIT_PUNTOS_FUERTES", PageId, portalConfig)</p>

                    <ul id="list_results" class="cols tLayoutFix mt15 tc full_m">
                        @{
                            int index = 1;
                            foreach (var test in Model.Candidate.Cv.CompetenceList.OrderByDescending(c => c.Result).Take(3).ToList())
                            {
                                <li class="pl10 pr10 vt mb15_m">

                                    <div class="posRel">
                                        <div style="height:80px">
                                            <canvas id="value_@index" width="300" height="100" style="display: block; width: 240px; height: 80px;"></canvas>
                                        </div>
                                        <div class="info_donut @(test.Result > 49? "fc_ok" : "fc_brand") fs21 fwB">@test.Result</div>
                                    </div>

                                    <input type="hidden" value="@test.Result" id="input_value_@index" />
                                    <p class="fwB mb5 mt10">@test.Title</p>
                                    <p class="fc_aux fs13">@test.Description</p>
                                </li>
                                index++;
                            }
                        }
                    </ul>
                </div>
            }

            @if (Model.ShowCoverLetter)
            {
                <div class="bb1 ptB pbB">
                    <h2 class="fs20 mb20 fs16_m mb0_m mB_neg_m title_m">@PageLiteralsHelper.GetLiteral("COVER_LETTER", PageId, portalConfig)</h2>
                    <div>
                        <p id="descriptionCoverLetter">@Model.DescriptionCoverLetter</p>
                        @if (Model.DescriptionCoverLetterIsOverLimit)
                        {
                            <button id="btnShowMoreCover" class="tag big mb10 @classIsPremiumButton">@PageLiteralsHelper.GetLiteral("LIT_VER_MAS", PageId, portalConfig)</button>
                        }
                    </div>
                </div>
            }

            @if (Model.Candidate.Cv.ExperiencesByCv.Any())
            {
                <div class="bb1 ptB pbB">
                    <h2 class="fs20 mb20 fs16_m mb0_m mB_neg_m title_m">@PageLiteralsHelper.GetLiteral("H2_EXP_PROFESIONAL", PageId, portalConfig)</h2>
                    <ul class="list_timeline small">

                        @foreach (var experience in Model.Candidate.Cv.ExperiencesByCv)
                        {
                            <li>
                                <div class="w20 left">
                                    @String.Format("{0} {1} {2} - ", experience.MesInicioString, PageLiteralsHelper.GetLiteral("LIT_DE", PageId, portalConfig), experience.AñoInicioStrint)
                                    @if (experience.IsActual)
                                    {
                                        @PageLiteralsHelper.GetLiteral("LIT_ACTUALMENTE", PageId, portalConfig)
                                    }
                                    else
                                    {
                                        @String.Format("{0} {1} {2}", experience.MesFinString, PageLiteralsHelper.GetLiteral("LIT_DE", PageId, portalConfig), experience.AñoFinString)
                                    }
                                    <p>
                                        @String.Format("{0}", "(")
                                        @if (experience.DiferenciaDeFechas.Years > 0)
                                        {
                                            @(experience.DiferenciaDeFechas.Years > 0 ? experience.DiferenciaDeFechas.Years == 1
                                                                        ? @String.Format("{0} {1}", experience.DiferenciaDeFechas.Years, PageLiteralsHelper.GetLiteral("LIT_EXP_AÑO", PageId, portalConfig))
                                                                        : @String.Format("{0} {1}", experience.DiferenciaDeFechas.Years, PageLiteralsHelper.GetLiteral("LIT_EXP_AÑOS", PageId, portalConfig))
                                                                        : string.Empty)
                                        }
                                        @if (experience.DiferenciaDeFechas.Months > 0)
                                        {
                                            if (experience.DiferenciaDeFechas.Years > 0)
                                            {
                                                @String.Format("{0} ", PageLiteralsHelper.GetLiteral("LIT_EXP_Y", PageId, portalConfig))
                                            }

                                            @String.Format("{0} ", experience.DiferenciaDeFechas.Months);

                                            @(experience.DiferenciaDeFechas.Months == 1 ? String.Format("{0}", PageLiteralsHelper.GetLiteral("LIT_EXP_MES", PageId, portalConfig)) : String.Format("{0}", PageLiteralsHelper.GetLiteral("LIT_EXP_MESES", PageId, portalConfig)))
                                        }
                                        @String.Format("{0}", ")")
                                    </p>
                                </div>

                                <div class="w80">
                                    <p>
                                        <span class="fwB">@experience.Cargo</span>
                                    </p>
                                    <div class="fc_aux list_dot mb5">
                                        @if (!Model.IsViewed)
                                        {
                                            <span>@PageLiteralsHelper.GetLiteral("LIT_COMPANY_NAME", PageId, portalConfig)</span>
                                            <a class="isBlocked js_bttUnblockBBDDCv" data-idoffer="@Model.OfferIdEncrypted" data-featureprod="@Model.ProdCWConvertToComplete">@PageLiteralsHelper.GetLiteral("LIT_FOR_COMPLETE_OFFERS_BBDD", PageId, portalConfig)</a>
                                        }
                                        else
                                        {
                                            <span>@String.Format("{0}", experience.CompanyName)</span>
                                        }

                                        @if (!string.IsNullOrEmpty(experience.Area))
                                        {
                                            <span>@String.Format("{0}", experience.Area)</span>
                                        }
                                        @if (!string.IsNullOrEmpty(experience.Category))
                                        {
                                            <span>@String.Format("{0}", experience.Category)</span>
                                        }
                                    </div>

                                    @if (experience.Tareas.Any())
                                    {
                                        <p>@String.Format("{0} {1}", PageLiteralsHelper.GetLiteral("LIT_DESCRIPCION_FUNCION", PageId, portalConfig), experience.Tareas) </p>
                                    }
                                </div>
                            </li>
                        }
                    </ul>
                </div>
            }

            @if (Model.Candidate.Cv.FormationsByCv.Any())
            {
                <div class="bb1 ptB pbB">
                    <h2 class="fs20 mb20 fs16_m mb0_m mB_neg_m title_m">@PageLiteralsHelper.GetLiteral("H2_FORMACION", PageId, portalConfig)</h2>

                    <ul class="list_timeline small">
                        @foreach (var formation in Model.Candidate.Cv.FormationsByCv)
                        {
                            <li>
                                <div class="w20 left">
                                    @if (formation.EndDate == DateTime.MinValue)
                                    {
                                        @String.Format("{0} {1}", formation.EndDateString, formation.InitDateString)
                                    }
                                    else
                                    {
                                        @String.Format("{0} - {1}", formation.InitDateString, formation.EndDateString)
                                    }
                                </div>
                                <div class="w80">
                                    @if (!string.IsNullOrEmpty(formation.Institution))
                                    {
                                        <p>
                                            @if (Model.IsLimitedCV)
                                            {
                                                <span>
                                                    @PageLiteralsHelper.GetLiteral("LIT_CENTER_NAME", PageId, portalConfig)
                                                    <a class="isBlocked js_bttUnblockBBDDCv" data-idoffer="@Model.OfferIdEncrypted" data-featureprod="@Model.ProdCWConvertToComplete"><span>@PageLiteralsHelper.GetLiteral("LIT_FOR_COMPLETE_OFFERS_BBDD", PageId, portalConfig)</span></a>
                                                </span>
                                            }
                                            else
                                            {
                                                <span class="fwB">
                                                    @formation.Institution
                                                </span>
                                            }
                                        </p>
                                    }
                                    <div class="fc_aux list_dot mb5">
                                        <span>
                                            @String.Format("{0}-{1} ", formation.Study, formation.StudyLevel)
                                        </span>
                                        @if (!string.IsNullOrEmpty(formation.IdSpecializationstr))
                                        {
                                            <span>
                                                @formation.IdSpecializationstr
                                            </span>
                                        }
                                    </div>
                                </div>
                            </li>
                        }
                    </ul>
                </div>
            }

            @if (Model.Candidate.Cv.SkillsByCv.Any() || Model.Candidate.Cv.LanguagesByCv.Any())
            {
                <div class="bb1 ptB pbB">
                    <h2 class="fs20 mb20 fs16_m mb0_m mB_neg_m title_m">@PageLiteralsHelper.GetLiteral("LIT_KNOWLEDGE_AND_SKILLS", PageId, portalConfig)</h2>

                    @if (Model.Candidate.Cv.SkillsByCv.Any())
                    {
                        foreach (var skillGroup in Model.Candidate.Cv.SkillsByCv.GroupBy(n => n.SkillTypeDescription))
                        {
                            <h3 class="fwB mb10">@(skillGroup.FirstOrDefault() != null ? skillGroup.FirstOrDefault().SkillTypeDescription : "")</h3>

                            <div class="mbB">
                                @foreach (var item in skillGroup)
                                {
                                    <span class="tag big bg_premium mb10">@item.Value</span>
                                }
                            </div>
                        }
                    }

                    @if (Model.Candidate.Cv.LanguagesByCv.Any())
                    {
                        <h3 class="fwB mb10">@PageLiteralsHelper.GetLiteral("H2_IDIOMAS", PageId, portalConfig)</h3>
                        <div class="mbB">
                            @foreach (var item in Model.Candidate.Cv.LanguagesByCv)
                            {
                                <span class="tag big bg_premium mb10">@string.Format("{0} ({1})", item.Lenguage, item.LenguageLevel)</span>
                            }
                        </div>
                    }
                </div>
            }

            @if (Model.HasTalentView3dFeature && Model.Candidate.HasTalentView3D)
            {

                if (Model.Candidate.Cv.CompetenceList != null && Model.Candidate.Cv.CompetenceList.Any())
                {
                    <div class="bb1 ptB pbB" id="competencias">
                        <h2 class="fs20 mbB fs16_m mB_neg_m title_m">@PageLiteralsHelper.GetLiteral("LIT_TEST_COMPETENCIAS", PageId, portalConfig)</h2>
                        <div class="box_border prB plB">
                            @{
                                int index = 1;
                                foreach (var competence in Model.Candidate.Cv.CompetenceList.OrderByDescending(c => c.Result).ToList())
                                {
                                    <div class="dFlex mt15 mb30">
                                        <div class="mrB">
                                            <div class="doughnut thin mAuto">
                                                <div id="value_detail_@index" class="portion" style="background-image: conic-gradient(#005DA9 0 calc((@competence.Result / 100) * 360deg), #eaf3fb 0);"></div>
                                                <p class="center fs20">@competence.Result</p>
                                            </div>
                                        </div>
                                        <input type="hidden" value="@competence.Result" id="input_value_detail_@index" />
                                        <div class="mlB">
                                            <p class="fs16 fwB mb5">@competence.Title</p>
                                            <p>@competence.Description</p>
                                        </div>
                                    </div>
                                }
                                index++;
                            }
                            <div style="height: 350px">
                                <canvas id="radarChart" width="975" height="350" style="display: block;" class="chartjs-render-monitor"></canvas>
                            </div>
                        </div>
                    </div>
                }

                if (Model.Candidate.Cv.ValuesList != null && Model.Candidate.Cv.ValuesList.Any())
                {
                    <div class="bb1 ptB pbB" id="valores">
                        <h2 class="fs20 mbB fs16_m mB_neg_m title_m">@PageLiteralsHelper.GetLiteral("LIT_TALENT_VIEW_VALUES", PageId, portalConfig)</h2>
                        <div class="box_border prB plB">
                            @foreach (var value in Model.Candidate.Cv.ValuesList.OrderByDescending(c => c.Result).OrderByDescending(x => x.Result).ToList())
                            {
                                // Mapear el valor de Result al rango de 0 a 5
                                var color = value.ShortScore == 1 ? "border-color: #7D66A8!important; "
                                : value.ShortScore == 2 ? "border-color: #7D66A820!important;"
                                : value.ShortScore == 3 ? "border-color: #005DA920!important;"
                                : value.ShortScore == 4 ? "border-color: #005DA950!important;"
                                : "border-color: #005DA9!important;";

                                <div class="dFlex mt15 mb30">
                                    <div class="mt15 mb15">
                                        <div class="doughnut half default">
                                            <div style="transform: rotate(@(value.ShortScoreRotate)deg);@color"></div>
                                            <span class="result">@value.ShortScore</span>
                                        </div>
                                        <span class="legend"><span>0</span><span>5</span></span>
                                    </div>
                                    <div class="mlB">
                                        <p class="fs16 fwB mb5">@value.Title</p>
                                        <p>@value.Description</p>
                                    </div>
                                </div>

                            }
                            <div class="w70 mAuto w100_m">
                                <div class="table_results ">
                                    @foreach (var group in Model.Candidate.Cv.ValuesList)
                                    {
                                        <div>
                                            <span class="@(group.ShortScoreGraph1Class)">
                                                @if (group.ShortScore1)
                                                {
                                                    <span></span>
                                                }
                                            </span>
                                            <span class="@(group.ShortScoreGraph2Class)">
                                                @if (group.ShortScore2)
                                                {
                                                    <span></span>
                                                }
                                            </span>
                                            <span class="@(group.ShortScoreGraph3Class)">
                                                @if (group.ShortScore3)
                                                {
                                                    <span></span>
                                                }
                                            </span>
                                            <span class="@(group.ShortScoreGraph4Class)">
                                                @if (group.ShortScore4)
                                                {
                                                    <span></span>
                                                }
                                            </span>
                                            <span class="@(group.ShortScoreGraph5Class)">
                                                @if (group.ShortScore5)
                                                {
                                                    <span></span>
                                                }
                                            </span>
                                            <span class="legend">@group.Title</span>
                                        </div>
                                    }
                                </div>
                                <div class="legend">
                                    <span>@PageLiteralsHelper.GetLiteral("LIT_TALENT_VIEW_NO_DOMINANT", PageId, portalConfig)</span>
                                    <span>@PageLiteralsHelper.GetLiteral("LIT_TALENT_VIEW_DOMINANT", PageId, portalConfig)</span>
                                </div>
                            </div>
                        </div>
                    </div>
                }

                if (Model.Candidate.Cv.PersonalityList != null && Model.Candidate.Cv.PersonalityList.Any())
                {
                    <div class="bb1 ptB pbB" id="personalidad">
                        <h2 class="fs20 mb15 fs16_m mb0_m mB_neg_m title_m">@PageLiteralsHelper.GetLiteral("LIT_TALENT_VIEW_PERSONALITY", PageId, portalConfig)</h2>
                        <div class="dFlex vm_fx w100 dB_m">
                            <div class=" w60 w100_m">
                                <p class="fwB fs18 mbB mtB">@Model.Candidate.Cv.PersonalityList.FirstOrDefault().Title</p>
                                <p>@Model.Candidate.Cv.PersonalityList.FirstOrDefault().Description</p>
                            </div>
                            <div class="w40 hide_m tc">
                                <img src="~/c/v2/img/triple_test.svg" alt="Tes de Personalidad Laboral" width="150">
                            </div>
                        </div>
                    </div>
                }
            }
            else if (!Model.IsBasicOffer && Model.HasCompanyTestCompetencesFeature && Model.Candidate.HasTestCompetences)
            {
                <div id="test" class="bb1 ptB pbB">
                    <h2 class="fs20 mb5 fs16_m mB_neg_m title_m">@PageLiteralsHelper.GetLiteral("LIT_TEST_COMPETENCIAS", PageId, portalConfig)</h2>

                    <div style="height: 350px">
                        <canvas id="radarChart" width="975" height="350" style="display: block;" class="chartjs-render-monitor"></canvas>
                    </div>

                    <ul id="list_results_detail" class="mbB mtB mt0_m">
                        @{
                            int index = 1;
                            foreach (var test in Model.Candidate.Cv.CompetenceList.OrderByDescending(c => c.Result).ToList())
                            {
                                <li class="cols mb15 mb10_m">

                                    <div class="pr15">
                                        <div class="posRel">
                                            <div style="height: 80px; width: 80px">
                                                <canvas id="value_detail_@index" width="80" height="80" style="display: block; width: 80px; height: 80px;" class="chartjs-render-monitor"></canvas>
                                            </div>
                                            <div class="info_donut @(test.Result > 49 ? "fc_ok" : "fc_brand") fwB fs21">@test.Result</div>
                                        </div>
                                    </div>
                                    <input type="hidden" value="@test.Result" id="input_value_detail_@index" />
                                    <div class="w100">
                                        <p class="fs15 fwB lh1 mb5">
                                            @test.Title
                                        </p>
                                        <p class="fc_aux">@test.Description</p>
                                    </div>
                                </li>
                                index++;
                            }
                        }
                    </ul>
                </div>

            }

            @if (Model.IsViewed && !string.IsNullOrEmpty(Model.Candidate.Cv.LongDescription))
            {
                <div class="bb1 ptB pbB">
                    <h2 class="fs20 mb20 fs16_m mb0_m mB_neg_m title_m">@PageLiteralsHelper.GetLiteral("H2_DESC_BREVE", PageId, portalConfig)</h2>
                    <div>
                        @Html.Raw(Model.Candidate.Cv.LongDescription.Replace(Environment.NewLine, "<br/>"))
                    </div>
                </div>
            }

            @if (!Model.IsBasicOffer && Model.HasCompanyRatingsFeature)
            {
                if (Model.Candidate.HasRatings)
                {
                    <div class="bg_info_light sub_box fc_aux dFlex vm_fx w100 mt20 dIB_m">
                        <div class="fwB mr15">@PageLiteralsHelper.GetLiteral("LIT_RATING", PageId, portalConfig)</div>
                        <div class="dFlex vm_fx">
                            <span class="mr10">
                                @PageLiteralsHelper.GetLiteral("LIT_MAXRATING", PageId, portalConfig)
                                <span class="fwB">@Model.Candidate.RatingTop.Rating</span>
                            </span>
                            <span class="stars">
                                <span style="width:@Html.Raw((Model.Candidate.RatingTop.Rating * 100) / 5)%;"></span>
                            </span>
                        </div>
                    </div>
                }
            }

            @if (!Model.IsBasicOffer && Model.HasCompanyRatingsFeature)
            {
                <div id="rates" class="bb1 ptB">
                    <h2 class="fs20 mb20 fc_base_m fs16_m mb0_m mB_neg_m title_m">@PageLiteralsHelper.GetLiteral("LIT_RATING", PageId, portalConfig)</h2>

                    @if (Model.Candidate.HasRatings)
                    {
                        foreach (var rating in @Model.Candidate.ListRating)
                        {
                            <div class="@(rating.UserId == companyCredentials.UserId ? "bg_info_light" : "bg_base_light") sub_box mbB pb20 pt20 mb15">
                                <p>
                                    <span class="fwB mr10">@rating.NameAuthor</span>
                                    <span class="fc_aux fs12">@rating.TextDate</span>
                                </p>
                                <span class="stars mt5">
                                    <span style="width:@Html.Raw((rating.Rating) * 100 / 5)%;"></span>
                                </span>

                                @if (rating.CanRemoving)
                                {
                                    <input type="button" title="@PageLiteralsHelper.GetLiteral("LIT_REMOVE_RATING", PageId, portalConfig)" class="b_transparent pAll0 icon i_close"
                                           id="removeRating" onclick="removeClickRating(event); return false;"
                                           data-idratinguser="@EncryptationHelper.Encrypt(rating.UserId.ToString())" />
                                }
                            </div>
                        }
                    }
                    else
                    {
                        <div class="mbB pb20 tc_m">
                            <label class="mb0 mr10 vm">@PageLiteralsHelper.GetLiteral("LIT_VALORA", PageId, portalConfig)</label>
                            <div class="field_stars dIB mb0 mr15 vm">
                                <div class="list_stars vm" id="js_stars_rate">
                                    <label>
                                        <input type="radio" value="1" name="js_stars_rate">
                                        <span id="js_stars_rate_1" class="full_star"></span>
                                    </label>
                                    <label>
                                        <input type="radio" value="2" name="js_stars_rate">
                                        <span id="js_stars_rate_2" class=""></span>
                                    </label>
                                    <label>
                                        <input type="radio" value="3" name="js_stars_rate">
                                        <span id="js_stars_rate_3" class=""></span>
                                    </label>
                                    <label>
                                        <input type="radio" value="4" name="js_stars_rate">
                                        <span id="js_stars_rate_4" class=""></span>
                                    </label>
                                    <label>
                                        <input type="radio" value="5" name="js_stars_rate">
                                        <span id="js_stars_rate_5" class=""></span>
                                    </label>
                                </div>
                            </div>
                            <div>
                                @if (Model.IsViewed)
                                {
                                    <input type="submit" class="b_primary wAuto mtB w100_m" data-processing="@PageLiteralsHelper.GetLiteral("LIT_PROCESSING", PageId, portalConfig)"
                                           id="btnInsertRating" value='@PageLiteralsHelper.GetLiteral("LIT_PUBLISH_RATING", PageId, portalConfig)' />
                                }
                                else
                                {
                                    <input type="submit" class="isBlocked b_primary wAuto mtB w100_m js_bttUnblockBBDDCv" data-processing="@PageLiteralsHelper.GetLiteral("LIT_PROCESSING", PageId, portalConfig)"
                                           value='@PageLiteralsHelper.GetLiteral("LIT_PUBLISH_RATING", PageId, portalConfig)' />
                                }
                            </div>
                        </div>
                    }

                </div>
            }

            @if (!Model.IsBasicOffer && Model.HasCompanyCommentsFeature)
            {
                <div class="bb1 ptB pbB">
                    <h2 class="fc_info fs20 mb20 fc_base_m fs16_m mb0_m mB_neg_m title_m">@PageLiteralsHelper.GetLiteral("LIT_COMENTARIOS", PageId, portalConfig)</h2>

                    @if (Model.Candidate.HasComments)
                    {
                        <ul id="commentList">
                            @foreach (var comment in Model.Candidate.ListComments)
                            {
                                <li class="@(comment.UserId == companyCredentials.UserId ? "bg_info_light" : "bg_base_light") sub_box mb15">
                                    <p>
                                        <span class="fwB mr10">@comment.NameAuthor</span>
                                        <span class="fc_aux fs12">@comment.TextDate</span>
                                    </p>
                                    <p class="mrB mt10">
                                        @comment.Comment
                                    </p>

                                    @if (comment.CanRemoving)
                                    {
                                        <input type="button" title="@PageLiteralsHelper.GetLiteral("LIT_REMOVE_RATING", PageId, portalConfig)" class="b_transparent pAll0 icon i_close"
                                               id="removeComment" onclick="removeClickComment(event); return false;"
                                               data-idcomment="@EncryptationHelper.Encrypt(comment.Id.ToString())" />
                                    }
                                </li>
                            }
                        </ul>
                    }

                    <div class="field_textarea mb15 mtB">
                        @Html.TextAreaFor(n => n.Candidate.CommentTextArea, 2, 20, new { @class = "field_textarea mb15 mtB" })
                        <span class="info_txta" id="charNum"></span>
                    </div>

                    @if (Model.IsViewed)
                    {
                        <input type="submit" class="b_primary wAuto w100_m" data-processing="@PageLiteralsHelper.GetLiteral("LIT_PROCESSING", PageId, portalConfig)" id="bttPublicar" value='@PageLiteralsHelper.GetLiteral("LIT_BTN_PUBLICAR_COMMENTS", PageId, portalConfig)' />
                    }
                    else
                    {
                        <input type="submit" class="isBlocked b_primary wAuto w100_m js-processing js_bttUnblockBBDDCv" data-processing="@PageLiteralsHelper.GetLiteral("LIT_PROCESSING", PageId, portalConfig)" value='@PageLiteralsHelper.GetLiteral("LIT_BTN_PUBLICAR_COMMENTS", PageId, portalConfig)' />
                    }
                </div>
            }

            @if (!Model.IsBasicOffer && Model.HasHistory && Model.Candidate.HistoryOffers.Count > 0)
            {
                <div id="MoreOffers" class="ptB">
                    <h2 class="fs20 mb20 fs16_m mb0_m mB_neg_m title_m">@PageLiteralsHelper.GetLiteral("LIT_TITLE_HISTORYOFFER", PageId, portalConfig)</h2>

                    <ul id="listHist">
                        @foreach (var hist in Model.Candidate.HistoryOffers)
                        {
                            <li class="box_border dFlex pAll0 mb15 dIB_m w100_m">
                                <div class="w70 pAll25 pAll15_m w100_m">
                                    <p class="fs16 fwB mb5">@hist.TitleOffer</p>
                                    <p>@PageLiteralsHelper.GetLiteral("LIT_PUBLICADA", PageId, portalConfig) @hist.TextOfferDate</p>
                                </div>
                                <div class="w30 mlAuto bg_base_light pAll15 tc dFlex tc_fx vm_fx row_fx w100_m">
                                    <p id="candidateStatus" class="fc_ok"></p>
                                    <p>@hist.TextCandidateDate</p>
                                </div>
                            </li>
                        }
                    </ul>
                </div>
            }
        </div>

    </article>

    <input type="hidden" id="hdrating" value="" />
    <input type="hidden" id="hiddenRatingUserRemove" value="" />
    <input type="hidden" id="hiddenCommentUserRemove" value="" />
    <input type="hidden" id="hdrating" value="" />
    @Html.HiddenFor(n => n.IdCvEncrypted)

    <!-- Fin nueva maquetación -->


    <div class="popup flex hide" id="popupDownloadPDF">
        <div class="w40">
            <div>
                <p class="tc mbB">@PageLiteralsHelper.GetLiteral("LIT_GENERATE_TALENT", PageId, portalConfig)</p>
                <div class="loading"><div><span></span><span></span><span></span><span></span></div><div><span></span><span></span><span></span><span></span></div><div><span></span><span></span><span></span><span></span></div></div>
            </div>
        </div>
    </div>

    @if (!Model.IsBasicOffer)
    {
        @Html.DisplayFor(p => p.NoFolderToMakeChatPopUp, new
        {
            IdPopUp = "CreateChatUniNoFolder",
            IdBtnOk = string.Empty,
            IdBtnKo = "lnkCancelar",
            IdInput = string.Empty,
            OnClick = string.Empty
        })

        @Html.DisplayFor(p => p.SureToLogicDelete, new
        {
            IdPopUp = "SureToLogicDelete",
            IdBtnOk = "bttSureToLogicDelete",
            IdBtnKo = "lnkCancelar",
            IdInput = string.Empty,
            OnClick = "CasuistryDeleteMatchDDL();"
        })

        @Html.DisplayFor(p => p.SureToDiscardCandidate, new
        {
            IdPopUp = "SureToDiscardCandidate",
            IdBtnOk = "bttSureToDiscardCandidate",
            IdBtnKo = "lnkCancelar",
            IdInput = string.Empty,
            OnClick = "CasuistryDeleteMatchDDL();"
        })
    }

</main>

@section CustomScriptsFooterSection
{
    <script>
        function CheckCvDocument() {
            hasDocument = '@Model.HasDocument'

            if (hasDocument == `False`) return;


            $.ajax({
                async: false,
                cache: false,
                dataType: "json",
                type: 'POST',
                url: '@Url.Action("CheckFileCvAmazon", "CompanyCvDownloader")',
                data:
                {
                    ic: '@Model.IdCandidateEncrypted',
                    idcv: '@Model.IdCvEncrypted',
                    mfid: ''
                },
                success: function (respuesta) {
                    if (respuesta == true) {
                        $(".js_download_file").removeClass("hide");
                    }
                }
            });
        }

        function GeneratePDF() {
            let c = '@Model.IdCandidateEncrypted';
            let cv = '@Model.IdCvEncrypted';

            $("#popupDownloadPDF").removeClass("hide");

            fetch('@Url.Action("ExportPdf", "CompanyExportToPDF")', {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({
                    c: c,
                    cv: cv
                })
            })
            .then(response => {
                if (!response.ok) {
                    return response.text().then(text => {
                        throw new Error(text);
                    });
                }
                return response.blob();
            })
            .then(blob => {
                let url = URL.createObjectURL(blob);
                let link = document.createElement("a");
                link.href = url;
                link.setAttribute("download", "TalentView.pdf");
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                URL.revokeObjectURL(url);
                $("#popupDownloadPDF").addClass("hide");
            })
            .catch(error => {
                console.error("Error en la solicitud AJAX:", error.message);
                $("#popupDownloadPDF").addClass("hide");
            });
        }

        $(document).ready(function () {
            CheckCvDocument();
        });

        function ReportPhoto(reason) {
            $.ajax({
                async: false,
                cache: false,
                dataType: "html",
                type: 'POST',
                url: '@Url.Action("InsertCandidatePhotoComplaint", "CompanyBBDDCvDetail")',
                data:
                {
                    idc: '@Model.IdCandidateEncrypted',
                    reason: reason
                },
                success: function (respuesta) {
                    if (respuesta.toLowerCase() == "true") {
                        $("#candidatePhotoReportedPopUp").removeClass("hide");
                    }
                }
            });
        };

        $('.info_tooltip').click(function () {
            $(this).find(".opt_bubble").toggleClass("show");
        });

        $(document).click(function (e) {
            if (!$(e.target).closest('.info_tooltip').length) {
                $('.opt_bubble.show').removeClass('show');
            }
        });

        $(".edit_custom_folder").click(function () {
            idEncryptedFolderEdited = $(this).attr("data-id-folder");
            document.getElementById("NewNameFolder").value = document.getElementById(idEncryptedFolderEdited).innerText;

            $('#popupUpdateFolder').removeClass("hide");
        });


        $(".newactionButtons").click(function () {
            $('#PopUpComparative').removeClass("hide");
        });

        $(".js_bttUnblockBBDDCv").click(function () {
            if (@Model.TotalAllowedCvVisualization > 0) {
                $('#UnblockBBDDCvPopUp').removeClass("hide");
            }
            else if (@Model.HaveMembresyActive.ToString().ToLower()) {
                window.location.href = '@Url.Action("NoTokens", "Contact", new { ctaOrigin = (short)CommercialRequestOriginEnum.CV_Visualization })';
            }
            else {
                $('#userNotCreditAvailablePopUp').removeClass("hide");
            }
        });

        $(".js_showBBDDcvProd").click(function () {
            if (@Model.HaveMembresyActive.ToString().ToLower()) {
                window.location.href = '@Url.Action("NoTokens", "Contact", new { ctaOrigin = (short)CommercialRequestOriginEnum.CV_Visualization })';
            }
            if (@(string.IsNullOrWhiteSpace(Model.OfferCtIdEncrypted).ToString().ToLower()) ||
            @((!string.IsNullOrWhiteSpace(Model.OfferCtIdEncrypted) && portalConfig.AEPortalConfig.UseBBDDcvProductForOffers).ToString().ToLower())) {
                $('#BBDDcvProdPopUp').removeClass("hide");
            }
            if (@((!string.IsNullOrWhiteSpace(Model.OfferCtIdEncrypted) && !portalConfig.AEPortalConfig.UseBBDDcvProductForOffers).ToString().ToLower())) {
                $('#userNotCreditAvailablePopUp').removeClass("hide");
            }
        });

        $("#addfolder").click(function (e) {
            $('#popupAddFolder').removeClass("hide");
        });

        $(".edit_folder").click(function (e) {
            sic = $(this).data('id');
            $('#editCVCustomFolderPopUp').removeClass("hide");
        });

        $(".delete_folder_action").click(function (e) {
            sic = $(this).data('id');
            fol = $(this).data('fol');
            idEncryptedFolderEdited = $(this).attr("data-id-folder");
            $('#DeleteFolderPopUp').removeClass("hide");
        });

        function removeClickRating(event) {
            var $this = $(event.target);
            $("#hiddenRatingUserRemove").val($this.data("idratinguser"));
            $('#deleteRatingPopUp').removeClass("hide");
        }

        function removeClickComment(event) {
            var $this = $(event.target);
            $("#hiddenCommentUserRemove").val($this.data("idcomment"));
            $('#deleteCVCommentPopUp').removeClass("hide");
        }
    </script>

    @if (!Model.IsBasicOffer)
    {
        <script type="text/javascript">
            document.addEventListener("DOMContentLoaded", function () {

                $('.field_stars .list_stars').each(function () {
                    setCheckedStar($(this));
                });

                $('.field_stars span[id*=js_stars]').mouseover(function () {
                    var nameStar = $(this).attr('id');
                    nameStar = nameStar.slice(9, -2);
                    numStar = $(this).attr('id').slice(-1);
                    fillStars(numStar, nameStar);
                });

                $('.field_stars .list_stars').mouseleave(function () {
                    setCheckedStar($(this));
                });

                $('.field_stars input[name*=js_stars]').change(function () {
                    if ($(this).prop("checked")) {
                        var nameStar = $(this).attr('name');
                        nameStar = nameStar.slice(9);
                        numStar = $(this).attr('value');
                        fillStars(numStar, nameStar);
                    }
                });
            });

            function fillStars(numStar, nameStar) {
                for (var i = 1; i <= 5; i++) {
                    if (nameStar != '')
                        name = '#js_stars_' + nameStar + '_' + i;
                    else
                        name = '#js_stars_' + i;
                    if (numStar >= i)
                        $(name).addClass('full_star');
                    else
                        $(name).removeClass('full_star');
                }
            };

            function setCheckedStar(star) {
                var nameStar = star.attr('id').slice(9);
                var checkedStar = star.find('input[name*=js_stars]:checked').val();
                if (checkedStar !== '0')
                    fillStars(checkedStar, nameStar);
                else
                    fillStars(0, nameStar);
            }
        </script>
    }


    @Scripts.Render("~/bundles/js/cvlist")
    <script type="text/javascript">
        $("#tab-searchCV").addClass("selec");
        var configCvList = {
            urlActionFormAjaxUpdateFolder: '@Url.Action("UpdateFolder","CompanyCvs")',
            urlActionFormAjaxAddFolder: '@Url.Action("AddFolder", "CompanyCvs")',
            urlActionFormAjaxRemoveCvs: '@Url.Action("DelecteCvsFromFolder","CompanyCvs")',
            urlActionFormAjaxRemoveFolder : '@Url.Action("DeleteFolder", "CompanyCvs")',
            isShowFolders: '@Model.HasCustomFolders',
            literalErrorInputNameFolder: '@PageLiteralsHelper.GetLiteral("LIT_PLS_INPUT_NAME", PageId, portalConfig)',
            literalErrorUpdateFolder: '@PageLiteralsHelper.GetLiteral("LIT_NOT_UPDATED", PageId, portalConfig)',
            urlActionFormAjaxAddCvsFolder: '@Url.Action("AddCvsToFolder","CompanyCvs")',
            idFolderEncrypted: '@Model.FolderSelectedEncrypted',
            passLimitFolder: '@((short)FolderCustomStatusResponseEnum.PASS_LIMIT)',
            notCanAddFolder: '@((short)FolderCustomStatusResponseEnum.NOT_CAN_ADD_CVS)',
            notAllCvsFolder: '@((short)FolderCustomStatusResponseEnum.NOTALLCVS)',
            AllCvsFolder: '@((short)FolderCustomStatusResponseEnum.ALLCVS)',
            existOrNotViewedFolder: '@((short)FolderCustomStatusResponseEnum.EXIST_OR_NOT_VIEWED)',
            noNameFolder: '@((short)FolderCustomStatusResponseEnum.NO_NAME)',
            literalErrorInputName: '@PageLiteralsHelper.GetLiteral("LIT_PLS_INPUT_NAME",PageId, portalConfig)',
            literalErrorMaxFolder: '@PageLiteralsHelper.GetLiteral("LIT_MAX_FOLDERS", PageId,portalConfig)',
            literalErrorNotRemoveFolder: '@PageLiteralsHelper.GetLiteral("LIT_NOT_UPDATED", PageId, portalConfig)',
            ConsumeCreditsToViewCv: "@Url.Action("ConsumeCreditToViewCv", "CompanyMatchCvDetail")",
            literalProcessing: '@PageLiteralsHelper.GetLiteral("LIT_PROCESSING", PageId, portalConfig)',
            goToPostPublishStepTwo: "@Url.Action("Index", "CompanyPostPublishStepTwo", new { oi = "#idofenc#", p = EncryptationHelper.Encrypt(((int)PageEnum.PostPublishStepTwo).ToString()), btn = Model.BtnConvertToCompleteEncryptedName })",
            ajaxManagePopUpConvertToCompleteBtt: "@Url.Action("ajaxManagePopUpConvertToCompleteBtt", "CompanyOffers")",
            goToMPCart: "@Url.Action("Index", "MultiPurchaseCart", new { oi = "#idofenc#", p = EncryptationHelper.Encrypt(((int)PageEnum.PackCart).ToString()), btn = Model.BtnConvertToCompleteEncryptedName, prod = Model.ProdCWConvertToComplete })"
        };
        InitializeCvList(configCvList);
        const cvList = new CvList(configCvList);

    </script>

    @if (!Model.IsViewed)
    {
        <script type="text/javascript">
            @if (Model.TotalAllowedCvVisualization > 0)
            {

                        @:function ConsumeCredits()
                        @:{
                        @:    let data ={
                        @:    'oi': '@Model.OfferIdEncrypted',
                        @:    'idc': '@Model.IdCandidateEncrypted',
                        @:    'idcv': '@Model.IdCvEncrypted'
                        @:    };
                        @: cvList.ConsumeCredictAjax(data);
                        @:}
            }
            else
            {
                    @:function ConsumeCredits()
                        @:{
                        @: ShowPopUp("#NoMoreCredits");
                        @:}
        }
        </script>
    }


    @Scripts.Render("~/bundles/js/match")
    @Scripts.Render("~/bundles/js/videoCandidate")

    <script type="text/javascript">

        InitializeVideo({
            GetCandidateVideoPresentation: '@Url.Action("GetCandidateVideoPresentation", "CompanyMatchCvDetail")',
            HasVideoPresentation: '@Model.ShowVideoPresentation',
            IsColombia: @portalConfig.PortalId == @((short)PortalEnum.ComputrabajoColombia) ? 'true' : 'false',
            IsViewed: '@Model.IsViewed',
            IsFromMatchesCVDetail: 'false'
        });

        InitializeMatchList({
            AddCustomFolders: "@Url.Action("AddCustomFolders", "CompanyMatches")",
            UpdateCustomFolders: "@Url.Action("UpdateCustomFolders", "CompanyMatches")",
            DeleteCustomFolders: "@Url.Action("DeleteCustomFolders", "CompanyMatches")",
            MoveCVsFolder: "@Url.Action("MoveCVsFolder", "CompanyMatches")",
            LogicalDeleteCVsFolder: "@Url.Action("LogicalDeleteCVsFolder", "CompanyMatches")",
            PrintCVs: "@Url.Action("Print", "CompanyMatchCvDetail")",
            ExportCVs: "@Url.Action("Extract", "CompanyMatches")",
            ExportCVsDownloader: "@Url.Action("Extract", "CompanyCvDownloader")",
            InsertMessage: '@Model.UrlInsertMessage',
            NewConver: '@Model.UrlCreateConversation',
            Messages: '@Model.UrlIndexConversations',
            Matches: '@Url.Action("Index", "CompanyMatches")',
            IsMembershipOrCompleteOffer: '@Model.HaveOfferCompletOrMembership',
            IdOfferEncrypted: '@Model.OfferIdEncrypted',
            IdOfferCTEncrypted: '@Model.OfferCtIdEncrypted',
            InsertRatingCv: '@Url.Action("InsertRating", "CompanyRatingsCv")',
            DeleteRatingCv: '@Url.Action("DeleteRating", "CompanyRatingsCv")',
            InsertComentCv:'@Url.Action("InsertComment", "CompanyCommentsCv")',
            DeleteComentCv: '@Url.Action("DeleteComment", "CompanyCommentsCv")',
            ConvertToCompleteAjax: "@Url.Action("ExecuteAction", "CompanyOffers")",
            LIT_OFFER_ACTION_UPGRADING: "@PageLiteralsHelper.GetLiteral("LIT_OFFER_ACTION_UPGRADING", PageId, portalConfig)",
            goToPostPublishStepTwo: "@Url.Action("Index", "CompanyPostPublishStepTwo", new { oi = "#idofenc#", p = EncryptationHelper.Encrypt(((int)PageEnum.PostPublishStepTwo).ToString()), btn = Model.BtnConvertToCompleteEncryptedName })",
            ajaxManagePopUpConvertToCompleteBtt: "@Url.Action("ajaxManagePopUpConvertToCompleteBtt", "CompanyOffers")",
            goToMPCart: "@Url.Action("Index", "MultiPurchaseCart", new { oi = "#idofenc#", p = EncryptationHelper.Encrypt(((int)PageEnum.PackCart).ToString()), btn = Model.BtnConvertToCompleteEncryptedName, prod = Model.ProdCWConvertToComplete })",
            DescriptionCoverLetterComplete: @Html.Raw(Json.Encode(Model.DescriptionCoverLetterComplete)),
            ShowMoreCoverLetter: '@Url.Action("ShowMoreCoverLetter", "CompanyMatchCvDetail")'
        });

        InitializeConvertToCompletePopUp({
            goToPostPublishStepTwo: "@Url.Action("Index", "CompanyPostPublishStepTwo", new { oi = "#idofenc#", p = EncryptationHelper.Encrypt(((int)PageEnum.PostPublishStepTwo).ToString()), btn = Model.BtnConvertToCompleteEncryptedName })",
            goToMPCart: "@Url.Action("Index", "MultiPurchaseCart", new { oi = "#idofenc#", p = EncryptationHelper.Encrypt(((int)PageEnum.PackCart).ToString()), btn = Model.BtnConvertToCompleteEncryptedName, prod = Model.ProdCWConvertToComplete })",
            goToChooseMP:"@Url.Action("landing-convert-packs", "Company/Landings", new { io = "#idofenc#", cc= EncryptationHelper.Encrypt("1"), btnConvertEnc = Model.BtnConvertToCompleteEncryptedName })",
            ajaxManagePopUpConvertToCompleteBtt: "@Url.Action("ajaxManagePopUpConvertToCompleteBtt", "CompanyOffers")",
            idPage: @PageId,
            ajaxConvertToCompleteUrl: "@Url.Action("ExecuteAction", "CompanyOffers")",
            buttonConvertToCompleteEncryptedName : "@buttonConvertToCompleteEncryptedName"
        });

        function RedirectToDeleteOne(selected) {
            CasuistryDeleteMatchDDL(selected);
        }

        function CasuistryDeleteMatchDDL(selected) {
            let fo = '4';
            if ('@Model.HasFoldersDetail'.toLowerCase() === 'true' || '@Model.HasCustomFolders'.toLowerCase() === 'true') {
                fo = '@Model.FolderSelected';
            }

            if (selected) {
                fd = selected;
            }
            else {
                fd = '4';
            }

            let oi = '@Model.OfferIdEncrypted';
            let id = '@Model.MatchIdEncrypted';

            let m = "<h1>" + '@PageLiteralsHelper.GetLiteral("LIT_UPDATING_BLOCK_UI", PageId, portalConfig)' + "...</h1>";

            if (fo == fd) {
                LogicalDeleteMatch(oi, id, fd, fo, m);
            }
            else {
                ChangeFolderMatch(oi, id, fd, fo, m, @PageId);
            }
        }
    </script>


    @if (!Model.IsBasicOffer)
    {
        <script type="text/javascript">
             var del = "0";

            if('@Model.HaveOfferCompletOrMembership'.toLowerCase() === 'true') {

            var sic = 0;
            var fol = 0;

            function DeleteFolder(event) {
                let dataFolder =
                    {
                        'namefd': $("#nameFolder").val(),
                        'oi': '@Model.OfferCtIdEncrypted',
                        'p': '@portalConfig.PortalId',
                        'idf': sic,
                        'f': fol,
                        'idu': '@EncryptationHelper.Encrypt(companyCredentials.UserId.ToString())',
                        'env': '@EncryptationHelper.Encrypt(((short)CustomFoldersEnvironmentEnum.Matches).ToString())'
                    };

                const acctionFolders = new customFolders(configModelMatch.DeleteCustomFolders, dataFolder, '@PageLiteralsHelper.GetLiteral("LIT_NOT_DELETED", PageId, portalConfig)');

                acctionFolders.DeleteCustomFolder();
                event.preventDefault();
            }

            function AddFolder(event) {
                let dataFolder =
                    {
                        'namefd': $("#nameAddFolder").val(),
                        'oi': '@Model.OfferCtIdEncrypted',
                        'p': '@portalConfig.PortalId',
                        'idu': '@EncryptationHelper.Encrypt(companyCredentials.UserId.ToString())',
                        'env': '@EncryptationHelper.Encrypt(((short)CustomFoldersEnvironmentEnum.Matches).ToString())'
                    };
                const acctionFolders = new customFolders(configModelMatch.AddCustomFolders, dataFolder, '@PageLiteralsHelper.GetLiteral("LIT_NOT_UPDATED", PageId, portalConfig)', '@PageLiteralsHelper.GetLiteral("LIT_PLS_INPUT_NAME", PageId, portalConfig)');

                acctionFolders.AddCustomFolders();
                event.preventDefault();
            }

            function EditFolder(event) {
                let dataFolder =
                    {
                        'namefd': $("#nameEditFolder").val(),
                        'oi': '@Model.OfferCtIdEncrypted',
                        'p': '@portalConfig.PortalId',
                        'idu': '@EncryptationHelper.Encrypt(companyCredentials.UserId.ToString())',
                        'env': '@EncryptationHelper.Encrypt(((short)CustomFoldersEnvironmentEnum.Matches).ToString())',
                        'idf': sic
                    };
                const acctionFolders = new customFolders(configModelMatch.UpdateCustomFolders, dataFolder, '@PageLiteralsHelper.GetLiteral("LIT_NOT_UPDATED", PageId, portalConfig)', '@PageLiteralsHelper.GetLiteral("LIT_PLS_INPUT_NAME", PageId, portalConfig)');

                acctionFolders.UpdateCustomFolder();
                event.preventDefault();
            };

            function NewConver(event) {

                let data =
                    {
                        'ims': '@Model.MatchIdEncrypted',
                        'oi': '@Model.OfferIdEncrypted'
                    };
                    NewConverDiscover(data);
                    event.preventDefault();
                }
            }


            $("#btnInsertRating").one("click", function (event) {
                var ratingNumber = 0;

                $("input[name='js_stars_rate']").each(function () {

                    if ($(this).is(':checked')) {
                        ratingNumber = $(this).val();
                        return false;
                    }
                });

                event.preventDefault();

                $.ajax({
                    async: false,
                    cache: false,
                    dataType: "html",
                    type: 'POST',
                    url: configModelMatch.InsertRatingCv,
                    data: {
                        rtg: ratingNumber,
                        oi: '@Model.OfferIdEncrypted',
                        idc: '@Model.IdCandidateEncrypted',
                        idcv: '@Model.IdCvEncrypted',
                        idu: '@EncryptationHelper.Encrypt(companyCredentials.UserId.ToString())',
                        ic: '@EncryptationHelper.Encrypt(companyCredentials.IdCompany.ToString())'
                    },
                    success:
                    function (respuesta) {
                        $.unblockUI();
                        if (respuesta.toLowerCase() == "true") {
                            location.reload();
                        }
                    },
                    error:
                    function () {
                        $.unblockUI();
                    }
                });

                $("#bttPublicar").removeAttr("disabled");
            });

            function btnRemoveClickRating(event) {
                event.preventDefault();

                $.ajax({
                    async: false,
                    cache: false,
                    dataType: "html",
                    type: 'POST',
                    url: configModelMatch.DeleteRatingCv,
                    data:
                    {
                        oi: '@Model.OfferIdEncrypted',
                        idc: '@Model.IdCandidateEncrypted',
                        idu: '@EncryptationHelper.Encrypt(companyCredentials.UserId.ToString())',
                        ic: '@EncryptationHelper.Encrypt(companyCredentials.IdCompany.ToString())',
                        idcv: '@Model.IdCvEncrypted'

                    },
                    success: function (respuesta) {
                        $.unblockUI();
                        if (respuesta.toLowerCase() == "true") {
                            location.reload();
                        }
                    },
                    error:
                    function () {
                        $.unblockUI();
                    }
                });

                $("#bttPublicar").removeAttr("disabled");
            };

            $("#Candidate_CommentTextArea").keyup(function () {
                return conta();
            });

            $("#bttPublicar").click(function (event){
                if(!$("#bttPublicar").hasClass("disabled") ) {
                    InsertComent(event);
                }
            });

            function InsertComent(event) {
                event.preventDefault();
                $("#bttPublicar").prop("disabled", "true");
                var comment = $("#Candidate_CommentTextArea").val();

                if (validateComment(comment)) {
                    $.ajax({
                        async: false,
                        cache: false,
                        dataType: "html",
                        type: 'POST',
                        url:  configModelMatch.InsertComentCv,
                        data: {
                            comm: comment,
                            commid: '0',
                            idcv: '@Model.IdCvEncrypted',
                            idc: '@Model.IdCandidateEncrypted',
                            io: '@Model.OfferIdEncrypted',
                            un: "@Html.Raw(companyCredentials.Name)"
                        },
                        success: function (respuesta) {
                            location.reload(true);
                        },
                        error: function (request, status, error) {
                            $("#bttPublicar").removeAttr("disabled");
                        }
                    });
                }

                $("#bttPublicar").removeAttr("disabled");
            }

            function btnRemoveClickComment(event) {
                event.preventDefault();

                $.ajax({
                    async: false,
                    cache: false,
                    dataType: "html",
                    type: 'POST',
                    url: configModelMatch.DeleteComentCv,
                    data:
                    {
                        idcom: $("#hiddenCommentUserRemove").val(),
                        oi: '@Model.OfferIdEncrypted',
                        idc: '@Model.IdCandidateEncrypted',
                        idcv: '@Model.IdCvEncrypted',
                        un: '@companyCredentials.Username)'
                    },
                    success: function (respuesta) {
                        $.unblockUI();
                        if (respuesta.toLowerCase() == "true") {
                            location.reload();
                        }
                    },
                    error:
                    function () {
                        $.unblockUI();
                    }
                });

                $("#bttPublicar").removeAttr("disabled");
            };

            function validateComment(comment) {
                var validateOk = true;
                var spanError = $("#charNum");

                if (comment == "") {
                    spanError.className = "error w_100 fl";
                    spanError.innerHTML = '(@PageLiteralsHelper.GetLiteral("LIT_VALIDATE_EMPTY_COMMENT", PageId, portalConfig))';
                    validateOk = false;
                }
                else if (comment.length > 500) {
                    spanError.className = "error w_100 fl";
                    spanError.innerHTML = '(@PageLiteralsHelper.GetLiteral("LIT_VALIDATE_LENGTH_COMMENT", PageId, portalConfig))';
                    validateOk = false;
                }

                return validateOk;
            };

            function conta()
            {
                var max_Lenght = 500;
                var coment = $("#Candidate_CommentTextArea").val();
                var total = max_Lenght - coment.length;

                if (coment.length <= 2)
                {
                    $("#bttPublicar").prop("disabled", "true");
                }
                else if (coment.length > max_Lenght)
                {
                    $("#Candidate_CommentTextArea").val(coment.substring(0, max_Lenght));
                    total = 0;
                }
                else
                {
                    $("#bttPublicar").removeAttr("disabled");
                }

                $("#charNum").html(total + '@PageLiteralsHelper.GetLiteral("LIT_CARACTERES_RESTANTES", PageId, portalConfig)');
            }

        </script>
    }
    else //Is basic offer
    {

        if (Model.IsLimitedCV && Model.ConvertToCompletePopUpAuto != null)
        {
            <script type="text/javascript">
                    $(document).ready(function () {
                        $("#encryptedoffer").val('@Model.OfferIdEncrypted');
                        $('#PopUpComparative').removeClass("hide");
                    });
            </script>
        }
    }

    @if (Model.ShowCompetencesTest ||
    (!Model.IsBasicOffer && Model.HasCompanyTestCompetencesFeature && Model.Candidate.HasTestCompetences))
    {
        <script type="text/javascript">

            $(document).ready(function () {

                $("#list_results li").each(function (index) {
                    var index_item = index + 1;
                    var color1 = "#91b54e";
                    var color2 = "#1e82c4";
                    var color_def = "#edeef1";
                    var value_result = parseInt($("#input_value_" + index_item).val());
                    var value_to_completed = 100 - value_result;
                    if (value_result >= 50) {
                        color_def = color1;
                    } else {
                        color_def = color2;
                    }

                    ChartSettings();
                    var ctx = document.getElementById("value_" + index_item).getContext('2d');
                    var myDoughnutChart = new Chart(ctx, {
                        type: 'pie',
                        data: {
                            datasets: [
                                {
                                    data: [
                                        value_result, value_to_completed
                                    ],
                                    backgroundColor: [
                                        color_def, '#edeef1'
                                    ],
                                    hoverBackgroundColor: [
                                        color_def, '#edeef1'
                                    ],
                                    borderWidth: 3,
                                    borderColor: [
                                        "#fff", "#fff"
                                    ],
                                    hoverBorderColor: ["#fff", "#fff"]
                                }
                            ]
                        },
                        options: {
                            cutoutPercentage: 75,
                            legend: {
                                display: false
                            },
                            maintainAspectRatio: false,

                            tooltips: {
                                enabled: false,
                                xPadding: 12,
                                yPadding: 8,
                                cornerRadius: 3,
                                displayColors: false
                            }
                        }
                    });
                });

                $("#list_results_detail li").each(function (index) {
                    var index_item = index + 1;
                    var color1 = "#708f41";
                    var color2 = "#1e82c4";
                    var color_def = "#000";
                    var value_result = parseInt($("#input_value_detail_" + index_item).val());
                    var value_to_completed = 100 - value_result;
                    if (value_result >= 50) {
                        color_def = color1;
                    }
                    else {
                        color_def = color2;
                    }

                    var ctx = document.getElementById("value_detail_" + index_item);
                    var myDoughnutChart = new Chart(ctx, {
                        type: 'pie',
                        data: {
                            datasets: [{
                                data: [value_result, value_to_completed],
                                backgroundColor: [ color_def, "#CCCCCC"],
                                borderColor: "rgba(0,0,0,0)",
                                borderWidth: 3,
                                borderColor: ["#fff", "#fff"],
                            }]
                        },
                        options: {
                            cutoutPercentage: 75,
                            legend: { display: false },
                            maintainAspectRatio: false,
                            tooltips: {
                                enabled: false,
                                xPadding: 12,
                                yPadding: 8,
                                cornerRadius: 3,
                                displayColors: false,
                            },
                        }
                    });
                });
            });

            function ChartSettings() {
                Chart.pluginService.register({
                    afterUpdate: function (chart) {
                        if (chart.config.options.elements.center) {
                            var helpers = Chart.helpers;
                            var centerConfig = chart.config.options.elements.center;
                            var globalConfig = Chart.defaults.global;
                            var ctx = chart.chart.ctx;

                            var fontStyle = helpers.getValueOrDefault(centerConfig.fontStyle, globalConfig.defaultFontStyle);
                            var fontFamily = helpers.getValueOrDefault(centerConfig.fontFamily, globalConfig.defaultFontFamily);

                            if (centerConfig.fontSize)
                                var fontSize = centerConfig.fontSize;
                            // figure out the best font size, if one is not specified
                            else {
                                ctx.save();
                                var fontSize = helpers.getValueOrDefault(centerConfig.minFontSize, 1);
                                var maxFontSize = helpers.getValueOrDefault(centerConfig.maxFontSize, 256);
                                var maxText = helpers.getValueOrDefault(centerConfig.maxText, centerConfig.text);

                                do {
                                    ctx.font = helpers.fontString(fontSize, fontStyle, fontFamily);
                                    var textWidth = ctx.measureText(maxText).width;

                                    // check if it fits, is within configured limits and that we are not simply toggling back and forth
                                    if (textWidth < chart.innerRadius * 2 && fontSize < maxFontSize)
                                        fontSize += 1;
                                    else {
                                        // reverse last step
                                        fontSize -= 1;
                                        break;
                                    }
                                } while (true)
                                ctx.restore();
                            }

                            // save properties
                            chart.center = {
                                font: helpers.fontString(fontSize, fontStyle, fontFamily),
                                fillStyle: helpers.getValueOrDefault(centerConfig.fontColor, globalConfig.defaultFontColor)
                            };
                        }
                    },
                    afterDraw: function (chart) {
                        if (chart.center) {
                            var centerConfig = chart.config.options.elements.center;
                            var ctx = chart.chart.ctx;

                            ctx.save();
                            ctx.font = chart.center.font;
                            ctx.fillStyle = chart.center.fillStyle;
                            ctx.textAlign = 'center';
                            ctx.textBaseline = 'middle';
                            var centerX = (chart.chartArea.left + chart.chartArea.right) / 2;
                            var centerY = (chart.chartArea.top + chart.chartArea.bottom) / 2;
                            ctx.fillText(centerConfig.text, centerX, centerY);
                            ctx.restore();
                        }
                    }
                })
            };

            ///RADAR CHART
             var radarChart = document.getElementById("radarChart"), myRadarChart;


             @{
                 var enumValues = new[] {
                        CompentencesTestRuleKeysEnum.RE,
                        CompentencesTestRuleKeysEnum.GE,
                        CompentencesTestRuleKeysEnum.LI,
                        CompentencesTestRuleKeysEnum.ML,
                        CompentencesTestRuleKeysEnum.IS,
                        CompentencesTestRuleKeysEnum.AU,
                        CompentencesTestRuleKeysEnum.AR,
                        CompentencesTestRuleKeysEnum.CO,
                };

                 var radarChartLabels = "'" + PageLiteralsHelper.GetLiteral("RESPONSABILIDAD_TEST_COMPETENCES", PageId, portalConfig) + "'," +
                                         "'" + PageLiteralsHelper.GetLiteral("EFICACIA_TEST_COMPETENCES", PageId, portalConfig) + "'," +
                                         "'" + PageLiteralsHelper.GetLiteral("LIDERAZGO_TEST_COMPETENCES", PageId, portalConfig) + "'," +
                                         "'" + PageLiteralsHelper.GetLiteral("MOTIVACION_TEST_COMPETENCES", PageId, portalConfig) + "'," +
                                         "'" + PageLiteralsHelper.GetLiteral("INTELIGENCIA_TEST_COMPETENCES", PageId, portalConfig) + "'," +
                                         "'" + PageLiteralsHelper.GetLiteral("AUTONOMIA_TEST_COMPETENCES", PageId, portalConfig) + "'," +
                                         "'" + PageLiteralsHelper.GetLiteral("ARBITRAJE_TEST_COMPETENCES", PageId, portalConfig) + "'," +
                                         "'" + PageLiteralsHelper.GetLiteral("COOPERACION_TEST_COMPETENCES", PageId, portalConfig) + "'";

                 if (Model.Candidate.HasTalentView3D)
                 {
                     enumValues = new[] {
                        CompentencesTestRuleKeysEnum.RC,
                        CompentencesTestRuleKeysEnum.TD,
                        CompentencesTestRuleKeysEnum.PC,
                        CompentencesTestRuleKeysEnum.RI,
                        CompentencesTestRuleKeysEnum.AD,
                        CompentencesTestRuleKeysEnum.CA,
                        CompentencesTestRuleKeysEnum.LI,
                        CompentencesTestRuleKeysEnum.TE,
                    };

                     radarChartLabels = "'" + PageLiteralsHelper.GetLiteral("RESOLUCION_TEST_COMPETENCES", PageId, portalConfig) + "'," +
                                         "'" + PageLiteralsHelper.GetLiteral("DECISIONES_TEST_COMPETENCES", PageId, portalConfig) + "'," +
                                         "'" + PageLiteralsHelper.GetLiteral("PENSAMIENTO_TEST_COMPETENCES", PageId, portalConfig) + "'," +
                                         "'" + PageLiteralsHelper.GetLiteral("RESILENCIA_TEST_COMPETENCES", PageId, portalConfig) + "'," +
                                         "'" + PageLiteralsHelper.GetLiteral("ADAPTACION_TEST_COMPETENCES", PageId, portalConfig) + "'," +
                                         "'" + PageLiteralsHelper.GetLiteral("CAPACIDAD_TEST_COMPETENCES", PageId, portalConfig) + "'," +
                                         "'" + PageLiteralsHelper.GetLiteral("LIDERAZGO_TEST_COMPETENCES", PageId, portalConfig) + "'," +
                                         "'" + PageLiteralsHelper.GetLiteral("TRABAJO_TEST_COMPETENCES", PageId, portalConfig) + "'";
                 }
            }

            if (radarChart) {
                var hasTalentView = "@Model.Candidate.HasTalentView3D";
                var radarColor = hasTalentView != "False" ? "#005DA9" : "#91b54e";
                var radarColorAlpha = hasTalentView != "False" ? "#005DA955" : "#91b54e55";

                myRadarChart = new Chart(radarChart, {
                    type: 'radar',
                    data: {
                        labels: [@(Html.Raw(radarChartLabels))],
                        datasets: [
                            {
                                backgroundColor: radarColorAlpha,
                                borderColor: radarColor,
                                borderWidth: '2',
                                data: [
                                    @{

                                        var competencies = enumValues.Select(e =>
                                        {
                                            var competence = Model.Candidate.Cv.CompetenceList.FirstOrDefault(x => string.Equals(x.Key, e.ToString()));
                                            return (competence == null ? 0 : competence.Result).ToString();
                                        }).Aggregate((a, b) => a + ", " + b);
                                     }
                                        @(competencies)
                                    ],
                                pointBackgroundColor: radarColor,
                                pointBorderColor: radarColor,
                                pointBorderWidth: '2',
                            }
                        ]
                    },
                    options: {
                        legend: { display: false },
                        maintainAspectRatio: false,
                        scale: {
                            ticks: {
                                fontColor: '#a0a0a0',
                                fontSize: 11,
                                max: 100,
                                maxTicksLimit: 5,
                                min: 0,
                            },
                            pointLabels: {
                                fontSize: 12,
                            },
                        },
                        tooltips: {
                            displayColors: false,
                            enabled: 1,
                        },
                    }
                });
            }

        </script>
    }
}