@model Redarbor.Company.WebUI.Models.Home.HomeDataModel
@using Redarbor.Company.WebUI.Helpers
@using Redarbor.Common.Entities.Enums
@using Redarbor.Master.Entities.Enums
@using System.Text.RegularExpressions;

@{
    Layout = "~/Views/Shared/Layouts/_LayoutHomeMobile.cshtml";
    short pageId = (short)PageEnum.HomeMasterCompany;
    short pageIdHome = (short)PageEnum.HomePublica;
    var portalConfig = PortalConfigHelper.GetPortalConfiguration();
}

@section MetaSection{
    <meta name="description" content="@PageLiteralsHelper.GetLiteral("META_DESCRIPTION_PRODUCT_AND_SERVICES", pageIdHome, portalConfig)" />
    <meta name="title" content="@PageLiteralsHelper.GetLiteral("META_TITLE_PRODUCT_AND_SERVICES", pageIdHome, portalConfig)" />
}

<main>

    <div class="menu_switch posSticky top0">
        <nav>
            <a href="@Url.Action("","Home")">@PageLiteralsHelper.GetLiteral("MASTER_TABS_INICIO", pageIdHome, portalConfig)</a>
            <a href="@Url.Action("ProductsAndServices","Home")" class="sel">@PageLiteralsHelper.GetLiteral("MASTER_TABS_PRODUCT_SERVICE", pageIdHome, portalConfig)</a>
            <a href="@Url.Action("About","Home")">@PageLiteralsHelper.GetLiteral("MASTER_TABS_ABOUT", pageIdHome, portalConfig)</a>
        </nav>
    </div>

    <div class="box">
        <h1 class="fs30 fwB lh1_2 tc mb30">@PageLiteralsHelper.GetLiteral("LIT_FIND_OUT_SERVICE", pageIdHome, portalConfig)</h1>
        <h2 class="fs18 tc mb30">@PageLiteralsHelper.GetLiteral("LIT_PRODUCT_ADAPTED", pageIdHome, portalConfig)</h2>
        <p class="fs16 tc mb15">@PageLiteralsHelper.GetLiteral("LIT_PUBLISH_NOW", pageIdHome, portalConfig)</p>
        <a href="@Url.Action("", "Register")" class="b_primary mbB">@PageLiteralsHelper.GetLiteral("LIT_PUBLISH_OFFER", pageIdHome, portalConfig)</a>
    </div>

    @if (Model.ProductsAndServicesProducts.Count == 3)
    {
        <div class="box bb0 bg_brand_light tc">
            <p class="fs30 lh1_2 pl25 pr25 mt15 mb15">@Html.Raw(PageLiteralsHelper.GetLiteral("LIT_SERVICES_GROW", pageIdHome, portalConfig))</p>

            <p class="fs18">@Html.Raw(PageLiteralsHelper.GetLiteral("LIT_REGISTER_CONTRACT", pageIdHome, portalConfig))</p>

            <ul class="mt30">
                @foreach (var product in Model.ProductsAndServicesProducts)
                {
                    var productName = product.CustomLiterals.GetValueOrDefault("Name");
                    var productClaim = product.CustomLiterals.GetValueOrDefault("Claim");
                    var productPrice = (product.HasPromotion
                        ? product.LiteralShowTotalPromotion : product.LiteralShowUnit).TrimEnd('*');

                    <li class="box_border mb15 bAll_promo posRel">
                        @if ((ProductSubGroupsEnum)product.SubGroupId == ProductSubGroupsEnum.Advanced)
                        {
                            <span class="tag tag_promo fwB">@PageLiteralsHelper.GetLiteral("LIT_MAS_POPULAR", pageIdHome, portalConfig)</span>
                        }

                        <p class="fwB fs24 mb5">@productName</p>
                        <p class="fs16 tc">@productClaim</p>

                        <ul class="tl mt30 mb30">
                            @foreach (var feature in product.FeaturesDescriptions)
                            {
                                <li class="cols mt10">
                                    <div class="vt"><span class="icon @(feature.Available == 1 ? "i_tick_ok" : "i_tick_ko" ) mr10"></span></div>
                                    <span class="w100 @(feature.Available == 1 ? "fwB" : "" )">@feature.Literal</span>
                                </li>
                            }

                        </ul>

                        <div class="fc_aux_light fs14 mt10 mbB">
                            @PageLiteralsHelper.GetLiteral("LIT_PRECIO_DESDE", pageIdHome, portalConfig)
                            <span class="fs30 fwB fc_base mr5 ml5">@productPrice</span>
                            @PageLiteralsHelper.GetLiteral("LIT_POROFERTA", pageId, portalConfig) @PageLiteralsHelper.GetLiteral("LIT_MAS_IVA", pageIdHome, portalConfig)
                        </div>

                        <a class="b_primary mb15" href="@Url.Action("", "Register")">@PageLiteralsHelper.GetLiteral("LIT_BUTTON_TEXT", pageIdHome, portalConfig)</a>


                    </li>
                }
            </ul>
            @if (CompanyHelper.HasRates(portalConfig.countryId))
            {
                <p class="fc_aux fs12 tc">@PageLiteralsHelper.GetLiteral("LIT_WITHOUT_VAT", pageId, portalConfig) @CompanyHelper.GetRateLiteral(portalConfig.countryId)</p>
            }
        </div>
    }

    <div class="box">
        @if (portalConfig.AEPortalConfig.IsPortalWithAssignedSalesTeam)
        {
            <p class="fs30 lh1_2 mtB">@Html.Raw(PageLiteralsHelper.GetLiteral("LIT_SOL_GRANDES_EMPRESAS_CON_COMERCIALES", pageIdHome, portalConfig))</p>
            <div class="mt30 mb30 tc">
                <img width="140" src="@Url.Content(string.Format("{0}img/icon-soluciones_sherlock.svg", portalConfig.PrefixPathImage, portalConfig.SufixPathImage))" alt="SherlockHR" title="SherlockHR">
            </div>
            <p class="fs18">@PageLiteralsHelper.GetLiteral("LIT_NECESIDADES_CON_COMERCIALES", pageIdHome, portalConfig)</p>
            <a href='@portalConfig.AEPortalConfig.ExternalUrl/members/?Portal=@portalConfig.PortalId' target="_blank" class="b_primary mt30 mb15">@PageLiteralsHelper.GetLiteral("LIT_CONTACTAR_CON_COMERCIALES", pageIdHome, portalConfig)</a>
        }
        else
        {
            <p class="fs30 lh1_2 mtB">@PageLiteralsHelper.GetLiteral("LIT_SOL_GRANDES_EMPRESAS", pageIdHome, portalConfig)</p>
            <div class="mt30 mb30 tc">
                <img width="140" src="@Url.Content(string.Format("{0}img/icon-soluciones_sherlock.svg", portalConfig.PrefixPathImage, portalConfig.SufixPathImage))" alt="SherlockHR" title="SherlockHR">
            </div>
            <p class="fs18">@PageLiteralsHelper.GetLiteral("LIT_NECESIDADES", pageIdHome, portalConfig)</p>
            <a href="@Url.Action("Index","Contact")" target="_blank" class="b_primary mt30 mb15">@PageLiteralsHelper.GetLiteral("LIT_CONTACTAR", pageIdHome, portalConfig)</a>
        }
    </div>
</main>

@{ Html.RenderPartial("_ProductContentFeatures", Model.LandingProductsPopUpDataModel); }
