@model Redarbor.Company.WebUI.Models.Company.Home.HomeFeaturesPostPublishDataModel
@using Redarbor.Company.WebUI.Helpers.Html
@using Redarbor.Common.Entities.Enums
@using Redarbor.Company.WebUI.Helpers
@using Redarbor.Master.Entities.Enums

@{
    Layout = "~/Views/Shared/Layouts/_LayoutMaster.cshtml";
    int pageId = Html.SetCurrentPage((int)PageEnum.AddOfferFeaturesPage);
    int pageLiteralId = Html.SetCurrentPage((int)PageEnum.LitAddOfferFeaturesPage);
    var portalConfig = PortalConfigHelper.GetPortalConfiguration();

    var hiddenCompanyContainerClass = Model.Offer.HiddenCompany == 0 ? "display:none" : string.Empty;
    var contactMailDisplayClass = Model.Offer.show_contact_email == 0 ? "display:none" : string.Empty;
    var contactPhoneDisplayClass = Model.Offer.show_phone_offer == 0 ? "display:none" : string.Empty;
    var contactAddressDisplayClass = Model.Offer.show_contact_address == 0 ? "display:none" : string.Empty;
    string guidJs = Guid.NewGuid().ToString();
    var hasFremmiumToConsume = Model.PostPublishProductsBoxes.Any(x => x.SubGroup == (int)ProductSubGroupsEnum.Freemium && x.AvailableUnits > 0);
}

@section TitleSection{
    <title>Visibilidad del aviso</title>
}

@section CustomScriptsSection{
    @Scripts.Render("~/bundles/js/featurespostpublish")
}

<div class="container">
    <section class="cm-12 breadinfo">
        <div class="cm-12 breadcrumb">
            <ol class="breadcrumb">
                <li>
                    <a href="@Url.Action("Index", "Company")">@PageLiteralsHelper.GetLiteral("BREADCRUMB_INICIO", pageLiteralId, portalConfig)</a>
                </li>
                <li class="active">
                    @Html.Raw(PageLiteralsHelper.GetLiteral("BREADCRUMB_PUBL_OFERTA", pageLiteralId, portalConfig))
                </li>
            </ol>
        </div>
    </section>

    @using (Html.BeginForm("Index", "CompanyFeaturesPostPublish", FormMethod.Post, new { id = "publishOffer" }))
    {
        @Html.AntiForgeryToken()
        <article class="formulario_oferta">
            <article class="cm-9 formulario">
                <header class="steps">
                    <div class="formulario_intro">
                        @if (Model.IsUpdateOffer)
                        {
                            <h1>@PageLiteralsHelper.GetLiteral("LIT_TITLE_EDIT", pageLiteralId, portalConfig) "@Model.NameOffer"</h1>
                        }
                        else
                        {
                            <h1>@PageLiteralsHelper.GetLiteral("LIT_TITLE_PUBLISH", pageLiteralId, portalConfig) "@Model.NameOffer"</h1>
                        }
                        <span class="info_tag">@PageLiteralsHelper.GetLiteral("LIT_STEP", pageLiteralId, portalConfig)</span>
                    </div>
                    <div class="formulario_back">
                        <a href="@Url.Action("Index", "CompanyOffersPostPublish", new { idEditOffer = Model.OfferIdEncrypted })">@PageLiteralsHelper.GetLiteral("LIT_RETURN_STEP", pageLiteralId, portalConfig)</a>
                    </div>
                </header>

                <div class="forms">

                    <div class="box_formulario visualizar">
                        <h2>@PageLiteralsHelper.GetLiteral("LVISIBILIDAD_OFERTA", pageLiteralId, portalConfig)</h2>
                        <div class="box new">

                            <div class="offer_col_wrapper">
                                <div class="offer_col_options">

                                    <div class="info_options">

                                        @if (Model.ShowOfferExpressCheck)
                                        {
                                            <div class="offer_option">
                                                <div class="form_texto">
                                                    <div>
                                                        <span class="form_texto_info">@PageLiteralsHelper.GetLiteral("LIT_PUBLISH_EXPRESS_OFFER", pageLiteralId, portalConfig)</span>
                                                        <span class="info_tooltip">
                                                            <span class="bubble_tooltip bottom">
                                                                @PageLiteralsHelper.GetLiteral("LIT_EXPRESS_OFFER_TOOLTIP", pageLiteralId, portalConfig)
                                                            </span>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="form_inputs">
                                                    <div>
                                                        <div id="div_Cvs" class="form_texto">
                                                            <table id="rbExpressOffer">
                                                                <tbody>
                                                                    <tr>
                                                                        <td>
                                                                            <label>
                                                                                <input type="radio" value="1" id="Offer_Express" @(Model.DisableExpressFeature ? "disabled" : "") name="Offer.express" @(Model.IsExpressChecked ? "checked" : "")>
                                                                                <span>@PageLiteralsHelper.GetLiteral("LIT_SI", pageLiteralId, portalConfig)</span>
                                                                            </label>
                                                                        </td>
                                                                        <td>
                                                                            <label>
                                                                                <input type="radio" value="0" id="Offer_Express" @(Model.DisableExpressFeature ? "disabled" : "") name="Offer.express" @(!Model.IsExpressChecked ? "checked" : "")>
                                                                                <span>@PageLiteralsHelper.GetLiteral("LIT_NO", pageLiteralId, portalConfig)</span>
                                                                            </label>
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>

                                            </div>
                                        }

                                        @if (Model.ShowKillerQuestionsFeature)
                                        {
                                            <div class="offer_option @(Model.FreemiumProductHaveKQ?"hide":"")">
                                                <div class="form_texto">
                                                    <div>@PageLiteralsHelper.GetLiteral("LIT_KQ", pageLiteralId, portalConfig):</div>
                                                </div>
                                                <div class="form_inputs">
                                                    <div>
                                                        <div id="div_OD" class="form_texto">
                                                            <table id="rbKQOffer">
                                                                <tbody>
                                                                    <tr>
                                                                        <td>
                                                                            <label>
                                                                                @if (Model.DisableKillerQuestionsFeature)
                                                                                {
                                                                                    @Html.RadioButtonFor(m => m.Offer.has_killer_questions, 1, new { @disabled = "disabled" })
                                                                                }
                                                                                else
                                                                                {
                                                                                    @Html.RadioButtonFor(m => m.Offer.has_killer_questions, 1, new { @class = "changeFeature" })
                                                                                }
                                                                                <span>@PageLiteralsHelper.GetLiteral("LIT_SI", pageLiteralId, portalConfig)</span>
                                                                            </label>
                                                                        </td>
                                                                        <td>
                                                                            <label>
                                                                                @if (Model.DisableKillerQuestionsFeature)
                                                                                {
                                                                                    @Html.RadioButtonFor(m => m.Offer.has_killer_questions, 0, new { @disabled = "disabled" })
                                                                                }
                                                                                else
                                                                                {
                                                                                    @Html.RadioButtonFor(m => m.Offer.has_killer_questions, 0, new { @class = "changeFeature" })
                                                                                }
                                                                                <span>@PageLiteralsHelper.GetLiteral("LIT_NO", pageLiteralId, portalConfig)</span>
                                                                            </label>
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        }

                                        @if (portalConfig.AEPortalConfig.AccessToAllCvsOnPostPublishOption)
                                        {
                                            <div class="offer_option">
                                                <div class="form_texto">
                                                    <div>
                                                        <span class="form_texto_info">@PageLiteralsHelper.GetLiteral("LIT_ESSENTIAL_CVS", pageLiteralId, portalConfig)</span>
                                                        <span class="info_tooltip">
                                                            <span class="bubble_tooltip bottom">
                                                                @PageLiteralsHelper.GetLiteral("LIT_LIT_ESSENTIAL_CVS_DESCRIPTION", pageLiteralId, portalConfig)
                                                            </span>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="form_inputs">
                                                    <div>
                                                        <div id="div_Cvs" class="form_texto">
                                                            <table id="rbEssentialOffer">
                                                                <tbody>
                                                                    <tr>
                                                                        <td>
                                                                            <label>
                                                                                <input class="changeFeature" type="radio" value="1" id="Offer_Essential" name="Offer.essential" @(Model.IsEssentialChecked || Model.AnyCheckboxIsChecked ? "checked" : "")>
                                                                                <span>@PageLiteralsHelper.GetLiteral("LIT_SI", pageLiteralId, portalConfig)</span>
                                                                            </label>
                                                                        </td>
                                                                        <td>
                                                                            <label>
                                                                                <input class="changeFeature" type="radio" value="0" id="Offer_Essential" name="Offer.essential"
                                                                                       @(!Model.IsEssentialChecked && !Model.AnyCheckboxIsChecked ? "checked" : "") @(Model.AnyCheckboxIsChecked || Model.Offer.idproduct > 0 || !hasFremmiumToConsume ? "disabled" : "")>
                                                                                <span>@PageLiteralsHelper.GetLiteral("LIT_NO", pageLiteralId, portalConfig)</span>
                                                                            </label>
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>

                                            </div>
                                        }

                                        <div class="offer_option">
                                            <div class="form_texto">
                                                <div>
                                                    <span class="form_texto_info">@PageLiteralsHelper.GetLiteral("FORM_OFERTA_DESTACADA", pageLiteralId, portalConfig)</span>
                                                    <span class="info_tooltip">
                                                        <span class="bubble_tooltip bottom">
                                                            @PageLiteralsHelper.GetLiteral("FORM_OFERTA_DESTACADA_DESC", pageLiteralId, portalConfig)
                                                        </span>
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="form_inputs">
                                                <div>
                                                    <div id="div_OD" class="form_texto">
                                                        <table id="rbHighlightedOffer">
                                                            <tbody>
                                                                <tr>
                                                                    <td>
                                                                        <label>
                                                                            @if (Model.DisableHighlightFeature)
                                                                            {
                                                                                @Html.RadioButtonFor(m => m.Offer.ishighlighted, 1, new { @disabled = "disabled" })
                                                                            }
                                                                            else
                                                                            {
                                                                                @Html.RadioButtonFor(m => m.Offer.ishighlighted, 1, new { @class = "changeFeature" })
                                                                            }
                                                                            <span>@PageLiteralsHelper.GetLiteral("LIT_SI", pageLiteralId, portalConfig)</span>
                                                                        </label>
                                                                    </td>
                                                                    <td>
                                                                        <label>
                                                                            @if (Model.DisableHighlightFeature)
                                                                            {
                                                                                @Html.RadioButtonFor(m => m.Offer.ishighlighted, 0, new { @disabled = "disabled" })
                                                                            }
                                                                            else
                                                                            {
                                                                                @Html.RadioButtonFor(m => m.Offer.ishighlighted, 0, new { @class = "changeFeature" })
                                                                            }
                                                                            <span>@PageLiteralsHelper.GetLiteral("LIT_NO", pageLiteralId, portalConfig)</span>
                                                                        </label>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="offer_option">
                                            <div class="form_texto">
                                                <div>
                                                    <span class="form_texto_info">@PageLiteralsHelper.GetLiteral("FORM_OFERTA_URGENTE", pageLiteralId, portalConfig)</span>
                                                    <span class="info_tooltip">
                                                        <span class="bubble_tooltip bottom">
                                                            @PageLiteralsHelper.GetLiteral("FORM_OFERTA_URGENTE_DESC", pageLiteralId, portalConfig)
                                                        </span>
                                                    </span>
                                                </div>
                                            </div>

                                            <div class="form_inputs">
                                                <div>
                                                    <div id="div_OU" class="form_texto">
                                                        <table id="rbUrgentOffer">
                                                            <tbody>
                                                                <tr>
                                                                    <td>
                                                                        <label>
                                                                            @if (Model.DisableUrgentFeature)
                                                                            {
                                                                                @Html.RadioButtonFor(m => m.Offer.isurgent, 1, new { @disabled = "disabled" })
                                                                            }
                                                                            else
                                                                            {
                                                                                @Html.RadioButtonFor(m => m.Offer.isurgent, 1, new { @class = "changeFeature" })
                                                                            }
                                                                            <span>@PageLiteralsHelper.GetLiteral("LIT_SI", pageLiteralId, portalConfig)</span>
                                                                        </label>
                                                                    </td>
                                                                    <td>
                                                                        <label>
                                                                            @if (Model.DisableUrgentFeature)
                                                                            {
                                                                                @Html.RadioButtonFor(m => m.Offer.isurgent, 0, new { @disabled = "disabled" })
                                                                            }
                                                                            else
                                                                            {
                                                                                @Html.RadioButtonFor(m => m.Offer.isurgent, 0, new { @class = "changeFeature" })
                                                                            }
                                                                            <span>@PageLiteralsHelper.GetLiteral("LIT_NO", pageLiteralId, portalConfig)</span>
                                                                        </label>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="offer_option">
                                            <div class="form_texto">
                                                <div>
                                                    <span class="form_texto_info">@PageLiteralsHelper.GetLiteral("FORM_OFERTA_CIEGA", pageLiteralId, portalConfig)</span>
                                                    <span class="info_tooltip">
                                                        <span class="bubble_tooltip bottom">
                                                            @PageLiteralsHelper.GetLiteral("FORM_OFERTA_CIEGA_DESC", pageLiteralId, portalConfig)
                                                        </span>
                                                    </span>
                                                </div>
                                            </div>

                                            <div class="form_inputs">
                                                <div>
                                                    <div id="div_OC" class="form_texto">
                                                        <table id="rbHiddenCompany" class="dev_Freemium">
                                                            <tbody>
                                                                <tr>
                                                                    <td>
                                                                        <label>
                                                                            @if (Model.DisableHiddenCompanyFeature)
                                                                            {
                                                                                @Html.RadioButtonFor(m => m.Offer.HiddenCompany, 1, new { @disabled = "disabled" })
                                                                            }
                                                                            else
                                                                            {
                                                                                @Html.RadioButtonFor(m => m.Offer.HiddenCompany, 1, new { @class = "changeFeature" })
                                                                            }
                                                                            <span>@PageLiteralsHelper.GetLiteral("LIT_SI", pageLiteralId, portalConfig)</span>
                                                                        </label>
                                                                    </td>
                                                                    <td>
                                                                        <label>
                                                                            @if (Model.DisableHiddenCompanyFeature)
                                                                            {
                                                                                @Html.RadioButtonFor(m => m.Offer.HiddenCompany, 0, new { @disabled = "disabled" })
                                                                            }
                                                                            else
                                                                            {
                                                                                @Html.RadioButtonFor(m => m.Offer.HiddenCompany, 0, new { @class = "changeFeature" })
                                                                            }
                                                                            <span>@PageLiteralsHelper.GetLiteral("LIT_NO", pageLiteralId, portalConfig)</span>
                                                                        </label>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>

                                                <div id="div_hidden_name_corporativo" class="form_nombre_empresa" style="@hiddenCompanyContainerClass">
                                                    <p>@PageLiteralsHelper.GetLiteral("FORM_NOMBRE_EMPRESA_SHOW", pageLiteralId, portalConfig)</p>
                                                    <div class="form_inputs containererror">
                                                        @Html.ValidationMessageFor(m => m.Offer.HiddenCompanyName, "", new { @class = "pos_rel fl field-validation-error w_100" })
                                                    </div>
                                                    @Html.TextBoxFor(m => m.Offer.HiddenCompanyName, new { @class = "cm-12" })
                                                </div>
                                            </div>
                                        </div>



                                        @if (portalConfig.offer_show_email_form)
                                        {
                                            <div class="offer_option">
                                                <div class="form_texto">
                                                    <div>@PageLiteralsHelper.GetLiteral("FORM_MOSTRAR_EMAIL_CONTACTO", pageLiteralId, portalConfig)</div>
                                                </div>
                                                <div class="form_inputs">
                                                    <table id="rbEmailContact">
                                                        <tbody>
                                                            <tr>
                                                                <td>
                                                                    <label>
                                                                        @if (Model.DisableShowEmailContactFeature)
                                                                        {
                                                                            @Html.RadioButtonFor(m => m.Offer.show_contact_email, 1, new { @disabled = "disabled" })
                                                                        }
                                                                        else
                                                                        {
                                                                            @Html.RadioButtonFor(m => m.Offer.show_contact_email, 1, new { @class = "changeFeature" })
                                                                        }
                                                                        <span>@PageLiteralsHelper.GetLiteral("LIT_SI", pageLiteralId, portalConfig)</span>
                                                                    </label>
                                                                </td>
                                                                <td>
                                                                    <label>
                                                                        @if (Model.DisableShowEmailContactFeature)
                                                                        {
                                                                            @Html.RadioButtonFor(m => m.Offer.show_contact_email, 0, new { @disabled = "disabled" })
                                                                        }
                                                                        else
                                                                        {
                                                                            @Html.RadioButtonFor(m => m.Offer.show_contact_email, 0, new { @class = "changeFeature" })
                                                                        }
                                                                        <span>@PageLiteralsHelper.GetLiteral("LIT_NO", pageLiteralId, portalConfig)</span>
                                                                    </label>
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                    <div id="form_contact_email" style="@contactMailDisplayClass">
                                                        <p>@PageLiteralsHelper.GetLiteral("FORM_EMAIL_CONTACTO", pageLiteralId, portalConfig)</p>
                                                        <div class="form_inputs containererror">
                                                            @Html.ValidationMessageFor(m => m.Offer.contact_email, "", new { @class = "pos_rel fl field-validation-error w_100" })
                                                        </div>
                                                        @Html.TextBoxFor(m => m.Offer.contact_email, new { @class = "cm-12" })
                                                    </div>
                                                </div>
                                            </div>
                                        }

                                        @if (portalConfig.offer_show_phone_form)
                                        {
                                            <div class="offer_option">
                                                <div class="form_texto">
                                                    <div>@PageLiteralsHelper.GetLiteral("FORM_MOSTRAR_TELEFONO_CONTACTO", pageLiteralId, portalConfig)</div>
                                                </div>
                                                <div class="form_inputs">
                                                    <table id="rbPhoneContact">
                                                        <tbody>
                                                            <tr>
                                                                <td>
                                                                    <label>
                                                                        @if (Model.DisableShowPhoneContactFeature)
                                                                        {
                                                                            @Html.RadioButtonFor(m => m.Offer.show_phone_offer, 1, new { @disabled = "disabled" })
                                                                        }
                                                                        else
                                                                        {
                                                                            @Html.RadioButtonFor(m => m.Offer.show_phone_offer, 1, new { @class = "changeFeature" })
                                                                        }
                                                                        <span>@PageLiteralsHelper.GetLiteral("LIT_SI", pageLiteralId, portalConfig)</span>
                                                                    </label>
                                                                </td>
                                                                <td>
                                                                    <label>
                                                                        @if (Model.DisableShowPhoneContactFeature)
                                                                        {
                                                                            @Html.RadioButtonFor(m => m.Offer.show_phone_offer, 0, new { @disabled = "disabled" })
                                                                        }
                                                                        else
                                                                        {
                                                                            @Html.RadioButtonFor(m => m.Offer.show_phone_offer, 0, new { @class = "changeFeature" })
                                                                        }
                                                                        <span>@PageLiteralsHelper.GetLiteral("LIT_NO", pageLiteralId, portalConfig)</span>
                                                                    </label>
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                    <div id="form_contact_phone" style="@contactPhoneDisplayClass">
                                                        <p>@PageLiteralsHelper.GetLiteral("FORM_TELEFONO_CONTACTO", pageLiteralId, portalConfig)</p>
                                                        <div class="form_inputs containererror">
                                                            @Html.ValidationMessageFor(m => m.Offer.contact_phone, "", new { @class = "pos_rel fl field-validation-error w_100" })
                                                        </div>
                                                        @Html.TextBoxFor(m => m.Offer.contact_phone, new { @class = "cm-12" })
                                                    </div>
                                                </div>
                                            </div>
                                        }

                                        @if (portalConfig.offer_show_address_form)
                                        {
                                            <div class="offer_option">
                                                <div class="form_texto">
                                                    <div>@PageLiteralsHelper.GetLiteral("FORM_MOSTRAR_DIRECCION_CONTACTO", pageLiteralId, portalConfig)</div>
                                                </div>
                                                <div class="form_inputs">
                                                    <table id="rbAdressContact">
                                                        <tbody>
                                                            <tr>
                                                                <td>
                                                                    <label>
                                                                        @if (Model.DisableShowAddressContactFeature)
                                                                        {
                                                                            @Html.RadioButtonFor(m => m.Offer.show_contact_address, 1, new { @disabled = "disabled" })
                                                                        }
                                                                        else
                                                                        {
                                                                            @Html.RadioButtonFor(m => m.Offer.show_contact_address, 1, new { @class = "changeFeature" })
                                                                        }
                                                                        <span>@PageLiteralsHelper.GetLiteral("LIT_SI", pageLiteralId, portalConfig)</span>
                                                                    </label>
                                                                </td>
                                                                <td>
                                                                    <label>
                                                                        @if (Model.DisableShowAddressContactFeature)
                                                                        {
                                                                            @Html.RadioButtonFor(m => m.Offer.show_contact_address, 0, new { @disabled = "disabled" })
                                                                        }
                                                                        else
                                                                        {
                                                                            @Html.RadioButtonFor(m => m.Offer.show_contact_address, 0, new { @class = "changeFeature" })
                                                                        }
                                                                        <span>@PageLiteralsHelper.GetLiteral("LIT_NO", pageLiteralId, portalConfig)</span>
                                                                    </label>
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                    <div id="form_contact_address" style="@contactAddressDisplayClass">
                                                        <p>@PageLiteralsHelper.GetLiteral("FORM_DIRECCION_CONTACTO", pageLiteralId, portalConfig)</p>
                                                        <div class="form_inputs containererror">
                                                            @Html.ValidationMessageFor(m => m.Offer.contact_address, "", new { @class = "pos_rel fl field-validation-error w_100" })
                                                        </div>
                                                        @Html.TextBoxFor(m => m.Offer.contact_address, new { @class = "cm-12" })
                                                    </div>
                                                </div>
                                            </div>
                                        }
                                        @if (Model.HasOfferMatchesNotification)
                                        {
                                            <div class="offer_option">
                                                <div class="form_texto">
                                                    <div>
                                                        <span class="form_texto_info">@PageLiteralsHelper.GetLiteral("FORM_OFERTA_NOTIFICATION", pageLiteralId, portalConfig)</span>
                                                        <span class="info_tooltip">
                                                            <span class="bubble_tooltip bottom">
                                                                @PageLiteralsHelper.GetLiteral("FORM_OFERTA_NOTIFICATION_INFO", pageLiteralId, portalConfig)
                                                            </span>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="form_inputs">
                                                    <div class="field_select_links small dIB mb0">
                                                        <div>
                                                            <div class="field_select w50">
                                                                @Html.Hidden("matchSummaryNotificationTypes", (int)MatchSummaryNotificationType.None)
                                                                @Html.DropDownListFor(m => m.IdMatchSummaryNotificationType, Model.MatchSummaryNotificationTypes, new { @class = "cm-12" })

                                                            </div>
                                                            @Html.ValidationMessageFor(m => m.IdMatchSummaryNotificationType, "")
                                                        </div>

                                                    </div>
                                                </div>
                                            </div>
                                        }
                                    </div>

                                    <div class="info_button">
                                        @if (!Model.IsUpdateOffer)
                                        {
                                            <div class="fs18 tc mt10">
                                                <span id="footer_info"></span>
                                            </div>
                                        }
                                        <div class="submitbtn">
                                            @if (Model.IsUpdateOffer)
                                            {
                                                <input type="submit" class="submit_n js-processing" value="@PageLiteralsHelper.GetLiteral("LIT_UPDATE", pageLiteralId, portalConfig)" />
                                            }
                                            else
                                            {
                                                <input type="button" id="btCp" class="submit_n js-processing" value="@PageLiteralsHelper.GetLiteral("LIT_PUBLISH", pageLiteralId, portalConfig)" />
                                            }
                                        </div>
                                    </div>

                                </div>
                                <div class="offer_col_menu">
                                    <p>@PageLiteralsHelper.GetLiteral("LIT_AVAILABLE_OFFERS_TITLE", pageLiteralId, portalConfig)</p>

                                    @if (Model.ShowPostPublishProductsBoxes)
                                    {
                                        <div class="offer_type">

                                            @foreach (var productBox in Model.PostPublishProductsBoxes)
                                            {
                                                <div class="offer_item @productBox.ClassName @(Model.SubGroupToConsume.Split(',').Contains(productBox.SubGroup.ToString())?" sel":"")" id="<EMAIL>">
                                                    <h3>@productBox.ProductName</h3>
                                                    @if (portalConfig.AEPortalConfig.HideBasicOffersNum &&
                                                        productBox.SubGroup == (int)ProductSubGroupsEnum.Freemium &&
                                                        productBox.AvailableUnits > 0)
                                                    {
                                                        <p>@PageLiteralsHelper.GetLiteral("LIT_AVAILABLE_OFFERS_CAP", pageLiteralId, portalConfig)</p>
                                                    }
                                                    else if (productBox.IsUnlimited)
                                                    {
                                                        <p>@PageLiteralsHelper.GetLiteral("LIT_FREE_UNLIMITED", pageLiteralId, portalConfig)</p>
                                                    }
                                                    else
                                                    {
                                                        <p>@productBox.AvailableUnits @PageLiteralsHelper.GetLiteral("LIT_AVAILABLE_OFFERS", pageLiteralId, portalConfig)</p>
                                                    }
                                                </div>
                                            }

                                        </div>
                                    }
                                    else
                                    {
                                        <div class="offer_type">
                                            @foreach (var productBox in Model.PostPublishProductsBoxes)
                                            {
                                                if (Model.OfferSubgroup == productBox.SubGroup)
                                                {
                                                    <div class="offer_item sel" id="<EMAIL>">
                                                        <h3>@productBox.ProductName</h3>
                                                    </div>
                                                }
                                            }
                                        </div>
                                    }

                                </div>
                            </div>

                        </div>

                    </div>

                </div>
            </article>
        </article>

        @Html.HiddenFor(m => m.OfferIdEncrypted);
        @Html.HiddenFor(m => m.SubGroupToConsume);
        @Html.HiddenFor(o => o.Offer.PostalCodeSuggestText, new { Value = "0" })
        @Html.HiddenFor(x => x.HasOfferMatchesNotification);
    }
</div>

@Html.DisplayFor(p => p.PublishOfferPopUp, new
{
    IdPopUp = "publishOfferPop",
    IdBtnOk = "SubmitPage",
    IdMessage = "bodyMessage",
})

@if (!Model.IsUpdateOffer)
{
    @Html.DisplayFor(p => p.ConvertToCompletePopUp, new
    {
        IdPopUp = "goToContactPage",
        PrintConvertButton = (short)6
    })
}


@section CustomScriptsFooterSection{
    <script type="text/javascript">

        document.addEventListener('DOMContentLoaded', () => {
            $("#js-header_publishbtt").addClass("hide");
        });

        InitializeFeaturesPostPublish({
            hiddenCompanyName: '@(Model.Offer.HiddenCompanyName)',
            freemiumProductHaveKQ: '@(Model.FreemiumProductHaveKQ)'
        });

        function canPublish(result) {

            if (result == @((short)PostPublishShowPopUpsEnum.ShowPublishFreemiumPopUp)) {
                $('#bodyMessage').html('@(PageLiteralsHelper.GetLiteral("LIT_POPUP_MESSAGE_BASIC", pageLiteralId, portalConfig).Replace("{0}", PageLiteralsHelper.GetLiteral("LIT_BASIC", pageLiteralId, portalConfig)))');
                $("#SubGroupToConsume").val(@((short)ProductSubGroupsEnum.Freemium));
                $.blockUI({ message: $("#publishOfferPop") });
                return;
            }
            if (result == @((short)PostPublishShowPopUpsEnum.ShowPublishStandardPopUp)) {
                $('#bodyMessage').html('@(PageLiteralsHelper.GetLiteral("LIT_POPUP_MESSAGE", pageLiteralId, portalConfig).Replace("{0}", PageLiteralsHelper.GetLiteral("LIT_STANDARD", pageLiteralId, portalConfig)))');
                $("#SubGroupToConsume").val(@((short)ProductSubGroupsEnum.Standard));
                $.blockUI({ message: $("#publishOfferPop") });
                return;
            }
            if (result == @((short)PostPublishShowPopUpsEnum.ShowPublishAdvancedPopUp)) {
                $('#bodyMessage').html('@(PageLiteralsHelper.GetLiteral("LIT_POPUP_MESSAGE", pageLiteralId, portalConfig).Replace("{0}", PageLiteralsHelper.GetLiteral("LIT_ADVANCED", pageLiteralId, portalConfig)))');
                $("#SubGroupToConsume").val(@((short)ProductSubGroupsEnum.Advanced));
                $.blockUI({ message: $("#publishOfferPop") });
                return;
            }
            if (result == @((short)PostPublishShowPopUpsEnum.ShowPublishPremiumPopUp)) {
                $('#bodyMessage').html('@(PageLiteralsHelper.GetLiteral("LIT_POPUP_MESSAGE", pageLiteralId, portalConfig).Replace("{0}", PageLiteralsHelper.GetLiteral("LIT_PREMIUM", pageLiteralId, portalConfig)))');
                $("#SubGroupToConsume").val(@((short)ProductSubGroupsEnum.Premium));
                $.blockUI({ message: $("#publishOfferPop") });
                return;
            }
            if (result == @((short)PostPublishShowPopUpsEnum.ShowPublishExpressPopUp)) {
                $('#bodyMessage').html('@(PageLiteralsHelper.GetLiteral("LIT_POPUP_MESSAGE", pageLiteralId, portalConfig).Replace("{0}", "Express"))');
                $("#SubGroupToConsume").val(@((short)ProductSubGroupsEnum.Express));
                $.blockUI({ message: $("#publishOfferPop") });
                return;
            }
            if (result == @((short)PostPublishShowPopUpsEnum.ShowPublishCustomOfferPopUp)) {
                $('#bodyMessage').html('@(PageLiteralsHelper.GetLiteral("LIT_POPUP_MESSAGE", pageLiteralId, portalConfig).Replace("{0}", PageLiteralsHelper.GetLiteral("LIT_CUSTOM", pageLiteralId, portalConfig)))');
                $("#SubGroupToConsume").val(@((short)ProductSubGroupsEnum.Promo));
                $.blockUI({ message: $("#publishOfferPop") });
                return;
            }
            if (result == @((short)PostPublishShowPopUpsEnum.ShowGoToContactPage)) {
                $('.goToContactPage_txt').html('@Html.Raw(PageLiteralsHelper.GetLiteral("LIT_CONTACT_POPUP", pageLiteralId, portalConfig))');
                $.blockUI({ message: $("#goToContactPage") });
                return;
            }
        }

        function IsValidForm() {
            return $("#publishOffer").valid();
        }

        function markNecessaryProducts(result) {

            $("#subgroup_1").removeClass("sel");
            $("#subgroup_2").removeClass("sel");
            $("#subgroup_3").removeClass("sel");
            $("#subgroup_4").removeClass("sel");
            $("#subgroup_5").removeClass("sel");
            $("#subgroup_6").removeClass("sel");
            $("#subgroup_9").removeClass("sel");
            $("#btCp").off('click').on('click', function () {
                if (IsValidForm()) {
                    callAjax("canPublish");
                }
            });
            if (result == @((short)PostPublishShowPopUpsEnum.ShowPublishFreemiumPopUp)) {
                $('#footer_info').html('@Html.Raw(PageLiteralsHelper.GetLiteral("LIT_INFO_PUBLISH", pageLiteralId, portalConfig).Replace("{0}", PageLiteralsHelper.GetLiteral("LIT_BASIC", pageLiteralId, portalConfig)))');
                $("#subgroup_1").addClass("sel");
                $("#btCp").val('@PageLiteralsHelper.GetLiteral("LIT_PUBLISH", pageLiteralId, portalConfig)')
                return;
            }
            if (result == @((short)PostPublishShowPopUpsEnum.ShowGoToStandardPackCartPopUp)) {
                $('#footer_info').html('@Html.Raw(PageLiteralsHelper.GetLiteral("LIT_INFO_CART", pageLiteralId, portalConfig).Replace("{0}", PageLiteralsHelper.GetLiteral("LIT_STANDARD", pageLiteralId, portalConfig)))');
                $("#subgroup_3").addClass("sel");
                setCartURL('@Model.StandardProduct');
                return;
            }
            if (result == @((short)PostPublishShowPopUpsEnum.ShowPublishStandardPopUp)) {
                $('#footer_info').html('@Html.Raw(PageLiteralsHelper.GetLiteral("LIT_INFO_PUBLISH", pageLiteralId, portalConfig).Replace("{0}", PageLiteralsHelper.GetLiteral("LIT_STANDARD", pageLiteralId, portalConfig)))');
                $("#subgroup_3").addClass("sel");
                $("#btCp").val('@PageLiteralsHelper.GetLiteral("LIT_PUBLISH", pageLiteralId, portalConfig)')
                return;
            }
            if (result == @((short)PostPublishShowPopUpsEnum.ShowPublishAdvancedPopUp)) {
                $('#footer_info').html('@Html.Raw(PageLiteralsHelper.GetLiteral("LIT_INFO_PUBLISH", pageLiteralId, portalConfig).Replace("{0}", PageLiteralsHelper.GetLiteral("LIT_ADVANCED", pageLiteralId, portalConfig)))');
                $("#subgroup_4").addClass("sel");
                $("#btCp").val('@PageLiteralsHelper.GetLiteral("LIT_PUBLISH", pageLiteralId, portalConfig)')
                return;
            }
            if (result == @((short)PostPublishShowPopUpsEnum.ShowPublishPremiumPopUp)) {
                $('#footer_info').html('@Html.Raw(PageLiteralsHelper.GetLiteral("LIT_INFO_PUBLISH", pageLiteralId, portalConfig).Replace("{0}", PageLiteralsHelper.GetLiteral("LIT_PREMIUM", pageLiteralId, portalConfig)))');
                $("#subgroup_5").addClass("sel");
                $("#btCp").val('@PageLiteralsHelper.GetLiteral("LIT_PUBLISH", pageLiteralId, portalConfig)')
                return;
            }
            if (result == @((short)PostPublishShowPopUpsEnum.ShowPublishCustomOfferPopUp)) {
                $('#footer_info').html('@Html.Raw(PageLiteralsHelper.GetLiteral("LIT_INFO_PUBLISH", pageLiteralId, portalConfig).Replace("{0}", PageLiteralsHelper.GetLiteral("LIT_CUSTOM", pageLiteralId, portalConfig)))');
                $("#subgroup_6").addClass("sel");
                $("#btCp").val('@PageLiteralsHelper.GetLiteral("LIT_PUBLISH", pageLiteralId, portalConfig)')
                return;
            }
            if (result == @((short)PostPublishShowPopUpsEnum.ShowGoToAdvancedPackCartPopUp)) {
                $('#footer_info').html('@Html.Raw(PageLiteralsHelper.GetLiteral("LIT_INFO_CART", pageLiteralId, portalConfig).Replace("{0}", PageLiteralsHelper.GetLiteral("LIT_ADVANCED", pageLiteralId, portalConfig)))');
                $("#subgroup_4").addClass("sel");
                setCartURL('@Model.AdvanceProduct');
                return;
            }
            if (result == @((short)PostPublishShowPopUpsEnum.ShowGoToPremiumPackCartPopUp)) {
                $('#footer_info').html('@Html.Raw(PageLiteralsHelper.GetLiteral("LIT_INFO_CART", pageLiteralId, portalConfig).Replace("{0}", PageLiteralsHelper.GetLiteral("LIT_PREMIUM", pageLiteralId, portalConfig)))');
                $("#subgroup_5").addClass("sel");
                setCartURL('@Model.PremiumProduct');
                return;
            }
            if (result == @((short)PostPublishShowPopUpsEnum.ShowGoToContactPage)) {
                $('#footer_info').html('@Html.Raw(PageLiteralsHelper.GetLiteral("LIT_CONTACT_INFO", pageLiteralId, portalConfig))');
                $("#btCp").val('@PageLiteralsHelper.GetLiteral("LIT_CONTACT", pageLiteralId, portalConfig)')
                return;
            }
            if (result == @((short)PostPublishShowPopUpsEnum.ShowPublishExpressPopUp)) {
                $('#footer_info').html('@Html.Raw(PageLiteralsHelper.GetLiteral("LIT_INFO_PUBLISH", pageLiteralId, portalConfig).Replace("{0}", "Express"))');
                $("#subgroup_9").addClass("sel");
                $("#btCp").val('@PageLiteralsHelper.GetLiteral("LIT_PUBLISH", pageLiteralId, portalConfig)')
                return;
            }
            if (result == @((short)PostPublishShowPopUpsEnum.ShowGoToExpressPackCartPopUp)) {
                $('#footer_info').html('@Html.Raw(PageLiteralsHelper.GetLiteral("LIT_INFO_CART", pageLiteralId, portalConfig).Replace("{0}", "Express"))');
                $("#subgroup_9").addClass("sel");
                setCartURL('@Model.ExpressProduct');
                return;
            }
        }

        function setCartURL(product) {
            $("#btCp").val('@PageLiteralsHelper.GetLiteral("LIT_BUY", pageLiteralId, portalConfig)')
            $("#btCp").off('click').on('click', function () {
                trackButtonPage(1, @pageId);
                var url = '@Html.Raw(@Url.Action("Index", "MultiPurchaseCart", new { oi = Model.OfferIdEncrypted, p = EncryptationHelper.Encrypt(((int)PageEnum.PackCart).ToString()), btn = Model.BtnConvertToCompleteEncryptedName}))';
                window.location.href = url + "&prod=" + encodeURIComponent(product);
            });
        }

        function callAjax(callfrom) {
            $.blockUI({ message: "<h1>@PageLiteralsHelper.GetLiteral("LIT_POSTING", pageLiteralId, portalConfig)</h1>" });

            var respuesta = "";
            var isUpdate = '@Model.IsUpdateOffer';
            var oi = '@Model.Offer.idofferencrypted';
            var kq = $("#Offer_has_killer_questions:checked").val();
            var ho = $("#Offer_ishighlighted:checked").val();
            var uo = $("#Offer_isurgent:checked").val();
            var fo = $("#Offer_Isflash:checked").val();
            var hc = $("#Offer_HiddenCompany:checked").val();
            var se = $("#Offer_show_contact_email:checked").val();
            var cp = $("#Offer_show_phone_offer:checked").val();
            var ca = $("#Offer_show_contact_address:checked").val();
            var co = '@Html.Raw(Model.HavePromoOfferAvailable.ToString())';
            var hcn = $("#Offer_HiddenCompanyName").val();
            var cat = $("#Offer_contact_address").val();
            var emailt = $("#Offer_contact_email").val();
            var pht = $("#Offer_contact_phone").val();
            var ess = $("#Offer_Essential:checked").val();
            var exp = $("#Offer_Express:checked").val();

            $.ajax({
                async: true,
                cache: false,
                dataType: "html",
                type: 'POST',
                url: "@Url.Action("CanPublish", "CompanyFeaturesPostPublish")",
                data: "kq=" + kq + "&ho=" + ho + "&uo=" + uo + "&fo=" + fo + "&hc=" + hc + "&se=" + se + "&cp=" + cp + "&ca=" + ca + "&co=" + co + "&oi=" + oi + "&hcn=" + hcn + "&cat=" + cat + "&emailt=" + emailt + "&pht=" + pht + "&ess=" + ess + "&exp=" + exp,
                success: function (result) {
                    let obj = null;

                    if (isJson(result)) {
                        obj = JSON.parse(result);
                    }
                    $.unblockUI();

                    if (callfrom == "markNecessaryProducts" && isUpdate == 'False') {
                        markNecessaryProducts(result);
                        return;
                    }
                    if (callfrom == "canPublish") {
                        canPublish(result);
                        return;
                    }
                },
                beforeSend: function () { },
                error: function (objXMLHttpRequest) { }
            });
        }

        function isJson(value) {
            try {
                JSON.parse(value);
            } catch (e) {
                return false;
            }
            return true;
        }
    </script>

    @if (!Model.IsUpdateOffer)
    {
        <script>
            $(document).ready(function () {
                callAjax("markNecessaryProducts");
            });
        </script>
    }
}

