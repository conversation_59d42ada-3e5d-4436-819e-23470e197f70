@model Redarbor.Company.WebUI.Models.Company.UserDataModel
@using Redarbor.Common.Entities.Enums
@using Redarbor.Company.WebUI.Helpers.Html
@using Redarbor.Company.WebUI.Helpers
@using Redarbor.Master.Entities.Enums
@{
    int pageId = Html.SetCurrentPage((int)PageEnum.CompanyAccounts);
    var portalConfig = PortalConfigHelper.GetPortalConfiguration();
    var IdEncrypt = !string.IsNullOrWhiteSpace(Model.IdEncrypt) ? Model.IdEncrypt : "0";
    var mainClassDiv = Model.IsNew ? "box mb15 js_box_crear_usuario" : string.Format("bg_brand_light bt1 w100 mB_neg_m pt10 pb20 pAllB_m pb0_m js_box_editar_{0}", IdEncrypt);
    var classFooter = Model.IsNew ? "form_action bt0_m pt0_m" : "no_label hor";
    var sizeClassMainDiv = Model.IsNew ? "" : "w65_mB fl";
    var widthSize = Model.IsNew ? "w45" : "w70";

}

@if (Model.IsValidToAdd || !Model.IsNew)
{
    using (Ajax.BeginForm("EditOrInsert", "CompanyConfigurationAccounts", new AjaxOptions { OnSuccess = "onSuccessEditUser", OnBegin = "loadingFullShow" }, new { @id = string.Format("Form_{0}", IdEncrypt), @class = "pr0 pl0 pt0 pb0" }))
    {
        @Html.AntiForgeryToken()
        @Html.HiddenFor(model => model.IdEncrypt, new { @id = string.Format("IdEncryptEditNewHidden_{0}", IdEncrypt) });
        @Html.HiddenFor(model => model.StatusId, new { @id = string.Format("StatusIdEditNewHidden_{0}", IdEncrypt) });
        @Html.HiddenFor(model => model.IsNew, new { @id = string.Format("IsNewEditNewHidden_{0}", IdEncrypt) });

        <p class="fs18 fwB tc mbB">@(Model.IsNew ? PageLiteralsHelper.GetLiteral("LIT_ALTA_USUARIO", pageId, portalConfig) : PageLiteralsHelper.GetLiteral("LIT_MOD_USUARIO", pageId, portalConfig))</p>
        <div class="field_input fwB">
            <label>@PageLiteralsHelper.GetLiteral("LIT_NOMBRE_APELLIDOS", pageId, portalConfig)<span class="required">*</span></label>
            @Html.TextBoxFor(model => model.ContactName, new { @id = "ContactName_" + IdEncrypt })
            @Html.ValidationMessageFor(model => model.ContactName, string.Empty, new { @class = "field-validation-valid" })
        </div>

        <div class="field_input fwB">
            <label>@PageLiteralsHelper.GetLiteral("LIT_EMAIL", pageId, portalConfig)<span class="required">*</span></label>
            @if (Model.DomainsCompany.Count > 0)
            {
                @Html.HiddenFor(model => model.Email, new { @id = string.Format("EmailEditNewHidden_{0}", IdEncrypt) })                 
                @Html.TextBoxFor(model => model.PrefixEmail, new { @id = string.Format("PrefixEmail_{0}", IdEncrypt) })                  
                @Html.DropDownListFor(model => model.DomainCompanySelected, Model.DomainsCompany, new { @id = string.Format("DomainCompany_{0}", IdEncrypt),  @class = "mt10" })                  
                @Html.ValidationMessageFor(model => model.PrefixEmail, string.Empty, new { @class = "field-validation-valid" })
            }
            else
            {
                @Html.TextBoxFor(model => model.Email, new { @id = string.Format("PrefixEmail_{0}", IdEncrypt) })
                @Html.ValidationMessageFor(model => model.Email, string.Empty, new { @class = "field-validation-valid" })

            }
        </div>

        <div class="field_input fwB">
            <label>@PageLiteralsHelper.GetLiteral("LIT_CLAVE", pageId, portalConfig)<span class="required">*</span></label>
            @Html.PasswordFor(model => model.Password, new { autocomplete = "new-password", @id = string.Format("Pass_{0}", IdEncrypt) })
            @Html.ValidationMessageFor(model => model.Password, string.Empty, new { @class = "field-validation-valid" })
        </div>

        <div class="field_input fwB">
            <label>@PageLiteralsHelper.GetLiteral("LIT_TELEFONO", pageId, portalConfig)</label>
            @Html.TextBoxFor(model => model.PhoneNumbers, new { type = "tel", @id = string.Format("PhoneNumbers_{0}", IdEncrypt) })
            @Html.ValidationMessageFor(model => model.PhoneNumbers, "", new { @class = "field-validation-valid" })
        </div>
        <div class="field_select fwB">
            <label>@PageLiteralsHelper.GetLiteral("LIT_ROL", pageId, portalConfig)</label>
            @if (Model.RoleId == (int)UserRoleEnum.ADMINISTRADOR_PRINCIPAL)
            {
                @Html.TextBox("roleAdminPrincipal", Html.Raw(" " + PageLiteralsHelper.GetLiteral(Model.UserRoleLiteral, pageId, portalConfig)).ToString(), new { @readonly = "readonly", @style = "background-color : whitesmoke" });
                @Html.HiddenFor(model => model.RolSelected);
            }
            else
            {
                var adminPrincipalLiteral = PageLiteralsHelper.GetLiteral("LIT_ADMINISTRADOR_PRINCIPAL", pageId, portalConfig);
                @Html.DropDownListFor(model => model.RolSelected, Model.Rols.Where(x => x.Text.ToUpper() != adminPrincipalLiteral.ToUpper()), new { @onchange = "javascript:printRoleFeatures('roleFeaturesDesc_" + @IdEncrypt + "',document.getElementById('" + string.Format("RolesItems_{0}", IdEncrypt) + "').value)", @class = "cm-12", @id = string.Format("RolesItems_{0}", IdEncrypt) })
            }
            @Html.ValidationMessageFor(model => model.RolSelected, string.Empty, new { @class = "field-validation-valid" })

        </div>
        <input type="submit" class="b_primary" value="@PageLiteralsHelper.GetLiteral("LIT_BTN_GUARDAR", pageId, portalConfig)">
        <a class="b_transparent js_hide_popup">@PageLiteralsHelper.GetLiteral("LIT_CANCELAR", pageId, portalConfig)</a>
    }
}


