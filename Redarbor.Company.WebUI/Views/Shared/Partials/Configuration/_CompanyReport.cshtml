@model Redarbor.Company.WebUI.Models.Company.Configuration.CompanyReportDataModel
@using Redarbor.Common.Entities.Enums
@using Redarbor.Company.WebUI.Helpers.Html
@using Redarbor.Company.WebUI.Helpers

@{
    int PageId = Html.SetCurrentPage((int)PageEnum.ListReports);
    var portalConfig = (Redarbor.Common.Entities.Configuration.PortalConfig)ViewData["portalConfig"];
}

<section class="table_data flex">
    <header class="bb0 hide_m">
        <div class="w60">@PageLiteralsHelper.GetLiteral("LIT_REPORT", PageId, portalConfig)</div>
        <div class="tc_fx w20">@PageLiteralsHelper.GetLiteral("LIT_DIA_EXTRACT", PageId, portalConfig)</div>
        <div class="tc_fx w20">@PageLiteralsHelper.GetLiteral("LIT_ACCIONES", PageId, portalConfig)</div>
    </header>
    <div class="tbody box pAll0">
        @foreach (var companyFile in Model.CompanyFiles)
        {
            <div class="js_click">
                <div class="w60 plB w70_m">
                    <div class="w100">
                        <p class="fc_link fs15 fwB">@companyFile.UserName</p>
                        <p class="fc_aux fs13 t_ellipsis">@companyFile.FileName</p>
                    </div>
                </div>
                <div class="tc_fx w20 hide_m">@companyFile.DateAdd.Day.ToString() @PageLiteralsHelper.GetLiteral("LIT_OF", PageId, portalConfig) @companyFile.MonthName</div>
                <div class="actions w20 w30_m">
                    <a href="@Url.Action("Download", "CompanyReport", new { fileEncryptedId = companyFile.FileCompanyIdEncrypted })" class="icon i_reports icon_tooltip">
                        <span>@PageLiteralsHelper.GetLiteral("LIT_DOWNLOAD", PageId, portalConfig)</span>
                    </a>
                    <a href="javascript:Delete('@companyFile.FileCompanyIdEncrypted' )" class="icon i_remove icon_tooltip">
                        <span>@PageLiteralsHelper.GetLiteral("LIT_DELETE", PageId, portalConfig)</span>
                    </a>
                </div>
            </div>
        }
    </div>
</section>
