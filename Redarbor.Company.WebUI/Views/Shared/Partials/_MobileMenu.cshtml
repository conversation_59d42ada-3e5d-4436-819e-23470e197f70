
@using System.Web.Mvc.Html
@using Redarbor.Company.WebUI.Helpers
@using Redarbor.Company.WebUI.Helpers.Html
@using Redarbor.Common.Entities.Enums
@using Redarbor.Master.Entities.Enums
@using Redarbor.Company.Contracts.ServiceLibrary.Enums
@using Redarbor.Core.Kpi.Contracts.ServiceLibrary.Enums

@{
    var PageId = (int)PageEnum.SiteMasterCompany;
    var portalConfig = PortalConfigHelper.GetPortalConfiguration();
    var companyCredentials = SecurityHelper.GetCompanyCredentials();
    var companyProduct = CompanyHelper.GetCurrentProduct(companyCredentials);
    var hasNewFreemiumChat = CompanyHelper.HasFreemiumChatHeader(companyProduct, portalConfig);
    var showAttractiveness = CompanyHelper.GetVisibilitySection(companyCredentials.IdCompany, CompanyVisibilitySectionEnum.DEGREE_ATTRACTION);
    var showEscapeRisk = CompanyHelper.GetVisibilitySection(companyCredentials.IdCompany, CompanyVisibilitySectionEnum.ESCAPE_RISK);
    var hasVisionFeature = CompanyHelper.HasFeature(ProductAmbitEnum.Vision, companyProduct);
    var hasVisionReportingFeature = CompanyHelper.HasFeature(ProductAmbitEnum.VisionReporting, companyProduct);
    var hasVisionRankingFeature = CompanyHelper.HasFeature(ProductAmbitEnum.VisionRaking, companyProduct);
    var isActionPermitedByRole = SecurityHelper.IsActionPemitedByRole(SecurityActionEnum.AllVisionAcces);
    var hasGetReviewFeature = CompanyHelper.HasFeature(ProductAmbitEnum.VisionNumberGetReviews, companyProduct);
    var name = string.IsNullOrEmpty(companyCredentials.Name) ? @PageLiteralsHelper.GetLiteral("LIT_TEXT_PLACE_HOLDER", PageId, portalConfig) : companyCredentials.Name;
    var hasMessagesMailing = CompanyHelper.HasMessagesMailingHeader(companyProduct, portalConfig);
}

<li class="menu_right">
    <div class="box_cand_log" id="showcan">
        <div class="link_cand">
            <a>
               @name
            </a>
        </div>
        <div class="box_cand_sub">
            <div class="triangulo_inf_login"></div>
            <div class="content_subnav">
                <ul class="links_cand">
                    <li class="user">
                        <div title="Logo" class="icon edificio_med">
                            @if (!string.IsNullOrEmpty(companyCredentials.ImgPath))
                            {
                                <img src="@string.Format("{0}{1}", portalConfig.cdn_images, companyCredentials.ImgPath)" id="img_company_logo" class="img-circle">
                            }
                            else
                            {
                                <img src="~/c/v2/img/user_empty.svg" id="id_company_logo" class="img-circle">
                            }
                        </div>
                        <span>
                            @companyCredentials.Name
                        </span>
                    </li>
                    <li><a href="@Url.Action("", "Company")">@PageLiteralsHelper.GetLiteral("MASTER_DROPDOWN_INICIO", PageId, portalConfig)</a></li>

                    @if (companyCredentials.UserRole != UserRoleEnum.COMPUADVISOR)
                    {
                        <li class="c_mbl more_fields">
                            <p class="title mvlRecruitment">@PageLiteralsHelper.GetLiteral("MASTER_TABS_RECRUITMENT", PageId, portalConfig)</p>
                            <ul id="mvlSubRecruitment" class="mvlSubMenu @Html.IsSelectedCompanyMenu(actions: "Index,CantPublish,Published,EndWelcome,LandingOfferPublish, Selection", controllers: "CompanyLanding,CompanyOffers,CompanyOffersPublish,CompanyDefaultCvs,CompanyCvs,CompanyOfferAI", cssClass: "show")">
                                <li class="@Html.IsSelectedCompanyMenu(actions: "Index", controllers: "CompanyOffers", cssClass: "activo")">
                                    <a href='@Url.Action("Index", "CompanyOffers")'>
                                        @PageLiteralsHelper.GetLiteral("MASTER_TABS_MIS_OFERTAS", PageId, portalConfig)
                                    </a>
                                </li>
                                @if (portalConfig.AEPortalConfig.HavePostPublish && companyProduct.GroupId != (short)ProductGroupsEnum.Membership)
                                {
                                    <li class="@Html.IsSelectedCompanyMenu(actions: "Index", controllers: "CompanyOffersPostPublish", cssClass: "activo")"><a href="@Url.Action("Index", "CompanyOffersPostPublish")">@PageLiteralsHelper.GetLiteral("MASTER_TABS_PUBLICAR_OFERTA", PageId, portalConfig)</a></li>
                                }
                                else
                                {
                                    if (companyProduct.GroupId == (short)ProductGroupsEnum.Freemium || (companyProduct.GroupId == (short)ProductGroupsEnum.Packs))
                                    {
                                        <li class="@Html.IsSelectedCompanyMenu(actions: "Index,PublishOffer", controllers: "CompanyOffersPublish,CompanyLanding", cssClass: "activo")"><a href="@Url.Action("LandingOfferPublish", "CompanyLanding")">@PageLiteralsHelper.GetLiteral("MASTER_TABS_PUBLICAR_OFERTA", PageId, portalConfig)</a></li>
                                    }
                                    else
                                    {
                                        <li class="@Html.IsSelectedCompanyMenu(actions: "Index,EndWelcome", controllers: "CompanyOffersPublish,CompanyLanding", cssClass: "activo")"><a href="@Url.Action("Index", "CompanyOffersPublish")">@PageLiteralsHelper.GetLiteral("MASTER_TABS_PUBLICAR_OFERTA", PageId, portalConfig)</a></li>
                                    }
                                }

                                <li class="@Html.IsSelectedCompanyMenu(actions: "Index", controllers: "CompanyDefaultCvs,CompanyCvs", cssClass: "activo")">
                                    <a href='@Url.Action("Index", "CompanyDefaultCvs")'>@PageLiteralsHelper.GetLiteral("MASTER_TABS_BUSCAR_CANDIDATOS", PageId, portalConfig)</a>
                                </li>
                            </ul>
                            <p class="arrow_more mvlRecruitment"></p>
                        </li>
                    }
                    @if (portalConfig.HaveVision && companyCredentials.IdMaster > 0 && ((short)ProductGroupsEnum.Membership == companyProduct.GroupId) || (((short)ProductGroupsEnum.Freemium == companyProduct.GroupId || (short)ProductGroupsEnum.Packs == companyProduct.GroupId) && hasVisionFeature))
                    {
                        <li class="c_mbl more_fields">
                            <p class="title mvlReviews"> @PageLiteralsHelper.GetLiteral("MASTER_TABS_REVIEWS", PageId, portalConfig)</p>
                            <ul id="mvlSubReview" class="mvlSubMenu @Html.IsSelectedCompanyMenu(actions: "Index", controllers: "CompanyVisionValuationRequest,CompanyVisionComments,CompanyVisionInterviewsComments,CompanyVisionValuations,CompanyVisionInterviewsStatistics,CompanyVisionRanking", cssClass: "show")">
                                <li class="@Html.IsSelectedCompanyMenu(actions: "Index", controllers: "CompanyVisionComments", cssClass: "activo")">
                                    <a @(companyCredentials.IdMaster > 0 ? string.Format("href={0}", Url.Action("Index", "CompanyVisionComments")) : "onclick=ShowPopUpContactVisionBlocked()")>
                                        @PageLiteralsHelper.GetLiteral("MASTER_TABS_REVIEWS", PageId, portalConfig)
                                    </a>
                                </li>
                                <li class="@Html.IsSelectedCompanyMenu(actions: "Index", controllers: "CompanyVisionValuationRequest", cssClass: "activo")">
                                    <a @(companyCredentials.IdMaster > 0 && !isActionPermitedByRole ? "onclick=ShowPopUpContactBlocked()" : companyCredentials.IdMaster > 0 && !hasGetReviewFeature ? "onclick=ShowPopUpContactVisionBlocked()" : string.Format("href={0}", Url.Action("Index", "CompanyVisionValuationRequest", new { afm = EncryptationHelper.Encrypt("1") })))>
                                        @PageLiteralsHelper.GetLiteral("MASTER_TABS_REQUEST_REVIEWS", PageId, portalConfig)
                                    </a>
                                </li>
                                <li class="@Html.IsSelectedCompanyMenu(actions: "Index", controllers: "CompanyVisionInterviewsComments", cssClass: "activo")">
                                    <a @(companyCredentials.IdMaster > 0 ? string.Format("href={0}", Url.Action("Index", "CompanyVisionInterviewsComments")) : "onclick=ShowPopUpContactVisionBlocked()")>
                                        @PageLiteralsHelper.GetLiteral("MASTER_TABS_REVIEWS_INTERVIEW", PageId, portalConfig)
                                    </a>
                                </li>
                                <li class="@Html.IsSelectedCompanyMenu(actions: "Index", controllers: "CompanyVisionValuations", cssClass: "activo")">
                                    <a @(companyCredentials.IdMaster > 0 ? string.Format("href={0}", Url.Action("Index", "CompanyVisionValuations")) : "onclick=ShowPopUpContactVisionBlocked()")>
                                        @PageLiteralsHelper.GetLiteral("MASTER_TABS_ANALYTICS_REVIEWS", PageId, portalConfig)
                                    </a>
                                </li>
                                <li class="@Html.IsSelectedCompanyMenu(actions: "Index", controllers: "CompanyVisionInterviewsStatistics", cssClass: "activo")">
                                    <a @(companyCredentials.IdMaster > 0 ? string.Format("href={0}", Url.Action("Index", "CompanyVisionInterviewsStatistics")) : "onclick=ShowPopUpContactVisionBlocked()")>
                                        @PageLiteralsHelper.GetLiteral("MASTER_TABS_ANALYTICS_INTERVIEWS", PageId, portalConfig)
                                    </a>
                                </li>
                                @if (hasVisionRankingFeature)
                                {
                                    <li class="@Html.IsSelectedCompanyMenu(actions: "Index", controllers: "CompanyVisionRanking", cssClass: "activo")">
                                        <a @(companyCredentials.IdMaster > 0 ? string.Format("href={0}", Url.Action("Index", "CompanyVisionRanking")) : "onclick=ShowPopUpContactVisionBlocked()")>
                                            @PageLiteralsHelper.GetLiteral("MASTER_TABS_RANKING", PageId, portalConfig)
                                        </a>
                                    </li>
                                }
                            </ul>
                            <p class="arrow_more mvlReviews"></p>
                        </li>
                        <li class="c_mbl more_fields">
                            <p class="title mvlEmployer">@PageLiteralsHelper.GetLiteral("MASTER_TABS_VALUATIONS", PageId, portalConfig)</p>
                            <ul id="mvlSubEmployer" class="mvlSubMenu @Html.IsSelectedCompanyMenu(actions: "Index", controllers: "CompanyVisionNewsDetail,CompanyVisionAboutCompany,CompanyVisionLogo,CompanyVisionPhotos,CompanyVisionNews,CompanyVisionBenefits,CompanyVisionAwards", cssClass: "show")">
                                <li class="@Html.IsSelectedCompanyMenu(actions: "Index", controllers: "CompanyVisionNews,CompanyVisionNewsDetail", cssClass: "activo")">
                                    <a @(companyCredentials.IdMaster > 0 ? string.Format("href={0}", Url.Action("Index", "CompanyVisionNews")) : "onclick=ShowPopUpContactVisionBlocked()")>
                                        @PageLiteralsHelper.GetLiteral("MASTER_TABS_NEWS", PageId, portalConfig)
                                    </a>
                                </li>
                                <li class="@Html.IsSelectedCompanyMenu(actions: "Index", controllers: "CompanyVisionPhotos", cssClass: "activo")">
                                    <a @(companyCredentials.IdMaster > 0 ? string.Format("href={0}", Url.Action("Index", "CompanyVisionPhotos")) : "onclick=ShowPopUpContactVisionBlocked()")>
                                        @PageLiteralsHelper.GetLiteral("MASTER_TABS_PHOTOS", PageId, portalConfig)
                                    </a>
                                </li>
                                <li class="@Html.IsSelectedCompanyMenu(actions: "Index", controllers: "CompanyVisionBenefits", cssClass: "activo")">
                                    <a @(companyCredentials.IdMaster > 0 ? string.Format("href={0}", Url.Action("Index", "CompanyVisionBenefits")) : "onclick=ShowPopUpContactVisionBlocked()")>
                                        @PageLiteralsHelper.GetLiteral("MASTER_TABS_BENEFITS", PageId, portalConfig)
                                    </a>
                                </li>
                                <li class="@Html.IsSelectedCompanyMenu(actions: "Index", controllers: "CompanyVisionAwards", cssClass: "activo")">
                                    <a @(companyCredentials.IdMaster > 0 ? string.Format("href={0}", Url.Action("Index", "CompanyVisionAwards")) : "onclick=ShowPopUpContactVisionBlocked()")>
                                        @PageLiteralsHelper.GetLiteral("MASTER_TABS_AWARDS", PageId, portalConfig)
                                    </a>
                                </li>
                                @if (companyCredentials.UserRole != UserRoleEnum.COMPUADVISOR)
                                {
                                    <li class="@Html.IsSelectedCompanyMenu(actions: "Index", controllers: "CompanyVisionAboutCompany", cssClass: "activo")">
                                        <a @(companyCredentials.IdMaster > 0 ? string.Format("href={0}", Url.Action("Index", "CompanyVisionAboutCompany")) : "onclick=ShowPopUpContactVisionBlocked()")>
                                            @PageLiteralsHelper.GetLiteral("MASTER_TABS_ABOUT_COMPANY", PageId, portalConfig)
                                        </a>
                                    </li>
                                    <li class="@Html.IsSelectedCompanyMenu(actions: "Index", controllers: "CompanyVisionLogo", cssClass: "activo")">
                                        <a @(companyCredentials.IdMaster > 0 ? string.Format("href={0}", Url.Action("Index", "CompanyVisionLogo")) : "onclick=ShowPopUpContactVisionBlocked()")>
                                            @PageLiteralsHelper.GetLiteral("MASTER_TABS_LOGO", PageId, portalConfig)
                                        </a>
                                    </li>
                                }
                            </ul>
                            <p class="arrow_more mvlEmployer"></p>
                        </li>
                        if (hasVisionReportingFeature)
                        {
                            <li class="c_mbl more_fields">
                                <p class="title mvlTalent">@PageLiteralsHelper.GetLiteral("MASTER_TABS_ESCAPEANDATTRACTIVENESS", PageId, portalConfig)</p>
                                <ul id="mvlSubTalent" class="mvlSubMenu @Html.IsSelectedCompanyMenu(actions: "Index", controllers: "CompanyVisionAttractiveness,CompanyVisionEscapeRisk,CompanyVisionTalentEscape", cssClass: "show")">
                                    @if (showAttractiveness)
                                    {
                                        <li class="@Html.IsSelectedCompanyMenu(actions: "Index", controllers: "CompanyVisionAttractiveness", cssClass: "activo")">
                                            <a @(companyCredentials.IdMaster > 0 ? string.Format("href={0}", Url.Action("Index", "CompanyVisionAttractiveness")) : "onclick=ShowPopUpContactVisionBlocked()")>
                                                @PageLiteralsHelper.GetLiteral("MASTER_TABS_ATTRACTIVENESS", PageId, portalConfig)
                                            </a>
                                        </li>
                                    }
                                    @if (showEscapeRisk)
                                    {
                                        <li class="@Html.IsSelectedCompanyMenu(actions: "Index", controllers: "CompanyVisionEscapeRisk", cssClass: "activo")">
                                            <a @(companyCredentials.IdMaster > 0 ? string.Format("href={0}", Url.Action("Index", "CompanyVisionEscapeRisk")) : "onclick=ShowPopUpContactVisionBlocked()")>
                                                @PageLiteralsHelper.GetLiteral("MASTER_TABS_ESCAPE_RISK", PageId, portalConfig)
                                            </a>
                                        </li>
                                    }
                                    <li class="@Html.IsSelectedCompanyMenu(actions: "Index", controllers: "CompanyVisionTalentEscape", cssClass: "activo")">
                                        <a @(companyCredentials.IdMaster > 0 ? string.Format("href={0}", Url.Action("Index", "CompanyVisionTalentEscape")) : "onclick=ShowPopUpContactVisionBlocked()")>
                                            @PageLiteralsHelper.GetLiteral("MASTER_TABS_TALENT_ESCAPE", PageId, portalConfig)
                                        </a>
                                    </li>
                                </ul>
                                <p class="arrow_more mvlTalent"></p>
                            </li>
                        }
                    }
                    @if (companyCredentials.UserRole != UserRoleEnum.COMPUADVISOR)
                    {
                        <li>
                            <a href="@Url.Action("Index", "CompanyConfiguration")">
                                @PageLiteralsHelper.GetLiteral("MASTER_TABS_CONFIGURACION", PageId, portalConfig)
                            </a>
                        </li>
                        <li class="c_mbl" id="help-tab-mbl"><a href="#">@PageLiteralsHelper.GetLiteral("MASTER_TABS_HELP", PageId, portalConfig)</a></li>
                    }

                    @if (hasNewFreemiumChat)
                    {
                        <li>
                            <a href="@Url.Action("Index", "CompanyConversations")">
                                @PageLiteralsHelper.GetLiteral("MENU_CHAT", PageId, portalConfig)
                            </a>
                        </li>
                    }
                </ul>
                @using (Html.BeginForm("Logout", "Login"))
                {
                    @Html.AntiForgeryToken()
                    <span title="@PageLiteralsHelper.GetLiteral("MASTER_DROPDOWN_CERRAR_SESION", PageId, portalConfig)" class="subnav_icon"></span>
                    <input type="submit" value="@PageLiteralsHelper.GetLiteral("MASTER_DROPDOWN_CERRAR_SESION", PageId, portalConfig)" class="subnav_btn" />
                }
            </div>
        </div>
    </div>

    @if(portalConfig.AEPortalConfig.IsNewSupportWithSF)
    {
        if (companyProduct.GroupId == (short)ProductGroupsEnum.Membership)
        {
            @Html.Partial("~/Views/Shared/Partials/_ContactButtons.cshtml", new ViewDataDictionary { { "origin", "_HeaderLinkNewPemMemb" }, { "pageid", PageId }, { "companycredentials", companyCredentials }, { "portalConfig", portalConfig } })
        }
        else
        {
            @Html.Partial("~/Views/Shared/Partials/_ContactButtons.cshtml", new ViewDataDictionary { { "origin", "_HeaderLinkNewPemNotMemb" }, { "pageid", PageId }, { "companycredentials", companyCredentials }, { "portalConfig", portalConfig } })
        }
    }

    <a href="@Url.Action("Index", "Contact", new { idc = EncryptationHelper.Encrypt(companyCredentials.IdCompany.ToString()) })" class="link">
        @Html.Raw(PageLiteralsHelper.GetLiteral("LIT_TECHNICAL_SUPPORT", PageId, portalConfig))
    </a>


    @if (portalConfig.has_chat == 1)
    {
        if (hasMessagesMailing || hasNewFreemiumChat)
        {
            <div id="masterChat" class="iconChat">
                <span id="infPendingMessages" class="icon chat" onmouseover="LoadChats(this)"></span>
                <div id="chat" class="boxC ocultar">
                    <div class="triangulo_inf_login"></div>                    
                    <ul id="headerItemsChat" class="links_chat">
                        <div id="loadingContactPopUp" class="loading"><div><span></span><span></span><span></span><span></span></div><div><span></span><span></span><span></span><span></span></div><div><span></span><span></span><span></span><span></span></div></div>
                    </ul>
                </div>
            </div>
        }
        else
        {
            <div id="masterChatNo" class="iconChat">
                <span id="infPendingMessages" class="icon chat" onmouseover="LoadChats(this)"></span>
                <div id="chatNo" class="boxC">
                    <div class="triangulo_inf_login"></div>
                    <ul id="headerItemsChat" class="links_chat">
                        <li class="txtInfo">
                            <span>@PageLiteralsHelper.GetLiteral("LIT_ACTION_NOT_ALLOWED_FEATURE_TITLE", PageId, portalConfig)</span>
                            <a href="#" onclick="addKpiAndRedirectToContact(@Convert.ToInt16(KpiEnum.FEATURE_CHAT_NOT_ALLOWED_CONTACT));">@PageLiteralsHelper.GetLiteral("LIT_CONTACT", PageId, portalConfig)</a>
                        </li>
                    </ul>
                </div>
            </div>

        }
    }
        
</li>