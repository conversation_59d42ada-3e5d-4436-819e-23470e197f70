@using Redarbor.Company.WebUI.Helpers;
@using Redarbor.Company.WebUI.Models.Company.Cart;
@using Redarbor.Master.Entities.Enums;
@using Redarbor.Company.WebUI.Helpers;
@using Redarbor.Common.Entities.Enums
@using Redarbor.Master.Contracts.ServiceLibrary.DTO

@model Redarbor.Company.WebUI.Models.Company.Cart.CompanyCartDataModel

@{
    //var productDescription = (string)ViewData["productDescription"];   @*SE DESACTIVA POR EL MOMENTO, POR PETICION DE PERE, SI SE VUELVE A ACTIVAR SE DEBERIA CREAR EL LITERAL EN BDD CORRESPONDIENTE A CADA TIPO DE SUBGRUPO*@
    Layout = null;
    var portalConfig = (Redarbor.Common.Entities.Configuration.PortalConfig)ViewData["portalConfig"];
    var CartProducts = (List<CompanyCartProductDataModel>)ViewData["cartData"];
    var productTitle = (string)ViewData["productTitle"];
    bool isOfferProduct = CartProducts.Exists(x => x.Product.Features.Exists(y => y.AmbitId == 1));
    bool haveProductsChecked = false;
    string productSubTitle = (string)ViewData["productSubTitle"];
    bool isBBDDCvProduct = ViewData["isBBDDCvProduct"] != null ? (bool)ViewData["isBBDDCvProduct"] : false;
    bool hasPrimeProductActive = ViewData["hasPrimeProductActive"] != null ? (bool)ViewData["hasPrimeProductActive"] : false;
    var percentageDiscount = isOfferProduct ? CartProducts.First().Temporalities.First().PercentageDiscount : string.Empty;
    var stylePromo = (isOfferProduct && !string.IsNullOrEmpty(percentageDiscount)) ? "bg_promo_light bt1_promo mtB mB_neg pAllB posRel" : string.Empty;

    foreach (var item in Model.PurchaseProductsList)
    {
        if (CartProducts.Exists(x => x.Product.Id == item))
        {
            haveProductsChecked = true;
            break;
        }
    }

        string iconClass = (string)ViewData["iconClass"];
        if (isBBDDCvProduct)
        {
            iconClass = "i_tests";
        }
    }

@if (CartProducts.Any())
{

    <div class="box mbB bAll1_m @(Model.IsFromPostPublishStepTwo ? "hide" : "")">
        <div class="js_productbox dFlex vm_fx dB_m">
            <div class="hide_m"><span class="icon @iconClass"></span></div>
            <div class="pl15 dIB_m pl0_m">
                <label for="js_oferta" class="fs16 fwB mr5">@productTitle</label>
                @*SE DESACTIVA POR EL MOMENTO, POR PETICION DE PERE, SI SE VUELVE A ACTIVAR SE DEBERIA CREAR EL LITERAL EN BDD CORRESPONDIENTE A CADA TIPO DE SUBGRUPO*@
                @*<div class="info_tooltip">
                    <div class="bubble_tooltip right hide">
                        <p>@Html.Raw(productDescription)</p>
                    </div>
                </div>*@
                <ul>
                    @Html.Raw(productSubTitle)
                </ul>
            </div>
            <div class="mlAuto tc tl_m mt15_m ml15_m pl15_m">
                <a class="js_showhideproduct dB @(isOfferProduct || isBBDDCvProduct ? "hide" : "")">@(haveProductsChecked ? PageLiteralsHelper.GetLiteral("LIT_REMOVEUNITS", Model.PageId, portalConfig) : PageLiteralsHelper.GetLiteral("LIT_ADDUNITS", Model.PageId, portalConfig))</a>
            </div>
        </div>
        @if (isOfferProduct && !string.IsNullOrEmpty(percentageDiscount))
        {
            @Html.Raw("<div class=\"" + stylePromo + "\">")
        }
        @if (isOfferProduct && !string.IsNullOrWhiteSpace(percentageDiscount))
        {
            <span class="tag promo fwB info big">- @percentageDiscount %  @PageLiteralsHelper.GetLiteral("LIT_FOR_A_LIMITED_TIME", Model.PageId, portalConfig)</span>
        }
        <div class="dFlex fc_aux_light fs13 mtB hide_m">
            <p class="w60 pl15">@PageLiteralsHelper.GetLiteral("LIT_OFFER_NUM", Model.PageId, portalConfig)</p>
            <p class="w20 tc">@PageLiteralsHelper.GetLiteral("LIT_TOTAL_PRICE", Model.PageId, portalConfig)</p>
            <p class="w20 tc prB">@PageLiteralsHelper.GetLiteral("LIT_PRICE_BY_UNIT", Model.PageId, portalConfig)</p>
        </div>
        <div class="field_radio_box mb0 mt10 @(isOfferProduct || haveProductsChecked || isBBDDCvProduct ? "" : "hide")">
            <div class="group">
                @foreach (var OfferProduct in CartProducts)
                {
                    var bBDDcvInitialFeature = OfferProduct.Product.Features.Find(x => x.AmbitId == (int)ProductAmbitEnum.CvBBDD);
                    var bBDDcvInitialUnits = bBDDcvInitialFeature != null ? bBDDcvInitialFeature.InitialUnits : 0;

                    var initialProductFeature = OfferProduct.Product.Features.Find(x => x.AmbitId == (int)ProductAmbitEnum.Offer);
                    var initialProductUnits = initialProductFeature != null ? initialProductFeature.InitialUnits : 0;

                    foreach (var temporality in OfferProduct.Temporalities.Take(1))
                    {
                        var hasPromotionWithoutVoucher = OfferProduct.Product.HasPromotion && !Model.HasVoucherPromotion;

                        <label class="radio w100 checkboxlist">
                            <input type="radio"
                                   id="<EMAIL>"
                                   name="<EMAIL>"
                                   class="js_productcheckbox"
                                   data-tmp="@temporality.Temporality"
                                   data-prodname="@OfferProduct.Product.ComercialName"
                                   data-prodprice="@temporality.TotalPriceLabel"
                                   data-prodpricewithoutprom="@temporality.TotalPriceLabelWithoutPromotion"
                                   data-numprice="@temporality.NumericPrice"
                                   data-groupid="@OfferProduct.Product.GroupId"
                                   data-bbddunits="@bBDDcvInitialUnits"
                                   data-offerUnits="@initialProductUnits"
                                   data-discounttext="@temporality.PriceToDiscount"
                                   data-percentdisc="@temporality.PercentageDiscount"
                                   @(OfferProduct.IsChecked ? "checked" : "") />

                            <span class="input"></span>

                            <span class="label_box dFlex vm_fx dB_m tc_m">
                                <p class="lh1 dIB w60 w100_m mbB_m prB_m @(hasPromotionWithoutVoucher ? "" : "pt10 pb10")">
                                    <span class="fwB fs18">@OfferProduct.Product.ComercialName</span>
                                </p>

                                <div class="w20 tc w100_m tl_m dFl_m vm_fx pl10_m">
                                    <span class="hide w20 fc_aux_light dIB_m mrAuto">@PageLiteralsHelper.GetLiteral("LIT_TOTAL_V2", Model.PageId, portalConfig)</span>
                                    <p class="fwB fs16 dIB_m">@Html.Raw(temporality.TotalPriceLabel)</p>
                                    @if (hasPromotionWithoutVoucher)
                                    {
                                        <p class="tdL fc_aux_light dIB_m w30_m tr_m">@Html.Raw(temporality.TotalPriceLabelWithoutPromotion)</p>
                                    }
                                </div>

                                <div class="w20 tc w100_m tl_m dFl_m vm_fx pl10_m">
                                    <span class="hide w20 fc_aux_light dIB_m mrAuto">@PageLiteralsHelper.GetLiteral("LIT_UNIT", Model.PageId, portalConfig)</span>
                                    <p class="fs16 dIB_m">@Html.Raw(temporality.UnitPriceLabel)</p>
                                    @if (hasPromotionWithoutVoucher)
                                    {
                                        <p class="tdL fc_aux_light dIB_m w30_m tr_m">@Html.Raw(temporality.UnitPriceLabelWithoutPromotion)</p>
                                    }
                                    @if (!string.IsNullOrEmpty(temporality.Saving) && !hasPrimeProductActive && !hasPromotionWithoutVoucher)
                                    {
                                        <p class="fc_col5 show_m hide_m">-@(temporality.Saving) @PageLiteralsHelper.GetLiteral("LIT_BY_UNIT", Model.PageId, portalConfig)</p>
                                    }
                                </div>
                                @if (!string.IsNullOrEmpty(temporality.Saving) && !hasPrimeProductActive && !hasPromotionWithoutVoucher)
                                {
                                    <p class="fc_col5 show_m hide">-@(temporality.Saving) @PageLiteralsHelper.GetLiteral("LIT_BY_UNIT", Model.PageId, portalConfig)</p>
                                }
                            </span>
                        </label>

                        @Html.Hidden(string.Format("CartProducts[{0}].Product", OfferProduct.PositionId), "", new { @Id = string.Format("hd{0}{1}", temporality.Temporality, OfferProduct.Product.Id) })

                        @*<div class="w_25 vm p10 pl20px">
                            @if (temporality.Saving != string.Empty)
                            {
                                <div class="box_offer_dto">
                                    <p class="fs20 fw_b">@Html.Raw(temporality.Saving)<span class="fs16">%</span></p>
                                    <p>@PageLiteralsHelper.GetLiteral("LIT_SAVE_BY_UNIT", PageId, portalConfig)</p>
                                </div>
                            }
                        </div>*@

                        <input type='hidden' id='@string.Format("hTotal{0}{1}", temporality.Temporality, OfferProduct.Product.Id)' class="hTotal" value='@temporality.TotalPriceLabel' data-price='@temporality.TotalPriceLabel' data-product='@OfferProduct.Product.Id' />
                        if (OfferProduct.HasVat)
                        {
                            <input type='hidden' id='@string.Format("hTotalWithoutIva{0}{1}", temporality.Temporality, OfferProduct.Product.Id)' value='@temporality.TotalPriceLabel' />
                            <input type='hidden' id='@string.Format("hIva{0}{1}", temporality.Temporality, OfferProduct.Product.Id)' value='@temporality.ValueVat' />
                            <input type='hidden' id='@string.Format("hTotalIva{0}{1}", temporality.Temporality, OfferProduct.Product.Id)' value='@temporality.ValuePriceWithVat' />
                        }
                        <input type='hidden' id='@string.Format("hPercentageDiscount{0}{1}", temporality.Temporality, OfferProduct.Product.Id)' value='@temporality.PercentageDiscount ' />
                        <input type='hidden' id='@string.Format("hPriceToDiscount{0}{1}", temporality.Temporality, OfferProduct.Product.Id)' value='@temporality.PriceToDiscount ' />
                        <input type='hidden' id='@string.Format("hPriceWithPromo{0}{1}", temporality.Temporality, OfferProduct.Product.Id)' value='@temporality.TotalPriceLabelWithPromotion ' />
                        <input type='hidden' id='@string.Format("hDescription{0}{1}", temporality.Temporality, OfferProduct.Product.Id)' value='@temporality.ProductName' />
                    }
                }
            </div>
        </div>
        @if (isOfferProduct && !string.IsNullOrEmpty(percentageDiscount))
        {
            @Html.Raw("</div>")
        }
        @if (isBBDDCvProduct)
        {
            <div class="sub_box fs16 pAll15 tc">
                @if (portalConfig.AEPortalConfig.HubSpotContact && portalConfig.AEPortalConfig.EnableComercialContactPopup)
                {

                    <a href="#" onclick="ga('send', 'event', 'Contact Buttons', 'Click Hubspot MultiPurchase', 'Click Hubspot Support MultiPurchase'); comercialRequest('@Url.Action("ComercialRequest", "Contact", new { sk = "1eG3SIQqWScyhf2z9Hm9Sxw5ajju" })'); return false;" class="fwB">
                        @PageLiteralsHelper.GetLiteral("LIT_COMERCIAL_REQUEST_CONTACT", Model.PageId, portalConfig)
                    </a> @PageLiteralsHelper.GetLiteral("LIT_COMERCIAL_REQUEST_CONTACT_2", Model.PageId, portalConfig)
                }
                else
                {
                    var companycredentials = SecurityHelper.GetCompanyCredentials();
                    var comercialAndTechnicalSupport = CompanyHelper.GetComercialAndTechnicalSupport(PageLiteralsHelper.GetLiteral("LIT_COMMENT_FORM_SF", (int)PageEnum.SiteMasterCompany, portalConfig), portalConfig, companycredentials);

                    <a href="#" onclick="javascript:setSrcInFormIframeNewPem('@comercialAndTechnicalSupport.Comments','@comercialAndTechnicalSupport.ComercialInformationUrl')">
                        @PageLiteralsHelper.GetLiteral("LIT_COMERCIAL_REQUEST_CONTACT", Model.PageId, portalConfig)
                    </a> @PageLiteralsHelper.GetLiteral("LIT_COMERCIAL_REQUEST_CONTACT_2", Model.PageId, portalConfig)
                }
            </div>
        }
    </div>

}