@model Redarbor.Company.WebUI.Models.Company.Offer.KillerQuestionDataModel
@using Redarbor.Company.WebUI.Helpers;
@using Redarbor.Common.Entities.Enums
@using Redarbor.Company.WebUI.Models.Company.Offer

@{
    short pageId = (int)PageEnum.PublishOffer;
    var portalConfig = (Redarbor.Common.Entities.Configuration.PortalConfig)ViewData["portalConfig"];
    var killerQuestionTypeList = (List<SelectListItem>)ViewData["killerQuestionTypeList"];
    var killerQuestionScoreList = (List<SelectListItem>)ViewData["killerQuestionScoreList"];
    var killerDataList = new List<KillerQuestionDataDataModel>()
{
        new KillerQuestionDataDataModel(),
        new KillerQuestionDataDataModel(),
        new KillerQuestionDataDataModel(),
        new KillerQuestionDataDataModel(),
        new KillerQuestionDataDataModel()
    };
}

<div>
    <p class="mb15 tl pt15_r">@PageLiteralsHelper.GetLiteral("FORM_KQ_INFO", pageId, portalConfig).Replace("#OPENKQ#", portalConfig.MaxKillersOpenedQuestions.ToString()).Replace("#CLOSEDKQ#", portalConfig.MaxKillersClosedQuestions.ToString())</p>
    <input id="addkillerquestion" type="button" class="submit_n fn plr3 mb15 js-processing w_100r" value="@PageLiteralsHelper.GetLiteral("LIT_ADD_QUESTION", pageId, portalConfig)" />
</div>
<div id="containeraddkillerquestion" class="mb15" style="display:none">
    <h3 class="fs20">@PageLiteralsHelper.GetLiteral("FORM_KQ_NEW_QUESTION", pageId, portalConfig)</h3>
    <div id="OpenQuestionsLimit" class="box_or tc p20 hide">
        <span class="fcred">@PageLiteralsHelper.GetLiteral("FORM_KQ_OPEN_LIMIT", pageId, portalConfig)</span><br>@PageLiteralsHelper.GetLiteral("FORM_KQ_OPEN_LIMIT_DELETE", pageId, portalConfig)
    </div>
    <div id="ClosedQuestionsLimit" class="box_or tc p20 hide">
        <span class="fcred">@PageLiteralsHelper.GetLiteral("FORM_KQ_CLOSED_LIMIT", pageId, portalConfig)</span><br>@PageLiteralsHelper.GetLiteral("FORM_KQ_CLOSED_LIMIT_DELETE", pageId, portalConfig)
    </div>
    <div class="w_100">
        <div class="w_30 m0i mt5_ri">
            <p>@PageLiteralsHelper.GetLiteral("LIT_TIPO_PREGUNTA", pageId, portalConfig)</p>
        </div>
        <div class="w_60">
            @Html.DropDownListFor(m => m.Type, killerQuestionTypeList, new { @class = "w_40", @id="AddKillerQuestionType", Name="AddKillerQuestionType" })
        </div>
    </div>
    <div>
        <div class="w_100">
            <div class="w_30 m0i mt5_ri">
                <p>@PageLiteralsHelper.GetLiteral("LIT_PREGUNTA", pageId, portalConfig)</p>
            </div>
            <div class="w_60 m0i">
                @Html.ValidationMessage("AddKillerQuestionQuestion", "", new { @class = "pos_rel fl field-validation-error" })
                @Html.TextAreaFor(m => m.Title, new { maxlength = 200, @id = "AddKillerQuestionQuestion", Name = "AddKillerQuestionQuestion", @class="mt0" })
                <span class="text_info fr mt5">(@PageLiteralsHelper.GetLiteral("FORM_KQ_MAX_LENGTH", pageId, portalConfig))</span>
            </div>
        </div>
    </div>
    <div id="killerquestionclosed" style="display:none">
        @{
            if (killerDataList.Any())
            {
                for (var i = 0; i < killerDataList.Count(); i++)
                {
                    var numeberKiller = i + 1;
                    <div class="w_100 mt15">
                        <div class="w_30 m0i mt5_ri">
                            <p>@PageLiteralsHelper.GetLiteral("LIT_RESPUESTA", pageId, portalConfig) @numeberKiller<span>@(numeberKiller < 3 ? "*" : "")</span></p>
                        </div>
                        <div class="w_45 mt5_ri">
                            @Html.ValidationMessage("AddKillerQuestionValue" + numeberKiller, "", new { @class = "pos_rel fl field-validation-error w_100" })
                            @Html.TextBoxFor(m => killerDataList[i].Answer, new { @class = "w_100 plr10", maxlength = 150, Name = "AddKillerQuestionValue" + numeberKiller, @id = "AddKillerQuestionValue" + numeberKiller })
                        </div>
                        <div class="w_15 mt5_ri">
                            @Html.DropDownListFor(m => killerDataList[i].Score, killerQuestionScoreList, new { @class = "w_90 fr", Name = "AddKillerQuestionAnswerScore" + numeberKiller, @id="AddKillerQuestionAnswerScore" + numeberKiller })
                        </div>
                    </div>
                }
            }
        }
    </div>
    <div class="w_100 tc mt15">
        <input class="js-processing submit_n fn m0_auto plr3 d_inlineblock w_100r" type="button" value="@PageLiteralsHelper.GetLiteral("LIT_SAVE", pageId, portalConfig)" id="confirmaddkillerquestion" onclick="AddKillerQuestion();" />
        <a id="btCancelAddKillerQuestion" class="ml2 clear mt15_r mlr0 mt20i">@PageLiteralsHelper.GetLiteral("LIT_CANCELAR", pageId, portalConfig)</a>
    </div>

    <div class="errorinfo" id="KillerQuestionsErrors" style="display: none"></div>
</div>