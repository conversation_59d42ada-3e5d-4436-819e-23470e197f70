@using Redarbor.Company.WebUI.Helpers;
@using Redarbor.Common.Entities.Enums

@{
    var portalConfig = (Redarbor.Common.Entities.Configuration.PortalConfig)ViewData["portalConfig"];
    int PageId = (int)PageEnum.OfferList;
}

<div id="popUpInfoSelectOffer" class="popup flex hide js_box_eliminar_oferta">
    <div class="w40">
        <div>
            <h2>@PageLiteralsHelper.GetLiteral("LIT_INFO", PageId, portalConfig)</h2>
            <p class="desc">@PageLiteralsHelper.GetLiteral("LIT_SELECT_OFFER_ALERT", PageId, portalConfig)</p>

            <footer class="form_action">                
                <div><a class="b_transparent js_hide_eliminar_oferta">@PageLiteralsHelper.GetLiteral("LIT_CANCELAR", PageId, portalConfig)</a></div>
            </footer>
        </div>
        <button class="icon i_close icon_tooltip js_hide_eliminar_oferta"><span>@PageLiteralsHelper.GetLiteral("LIT_CERRAR", PageId, portalConfig)</span></button>
    </div>
</div>