@using Redarbor.Company.WebUI.Helpers;
@using Redarbor.Common.Entities.Enums
@using Redarbor.Master.Entities.Enums
@using Redarbor.Company.WebUI.Models;

@{
    var portalConfig = (Redarbor.Common.Entities.Configuration.PortalConfig)ViewData["portalConfig"];
    int pageId = (int)PageEnum.HomePrivada;
    var phone = ViewData["phone"];
}

<div class="popup draggable" id="popupAtencionComercial" style="z-index: 999999;">
    <div>
        <div class="bg_title">
            <div class="btn_click" onclick="closePopUp();">
                <span class="icon i_close"></span>
            </div>
            <div class="bullet_icon"><span class="icon i_people"></span></div>
            <span class="tag new">@PageLiteralsHelper.GetLiteral("LIT_BBDDCV_POPUP_NEW", pageId, portalConfig)</span>
        </div>
        <div>
            <p class="fs18 fwB tc mbB">@PageLiteralsHelper.GetLiteral("LIT_COMMERCIAL_ATTENTION", pageId, portalConfig)</p>

            <p class="mbB">@PageLiteralsHelper.GetLiteral("LIT_GIVE_PHONE_NUMBER", pageId, portalConfig)</p>

            <div class="field_input">
                <label><span class="fs18 fwB">@PageLiteralsHelper.GetLiteral("LIT_ENTER_PHONE_NUMBER", pageId, portalConfig)</span></label>
                <input type="text" placeholder="@PageLiteralsHelper.GetLiteral("LIT_ENTER_PHONE_NUMBER", pageId, portalConfig)" class="phone_field" value="@phone" />
                <span class="field-validation-valid"></span>
            </div>
            <a class="b_primary" onclick="sendData('@Url.Action("ComercialRequest", "Contact", new { sk = "1eG3SIQqWScyhf2z9Hm9Sxw5ajju" })');">@PageLiteralsHelper.GetLiteral("LIT_SEND", pageId, portalConfig)</a>
        </div>
    </div>
</div>

<div class="popup draggable" id="popupAtencionComercialDone">
    <div>
        <div class="bg_title">
            <div class="btn_click" onclick="closePopUp();">
                <span class="icon i_close"></span>
            </div>
            <div class="bullet_icon"><span class="icon i_people"></span></div>
            <span class="tag new">@PageLiteralsHelper.GetLiteral("LIT_BBDDCV_POPUP_NEW", pageId, portalConfig)</span>
        </div>
        <div>
            <p class="fs18 fwB tc mbB">@PageLiteralsHelper.GetLiteral("LIT_COMMERCIAL_ATTENTION", pageId, portalConfig)</p>
            <div class="box_ok">@PageLiteralsHelper.GetLiteral("LIT_COMERCIAL_CONTACT_THANK_YOU_MESSAGE", pageId, portalConfig)</div>
        </div>
    </div>
</div>

<script>
    function sendData(requestUrl) {
        loadingFullShow();
        $.ajax({
            url: requestUrl,
            data: { phone: $('.phone_field').val() },
            method: 'POST',
            complete: function () {
                loadingFullHide();
                closePopUp();
                popupDetail('#popupAtencionComercialDone');
            }
        });
    }
</script>
