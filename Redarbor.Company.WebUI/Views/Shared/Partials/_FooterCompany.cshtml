@using Redarbor.Company.WebUI.Helpers;
@using Redarbor.Common.Entities.Enums;
@using System.Web.Optimization;

@{
    short pageId = (short)PageEnum.HomeMasterCompany;
    var portalConfig = PortalConfigHelper.GetPortalConfiguration();
}

<footer>
    <div class="footer_c">
        <div class="container">
            <section>
                <div class="cm-3">
                    <h3>@PageLiteralsHelper.GetLiteral("MASTER_FOOTER_INSTITUCIONAL", pageId, portalConfig)</h3>
                    <ul>
                        <li><a href="@string.Format("{0}{1}", portalConfig.url_web, PageLiteralsHelper.GetLiteral("LIT_HOME_URL_GETTOKNOWUS", pageId, portalConfig))">@PageLiteralsHelper.GetLiteral("MASTER_FOOTER_QUIENES_SOMOS", pageId, portalConfig)</a></li>
                        <li><a href="@PageLiteralsHelper.GetLiteral("LIT_URL_COMPUTRABAJO_COM", pageId, portalConfig)">@PageLiteralsHelper.GetLiteral("MASTER_FOOTER_CT_OTROS_PAISES", pageId, portalConfig)</a></li>
                        <li>
                            <a href="@string.Format("{0}/avisolegal/", portalConfig.url_web, PageLiteralsHelper.GetLiteral("LIT_HOME_URL_LEGAL", pageId, portalConfig))">@PageLiteralsHelper.GetLiteral("MASTER_FOOTER_AVISO_LEGAL", pageId, portalConfig)</a>
                            @PageLiteralsHelper.GetLiteral("SEPARA_Y", pageId, portalConfig)
                            <a href="@string.Format("{0}{1}", portalConfig.url_web, PageLiteralsHelper.GetLiteral("LIT_HOME_URL_PRIVACY", pageId, portalConfig))">@PageLiteralsHelper.GetLiteral("FOOT_PRIVACIDAD", pageId, portalConfig)</a>
                        </li>
                        @if (portalConfig.AEPortalConfig.ShowComplaintsBook)
                        {
                            <li>
                                <span class="icon i_book"></span>
                                <form name="ingreso" method="post" action="https://librodereclamaciones.com.pe/ingresar.php" target="_blank">
                                    <input type="hidden" name="usuario" value="bumer_ope1">
                                    <input type="hidden" name="token" value="9195502742d844b18fd879ecc5c33856">
                                    <input type="submit" name="enviar" value="Libro de reclamaciones">
                                </form>
                            </li>
                        }
                    </ul>
                </div>
                <div class="cm-3">
                    <h3>@PageLiteralsHelper.GetLiteral("MASTER_FOOTER_PERSONAS", pageId, portalConfig)</h3>
                    <ul>
                        <li><a href="@string.Format("{0}{1}", portalConfig.url_candidate, PageLiteralsHelper.GetLiteral("LIT_URL_SOPORTE_PERSONAS", pageId, portalConfig))">@PageLiteralsHelper.GetLiteral("MASTER_FOOTER_AYUDA_PARA_PERSONAS", pageId, portalConfig)</a></li>
                        <li><a href="@string.Format("{0}{1}", portalConfig.url_candidate, PageLiteralsHelper.GetLiteral("LIT_HOME_URL_CONTACTCANDIDATE", pageId, portalConfig))">@PageLiteralsHelper.GetLiteral("FOOT_CONT_CANDIDATO", pageId, portalConfig)</a></li>
                        <li><a href="@string.Format("{0}{1}", portalConfig.url_web, PageLiteralsHelper.GetLiteral("LIT_HOME_URL_SEARCHJOBS", pageId, portalConfig))">@PageLiteralsHelper.GetLiteral("MASTER_FOOTER_BUSCAR_EMPLEO", pageId, portalConfig)</a></li>
                    </ul>
                </div>
                <div class="cm-3">
                    <h3>@PageLiteralsHelper.GetLiteral("MASTER_FOOTER_EMPRESA", pageId, portalConfig)</h3>
                    <ul>
                        <li><a href="@PageLiteralsHelper.GetLiteral("LIT_URL_SOPORTE_EMPRESAS", pageId, portalConfig)">@PageLiteralsHelper.GetLiteral("MASTER_FOOTER_AYUDA_PARA_EMPRESAS", pageId, portalConfig)</a></li>
                        <li><a href="@Url.Action("Index","Contact")">@PageLiteralsHelper.GetLiteral("FOOT_CONT_EMPRESA", pageId, portalConfig)</a></li>

                        @if (portalConfig.ShowPublicCandidateSearch)
                        {
                            <li><a href="@Url.Action("Index","CompanyDefaultCvs")">@PageLiteralsHelper.GetLiteral("MASTER_FOOTER_BUSCAR_PERSONAS", pageId, portalConfig)</a></li>
                        }
                    </ul>
                </div>
                <div class="footer_mov">
                    <section class="info_footer">
                        @PageLiteralsHelper.GetLiteral("MASTER_FOOTER_COPYRIGHT", pageId, portalConfig)
                        @DateTime.Now.Year.ToString()
                        @PageLiteralsHelper.GetLiteral("MASTER_FOOTER_DGNET_LTD", pageId, portalConfig)
                    </section>
                    <section class="menu_footer">
                        <a href="@string.Format("{0}{1}/", portalConfig.url_empresa, PageLiteralsHelper.GetLiteral("LIT_HOME_URL_CONTACTCOMPANY", pageId, portalConfig))">@PageLiteralsHelper.GetLiteral("MASTER_FOOTER_CONTACTAR", pageId, portalConfig)</a>
                        <a href="@string.Format("{0}/avisolegal/", portalConfig.url_web, PageLiteralsHelper.GetLiteral("LIT_HOME_URL_LEGAL", pageId, portalConfig))">@PageLiteralsHelper.GetLiteral("MASTER_FOOTER_AVISO_LEGAL", pageId, portalConfig)</a>
                    </section>
                </div>
                <div class="cm-3 socialicon">
                    <ul>
                        <li class="cm-12">
                            <a href="@PageLiteralsHelper.GetLiteral("MASTER_FOOTER_TWITTER_LINK", pageId, portalConfig)" target="_blank">
                                <div class="footer_circle twitter"></div>
                            </a>
                        </li>
                        <li class="cm-12">
                            <a href="@PageLiteralsHelper.GetLiteral("MASTER_FOOTER_FB_LINK", pageId, portalConfig)" target="_blank">
                                <div class="footer_circle facebook"></div>
                            </a>
                        </li>
                    </ul>
                </div>
                <p class="last_footer">
                    @PageLiteralsHelper.GetLiteral("MASTER_FOOTER_COPYRIGHT", pageId, portalConfig)
                    @DateTime.Now.Year.ToString()
                    @PageLiteralsHelper.GetLiteral("MASTER_FOOTER_DGNET_LTD", pageId, portalConfig)
                </p>
            </section>
        </div>
    </div>
</footer>
@Html.Raw(portalConfig.html_js)

