@model Redarbor.Company.WebUI.Models.ValidateLoginDataModel
@using Redarbor.Company.WebUI.Helpers;
@using Redarbor.Common.Entities.Enums;

@{
    var portalConfig = (Redarbor.Common.Entities.Configuration.PortalConfig)ViewData["portalConfig"];
    int pageId = (int)PageEnum.CompanyValidateLogin;
}

<div class="login-container" style="right: 0px; opacity: 1;">
    <div class="w70 mAuto mbB pbB w100_m">
        <header class="mbB pb15 pt15">
            <div class="carousel tc fs21">
                <div class="pre-word mb5">@Html.Raw(PageLiteralsHelper.GetLiteral("LIT_COMPANY_VALIDATE_REGISTER_JOIN_UP", pageId, portalConfig))</div>
                <div class="change-word fwB">
                    <div class="change-inner">
                        <div class="change-inner-element">@Html.Raw(PageLiteralsHelper.GetLiteral("LIT_COMPANY_VALIDATE_REGISTER_OFFERS_FREE", pageId, portalConfig))</div>
                        <div class="change-inner-element">@Html.Raw(PageLiteralsHelper.GetLiteral("LIT_COMPANY_VALIDATE_REGISTER_OFFERS_FREE", pageId, portalConfig))</div>
                        <div class="change-inner-element">@Html.Raw(PageLiteralsHelper.GetLiteral("LIT_COMPANY_VALIDATE_REGISTER_OFFERS_FREE", pageId, portalConfig))</div>
                        <div class="change-inner-element">@Html.Raw(PageLiteralsHelper.GetLiteral("LIT_COMPANY_VALIDATE_REGISTER_OFFERS_FREE", pageId, portalConfig))</div>
                        <div class="change-inner-element">@Html.Raw(PageLiteralsHelper.GetLiteral("LIT_COMPANY_VALIDATE_REGISTER_OFFERS_FREE", pageId, portalConfig))</div>
                    </div>
                </div>
            </div>
        </header>
        <div class="field_group mb0_m">           
            <label class="fwB">@Html.Raw(PageLiteralsHelper.GetLiteral("LIT_COMPANY_VALIDATE_REGISTER_ADD_NIT", pageId, portalConfig))</label>
            <div class="field_select mb15 w49">
                @Html.DropDownListFor(m => m.CountryIdSelected, Model.Countries, new { @id = "ddCountry", Name = "ValidateLoginDataModel.CountryIdSelected" })
                @Html.HiddenFor(m => m.CountryId, new { Name = "ValidateLoginDataModel.CountryId" })
                @Html.ValidationMessageFor(m => m.CountryIdSelected, "", new { data_valmsg_for = "ValidateLoginDataModel.CountryIdSelected" })
            </div>
            <div class="field_input_icon right end mb15 w49">
                <div class="cont">
                    @Html.TextBoxFor(m => m.Nit, new { @id = "txtNit", Name = "ValidateLoginDataModel.Nit", @placeholder = @PageLiteralsHelper.GetLiteral("LIT_REGISTER_SIMPLIFIED_RFC", pageId, portalConfig) })
                    @Html.ValidationMessageFor(m => m.Nit, "", new { @id = "Nit-error" })
                    <div class="info_tooltip">
                        <div class="bubble_tooltip left">@Html.Raw(PageLiteralsHelper.GetLiteral("LIT_NIT_NOTA", pageId, portalConfig))</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="field_input js_open-register-container">
            <label class="fwB">@Html.Raw(PageLiteralsHelper.GetLiteral("LIT_COMPANY_VALIDATE_REGISTER_COMPANY_EMAIL", pageId, portalConfig))</label>
            @Html.TextBoxFor(m => m.Email, new { @id = "txtEmailRegister", Name = "ValidateLoginDataModel.Email", @placeholder = @PageLiteralsHelper.GetLiteral("LIT_REGISTER_SIMPLIFIED_EMAIL", pageId, portalConfig) })
            @Html.ValidationMessageFor(m => m.Email, "", new { @id = "Email-error" })
        </div>
        <button id="btnOpenPswrd" class="b_primary big mbB dB mAuto rounded w100 js_open-pswrd-container">Siguiente</button>
    </div>
    <div class="fs13">
        <p>
            @PageLiteralsHelper.GetLiteral("LBL_CONDICIONES1", pageId, portalConfig)
            <a href="@string.Format("{0}/avisolegal/", portalConfig.url_web)">
                @PageLiteralsHelper.GetLiteral("LIT_LNK_AVISOLEGAL", pageId, portalConfig)
            </a>
            @if (portalConfig.PortalId == (short)PortalEnum.ComputrabajoColombia)
            {
                @PageLiteralsHelper.GetLiteral("LIT_SEP_1", pageId, portalConfig)
                <a href="@string.Format("{0}/prestaciondeservicio/", portalConfig.url_web)">
                    @PageLiteralsHelper.GetLiteral("LIT_LNK_PRESTACION_SERVICIOS", pageId, portalConfig)
                </a>
            }
            @PageLiteralsHelper.GetLiteral("LIT_SEP_2", pageId, portalConfig)
            <a href="@string.Format("{0}/privacidad/", portalConfig.url_web)">
                @PageLiteralsHelper.GetLiteral("LIT_LNK_PRIVACIDAD", pageId, portalConfig)
            </a>
            @PageLiteralsHelper.GetLiteral("LBL_CONDICIONES2", pageId, portalConfig)
            <a id="pdShowLegal" class="dIB js_show js_show_legal">@PageLiteralsHelper.GetLiteral("CHECKBOX_INFO_GDPR", pageId, portalConfig)</a>
            <a id="pdHideLegal" class="dIB js_show js_show_legal hide">@PageLiteralsHelper.GetLiteral("CHECKBOX_INFO_GDPR", pageId, portalConfig)</a>
            <div class="box_border hide mt15 js_box_legal">
                <p class="fwB">@PageLiteralsHelper.GetLiteral("LIT_BASIC_INFO", pageId, portalConfig)</p>
                <p class="fwB mt10 mb5">@PageLiteralsHelper.GetLiteral("LIT_RESPONSABLE", pageId, portalConfig)</p>
                <p>@PageLiteralsHelper.GetLiteral("LIT_DGNET", pageId, portalConfig)</p>

                <p class="fwB mt10 mb5">@PageLiteralsHelper.GetLiteral("LIT_FINALIDAD", pageId, portalConfig)</p>
                <p>@PageLiteralsHelper.GetLiteral("LIT_FINALIDAD_DESC", pageId, portalConfig)</p>

                <p class="fwB mt10 mb5">@PageLiteralsHelper.GetLiteral("LIT_LEGITIMACION", pageId, portalConfig)</p>
                <p>@PageLiteralsHelper.GetLiteral("LIT_LEGITIMACION_DESC", pageId, portalConfig)</p>

                <p class="fwB mt10 mb5">@PageLiteralsHelper.GetLiteral("LIT_DESTINATARIO", pageId, portalConfig)</p>
                <p>@PageLiteralsHelper.GetLiteral("LIT_DESTINATARIO_DESC", pageId, portalConfig)</p>

                <p class="fwB mt10 mb5">@PageLiteralsHelper.GetLiteral("LIT_DERECHOS", pageId, portalConfig)</p>
                <p>@PageLiteralsHelper.GetLiteral("LIT_DERECHOS_DESC", pageId, portalConfig)</p>
            </div>
        </p>
    </div>
</div>