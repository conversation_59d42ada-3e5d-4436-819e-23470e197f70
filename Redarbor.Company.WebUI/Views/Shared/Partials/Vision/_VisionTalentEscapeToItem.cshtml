@model Redarbor.Company.WebUI.Models.Company.Vision.VisionTalentEscapeDetailModel
@using Redarbor.Company.WebUI.Helpers;

@{
    Layout = null;

    var portalConfig = PortalConfigHelper.GetPortalConfiguration();
}

<li>
    <span class="linum">@String.Format("#{0}", Model.Counter)</span>

    @if (Model.IsNameVisible)
    {
        <span class="nomem">@Model.CompanyName</span>
    }

    @if (Model.IsBlurVisible)
    {
        <span class="nomem blur showPopUpVision cp" title="@Model.Title">">@Model.CompanyName</span>
    }

    <span class="bolitaB">
        @String.Format("{0}%", Model.Percentaje)
    </span>
</li>