@model Redarbor.Company.WebUI.Models.LoginDataModel
@using Redarbor.Company.WebUI.Helpers;
@using Redarbor.Company.WebUI.Helpers.Html;
@using Redarbor.Common.Entities.Enums;

@{
    Layout = null;
    int PageId = Html.SetCurrentPage((int)PageEnum.HomeMasterCompany);

    var portalConfig = (Redarbor.Common.Entities.Configuration.PortalConfig)ViewData["portalConfig"];
    var showBox = portalConfig.PortalId == (int)PortalEnum.ComputrabajoElSalvador ? "show special" : "hide";
}
@Scripts.Render("~/bundles/js/newlogin")
<a class="js_login mlAuto" id="newlogintoggle">@PageLiteralsHelper.GetLiteral("MASTER_ACCESO_EMPRESAS_REGISTRADAS", PageId, portalConfig)</a>

<div id=form_new_login_menu class="box_login  @showBox" style="z-index: 10 !important;">
    @using (Html.BeginForm("", "Login", FormMethod.Post))
    {
        @Html.AntiForgeryToken()
        <div class="field_input mb5 small">
            @Html.ValidationMessageFor(m => m.UserName, "", new { @class = "field-validation-valid" })
        </div>
        @Html.TextBoxFor(m => m.UserName, new { @class = "input_user js-login-email", @placeholder = @PageLiteralsHelper.GetLiteral("LIT_LOGIN_EMAIL_O_USUARIO", PageId, portalConfig) })
        <div class="field_input mb5 small">
            @Html.ValidationMessageFor(m => m.Password, "", new { @class = "field-validation-valid" })
        </div>
        <a href="@Url.Action("", "RecoverEmailNit")" class="dB mb15">
            @PageLiteralsHelper.GetLiteral("LIT_NOT_REMEMBER_EMAIL", PageId, portalConfig)
        </a>
        @Html.PasswordFor(m => m.Password, new { @class = "input_pass js-login-password", autocomplete = "new-password", @placeholder = @PageLiteralsHelper.GetLiteral("LIT_LOGIN_CLAVE", PageId, portalConfig) })
        <a href="@Url.Action("", "RecoverPassword")" class="dB mb15">
            @PageLiteralsHelper.GetLiteral("MASTER_NO_RECUERDA_CLAVE", PageId, portalConfig)
        </a>
        <div class="field_checkbox fs13 mb0 mt5">
            <label for="KeepMeLoggedIn" class="checkbox">
                @Html.CheckBoxFor(m => m.KeepMeLoggedIn, new { @checked = true })
                <span class="input"></span>
                @PageLiteralsHelper.GetLiteral("MASTER_PERMANECER_CONECTADO", PageId, portalConfig)
            </label>
        </div>
        <button type="submit" id="btnLogin" class="b_primary small">@PageLiteralsHelper.GetLiteral("MASTER_BTN_LOGIN_ENTRAR", PageId, portalConfig)</button>
    }
</div>