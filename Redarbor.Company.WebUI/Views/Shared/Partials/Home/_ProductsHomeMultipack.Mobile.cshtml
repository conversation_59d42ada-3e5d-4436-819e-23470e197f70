@model Redarbor.Company.WebUI.Models.Company.Product.LandingProductsDataModel
@using Redarbor.Company.WebUI.Helpers
@using Redarbor.Common.Entities.Enums
@using Redarbor.Master.Entities.Enums
@using System.Text.RegularExpressions;

@{
    short PageId = (short)PageEnum.HomeMasterCompany;
    var portalConfig = PortalConfigHelper.GetPortalConfiguration();
}

@foreach (var productModel in Model.LandingProductsContentDataModel)
{
    <li class="box_border mb15">
        <p class="fs24 fwB tc">@PageLiteralsHelper.GetLiteral("OFERT", PageId, portalConfig)@Regex.Replace(productModel.Title, @"Pack (\d+)", string.Empty)</p>
        <p class="fs16 tc">@productModel.SubTitle</p>
        <ul class="tl mt30 mb30">
            @foreach (var item in productModel.FeaturesDescriptions)
            {
                <li class="cols mt10">
                    <div class="vt"><span title="Contiene" class="icon @(item.Available == 1 ? "i_tick_ok" : "i_tick_ko") mr10"></span></div>
                    <span class=" w100 @(item.Place == 1 ? "fwB" : "")">@item.Literal</span>
                </li>
            }
        </ul>
        <p class="fc_aux mb15">
            @if (productModel.ProductId == (int)ProductEnum.FreemiumService)
            {
                if (productModel.HasUnlimitedOffers)
                {
                    <span class="fwB fs16 fc_base mr5">
                        @PageLiteralsHelper.GetLiteral("LIT_FREE_UNLIMITED_OFFERS", PageId, portalConfig)
                    </span>
                }
                else
                {
                    <span class="fwB fs30 fc_base mr5">@productModel.FreeOffers</span>
                    if (productModel.FreeOffers > 1)
                    {
                        @PageLiteralsHelper.GetLiteral("LIT_FREE_OFFERS", PageId, portalConfig)
                    }
                    else
                    {
                        @PageLiteralsHelper.GetLiteral("LIT_FREE_OFFER", PageId, portalConfig)
                    }
                }
            }
            else
            {
                @PageLiteralsHelper.GetLiteral("LIT_DESDE", PageId, portalConfig)
                <span class="fwB fs30 fc_base mr5 ml5">@Html.Raw(productModel.Price)</span>
                @PageLiteralsHelper.GetLiteral("LIT_POROFERTA", PageId, portalConfig)
            }
        </p>
        @if (productModel.ProductId == (int)ProductEnum.FreemiumService)
        {
            <a href="/Register" class="b_primary mb15">@PageLiteralsHelper.GetLiteral("LIT_PUBLISH", PageId, portalConfig)</a>
        }
        else
        {
            <a href="@Url.Action("Index", "MultiPurchaseCart", new
                {
                    prod = EncryptationHelper.Encrypt(productModel.ProductId.ToString()),
                    p = EncryptationHelper.Encrypt(((int)PageEnum.PackCart).ToString())
                })" class="b_primary mb15">@PageLiteralsHelper.GetLiteral("LIT_COMPRAR", PageId, portalConfig)</a>
            }
        <a class="dIB tc mb15 js_popup js_show_informacion_ofertas">@PageLiteralsHelper.GetLiteral("LIT_SHOW_POPUP", PageId, portalConfig)</a>
    </li>
}
