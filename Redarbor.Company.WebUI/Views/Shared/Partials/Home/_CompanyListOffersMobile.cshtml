@model Redarbor.Company.WebUI.Models.Company.Home.HomeOfferDataModel
@using Redarbor.Company.WebUI.Helpers.Html
@using Redarbor.Company.WebUI.Helpers
@using Redarbor.Common.Entities.Enums
@using Redarbor.Master.Entities.Enums
@using Redarbor.Company.WebUI.Enums
@using Redarbor.Company.WebUI.Models.Company.Home


@{
    int pageId = Html.SetCurrentPage((int)PageEnum.OfferList);
    var portalConfig = (Redarbor.Common.Entities.Configuration.PortalConfig)ViewData["portalConfig"];
    var counter = 0;
}

@functions{
    public bool CanShowShowMore<T>(ICollection<T> collection)
    {
        return collection.Count > 3;
    }
}

@using (Html.BeginForm("Index", "CompanyOffers", FormMethod.Post, new { id = "searchOffersMobile" }))
{
    @Html.AntiForgeryToken()
    @Html.HiddenFor(m => m.Pager.PageSelected)
    @Html.HiddenFor(m => m.CanDelete)
    @Html.HiddenFor(m => m.CanManageOffers)

    <div class="box_filters">
        <div class="tag tag_icon mr10" onclick="showPopupAllFilters()">
            <span class="icon i_filters"></span>
            <span id="bulletFilter" class="bullet hide"></span>
        </div>

        @foreach (var g in Model.OfferStatus)
        {
            var intVal = Convert.ToInt32(g.Value);
            var isSelected = Model.SelectedOfferStatus.Contains(intVal) ? "sel" : "";
            <div class="tag specialTag mr10 @isSelected" data-sel-id="@intVal">
                @g.Text
            </div>
        }

    </div>


    <div class="popup draggable" id="popupAllFilters">
        <div>
            <div class="dragger">
                <div class="btn_click js_hide_popup">
                    <span class="icon i_close"></span>
                </div>
            </div>

            <div class="content pt0">
                <p class="fs18 fwB tc mbB">@PageLiteralsHelper.GetLiteral("H2_FILTROS", pageId, portalConfig)</p>
                <div class="field_search">
                    <label class="fwB title_filter" title='@PageLiteralsHelper.GetLiteral("LIT_OFFER_TITLE", pageId, portalConfig)'>@PageLiteralsHelper.GetLiteral("LIT_OFFER_TITLE", pageId, portalConfig)</label>
                    <div class="group posRel z10">
                        <div>@Html.AutocompleteFor(m => m.SearchTitle, "AutoCompleteOfferTitle", "CompanyOffers", "offerMemeberShip.getModelSuggestFilter", new { @class = "w_74", @id = "searchTitle", placeholder = @PageLiteralsHelper.GetLiteral("LIT_TITLE", pageId, portalConfig), autocomplete = "off" })</div>
                        <button type="button" class="icon i_search cvsFilterButton"></button>
                    </div>
                </div>

                <div class="field_checkbox_box dFlex fx_wrap">
                    <label class="fwB">Estado de la oferta</label>
                    <div class="group w100">
                        @foreach (var g in Model.OfferStatus)
                        {
                            <label for="@g.Value" class="checkbox w100">
                                <input type="checkbox" name="SelectedOfferStatus" value="@g.Value" id="@g.Value" @(Model.SelectedOfferStatus.Contains(Convert.ToInt32(g.Value)) ? "checked" : "") />
                                <span class="input"></span>
                                <span class="label_box">@g.Text</span>
                            </label>
                        }
                    </div>
                </div>

                @if (Model.ShowManagers)
                {
                    <div class="field_checkbox_box dFlex fx_wrap">
                        @if (CanShowShowMore(Model.Managers))
                        {
                            <label class="fwB w100">@PageLiteralsHelper.GetLiteral("LIT_FILTRAR_GESTOR", pageId, portalConfig)<span class="fr fc_link fwN" onclick="seeMore(event)" data-see-less-text="Ver menos" data-see-more-text="Ver todos">Ver todos</span></label> @*//TODO LITERAL*@
                        }
                        <div class="group w100">
                            @foreach (var g in Model.Managers)
                            {
                                var isChecked = Model.SelectedManagers.Contains(g.Value);
                                <label for="@g.Value" class="checkbox w100  @(counter >= 3 && !isChecked ? "hide" : "")">
                                    <input type="checkbox" name="SelectedManagers" value="@g.Value" id="@g.Value" @(isChecked ? "checked" : "") />
                                    <span class="input"></span>
                                    <span class="label_box">@g.Text</span>
                                </label>
                                counter++;
                            }

                        </div>
                    </div>
                }


                <div class="field_checkbox_box dFlex fx_wrap">
                    @if (CanShowShowMore(Model.ProfesionalCategories))
                    {
                        <label class="fwB w100">@PageLiteralsHelper.GetLiteral("FILTER_PROFESSIONAL_CATEGORY", pageId, portalConfig)<span class="fr fc_link fwN" onclick="seeMore(event)" data-see-less-text="Ver menos" data-see-more-text="Ver todas">Ver todas</span></label> @*//TODO LITERAL*@
                    }
                    <div class="group w100">
                        @{ counter = 0;}
                        @foreach (var g in Model.ProfesionalCategories)
                        {
                            var idVal = g.Value + "_" + "proCat";
                            var isChecked = Model.SelectedProfesionalCategories.Contains(Convert.ToInt32(g.Value));

                            <label for="@idVal" class="checkbox w100 @(counter >= 3 && !isChecked ? "hide" : "")">
                                <input type="checkbox" name="SelectedProfesionalCategories" value="@g.Value" id="@idVal" @(isChecked ? "checked" : "") />
                                <span class="input"></span>
                                <span class="label_box">@g.Text</span>
                            </label>
                            counter++;
                        }
                    </div>
                </div>

                <div class="field_checkbox_box dFlex fx_wrap">
                    <label class="fwB w100">Características<span class="fr fc_link fwN" onclick="seeMore(event)" data-see-less-text="Ver menos" data-see-more-text="Ver todas">Ver todas</span></label>
                    <div class="group w100">
                        <label class="checkbox w100">
                            @Html.CheckBoxFor(m => m.Highlighteds, new { @name = "caracteristicas", identity = EncryptationHelper.Encrypt(((int)ProductAmbitEnum.OfferHighlighted).ToString()) })
                            <span class="input"></span>
                            <span class="label_box">@PageLiteralsHelper.GetLiteral("FEATURE_HIGHLIGHTED", pageId, portalConfig)</span>
                        </label>
                        <label class="checkbox w100">
                            @Html.CheckBoxFor(m => m.Urgents, new { @name = "caracteristicas", identity = EncryptationHelper.Encrypt(((int)ProductAmbitEnum.OfferUrgent).ToString()) })
                            <span class="input"></span>
                            <span class="label_box">@PageLiteralsHelper.GetLiteral("FEATURE_URGENT", pageId, portalConfig)</span>
                        </label>
                        <label class="checkbox w100">
                            @Html.CheckBoxFor(m => m.KillerQuestions, new { @name = "caracteristicas", identity = EncryptationHelper.Encrypt(((int)ProductAmbitEnum.KillerQuestions).ToString()) })
                            <span class="input"></span>
                            <span class="label_box">@PageLiteralsHelper.GetLiteral("FEATURE_KILLERQUESTIONS", pageId, portalConfig)</span>
                        </label>
                        <label class="checkbox w100 @(Model.HiddenName ? string.Empty : "hide" )">
                            @Html.CheckBoxFor(m => m.HiddenName, new { @name = "caracteristicas", identity = EncryptationHelper.Encrypt(((int)ProductAmbitEnum.OfferHiddenName).ToString()) })
                            <span class="input"></span>
                            <span class="label_box">@PageLiteralsHelper.GetLiteral("FEATURE_HIDDENNAME", pageId, portalConfig)</span>
                        </label>
                    </div>
                </div>
            </div>
            <div class="buttons">
                <a class="b_primary_inv" href="@Url.Action("Index", "CompanyOffers")">@PageLiteralsHelper.GetLiteral("LIT_FILTER_REMOVE", pageId, portalConfig)</a>
                <button type="button" class="b_primary offersFilterButton">@PageLiteralsHelper.GetLiteral("FILTER_BUTTON", pageId, portalConfig)</button>
            </div>
        </div>
    </div>
}


@if (!Model.Offers.Any())
{
    <div class="tc mt25">
        <p class="fs20 fwB mbB">@PageLiteralsHelper.GetLiteral("SPAN_MIS_OFETAS", pageId, portalConfig)</p>
        <p>
            @PageLiteralsHelper.GetLiteral("P_OFERTAS_DESCRIPCION", pageId, portalConfig)
        </p>
    </div>
}
else
{
    <div class="mt25">
        @foreach (var offer in Model.Offers)
        {
            Html.RenderPartial("_OfferRowMobile", offer, new ViewDataDictionary {
                { "portalConfig", portalConfig },
                { "haveMembresyActive", Model.HaveMembresyActive },
                { "ShowTotalNotViewed", Model.ShowTotalNotViewed },
                { "CanFindBBDDcvs", offer.OfferPackAndBasicDataModel.CanFindBBDDcvs},
                {"UrlChatControllerName", Model.UrlChatControllerName},
                {"HasNewMatchesNotificationsFeature", Model.HasNewMatchesNotificationsFeature}
            });
        }
        @Html.DisplayFor(i => i.Pager, new { portalConfig = portalConfig, formIdToSubmit = "searchOffersMobile" })
    </div>
}

@if (!string.IsNullOrWhiteSpace(Model.ShowPopupMatchesMailTitleOffer))
{
    <div class="overlay"></div>
    @Html.Partial("PopUps/_DisableMatchesOfferMailPopUpMobile", new ViewDataDictionary { { "portalConfig", portalConfig }, { "titleOffer", Model.ShowPopupMatchesMailTitleOffer } })
}

@{
    <script type="text/javascript" charset="utf-8">
        class ParametersJs {
            constructor(
                homeOfferSuggestFilterModel
            ) {
                this.HomeOfferSuggestFilterModel = homeOfferSuggestFilterModel;
            }
        }

        let parametersJs = new ParametersJs(
            @Html.Raw(Json.Encode(new HomeOfferSuggestFilterModel()))
        );

        window.parametersJs = parametersJs;


        document.addEventListener('click', function(event) {
            var target = event.target;
            if (window.matchMedia("(max-width: 768px)").matches) {
                var container = target.closest('.aClick');
                var isLink = target.tagName.toLowerCase() === 'a' || target.closest('a');
                if (container && !isLink) {
                    var link = container.querySelector('.click');
                    if (link) {
                        var url = link.getAttribute('href');
                        window.location.href = url;
                        loadingFullShow();
                    }
                }
            }
        });

        $(".js_hide_popup").click(function () {
            $(this).closest(".popup").removeClass("show_popup");
            $(".overlay").addClass("hide");
            $("body").removeClass("flowHid");
        });

        function redirectToCompanyMatches(newApplies, offer, companyFolder, event) {
            event.stopPropagation();
            event.preventDefault();
            var url = '@Url.Action("Index", "CompanyMatches")?oi=' + offer + '&cf=' + companyFolder + '&newApplies=' + newApplies;
            window.location.href = url;
            loadingFullShow();
        }
        function redirectToEditOffer(canedit, offer, event) {
            event.stopPropagation();
            event.preventDefault();
            if(canedit)
            {
                var url = '@Url.Action("Index", "CompanyOffersPublish")?oi=' + offer;
                window.location.href = url;
                loadingFullShow();
            }
        }

        document.addEventListener('DOMContentLoaded', function () {
            var divTags = document.querySelectorAll('.specialTag'); // Seleccionar todos los divs con la clase "tag"

            // Función para actualizar el estado de selección
            function updateSelectionState() {
                var anySelected = false;

                // Verificar si algún div está seleccionado
                divTags.forEach(function (div) {
                        anySelected = true;
                        var selectedId = div.getAttribute('data-sel-id');
                        var checkbox = document.getElementById(selectedId);
                        checkbox.checked = div.classList.contains('sel');
                });

                // Si ningún div está seleccionado, desmarcar todos los checkboxes
                if (!anySelected) {
                    var checkboxes = document.querySelectorAll('input[name="SelectedOfferStatus"]');
                    checkboxes.forEach(function (checkbox) {
                        checkbox.checked = false;
                    });
                }
            }

            // Agregar evento click a cada div
            divTags.forEach(function (div) {
                div.addEventListener('click', function () {
                    var selectedId = div.getAttribute('data-sel-id'); // Obtener el data-selected-id del div
                    var checkbox = document.getElementById(selectedId); // Encontrar el checkbox por su id

                    if (checkbox) {
                        // Cambiar el estado de selección del div (agregar/eliminar la clase "sel")
                        div.classList.toggle('sel');
                        // Actualizar el estado de los checkboxes según la selección
                        updateSelectionState();
                        reinitPage();
                        $("#searchOffersMobile").submit();
                        loadingFullShow();

                    }
                });
            });

            // Llamar a la función updateSelectionState al cargar la página
            updateSelectionState();
        });

        function handleOfferStatusChange(selectElement, idOffer) {
            var opcionSeleccionada = selectElement.value;
            var idOffer = idOffer;
            var convertOffer = @Convert.ToInt16(OfferActionEnum.ChangeStatus);

            $(".show_popup").removeClass("show_popup");
            $(".overlay").removeClass("hide");

            switch (opcionSeleccionada) {
                case "2":
                    offersAction(idOffer, convertOffer, opcionSeleccionada);
                    break;
                case "4":
                    deleteAction(idOffer, convertOffer, opcionSeleccionada);
                    break;
                case "6":
                    offersAction(idOffer, convertOffer, opcionSeleccionada);
                    break;
                default:
                    break;
            }
        }
    </script>


    @Scripts.Render("~/bundles/js/company/offer/offermembership")
}
