@model Redarbor.Company.WebUI.Models.SimplifiedRegister.SimplifiedRegisterDataModel
@using Redarbor.Company.WebUI.Helpers.Html;
@using Redarbor.Company.WebUI.Helpers;
@using Redarbor.Common.Entities.Enums;

@{
    int pageId = Html.SetCurrentPage((short)PageEnum.CompanyPreRegister);
    var portalConfig = PortalConfigHelper.GetPortalConfiguration();
}

@using (Html.BeginForm("SimplifiedPreRegister", "PreRegister", FormMethod.Post, new { enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()
    @Html.HiddenFor(model => model.PreRegisterDataModel.CompanyIdPreRegisterEncrypt, new { @id = "CompanyIdPreRegisterEncrypt", Name = "PreRegisterDataModel.CompanyIdPreRegisterEncrypt" })
    @Html.HiddenFor(model => model.PreRegisterDataModel.UserId, new { Name = "PreRegisterDataModel.UserId" })
    <div class="box w50 mAuto mt40 w100_m">
        <p class="mb15 fwB fs18 tc">Hola @Model.PreRegisterDataModel.ContactFullName</p>
        <p class="mbB tc fs16">@PageLiteralsHelper.GetLiteral("LIT_NEW_PRE_REGISTER_PUBLISH", pageId, portalConfig)</p>
        <div class="w50 mAuto w100_m tc_m">
            <img class="mbB" src="~/c/v2/img/landing/landing-pre-registro.svg" />
            <label class="w100 fwB dB tl_m">
                <svg class="mr5 vt" xmlns="http://www.w3.org/2000/svg" xmlns:v="https://vecta.io/nano" width="16" height="18" fill="#4b5968" fill-rule="nonzero"><path d="M7.999.225c2.213 0 4.01 1.742 4.01 3.878v1.553h1.638c1.174 0 2.128.944 2.128 2.103v7.914c0 1.159-.954 2.103-2.128 2.103H2.353c-1.174 0-2.128-.944-2.128-2.103V7.759c0-1.159.954-2.103 2.128-2.103h1.635V4.103c0-2.137 1.798-3.878 4.01-3.878zm5.649 6.758H2.353a.78.78 0 0 0-.784.776v7.914a.78.78 0 0 0 .784.776h11.294a.78.78 0 0 0 .784-.776V7.759a.78.78 0 0 0-.784-.776zM7.999 1.327c-1.592 0-2.892 1.243-2.892 2.776v1.553h5.783V4.103c0-1.533-1.299-2.776-2.892-2.776zM8 9.458c.678 0 1.226.542 1.226 1.21 0 .416-.215.797-.564 1.018l-.104.19v1.701c0 .304-.25.551-.559.551s-.559-.247-.559-.551v-1.703l-.104-.19c-.348-.222-.561-.601-.561-1.017 0-.668.548-1.21 1.226-1.21z" /></svg>
                @PageLiteralsHelper.GetLiteral("LIT_PRE_REGISTER_COMPLETE_PASS", pageId, portalConfig)
            </label>
            <div class="field_input_button mb15">
                <div class="cont">
                    @Html.PasswordFor(model => model.PreRegisterDataModel.Password, new { @id = "txtPwd", @class = "", autocomplete = "new-password", Name = "PreRegisterDataModel.Password", @placeholder = @PageLiteralsHelper.GetLiteral("PH_PASS", pageId, portalConfig) })
                    <span class="">
                        <span class="icon_tooltip bottom iEye">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:v="https://vecta.io/nano" width="18" height="10"><path d="M13.243.937c1.857.823 3.495 2.108 4.655 3.741.137.193.137.453-.001.646C15.741 8.347 12.47 10 8.871 10c-1.363 0-2.643-.239-3.838-.717-.284-.114-.422-.437-.309-.722s.435-.424.719-.31c1.062.425 2.203.638 3.428.638 3.101 0 5.918-1.354 7.86-3.856l.024-.032-.054-.07c-.965-1.215-2.242-2.198-3.678-2.873l-.228-.104c-.28-.124-.406-.452-.283-.733s.45-.408.73-.284zM8.871 0c.823 0 1.612.087 2.367.26.298.069.484.367.416.666s-.365.486-.663.417c-.672-.155-1.378-.232-2.119-.232-2.92 0-5.383 1.206-7.42 3.648l-.196.24.124.154c.648.787 1.338 1.449 2.07 1.986l.221.157c.252.174.315.52.142.773a.55.55 0 0 1-.77.143C1.978 7.475 1.001 6.516.112 5.335c-.149-.198-.149-.471-.001-.669C2.437 1.568 5.37 0 8.871 0zm1.892 2.972c.555.522.876 1.25.876 2.028 0 1.534-1.239 2.778-2.768 2.778-.733 0-1.423-.289-1.937-.794-.218-.215-.222-.566-.008-.786a.55.55 0 0 1 .783-.008 1.65 1.65 0 0 0 1.163.477c.917 0 1.661-.746 1.661-1.667 0-.468-.192-.904-.526-1.217-.223-.21-.234-.561-.025-.785a.55.55 0 0 1 .782-.025zm-1.834-.71c.306.011.545.268.534.575s-.267.547-.573.536c-.917-.032-1.686.688-1.718 1.607-.011.307-.267.547-.573.536s-.545-.268-.534-.575C6.119 3.408 7.4 2.209 8.928 2.262z" fill="#999" /></svg>
                            <span>@PageLiteralsHelper.GetLiteral("LIT_ERR_LENGTH_BETWEEN_4_50", pageId, portalConfig)</span>
                        </span>
                    </span>
                </div>
                @Html.ValidationMessageFor(m => m.PreRegisterDataModel.Password, "", new { data_valmsg_for = "PreRegisterDataModel.Password" })
            </div>
            <button id="submit_register" class="submit_n b_primary mbB  w100 js-processing">@PageLiteralsHelper.GetLiteral("LIT_PRE_REGISTER_SIMPLIFIED_SUBMIT", pageId, portalConfig)</button>
        </div>
        <div class="fs13 tc">
            <p>
                @PageLiteralsHelper.GetLiteral("LBL_CONDICIONES1", pageId, portalConfig)
                <a href="@string.Format("{0}/avisolegal/", portalConfig.url_web)">
                    @PageLiteralsHelper.GetLiteral("LIT_LNK_AVISOLEGAL", pageId, portalConfig)
                </a>
                @if (portalConfig.PortalId == (short)PortalEnum.ComputrabajoColombia)
                {
                    @PageLiteralsHelper.GetLiteral("LIT_SEP_1", pageId, portalConfig)
                    <a href="@string.Format("{0}/prestaciondeservicio/", portalConfig.url_web)">
                        @PageLiteralsHelper.GetLiteral("LIT_LNK_PRESTACION_SERVICIOS", pageId, portalConfig)
                    </a>
                }
                @PageLiteralsHelper.GetLiteral("LIT_SEP_2", pageId, portalConfig)
                <a href="@string.Format("{0}/privacidad/", portalConfig.url_web)">
                    @PageLiteralsHelper.GetLiteral("LIT_LNK_PRIVACIDAD", pageId, portalConfig)
                </a>
                @PageLiteralsHelper.GetLiteral("LIT_SEP_3", pageId, portalConfig)
            </p>

            <a id="pd" class="js_show js_show_legal">@PageLiteralsHelper.GetLiteral("CHECKBOX_INFO_GDPR", pageId, portalConfig)</a>
            <div class="box_border mt15 js_box_legal tl hide">
                <p class="fw_b m0"><strong>@PageLiteralsHelper.GetLiteral("LIT_BASIC_INFO", pageId, portalConfig)</strong></p>

                <p class="fw_b mt10 mb5"><strong>@PageLiteralsHelper.GetLiteral("LIT_RESPONSABLE", pageId, portalConfig)</strong></p>
                <p>@PageLiteralsHelper.GetLiteral("LIT_DGNET", pageId, portalConfig)</p>

                <p class="fw_b mt10 mb5"><strong>@PageLiteralsHelper.GetLiteral("LIT_FINALIDAD", pageId, portalConfig)</strong></p>
                <p>@PageLiteralsHelper.GetLiteral("LIT_FINALIDAD_DESC", pageId, portalConfig)</p>

                <p class="fw_b mt10 mb5"><strong>@PageLiteralsHelper.GetLiteral("LIT_LEGITIMACION", pageId, portalConfig)</strong></p>
                <p>@PageLiteralsHelper.GetLiteral("LIT_LEGITIMACION_DESC", pageId, portalConfig)</p>

                <p class="fw_b mt10 mb5"><strong>@PageLiteralsHelper.GetLiteral("LIT_DESTINATARIO", pageId, portalConfig)</strong></p>
                <p>@PageLiteralsHelper.GetLiteral("LIT_DESTINATARIO_DESC", pageId, portalConfig)</p>

                <p class="fw_b mt10 mb5"><strong>@PageLiteralsHelper.GetLiteral("LIT_DERECHOS", pageId, portalConfig)</strong></p>
                <p>@PageLiteralsHelper.GetLiteral("LIT_DERECHOS_DESC", pageId, portalConfig)</p>
            </div>
        </div>
    </div>

    if (portalConfig.ShowAcceptInformationGdpr)
    {
        <label class="mt10 fs12">
            @Html.CheckBoxFor(m => m.IsCheckedAdditionalConditions, new { @id = "ckConditionsAditionals" })
            @PageLiteralsHelper.GetLiteral("LIT_ADDITIONAL_CONDITIONS", pageId, portalConfig)
        </label>
    }
    <div class="tooltip_w">
        <div class="js_box_legal hide tooltiptext">

        </div>
    </div>

}
<script>
    window.onload = function () {
        history.pushState(null, null, window.location.href);
        window.addEventListener('popstate', function (event) {
            history.pushState(null, null, window.location.href);
        });
    };

    $('.iEye').click(function () { var input = $(this).closest('.field_input_button').find('input'); if (input.attr('type') == 'password') { input.attr('type', 'text'); } else { input.attr('type', 'password'); } });
    var urlActions = {
        addTimeLinePassword: '@Url.Action("AddTimeLineWritePassword", "PreRegister")',
    };
    var preRegisterConfigModel = {
        urlActions: urlActions
    };
    InitializePreRegister(preRegisterConfigModel);
</script>

