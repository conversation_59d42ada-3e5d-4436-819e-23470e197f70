@model Redarbor.Company.WebUI.Models.Company.Home.HomeMatchesDataModel
@using Redarbor.Company.WebUI.Helpers
@using Redarbor.Common.Entities.Enums
@using Redarbor.Master.Entities.Enums

@{
    int PageId = (int)PageEnum.MatchOffer;
    var portalConfig = PortalConfigHelper.GetPortalConfiguration();
    var companyCredentials = SecurityHelper.GetCompanyCredentials();
    var companyProduct = CompanyHelper.GetCurrentProduct(companyCredentials);    
}
<section class="gescan_ep1 bgBlueMbl ">
    <div class="box tc p30">
        @if (companyProduct.GroupId == (short)ProductGroupsEnum.Membership)
        {
            <h3 class="fs22 fw_b mb20">@PageLiteralsHelper.GetLiteral("H3_CANDIDATOS_SIN_ACCESO", PageId, portalConfig)</h3>
            <p class="mb30 w_80 mAuto w_100r">@PageLiteralsHelper.GetLiteral("LIT_CANDIDATOS_CADUCADOS", PageId, portalConfig)</p>
        }
        else
        {
            <h3 class="fs22 fw_b mb20">@PageLiteralsHelper.GetLiteral("H3_OFERTA_SIN_ACCESO_CVS", PageId, portalConfig)</h3>
            <p class="mb30 w_80 mAuto w_100r">
                @if (Model.PublisedOffers <= 10 && String.IsNullOrEmpty(Model.ContactMail))
                {
                    @Html.Raw(PageLiteralsHelper.GetLiteral("LIT_PARA_CONTINUAR_ACCEDIENDO_PAGA", PageId, portalConfig))
                }
                else
                {
                    @Html.Raw(PageLiteralsHelper.GetLiteral("LIT_PARA_CONTINUAR_ACCEDIENDO_PAGA_MEMBRESIA", PageId, portalConfig))
                }
            </p>
            <div style="max-width: 375px;" class="mAuto">
                <div class="cols mb10">
                    <div>
                        <img srcset="@Url.Content(string.Format("{0}img/mails.svg{1}", portalConfig.PrefixPathImage, portalConfig.SufixPathImage))" />
                    </div>
                    <div class="vm w_75 tl pl15px">
                        <a href="@Url.Action("Index", "Contact", new { idc = EncryptationHelper.Encrypt(companyCredentials.IdCompany.ToString()) })" class="w_100">
                            @PageLiteralsHelper.GetLiteral("LIT_TECHNICAL_SUPPORT_URL", PageId, portalConfig)
                        </a>
                        @Html.Partial("~/Views/Shared/Partials/_ContactButtons.cshtml", new ViewDataDictionary { { "origin", "_InfoCanNotViewMatches" }, { "pageid", PageId }, { "companycredentials", companyCredentials }, { "portalConfig", portalConfig } })
                    </div>
                </div>
            </div>
        }
    </div>
</section>