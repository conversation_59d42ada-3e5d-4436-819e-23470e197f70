
@model Redarbor.Company.WebUI.Models.Company.Home.HomeMatchesDataModel
@using Redarbor.Company.WebUI.Helpers
@using Redarbor.Company.WebUI.Enums
@using Redarbor.Common.Entities.Enums
@using Redarbor.Master.Entities.Enums


@{
    short pageId = (int)PageEnum.MatchOffer;
    var portalConfig = PortalConfigHelper.GetPortalConfiguration();
    var companyCredentials = SecurityHelper.GetCompanyCredentials();
    var SeeFilterExclusionMessage = (string)ViewData["SeeFilterExclusionMessage"];
    bool ShowPunctuationColumn = ViewData["ShowPunctuationColumn"] != null ? (bool)ViewData["ShowPunctuationColumn"] : false;
    var hasMatches = Model.TotalMatches > 0;
}


@if (hasMatches)
{
    <p class="fc_link tc mtB_neg">@Model.TotalCandidates</p>
}

<div class="plB prB pbB">
    @if (!Model.HasNewFreemiumChat && hasMatches)
    {
        <div class="box_border pAll5 tc fs12 mtB">
            @PageLiteralsHelper.GetLiteral("LIT_OFERTA_BASICA", pageId, portalConfig): @PageLiteralsHelper.GetLiteral("LIT_ACCESO", pageId, portalConfig)
            <strong>@PageLiteralsHelper.GetLiteral("LIT_LIMITADO", pageId, portalConfig)</strong>
            @string.Format(@PageLiteralsHelper.GetLiteral("LIT_N_PRIMEROS_CANDIDATOS", pageId, portalConfig), Model.MaxAppliesToSee)
        </div>
    }
    @if (hasMatches)
    {
        <div class="box_filters mb10">
            @if (portalConfig.ShowMultifiltersTags)
            {
                <div class="tag tag_icon mr10" onclick="showPopupAllFilters()">
                    <span class="icon i_filters"></span>
                    <span id="bulletFilter" class="bullet hide"></span>
                </div>
                @Html.EditorFor(m => m.MultifiltersDataModel, "MatchesFiltersMobile", new { Model.IsNuggetCv, Model.IsNewFiltersActive, Model.IsMemberShip, Model.OfferIdCompanyProduct, Model.IsForeign, Model.OfferIdEncrypted, Model.FolderSelectedEncrypted })
                if (Model.MultifiltersDataModel.IsFiltered)
                {
                    <div class="hide cvsMulti">
                        @Html.EditorFor(m => Model.MultifiltersDataModel, "CvsMultifilterTags")
                    </div>
                }
            }

            @if (Model.HasFolders)
            {
                foreach (var folder in Model.Folders)
                {
                    <div class="tag mr10 @(Model.FolderSelected == folder.Position ? "sel" : "")" id="@folder.PositionEncrypted">
                        @if (folder.Position == (int)CompanyOfferFolderEnum.BBDD)
                        {
                            <a id="@folder.AnchorName" href="@Url.Action("Index", "CompanyMatches", new { oi = Model.OfferIdEncrypted, cf = folder.PositionEncrypted })">
                                <p>@folder.NameFolder</p>
                            </a>
                        }
                        else if (folder.Count > 0)
                        {
                            <a id="@folder.AnchorName" href="@Url.Action("Index", "CompanyMatches", new { oi = Model.OfferIdEncrypted, cf = folder.PositionEncrypted })">
                                <p>@string.Format("{0}({1})", folder.NameFolder, StringToolsHelper.ChangeGroupPointForDecimalPoint(folder.Count.ToString("N0"), portalConfig.PortalId))</p>
                            </a>
                        }
                        else
                        {
                            <p>@folder.NameFolder (0)</p>
                        }
                    </div>
                }
                <div class="tag mr10 showConvertToCompleteCompPopUpBtt" data-idoffer="@Model.OfferIdEncrypted" data-featureprod="@Model.ProdCWCVBBDD">
                    <a>
                        @PageLiteralsHelper.GetLiteral("LIT_BBDD_HDV", pageId, portalConfig)
                    </a>
                </div>
            }
        </div>
    
        foreach (var match in Model.Matches)
        {
            Html.RenderPartial("_MatchesMobile", match, new ViewDataDictionary {
            { "portalConfig", portalConfig },
            { "companyCredentials", companyCredentials},
            {"haveOfferCompletOrMembership", Model.HaveOfferCompletOrMembership },
            {"ShowPunctuationColumn", Model.ShowPunctuationColumn },
            {"categoriesEncrypted", Model.MultifiltersDataModel.IdsProfesionalCategoriesSelected},
            {"provincesEncrypted", Model.MultifiltersDataModel.IdsLocalizationsSelected},
            {"folderEncrypted", Model.FolderSelectedEncrypted },
            {"hasFolders", Model.HasFolders },
            {"hasNewMatchesNotificationsFeature", Model.HasNewMatchesNotificationsFeature}
            });
        }
        @Html.DisplayFor(i => i.Pager, new { portalConfig, formIdToSubmit = "MatchesList" })
    }
    else
    {
        <div class="box_shadow fs14 fc_aux tc mtB">@PageLiteralsHelper.GetLiteral("NO_CANDIDATES_TEXT", pageId, portalConfig)</div>
    }
</div>



