@using Redarbor.Company.WebUI.Helpers;
@using Redarbor.Common.Entities.Enums;
@{
    var idPopUp = (string)ViewData["idPopUp"];
    var idClosePopUp = (string)ViewData["idClosePopUp"];
    var titlePopUp = (string)ViewData["titlePopUp"];
    var descriptionPopUp = (string)ViewData["descriptionPopUp"];
    var comment = (string)ViewData["comment"];
    var PageId = (int)PageEnum.SiteMasterCompany;
    var portalConfig = PortalConfigHelper.GetPortalConfiguration();
    var companycredentials = SecurityHelper.GetCompanyCredentials();
}


<div id="@idPopUp" class="popup flex hide js_box_soporte">
    <div class="w40">
        <div>
            <h2>@titlePopUp</h2>
            <p class="mt15 mb20 tc">@Html.Raw(descriptionPopUp)</p>
            <div class="mAuto pbB" style="max-width: 375px;">
                <div class="cols mb10">
                    <div>
                        <img srcset="@Url.Content(string.Format("{0}img/mails.svg{1}", portalConfig.PrefixPathImage, portalConfig.SufixPathImage))" />
                    </div>
                    <div class="vm w75 tl pl15">
                        <a href="@Url.Action("Index", "Contact", new { idc = EncryptationHelper.Encrypt(companycredentials.IdCompany.ToString()) })" class="w_100">
                            @PageLiteralsHelper.GetLiteral("LIT_TECHNICAL_SUPPORT_URL", PageId, portalConfig)
                        </a>
                    </div>
                </div>
                <div class="cols">
                    <div>
                        <img srcset="@Url.Content(string.Format("{0}img/telef.svg{1}", portalConfig.PrefixPathImage, portalConfig.SufixPathImage))" />
                    </div>
                    <div class="vm w75 tl pl15">
                        @Html.Partial("~/Views/Shared/Partials/_ContactButtons.cshtml", new ViewDataDictionary { { "origin", "_SupportPopUpNewPem" }, { "pageid", PageId }, { "companycredentials", companycredentials }, { "portalConfig", portalConfig }, { "comment", comment } })                       
                    </div>
                </div>
            </div>
        </div>
        <a class="icon i_close icon_tooltip js_hide_soporte"><span>Cerrar</span></a>
    </div>
</div>

<div id="divSupportConfirmedDeliveryPopUpNewPem" class="popup flex hide js_box_mail_enviado js_blocked">
    <div class="w40 pbB">
        <div style="cursor: default;" id="loadingSupportPopUpNewPem" class="">
            <div class="loading"><div><span></span><span></span><span></span><span></span></div><div><span></span><span></span><span></span><span></span></div><div><span></span><span></span><span></span><span></span></div></div>
            <p class="tc">@PageLiteralsHelper.GetLiteral("LIT_PROCESSING", PageId, portalConfig)</p>
        </div>
        <div id="contentConfirmSupportPopUpNewPem">
            <h2 class="fc_ok">@PageLiteralsHelper.GetLiteral("LIT_THANKS", PageId, portalConfig)</h2>
            <p class="tc">@PageLiteralsHelper.GetLiteral("LIT_SEND_FORM_SF", PageId, portalConfig)</p>
            <a class="icon i_close icon_tooltip js_hide_mail_enviado posAbs" style="top:15px; right:15px"><span>@PageLiteralsHelper.GetLiteral("LIT_CERRAR", PageId, portalConfig)</span></a>
        </div>
    </div>
</div>