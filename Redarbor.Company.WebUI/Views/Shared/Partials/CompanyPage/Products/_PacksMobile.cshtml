@model List<Redarbor.Company.WebUI.Models.Company.Product.ProductDataModel>
@using Redarbor.Company.WebUI.Helpers;
@using Redarbor.Common.Entities.Enums
@using Redarbor.Master.Entities.Enums

@{
    int pageId = (int)ViewData["pageId"];
    var offerId = ViewData["io"] != null ? (string)ViewData["io"] : string.Empty;
    var isConvertToComplete = ViewData["cc"] != null ? (string)ViewData["cc"] : string.Empty;
    var companyHasPrime = ViewData["hasPrime"] != null ? (bool)ViewData["hasPrime"] : false;

    var portalConfig = PortalConfigHelper.GetPortalConfiguration();
    var urlCart = string.Empty;
    var litBuy = PageLiteralsHelper.GetLiteral("LIT_COMPRAR", pageId, portalConfig);
    var litRecommended = PageLiteralsHelper.GetLiteral("LIT_RECOMMENDED", pageId, portalConfig);
    var litNameProduct = PageLiteralsHelper.GetLiteral("LIT_NAME_PRODUCT", pageId, portalConfig);
    var litPromotion = PageLiteralsHelper.GetLiteral("LIT_PROMOTION", pageId, portalConfig);
    var litLandingTag = PageLiteralsHelper.GetLiteral("LIT_LANDING_TAG", pageId, portalConfig);
    var litOffer = PageLiteralsHelper.GetLiteral("OFERT", pageId, portalConfig);
    var litSeeDateil = PageLiteralsHelper.GetLiteral("LIT_SEE_DETAIL_PACK", pageId, portalConfig);
    var litPromotionInfo = PageLiteralsHelper.GetLiteral("LIT_PROMOTION_INFO", pageId, portalConfig);
    var litAvailableOffers = PageLiteralsHelper.GetLiteral("AVAILABLE_OFFERS", pageId, portalConfig);
    var litAvailableOneOffer = PageLiteralsHelper.GetLiteral("AVAILABLE_OFFER", pageId, portalConfig);
    var litConvertBtt = PageLiteralsHelper.GetLiteral("LIT_CONVERT_BTT", pageId, portalConfig);
    var litAvailableCvs = PageLiteralsHelper.GetLiteral("LIT_AVAILABLE_CVS", pageId, portalConfig);
    var litFind = PageLiteralsHelper.GetLiteral("LIT_FIND", pageId, portalConfig);
    var litBtnPublicarOferta = PageLiteralsHelper.GetLiteral("BTN_PUBLICAR_OFERTA", pageId, portalConfig);
    var litFrom = PageLiteralsHelper.GetLiteral("LIT_DESDE", pageId, portalConfig);
    var litByCv = PageLiteralsHelper.GetLiteral("LIT_BY_CV", pageId, portalConfig);
    var litByOffer = PageLiteralsHelper.GetLiteral("LIT_POROFERTA", pageId, portalConfig);
    var litPriceWithoutIva = PageLiteralsHelper.GetLiteral("LIT_PRICE_WITHOUT_IVA", pageId, portalConfig);

    var litBuyAndGetOneYear = PageLiteralsHelper.GetLiteral("LIT_BUY_AND_GET_ONE_YEAR", pageId, portalConfig);

    var litDiscount = PageLiteralsHelper.GetLiteral("LIT_DISCOUNT", pageId, portalConfig);
    var litPartnerPrime = PageLiteralsHelper.GetLiteral("LIT_PARTNER_PRIME", pageId, portalConfig);
    var litFreeTwelveMonth = PageLiteralsHelper.GetLiteral("LIT_FREE_TWELVE_MONTHS", pageId, portalConfig);
    var litAfterSecondBuy = PageLiteralsHelper.GetLiteral("LIT_AFTER_SECOND_BUY", pageId, portalConfig);
    var litSelectOffer = PageLiteralsHelper.GetLiteral("LIT_SELECT_OFFER", pageId, portalConfig);
    var litJoinFree = PageLiteralsHelper.GetLiteral("LIT_JOIN_FREE", pageId, portalConfig);

}

@foreach (var product in Model)
{

    var printArticleClass = "";
    var idPopUpDetail = string.Format("#viewDetailPack_{0}", product.Id);
    var isOfferExpressProduct = product.SubGroupId == (short)ProductSubGroupsEnum.Express;

    if (product.ProductIsPrime || companyHasPrime)
    {
        printArticleClass = companyHasPrime ? "add_info_top" : " bg_partner_light";
    }
    else
    {
        printArticleClass = product.Recommended || product.HasPromotion ? "promo" : product.ShowTagPromo || isOfferExpressProduct ? "new_feature" : string.Empty;
    }

    if (product.ProductIsPrime)
    {
        <div class="box_fix_prime has_menu" onclick="popupDetail('#popupDetailPartnerPrime')">
            <div class="button icon i_diamon bg_white fc_partner mr10"></div>
            <div class="">@litJoinFree <span class="ff_nexa">@litPartnerPrime</span> <span class="icon i_info ml5"></span></div>
        </div>

        @Html.Partial("PopUpsMobile/_PrimePopupMobile", product, new ViewDataDictionary { { "pageId", pageId } })
    }
    else
    {
<article class="pack_alpha mtB @(printArticleClass)">

    @if (companyHasPrime)
    {
        <span class="box_top_info bg_partner_light fc_partner fs16">@String.Format("{0:0}", product.Saving)%  @litDiscount  <span class="ff_nexa">@litPartnerPrime</span></span>
    }
    else if (product.HasPromotion)
    {
        <span class="tag new">@litPromotion</span>
    }
    else if (product.Recommended)
    {
        <span class="tag new">@litRecommended</span>
    }
    else if (product.ShowTagPromo || isOfferExpressProduct)
    {
        <span class="tag new_feature">@litLandingTag</span>
    }
    @if (!product.ProductIsPrime)
    {
        <header>
            @if (portalConfig.AEPortalConfig.PostPublishWithConsumables || product.IsBBDDcvConsumableProduct)
            {
                <h3 class="fs20 fwB">@litNameProduct</h3>
            }
            else
            {
                <h3 class="fs20 fwB">@litOffer @ProductDataModelHelper.GetTitleProductCapitalizeFirstLetter(product)</h3>
            }
            @if (product.HasPromotion)
            {
                <div class="tag dto fs14 mt10">
                    <span class="fs18">@String.Format("{0:0}", product.Saving)%</span>
                    @litPromotionInfo
                </div>
            }
            else
            {
                <p class="fs14 mt5">@product.LandingProductSubtitle</p>
            }
            <div class="fc_aux_light fs14 mt10">
                @litFrom
                <span class="fs30 fwB fc_base mr5 ml5">@Html.Raw(product.HasPromotion ? product.LiteralShowTotalPromotion.Replace(" ", string.Empty) : product.LiteralShowUnit.Replace(" ", string.Empty))</span>
                @if (product.HasPromotion)
                {
                    <span class="tdL">(@Html.Raw(product.UnitPriceWithoutPromotion.Replace(" ", string.Empty)))</span>
                }
                @if (product.IsBBDDcvConsumableProduct)
                {
                    @litByCv
                }
                else
                {
                    @litByOffer
                }

            </div>
            <a href="@Url.Action("Index", "MultiPurchaseCart", new
                {
                    prod = EncryptationHelper.Encrypt(product.Id.ToString()),
                    p = product.CompanyProductOfferDataModel.GroupId == (int)ProductGroupsEnum.Membership ? EncryptationHelper.Encrypt(((int)PageEnum.MembershipCart).ToString()) : EncryptationHelper.Encrypt(((int)PageEnum.PackCart).ToString()),
                    btn = EncryptationHelper.Encrypt(product.BtnOriginId.ToString())
                })" class="b_primary mtB">@litBuy</a>
                <a class="mtB dB" onclick="popupDetail('@idPopUpDetail')">@litSeeDateil</a>
                @Html.Partial("PopUpsMobile/_ViewDetailPackPopUpMobile", product, new ViewDataDictionary { { "pageId", pageId }, { "isConvertToComplete", isConvertToComplete } })
            </header>
        }
</article>
    }
}
@if (Model.Any(x => x.AvailabeUnits == 0))
{
    <div class="fc_aux_light fs14 tc mtB">@litPriceWithoutIva</div>
}
