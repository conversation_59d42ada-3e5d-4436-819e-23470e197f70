@model Redarbor.Company.WebUI.Models.Company.Home.HomeConversationsDataModel
@using Redarbor.Company.WebUI.Enums
@using Redarbor.Company.WebUI.Helpers
@using Redarbor.Common.Entities.Enums

@{
    short PageId = (short)PageEnum.Messages;
    var portalConfig = PortalConfigHelper.GetPortalConfiguration();
}

<div id="candidatesByOfferSection" class="list h100 @(Model.NavigationControl.ChatNavigationPoint == ChatNavigationPointEnum.CandidatesByOffer ? "": "hide")">
    <ul class="chat-list clicked">
        <li class="offer js_dropw_down">
            <span class="fwB">
                <p id="TitleOfferCandidatesByOffer"></p>
                <p id="localizationAndCityCandidatesByOffer" class="fwN mt5"></p>
            </span>
            <span id="backCandidatesByOffer" class="icon i_next mlAuto"></span>
        </li>
    </ul>
    <div id="candidatesByOfferList">
        @if (Model.NavigationControl.ChatNavigationPoint == ChatNavigationPointEnum.CandidatesByOffer
            && Model.Conversations.Any())
        {
            @Html.Partial("Conversation/_ConversationCandidatesByOffer", Model.Conversations, new ViewDataDictionary { { "idConverSelected", Model.SelectedConversation } });
        }
    </div>

    <div id="PaginationForCandidatesByOffer" class="dFlex mt10 @(Model.NavigationControl.CandidatesByOfferPageControl.HasPagination ? "" : "hide")">
        <div class="w49"><a id="candidatesByOfferBefore" class="b_aux small w100 disabled">@PageLiteralsHelper.GetLiteral("LIT_CONVER_BEFORE", PageId, portalConfig)</a></div>
        <div class="w49 mlAuto"><a id="candidatesByOfferNext" class="b_primary small w100">@PageLiteralsHelper.GetLiteral("LIT_CONVER_NEXT", PageId, portalConfig)</a></div>
    </div>
</div>