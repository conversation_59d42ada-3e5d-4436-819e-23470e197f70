
@model Redarbor.Company.WebUI.Models.Company.Conversations.ContentMessageDataModel
@using Redarbor.Company.WebUI.Helpers
@using Redarbor.Common.Entities.Enums
@using Redarbor.Master.Entities.Enums

@{
    short PageId = (short)PageEnum.Messages;
    var portalConfig = PortalConfigHelper.GetPortalConfiguration();
    var companyCredentials = SecurityHelper.GetCompanyCredentials();
    var urlCV = Url.Action("Index", "CompanyMatchCvDetail", new
    {
        ims = Model.CurrentConversation.Match.IdEncrtypted,
        oi = Model.CurrentConversation.IdOfferEncrypted,
        cf = Model.CurrentConversation.Match.CompanyFolderIdEncrypted
    });
    var urlOffer = Url.Action("Index", "CompanyMatches", new { oi = Model.CurrentConversation.IdOfferEncrypted, cf = Model.CurrentConversation.Match.CompanyFolderIdEncrypted });

    var classTextMessageDisabled = Model.CandSendMessages ? "" : "disabled";
    var buttonSendDisabled = Model.CandSendMessages ? "i_send_big" : "i_send_big_dis";
    var showMessageInfo = !String.IsNullOrEmpty(Model.MessageInformation);
    bool hasConversationCharged = Model.CurrentConversation.IdConversation > 0;
    var classHide = hasConversationCharged ? "" : "hide";
    var litInputplaceHolder = Model.CandSendMessages ? PageLiteralsHelper.GetLiteral("LIT_ESCRIBE", PageId, portalConfig) : string.Empty;
    bool hasMessages = hasConversationCharged && Model.Messages.Any();
    var classHideMessageInitial = !hasMessages ? "" : "hide";

    var logoCompany = (string)ViewData["logoCompany"];
    if (string.IsNullOrEmpty(logoCompany))
    {
        logoCompany = CompanyHelper.GetLogoPath(companyCredentials);
    }
}

@if (Model.ShowPromoFreemium)
{
    <div id="bannerDiv" class="bg_brand dFlex vm_fx fc_white pAllB dB_m tc_m banner hide_m @classHide">
        <div class="hide_m"><img src="@Url.Content(String.Format("{0}img/freemium2.png{1}", portalConfig.PrefixPathImage, portalConfig.SufixPathImage))" width="80px"></div>
        <div class="mrAuto plB prB">
            <p class="fs16 mb10_m"><strong>@PageLiteralsHelper.GetLiteral("LIT_CONVER_BANNER_CONTACT_1", PageId, portalConfig)</strong> @PageLiteralsHelper.GetLiteral("LIT_CONVER_BANNER_CONTACT_2", PageId, portalConfig)</p>
        </div>
        <div><a class="b_primary small no_wrap flowHid js_show_convertir_oferta js_hide_chat_freemium" href="@Url.Action("SendToBuyProduct","CompanyConversations", new { oi = Model.CurrentConversation.IdOfferEncrypted})">@PageLiteralsHelper.GetLiteral("LIT_CONVER_BETTER_OFFER", PageId, portalConfig)</a></div>
    </div>
}

<div id="idTitleChat" class="title_chat @classHide">
    <a class="icon i_prev hide dIB_m mr15_m"></a>
    <div class="user_photo mr15 hide_m">

        @if (String.IsNullOrEmpty(Model.CurrentConversation.Candidate.Photo))
        {
            <span class="icon i_candidato_empty_big"></span>
        }
        else
        {
            if (Model.CurrentConversation.Candidate.Photo.StartsWith("http"))
            {
                <img src="@Model.CurrentConversation.Candidate.Photo" alt="@Model.CurrentConversation.Candidate.Name">
            }
            else
            {
                <img src="@<EMAIL>" alt="@Model.CurrentConversation.Candidate.Name">
            }
        }
    </div>
    <div>
        <p class="fwB fs18">@Model.CurrentConversation.Candidate.Name  @Model.CurrentConversation.Candidate.Surname</p>
    </div>
    <div class="mlAuto">
        @if (Model.CanViewCV)
        {
            <a class="icon_tooltip mr10" href="@urlCV">
                <span class="icon i_candidato_cv"></span>
                <span>@PageLiteralsHelper.GetLiteral("LIT_CONVER_VIEW_CV", PageId, portalConfig)</span>
            </a>
        }

        @if (Model.CurrentConversation.OfferConversationDetail.IsActive)
        {
            <a class="icon_tooltip mr10" href="@urlOffer">
                <span class="icon i_offer"></span>
                <span>@PageLiteralsHelper.GetLiteral("LIT_CONVER_VIEW_OFFER", PageId, portalConfig)</span>
            </a>
        }

        @if (Model.CanDelete)
        {
            <a class="icon_tooltip" id="delConversation">
                <span class="icon i_remove_black"></span>
                <span>@PageLiteralsHelper.GetLiteral("LIT_CONVER_VIEW_DELETE", PageId, portalConfig)</span>
            </a>
        }
    </div>
</div>

<div id="IdConvBoxEnc" data-idconver="@Model.CurrentConversation.IdConversationEncrypted" data-idoffer="@Model.CurrentConversation.IdOfferEncrypted"
     data-type="@Model.CurrentConversation.IdConversationTypeEcnrypted"></div>

<div id="loadingConvers" class="pAll30 hide">
    <div class="box_animating mt20" style="width: 55%; height: 15px;">
        <div class="bg_animating"></div>
    </div>
    <div class="box_animating mt10" style="width: 15%; height: 15px;">
        <div class="bg_animating"></div>
    </div>
    <div class="box_animating mt10" style="width: 35%; height: 15px;">
        <div class="bg_animating"></div>
    </div>
    <div class="box_animating mt10" style="width: 80%; height: 20px;">
        <div class="bg_animating"></div>
    </div>
    <div class="box_animating mt10" style="width: 80%; height: 20px;">
        <div class="bg_animating"></div>
    </div>
    <div class="box_animating mt10" style="width: 60%; height: 15px;">
        <div class="bg_animating"></div>
    </div>
    <div class="box_animating mt10" style="width: 60%; height: 15px;">
        <div class="bg_animating"></div>
    </div>
    <div class="box_animating mt10" style="width: 55%; height: 15px;">
        <div class="bg_animating"></div>
    </div>
    <div class="box_animating mt10" style="width: 40%; height: 15px;">
        <div class="bg_animating"></div>
    </div>
    <div class="box_animating mt20" style="width: 55%; height: 15px;">
        <div class="bg_animating"></div>
    </div>
</div>

@if (!hasConversationCharged)
{
    <div id="Messages" class="no_conv tc h100">
        <span class="icon i_msg_big mAuto"></span>
        <p class="mAuto mt10 w30">@PageLiteralsHelper.GetLiteral("LIT_CONVER_VIEW_SELECT", PageId, portalConfig)</p>
    </div>
}
else
{
    <ul id="Messages">
        <li id="firstLiInfo" class="fc_aux_light fs13 tc_fx">
            @PageLiteralsHelper.GetLiteral("LIT_CONVER_APPLY_CAND_1", PageId, portalConfig) @Model.CurrentConversation.Match.ViewDate.ToString("dd 'de' MMMM 'de' yyyy", new System.Globalization.CultureInfo(portalConfig.cultura)) @PageLiteralsHelper.GetLiteral("LIT_CONVER_APPLY_CAND_2", PageId, portalConfig) @Model.CurrentConversation.OfferConversationDetail.Title  @Model.CurrentConversation.OfferConversationDetail.LocalizationName - @Model.CurrentConversation.OfferConversationDetail.CityName
        </li>

        @if (!Model.Messages.Any())
        {
            <li>
                <div id="MessageInitialEmpty" class="sub_box mt80 pt30 tc w100 @classHideMessageInitial">
                    <img src="@Url.Content(String.Format("{0}img/iniciar-chat.svg{1}", portalConfig.PrefixPathImage, portalConfig.SufixPathImage))" width="300" style="margin-top:-75px;" class="hide_m mbB">
                    <p class=" fs16"> @PageLiteralsHelper.GetLiteral("LIT_CONVER_MESSAGES_EMPTY_1", PageId, portalConfig) <strong>@PageLiteralsHelper.GetLiteral("LIT_CONVER_MESSAGES_EMPTY_2", PageId, portalConfig)</strong></p>
                </div>
            </li>
        }

        @{
            bool isNewMessage = false;
            int contadorNewMessage = 0;
            bool showLineNewMessage = false;
            string dateShowInitial = string.Empty;
            bool showDateShowHead = false;
            int totalMessages = Model.Messages.Count;
            int contadorMessages = 1;
            string messageInformation = string.Empty;
            int idUserTypeActual = 0;
            foreach (var message in Model.Messages)
            {
                // Esta estructura meterla en el modelo si tiene nuevos mensajes pendientes de leer
                if (message.StatusMessage == (int)1 && message.UserType == (short)UserTypeEnum.Candidate)
                {
                    isNewMessage = true;
                    contadorNewMessage++;
                }

                if (isNewMessage && contadorNewMessage == 1) { showLineNewMessage = true; }

                //Contador de dias si es el mismo dia no mostrar, sino mostrar
                if (message.DateShowHead(portalConfig) != dateShowInitial)
                {
                    dateShowInitial = message.DateShowHead(portalConfig);
                    showDateShowHead = true;
                }

                if (!String.IsNullOrEmpty(Model.MessageInformation) && contadorMessages == totalMessages)
                {
                    messageInformation = Model.MessageInformation;
                }


                Html.RenderPartial("Conversation/_ConversationMessages", message,
                new ViewDataDictionary { { "showLineNewMessage", showLineNewMessage },
                                                                         { "showDateShowHead", showDateShowHead },
                                                                         {"messageInformation",messageInformation },
                                                                         {"isSameMessageOwner",(idUserTypeActual == message.UserType) && (!showLineNewMessage && !showDateShowHead)  } });

                showDateShowHead = false;
                showLineNewMessage = false;

                contadorMessages++;
                idUserTypeActual = message.UserType;
            }
        }

    </ul>

}

<!--Sección caja escribir mensaje-->
<div class="box_send_v2 @classHide" id="writtingArea">
    <input id="txtMessage" type="text" placeholder="@litInputplaceHolder" maxlength="499" ondrop="return false;" @classTextMessageDisabled>
    <input id="addMessage" name="data" type="button" class="icon mlAuto @buttonSendDisabled">
</div>
<span id="charNumMessage" class="fs12 fc_ok ml30 posAbs" style="bottom: 3px;" message_chars_ok="@PageLiteralsHelper.GetLiteral("CHAT_MESSAGE_CHARS_OK", PageId, portalConfig)" message_chars_ko="@PageLiteralsHelper.GetLiteral("CHAT_MESSAGE_CHARS_KO", PageId, portalConfig)"></span>
@using (Html.BeginForm(null, null, FormMethod.Post, new { id = "__AjaxAntiForgeryForm" }))
{
    @Html.AntiForgeryToken()
}
<!--Fin Sección caja escribir mensaje-->
<!-- Path: Esta parte es la maquetacion de mobile -->
