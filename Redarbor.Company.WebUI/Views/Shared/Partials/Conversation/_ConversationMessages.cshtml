@model Redarbor.Company.WebUI.Models.Company.Conversations.MessageDataModel
@using Redarbor.Master.Entities.Enums
@using Redarbor.Company.WebUI.Helpers
@using Redarbor.Common.Entities.Enums


@{
    short PageId = (short)PageEnum.Messages;
    var portalConfig = PortalConfigHelper.GetPortalConfiguration();
    var mssgClass = Model.UserType == (short)UserTypeEnum.Company ? "owner" : "";
    var hourClass = Model.UserType == (short)UserTypeEnum.Company ? "" : "fc_aux";

    bool showLineNewMessage = (bool?)ViewData["showLineNewMessage"] ?? false;
    bool showDateShowHead = (bool?)ViewData["showDateShowHead"] ?? false;
    string messageInformation = ViewData["messageInformation"] == null ? String.Empty : (string)ViewData["messageInformation"];
    bool isSameMessageOwner = (bool?)ViewData["isSameMessageOwner"] ?? false;
    var classNoAnswer = isSameMessageOwner && (!showDateShowHead || !showLineNewMessage) ? "no_answer" : "";
    var emptyImage = Model.UserType == (short)UserTypeEnum.Company ? "i_empresa_empty" : "i_candidato_empty";
}

@if (showDateShowHead)
{
    <li class="time_msg time_msg_event">@Model.DateShowHead(portalConfig)</li>
}

@if (showLineNewMessage)
{
    <li><div class="line_info"><span class="mr10 ml10">@PageLiteralsHelper.GetLiteral("LIT_CONVER_NEW_MESSAGE", PageId, portalConfig)</span></div></li>
}

<li class="messageContentChat @mssgClass @classNoAnswer">
    <div class="user_photo mt15">
        @if (!string.IsNullOrEmpty(Model.Image))
        {
            if (Model.Image.StartsWith("http"))
            {
                <img src=@Model.Image class="show" />
            }
            else
            {
                <img src=@<EMAIL> class="show" />
            }
        }
        else
        {
            <span class="icon @emptyImage"></span>
        }
    </div>
    <div id="<EMAIL>" data-id="@Model.IdMessage" class="bubble_box">
        <p>@Model.MessageBody</p>
        <p class="fs12 fr mt5 hide_m @hourClass">@Model.HourShow(portalConfig)</p>
    </div>
</li>

@if (!String.IsNullOrEmpty(messageInformation))
{
    <p class="sub_box"> @messageInformation  </p>
}
