@using Redarbor.Company.WebUI.Helpers
@using Redarbor.Common.Entities.Enums
@using Redarbor.Master.Entities.Enums

@{
    int PageId = (int)PageEnum.PopUpConvertToComplete;
    var portalConfig = (Redarbor.Common.Entities.Configuration.PortalConfig)ViewData["portalConfig"];
    var companyCredentials = (Redarbor.Master.Contracts.ServiceLibrary.DTO.CompanyCredentials)ViewData["companyCredentials"];

    var baseProduct = ViewData["baseProduct"];
    var hasNewFreemiumChat = (bool)(ViewData["HasNewFreemiumChat"] ?? false);
    var textConvertButton = hasNewFreemiumChat ? PageLiteralsHelper.GetLiteral("LIT_CONVERT_OFFER_NEW_CHAT", PageId, portalConfig) : PageLiteralsHelper.GetLiteral("LIT_CONVERT_OFFER", PageId, portalConfig);
    var convertToCompleteDescBasic = PopUpConverToCompleteDescHelper.GetConvertToCompleteDesc(portalConfig, (short)ProductSubGroupsEnum.Freemium, companyCredentials);
    var convertToCompleteDescComplete = PopUpConverToCompleteDescHelper.GetConvertToCompleteDesc(portalConfig, (short)ProductSubGroupsEnum.Complete, companyCredentials);
}

<div id="PopUpComparative" style="display: none">
    <div class="blockUI blockOverlay" style="z-index: 9999; border: none; margin: 0px; padding: 0px; width: 100%; height: 100%; top: 0px; left: 0px; opacity: 0.6; position: fixed; background-color: rgb(0, 0, 0);"></div>
    <div class="blockUI blockMsg blockPage" style="z-index: 9999; position: fixed; padding: 0px; margin: 0px; width: 50%; top: 15%; left: 25%; text-align: center; color: rgb(0, 0, 0); border: 3px solid rgb(170, 170, 170); background-color: rgb(255, 255, 255);">
        <div style="cursor: default;" class="popup_gestof popup_gestcan content_popup convert_offer">
            <div class="md-modal md-effect-3 md-show">
                <div class="md-content w_100" style="overflow-y:auto;max-height:500px">
                    <button onclick="$.unblockUI();"><span title="Cancelar" class="icon ico_close"></span></button>

                    <span class="c_txt pb20">@PageLiteralsHelper.GetLiteral("LIT_POPUP_TITLE", PageId, portalConfig)</span>
                    <div class="dFlex vm_fx p15 mb20">
                        <article class="box_shadow pt15_r pb0i w_48 basica">
                            <span class="tag fw_b">@PageLiteralsHelper.GetLiteral("LIT_POPUP_CURRENT", PageId, portalConfig)</span>
                            <div class="explain bg_bluelight pb10">
                                <h1 class="tc fs24 mb10 plr15 mt0 pt20">@PageLiteralsHelper.GetLiteral("LIT_BASIC_OFFER_PP", PageId, portalConfig)</h1>
                                <p class="tc fc80 plr15 h_25">@PageLiteralsHelper.GetLiteral("LIT_PUBLISHED_TIME", PageId, portalConfig)</p>
                            </div>
                            <ul class="mt0 p10 tl">
                                @foreach (var item in convertToCompleteDescBasic)
                                {
                                    <li class="mt10 dFlex">
                                        <span class="icon @(item.Available==1?"estado_verde_s":"estado_rojo_s")  fl d_ib"></span>
                                        <span class="pl10px w_100">@item.Literal</span>
                                    </li>
                                }
                            </ul>
                        </article>
                        <div class="pr10px pl10px"><span class="icon i_next"></span></div>
                        <article class="box_shadow pt15_r pb0i w_48 completa">
                            <span class="tag fw_b">@PageLiteralsHelper.GetLiteral("LIT_RECOMMENDED_PP", PageId, portalConfig)</span>
                            <div class="explain pb10">
                                <h1 class="tc fs24 mb10 plr15 mt0 pt20">@PageLiteralsHelper.GetLiteral("LIT_COMPLETE_OFFER", PageId, portalConfig)</h1>
                                <p class="tc fc80 plr15 h_25">@PageLiteralsHelper.GetLiteral("LIT_MORE_CANDIDATES_PP", PageId, portalConfig)</p>
                            </div>
                            <ul class="mt0 p10 tl">
                                @foreach (var item in convertToCompleteDescComplete)
                                {
                                    <li class="mt10 dFlex">
                                        <span class="icon @(item.Available==1?"estado_verde_s":"estado_rojo_s")  fl d_ib"></span>
                                        <span class="pl10px w_100">@item.Literal</span>
                                    </li>
                                }
                            </ul>
                        </article>
                    </div>

                    <input id="popUpConverToCompleteBtt" class="submit_n m0_auto fn" data-processing="" type="button" value="@textConvertButton">
                    <div class="submitbtn mt0 pt0i">
                        <input value="@PageLiteralsHelper.GetLiteral("LIT_NOT_NOW", PageId, portalConfig)" class="cancelar_link" type="button" onclick="$.unblockUI();">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<input type="hidden" id="featureprod" value="@baseProduct" />