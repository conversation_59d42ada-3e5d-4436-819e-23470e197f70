@{
    string classes = ViewBag.Classes;
    bool? spin = ViewBag.Spin;
    string spinClass = spin.HasValue && spin.Value ? "spin" : string.Empty;
}

<div class="img_ia @classes">
    <img src="@Url.Content("~/c/v2/img/sherlock_IA23_1.svg")" />
    <svg class="posAbs" xmlns="http://www.w3.org/2000/svg" width="47" height="41" fill-rule="nonzero" xmlns:v="https://vecta.io/nano" style="left:25%;top:25%;">
        <path d="M19.061 40.937c11.118.858 20.859-7.178 22.126-18.077l5.042-3.25a1.68 1.68 0 0 0-.154-2.909l-5.909-2.985A20.64 20.64 0 0 0 22.27.063C10.891-.816.95 7.623.063 18.909s7.62 21.151 18.997 22.028z" fill="#fff"></path>
        <path class="spin" d="M30.429 18.907l-1.562-.567c-.875-.316-1.279-1.311-.875-2.143l.726-1.486a1.55 1.55 0 0 0-.292-1.772l-.015-.015c-.466-.471-1.18-.599-1.782-.32l-1.509.695c-.845.388-1.84-.03-2.147-.902l-.544-1.559a1.57 1.57 0 0 0-1.472-1.049h-.021a1.57 1.57 0 0 0-1.487 1.023l-.572 1.55c-.318.868-1.322 1.271-2.16.868l-1.498-.721a1.58 1.58 0 0 0-1.786.29l-.015.015c-.475.465-.604 1.173-.325 1.77l.703 1.497c.391.836-.03 1.825-.909 2.128l-1.573.539c-.628.215-1.049.802-1.057 1.46v.021a1.56 1.56 0 0 0 1.032 1.475l1.562.565c.875.318 1.281 1.313.875 2.143l-.726 1.486a1.55 1.55 0 0 0 .292 1.772c.004.006.011.013.015.017.466.469 1.182.597 1.784.32l1.509-.697c.842-.388 1.84.03 2.145.902l.544 1.561c.217.623.808 1.04 1.47 1.049h.024a1.57 1.57 0 0 0 1.487-1.023l.57-1.55c.32-.868 1.322-1.269 2.16-.868l1.498.721a1.58 1.58 0 0 0 1.786-.29c.004-.004.013-.011.017-.015a1.55 1.55 0 0 0 .322-1.768l-.703-1.497c-.391-.838.03-1.825.909-2.13l1.573-.539c.628-.215 1.049-.8 1.058-1.46v-.021a1.55 1.55 0 0 0-1.03-1.475zm-5.634 3.056c-.924 2.157-3.433 3.165-5.608 2.249s-3.189-3.406-2.267-5.562 3.433-3.163 5.608-2.249 3.189 3.406 2.267 5.562z" fill="#8480bf"></path>
    </svg>
</div>