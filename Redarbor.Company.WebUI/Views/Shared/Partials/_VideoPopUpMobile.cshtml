

<div class="popup full" id="popupVideoPresentacion">
    <div class="bg_transparent" style="max-width: 100%;">
        <div class="dFlex vm_fx w100" style="height: 100vh">
            <div class="video-wrapper w100">
                <button class="icon i_close_w" style="top:-30px" onclick="closeVideoPlayer()"></button>
                <video id="videoPlayer" playsinline="">
                    <source type="video/mp4">
                    Your browser does not support the video tag.
                </video>

                <div class="video-controls" id="videoControls" style="opacity: 1;">
                    <div class="controls-container">
                        <button class="control-button" id="playPauseButton" onclick="togglePlay()">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 32 32" class="play-icon">
                                <path d="m8.192.602 20.08 11.553c2.97 1.71 2.97 5.98 0 7.69L8.191 31.398C5.22 33.108 1.5 30.972 1.5 27.552V4.448c0-3.42 3.719-5.556 6.692-3.846z" fill="#fff" fill-rule="nonzero"></path>
                            </svg>
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 32 32" class="pause-icon hide"><g fill="#fff" fill-rule="nonzero"><path d="M13 25.495a2.996 2.996 0 0 1-2.995 3.005A3.005 3.005 0 0 1 7 25.495V6.51C7 4.85 8.35 3.5 10.01 3.5A2.996 2.996 0 0 1 13 6.51v18.985zM25 25.495a2.996 2.996 0 0 1-2.995 3.005A3.005 3.005 0 0 1 19 25.495V6.51c0-1.66 1.35-3.01 3.01-3.01A2.996 2.996 0 0 1 25 6.51v18.985z"></path></g></svg>
                        </button>

                        <div class="volume-control">
                            <button class="control-button" id="volumeButton" onclick="toggleMute()">
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 32 26">
                                    <g fill="#fff" fill-rule="nonzero">
                                        <path d="M2.635 6.412A2.633 2.633 0 0 0 0 9.044v7.912a2.633 2.633 0 0 0 2.635 2.632h4.233l6.988 5.943a1.984 1.984 0 0 0 3.269-1.51V1.98a1.984 1.984 0 0 0-3.27-1.512L6.869 6.412H2.635zM20.018 8.161v9.692c2.68 0 4.85-2.17 4.85-4.846a4.848 4.848 0 0 0-4.85-4.846z"></path>
                                        <path d="M19.838 4.932a8.086 8.086 0 0 1 7.087 4.008 8.07 8.07 0 0 1 0 8.136 8.086 8.086 0 0 1-7.087 4.008v3.948c4.33.045 8.35-2.237 10.528-5.976a12.015 12.015 0 0 0 0-12.097A12.038 12.038 0 0 0 19.838.984v3.948z"></path>
                                    </g>
                                </svg>
                            </button>
                            <input type="range" id="volumeSlider" class="volume-slider" min="0" max="1" step="0.1" value="0.5" oninput="updateVolume(this.value)">

                        </div>

                        <div class="playback-speeds">
                            @foreach (var speed in new[] { 0.5, 1.0, 1.5, 2.0 })
                            {
                                <button class="speed-button @(speed == 1.0 ? "active" : "")" onclick="setPlaybackSpeed(@speed)">
                                    @speed.ToString("0.#")x
                                </button>
                            }
                        </div>

                        <button class="control-button" onclick="toggleFullscreen()">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M8 3H5a2 2 0 0 0-2 2v3"></path>
                                <path d="M21 8V5a2 2 0 0 0-2-2h-3"></path>
                                <path d="M3 16v3a2 2 0 0 0 2 2h3"></path>
                                <path d="M16 21h3a2 2 0 0 0 2-2v-3"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<script>

    const video = document.getElementById('videoPlayer');
    const controls = document.getElementById('videoControls');
    const playPauseButton = document.getElementById('playPauseButton');
    const playIcon = playPauseButton.querySelector('.play-icon');
    const pauseIcon = playPauseButton.querySelector('.pause-icon');

	const volumeSlider = document.getElementById('volumeSlider');
    let controlsTimeout;

    function togglePlay() {
        if (video.paused) {
            video.play();
            playIcon.classList.add('hide');
            pauseIcon.classList.remove('hide');
        } else {
            video.pause();
            playIcon.classList.remove('hide');
            pauseIcon.classList.add('hide');
        }
    }

    function updateVolume(value) {
        video.volume = value;
        updateVolumeIcon(value);
    }

	document.getElementById('volumeSlider').addEventListener('input', function () {
		var value = (this.value - this.min) / (this.max - this.min) * 100;
		this.style.background = `linear-gradient(to right, white ${value}%, #888 ${value}%, #888 100%)`;
	});

    function toggleMute() {
        video.muted = !video.muted;
        if (video.muted) {
            document.getElementById('volumeSlider').value = 0;
			volumeSlider.style.background = `linear-gradient(to right, white 0%, #888 0%, #888 100%)`;
        } else {
            document.getElementById('volumeSlider').value = video.volume;
        }
        updateVolumeIcon(video.muted ? 0 : video.volume);
    }

    function updateVolumeIcon(volume) {
        const volumeButton = document.getElementById('volumeButton');
        const icon = volume === 0
            ? '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 32 26"><g fill="#fff" fill-rule="nonzero"><path d="M2.635 6.412A2.633 2.633 0 0 0 0 9.044v7.912a2.633 2.633 0 0 0 2.635 2.632h4.233l6.988 5.944a1.984 1.984 0 0 0 3.269-1.512V1.98a1.984 1.984 0 0 0-3.27-1.511L6.869 6.412H2.635zM30.653 8.385a1.24 1.24 0 0 0-1.777-.02l-2.413 2.414-.288.288-.176.176-.175-.176-.289-.289-2.411-2.414a1.24 1.24 0 0 0-1.777.021c-.422.444-.46 1.132-.11 1.583l.131.151 2.347 2.35.311.312.219.219-.219.219-.311.312-2.318 2.32-.133.148a1.24 1.24 0 0 0 1.86 1.637l2.41-2.416.29-.29.175-.175.176.176.288.289 2.413 2.416a1.24 1.24 0 0 0 1.777-.021c.422-.443.46-1.132.11-1.583l-.131-.151-2.348-2.352-.312-.312-.219-.22.22-.218.311-.312 2.319-2.317.133-.149a1.24 1.24 0 0 0-.083-1.616z"/></g></svg>'
            : volume < 0.5
                ? '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 32 26"><g fill="#fff" fill-rule="nonzero"><path d="M2.635 6.412A2.633 2.633 0 0 0 0 9.044v7.912a2.633 2.633 0 0 0 2.635 2.632h4.233l6.988 5.943a1.984 1.984 0 0 0 3.269-1.51V1.98a1.984 1.984 0 0 0-3.27-1.512L6.869 6.412H2.635zM20.018 8.161v9.692c2.68 0 4.85-2.17 4.85-4.846a4.848 4.848 0 0 0-4.85-4.846z"/></g></svg>'
                : '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 32 26"><g fill="#fff" fill-rule="nonzero"><path d="M2.635 6.412A2.633 2.633 0 0 0 0 9.044v7.912a2.633 2.633 0 0 0 2.635 2.632h4.233l6.988 5.943a1.984 1.984 0 0 0 3.269-1.51V1.98a1.984 1.984 0 0 0-3.27-1.512L6.869 6.412H2.635zM20.018 8.161v9.692c2.68 0 4.85-2.17 4.85-4.846a4.848 4.848 0 0 0-4.85-4.846z"/><path d="M19.838 4.932a8.086 8.086 0 0 1 7.087 4.008 8.07 8.07 0 0 1 0 8.136 8.086 8.086 0 0 1-7.087 4.008v3.948c4.33.045 8.35-2.237 10.528-5.976a12.015 12.015 0 0 0 0-12.097A12.038 12.038 0 0 0 19.838.984v3.948z"/></g></svg>';
        volumeButton.innerHTML = icon;
    }

    function setPlaybackSpeed(speed) {
        video.playbackRate = speed;
        document.querySelectorAll('.speed-button').forEach(button => {
            button.classList.toggle('active', button.textContent.replace('x', '') == speed);
        });
    }

    function toggleFullscreen() {
        if (!document.fullscreenElement) {
            video.requestFullscreen();
        } else {
            document.exitFullscreen();
        }
    }

    function showControls() {
        controls.style.opacity = '1';
        clearTimeout(controlsTimeout);
        controlsTimeout = setTimeout(() => {
            if (!video.paused) {
                controls.style.opacity = '0';
            }
        }, 3000);
    }

    function closeVideoPlayer() {
        video.pause();
        closePopUp()
    }

    video.addEventListener('touchstart', showControls);
    video.addEventListener('mousemove', showControls);
    video.addEventListener('play', () => {
        playIcon.classList.add('hide');
        pauseIcon.classList.remove('hide');
    });
    video.addEventListener('pause', () => {
        playIcon.classList.remove('hide');
        pauseIcon.classList.add('hide');
    });

    // Prevent controls from hiding when interacting with them
    controls.addEventListener('mouseover', () => {
        clearTimeout(controlsTimeout);
        controls.style.opacity = '1';
    });

</script>