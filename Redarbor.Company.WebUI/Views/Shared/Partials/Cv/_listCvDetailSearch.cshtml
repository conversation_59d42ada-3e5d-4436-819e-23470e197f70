@model Redarbor.Company.WebUI.Models.Company.Candidate.Cv.CvDetailDataModel
@using Redarbor.Company.WebUI.Helpers.Html
@using Redarbor.Company.WebUI.Helpers
@using Redarbor.Common.Entities.Enums
@using Redarbor.Master.Entities.Enums
@using Redarbor.Core.Kpi.Contracts.ServiceLibrary.Enums
@using Redarbor.Core.Contracts.ServiceLibrary.Enums
@using Redarbor.Extensions.Library.Extensions

@{
    Layout = "";
    int PageId = Html.SetCurrentPage((int)PageEnum.DetailCVCompany);
    var portalConfig = PortalConfigHelper.GetPortalConfiguration();
    var companyCredentials = SecurityHelper.GetCompanyCredentials();
    var companyProduct = CompanyHelper.GetCurrentProduct(companyCredentials);
    var classIsPremium = (Model.IsPremium ? "premium" : string.Empty);
    var classPremiumVerifiedFound = Model.ShowPhone1Verification ? "tlf_whatsapp" : "tlf_whatsapp2";


}

<div class="container">
    <input type="hidden" id="hdrating" value="" />
    <input type="hidden" id="hiddenRatingUserRemove" value="" />
    <input type="hidden" id="hiddenCommentUserRemove" value="" />

    <section class="cm-12 breadinfo">
        <div class="bread_buscador pg_grid ant_sig">
            <a id="js-listado" class="listado" href="@(!string.IsNullOrEmpty(Model.OfferIdEncrypted) ? Url.Action("Index","CompanyMatches", new { oi= Model.OfferIdEncrypted, cf =  Model.FolderSelectedEncrypted}) : Url.Action("Index", "CompanyCvs"))">@Html.Raw(PageLiteralsHelper.GetLiteral("LIT_PAG_VOLVER_LISTADO", PageId, portalConfig))</a>
            @if (!string.IsNullOrEmpty(Model.NextUrl))
            {
                <a style="border: 1px solid #ccc" id="js-next" class="next" href="@Model.NextUrl"></a>
            }
            @if (!string.IsNullOrEmpty(Model.PreviousUrl))
            {
                <a id="js-before" class="before" href="@Model.PreviousUrl"></a>
            }
        </div>
        <div class="cm-4 creditos_hdv">
            @if (Model.TotalAllowedCvVisualization != int.MaxValue)
            {
                <p>@string.Format(PageLiteralsHelper.GetLiteral("LIT_X_HDV_DISPONIBLES", (int)PageEnum.PackCVSearch, portalConfig), Model.TotalAllowedCvVisualizationFormatted)</p>
            }
            else
            {
                <p>@Html.Raw(PageLiteralsHelper.GetLiteral("LIT_UNLIMITED_ACCESS", (int)PageEnum.PackCVSearch, portalConfig))</p>
            }
        </div>
    </section>
    @if (Model.HasCustomFolders)
    {
        <section class="gescan_ep1">
            <section class="cm-9 parrilla_oferta w_100 mb15">
                <div id="all_tabs">
                    <div class="nav-tab">
                        <ul class="tabs">
                            @if (!string.IsNullOrEmpty(Model.FolderSelectedEncrypted) && Model.FolderSelected != 0)
                            {
                                <li> <a href="@Url.Action("Index","CompanyCvs")">@PageLiteralsHelper.GetLiteral("LIT_CV_SEARCH", PageId, portalConfig)</a> </li>
                            }
                            else
                            {
                                <li class="active"><span>@PageLiteralsHelper.GetLiteral("LIT_CV_SEARCH", PageId, portalConfig)</span> </li>
                            }

                            @if (Model.CustomFoldersBBDD.Any())
                            {
                                foreach (var item in Model.CustomFoldersBBDD)
                                {
                                    <li id="folder" class="@(Model.FolderSelectedEncrypted == item.IdEncrypted ? "custom_folder active" : "custom_folder")">
                                        <a id="linkFolder" href="@Url.Action("Index","CompanyCvs", new { idFolder = item.IdEncrypted})" title="@item.NameFolder">
                                            <p id="@item.IdEncrypted" class="@(item.NameFolder.Length > 10 ? "cont": string.Empty )"> @item.NameFolder</p>
                                        </a>
                                        @if (Model.FolderSelectedEncrypted != item.IdEncrypted && !string.IsNullOrEmpty(item.IdEncrypted))
                                        {
                                            <span class="arrow_select">
                                                <div class="tab_act">
                                                    <a data-id-folder="@item.IdEncrypted" class="edit_folder">@PageLiteralsHelper.GetLiteral("LIT_EDIT_FOLDER", PageId, portalConfig)</a>
                                                    <a data-id-folder="@item.IdEncrypted" class="delete_folder">@PageLiteralsHelper.GetLiteral("LIT_DELETE_FOLDER", PageId, portalConfig)</a>
                                                </div>
                                            </span>
                                        }
                                    </li>
                                }
                            }

                            @if (Model.CanCreateNewFolder)
                            {
                                <li class="add_folder"> <a id="addfolder" href="#" onclick="$.blockUI({ message: $('#popupAddFolder')})">+</a></li>
                            }
                        </ul>
                    </div>
                </div>
                <section class="box_filtro_der bg_blanco">
                    <div class="order_cand cm-9">
                        <p>@PageLiteralsHelper.GetLiteral("LIT_MOVERA", PageId, portalConfig)</p>
                        @Html.DropDownListFor(d => d.FolderSelected, Model.CustomFoldersDropDownList, Model.IsViewed ? null : new { disabled = true })
                    </div>
                    @if (!string.IsNullOrEmpty(Model.FolderSelectedEncrypted) && Model.FolderSelected != 0)
                    {
                        <div class="acciones cm-3">
                            <a href="#" id="deleteCVFolderBBDD" class="acc_ofertas"><span title="@PageLiteralsHelper.GetLiteral("LIT_ELIMINAR", PageId, portalConfig)" class="icon papelera">&nbsp</span></a>
                        </div>
                    }
                </section>
            </section>
        </section>
    }

    <input type="hidden" id="hdrating" value="" />
    @Html.HiddenFor(n => n.IdCvEncrypted)
    @if (Model.IsViewed)
    {
        <section class="gescan_ep1 bgBlueMbl ">
            <article id="candidato" class="cm-12 grid">
                <div class="cm-12 box box_c @classIsPremium">

                    @if (Model.IsPremium)
                    {
                        <div class="premium_tag">
                            @PageLiteralsHelper.GetLiteral("PREMIUM", PageId, portalConfig)
                        </div>
                    }

                    <header id="headerCvDetail" class="btnX caja_titulo">
                        <h1>
                            <p class="fl">
                                <strong> @PageLiteralsHelper.GetLiteral("LIT_TITULO_CANDIDATE", PageId, portalConfig) @Model.Candidate.Candidate.Name @Model.Candidate.Candidate.Surname</strong>
                            </p>
                        </h1>
                        <div id="cvCandidatePdf" class="candidato_pdf">
                            @if (Model.HasDocument)
                            {
                                <a href="@Url.Action("DownloadCvAmazon", "CompanyCvDownloader", new {  idcv = Model.IdCvEncrypted })" target="_blank">
                                    <span title="@PageLiteralsHelper.GetLiteral("LIT_MOSTRAR_HDV_ADJUNTA", PageId, portalConfig)" class="@string.Format("icon {0}", Model.TypeDocument)"></span>
                                </a>
                            }

                            @if (Model.ShowVideoPresentation)
                            {
                                <a href="#" id="" class="acc_ofertas js_show_video"><span title="@PageLiteralsHelper.GetLiteral("VIDEO_INTERVIEW", PageId, portalConfig)" class="icon i_video_entr">&nbsp;</span></a>
                            }

                        </div>
                    </header>
                    <section>
                        <div class="cm-3 box_p_icon">
                            <div title="@string.Format("{0}", (Model.Candidate.Candidate.IdGender == 1) ? "MEN" : "WOMEN")">

                                @if (!string.IsNullOrEmpty(Model.Candidate.Candidate.Photo))
                                {
                                    if (Model.Candidate.Candidate.Photo.StartsWith("http"))
                                    {
                                        <div class="circle icon men"><img src="@Model.Candidate.Candidate.Photo" height="130" width="130" /></div>
                                    }
                                    else
                                    {
                                        <div class="circle icon men"><img src="@<EMAIL>" height="130" width="130" /></div>
                                    }
                                }
                                else
                                {
                                    <div class="@string.Format("circle icon {0}", (Model.Candidate.Candidate.IdGender == 1) ? "men" : "women")"></div>
                                }
                            </div>

                            <ul>
                                @if (!string.IsNullOrEmpty(Model.Candidate.User.Email))
                                {
                                    <li class="bWord"><span title="@PageLiteralsHelper.GetLiteral("LIT_EMAIL", PageId, portalConfig)" class="icon email"></span>@Model.Candidate.User.Email</li>
                                }
                                @if (Model.Candidate.CanShowNit)
                                {
                                    <li><span title="@PageLiteralsHelper.GetLiteral("LIT_IDENTIFICACION", PageId, portalConfig)" class="icon defic"></span>@Model.Candidate.Candidate.Nit</li>
                                }

                                @if (!string.IsNullOrEmpty(Model.Candidate.Candidate.Phone1ToWhatsApp))
                                {
                                    <li class="@classPremiumVerifiedFound">
                                        <span class="icon mvl"></span>
                                        @if (Model.ShowPhone1Verification)
                                        {
                                            <span class="fl">
                                                @Model.Candidate.Candidate.Phone1
                                                <p class="fc_ok fw_n w_auto fn d_ib ml15px mr5px">
                                                    @PageLiteralsHelper.GetLiteral("VERIFIED", PageId, portalConfig)
                                                </p>
                                                <span class="icon i_tlf_ver fr_i m0"></span>
                                            </span>
                                        }
                                        else
                                        {
                                            <span class="fl mt3 mb5">@Model.Candidate.Candidate.Phone1</span>
                                        }

                                        @if (Model.HasWhatsapp && Model.WhatsappPhone != string.Empty)
                                        {
                                            <div class="clear pt5">
                                                <span class="icon iconwhatsapp fl ml5px"></span>
                                                <a href="https://api.whatsapp.com/send?phone=@Model.WhatsappPhone&text=@Model.WhatsappMessage" target='_blank'>
                                                    <span class="fl fw_n mt3">@PageLiteralsHelper.GetLiteral("SEND_WHATSAPP", PageId, portalConfig)</span>
                                                </a>
                                            </div>
                                        }
                                    </li>
                                }

                                @if (!string.IsNullOrEmpty(Model.Candidate.Candidate.Phone2ToWhatsApp))
                                {
                                    <li class="tlf_whatsapp2">
                                        <span class="icon mvl"></span>
                                        <span class="fl mt3 mb5">@Model.Candidate.Candidate.Phone2</span>

                                        @if (Model.HasWhatsapp && Model.WhatsappPhone2 != string.Empty)
                                        {
                                            <div class="clear pt5">
                                                <span class="icon iconwhatsapp fl ml5px"></span>
                                                <a href="https://api.whatsapp.com/send?phone=@Model.WhatsappPhone2&text=@Model.WhatsappMessage2" target='_blank'>
                                                    <span class="fl fw_n mt3">@PageLiteralsHelper.GetLiteral("SEND_WHATSAPP", PageId, portalConfig)</span>
                                                </a>
                                            </div>
                                        }
                                    </li>
                                }

                                @if (portalConfig.AEPortalConfig.ShowSkypeContact && Model.HasSkype && !string.IsNullOrEmpty(Model.Candidate.Candidate.SkypeName))
                                {
                                    <li class="pos_rel">
                                        <span title="" class="icon iconskype"></span><a class="d_Block mt3 js_show_skype">@Model.Candidate.Candidate.SkypeName</a>
                                        <div class="hide js_box_skype">
                                            <div class="box_skype">
                                                <div class="cols pb10"><span class="icon iconskype_msg m0"></span><a class="w_100 pl10px" href="skype:@Model.Candidate.Candidate.SkypeName?chat">Enviar mensaje</a></div>
                                                <div class="cols"><span class="icon iconskype_call m0"></span><a class="w_100 pl10px" href="skype:@Model.Candidate.Candidate.SkypeName?call">Llamar</a></div>
                                            </div>
                                        </div>
                                    </li>
                                }

                                <li><span title="@PageLiteralsHelper.GetLiteral("LIT_PROVINCE_CITY", PageId, portalConfig)" class="icon pais"></span>@Model.Candidate.ProvinceString/@Model.Candidate.CityString</li>
                                @if (Model.Candidate.CanShowRace)
                                {
                                    <li> @PageLiteralsHelper.GetLiteral("LIT_RACE", PageId, portalConfig) <span> @Model.Candidate.CandidateRaceString </span></li>
                                }
                                <li><span title='@PageLiteralsHelper.GetLiteral("LIT_AGE", PageId, portalConfig)' class="icon edad"></span>@Model.Candidate.Candidate.Age</li>
                                @if (!string.IsNullOrEmpty(Model.Candidate.CivilianStatus))
                                {
                                    <li><span title='@PageLiteralsHelper.GetLiteral("LIT_CIVIL_STATUS", PageId, portalConfig)' class="icon pareja"></span>@Model.Candidate.CivilianStatus</li>
                                }
                                @if (Model.Candidate.Candidate.Disability > 0)
                                {
                                    <li><span title='@PageLiteralsHelper.GetLiteral("LIT_DISABILITY", PageId, portalConfig)' class="icon i_discapacity"></span>@string.Format("{0} %", Model.Candidate.Candidate.Disability)</li>
                                }
                                <li><span title='@PageLiteralsHelper.GetLiteral("LIT_YES", PageId, portalConfig)' class="icon si"></span>@Model.Candidate.ActualWork</li>
                                @if (Model.Candidate.HasDriverLicense)
                                {
                                    <li>
                                        <span title='@PageLiteralsHelper.GetLiteral(string.Format("LIT_{0}", Model.Candidate.DriveLicense ? "YES" : "NO"), PageId, portalConfig)' class="@String.Format("icon {0}", Model.Candidate.DriveLicense ? "si" : "no") "></span>
                                        @PageLiteralsHelper.GetLiteral("LIT_DRIVE_LICENSE", PageId, portalConfig)
                                    </li>
                                }
                                @if (Model.Candidate.HasVehicles)
                                {
                                    <li>
                                        <span title='@PageLiteralsHelper.GetLiteral(string.Format("LIT_{0}", Model.Candidate.Vehicles ? "YES" : "NO"), PageId, portalConfig)' class="@String.Format("icon {0}", Model.Candidate.Vehicles ? "si" : "no") "></span>
                                        @PageLiteralsHelper.GetLiteral("LIT_TRANSPORTATION", PageId, portalConfig)
                                    </li>
                                }
                                @if (Model.Candidate.HasTravel)
                                {
                                    <li>
                                        <span title='@PageLiteralsHelper.GetLiteral(string.Format("LIT_{0}", Model.Candidate.Travel ? "YES" : "NO"), PageId, portalConfig)' class="@String.Format("icon {0}", Model.Candidate.Travel ? "si" : "no") "></span>
                                        @PageLiteralsHelper.GetLiteral("LIT_AVAILABILITY_TRAVEL", PageId, portalConfig)
                                    </li>
                                }
                                @if (Model.Candidate.HasChangeResidence)
                                {
                                    <li>
                                        <span title='@PageLiteralsHelper.GetLiteral(string.Format("LIT_{0}", Model.Candidate.ChangeResidence ? "YES" : "NO"), PageId, portalConfig)' class="@String.Format("icon {0}", Model.Candidate.ChangeResidence ? "si" : "no") "></span>
                                        @PageLiteralsHelper.GetLiteral("LIT_AVAILABILITY_RESIDENCE", PageId, portalConfig)
                                    </li>
                                }
                                <li>
                                    <span title='@PageLiteralsHelper.GetLiteral("LIT_TITLE_SALARY", PageId, portalConfig)' class="icon salario"></span>
                                    @PageLiteralsHelper.GetLiteral("LIT_TITLE_SALARY", PageId, portalConfig) @Model.Candidate.Salary
                                </li>
                            </ul>
                            <ul class="infomodificaciones">
                                <li>@Html.Raw(String.Format("{0}<span>{1}</span>", PageLiteralsHelper.GetLiteral("LIT_ULTIMO_LOGIN", PageId, portalConfig), Model.Candidate.UserLastLoginCultureInfo)) </li>
                                <li>@Html.Raw(String.Format("{0}<span>{1}</span>", PageLiteralsHelper.GetLiteral("LIT_ULTIMA_MODIF", PageId, portalConfig), Model.Candidate.CandidateUpdatedOnCultureInfo))</li>
                                <li>@Html.Raw(@String.Format("{0}<span>{1}</span>", PageLiteralsHelper.GetLiteral("LIT_REGISTRADO", PageId, portalConfig), Model.Candidate.UserCreatedOnCultureInfo))</li>
                            </ul>
                        </div>

                        <div class="cm-9">
                            <ul>
                                <li>
                                    <h2>@Model.Candidate.Cv.TittleAdd</h2>
                                </li>
                                <li>
                                    <p>@Model.Candidate.Cv.ShortDescription</p>
                                    <br />
                                </li>
                            </ul>

                            @if (!Model.IsBasicOffer && Model.HasCompanyTestCompetencesFeature)
                            {
                                if (Model.Candidate.HasTestCompetences)
                                {
                                    <ul>
                                        <li class="resultados_test">
                                            <p class="name_candidato">@PageLiteralsHelper.GetLiteral("LIT_TEST_COMPETENCIAS", PageId, portalConfig)</p>
                                            <p><span>@PageLiteralsHelper.GetLiteral("LIT_PUNTOS_FUERTES", PageId, portalConfig)</span></p>
                                            <ul id="list_results">
                                                @{
                                                    int index = 1;
                                                    foreach (var test in Model.Candidate.Cv.CompetenceList.OrderByDescending(c => c.Result).Take(3).ToList())
                                                    {
                                                        <li>
                                                            <div class="result_value_test">
                                                                <canvas id="value_@index" class="result_value_test" width="80" height="80"></canvas>
                                                            </div>
                                                            <input type="hidden" value="@test.Result" id="input_value_@index" />
                                                            <div class="info">
                                                                <p class="title">
                                                                    @test.Title
                                                                </p>
                                                                <span class="icon_test <EMAIL>"></span>
                                                                <p>@test.Description</p>
                                                            </div>
                                                        </li>
                                                        index++;
                                                    }
                                                }
                                            </ul>
                                        </li>
                                    </ul>
                                }
                            }

                            @if (!Model.IsBasicOffer && Model.HasCompanyRatingsFeature)
                            {
                                if (Model.Candidate.HasRatings)
                                {
                                    <ul>
                                        <li class="boxResults box_or comentario">
                                            <div>
                                                <span>@PageLiteralsHelper.GetLiteral("LIT_RATING", PageId, portalConfig)</span>
                                                <div>
                                                    @PageLiteralsHelper.GetLiteral("LIT_MAXRATING", PageId, portalConfig)
                                                    <strong>
                                                        @Model.Candidate.RatingTop.Rating
                                                    </strong>
                                                    <div class="stars-wrapper">
                                                        @for (var i = 0; i < Model.Candidate.RatingTop.Rating; i++)
                                                        {
                                                            <span class="goldstar"></span>
                                                        }
                                                        @for (var i = Model.Candidate.RatingTop.Rating; i < 5; i++)
                                                        {
                                                            <span class="graystar"></span>
                                                        }
                                                    </div>
                                                </div>
                                            </div>
                                            <a href="#rates">@PageLiteralsHelper.GetLiteral("LIT_SHOW_RATING", PageId, portalConfig)</a>
                                        </li>
                                    </ul>
                                }
                            }


                            @if (Model.Candidate.Cv.ExperiencesByCv.Any())
                            {
                                <h2>@PageLiteralsHelper.GetLiteral("H2_EXP_PROFESIONAL", PageId, portalConfig)</h2>
                                <ul class="estudios">
                                    @foreach (var experience in Model.Candidate.Cv.ExperiencesByCv)
                                    {
                                        <li>
                                            <h3>@experience.Cargo</h3>
                                            <h4>
                                                @String.Format("{0} / {1} / {2}", experience.CompanyName, experience.Area, experience.Category)
                                            </h4>
                                            <p class='tiempo'>
                                                @String.Format("{0}-{1} ", experience.AñoInicioStrint, experience.MesInicioString)
                                                @if (experience.IsActual)
                                                {
                                                    @PageLiteralsHelper.GetLiteral("LIT_ACTUALMENTE", PageId, portalConfig)
                                                }
                                                else
                                                {
                                                    @String.Format("{0}-{1}", experience.AñoFinString, experience.MesFinString)
                                                }
                                                @String.Format("{0}", "(")
                                                @if (experience.DiferenciaDeFechas.Years > 0)
                                                {
                                                    @(experience.DiferenciaDeFechas.Years > 0
                                                                    ? experience.DiferenciaDeFechas.Years == 1
                                                                    ? string.Format("{0}{1}", experience.DiferenciaDeFechas.Years, PageLiteralsHelper.GetLiteral("LIT_EXP_AÑO", PageId, portalConfig))
                                                                    : string.Format("{0}{1}", experience.DiferenciaDeFechas.Years, PageLiteralsHelper.GetLiteral("LIT_EXP_AÑOS", PageId, portalConfig))
                                                                    : string.Empty)
                                                }
                                                @if (experience.DiferenciaDeFechas.Months > 0)
                                                {
                                                    @(experience.DiferenciaDeFechas.Months > 0
                                                                ? (experience.DiferenciaDeFechas.Months == 1
                                                                ? (experience.DiferenciaDeFechas.Years > 0
                                                                ? string.Format("{0}{1}{2}", PageLiteralsHelper.GetLiteral("LIT_EXP_Y", PageId, portalConfig), experience.DiferenciaDeFechas.Months, PageLiteralsHelper.GetLiteral("LIT_EXP_MES", PageId, portalConfig))
                                                                : string.Format("{0}{1}", experience.DiferenciaDeFechas.Months, PageLiteralsHelper.GetLiteral("LIT_EXP_MES", PageId, portalConfig)))
                                                                : (experience.DiferenciaDeFechas.Years > 0
                                                                ? string.Format("{0}{1}{2}", PageLiteralsHelper.GetLiteral("LIT_EXP_Y", PageId, portalConfig), experience.DiferenciaDeFechas.Months, PageLiteralsHelper.GetLiteral("LIT_EXP_MESES", PageId, portalConfig))
                                                                : string.Format("{0}{1}", experience.DiferenciaDeFechas.Months, PageLiteralsHelper.GetLiteral("LIT_EXP_MES", PageId, portalConfig))))
                                                                : string.Empty)
                                                }
                                                @String.Format("{0}", ")")
                                            </p>

                                            @if (experience.Tareas.Any())
                                            {
                                                <p>@String.Format("{0} {1}", PageLiteralsHelper.GetLiteral("LIT_DESCRIPCION_FUNCION", PageId, portalConfig), experience.Tareas) </p>
                                            }

                                        </li>
                                    }
                                </ul>
                            }



                            @if (Model.Candidate.Cv.FormationsByCv.Any())
                            {
                                <h2>@PageLiteralsHelper.GetLiteral("H2_FORMACION", PageId, portalConfig)</h2>
                                <ul class="estudios">
                                    @foreach (var formation in Model.Candidate.Cv.FormationsByCv)
                                    {
                                        <li>
                                            @if (!string.IsNullOrEmpty(formation.Institution))
                                            {
                                                <h3>@formation.Institution</h3>
                                            }
                                            <h3>
                                                @String.Format("{0}-{1} ", formation.Study, formation.StudyLevel)
                                            </h3>
                                            <h4>
                                                @formation.IdSpecializationstr
                                            </h4>
                                            <p>
                                                @if (formation.EndDate == DateTime.MinValue)
                                                {
                                                    @String.Format("{0} {1}", formation.EndDateString, formation.InitDateString)
                                                }
                                                else
                                                {
                                                    @String.Format("{0} / {1}", formation.InitDateString, formation.EndDateString)
                                                }
                                            </p>
                                        </li>
                                    }
                                </ul>
                            }
                            else
                            {
                                <ul> <li> <p>@PageLiteralsHelper.GetLiteral("LIT_NO_ESPECIFICADO", PageId, portalConfig)</p></li> </ul>
                            }


                            @if (Model.Candidate.Cv.SkillsByCv.Any() || Model.Candidate.Cv.LanguagesByCv.Any())
                            {
                                <h2>@PageLiteralsHelper.GetLiteral("H2_HABILIDADES", PageId, portalConfig)</h2>
                                <ul class="estudios">
                                    @if (Model.Candidate.Cv.SkillsByCv.Any())
                                    {
                                        foreach (var skillGroup in Model.Candidate.Cv.SkillsByCv.GroupBy(n => n.SkillTypeDescription))
                                        {
                                            <li><h3>@(skillGroup.FirstOrDefault() != null ? skillGroup.FirstOrDefault().SkillTypeDescription : "")</h3></li>
                                            <h4>
                                                @string.Join(", ", skillGroup.Select(s => s.Value))
                                            </h4>
                                        }
                                    }
                                    @if (Model.Candidate.Cv.LanguagesByCv.Any())
                                    {
                                        <h2>@PageLiteralsHelper.GetLiteral("H2_IDIOMAS", PageId, portalConfig)</h2>

                                        @(string.Join(", ", Model.Candidate.Cv.LanguagesByCv.Select(s => string.Format("{0} ( {1})", s.Lenguage, s.LenguageLevel))));
                                    }
                                </ul>
                            }

                            @if (!string.IsNullOrEmpty(Model.Candidate.Cv.LongDescription))
                            {
                                <h2>@PageLiteralsHelper.GetLiteral("H2_PERFIL_PROF", PageId, portalConfig)</h2>
                                <ul class="estudio">
                                    <li> <p>@Html.Raw(Model.Candidate.Cv.LongDescription.Replace(Environment.NewLine, "<br/>"))</p></li>
                                </ul>
                            }


                            @if (!Model.IsBasicOffer && Model.HasCompanyTestCompetencesFeature)
                            {
                                if (Model.Candidate.HasTestCompetences)
                                {
                                    <div id="test" class="test_resultados">
                                        <h2>@PageLiteralsHelper.GetLiteral("LIT_TEST_COMPETENCIAS", PageId, portalConfig)</h2>
                                        <canvas id="radarChart" class="radarChart_results mb10" height="150"></canvas>
                                    </div>

                                    <div class="resultados_test">
                                        <ul id="list_results_detail">
                                            @{
                                                int index = 1;
                                                foreach (var test in Model.Candidate.Cv.CompetenceList.OrderByDescending(c => c.Result).ToList())
                                                {
                                                    <li class="w_100">

                                                        <div class="result_value_test">
                                                            <canvas id="value_detail_@index" class="result_value_test" width="80" height="80"></canvas>
                                                        </div>
                                                        <input type="hidden" value="@test.Result" id="input_value_detail_@index" />
                                                        <div class="info">
                                                            <p class="title">
                                                                @test.Title
                                                            </p>
                                                            <span class="icon_test <EMAIL>"></span>
                                                            <p>@test.Description</p>
                                                        </div>
                                                    </li>
                                                    index++;
                                                }
                                            }
                                        </ul>
                                    </div>
                                }
                            }
                            @if (!Model.IsBasicOffer && Model.HasCompanyRatingsFeature)
                            {
                                <div id="rates" class="comentario rates">
                                    <p class="titulo">@PageLiteralsHelper.GetLiteral("LIT_RATING", PageId, portalConfig)</p>

                                    @if (Model.Candidate.HasRatings)
                                    {
                                        <ul>
                                            @foreach (var rating in @Model.Candidate.ListRating)
                                            {
                                                <li class="@(rating.UserId == companyCredentials.UserId ? "y_com" : " ")">
                                                    @if (rating.CanRemoving)
                                                    {
                                                        <input type="button" title="@PageLiteralsHelper.GetLiteral("LIT_REMOVE_RATING", PageId, portalConfig)" class="icon close_l"
                                                               id="removeRating" onclick="removeClickRating(event); return false;"
                                                               data-idratinguser="@EncryptationHelper.Encrypt(rating.UserId.ToString())" />
                                                    }

                                                    <p class="user">
                                                        <span>
                                                            @rating.NameAuthor
                                                            <span>@rating.TextDate</span>
                                                        </span>

                                                        <span class="stars-wrapper">

                                                            @for (var i = 0; i < rating.Rating; i++)
                                                            {
                                                                <span class="goldstar"></span>
                                                            }
                                                            @for (var i = rating.Rating; i < 5; i++)
                                                            {
                                                                <span class="graystar"></span>
                                                            }
                                                        </span>
                                                    </p>
                                                </li>
                                            }
                                        </ul>
                                    }
                                    else
                                    {
                                        <div class="b_v">
                                            <div class="y_rate">
                                                <span class="txt">@PageLiteralsHelper.GetLiteral("LIT_VALORA", PageId, portalConfig)</span>
                                                <span class="stars-wrapper" id="stars">
                                                    <span class="graystar"></span>
                                                    <span class="graystar"></span>
                                                    <span class="graystar"></span>
                                                    <span class="graystar"></span>
                                                    <span class="graystar"></span>
                                                </span>
                                            </div>
                                            <input type="submit" class="submit_n js-processing" data-processing="@PageLiteralsHelper.GetLiteral("LIT_PROCESSING", PageId, portalConfig)"
                                                   id="btnInsertRating" value='@PageLiteralsHelper.GetLiteral("LIT_PUBLISH_RATING", PageId, portalConfig)' />
                                        </div>
                                    }

                                </div>
                            }

                            @if (!Model.IsBasicOffer && Model.HasCompanyCommentsFeature)
                            {
                                <div class="comentario">
                                    <p class="titulo"> @PageLiteralsHelper.GetLiteral("LIT_COMENTARIOS", PageId, portalConfig)</p>
                                    @if (Model.Candidate.HasComments)
                                    {
                                        <ul id="commentList">
                                            @foreach (var comment in Model.Candidate.ListComments)
                                            {
                                                <li class="@(comment.UserId == companyCredentials.UserId ? "y_com" : " ")">
                                                    @if (comment.CanRemoving)
                                                    {
                                                        <input type="button" title="@PageLiteralsHelper.GetLiteral("LIT_REMOVE_RATING", PageId, portalConfig)" class="icon close_l"
                                                               id="removeComment" onclick="removeClickComment(event); return false;"
                                                               data-idcomment="@EncryptationHelper.Encrypt(comment.Id.ToString())" />
                                                    }
                                                    <p class="user">@comment.NameAuthor <span>@comment.TextDate</span></p>
                                                    <p class="txt">
                                                        @comment.Comment
                                                    </p>
                                                </li>
                                            }
                                        </ul>
                                    }
                                    <a class="more fl w_100" style="display: none">@PageLiteralsHelper.GetLiteral("LIT_VER_MAS", PageId, portalConfig)</a>
                                    <a class="minus fl w_100" style="display: none">@PageLiteralsHelper.GetLiteral("LIT_VER_MENOS", PageId, portalConfig)</a>


                                    @Html.TextAreaFor(n => n.Candidate.CommentTextArea, 2, 20, new { @class = "" })
                                    <span class="info_txta" id="charNum"></span>
                                    <input type="submit" class="submit_n js-processing" data-processing="@PageLiteralsHelper.GetLiteral("LIT_PROCESSING", PageId, portalConfig)" id="bttPublicar" value='@PageLiteralsHelper.GetLiteral("LIT_BTN_PUBLICAR_COMMENTS", PageId, portalConfig)' />

                                </div>

                            }

                            @if (!Model.IsBasicOffer && Model.HasHistory && Model.Candidate.HistoryOffers.Count > 0)
                            {
                                <div id="MoreOffers" class="estudios">
                                    <h2>@PageLiteralsHelper.GetLiteral("LIT_TITLE_HISTORYOFFER", PageId, portalConfig)</h2>
                                    <ul id="listHist" class="list_hist">
                                        @foreach (var hist in Model.Candidate.HistoryOffers)
                                        {
                                            <li>
                                                <div class="of">
                                                    <h3 class="offersCompare">@hist.TitleOffer</h3>
                                                    <span>@PageLiteralsHelper.GetLiteral("LIT_PUBLICADA", PageId, portalConfig) @hist.TextOfferDate</span>
                                                </div>
                                                <div class="pr_of">
                                                    <span id="candidateStatus" class="state"></span>
                                                    <span>@hist.TextCandidateDate</span>
                                                </div>
                                            </li>
                                        }
                                    </ul>
                                </div>
                            }
                        </div>

                    </section>
                </div>
            </article>
        </section>
    }
    else
    {
        <section class="gescan_ep1 bgBlueMbl ">
            <article id="candidato" class="cm-12 grid">
                <div class="cm-12 box box_c @classIsPremium">
                    @if (Model.IsPremium)
                    {
                        <div class="premium_tag">
                            @PageLiteralsHelper.GetLiteral("PREMIUM", PageId, portalConfig)
                        </div>
                    }
                    <header id="headerCvDetail" class="caja_titulo">
                        <h1>
                            <p class="fl">
                                <strong>@Model.Candidate.Cv.TittleAdd</strong>
                            </p>
                        </h1>
                        <div class="fecha">
                            <span title="@PageLiteralsHelper.GetLiteral("LIT_ACTUALIZADO", PageId, portalConfig)" class="icon clock"></span>
                            <p>
                                @PageLiteralsHelper.GetLiteral("LIT_ACTUALIZADO", PageId, portalConfig)
                                @StringToolsHelper.NormalizeGetFormatDate(@Model.Candidate.Cv.UpdatedOn.GetFormattedDate(string.Empty, new System.Globalization.CultureInfo(portalConfig.cultura)),
                                PageLiteralsHelper.GetLiteral("LIT_AYER", PageId, portalConfig),
                                PageLiteralsHelper.GetLiteral("LIT_HOY", PageId, portalConfig),
                                PageLiteralsHelper.GetLiteral("LIT_DIAS", PageId, portalConfig),
                                string.Empty)
                            </p>
                        </div>
                    </header>
                    <section>
                        <div class="cm-3 box_p_icon">
                            <div title="@string.Format("{0}", (Model.Candidate.Candidate.IdGender == 1) ? "MEN" : "WOMEN")">

                                @if (!string.IsNullOrEmpty(Model.Candidate.Candidate.Photo))
                                {
                                    if (Model.Candidate.Candidate.Photo.StartsWith("http"))
                                    {
                                        <div class="circle icon men"><img src="@Model.Candidate.Candidate.Photo" height="130" width="130" /></div>
                                    }
                                    else
                                    {
                                        <div class="circle icon men"><img src="@<EMAIL>" height="130" width="130" /></div>
                                    }
                                }
                                else
                                {
                                    <div class="@string.Format("circle icon {0}", (Model.Candidate.Candidate.IdGender == 1) ? "men" : "women")"></div>
                                }
                            </div>

                            <input class="submit_n" type="submit" value="@PageLiteralsHelper.GetLiteral("BTN_INFO_CONTACTO", PageId, portalConfig)" onclick="$.blockUI({ message: $('#ConsumeCreditsPopUp')})" />

                            <ul>
                                @if (!string.IsNullOrEmpty(Model.Candidate.User.Email))
                                {
                                    <li class="bWord"><span title="@PageLiteralsHelper.GetLiteral("LIT_EMAIL", PageId, portalConfig)" class="icon email"></span>@Model.Candidate.User.Email</li>
                                }
                                @if (Model.Candidate.CanShowNit)
                                {
                                    <li>
                                        <span title="@PageLiteralsHelper.GetLiteral("LIT_IDENTIFICACION", PageId, portalConfig)" class="icon defic"></span>
                                        @Model.Candidate.IdentificationType
                                        @(new string('*', Model.Candidate.Candidate.Nit.Length))
                                    </li>
                                }
                                @if (!string.IsNullOrEmpty(Model.Candidate.Candidate.Phone1))
                                {
                                    <li>
                                        <span class="icon mvl"></span>

                                        <span class="icon mvl"></span>
                                        <span class="fl fw_n mt3">@Model.Candidate.Candidate.Phone1</span>
                                    </li>
                                }
                                <li><span title="@PageLiteralsHelper.GetLiteral("LIT_PROVINCE_CITY", PageId, portalConfig)" class="icon pais"></span>@Model.Candidate.ProvinceString/@Model.Candidate.CityString</li>
                                @if (Model.Candidate.CanShowRace)
                                {
                                    <li> @PageLiteralsHelper.GetLiteral("LIT_RACE", PageId, portalConfig) <span> @Model.Candidate.CandidateRaceString </span></li>
                                }
                                <li><span title='@PageLiteralsHelper.GetLiteral("LIT_AGE", PageId, portalConfig)' class="icon edad"></span>@(new string('*', Model.Candidate.Candidate.Age.ToString().Length))</li>
                                @if (!string.IsNullOrEmpty(Model.Candidate.CivilianStatus))
                                {
                                    <li><span title='@PageLiteralsHelper.GetLiteral("LIT_CIVIL_STATUS", PageId, portalConfig)' class="icon pareja"></span>@(new string('*', Model.Candidate.CivilianStatus.Length))</li>
                                }
                                @if (Model.Candidate.Candidate.Disability > 0)
                                {
                                    <li><span title='@PageLiteralsHelper.GetLiteral("LIT_DISABILITY", PageId, portalConfig)' class="icon i_discapacity"></span>@String.Format("{0} %", Model.Candidate.Candidate.Disability)</li>
                                }
                                <li><span title='@PageLiteralsHelper.GetLiteral("LIT_YES", PageId, portalConfig)' class="icon si"></span>@Model.Candidate.ActualWork</li>
                                @if (Model.Candidate.HasDriverLicense)
                                {
                                    <li>
                                        <span title='@PageLiteralsHelper.GetLiteral(string.Format("LIT_{0}", Model.Candidate.DriveLicense ? "YES" : "NO"), PageId, portalConfig)' class="@String.Format("icon {0}", Model.Candidate.DriveLicense ? "si" : "no") "></span>
                                        @PageLiteralsHelper.GetLiteral("LIT_DRIVE_LICENSE", PageId, portalConfig)
                                    </li>
                                }
                                @if (Model.Candidate.HasVehicles)
                                {
                                    <li>
                                        <span title='@PageLiteralsHelper.GetLiteral(string.Format("LIT_{0}", Model.Candidate.Vehicles ? "YES" : "NO"), PageId, portalConfig)' class="@String.Format("icon {0}", Model.Candidate.Vehicles ? "si" : "no") "></span>
                                        @PageLiteralsHelper.GetLiteral("LIT_TRANSPORTATION", PageId, portalConfig)
                                    </li>
                                }
                                @if (Model.Candidate.HasTravel)
                                {
                                    <li>
                                        <span title='@PageLiteralsHelper.GetLiteral(string.Format("LIT_{0}", Model.Candidate.Travel ? "YES" : "NO"), PageId, portalConfig)' class="@String.Format("icon {0}", Model.Candidate.Travel ? "si" : "no") "></span>
                                        @PageLiteralsHelper.GetLiteral("LIT_AVAILABILITY_TRAVEL", PageId, portalConfig)
                                    </li>
                                }
                                @if (Model.Candidate.HasChangeResidence)
                                {
                                    <li>
                                        <span title='@PageLiteralsHelper.GetLiteral(string.Format("LIT_{0}", Model.Candidate.ChangeResidence ? "YES" : "NO"), PageId, portalConfig)' class="@String.Format("icon {0}", Model.Candidate.ChangeResidence ? "si" : "no") "></span>
                                        @PageLiteralsHelper.GetLiteral("LIT_AVAILABILITY_RESIDENCE", PageId, portalConfig)
                                    </li>
                                }
                                <li>
                                    <span title='@PageLiteralsHelper.GetLiteral("LIT_TITLE_SALARY", PageId, portalConfig)' class="icon salario"></span>
                                    @PageLiteralsHelper.GetLiteral("LIT_TITLE_SALARY", PageId, portalConfig) @Model.Candidate.Salary
                                </li>
                            </ul>
                        </div>

                        <div class="cm-9">
                            <ul>
                                <li>
                                    <h2>@Model.Candidate.Cv.TittleAdd</h2>
                                </li>
                                <li>
                                    <p> @Model.Candidate.Cv.ShortDescription   </p>
                                    <br />
                                </li>
                            </ul>

                            @if (!Model.IsBasicOffer && Model.HasCompanyTestCompetencesFeature)
                            {
                                if (Model.Candidate.HasTestCompetences)
                                {
                                    <ul>
                                        <li class="resultados_test">
                                            <p class="name_candidato">@PageLiteralsHelper.GetLiteral("LIT_TEST_COMPETENCIAS", PageId, portalConfig)</p>
                                            <p><span>@PageLiteralsHelper.GetLiteral("LIT_PUNTOS_FUERTES", PageId, portalConfig)</span></p>
                                            <ul id="list_results">
                                                @{
                                                    int index = 1;
                                                    foreach (var test in Model.Candidate.Cv.CompetenceList.OrderByDescending(c => c.Result).Take(3).ToList())
                                                    {
                                                        <li>
                                                            <div class="result_value_test">
                                                                <canvas id="value_@index" class="result_value_test" width="80" height="80"></canvas>
                                                            </div>
                                                            <input type="hidden" value="@test.Result" id="input_value_@index" />
                                                            <div class="info">
                                                                <p class="title">
                                                                    @test.Title
                                                                </p>
                                                                <span class="icon_test <EMAIL>"></span>
                                                                <p>@test.Description</p>
                                                            </div>
                                                        </li>
                                                        index++;
                                                    }
                                                }
                                            </ul>
                                        </li>
                                    </ul>
                                }
                            }

                            @if (Model.Candidate.Cv.ExperiencesByCv.Any())
                            {
                                <h2>@PageLiteralsHelper.GetLiteral("H2_EXP_PROFESIONAL", PageId, portalConfig)</h2>
                                <ul class="estudios">
                                    @foreach (var experience in Model.Candidate.Cv.ExperiencesByCv)
                                    {
                                        <li>
                                            <h3>@experience.Cargo</h3>
                                            <h4>
                                                @PageLiteralsHelper.GetLiteral("LIT_COMPANY_NAME", PageId, portalConfig)

                                                <a href="#" class="isBlocked" onclick="$.blockUI({ message: $('#ConsumeCreditsPopUp')})">
                                                    <span>@PageLiteralsHelper.GetLiteral("LIT_NOM_EMPRESA_OCULTO", PageId, portalConfig)</span>
                                                </a>

                                                @String.Format("{0} / {1}", experience.Area, experience.Category)
                                            </h4>
                                            <p class='tiempo'>
                                                @String.Format("{0}-{1} / ", experience.AñoInicioStrint, experience.MesInicioString)
                                                @if (experience.IsActual)
                                                {
                                                    @PageLiteralsHelper.GetLiteral("LIT_ACTUALMENTE", PageId, portalConfig)
                                                }
                                                else
                                                {
                                                    @String.Format("{0}-{1}", experience.AñoFinString, experience.MesFinString)
                                                }
                                                @String.Format("{0}", "(")
                                                @if (experience.DiferenciaDeFechas.Years > 0)
                                                {
                                                    @(experience.DiferenciaDeFechas.Years > 0
                                                                                            ? experience.DiferenciaDeFechas.Years == 1
                                                                                            ? string.Format("{0}{1}", experience.DiferenciaDeFechas.Years, PageLiteralsHelper.GetLiteral("LIT_EXP_AÑO", PageId, portalConfig))
                                                                                            : string.Format("{0}{1}", experience.DiferenciaDeFechas.Years, PageLiteralsHelper.GetLiteral("LIT_EXP_AÑOS", PageId, portalConfig))
                                                                                            : string.Empty)
                                                }
                                                @if (experience.DiferenciaDeFechas.Months > 0)
                                                {
                                                    @(experience.DiferenciaDeFechas.Months > 0
                                                                                            ? (experience.DiferenciaDeFechas.Months == 1
                                                                                            ? (experience.DiferenciaDeFechas.Years > 0
                                                                                            ? string.Format("{0}{1}{2}", PageLiteralsHelper.GetLiteral("LIT_EXP_Y", PageId, portalConfig), experience.DiferenciaDeFechas.Months, PageLiteralsHelper.GetLiteral("LIT_EXP_MES", PageId, portalConfig))
                                                                                            : string.Format("{0}{1}", experience.DiferenciaDeFechas.Months, PageLiteralsHelper.GetLiteral("LIT_EXP_MES", PageId, portalConfig)))
                                                                                            : (experience.DiferenciaDeFechas.Years > 0
                                                                                            ? string.Format("{0}{1}{2}", PageLiteralsHelper.GetLiteral("LIT_EXP_Y", PageId, portalConfig), experience.DiferenciaDeFechas.Months, PageLiteralsHelper.GetLiteral("LIT_EXP_MESES", PageId, portalConfig))
                                                                                            : string.Format("{0}{1}", experience.DiferenciaDeFechas.Months, PageLiteralsHelper.GetLiteral("LIT_EXP_MES", PageId, portalConfig))))
                                                                                            : string.Empty)
                                                }
                                                @String.Format("{0}", ")")
                                            </p>

                                            @if (experience.Tareas.Any())
                                            {
                                                <p>@String.Format("{0} {1}", PageLiteralsHelper.GetLiteral("LIT_DESCRIPCION_FUNCION", PageId, portalConfig), experience.Tareas) </p>
                                            }

                                        </li>
                                    }
                                </ul>
                            }

                            <h2>@PageLiteralsHelper.GetLiteral("H2_FORMACION", PageId, portalConfig)</h2>

                            <ul class="estudios">
                                @if (Model.Candidate.Cv.FormationsByCv.Any())
                                {
                                    foreach (var formation in Model.Candidate.Cv.FormationsByCv)
                                    {
                                        <li>
                                            @if (!string.IsNullOrEmpty(formation.Institution))
                                            {
                                                <h3>@formation.Institution</h3>
                                            }
                                            <h3>
                                                @String.Format("{0}-{1} / ", formation.Study, formation.StudyLevel)
                                            </h3>
                                            <h4>
                                                @formation.IdSpecializationstr
                                            </h4>
                                            <p>
                                                @if (formation.EndDate == DateTime.MinValue)
                                                {
                                                    @String.Format("{0} {1}", formation.EndDateString, formation.InitDateString)
                                                }
                                                else
                                                {
                                                    @String.Format("{0} / {1}", formation.InitDateString, formation.EndDateString)
                                                }
                                            </p>
                                        </li>
                                    }
                                }
                                else
                                {
                                    <li> <p>@PageLiteralsHelper.GetLiteral("LIT_NO_ESPECIFICADO", PageId, portalConfig)</p></li>
                                }
                            </ul>

                            @if (Model.Candidate.Cv.SkillsByCv.Any() || Model.Candidate.Cv.LanguagesByCv.Any())
                            {
                                <h2>@PageLiteralsHelper.GetLiteral("H2_HABILIDADES", PageId, portalConfig)</h2>
                                <ul class="estudios">
                                    @if (Model.Candidate.Cv.SkillsByCv.Any())
                                    {
                                        foreach (var skillGroup in Model.Candidate.Cv.SkillsByCv.GroupBy(n => n.SkillTypeDescription))
                                        {
                                            <li><h3>@(skillGroup.FirstOrDefault() != null ? skillGroup.FirstOrDefault().SkillTypeDescription : "") </h3></li>
                                            <h4>
                                                @string.Join(", ", skillGroup.Select(s => s.Value))
                                            </h4>
                                        }
                                    }
                                    @if (Model.Candidate.Cv.LanguagesByCv.Any())
                                    {
                                        <h2>@PageLiteralsHelper.GetLiteral("H2_IDIOMAS", PageId, portalConfig)</h2>

                                        @(string.Join(", ", Model.Candidate.Cv.LanguagesByCv.Select(s => string.Format("{0} ( {1})", s.Lenguage, s.LenguageLevel))));
                                    }
                                </ul>
                            }

                            <ul class="estudios">
                                <li>
                                    <h2>@PageLiteralsHelper.GetLiteral("H2_DESC_BREVE", PageId, portalConfig)</h2>
                                </li>
                                <li>
                                    <h4>@Model.Candidate.Cv.ShortDescription</h4>
                                </li>
                            </ul>

                            @if (!Model.IsBasicOffer && Model.HasCompanyRatingsFeature)
                            {
                                <div id="rates" class="comentario rates">
                                    <p class="titulo">@PageLiteralsHelper.GetLiteral("LIT_RATING", PageId, portalConfig)</p>

                                    @if (Model.Candidate.HasRatings)
                                    {
                                        <ul>
                                            @foreach (var rating in @Model.Candidate.ListRating)
                                            {
                                                <li class="@(rating.UserId == companyCredentials.UserId ? "y_com" : " ")">
                                                    @if (rating.CanRemoving)
                                                    {
                                                        <input type="button" onclick="$.blockUI({ message: $('#ConsumeCreditsPopUp')})" title="@PageLiteralsHelper.GetLiteral("LIT_REMOVE_RATING", PageId, portalConfig)" class="icon close_l isBlocked" />
                                                    }

                                                    <p class="user">
                                                        <span>
                                                            @rating.NameAuthor
                                                            <span>@rating.TextDate</span>
                                                        </span>

                                                        <span class="stars-wrapper">

                                                            @for (var i = 0; i < rating.Rating; i++)
                                                            {
                                                                <span class="goldstar"></span>
                                                            }
                                                            @for (var i = rating.Rating; i < 5; i++)
                                                            {
                                                                <span class="graystar"></span>
                                                            }
                                                        </span>
                                                    </p>
                                                </li>
                                            }
                                        </ul>
                                    }
                                    else
                                    {
                                        <div class="b_v">
                                            <div class="y_rate">
                                                <span class="txt">@PageLiteralsHelper.GetLiteral("LIT_VALORA", PageId, portalConfig)</span>
                                                <span class="stars-wrapper" id="stars">
                                                    <span class="graystar"></span>
                                                    <span class="graystar"></span>
                                                    <span class="graystar"></span>
                                                    <span class="graystar"></span>
                                                    <span class="graystar"></span>
                                                </span>
                                            </div>
                                            <input type="submit" onclick="$.blockUI({ message: $('#ConsumeCreditsPopUp')})" class="isBlocked submit_n js-processing" data-processing="@PageLiteralsHelper.GetLiteral("LIT_PROCESSING", PageId, portalConfig)"
                                                   value='@PageLiteralsHelper.GetLiteral("LIT_PUBLISH_RATING", PageId, portalConfig)' />
                                        </div>
                                    }

                                </div>
                            }

                            @if (!Model.IsBasicOffer && Model.HasCompanyCommentsFeature)
                            {
                                <div class="comentario">
                                    <p class="titulo"> @PageLiteralsHelper.GetLiteral("LIT_COMENTARIOS", PageId, portalConfig)</p>
                                    @if (Model.Candidate.HasComments)
                                    {
                                        <ul id="commentList">
                                            @foreach (var comment in Model.Candidate.ListComments)
                                            {
                                                <li class="@(comment.UserId == companyCredentials.UserId ? "y_com" : " ")">
                                                    @if (comment.CanRemoving)
                                                    {
                                                        <input type="button" onclick="$.blockUI({ message: $('#ConsumeCreditsPopUp')})" title="@PageLiteralsHelper.GetLiteral("LIT_REMOVE_RATING", PageId, portalConfig)" class="icon close_l isBlocked" />
                                                    }
                                                    <p class="user">@comment.NameAuthor <span>@comment.TextDate</span></p>
                                                    <p class="txt">
                                                        @comment.Comment
                                                    </p>
                                                </li>
                                            }
                                        </ul>
                                    }
                                    <a class="more fl w_100" style="display: none">@PageLiteralsHelper.GetLiteral("LIT_VER_MAS", PageId, portalConfig)</a>
                                    <a class="minus fl w_100" style="display: none">@PageLiteralsHelper.GetLiteral("LIT_VER_MENOS", PageId, portalConfig)</a>


                                    @Html.TextAreaFor(n => n.Candidate.CommentTextArea, 2, 20, new { @class = "" })
                                    <span class="info_txta" id="charNum"></span>
                                    <input type="submit" onclick="$.blockUI({ message: $('#ConsumeCreditsPopUp')})" class="isBlocked submit_n js-processing" data-processing="@PageLiteralsHelper.GetLiteral("LIT_PROCESSING", PageId, portalConfig)" value='@PageLiteralsHelper.GetLiteral("LIT_BTN_PUBLICAR_COMMENTS", PageId, portalConfig)' />

                                </div>

                            }

                            @if (!Model.IsBasicOffer && Model.HasHistory && Model.Candidate.HistoryOffers.Count > 0)
                            {
                                <div id="MoreOffers" class="estudios">
                                    <h2>@PageLiteralsHelper.GetLiteral("LIT_TITLE_HISTORYOFFER", PageId, portalConfig)</h2>
                                    <ul id="listHist" class="list_hist">
                                        @foreach (var hist in Model.Candidate.HistoryOffers)
                                        {
                                            <li>
                                                <div class="of">
                                                    <h3 class="offersCompare">@hist.TitleOffer</h3>
                                                    <span>@PageLiteralsHelper.GetLiteral("LIT_PUBLICADA", PageId, portalConfig) @hist.TextOfferDate</span>
                                                </div>
                                                <div class="pr_of">
                                                    <span id="candidateStatus" class="state"></span>
                                                    <span>@hist.TextCandidateDate</span>
                                                </div>
                                            </li>
                                        }
                                    </ul>
                                </div>
                            }
                        </div>

                    </section>
                </div>
            </article>
        </section>
    }

</div>



@if (!Model.IsViewed)
{
    @Html.DisplayFor(p => p.IsViewdPopUp, new
    {
        IdPopUp = "ConsumeCreditsPopUp",
        IdBtnOk = "bttConsumeCreditsPopUp",
        IdBtnKo = "lnkCancelar",
        IdInput = string.Empty,
        OnClick = "ConsumeCredits();"
    })

    @Html.DisplayFor(p => p.NoMoreCreditsPopUp, new
    {
        IdPopUp = "NoMoreCredits",
        IdBtnOk = "bttNoMoreCredits",
        IdBtnKo = "lnkCancelar",
        IdInput = string.Empty,
        OnClick = ""
    })
}


@if (Model.HasCustomFolders)
{

    @Html.DisplayFor(p => p.UpdateCvSearchDetailFolderPopUp, new
    {
        IdPopUp = "popupUpdateFolder",
        IdBtnOk = "btnUpdateFolder",
        IdInput = "NewNameFolder",
        OnClick = string.Empty
    })


    @Html.DisplayFor(p => p.AddCvSearchDetailFolderPopUp, new
    {
        IdPopUp = "popupAddFolder",
        IdBtnOk = "btnAddFolder",
        IdInput = "nameFolder",
        OnClick = string.Empty
    })

    @Html.DisplayFor(p => p.DeleteCvSearchDetailFolderPopUp, new
    {
        IdPopUp = "DeleteFolderPopUp",
        IdBtnOk = "btnDeleteFolder",
        IdInput = string.Empty,
        OnClick = string.Empty
    })

}

@if (!Model.IsBasicOffer && Model.IsViewed)
{

    if (Model.HasCustomFolders && !Model.IsMatch)
    {



        @Html.DisplayFor(p => p.MaxCvSearchDetailFolderPopUp, new
        {
            IdPopUp = "popupMaxCvFolder",
            IdBtnOk = string.Empty,
            IdInput = string.Empty,
            OnClick = "closePopup"
        })

        @Html.DisplayFor(p => p.NotViewCvSearchDetailFolderPopUp, new
        {
            IdPopUp = "popupNotViewCv",
            IdBtnOk = string.Empty,
            IdInput = string.Empty,
            OnClick = "closePopup"
        })

        @Html.DisplayFor(p => p.NotAllCvSearchDetailFolderPopUp, new
        {
            IdPopUp = "popupNotAllCv",
            IdBtnOk = string.Empty,
            IdInput = string.Empty,
            OnClick = "closePopup"
        })

        @Html.DisplayFor(p => p.AdvertCvSearchDetailFolderPopUp, new
        {
            IdPopUp = "popupAdvert",
            IdBtnOk = string.Empty,
            IdInput = string.Empty,
            OnClick = "closePopup"
        })


        @Html.DisplayFor(p => p.DeleteCvsCvSearchDetailFolderPopUp, new
        {
            IdPopUp = "DeleteCvsPopup",
            IdBtnOk = "btDeleteCvFolder",
            IdInput = string.Empty,
            OnClick = string.Empty
        })


    }
}

