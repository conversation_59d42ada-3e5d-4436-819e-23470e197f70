@using Redarbor.Company.WebUI.Helpers;

@{
    var origin = (string)ViewData["origin"];
    var PageId = (int)ViewData["pageid"];
    var companycredentials = (Redarbor.Master.Contracts.ServiceLibrary.DTO.CompanyCredentials)ViewData["companyCredentials"];
    var portalConfig = PortalConfigHelper.GetPortalConfiguration();


    if (portalConfig.AEPortalConfig.HubSpotContact)
    {
        if (portalConfig.AEPortalConfig.EnableComercialContactPopup)
        {
            if (origin == "_HeaderLinkNewPemMemb")
            {
                <a href="#"
                   onclick="ga('send', 'event', 'Contact Buttons', 'Click Hubspot Support Header NewPEm', 'Click Hubspot Support Header NewPEm'); comercialRequest('@Url.Action("ComercialRequest", "Contact", new { sk = "1eG3SIQqWScyhf2z9Hm9Sxw5ajju" })'); return false;" class="button link contactLinkFix hide">
                    @PageLiteralsHelper.GetLiteral("LIT_COMMERCIAL_ATTENTION", PageId, portalConfig)
                </a>
            }
            if (origin == "_HeaderLinkNewPemNotMemb")
            {
                <a href="#"
                   onclick="ga('send', 'event', 'Contact Buttons', 'Click Hubspot Support Header NewPEm', 'Click Hubspot Support Header NewPEm'); comercialRequest('@Url.Action("ComercialRequest", "Contact", new { sk = "1eG3SIQqWScyhf2z9Hm9Sxw5ajju" })'); return false;" class="button link contactLinkFix hide">
                    @PageLiteralsHelper.GetLiteral("LIT_CONTRACT_PRODUCTS", PageId, portalConfig)
                </a>
            }
        }
        else
        {
            if (origin == "_HeaderLinkNewPemMemb")
            {
                <a href="@Url.Action("HubSpotContact", "Contact", new { sk = "1eG3SIQqWScyhf2z9Hm9Sxw5ajju" })"
                   onclick="ga('send', 'event', 'Contact Buttons', 'Click Hubspot Support Header NewPEm', 'Click Hubspot Support Header NewPEm');" class="button link contactLinkFix hide">
                    @PageLiteralsHelper.GetLiteral("LIT_COMMERCIAL_ATTENTION", PageId, portalConfig)
                </a>
            }
            if (origin == "_HeaderLinkNewPemNotMemb")
            {
                <a href="@Url.Action("HubSpotContact", "Contact", new { sk = "1eG3SIQqWScyhf2z9Hm9Sxw5ajju" })"
                   onclick="ga('send', 'event', 'Contact Buttons', 'Click Hubspot Support Header NewPEm', 'Click Hubspot Support Header NewPEm');" class="button link contactLinkFix hide">
                    @PageLiteralsHelper.GetLiteral("LIT_CONTRACT_PRODUCTS", PageId, portalConfig)
                </a>
            }
        }

        if (origin == "_VisionBanner")
        {
            <a href="#"
               onclick="ga('send', 'event', 'Contact Buttons', 'Click Hubspot Support PopUp NewPem VisionBanner', 'Click Hubspot Support PopUp NewPem VisionBanner'); comercialRequest('@Url.Action("ComercialRequest", "Contact", new { sk = "1eG3SIQqWScyhf2z9Hm9Sxw5ajju" })'); return false;" class="button link contactLinkFix hide">
                @PageLiteralsHelper.GetLiteral("LIT_COMMERCIAL_ATTENTION", PageId, portalConfig)
            </a>
        }
        if (origin == "_VisionLogo")
        {
            <a href="#" type="button" class="b_primary" onclick="ga('send', 'event', 'Contact Buttons', 'Click Hubspot Support PopUp NewPem Vision Logo', 'Click Hubspot Support PopUp NewPem Vision Logo'); comercialRequest('@Url.Action("ComercialRequest", "Contact", new { sk = "1eG3SIQqWScyhf2z9Hm9Sxw5ajju" })'); return false;">@PageLiteralsHelper.GetLiteral("BTN_UPLOAD", PageId, portalConfig)</a>
        }
        if (origin == "_VisionBenefits")
        {
            <a href="#" type="button" class="b_primary" onclick="ga('send', 'event', 'Contact Buttons', 'Click Hubspot Support PopUp NewPem Vision Benefits', 'Click Hubspot Support PopUp NewPem Vision Benefits'); comercialRequest('@Url.Action("ComercialRequest", "Contact", new { sk = "1eG3SIQqWScyhf2z9Hm9Sxw5ajju" })'); return false;">@PageLiteralsHelper.GetLiteral("LIT_BTT_SAVE", PageId, portalConfig)</a>
        }
        if (origin == "_VisionAboutCompany")
        {
            <a href="#" type="button" class="b_primary" onclick="ga('send', 'event', 'Contact Buttons', 'Click Hubspot Support PopUp NewPem Vision About Company', 'Click Hubspot Support PopUp NewPem Vision About Company'); comercialRequest('@Url.Action("ComercialRequest", "Contact", new { sk = "1eG3SIQqWScyhf2z9Hm9Sxw5ajju" })'); return false;">@PageLiteralsHelper.GetLiteral("LIT_BTT_SAVE", PageId, portalConfig)</a>
        }
        if (origin == "_VisionAwards")
        {
            <a href="#" type="button" class="b_primary" onclick="ga('send', 'event', 'Contact Buttons', 'Click Hubspot Support PopUp NewPem Vision Awards', 'Click Hubspot Support PopUp NewPem Vision Awards'); comercialRequest('@Url.Action("ComercialRequest", "Contact", new { sk = "1eG3SIQqWScyhf2z9Hm9Sxw5ajju" })'); return false;">@PageLiteralsHelper.GetLiteral("LIT_SAVE", PageId, portalConfig)</a>
        }
        if (origin == "_VisionPhotos")
        {
            <a href="#" type="button" class="b_primary" onclick="ga('send', 'event', 'Contact Buttons', 'Click Hubspot Support PopUp NewPem Vision Photos', 'Click Hubspot Support PopUp NewPem Vision Photos'); comercialRequest('@Url.Action("ComercialRequest", "Contact", new { sk = "1eG3SIQqWScyhf2z9Hm9Sxw5ajju" })'); return false;">@PageLiteralsHelper.GetLiteral("LIT_ADD_PHOTOS", PageId, portalConfig)</a>
        }
        if (origin == "_SupportPopUpNewPem")
        {
            <a href="@Url.Action("HubSpotContact", "Contact", new { sk = "1eG3SIQqWScyhf2z9Hm9Sxw5ajju" })"
               onclick="ga('send', 'event', 'Contact Buttons', 'Click Hubspot Support PopUp NewPem', 'Click Hubspot Support PopUp NewPem')" class="w_100 contactLinkFix hide">
                @PageLiteralsHelper.GetLiteral("LIT_COMERCIAL_INFORMATION_URL", PageId, portalConfig)
            </a>
        }
        if (origin == "_RenewMembership")
        {
            <a href="@Url.Action("HubSpotContact", "Contact", new { sk = "1q7bC1Y9SQTeQoPI3r91DVg5ajju" })"
               onclick="ga('send', 'event', 'Contact Buttons', 'Click Hubspot Renew Membership', 'Click Hubspot Renew Membership')" class="b_primary_inv big contactLinkFix hide">
                @PageLiteralsHelper.GetLiteral("LIT_RENOVAR", PageId, portalConfig)
            </a>
        }
        if (origin == "_Alert")
        {
            <a href="@Url.Action("HubSpotContact", "Contact", new { sk = "1eG3SIQqWScyhf2z9Hm9Sxw5ajju" })"
               onclick="ga('send', 'event', 'Contact Buttons', 'Click Hubspot Alert', 'Click Hubspot Alert')" class="submit_n mt10 mb15 contactLinkFix hide">
                @PageLiteralsHelper.GetLiteral("LIT_BTN_CONTACTAR", PageId, portalConfig)
            </a>
        }
        if (origin == "_AlertNewPem")
        {
            <a href="@Url.Action("HubSpotContact", "Contact", new { sk = "10VZMXhBiRQqpsgDTccdctg5ajju" })"
               onclick="ga('send', 'event', 'Contact Buttons', 'Click Hubspot Alert New Pem', 'Click Hubspot Alert New Pem')" class="b_primary_inv big mt20 contactLinkFix hide">
                @PageLiteralsHelper.GetLiteral("LIT_RENOVAR", PageId, portalConfig)
            </a>
        }
        if (origin == "_SupportPopUp")
        {
            <a href="@Url.Action("HubSpotContact", "Contact", new { sk = "1eG3SIQqWScyhf2z9Hm9Sxw5ajju" })"
               onclick="ga('send', 'event', 'Contact Buttons', 'Click Hubspot Support PopUp', 'Click Hubspot Support PopUp')" class="mb10 w_100 d_block contactLinkFix hide">
                @PageLiteralsHelper.GetLiteral("LIT_COMERCIAL_INFORMATION_URL", PageId, portalConfig)
            </a>
        }
        if (origin == "_InfoExceededLimitUsers")
        {
            <a href="@Url.Action("HubSpotContact", "Contact", new { sk = "1eG3SIQqWScyhf2z9Hm9Sxw5ajju" })"
               onclick="ga('send', 'event', 'Contact Buttons', 'Click Hubspot Info Exceeded Limit Users', 'Click Hubspot Info Exceeded Limit Users')" class="contactLinkFix hide">
                <span class="icon i_phone mr5"></span>
                @PageLiteralsHelper.GetLiteral("LIT_COMERCIAL_INFORMATION_URL", PageId, portalConfig)
            </a>
        }
        if (origin == "CompanyMatchesIndex")
        {
            <a href="@Url.Action("HubSpotContact", "Contact", new { sk = "1eG3SIQqWScyhf2z9Hm9Sxw5ajju" })"
               onclick="ga('send', 'event', 'Contact Buttons', 'Click Hubspot Company Matches Index', 'Click Hubspot Company Matches Index')" class="contactLinkFix hide">
                @PageLiteralsHelper.GetLiteral("LIT_COMERCIAL_INFORMATION_URL", PageId, portalConfig)
            </a>
        }
        if (origin == "_InfoCanNotViewMatches")
        {
            <a onclick="ga('send', 'event', 'Contact Buttons', 'Click Hubspot Info Can Not View Matches', 'Click Hubspot Info Can Not View Matches'); comercialRequest('@Url.Action("ComercialRequest", "Contact", new { sk = "1eG3SIQqWScyhf2z9Hm9Sxw5ajju" })');" class="mb10 w_100 d_block contactLinkFix hide">
                @PageLiteralsHelper.GetLiteral("LIT_COMERCIAL_INFORMATION_URL", PageId, portalConfig)
            </a>
        }
    }
    else
    {
        if (origin == "_HeaderLinkNewPemMemb")
        {
            var comment = (string)ViewData["comment"];
            var comercialAndTechnicalSupport = CompanyHelper.GetComercialAndTechnicalSupport(comment, portalConfig, companycredentials);

            <a href="#" class="button link" onclick="javascript:setSrcInFormIframeNewPem('@comercialAndTechnicalSupport.Comments','@comercialAndTechnicalSupport.ComercialInformationUrl')">
                @PageLiteralsHelper.GetLiteral("LIT_COMMERCIAL_ATTENTION", PageId, portalConfig)
            </a>
        }
        if (origin == "_HeaderLinkNewPemNotMemb")
        {
            var comment = (string)ViewData["comment"];
            var comercialAndTechnicalSupport = CompanyHelper.GetComercialAndTechnicalSupport(comment, portalConfig, companycredentials);

            <a href="#" class="button link" onclick="javascript:setSrcInFormIframeNewPem('@comercialAndTechnicalSupport.Comments','@comercialAndTechnicalSupport.ComercialInformationUrl')">
                @PageLiteralsHelper.GetLiteral("LIT_COMMERCIAL_ATTENTION", PageId, portalConfig)
            </a>
        }
        if (origin == "_SupportPopUpNewPem")
        {
            var comment = (string)ViewData["comment"];
            var comercialAndTechnicalSupport = CompanyHelper.GetComercialAndTechnicalSupport(comment, portalConfig, companycredentials);

            <a href="#" class="mb10 w_100 d_block" onclick="javascript:setSrcInFormIframeNewPem('@comercialAndTechnicalSupport.Comments','@comercialAndTechnicalSupport.ComercialInformationUrl')">
                @PageLiteralsHelper.GetLiteral("LIT_COMERCIAL_INFORMATION_URL", PageId, portalConfig)
            </a>
        }
        if (origin == "_RenewMembership")
        {
            var productExpiredRenewUrl = (string)ViewData["productExpiredRenewUrl"];
            <a class="b_primary_inv big" href="#" onclick="javascript:setSrcInFormIframeNewPem('-','@productExpiredRenewUrl')">@PageLiteralsHelper.GetLiteral("LIT_RENOVAR", PageId, portalConfig)</a>
        }
        if (origin == "_Alert")
        {
            var comercialAndTechnicalSupport = portalConfig.AEPortalConfig.IsNewSupportWithSF ? CompanyHelper.GetComercialAndTechnicalSupport("membresia_vencida", portalConfig) : new Redarbor.Company.WebUI.Models.InternalModels.ComercialAndTechnicalSupportInternalModel();
            <a id="lnk_buy" class="submit_n mt10 mb15" href="#" onclick="javascript:setSrcInFormIframe('@comercialAndTechnicalSupport.Comments','@comercialAndTechnicalSupport.MembershipExpiredUrl')">@PageLiteralsHelper.GetLiteral("LIT_BTN_CONTACTAR", PageId, portalConfig)</a>
        }
        if (origin == "_AlertNewPem")
        {
            var comment = (string)ViewData["comment"];
            var comercialAndTechnicalSupport = CompanyHelper.GetComercialAndTechnicalSupport(comment, portalConfig, companycredentials);

            <p class="mAuto mt15 w50 w100_m">
                @PageLiteralsHelper.GetLiteral("LIT_CONTACTOEMAIL_TEXT", PageId, portalConfig):
                <a id="lnk_buy" class="fc_white tdY" onclick="javascript:setSrcInFormIframeNewPem('@comercialAndTechnicalSupport.Comments','@comercialAndTechnicalSupport.MembershipExpiredUrl')">@PageLiteralsHelper.GetLiteral("LIT_BTN_CONTACTAR", PageId, portalConfig)</a>
            </p>
        }
        if (origin == "_SupportPopUp")
        {
            var comment = (string)ViewData["comment"];
            var comercialAndTechnicalSupport = CompanyHelper.GetComercialAndTechnicalSupport(comment, portalConfig, companycredentials);

            <a href="#" class="mb10 w_100 d_block" onclick="javascript:setSrcInFormIframe('@comercialAndTechnicalSupport.Comments','@comercialAndTechnicalSupport.ComercialInformationUrl')">
                @PageLiteralsHelper.GetLiteral("LIT_COMERCIAL_INFORMATION_URL", PageId, portalConfig)
            </a>
        }
        if (origin == "_InfoExceededLimitUsers")
        {
            var comercialAndTechnicalSupport = CompanyHelper.GetComercialAndTechnicalSupport(PageLiteralsHelper.GetLiteral("LIT_COMMENT_FORM_SF", PageId, portalConfig), portalConfig, companycredentials);

            <a href="#" onclick="javascript:setSrcInFormIframeNewPem('@comercialAndTechnicalSupport.Comments','@comercialAndTechnicalSupport.ComercialInformationUrl')">
                <span class="icon i_phone mr5"></span>
                @PageLiteralsHelper.GetLiteral("LIT_COMERCIAL_INFORMATION_URL", PageId, portalConfig)
            </a>
        }
        if (origin == "CompanyMatchesIndex")
        {
            var comercialAndTechnicalSupport = portalConfig.AEPortalConfig.IsNewSupportWithSF ? CompanyHelper.GetComercialAndTechnicalSupport("-", portalConfig) : new Redarbor.Company.WebUI.Models.InternalModels.ComercialAndTechnicalSupportInternalModel();
            <a href="#" onclick="javascript:setSrcInFormIframe('@comercialAndTechnicalSupport.Comments','@comercialAndTechnicalSupport.ComercialInformationUrl')">@PageLiteralsHelper.GetLiteral("LIT_COMERCIAL_INFORMATION_URL", PageId, portalConfig)</a>
        }
        if (origin == "_InfoCanNotViewMatches")
        {
            var comercialAndTechnicalSupport = CompanyHelper.GetComercialAndTechnicalSupport(PageLiteralsHelper.GetLiteral("LIT_COMMENT_FORM_SF", PageId, portalConfig), portalConfig, companycredentials);
            <a href="#" class="mb10 w_100 d_block" onclick="javascript:setSrcInFormIframe('@comercialAndTechnicalSupport.Comments','@comercialAndTechnicalSupport.ComercialInformationUrl')">@PageLiteralsHelper.GetLiteral("LIT_COMERCIAL_INFORMATION_URL", PageId, portalConfig)</a>
        }
    }

    if (portalConfig.AEPortalConfig.HubSpotContact && portalConfig.AEPortalConfig.EnableComercialContactPopup)
    {
        <script>
            $(document).ready(function () {
                if ($('.js_box_atencion_comercial').length > 0) {
                    $('.contactLinkFix').removeClass('hide');
                }
            });
        </script>
    }
}

