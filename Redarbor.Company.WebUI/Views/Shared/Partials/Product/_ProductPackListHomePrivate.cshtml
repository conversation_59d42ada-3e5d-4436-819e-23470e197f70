@model List<Redarbor.Company.WebUI.Models.Company.Product.ProductDataModel>
@using Redarbor.Company.WebUI.Helpers
@using Redarbor.Common.Entities.Enums
@using Redarbor.Master.Entities.Enums

@{
    var PageId = (int)ViewData["pageId"];
    var portalConfig = PortalConfigHelper.GetPortalConfiguration();
    var offerAlone = Model.Count == 1 ? "w_50 y m0_auto" : "";
    var last = Model.Any() ? Model.Last() : null;
}

@foreach (var product in Model)
{    
    var recommended = product.Recommended ? "select" : "";
    <article class="@(offerAlone) box_shadow pt15_r @(recommended) @(product.HasPromotion?"promo":"")">

        @if (product.HasPromotion)
        {
            <span><span>@PageLiteralsHelper.GetLiteral("LIT_PROMOTION", PageId, portalConfig)</span></span>
        }

        @if (product.Recommended)
        {
            <span>
                @PageLiteralsHelper.GetLiteral("LIT_RECOMMENDED", PageId, portalConfig)
            </span>
        }

        <div class="explain">
            @if (portalConfig.AEPortalConfig.PostPublishWithConsumables)
            {
                <h1 class="tc fs24 mb10 plr15 mt0 pt20">@product.ComercialName</h1>
            }
            else
            { 
                <h1 class="tc fs24 mb10 plr15 mt0 pt20">@PageLiteralsHelper.GetLiteral("OFERT", PageId, portalConfig) @char.ToUpper(product.CompanyProductOfferDataModel.Title[0])@product.CompanyProductOfferDataModel.Title.Substring(1).ToLower()</h1>            
            }

            @if (product.HasPromotion)
            {
                <p class="tc plr15">
                   <span class="fs21 fw_b">@String.Format("{0:0}", product.Saving)%</span> @PageLiteralsHelper.GetLiteral("LIT_PROMOTION_INFO", PageId, portalConfig)
                </p>
            }
            else
            {
                <p class="tc fc80 plr15 h_25">@product.LandingProductSubtitle</p>                    
            }
        </div>

        <ul class="bg_bluelight mt0 pt10 pb20 plr10p">
            @foreach (var item in product.FeaturesDescriptions)
            {
                <li class="mt10">
                    <span title="Contiene" class="icon @(item.Available==1?"estado_verde_s":"estado_rojo_s") fl d_ib"></span>
                    <span class="pl10px @(item.Place==1?"fw_b":"")">@item.Literal</span>
                </li>
            }
        </ul>

        @if ((product.AvailabeUnits > 0 || product.GroupId == (short)ProductGroupsEnum.Freemium) && !portalConfig.AEPortalConfig.PostPublishWithConsumables)
        {
            <p class="tc mb30">
                <span class="fs22 @product.CompanyProductOfferDataModel.ClassColor"><strong>@product.AvailabeUnits</strong> <span class="fc80 fs16">@PageLiteralsHelper.GetLiteral("AVAILABLE_OFFERS", PageId, portalConfig)</span></span>
                <span class="bar_pack" off-used="@product.UsedUnits" off-total="@product.TotalUnits">
                    <span class="bar @product.CompanyProductOfferDataModel.ClassColor" style="overflow: hidden; width: @(product.PercentUnits)%;"></span>
                </span>
            </p>

            if (product.AvailabeUnits > 0)
            {
                <div class="plr30">
                    <a href="@Url.Action("Index", "CompanyOffersPublish" , new { sg = EncryptationHelper.Encrypt(product.CompanyProductOfferDataModel.SubGroupId.ToString())})" class="submit_n mt10 mb15"><span title="@PageLiteralsHelper.GetLiteral("LIT_ICON_PUBLICAR", PageId, portalConfig)"></span>@PageLiteralsHelper.GetLiteral("BTN_PUBLICAR_OFERTA", PageId, portalConfig)</a>
                </div>
            }
            else
            {
                <a class="submit_n mt10 mb15 disabled">@PageLiteralsHelper.GetLiteral("BTN_PUBLICAR_OFERTA", PageId, portalConfig)</a>
            }
        }
        else
        {
            <p class="tc mb20 pt10">                
                <span class="fc80 fs16">@PageLiteralsHelper.GetLiteral("LIT_DESDE", PageId, portalConfig)</span>                
                <span class="fs22">
                    <strong>
                        @Html.Raw(product.HasPromotion ? product.LiteralShowTotalPromotion : product.LiteralShowUnit)
                    </strong>
                    <span class="fc80 fs16">
                        @if (product.HasPromotion)
                        {
                            <span class="tdL">(@Html.Raw(product.TotalPriceWithoutPromotion))</span>
                        }
                        @PageLiteralsHelper.GetLiteral("LIT_POROFERTA", PageId, portalConfig)
                    </span>
                </span>
                <span class="fs12 mt5 fc80 d_block">@PageLiteralsHelper.GetLiteral("INFO_EXTRA_PRODUCT", PageId, portalConfig)</span>
            </p>

            <div class="plr30">
                <a class="submit_n mt10 mb15" href="@Url.Action("Index", "MultiPurchaseCart" , new { prod=EncryptationHelper.Encrypt(product.Id.ToString()),
                                                    p = product.CompanyProductOfferDataModel.GroupId == (int)ProductGroupsEnum.Membership ? EncryptationHelper.Encrypt(((int)PageEnum.MembershipCart).ToString()) : EncryptationHelper.Encrypt(((int)PageEnum.PackCart).ToString()),
                                                    btn = EncryptationHelper.Encrypt(product.BtnOriginId.ToString())
                                                })">@PageLiteralsHelper.GetLiteral("LIT_COMPRAR", PageId, portalConfig)</a>
            </div>
        }

    </article>
    if (product != last)
    {
        <div class="w_2"></div>
    }   
}

