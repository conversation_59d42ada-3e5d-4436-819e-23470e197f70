@using Redarbor.Company.WebUI.Helpers;
@using Redarbor.Common.Entities.Enums

@{
    var portalConfig = (Redarbor.Common.Entities.Configuration.PortalConfig)ViewData["portalConfig"];
    int pageId = (int)PageEnum.CompanyNews;
}


<div class="popup draggable" id="deleteNewsPopUp">
    <div>
        <div class="dragger">
            <div class="btn_click js_hide_popup">
                <span class="icon i_close"></span>
            </div>
        </div>
        <div class="content pt0">
            <p class="fs18 fwB tc mbB">@PageLiteralsHelper.GetLiteral("QUESTION_DELETE_TITLE", pageId, portalConfig)</p>
            <p>@PageLiteralsHelper.GetLiteral("QUESTION_DELETE_DETAIL", pageId, portalConfig)</p>
            <button class="b_primary mtB js_hide_popup" id="bttDeletePopUpOk">@PageLiteralsHelper.GetLiteral("LIT_DELETE", pageId, portalConfig)</button>
            <button class="b_primary mtB js_hide_popup">@PageLiteralsHelper.GetLiteral("LIT_CANCEL", pageId, portalConfig)</button>
        </div>
    </div>
</div>