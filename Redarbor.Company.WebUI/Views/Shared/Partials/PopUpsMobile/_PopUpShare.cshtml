@model Redarbor.Company.WebUI.Models.Company.Offer.OfferDataModel
@using Redarbor.Company.WebUI.Helpers;
@using Redarbor.Common.Entities.Enums

@{
    var portalConfig = (Redarbor.Common.Entities.Configuration.PortalConfig)ViewData["portalConfig"];
    int pageId = (int)PageEnum.OfferList;
}


<div class="popup full ignore-slide-down" id="<EMAIL>">
    <div>
        <div class="js_click_btn_share">
            <div class="btn_click_return js_hide_popup">
                <span class="icon i_back"></span>
            </div>
            <p class="ptB tc">@PageLiteralsHelper.GetLiteral("LIT_SHARE_OFFER", pageId, portalConfig)</p>

            <div class="ptB mtB opt_bubble" data-offer-detail-share-options-popup data-url="@Model.OfferPublicUrl">
                <div class="grid2 tc fc_aux">
                    <div class="box_border" data-offer-detail-share-option-whatsapp>
                        <span class="icon i_whatsapp fc_aux fs28"></span>
                        <p class="mt10">@PageLiteralsHelper.GetLiteral("LIT_SHARE_WHATSAPP", pageId, portalConfig)</p>
                    </div>
                    <div class="box_border" data-offer-detail-share-option-facebook>
                        <span class="icon i_fb fc_aux fs28"></span>
                        <p class="mt10">@PageLiteralsHelper.GetLiteral("LIT_SHARE_FACEBOOK", pageId, portalConfig)</p>
                    </div>
                    <div class="box_border" data-offer-detail-share-option-twitter>
                        <span class="icon i_tw fc_aux fs28"></span>
                        <p class="mt10">@PageLiteralsHelper.GetLiteral("LIT_SHARE_TWITTER", pageId, portalConfig)</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

