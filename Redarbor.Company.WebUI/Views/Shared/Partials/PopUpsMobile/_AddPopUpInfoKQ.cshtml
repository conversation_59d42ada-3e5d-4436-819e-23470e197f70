@using Redarbor.Company.WebUI.Helpers
@using Redarbor.Common.Entities.Enums

@{
    var portalConfig = (Redarbor.Common.Entities.Configuration.PortalConfig)ViewData["portalConfig"];
    int pageId = (int)PageEnum.PublishOffer;
}

<div class="popup draggable" id="popupInfoKQ">
    <div>
        <div class="dragger">
            <div class="btn_click js_hide_popup">
                <span class="icon i_close"></span>
            </div>
        </div>
        <div class="content pt0">
            <p class="fs18 fwB tc mbB">@PageLiteralsHelper.GetLiteral("LIT_KQ_TITLE", pageId, portalConfig)</p>

            <p class="mbB tc">@PageLiteralsHelper.GetLiteral("LIT_INFO_ADD_KQ", pageId, portalConfig)</p>
        </div>
    </div>
</div>
