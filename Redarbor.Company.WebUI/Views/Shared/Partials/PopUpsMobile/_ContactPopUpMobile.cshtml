@using Redarbor.Common.Entities.Enums
@using Redarbor.Company.WebUI.Helpers
@model Redarbor.Company.WebUI.Models.Company.Shared.PopUpDataModel

@{
    var onClickAction = (string)ViewData["OnClick"];
    var idPopUp = (string)ViewData["IdPopUp"];
    var portalConfig = (Redarbor.Common.Entities.Configuration.PortalConfig)ViewData["portalConfig"];
}


<div class="popup draggable" id="@idPopUp">
    <div>
        <div class="dragger">
            <div class="btn_click js_hide_popup">
                <span class="icon i_close"></span>
            </div>
        </div>
        <div class="content pt0">
            <p class="fs18 fwB tc mbB">@Model.Title</p>
            <p id="PopupMessage">@Html.Raw(Model.Message)</p>
            <button class="b_primary mtB js_hide_popup featurebtt hide" onclick="@onClickAction" type="button" id="btnGoToContactForm" > @Model.TitleBtnOk </button>   
        </div>
    </div>
</div>