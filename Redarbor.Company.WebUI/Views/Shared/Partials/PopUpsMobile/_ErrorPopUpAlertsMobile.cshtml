@model Redarbor.Company.WebUI.Models.Company.Shared.PopUpErrorAlertsDataModel

<div class="popup full show_popup" id="popupErrorNIT">
    <div>
        <div>
            <div class="bg_yellow mB_neg pAll30 tc">
                <div class="btn_click js_hide_popup">
                    <span class="icon i_close"></span>
                </div>
                <span class="icon i_alert_circle_neg fs60"></span>
                <p class="fs18 fwB mtB mb5">@(Model.Title)</p>
                <p>@(Model.Reason)</p>
            </div>
            <div class="pAllB">
                <p class="tc">@(Model.Solution)</p>
                <div class="box_fix dFlex">
                    <button class="b_primary_inv w50 fwN ml5 mr5" onclick="closePopUp();">@(Model.TextButtonClose)</button>
                    <button type="submit" class="b_primary ml5 mr5" onclick="window.location.href='@(Model.UrlToRedirecto)';">@(Model.TextButtonRedirect)</button>
                </div>
            </div>
        </div>
    </div>
</div>