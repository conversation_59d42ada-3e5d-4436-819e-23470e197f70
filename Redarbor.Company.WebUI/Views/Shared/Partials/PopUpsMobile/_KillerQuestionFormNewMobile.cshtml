@model Redarbor.Company.WebUI.Models.Company.Offer.KillerQuestionDataModel
@using Redarbor.Company.WebUI.Helpers;
@using Redarbor.Common.Entities.Enums
@using Redarbor.Company.WebUI.Models.Company.Offer

@{
    var OPEN_KQ = "1";
    short pageId = (int)PageEnum.PublishOffer;
    var portalConfig = (Redarbor.Common.Entities.Configuration.PortalConfig)ViewData["portalConfig"];
    var killerQuestionTypeList = (List<SelectListItem>)ViewData["killerQuestionTypeList"];
    var killerQuestionScoreList = (List<SelectListItem>)ViewData["killerQuestionScoreList"];
    var killerDataList = new List<KillerQuestionDataDataModel>()
{
        new KillerQuestionDataDataModel(),
        new KillerQuestionDataDataModel(),
        new KillerQuestionDataDataModel(),
        new KillerQuestionDataDataModel(),
        new KillerQuestionDataDataModel()
    };
}


<div class="popup full ignore-slide-down" id="popupKQ">
    <div>
        <div style="padding-bottom: 85px !important;">
            <div class="title_page thin bg_none">
                <div class="btn_back title js_hide_popup noLoader" id="btCancelAddKillerQuestion">
                    <span class="icon i_back"></span>
                </div>
                @PageLiteralsHelper.GetLiteral("LIT_EDIT_QUESTION", pageId, portalConfig)
            </div>
            <div id="openQuestionsLimit" class="box_or tc p20 hide">
                <span class="fc_error">@PageLiteralsHelper.GetLiteral("FORM_KQ_OPEN_LIMIT", pageId, portalConfig)</span><br>@PageLiteralsHelper.GetLiteral("FORM_KQ_OPEN_LIMIT_DELETE", pageId, portalConfig)
            </div>
            <div id="closedQuestionsLimit" class="box_or tc p20 hide">
                <span class="fc_error">@PageLiteralsHelper.GetLiteral("FORM_KQ_CLOSED_LIMIT", pageId, portalConfig)</span><br>@PageLiteralsHelper.GetLiteral("FORM_KQ_CLOSED_LIMIT_DELETE", pageId, portalConfig)
            </div>
            <div class="field_select">
                <label class="fwB">@PageLiteralsHelper.GetLiteral("LIT_TIPO_PREGUNTA", pageId, portalConfig)</label>
                @Html.DropDownListFor(m => m.Type, killerQuestionTypeList, new { @class = "w_40", @id = "addKillerQuestionType", Name = "addKillerQuestionType" })
            </div>
            <div class="field_textarea">
                <label class="fwB">@PageLiteralsHelper.GetLiteral("LIT_PREGUNTA", pageId, portalConfig)</label><span class="required" aria-required="true">*</span>
                @Html.TextAreaFor(m => m.Title, new { maxlength = 200, @id = "addKillerQuestionQuestion", Name = "addKillerQuestionQuestion", @rows = "10" })
                <div class="info_box">
                    @Html.Raw(PageLiteralsHelper.GetLiteral("LIT_QUESTION_RULES", pageId, portalConfig))
                </div>
                <p class="field_help">(@PageLiteralsHelper.GetLiteral("FORM_KQ_MAX_LENGTH", pageId, portalConfig))</p>
                <span class="field-validation-error hide" id="killerQuestionsErrorsAnswer"></span>
            </div>

            <div id="killerquestionclosed" class="@(killerQuestionTypeList.First().Value == OPEN_KQ ? "hide" : "")">
                @if (killerDataList.Any())
                {
                    for (var i = 0; i < killerDataList.Count(); i++)
                    {
                        var numberKiller = i + 1;
                        <div class="mbB @(numberKiller > 2 ? "hide" : "")" id="more_answer_@numberKiller">
                            <label class="fwB">@PageLiteralsHelper.GetLiteral("LIT_RESPUESTA", pageId, portalConfig) @numberKiller<span class="required">@(numberKiller < 3 ? "*" : "")</span></label>
                            <div class="field_input mb0">
                                @Html.TextBoxFor(m => killerDataList[i].Answer, new { maxlength = 200, Name = "addKillerQuestionValue" + numberKiller, @id = "addKillerQuestionValue" + numberKiller, @class = "add_killer_question_value" })
                                @Html.ValidationMessage("addKillerQuestionValue" + numberKiller, "")
                                <span class="field-validation-error hide" id="@("killerQuestionsErrorsQuestion" + numberKiller)"></span>
                            </div>
                            <div class="dFlex vm_fx mt10">
                                <span class="fs14">Puntuación</span>
                                <div class="field_checkbox mlAuto mb0">
                                    <label class="checkbox fs14 mb0">
                                        <input type="checkbox" id="excluyenteCheckbox@(numberKiller)" name="rating@(numberKiller)" class="addKillerQuestionAnswerScore@(numberKiller) rating_excluyente excluyente_checkbox" data-index="@numberKiller">
                                        <span class="input"></span>
                                        <span>Excluyente</span>
                                    </label>
                                </div>
                            </div>
                            <div class="range_container mb50 slider_points_answer" id="slider-points-answer-@numberKiller">
                                <div class="sliders_control">
                                    @Html.TextBoxFor(m => killerDataList[i].Score, new
                                    {
                                        @class = "js_slider add_killer_question_answer_score",
                                        @type = "range",
                                        @id = "addKillerQuestionAnswerScore" + numberKiller,
                                        @value = "0",
                                        @min = "0",
                                        @max = "10",
                                        @step = "1",
                                        @style = "background: linear-gradient(to right, rgb(225, 229, 234) 0%, rgb(225, 229, 234) 0%, rgb(13, 56, 120) 0%, rgb(13, 56, 120) 0%, rgb(225, 229, 234) 0%, rgb(225, 229, 234) 100%);"
                                    })
                                </div>
                                <div class="form_control">
                                    <span class="posAbs pt10 pl10">0</span>
                                    <span class="posAbs pt10 pl10 fwB js_control_slider min_points_answers" id="min_points_answer_@(numberKiller)" style="left: 0%;">0</span>
                                    <span class="posAbs pt10 pl10" style="left: 100%">10</span>
                                </div>
                            </div>
                        </div>
                    }
                }

                <div class="b_transparent mbB" id="btn_more_answers">@PageLiteralsHelper.GetLiteral("LIT_ADD_MORE_ANSWERS", pageId, portalConfig)</div>
            </div>
            <div class="box_fix">
                <input type="button" class="b_primary" id="confirmaddkillerquestion" onclick="AddKillerQuestion();" value="@PageLiteralsHelper.GetLiteral("LIT_ADD", pageId, portalConfig)" />
            </div>


        </div>
    </div>

</div>
