@using Redarbor.Company.WebUI.Helpers;
@using Redarbor.Common.Entities.Enums;

@{
    int PageId = (int)PageEnum.MatchOffer;
    var portalConfig = (Redarbor.Common.Entities.Configuration.PortalConfig)ViewData["portalConfig"];
    var cityId = (int)ViewData["idCity"];
    var offerId = ViewData["idOffer"];
    var baseProduct = ViewData["baseProduct"];
    var localizations = PopUpAddGeolocalizationOfferHelper.GetGeolocalizationByIdCity(portalConfig, cityId);
}

<div id="PopUpAddGeolocalizationOffer" style="display:none ;">
    <div class="blockUI blockOverlay" style="z-index: 9999; border: none; margin: 0px; padding: 0px; width: 100%; height: 100%; top: 0px; left: 0px; opacity: 0.6; position: fixed; background-color: rgb(0, 0, 0);"></div>
    <div class="blockUI blockMsg blockPage" style="z-index: 9999; position: fixed; padding: 0px; margin: 0px; width: 35%; top: 35%; left: 32.5%; text-align: center; color: rgb(0, 0, 0); border: 3px solid rgb(170, 170, 170); background-color: rgb(255, 255, 255);">
        <div style="cursor: default;" class="popup_gestof popup_gestcan content_popup">
            <div class="md-modal md-effect-3 md-show">
                <div class="md-content popUp_save popUp_remove w_100">
                    <button><span title="Cerrar" class="js_hide_proximidad icon ico_close" onclick="$('#Distance').val('0');$.unblockUI();"></span></button>

                    <span class="c_txt pt15">@PageLiteralsHelper.GetLiteral("LIT_GEO_POPUP_TITLE", PageId, portalConfig)</span>
                    <span class="txt pr15px pl15px pb15">@PageLiteralsHelper.GetLiteral("LIT_GEO_POPUP_DESCRIPTION", PageId, portalConfig)</span>

                    <div id="PostalCodeDiv" class="w_50i m0_auto fn pl15px tl">
                        @Html.DropDownList("postalcode", localizations, "Introduce el CP de esta vacante", new { @class = "cm-12" })
                    </div>
                    <div class="submitbtn pt0i">
                        <input class="b_primary" type="button" id="btnSaveLocation" onclick="javascript: UpdateGeoLocalizationOffer('@offerId', $('#postalcode').val(), @portalConfig.PortalId);" value="Aceptar">
                        <input id="lnkCancelar2" value="Ignorar" class="js_hide_proximidad cancelar_link" type="button" onclick="$('#Distance').val('0');$.unblockUI();">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
