@model Redarbor.Company.WebUI.Models.Home.HomeDataModel
@using Redarbor.Company.WebUI.Helpers;
@using Redarbor.Common.Entities.Enums;
@using System.Web.Optimization;

@{
    short pageId = (short)PageEnum.HomeMasterCompany;
    short pageIdHome = (short)PageEnum.HomePublica;
    var portalConfig = PortalConfigHelper.GetPortalConfiguration();
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">

<head>

    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    @if (Model.IsProductsAndServicesLanding)
    {
        @Styles.Render(string.Format("~/c/v2/css/landing_productos.css", portalConfig.staticVirtualBundle))
    }
    else
    {
        @Styles.Render(string.Format("~/bundles/css/pem_new{0}", portalConfig.staticVirtualBundle))
    }
    <link rel="shortcut icon" type="image/x-icon" href="@Url.Content(string.Format("{0}img/favicon.ico{1}", portalConfig.PrefixPathImage, portalConfig.SufixPathImage))" />

    <link rel="apple-touch-icon" sizes="120x120" href="@Url.Content(string.Format("{0}img/apple-touch-icon-120x120.png{1}", portalConfig.PrefixPathImage, portalConfig.SufixPathImage))" />
    <link rel="apple-touch-icon" href="@Url.Content(string.Format("{0}img/apple-touch-icon.png{1}", portalConfig.PrefixPathImage, portalConfig.SufixPathImage))" />

    <title>@PageLiteralsHelper.GetLiteral("HEAD_TITLE_BOLSA_TRABAJO_OFERTAS_EMPLEO", pageIdHome, portalConfig)</title>

    @RenderSection("MetaSection", required: true)


    @if (portalConfig.AEPortalConfig.HasHubSpot)
    {
        <!-- Start of HubSpot Embed Code -->
        <script type="text/javascript" id="hs-script-loader" async defer src="//js.hs-scripts.com/8889978.js?businessUnitId=660018"></script>
        <!-- End of HubSpot Embed Code -->
    }

</head>
<!-- Javascript -->
<!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->
<!--[if lt IE 9]>

    <script type='text/javascript' src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <script type='text/javascript' src="//css3-mediaqueries-js.googlecode.com/svn/trunk/css3-mediaqueries.js"></script>

<![endif]-->

<body>
    <header>
        <div class="menu_tools">
            <div class="container">
                <a class="menu_m" id="js_abrirnav"><span class="icon i_menu"></span></a>
                <a href="@portalConfig.url_web" class="logo home">
                    <img srcset="@Url.Content(string.Format("{0}img/logoportalwhite.svg{1}", portalConfig.PrefixPathImage, portalConfig.SufixPathImage))" alt="@PageLiteralsHelper.GetLiteral("ALT_IMG_LOGO_PORTAL", pageId, portalConfig)" class="cep_logo">
                </a>
                <nav class="public">
                    <a href="@portalConfig.url_web">@PageLiteralsHelper.GetLiteral("MASTER_PERSONA", pageId, portalConfig)</a>
                    <a href="@portalConfig.url_empresa" class="sel">@PageLiteralsHelper.GetLiteral("MASTER_EMPRESA", pageId, portalConfig)</a>
                    @if (Model.ShowCompaniesTab)
                    {
                        <a href="@PageLiteralsHelper.GetLiteral("LIT_COMPANIES_LINK", pageId, portalConfig)">@PageLiteralsHelper.GetLiteral("LIT_COMPANIES", pageId, portalConfig)</a>
                    }
                    @if (Model.ShowSalariesTab)
                    {
                        <a href="@PageLiteralsHelper.GetLiteral("LIT_SALARIES_LINK", pageId, portalConfig)">@PageLiteralsHelper.GetLiteral("LIT_SALARIES", pageId, portalConfig)</a>
                    }
                    @if (!string.IsNullOrEmpty(Model.UrlBlog))
                    {
                        <a href="@Model.UrlBlog" target="_blank">@PageLiteralsHelper.GetLiteral("LIT_BLOG", pageId, portalConfig)</a>
                    }

                    @{ Html.RenderPartial("_NewLogin", Model.LoginDataModel, new ViewDataDictionary { { "portalConfig", portalConfig } }); }
                </nav>
            </div>
            <nav id="menu" style="display: none;" class="">
                <div>
                    <a href="@portalConfig.url_web">
                        <img srcset="@Url.Content(string.Format("{0}img/logoportalwhite.svg{1}", portalConfig.PrefixPathImage, portalConfig.SufixPathImage))" alt="@PageLiteralsHelper.GetLiteral("ALT_IMG_LOGO_PORTAL", pageId, portalConfig)" class="cep_logo">
                    </a>
                    <a href="#" class="icon i_close_w fr" id="click_close" aria-label="Cerrar menú"></a>
                </div>

                <a href="@portalConfig.url_web">@PageLiteralsHelper.GetLiteral("MASTER_PERSONA", pageId, portalConfig)</a>
                <a href="@portalConfig.url_empresa" class="sel">@PageLiteralsHelper.GetLiteral("MASTER_EMPRESA", pageId, portalConfig)</a>
                @if (Model.ShowCompaniesTab)
                {
                    <a href="@PageLiteralsHelper.GetLiteral("LIT_COMPANIES_LINK", pageId, portalConfig)">@PageLiteralsHelper.GetLiteral("LIT_COMPANIES", pageId, portalConfig)</a>
                }
                @if (Model.ShowSalariesTab)
                {
                    <a href="@PageLiteralsHelper.GetLiteral("LIT_SALARIES_LINK", pageId, portalConfig)">@PageLiteralsHelper.GetLiteral("LIT_SALARIES", pageId, portalConfig)</a>
                }
                @if (!string.IsNullOrEmpty(Model.UrlBlog))
                {
                    <a href="@Model.UrlBlog" target="_blank">@PageLiteralsHelper.GetLiteral("LIT_BLOG", pageId, portalConfig)</a>
                }
            </nav>
        </div>
    </header>

    @RenderBody()

    @{ Html.RenderPartial("_NewFooter"); }

    @Scripts.Render("~/bundles/js/jquery")
    @Scripts.Render("~/bundles/js/required")
    @Scripts.Render("~/bundles/js/menutools")

    <script type="text/javascript">
        SendGTMDataLayer('@Url.Action("GetGTMDataLayerData", "GoogleTagManager")');
    </script>
    @{ Html.RenderPartial("_GTMandAnalytics"); }

    @if (portalConfig.AEPortalConfig.ShowCookiesConsent)
    {
        Html.RenderPartial("_CookiesConsent", new ViewDataDictionary { { "portalConfig", portalConfig }, { "pageId", (int)pageId } });
    }
</body>
</html>