@model Redarbor.Company.WebUI.Models.SimplifiedRegister.SimplifiedRegisterDataModel
@using Redarbor.Company.WebUI.Helpers
@using Redarbor.Common.Entities.Enums
@using System.Web.Optimization;

@{
    short PageId = (short)PageEnum.HomeMasterCompany;
    var portalConfig = PortalConfigHelper.GetPortalConfiguration();
}

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="@portalConfig.cultura">
<head>

    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="robots" content="noindex, nofollow" />

    @RenderSection("MetaSection", required: false)

    <link title="@PageLiteralsHelper.GetLiteral("LIT_COMPUTRABAJO", PageId, portalConfig)" rel="shortcut icon" type="image/x-icon" href="@Url.Content(string.Format("{0}img/favicon.ico{1}", portalConfig.PrefixPathImage, portalConfig.SufixPathImage))" />
    <title>@PageLiteralsHelper.GetLiteral("HEAD_TITLE_REGISTER_COMPANY", PageId, portalConfig)</title>
    <meta name="description" content="@PageLiteralsHelper.GetLiteral("META_DESCRIPTION_REGISTER_COMPANY", PageId, portalConfig)" />
    <meta name="keywords" content="@PageLiteralsHelper.GetLiteral("META_KEYWORDS_REGISTER_COMPANY", PageId, portalConfig)" />

    <script src="https://www.googleoptimize.com/optimize.js?id=OPT-T9XF9K8"></script>

    @Scripts.Render("~/bundles/js/jquery")
    @Scripts.Render("~/bundles/js/required")

    @RenderSection("CustomScriptsSection", required: false)
    @Styles.Render(string.Format("~/bundles/css/pem_new{0}", portalConfig.staticVirtualBundle))
    @RenderSection("CustomStylesSection", required: false)
    <script>
        $.validator.setDefaults({
            onkeyup: function (element) {
                return !$(element).hasClass('js-remote');
            }
        });
    </script>

    @if (portalConfig.AEPortalConfig.HasHubSpot)
    {
        <!-- Start of HubSpot Embed Code -->
        <script type="text/javascript" id="hs-script-loader" async defer src="//js.hs-scripts.com/8889978.js?businessUnitId=660018"></script>
        <!-- End of HubSpot Embed Code -->
    }

</head>
<body>
    <div>
        @RenderSection("HeaderSection", required: false)
    </div>

    @RenderSection("PopupSection", required: false)

<div>
    @RenderBody()
    <script type="text/javascript">
        SendGTMDataLayer('@Url.Action("GetGTMDataLayerData", "GoogleTagManager")');
    </script>
    @{ Html.RenderPartial("_GTMandAnalytics"); }
</div>

    @RenderSection("CustomScriptsFooterSection", required: false)
</body>
</html>
