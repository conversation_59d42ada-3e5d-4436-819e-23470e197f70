@using Redarbor.Company.WebUI.Helpers
@using Redarbor.Common.Entities.Enums
@using Redarbor.Master.Entities.Enums
@using Redarbor.Master.Entities.Product
@using System.Web.Optimization;

@{
    short PageId = (short)PageEnum.HomeMasterCompany;
    var portalConfig = PortalConfigHelper.GetPortalConfiguration();
}

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="@portalConfig.Lang">
<head>
    @RenderSection("TitleSection", required: true)

    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="robots" content="noindex, nofollow" />

    @RenderSection("MetaSection", required: false)

    <link title="@PageLiteralsHelper.GetLiteral("LIT_COMPUTRABAJO", PageId, portalConfig)" rel="shortcut icon" type="image/x-icon" href="@Url.Content(string.Format("{0}img/favicon.ico{1}", portalConfig.PrefixPathImage, portalConfig.SufixPathImage))" />
    @Scripts.Render("~/bundles/js/jquery")
    @Scripts.Render("~/bundles/js/required")

    @RenderSection("CustomScriptsSection", required: false)
    @Styles.Render(string.Format("~/bundles/css/pem_new{0}", portalConfig.staticVirtualBundle))
    @RenderSection("CustomStylesSection", required: false)
    <script>
        $.validator.setDefaults({
            onkeyup: function (element) {
                return !$(element).hasClass('js-remote');
            }
        });
    </script>

    @if (portalConfig.AEPortalConfig.HasHubSpot)
    {
        <!-- Start of HubSpot Embed Code -->
        <script type="text/javascript" id="hs-script-loader" async defer src="//js.hs-scripts.com/8889978.js?businessUnitId=660018"></script>
        <!-- End of HubSpot Embed Code -->
    }

</head>
<body>

    <div class="container dFlex vm_fx pt10 pb10">
        <a class="w25_m tc_m" href="@Url.Action("", "Home")"> <img srcset="@Url.Content(string.Format("{0}img/logoportal.svg{1}", portalConfig.PrefixPathImage, portalConfig.SufixPathImage))" alt="@PageLiteralsHelper.GetLiteral("ALT_IMG_LOGO_PORTAL", PageId, portalConfig)" class="cep_logo"></a>
    </div>
    <div>
        @RenderSection("HeaderSection", required: false)
    </div>


    @RenderSection("PopupSection", required: false)

    <div>
        @RenderBody()
    </div>


    @{ Html.RenderPartial("_NewFooter"); }
    <script type="text/javascript">
        SendGTMDataLayer('@Url.Action("GetGTMDataLayerData", "GoogleTagManager")');
    </script>
    @{ Html.RenderPartial("_GTMandAnalytics"); }

    @{
        if (SecurityHelper.IsUserLoggedIn())
        {
            CompanyProductEntity companyproduct = CompanyHelper.GetCurrentProduct(SecurityHelper.GetCompanyCredentials());

            if (companyproduct != null)
            {
                var hasNewChat = CompanyHelper.HasFreemiumChatHeader(companyproduct, portalConfig);
                var isFeatureChatActive = CompanyHelper.HasMessagesMailingHeader(companyproduct, portalConfig);

                <script type="text/javascript">
                    SessionHeartbeat.Setup('@Url.Action("KeepSessionAlive", "Login")');
                    TimeUseCheckToAdd.Setup('@Url.Action("TimeUseByUser", "TimeUse")');

                    var urlActions = {
                        getCountMessagesPendingHeader: '@(hasNewChat ? Url.Action("GetCountMessagesPendingHeader", "CompanyConversations") : Url.Action("GetCountMessagesPendingHeader", "CompanyMessages"))',
                        getConversation: '@(hasNewChat ? Url.Action("GetConversationsHeader", "CompanyConversations") : Url.Action("GetConversationsHeader", "CompanyMessages"))',
                        trackButtonPage: '@Url.Action("SetSessionTrack", "TrackButton")'
                    };

                    var configModel = {
                        urlActions: urlActions,
                        isFeatureChatActive: '@(isFeatureChatActive || hasNewChat)' === 'True',
                        urlKpis: "@Url.Action("ExecuteAddKpi", "Company")",
                        urlCompany: "@portalConfig.url_empresa"
                    };

                    InitializeMaster(configModel);
                </script>
            }
        }
    }

    @RenderSection("CustomScriptsFooterSection", required: false)
</body>
</html>
