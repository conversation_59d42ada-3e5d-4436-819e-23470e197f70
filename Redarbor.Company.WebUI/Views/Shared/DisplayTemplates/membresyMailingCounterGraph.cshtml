@model List<Redarbor.Company.WebUI.Models.Company.Shared.KpiDataModel>
@using Redarbor.Company.WebUI.Helpers
@using Redarbor.Common.Entities.Enums
@using Redarbor.Company.WebUI.Enums

@{
    short PageId = (int)PageEnum.HomePrivada;
    var portalConfig = (Redarbor.Common.Entities.Configuration.PortalConfig)ViewData["portalConfig"];
    var canvasName = (string)ViewData["canvasName"];
    var display = (bool)ViewData["display"];
    var styleCanvas = (string)ViewData["styleCanvas"];
}

<div id="div_graph_@canvasName" class="g_estadistica" style="@(display ? string.Empty : "display:none")">
    <p style="float:left; padding:10px 0 0 25px;">
        @PageLiteralsHelper.GetLiteral("LAST_30_DAYS", PageId, portalConfig)
    </p>
    <p style="float:right; padding:10px 25px 0 0;">
        @PageLiteralsHelper.GetLiteral("P_TOTAL", PageId, portalConfig) @Model.Sum(m => m.total)
    </p>
    <div class="pt10 m0_auto" style="@(styleCanvas)">
        <canvas id="canvas_@canvasName" width="235" height="120"></canvas>
    </div>
</div>
<script>
    $(document).ready(function () {
        var ctx_@canvasName = document.getElementById("canvas_@canvasName");

         var @canvasName = new Chart(ctx_@canvasName, {
             type: 'line',
            data: {
                labels: [@Html.Raw(string.Join(",", Model.Select(m => string.Format("\"{0}\"", StringToolsHelper.GetLocalizedDate(m.date, DateFormatEnum.ShortDate)))))],
                datasets: [
                    {
                        data: [@string.Join(",", Model.Select(m => m.total).ToList())],
                        backgroundColor: '#97BBCD'
                    }
                ]
            },
            options: {
                scaleBeginAtZero: true,
                responsive: false,
                scales: {
                    yAxes: [{
                        ticks: {
                            fontSize: 9
                        }
                    }]
                },
                legend: {
                    display: false,
                },
                tooltips: {
                    enabled: true,
                    displayColors: false
                }
            }
         });

    });

</script>
