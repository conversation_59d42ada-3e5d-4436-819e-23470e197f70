@model Redarbor.Company.WebUI.Models.Company.Shared.EmailDataModel
@using Redarbor.Common.Entities.Enums;
@using Redarbor.Company.WebUI.Helpers;
@using Redarbor.Company.WebUI.Enums;
@{
    var pageId = (short)ViewData["pageId"]; ;
    var portalConfig = (Redarbor.Common.Entities.Configuration.PortalConfig)ViewData["portalConfig"];
    System.Net.ServicePointManager.SecurityProtocol |= System.Net.SecurityProtocolType.Tls11 | System.Net.SecurityProtocolType.Tls12;
}
@using (Html.BeginForm("SendEmail", "Landing", FormMethod.Post, new { id = "frmSendEmail"}))
{
    @Html.AntiForgeryToken()
    <p class="mb15 fs18">Contáctanos y te informaremos</p>
    if (Model.IsEmailOk == ResultEnum.OK.ToString())
    {
        <div class="box_ok small mb15">
            <p>@PageLiteralsHelper.GetLiteral("LIT_CONSULTA_RECIBIDA_CORRECTAMENTE", pageId, portalConfig)</p>
            <p>@PageLiteralsHelper.GetLiteral("LIT_BREVE_CONTACTO", pageId, portalConfig)</p>
        </div>
    }
    else if (Model.IsEmailOk == ResultEnum.KO.ToString())
    {
        <div class="box_error small mb15">
            <p>@PageLiteralsHelper.GetLiteral("LIT_CONSULTA_NO_ENVIADA", pageId, portalConfig)</p>
            <p>@PageLiteralsHelper.GetLiteral("LIT_VUELVA_INTENTARLO", pageId, portalConfig)</p>
        </div>
    }
    <div class="field_input">
        <label>Tu e-mail<span class="required">*</span></label>
        @Html.TextBoxFor(model => model.Email, new { @class = "cm-12" })
        <span class="field-validation-valid">@Html.ValidationMessageFor(model => model.Email, "", new { @class = "pos_rel fl w_100 valid-msg" })</span>
    </div>
    <div class="field_select">
        <label>Asunto<span class="required">*</span></label>
        @Html.DropDownListFor(model => model.SubjectSelected, Model.Subjects, "Seleccion un asunto", new { @class = "cm-12" })
        <span class="field-validation-valid">@Html.ValidationMessageFor(model => model.SubjectSelected, "", new { @class = "pos_rel fl  w_100" })</span>
    </div>
    <div class="field_textarea">
        <label>Texto del mensaje<span class="required">*</span></label>
        @Html.TextAreaFor(model => model.Message, new { @class = "cm-12", rows = "2", cols = "20" })
        <span class="field-validation-valid">@Html.ValidationMessageFor(model => model.Message, "", new { @class = "pos_rel fl w_100" })</span>
    </div>
    <p class="mb15 fc_aux fs14">Al hacer clic en enviar consulta, aceptas las <a target="_blank" href="@portalConfig.url_web/avisolegal/">Condiciones legales</a> y la <a target="_blank" href="@portalConfig.url_web/privacidad/">Politica de privacidad</a> de CompuTrabajo. <a class="js_show js_show_legal">Ver detalle legal</a></p>
    <div class="box_border hide js_box_legal mb15 w80 fs12">
        <p class="fwB">Información básica sobre los datos almacenados</p>
        <p class="fwB mt10 mb5">Responsable</p>
        <p>DGNET Ltd., registrada en Escocia n°189977, con domicilio en 64A Cumberland Street, Edimburgo EH3 6RE, Reino Unido y teléfono de contacto: +44 ************</p>        
        <p class="fwB mt10 mb5">Finalidad</p>
        <p>Gestionar correctamente los servicios solicitados (crear una cuenta en CompuTrabajo, inscripción en vacantes de empleo, contactar con potenciales empleadores, entre otros) y recibir comunicaciones y promociones de DGNET Ltd.</p>
        <p class="fwB mt10 mb5">Legitimación</p>
        <p>Ejecución de un contrato, consentimiento, interés legítimo y cumplimiento de una obligación legal.</p>
        <p class="fwB mt10 mb5">Destinatarios</p>
        <p>Únicamente se cederán datos por obligación legal.</p>
        <p class="fwB mt10 mb5">Derechos</p>
        <p>Tienes derecho a acceder, rectificar y suprimir los datos, así como otros derechos, cuyo detalle se incluye en nuestra Política de Privacidad completa.</p>
    </div>
    <input type="submit" value="Enviar consulta" class="b_primary_inv"/>
}
