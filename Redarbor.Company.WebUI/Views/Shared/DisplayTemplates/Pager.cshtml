@model Redarbor.Company.WebUI.Models.PagerDataModel
@using Redarbor.Common.Entities.Enums
@using Redarbor.Company.WebUI.Helpers

@if (Model.TotalRows > Model.PageSizeSelected)
{
    var hiddenPageId = (string)ViewData["hiddenPageId"] ?? "Pager_PageSelected";
    var qname = (string)ViewData["qname"] ?? "p";

    <nav id="pager_@hiddenPageId" class="pag_numeric" data="@qname">
        @{
            int i = 1;
            int initPage = 0;
            int endPage = 0;

            short PageId = (int)PageEnum.OfferList;
            var portalConfig = (Redarbor.Common.Entities.Configuration.PortalConfig)ViewData["portalConfig"];
            var formIdToSubmit = (string)ViewData["formIdToSubmit"];
            var maxPages = (int?)ViewData["maxPages"] ?? 4;
            int totalPages = Convert.ToInt32(Math.Ceiling((double)Model.TotalRows / Model.PageSizeSelected));

            if (totalPages < maxPages)
            {
                initPage = 1;
                endPage = totalPages;
            }
            else
            {
                if (Model.PageSelected < maxPages)
                {
                    initPage = 1;
                    endPage = initPage + maxPages;

                    if (endPage > totalPages)
                    {
                        endPage = totalPages;
                    }
                }
                else
                {
                    if ((Model.PageSelected + (maxPages / 2)) < totalPages)
                    {
                        initPage = Model.PageSelected - (maxPages / 2);
                        endPage = Model.PageSelected + (maxPages / 2);

                        if (endPage > totalPages)
                        {
                            endPage = totalPages;
                        }
                    }
                    else
                    {
                        initPage = totalPages - maxPages;

                        if (initPage < 1)
                        {
                            initPage = Model.PageSelected - Convert.ToInt32(maxPages / 2);
                        }
                        endPage = totalPages;
                    }
                }
            }

            if (Model.PageSelected > 1)
            {
                <a id="@(Model.PageSelected - 1)" class="b_prev">
                    <span class="icon i_prev"></span>
                    <span class="hide show_m">@PageLiteralsHelper.GetLiteral("LIT_ANTERIOR", PageId, portalConfig)</span>
                </a>
            }

            for (i = initPage; i <= endPage; i++)
            {
                if (Model.PageSelected != i)
                {
                    <a title="@PageLiteralsHelper.GetLiteral("LIT_IRPAGINA", PageId, portalConfig)" id="@i">@i</a>
                }
                else
                {
                    <a id="@i" class="sel">@i</a>
                }
            }

            if (Model.PageSelected < totalPages)
            {
                <a id="@(Model.PageSelected + 1)" class="b_next">
                    <span class="hide show_m">@PageLiteralsHelper.GetLiteral("LIT_SIGUIENTE", PageId, portalConfig)</span>
                    <span class="icon i_next"></span>
                </a>
            }
        }
    </nav>

    <script type="text/javascript">
        $(document).ready(function () {
            $("#pager_@hiddenPageId a").click(function () {
                var id = $(this).attr("id");
                $("#@hiddenPageId").val(id);
                $("#@formIdToSubmit").submit();
            });
        });
       
    </script>
}