@model Redarbor.Company.WebUI.Models.Company.Shared.PopUpDataModel

@{
    var onClickAction = (string)ViewData["OnClick"];
    var idPopUp = (string)ViewData["IdPopUp"];
    var idBtnOk = (string)ViewData["IdBtnOk"];
    var idBtnKo = (string)ViewData["IdBtnKo"];
    var idInput = (string)ViewData["IdInput"];
    var idInputCheckBox = (string)ViewData["IdInputCheckBox"];
    var idCross = (string)ViewData["IdCross"];
    var idMessage = (string)ViewData["IdMessage"];
}

<div id="@idPopUp" style="display: none">
    <div class="blockUI blockOverlay" style="z-index: 9999; border: none; margin: 0px; padding: 0px; width: 100%; height: 100%; top: 0px; left: 0px; opacity: 0.6; position: fixed; background-color: rgb(0, 0, 0);"></div>
    <div class="blockUI blockMsg blockPage" style="z-index: 9999; position: fixed; padding: 0px; margin: 0px; width: 35%; top: 35%; left: 32.5%; text-align: center; color: rgb(0, 0, 0); border: 3px solid rgb(170, 170, 170); background-color: rgb(255, 255, 255);">
        <div style="cursor: default;" class="popup_gestof popup_gestcan content_popup">
            <div class="md-modal md-effect-3 md-show">
                <div class="md-content popUp_save popUp_remove w_100">
                    <div class="p30">
                        <button id="@idCross" onclick="$.unblockUI();"><span title="@Model.TitleBtnKo" class="icon ico_close"></span></button>

                        <span class="c_txt">@Model.Title</span>
                        @if (!string.IsNullOrEmpty(Model.Message))
                        {
                            <span id="@idMessage" class="txt">@Html.Raw(Model.Message)</span>
                        }
                        @if (!string.IsNullOrEmpty(Model.ExtraDynamicMessage))
                        {
                            <strong><span class="DynamicInfo"></span></strong>
                            <span class="txt">@Model.ExtraDynamicMessage</span>
                        }

                        @if (Model.ItemList != null)
                        {

                            <ul class="mt20 mb30 pos_rel w_70 mAuto">
                                @foreach (var item in Model.ItemList)
                                {
                                    <li class="dFlex vm_fx mb15">
                                        <img src="~/c/v2/img/i_ok.svg">
                                        <p class="ml15px">@item</p>
                                    </li>
                                }
                            </ul>
                        }

                        @if (Model.HasCheckBox)
                        {
                            <input class="m0 mt5" type="checkbox" value="1" id="@idInputCheckBox" />
                            <p>@Model.DescriptionCheckbox</p>
                        }
                        @if (Model.HasInput)
                        {
                            if (Model.HasTextArea)
                            {
                                <textarea type="text" cols="40" rows="5" class="fl w_100 mb15" MaxLength="499" id="@idInput"></textarea>
                            }
                            else
                            {
                                <input type="text" class="fl w_100 mb15" id="@idInput" placeholder="@Model.PlaceHolderInput" />
                            }
                        }

                        @if (Model.HasButtonOk)
                        {
                            <input class="submit_n js-processing" data-processing="@Model.LiteralProcessing" onclick="@onClickAction" type="button" id="@idBtnOk" value="@Model.TitleBtnOk" />
                        }

                        @if (Model.HasButtonOkNoJsProcessing)
                        {
                            <input class="submit_n blockInActionAjax" data-processing="@Model.LiteralProcessing" onclick="@onClickAction" type="button" id="@idBtnOk" value="@Model.TitleBtnOk" />
                        }
                        <div class="submitbtn mt0 pt0i">
                            @if (!string.IsNullOrEmpty(Model.TitleBtnKo))
                            {
                                <input id="@idBtnKo" value="@Model.TitleBtnKo" class="cancelar_link" type="button" onclick="$.unblockUI();">
                            }
                        </div>

                        <p style="color:red" id="@Model.LiteralError" name="descriptionError"></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>