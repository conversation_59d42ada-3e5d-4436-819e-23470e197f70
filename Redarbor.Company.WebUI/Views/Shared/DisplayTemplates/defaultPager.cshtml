@model Redarbor.Company.WebUI.Models.PagerDataModel
@using Redarbor.Common.Entities.Enums
@using Redarbor.Company.WebUI.Helpers

@if (Model.TotalRows > Model.PageSizeSelected)
{
    var hiddenPageId = (string)ViewData["hiddenPageId"] ?? "Pager_PageSelected";
    var qname = (string)ViewData["qname"] ?? "p";

    <div id="pager_@hiddenPageId" class="paginas" data="@qname" style="margin-bottom:30px;">
        <ul>
            @{
                int i = 1;
                int initPage = 0;
                int endPage = 0;

                short PageId = (int)PageEnum.OfferList;
                var portalConfig = (Redarbor.Common.Entities.Configuration.PortalConfig)ViewData["portalConfig"];
                var formIdToSubmit = (string)ViewData["formIdToSubmit"];
                var maxPages = (int?)ViewData["maxPages"] ?? 4;
                int totalPages = Model.PageCount;

                if (totalPages < maxPages)
                {
                    initPage = 1;
                    endPage = totalPages;
                }
                else
                {
                    if (Model.PageSelected < maxPages)
                    {
                        initPage = 1;
                        endPage = initPage + maxPages;

                        if (endPage > totalPages)
                        {
                            endPage = totalPages;
                        }
                    }
                    else
                    {
                        if ((Model.PageSelected + (maxPages / 2)) < totalPages)
                        {
                            initPage = Model.PageSelected - (maxPages / 2);
                            endPage = Model.PageSelected + (maxPages / 2);

                            if (endPage > totalPages)
                            {
                                endPage = totalPages;
                            }
                        }
                        else
                        {
                            initPage = totalPages - maxPages;

                            if (initPage < 1)
                            {
                                initPage = Model.PageSelected - Convert.ToInt32(maxPages / 2);
                            }
                            endPage = totalPages;
                        }
                    }
                }

                if (Model.PageSelected > 1)
                {
                    <li class="anterior">
                        <a id="@(Model.PageSelected - 1)">@PageLiteralsHelper.GetLiteral("LIT_ANTERIOR", PageId, portalConfig)</a>
                    </li>
                }

                for (i = initPage; i <= endPage; i++)
                {
                    if (Model.PageSelected != i)
                    {
                        <li class="anterior">
                            <a title="@PageLiteralsHelper.GetLiteral("LIT_IRPAGINA", PageId, portalConfig)" id="@i">@i</a>
                        </li>
                    }
                    else
                    {
                        <li class="active">
                            <a id="@i">@i</a>
                        </li>
                    }
                }

                if (Model.PageSelected < totalPages)
                {
                    <li class="siguiente">
                        <a id="@(Model.PageSelected + 1)">@PageLiteralsHelper.GetLiteral("LIT_SIGUIENTE", PageId, portalConfig)</a>
                    </li>
                }
            }
        </ul>
    </div>

    <script type="text/javascript">
        $(document).ready(function () {
            $("#pager_@hiddenPageId a").click(function () {
                var id = $(this).attr("id");
                $("#@hiddenPageId").val(id);
                $("#@formIdToSubmit").submit();
            });
        });
       
    </script>
}