@using Redarbor.Common.Entities.Enums;
@using Redarbor.Company.WebUI.Helpers;

@{

    Layout = "~/Views/Shared/Layouts/_LayoutSimple.cshtml";
    short pageId = (short)PageEnum.NavigationBlocked;
    var portalConfig = PortalConfigHelper.GetPortalConfiguration();
}

@section TitleSection{
    <title>@PageLiteralsHelper.GetLiteral("HEAD_TITLE_COMPUTRABAJO_UNUSUAL_ACTIVITY_TITLE_PAGE", pageId, portalConfig)</title>
}

<div class="slider_error">
    <div class="container">
        <h1>
            @Html.Raw(PageLiteralsHelper.GetLiteral("HEAD_TITLE_COMPUTRABAJO_UNUSUAL_ACTIVITY_PAGE", pageId, portalConfig))
        </h1>
        <p class="fr tl w_50">@Html.Raw(PageLiteralsHelper.GetLiteral("LIT_REASON_UNUSUAL_ACTIVITY", pageId, portalConfig))</p>
    </div>
</div>