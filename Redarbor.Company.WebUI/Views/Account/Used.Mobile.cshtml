@using Redarbor.Company.WebUI.Helpers
@using Redarbor.Common.Entities.Enums

@{
    Layout = null;

    short pageId = (short)PageEnum.AccountUsedCompany;
    var portalConfig = PortalConfigHelper.GetPortalConfiguration();
}


<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="robots" content="noindex, nofollow" />

    <link title="@PageLiteralsHelper.GetLiteral("LIT_COMPUTRABAJO", pageId, portalConfig)" rel="shortcut icon" type="image/x-icon" href="@Url.Content(string.Format("{0}img/favicon.ico{1}", portalConfig.PrefixPathImage, portalConfig.SufixPathImage))" />
    <title>@PageLiteralsHelper.GetLiteral("ACCOUNT_USED", pageId, portalConfig)</title>

    @Styles.Render(string.Format("~/bundles/css/pem_min{0}", portalConfig.staticVirtualBundle))
</head>

<body>
    <header class="header_main tc">
        <div class="menu_tools log">
            <div class="cols">
                <a href="@portalConfig.url_web" class="logo">
                    <img src="~/c/v2/img/logoct.svg" alt="Computrabajo" class="cep_logo" />
                </a>
            </div>
        </div>
    </header>
    <main>
        <div class="pAllB tc">
            <p class="fs20 fwB mbB"> @Html.Raw(PageLiteralsHelper.GetLiteral("LIT_NOT_ACCESS_DESCRIPTION2", pageId, portalConfig))</p>
            <p>
                @PageLiteralsHelper.GetLiteral("LIT_LATER_PART1", pageId, portalConfig)<a href="/contacto/">@PageLiteralsHelper.GetLiteral("LIT_LATER_PART2", pageId, portalConfig)</a><br>
                @Html.Raw(PageLiteralsHelper.GetLiteral("LIT_LATER2", pageId, portalConfig))
            </p><p><span class="fwB">@PageLiteralsHelper.GetLiteral("LIT_EMAIL", pageId, portalConfig)</span> @Html.Raw(PageLiteralsHelper.GetLiteral("LIT_EMAIL_a", pageId, portalConfig))</p>
            <p><span class="fwB">@PageLiteralsHelper.GetLiteral("LIT_PHONE", pageId, portalConfig)</span> @Html.Raw(PageLiteralsHelper.GetLiteral("LIT_PHONE_EXTENSION", pageId, portalConfig))</p>
            <p></p>
        </div>
    </main>
</body>
</html>