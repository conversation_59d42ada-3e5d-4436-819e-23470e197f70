@model Redarbor.Company.WebUI.Models.Company.Home.HomeMatchesDataModel
@using Redarbor.Company.WebUI.Helpers
@using Redarbor.Common.Entities.Enums
@using Redarbor.Master.Entities.Enums
@using Redarbor.Company.WebUI.Enums


@{
    short PageId = (int)PageEnum.MatchOffer;
    var portalConfig = PortalConfigHelper.GetPortalConfiguration();
    var companyCredentials = SecurityHelper.GetCompanyCredentials();
    if (portalConfig.AEPortalConfig.CTHROnBoardingEnabled && Model.Folders.Where(x => x.Position == (int)CompanyOfferFolderEnum.Contratados).Any())
    {
        var folderToMove = Model.Folders.Where(x => x.Position == (int)CompanyOfferFolderEnum.Contratados).First();
        Model.Folders.Remove(folderToMove);
        int index = Model.Folders.FindIndex(x => x.Position == (int)CompanyOfferFolderEnum.Descartados);
        Model.Folders.Insert(index, folderToMove);
    };
}


<section class="gescan_ep1">

    <div class="headTable hide_m">
        <a class="submit_n d_mbl" id="js-filtrar">Filtrar</a>
    </div>
    <div class="dFlex vm_fx w100 dB_r">
        <div class="gescan_subtit">
            <span class="fs18_m">
                @PageLiteralsHelper.GetLiteral("SUBTITLE", PageId, portalConfig) <strong> @(Model.OfferName.Length < 40 ? Model.OfferName : string.Format("{0}...", Model.OfferName.Substring(0, 40))) </strong>
            </span>
            <div class="hide_m d_inlineblock">@Model.TotalCandidates</div>
        </div>
    </div>
    <div class="mb20 fl100 hide_m">
        <section class="cm-9 parrilla_oferta w_100 m0">
            <div class="nav-tab">
                @if (Model.HasFolders)
                {
                    <ul class="tabs">
                        @foreach (var folder in Model.Folders)
                        {
                            <li id="@folder.PositionEncrypted" class="@(Model.FolderSelected == folder.Position ? "active" : "") @(folder.Position == (int)CompanyCustomFoldersMatchEnum.Folder1 || folder.Position == (int)CompanyCustomFoldersMatchEnum.Folder2 ? "custom_folder" : "")">

                                @if (folder.Position == (int)CompanyOfferFolderEnum.BBDD)
                                {
                                    <a id="@folder.AnchorName" class="fl" href="@Url.Action("Index", "CompanyMatches", new { oi = Model.OfferIdEncrypted, cf = folder.PositionEncrypted })">
                                        @folder.NameFolder
                                    </a>
                                }
                                else if (folder.Count > 0)
                                {
                                    <a id="@folder.AnchorName" class="fl" href="@Url.Action("Index", "CompanyMatches", new { oi = Model.OfferIdEncrypted, cf = folder.PositionEncrypted })">
                                        @string.Format("{0}({1})", folder.NameFolder, StringToolsHelper.ChangeGroupPointForDecimalPoint(folder.Count.ToString("N0"), portalConfig.PortalId))
                                    </a>
                                }
                                else
                                {
                                    <span class="@(folder.Position == (int)CompanyCustomFoldersMatchEnum.Folder1 || folder.Position == (int)CompanyCustomFoldersMatchEnum.Folder2 ? "w_auto nav-tab-lbl fl" : "nav-tab-lbl")">@folder.NameFolder (0)</span>
                                }

                                @if (Model.HasCustomFolders && (folder.Position == (int)CompanyCustomFoldersMatchEnum.Folder1 || folder.Position == (int)CompanyCustomFoldersMatchEnum.Folder2))
                                {
                                    <div id="<EMAIL>" class="arrow_select">
                                        <div class="tab_act">
                                            <a id="<EMAIL>" class="edit_folder fl" data-id-folder="@folder.IdEncrypted" href="#">
                                                @PageLiteralsHelper.GetLiteral("LIT_EDIT_FOLDER", PageId, portalConfig)
                                            </a>

                                            @if (folder.Count == 0)
                                            {
                                                <a id="<EMAIL>" class="delete_folder fl" data-id-folder="@folder.IdEncrypted" data-fol="@folder.Position" href="#">
                                                    @PageLiteralsHelper.GetLiteral("LIT_DELETE_FOLDER", PageId, portalConfig)
                                                </a>
                                            }
                                        </div>
                                    </div>
                                }
                            </li>
                         }
                        @if (Model.HasCustomFolders && (!Model.Folders.Any(n => n.Position == (int)CompanyCustomFoldersMatchEnum.Folder1) || !Model.Folders.Any(n => n.Position == (int)CompanyCustomFoldersMatchEnum.Folder2)))
                        {
                            <li id="li_add" class="add_folder">
                                <a id="addfolder" href="#">+</a>
                            </li>
                        }
                    </ul>
                }
            </div>

            @if (Model.MultifiltersDataModel.IsAOfferPack && Model.MultifiltersDataModel.IsFolderBBDD)
            {
                <section class="box_filtro_der bg_blanco">
                    <div class="order_cand cm-9">
                        <p>@PageLiteralsHelper.GetLiteral("LIT_BUSCADOR_HDV", PageId, portalConfig)</p>
                    </div>
                    @if (Model.IsNuggetCv && Model.IsNewFiltersActive)
                    {
                        <div class="order_cand cm-9">
                            @Html.DropDownListFor(d => d.MultifiltersDataModel.Order, Model.MultifiltersDataModel.OrderDropDownList, new { Id = "OrderSelected" })
                        </div>
                    }
                    <div class="acciones cm-6">
                        <p class="hdv_disponibles">
                            @(string.Format("{0}{1}", Model.NumberCvsAvailable, PageLiteralsHelper.GetLiteral("LIT_HDV_DISP", PageId, portalConfig)))
                        </p>
                        <a class="hdv_seleccionadas @(Model.MultifiltersDataModel.IsMySelection ? "activo" : string.Empty)" href="@Url.Action("Index", "CompanyMatches", new { ms = "true", oi = Model.OfferIdEncrypted, cf = Model.FolderBBDDEncrypted })">
                            @PageLiteralsHelper.GetLiteral("LIT_SELECCIONES_HDV", PageId, portalConfig)
                            (@Model.NumberCvsInMySelections)<span title="@PageLiteralsHelper.GetLiteral("LIT_SELECCIONES_HDV", PageId, portalConfig)" class="icon hdvselec"></span>
                        </a>
                    </div>
                </section>
            }
            else
            {
                <section class="box_filtro_der bg_blanco btn_filt_auto hide_m">
                    <div class="order_cand cm-9">
                        <p>@PageLiteralsHelper.GetLiteral("ACTION", PageId, portalConfig)</p>
                        @Html.DropDownListFor(m => m.MatchActionsSelected, Model.MatchActions, new { @onclick = "ga('send', 'event', 'Company Offer Matches Membership', 'Click Realizar Accion', 'Click Realizar Accion')" })

                        @if (Model.HasFolders)
                        {
                            <p>@PageLiteralsHelper.GetLiteral("LIT_MOVERA", PageId, portalConfig)</p>
                            @Html.DropDownListFor(m => m.MatchFoldersSelected, Model.MatchFolders, new { @onclick = "ga('send', 'event', 'Company Offer Matches Membership', 'Click Mover Carpeta', 'Click Mover Carpeta')" })
                        }

                    </div>
                    <div class="acciones cm-3">
                        <a href="#" id="it-print" class="acc_ofertas" onclick="ga('send', 'event', 'Company Offer Matches Membership', 'Click Imprimir CV', 'Click Imprimir CV')"><span title="@PageLiteralsHelper.GetLiteral("LIT_PRINT", PageId, portalConfig)" class="icon print">&nbsp</span></a>
                        <a href="#" id="it-deleteV2" class="acc_ofertas" onclick="ga('send', 'event', 'Company Offer Matches Membership', 'Click Borrar CV', 'Click Borrar CV')"><span title="@PageLiteralsHelper.GetLiteral("LIT_ELIMINAR", PageId, portalConfig)" class="icon papelera">&nbsp</span></a>
                        @if ((Model.HasCvsExtractions && Model.FolderSelected != (int)CompanyOfferFolderEnum.Recibidos) ||
                            (Model.HasCVDownloadMatchesReceived && Model.FolderSelected == (int)CompanyOfferFolderEnum.Recibidos))
                        {
                            <a href="#" id="itextract" class="acc_ofertas"><span title="@PageLiteralsHelper.GetLiteral("LIT_EXPORT", PageId, portalConfig)" class="icon extraccion">&nbsp</span></a>
                        }
                        @if (Model.HasCandidateComparator)
                        {
                            <a href="#" id="idCandidateComparator" class="acc_ofertas" onclick="ga('send', 'event', 'Company Offer Matches Membership', 'Click Candidate Comparator', 'Click Candidate Comparator')"><span title="@PageLiteralsHelper.GetLiteral("LIT_COMPARATIVE_TITLE", PageId, portalConfig)" class="icon comparar">&nbsp</span></a>
                        }
                        @if (Model.HasChatMatches)
                        {
                            <a id="createChatUni" class="acc_ofertas"><span class="icon iX">&nbsp</span></a>
                        }
                        @if (Model.MultifiltersDataModel.HasExclusion)
                        {
                            <div class="btn pt0i pb0i">
                                <a id="automaticFilters" class="submit_wh2 fw_n plr15 ptb10 fs14i" href="@Url.Action("Index", "CompanyMatchesExclusion", new { oi = Model.OfferIdEncrypted })">@PageLiteralsHelper.GetLiteral("LIT_FILTERS_EXCLUSION", PageId, portalConfig)</a>
                            </div>
                        }
                    </div>
                </section>
            }
        </section>
    </div>

    @if (Model.HasFolders)
    {
        <div class="d_mbli mt20">
            <div class="folders_links">
                @foreach (var folder in Model.Folders)
                {
                    if (folder.Position == (int)CompanyOfferFolderEnum.BBDD)
                    {
                        <a id="@folder.AnchorName" class="field_select_links @(Model.FolderSelected == folder.Position ? "sel" : "")" href="@Url.Action("Index", "CompanyMatches", new { oi = Model.OfferIdEncrypted, cf = folder.PositionEncrypted })">
                            <p>@folder.NameFolder</p>
                        </a>
                    }
                    else if (folder.Count > 0)
                    {
                        <a id="@folder.AnchorName" class="field_select_links @(Model.FolderSelected == folder.Position ? "sel" : "")" href="@Url.Action("Index", "CompanyMatches", new { oi = Model.OfferIdEncrypted, cf = folder.PositionEncrypted })">
                            <p>@string.Format("{0}({1})", folder.NameFolder, StringToolsHelper.ChangeGroupPointForDecimalPoint(folder.Count.ToString("N0"), portalConfig.PortalId))</p>
                        </a>
                    }
                    else
                    {
                        <a class="@(folder.Position == (int)CompanyCustomFoldersMatchEnum.Folder1 || folder.Position == (int)CompanyCustomFoldersMatchEnum.Folder2 ? "field_select_links" : "field_select_links")">
                            <p>@folder.NameFolder (0)</p>
                        </a>
                    }

                    if (Model.HasCustomFolders && (folder.Position == (int)CompanyCustomFoldersMatchEnum.Folder1 || folder.Position == (int)CompanyCustomFoldersMatchEnum.Folder2))
                    {
                        <div id="<EMAIL>" class="arrow_select">
                            <div class="tab_act">
                                <a id="<EMAIL>" class="edit_folder field_select_links @(Model.FolderSelected == folder.Position ? "sel" : "")" data-id-folder="@folder.IdEncrypted" href="#">
                                    @PageLiteralsHelper.GetLiteral("LIT_EDIT_FOLDER", PageId, portalConfig)
                                </a>

                                @if (folder.Count == 0)
                                {
                                    <a id="<EMAIL>" class="delete_folder field_select_links @(Model.FolderSelected == folder.Position ? "sel" : "")" data-id-folder="@folder.IdEncrypted" data-fol="@folder.Position" href="#">
                                        @PageLiteralsHelper.GetLiteral("LIT_DELETE_FOLDER", PageId, portalConfig)
                                    </a>
                                }
                            </div>
                        </div>
                    }
                }
            </div>
        </div>
    }

    <div class="cm-3 facet facetMbl">
        <div class="box_filtrar">
            <h2 class="box_filtro a_claro">@PageLiteralsHelper.GetLiteral("FILTERS", PageId, portalConfig)</h2>
            @if (Model.TotalMatchesByFolder == 0 && !Model.MultifiltersDataModel.IsFolderBBDD)
            {
                <span class="tc mostrar ptb15">@PageLiteralsHelper.GetLiteral("LIT_NO_APLICAR_FILTRO", PageId, portalConfig)</span>
            }
            @if (Model.HasSaveFilters)
            {
                <div class="box_filtrar_sel saveFilter">
                    <div class="box_filtrar_t2">
                        <span class="icon ico_list"></span>
                        <h3>@PageLiteralsHelper.GetLiteral("LIT_YOUR_SAVED_FILTERS", PageId, portalConfig)</h3>
                    </div>
                    @if (Model.FiltersSaved.Any())
                    {
                        <ul id="listFilterSave" class="listFilterSave">
                            @foreach (var filter in Model.FiltersSaved)
                            {
                                <li class="@(filter.Value == Model.MultifiltersDataModel.IdSaveFilterSelected ? "filterSelect" : string.Empty)">
                                    <a id="@(string.Format("filterSave_{0}", filter.Value))" href="@Url.Action("Index", "CompanyMatches", new { oi = Model.OfferIdEncrypted, cf = Model.FolderSelectedEncrypted, ft = filter.Value })">
                                        @filter.Text
                                    </a>

                                    @if (filter.Value == Model.MultifiltersDataModel.IdSaveFilterSelected)
                                    {
                                        <a href="@Url.Action("Index", "CompanyMatches", new { oi = Model.OfferIdEncrypted, cf = Model.FolderSelectedEncrypted, undoFilter = true })"><span id="remove_saved_filter" class="icon ico_close_w"></span></a>
                                    }

                                    <span class="icon remove delfilter ocultar" onclick="deleteFilter('@filter.Value', '@filter.Text')" />

                                </li>
                                <li class="ocultar"></li>
                                <li id="second_li" class="lineF">
                                    <hr />
                                </li>
                            }
                        </ul>
                        <a id="btt_gest_filters" class="deshacer" href="#">@PageLiteralsHelper.GetLiteral("LIT_GEST_SAVED_FILTERS", PageId, portalConfig)</a>
                        <a id="btt_return_filters" class="deshacer ocultar" href="#">@PageLiteralsHelper.GetLiteral("LIT_VOLVER", PageId, portalConfig)</a>
                    }
                    else
                    {
                        <ul class="listFilterSave">
                            <li class="listFilters">@PageLiteralsHelper.GetLiteral("LIT_WITHOUT_SAVED_FILTERS", PageId, portalConfig)</li>
                        </ul>
                    }
                </div>
            }
            @Html.EditorFor(m => m.MultifiltersDataModel, "MatchesFilters", new { Model.IsNuggetCv, Model.IsNewFiltersActive, Model.IsMemberShip, Model.OfferIdCompanyProduct, Model.IsForeign })
        </div>
    </div>


    @if (Model.MultifiltersDataModel.SeeFilterExclusionMessage != string.Empty)
    {
        if (EncryptationHelper.Decrypt(Model.MultifiltersDataModel.SeeFilterExclusionMessage) == ((int)ResultEnum.OK).ToString())
        {
            <div class="cm-9 pr0i">
                <div class="box_green boxautohide mtb15 p15 fs_15">
                    @PageLiteralsHelper.GetLiteral("LIT_GREEN_MESSAGE", PageId, portalConfig) <strong>@Model.OfferName</strong>
                </div>
            </div>
        }
        if (EncryptationHelper.Decrypt(Model.MultifiltersDataModel.SeeFilterExclusionMessage) == ((int)ResultEnum.KO).ToString())
        {
            <div class="cm-9 pr0i">
                <div class="box_red boxautohide mtb15 p15 fs_15">
                    @PageLiteralsHelper.GetLiteral("LIT_RED_MESSAGE", PageId, portalConfig) <strong>@Model.OfferName</strong>
                </div>

            </div>
        }
    }

    <div class="cm-9">
        @if (Model.MultifiltersDataModel.IsFiltered && portalConfig.ShowMultifiltersTags)
        {
            @Html.EditorFor(m => m.MultifiltersDataModel, "CvsMultifilterTags")
        }
    </div>

    @if (!Model.MultifiltersDataModel.IsFolderBBDD)
    {
        <section id="sectFilter" class="cm-9 mb0 lista tabla_gestion_candidatos listacheckbox @(Model.ShowPunctuationColumn ? "kq" : ""  ) hide_m">
            <div class="head_row">
                <ul>
                    <li class="nombre">
                        <input type="checkbox" id="chk_select_all" />
                        <p>@PageLiteralsHelper.GetLiteral("COLUMN_CANDIDATE_NAME", PageId, portalConfig)</p>
                        <div class="ic_ordenar">
                            <a href="@Url.Action("Index", "CompanyMatches", new { oi = Model.OfferIdEncrypted, cf = Model.FolderSelectedEncrypted, by = "name", o = "a" })"><span class="icord_sup"></span></a>
                            <a href="@Url.Action("Index", "CompanyMatches", new { oi = Model.OfferIdEncrypted, cf = Model.FolderSelectedEncrypted, by = "name", o = "d" })"><span class="icord_inf"></span></a>
                        </div>
                    </li>
                    <li class="aplicado">
                        <p>@PageLiteralsHelper.GetLiteral("LIT_HA_APLICADO", PageId, portalConfig)</p>
                        <div class="ic_ordenar">
                            <a href="@Url.Action("Index", "CompanyMatches", new { oi = Model.OfferIdEncrypted, cf = Model.FolderSelectedEncrypted, by = "appliedOn", o = "a" })"><span class="icord_sup"></span></a>
                            <a href="@Url.Action("Index", "CompanyMatches", new { oi = Model.OfferIdEncrypted, cf = Model.FolderSelectedEncrypted, by = "appliedOn", o = "d" })"><span class="icord_inf"></span></a>
                        </div>
                    </li>
                    <li class="edad">
                        <p>@PageLiteralsHelper.GetLiteral("COLUMN_AGE", PageId, portalConfig)</p>
                        <div class="ic_ordenar">
                            <a href="@Url.Action("Index", "CompanyMatches", new { oi = Model.OfferIdEncrypted, cf = Model.FolderSelectedEncrypted, by = "birthDate", o = "d" })"><span class="icord_sup"></span></a>
                            <a href="@Url.Action("Index", "CompanyMatches", new { oi = Model.OfferIdEncrypted, cf = Model.FolderSelectedEncrypted, by = "birthDate", o = "a" })"><span class="icord_inf"></span></a>
                        </div>
                    </li>
                    <li class="estudios">
                        <p>@PageLiteralsHelper.GetLiteral("COLUMN_STUDYLEVEL", PageId, portalConfig)</p>
                        <div class="ic_ordenar">
                            <a href="@Url.Action("Index", "CompanyMatches", new { oi = Model.OfferIdEncrypted, cf = Model.FolderSelectedEncrypted, by = "educationLevelId", o = "a" })"><span class="icord_sup"></span></a>
                            <a href="@Url.Action("Index", "CompanyMatches", new { oi = Model.OfferIdEncrypted, cf = Model.FolderSelectedEncrypted, by = "educationLevelId", o = "d" })"><span class="icord_inf"></span></a>
                        </div>
                    </li>

                    @if (Model.ShowPunctuationColumn)
                    {
                        <li class="puntuacion">
                            <p>@PageLiteralsHelper.GetLiteral("COLUMN_PUNTUACION", PageId, portalConfig)</p>
                            <div class="ic_ordenar">
                                <a href="@Url.Action("Index", "CompanyMatches", new { oi = Model.OfferIdEncrypted, cf = Model.FolderSelectedEncrypted, by = "kQScoreAvg", o = "a" })"><span class="icord_sup"></span></a>
                                <a href="@Url.Action("Index", "CompanyMatches", new { oi = Model.OfferIdEncrypted, cf = Model.FolderSelectedEncrypted, by = "kQScoreAvg", o = "d" })"><span class="icord_inf"></span></a>

                            </div>
                        </li>
                    }

                    @if (Model.HasAdequacy)
                    {
                        <li class="adecuacion">
                            <p>@PageLiteralsHelper.GetLiteral("COLUMN_ADECUACION", PageId, portalConfig)</p>
                            <div class="ic_ordenar">

                                <a href="@Url.Action("Index", "CompanyMatches", new { oi = Model.OfferIdEncrypted, cf = Model.FolderSelectedEncrypted, by = "adequacy_points", o = "a" })"><span class="icord_sup"></span></a>
                                <a href="@Url.Action("Index", "CompanyMatches", new { oi = Model.OfferIdEncrypted, cf = Model.FolderSelectedEncrypted, by = "adequacy_points", o = "d" })"><span class="icord_inf"></span></a>

                            </div>
                        </li>
                    }
                </ul>
            </div>
        </section>
    }

</section>