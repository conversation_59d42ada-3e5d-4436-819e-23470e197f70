@using Redarbor.Company.WebUI.Helpers;
@using Redarbor.Common.Entities.Enums;
@model Redarbor.Company.WebUI.Models.DeviceValidationDataModel

@{

    Layout = "~/Views/Shared/Layouts/_LayoutDeviceVerification.cshtml";
    short pageId = (short)PageEnum.DeviceValidation;
    var portalConfig = PortalConfigHelper.GetPortalConfiguration();
}

@section TitleSection{
    <title>@PageLiteralsHelper.GetLiteral("TITLE_SECTION", (short)PageEnum.HomeMasterCompany, portalConfig)</title>
}

@using (Html.BeginForm("ValidateVerificationCode", "DeviceAuthentication", FormMethod.Post, new { id = "validateDevAuth" }))
{
    @Html.AntiForgeryToken()

    <header>
        <a class="login" href="http://www.computrabajo.com.mx">
            <h1>@PageLiteralsHelper.GetLiteral("LIT_LOGO", (short)PageEnum.HomeMasterCompany, portalConfig)</h1>

            <img src="@Url.Content(string.Format("{0}img/logo_{1}.svg{2}", portalConfig.PrefixPathImage, portalConfig.countrycode.ToLower(), portalConfig.SufixPathImage))" alt="@PageLiteralsHelper.GetLiteral("ALT_IMG_LOGO_PORTAL", (short)PageEnum.HomeMasterCompany, portalConfig)" />
        </a>
    </header>

    <!-- BEGIN: Caja de verificación código -->
    <div class="container">
        <section class="ventana_login">
            <div class="box">
                <header>
                    <h1>@PageLiteralsHelper.GetLiteral("SECUTITY_VERIFICATION", pageId, portalConfig)</h1>
                </header>
                <div class="pt15 plr15">
                    <p>@PageLiteralsHelper.GetLiteral("SECUTITY_VERIFICATION_DESCRIPTION", pageId, portalConfig)</p>
                    
                    @if (Model != null && Model.ErrorInValidationCode)
                    {
                <div class="box_error mt10">@PageLiteralsHelper.GetLiteral("WRONG_CODE", pageId, portalConfig)</div>
                    }

                <div id="sendOkDeviceVerificationEmailMessage" class="box_ok mt10" style="display: none;">@PageLiteralsHelper.GetLiteral("MAIL_SEND_OK", pageId, portalConfig)</div>
                <div id="sendErrorDeviceVerificationEmailMessage" class="box_ok mt10" style="display: none;">@PageLiteralsHelper.GetLiteral("MAIL_SEND_ERROR", pageId, portalConfig)</div>
                </div>
                <div class="table_datos">
                    <ul id="erremail" class="">
                        <li class="email_psw">@PageLiteralsHelper.GetLiteral("CODE", pageId, portalConfig)</li>
                        <li class="campo">
                            @Html.TextBoxFor(m => m.ValidationCode, new { @Placeholder = "", @class = "cm-3" })
                            @Html.ValidationMessageFor(m => m.ValidationCode)
                        </li>
                    </ul>

                </div>
                <div id="sendDeviceVerificationEmail" class="conectado pr15px">
                    <p>@PageLiteralsHelper.GetLiteral("MAIL_NOT_RECIVED", pageId, portalConfig) <a class="fn tl">@PageLiteralsHelper.GetLiteral("RESEND_CODE", pageId, portalConfig)</a></p>
                </div>
                <div class="conectado">
                    <input type="submit" name="" value="@PageLiteralsHelper.GetLiteral("ACCEPT", pageId, portalConfig)" id="" class="pd2 submit_n">
                </div>
            </div>
        </section>
    </div>
}

@section CustomScriptsFooterSection{
    <script type="text/javascript">

        document.addEventListener("DOMContentLoaded", function () {
            $('#sendDeviceVerificationEmail').click(function (e) {

                      $.ajax({
                                url: '@Url.Action("SendValidationCodeMail", "DeviceAuthentication")',
                                cache: false,
                                data: { },
                                type: "POST",
                                success: function (respuesta) {
                                    if (respuesta.toLowerCase() === "true") {
                                        $("#sendErrorDeviceVerificationEmailMessage").hide()
                                        $("#sendOkDeviceVerificationEmailMessage").show()
                                    }
                                    else {
                                        $("#sendOkDeviceVerificationEmailMessage").hide()
                                        $("#sendErrorDeviceVerificationEmailMessage").show()
                                    }
                                }
                      });
                });
        });


    </script>
}