@model Redarbor.Company.WebUI.Models.Company.Cart.CompanyCartPaymentFormDataModel

<html>
<body onload='document.forms[0].submit()'>
    <form action='@Model.PayUUrl' method='post' target='_self'>
        <input type='hidden' value='@Model.PayUMerchandtId' name='merchantId' />
        <input type='hidden' value='@Model.PayUApiKey' name='ApiKey' />
        <input type='hidden' value='@Model.ReferenceCode' name='referenceCode' />
        <input type='hidden' value='@Model.AccountId' name='accountId' />
        <input type='hidden' value='@Model.Desciption' name='description' />
        <input type='hidden' value='@Model.Amount' name='amount' />
        <input type='hidden' value='@Model.Tax' name='tax' />
        <input type='hidden' value='@Model.TaxReturnBase' name='taxReturnBase' />
        <input type='hidden' value='@Model.Currency' name='currency' />
        <input type='hidden' value='@Model.Signature' name='signature' />
        <input type='hidden' value='@Model.Test' name='test' />
        <input type='hidden' value='@Model.BuyerEmail' name='buyerEmail' />
        <input type='hidden' value='@Model.RefVenta' name='refVenta' />
        <input type='hidden' value='@Model.ResponseUrl' name='responseUrl' />
        <input type='hidden' value='@Model.ConfirmationUrl' name='confirmationUrl' />
        @if (@Model.ShowNitWithValueInPayU)
        {
            <input type='hidden' value='@Model.PayerDocument' name='payerDocument' />
        }
    </form>
</body>
</html>
