@model Redarbor.Company.WebUI.Models.Company.Offer.OfferAIViewModel
@using Redarbor.Company.WebUI.Helpers.Html
@using Redarbor.Company.WebUI.Helpers
@using Redarbor.Common.Entities.Enums
@using System.Web.Optimization

@{
    Layout = "~/Views/Shared/Layouts/_LayoutMasterNewPem.cshtml";
    var pageId = Html.SetCurrentPage((int)PageEnum.OfferAI);
    var portalConfig = PortalConfigHelper.GetPortalConfiguration();
}

@section TitleSection{
    <title>@PageLiteralsHelper.GetLiteral("LIT_AI_TITLE_SECTION", pageId, portalConfig)</title>
}

@section CustomScriptsSection{
    @Scripts.Render("~/bundles/js/company/offer/offerai")
}


@section CustomScriptsFooterSection{
    <script>
        InitializeOfferAIPublish({
            generateOfferAIUrl: '@Url.Action("GenerateOfferAI", "CompanyOfferAI")',
            isnewpem: '1',
        });
    </script>
}

@section CustomStylesSection{
    @Styles.Render(string.Format("~/bundles/css/chosen{0}", portalConfig.staticVirtualBundle))
}


<div class="box pt25 pb25 js_fix">
    <div class="container">
        <div class="cols">
            <h1>@PageLiteralsHelper.GetLiteral("LIT_AI_TITLE_SECTION_H1", pageId, portalConfig) </h1>
            <div class="tr">
                <a href="@Url.Action("Selection", "CompanyOfferAI")" class="fc_base"><span class="icon i_prev_small mr10"></span>@PageLiteralsHelper.GetLiteral("LIT_AI_GO_BACK", pageId, portalConfig)</a>
            </div>
        </div>
    </div>
</div>

<main>
    <div class="small mbB box_error js_autohide" id="serverResponse" style="display:none;">@PageLiteralsHelper.GetLiteral("AI_OFFER_ERROR_MESSAGE", pageId, portalConfig)</div>
    <section class="box pt0_m">
        <h2 class="mbB">@PageLiteralsHelper.GetLiteral("LIT_AI_MAIN_HEADER_MESSAGE", pageId, portalConfig)</h2>
        <form id="formOfferAIPublish" class="mt25">
            <div class="field_input hor">
                <label>@PageLiteralsHelper.GetLiteral("LIT_FORM_AI_TITLE_TITLE", pageId, portalConfig)<span class="required">*</span></label>

                <div class="fx_wrap w45 dB_m">
                    <div class="w100">
                        <div class="field_input mb0 mbB_m posRel">
                            @Html.AutocompleteFor(m => m.Profession, "AutoCompleteOfferPosition", "CompanyOfferAI", new
                            {
                                @class = "",
                                placeholder = @PageLiteralsHelper.GetLiteral("LIT_FORM_AI_CARGO_PLACEHOLDER", pageId, portalConfig),
                                autocomplete = "disabled"
                            })
                        </div>
                        @Html.ValidationMessageFor(m => m.Profession, "")
                    </div>
                </div>
            </div>

            @*<div class="field_select hor">
            <label>@PageLiteralsHelper.GetLiteral("LIT_FORM_AI_TITLE_WORKTYPE", pageId, portalConfig)<span class="required">*</span></label>
            <div class="w45">
                <div>
                    @Html.DropDownListFor(m => m.IdWorkPlaceType, Model.WorkPlaceType, @PageLiteralsHelper.GetLiteral("LIT_FORM_AI_SELECCIONE", pageId, portalConfig))
                </div>
                @Html.Hidden("loadSkills", Model.SkillsSelectedHidden)
                @Html.ValidationMessageFor(m => m.IdWorkPlaceType, "")
            </div>
        </div>*@

            <div class="field_radio_box hor">
                <label>@PageLiteralsHelper.GetLiteral("LIT_FORM_AI_TITLE_WORKTYPE", pageId, portalConfig)<span class="required">*</span></label>
                <div class="group w45">
                    <div>
                        @foreach (var item in Model.WorkPlaceType)
                        {

                            <label class="radio mb0_m">
                                <input type="radio" name="IdWorkPlaceType" value="@item.Value" @(Model.IdWorkPlaceType.ToString() == item.Value ? "checked" : "") />
                                <span class="input"></span>
                                <span class="label_box">@item.Text</span>
                            </label>

                        }
                        @Html.HiddenFor(m => m.IdWorkPlaceType)
                        @Html.ValidationMessageFor(m => m.IdWorkPlaceType, "")
                    </div>
                    @Html.Hidden("loadSkills", Model.SkillsSelectedHidden)
                </div>
            </div>

            <div class="field_select hor">
                <label>@PageLiteralsHelper.GetLiteral("LIT_FORM_AI_TITLE_SKILLS", pageId, portalConfig)</label>

                <div class="w45">
                    <div>
                        @Html.DropDownListFor(m => m.SelectedOfficeSkills, Model.Skills)
                        @Html.Hidden("loadSkills", Model.SkillsSelectedHidden)
                    </div>
                </div>
            </div>

            <div id="uiIDskills" class="field_tag hor no_wrap">
                <input type="hidden" clientidmode="static" name="atsk" id="atsk" />
                <input type="hidden" clientidmode="static" name="avsk" id="avsk" />
            </div>

            <div class="field_radio_box hor">
                <label>@PageLiteralsHelper.GetLiteral("LIT_FORM_AI_TITLE_HIDE_COMPANY_NAME", pageId, portalConfig)<span class="required">*</span></label>
                <div class="group w45">
                    <div>
                        <label class="radio mb0_m">
                            @Html.RadioButtonFor(m => m.ShowCompanyName, true)
                            <span class="input"></span>
                            <span class="label_box">@PageLiteralsHelper.GetLiteral("LIT_FORM_AI_YES", pageId, portalConfig)</span>
                        </label>
                        <label class="radio mb0_m">
                            @Html.RadioButtonFor(m => m.ShowCompanyName, false)
                            <span class="input"></span>
                            <span class="label_box">@PageLiteralsHelper.GetLiteral("LIT_FORM_AI_NO", pageId, portalConfig)</span>
                        </label>
                    </div>

                    @Html.ValidationMessageFor(m => m.ShowCompanyName, "")


                </div>
            </div>

            <footer class="form_action">
                <button type="button" id="btnGenerateOfferAI" class="b_primary">@PageLiteralsHelper.GetLiteral("LIT_FORM_BUTTON_NEXT", pageId, portalConfig)</button>
            </footer>
        </form>
    </section>

    <div id="generatingAIPopUp" class="popup flex js_box_ia_working hide">
        <div class="w40">
            <div class="tc">
                <h2 class="mb5 fwB">@PageLiteralsHelper.GetLiteral("LIT_POPUP_H2", pageId, portalConfig)</h2>
                <p class="fs16 mbB">@PageLiteralsHelper.GetLiteral("LIT_POPUP_SUB_P", pageId, portalConfig)</p>
                @{Html.RenderPartial("Partials/Boxes/_OfferAIGenerationBox", new ViewDataDictionary { { "Classes", "animate mAuto mbB svg" }, { "Spin", true } }); }
                <p class="mb15 fc_aux">@PageLiteralsHelper.GetLiteral("LIT_POPUP_MAIN_P", pageId, portalConfig)</p>

                <footer>
                    <div>
                        <a href="#" class="b_transparent js_hide_ia_working" onclick="cancelGenerate(true); ClosePopUp();"><span>@PageLiteralsHelper.GetLiteral("LIT_CANCEL_CRATION", pageId, portalConfig)</span></a>
                    </div>
                </footer>
            </div>
            <a class="icon i_close icon_tooltip js_hide_ia_working" onclick="cancelGenerate(true); ClosePopUp();"><span>@PageLiteralsHelper.GetLiteral("LIT_CLOSE", pageId, portalConfig)</span></a>
        </div>
    </div>
</main>