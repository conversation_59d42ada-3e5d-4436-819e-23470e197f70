@model Redarbor.Company.WebUI.Models.Company.Home.HomeDataModel
@using Redarbor.Company.WebUI.Helpers
@using Redarbor.Common.Entities.Enums
@using Redarbor.Company.WebUI.Enums
@using Redarbor.Core.Kpi.Contracts.ServiceLibrary.Enums

@{
    Layout = "~/Views/Shared/Layouts/_LayoutMasterNewPem.cshtml";
    short pageId = (int)PageEnum.HomePrivada;
    var portalConfig = PortalConfigHelper.GetPortalConfiguration();
    var companyCredentials = SecurityHelper.GetCompanyCredentials();
    var productExpiredRenewUrl = portalConfig.AEPortalConfig.IsNewSupportWithSF && !string.IsNullOrEmpty(portalConfig.ProductExpiredRenewUrl) ? CompanyHelper.GetProductExpiredRenewUrl(portalConfig, companyCredentials) : string.Empty;
}

@section TitleSection{
    <title>@PageLiteralsHelper.GetLiteral("HEAD_COMPANY", pageId, portalConfig)</title>
}

@section CustomStylesSection{
    @Styles.Render(string.Format("~/bundles/css/vision{0}", portalConfig.staticVirtualBundle))
}

@section PopupSection{
    @if (Model.AlertDataModel != null)
    {
        Html.RenderPartial("_AlertNewPem", Model.AlertDataModel, new ViewDataDictionary { { "portalConfig", portalConfig }, { "companyCredentials", companyCredentials } });
    }
    @if (Model.ErrorAlertsDataModel.IsVisible)
    {
        @Html.Partial("PopUps/_ErrorPopUpAlerts", Model.ErrorAlertsDataModel);
    }
}

<html>
<body>
    @if (Model.ShowRenewLink)
    {
        Html.RenderPartial("CompanyPage/_RenewMembership", Model, new ViewDataDictionary { { "daysToExpire", Model.DaysToExpire }, { "productExpiredRenewUrl", productExpiredRenewUrl }, { "portalConfig", portalConfig }, { "companyCredentials", companyCredentials } });
    }
    <main>


        @if (!Model.HasCompanyDescription && Model.IsEditConfigPemitedByRoleAndIsNotAdvisorRole && Model.JustLoggedIn)
        {
            @Html.Partial("Home/_CompanyDescriptionPopup", portalConfig);
        }

        @if (Model.ShowRememberAuthorizationMail)
        {
            if (Model.CanSendRememberAuthorizationMail)
            {
                <div class="box_info mbB msg_send_user_inactive">
                    Su correo no ha sido validado, solo es un paso más. Active su cuenta con el código que le enviaremos por correo electrónico.
                    <a class="tdY msg_send_code">Reenviar mail con código</a>
                </div>

                <div class="box_ok small mbB msg_sended_user_inactive" style="display:none;">El correo ha sido enviado correctamente</div>
            }
            else
            {
                <div class="box_ok small mbB">Te hemos enviado un email a tu cuenta correo, si necesitas volver a reenviarlo dentro de unos minutos podrás enviarlo de nuevo</div>
            }
        }
        @if (Model.IsUserValidated)
        {
            <div class="box_ok small mbB">Hemos validado correctamente tu usuario</div>
        }
        <div class="fl w65_mB w100_m">
            <section class="box dFlex mb20 vm_fx mb0_m">
                @if (!string.IsNullOrEmpty(companyCredentials.ImgPath))
                {
                    <img id="id_company_logo" style="width:125px" src="@string.Format("{0}{1}", portalConfig.cdn_images, companyCredentials.ImgPath)" />
                }
                else
                {
                    <img id="id_company_logo" style="width:125px" class="img-circle" src="@Url.Content(string.Format("{0}img/logo_empty.svg", portalConfig.PrefixPathImage, portalConfig.countrycode.ToLower(), portalConfig.SufixPathImage))">
                }

                <div class="ml25">
                    <h1 class="mb5">@CompanyHelper.GetComercialName(companyCredentials)</h1>
                    <p class="fc_aux">@string.Format("{0} {1}", @PageLiteralsHelper.GetLiteral("LIT_USER", pageId, portalConfig), Model.RoleName)</p>
                </div>
            </section>

            <section class="box mb20 pb15 pt20 pt0_m mb0_m">
                <header class="header_block mB_neg_m title_m mb0_m">
                    <h1 class="mrAuto">@PageLiteralsHelper.GetLiteral("MASTER_TABS_RECRUITMENT", pageId, portalConfig)</h1>
                    @if (Model.LastOfferList.Any())
                    {
                        <a href="@Url.Action("Index", "CompanyOffers")" class="fs13_m">@PageLiteralsHelper.GetLiteral("LIT_GESTIONAR_OFERTAS", pageId, portalConfig)</a>
                    }
                </header>
                <div class="grid3 mb25 tc full_m hide_m">

                    <div>
                        <h3 class="mb15">@PageLiteralsHelper.GetLiteral("LIT_OFFERS_ACTIVES", pageId, portalConfig)</h3>
                        <div class="posRel">
                            <div style="height:180px">
                                <div class="chartjs-size-monitor" style="position: absolute; inset: 0px; overflow: hidden; pointer-events: none; visibility: hidden; z-index: -1;">
                                    <div class="chartjs-size-monitor-expand" style="position:absolute;left:0;top:0;right:0;bottom:0;overflow:hidden;pointer-events:none;visibility:hidden;z-index:-1;">
                                        <div style="position:absolute;width:1000000px;height:1000000px;left:0;top:0"></div>
                                    </div>
                                    <div class="chartjs-size-monitor-shrink" style="position:absolute;left:0;top:0;right:0;bottom:0;overflow:hidden;pointer-events:none;visibility:hidden;z-index:-1;">
                                        <div style="position:absolute;width:200%;height:200%;left:0; top:0"></div>
                                    </div>
                                </div>
                                <canvas id="chart-offer-pub" width="227" height="180" style="display: block; width: 227px; height: 180px;" class="chartjs-render-monitor"></canvas>
                            </div>
                            <div id="legend-offer-pub" class="info_donut"></div>
                        </div>

                        @if (Model.HomeMembresyResumDataModel.ActiveOffersCounter.UnitsExceded)
                        {
                            <div class="dIB mAuto mt10 posRel hover_bubble">
                                <span class="fc_error fs13 vm">@PageLiteralsHelper.GetLiteral("LIT_LIMITE_EXCEDIDO", pageId, portalConfig)</span>
                                <span class="info_tooltip ml5 vm">
                                    <div class="bubble_tooltip bottom hide">
                                        @string.Format(PageLiteralsHelper.GetLiteral("LIT_LIMITE_EXCEDIDO_DESC", pageId, portalConfig), Model.HomeMembresyResumDataModel.ActiveOffersCounter.TotalInitialByFeature)
                                    </div>
                                </span>
                            </div>
                        }
                    </div>


                    @if (Model.HomeMembresyResumDataModel.PublishOffersCounter.TotalInitialByFeature > 0)
                    {
                        <div>
                            <h3 class="mb15">@PageLiteralsHelper.GetLiteral("LIT_PUBLISHED_OFFERS", pageId, portalConfig)</h3>
                            <div class="posRel">
                                <div style="height:180px">
                                    <div class="chartjs-size-monitor" style="position: absolute; inset: 0px; overflow: hidden; pointer-events: none; visibility: hidden; z-index: -1;">
                                        <div class="chartjs-size-monitor-expand" style="position:absolute;left:0;top:0;right:0;bottom:0;overflow:hidden;pointer-events:none;visibility:hidden;z-index:-1;">
                                            <div style="position:absolute;width:1000000px;height:1000000px;left:0;top:0"></div>
                                        </div>
                                        <div class="chartjs-size-monitor-shrink" style="position:absolute;left:0;top:0;right:0;bottom:0;overflow:hidden;pointer-events:none;visibility:hidden;z-index:-1;">
                                            <div style="position:absolute;width:200%;height:200%;left:0; top:0"></div>
                                        </div>
                                    </div>
                                    <canvas id="chart-offer-pubXN" width="227" height="180" style="display: block; width: 227px; height: 180px;" class="chartjs-render-monitor"></canvas>
                                </div>
                                <div id="legend-offer-pubXN" class="info_donut"></div>
                            </div>

                            @if (Model.HomeMembresyResumDataModel.PublishOffersCounter.UnitsExceded)
                            {
                                <div class="dIB mAuto mt10 posRel hover_bubble">
                                    <span class="fc_error fs13 vm">@PageLiteralsHelper.GetLiteral("LIT_LIMITE_EXCEDIDO", pageId, portalConfig)</span>
                                    <span class="info_tooltip ml5 vm">
                                        <div class="bubble_tooltip bottom hide">
                                            @string.Format(PageLiteralsHelper.GetLiteral("LIT_LIMITE_EXCEDIDO_DESC_PUBLISH", pageId, portalConfig), Model.HomeMembresyResumDataModel.ActiveOffersCounter.TotalInitialByFeature)
                                        </div>
                                    </span>
                                </div>
                            }
                        </div>

                        <div class="dFlex fx_wrap vm_fx">
                            <div class="w30">
                                <div style="height:80px">
                                    <div class="chartjs-size-monitor" style="position: absolute; inset: 0px; overflow: hidden; pointer-events: none; visibility: hidden; z-index: -1;">
                                        <div class="chartjs-size-monitor-expand" style="position:absolute;left:0;top:0;right:0;bottom:0;overflow:hidden;pointer-events:none;visibility:hidden;z-index:-1;">
                                            <div style="position:absolute;width:1000000px;height:1000000px;left:0;top:0"></div>
                                        </div>
                                        <div class="chartjs-size-monitor-shrink" style="position:absolute;left:0;top:0;right:0;bottom:0;overflow:hidden;pointer-events:none;visibility:hidden;z-index:-1;">
                                            <div style="position:absolute;width:200%;height:200%;left:0; top:0"></div>
                                        </div>
                                    </div>
                                    <canvas id="chart-offer-urg" width="68" height="80" style="display: block; width: 68px; height: 80px;" class="chartjs-render-monitor"></canvas>
                                </div>
                            </div>
                            <div class="w70 pl10 tl">
                                <p>@PageLiteralsHelper.GetLiteral("LIT_OFFER_URGENT", pageId, portalConfig)</p>
                                <p class="fc_aux fs13">
                                    <span class="fc_brand_aux fs20 fwB mr5">@StringToolsHelper.ChangeGroupPointForDecimalPoint(Model.HomeMembresyResumDataModel.UrgentOffersCounter.ConsumedOffers.ToString("N0"), portalConfig.PortalId)</span>
                                    de @StringToolsHelper.ChangeGroupPointForDecimalPoint(Model.HomeMembresyResumDataModel.UrgentOffersCounter.TotalInitialByFeature.ToString("N0"), portalConfig.PortalId)
                                </p>
                                @if (Model.HomeMembresyResumDataModel.UrgentOffersCounter.UnitsExceded)
                                {
                                    <div class="dIB mt5 posRel hover_bubble">
                                        <span class="fc_error fs13 vm">@PageLiteralsHelper.GetLiteral("LIT_LIMITE_EXCEDIDO", pageId, portalConfig)</span>
                                        <span class="info_tooltip ml5 vm">
                                            <div class="bubble_tooltip bottom hide">@string.Format(PageLiteralsHelper.GetLiteral("LIT_LIMITE_EXCEDIDO_URG_DESC", pageId, portalConfig), Model.HomeMembresyResumDataModel.UrgentOffersCounter.TotalInitialByFeature)</div>
                                        </span>
                                    </div>
                                }
                            </div>

                            <div class="w30">
                                <div style="height:80px">
                                    <div class="chartjs-size-monitor" style="position: absolute; inset: 0px; overflow: hidden; pointer-events: none; visibility: hidden; z-index: -1;">
                                        <div class="chartjs-size-monitor-expand" style="position:absolute;left:0;top:0;right:0;bottom:0;overflow:hidden;pointer-events:none;visibility:hidden;z-index:-1;">
                                            <div style="position:absolute;width:1000000px;height:1000000px;left:0;top:0"></div>
                                        </div>
                                        <div class="chartjs-size-monitor-shrink" style="position:absolute;left:0;top:0;right:0;bottom:0;overflow:hidden;pointer-events:none;visibility:hidden;z-index:-1;">
                                            <div style="position:absolute;width:200%;height:200%;left:0; top:0"></div>
                                        </div>
                                    </div>
                                    <canvas id="chart-offer-dest" width="68" height="80" style="display: block; width: 68px; height: 80px;" class="chartjs-render-monitor"></canvas>
                                </div>
                            </div>
                            <div class="w70 pl10 tl">
                                <p>@PageLiteralsHelper.GetLiteral("LIT_OFFER_HIGHLIGHTED", pageId, portalConfig)</p>
                                <p class="fc_aux fs13">
                                    <span class="fc_info fs20 fwB mr5">@StringToolsHelper.ChangeGroupPointForDecimalPoint(Model.HomeMembresyResumDataModel.HighlightedOffersCounter.ConsumedOffers.ToString("N0"), portalConfig.PortalId)</span>de @StringToolsHelper.ChangeGroupPointForDecimalPoint(Model.HomeMembresyResumDataModel.HighlightedOffersCounter.TotalInitialByFeature.ToString("N0"), portalConfig.PortalId)
                                </p>

                                @if (Model.HomeMembresyResumDataModel.HighlightedOffersCounter.UnitsExceded)
                                {
                                    <div class="dIB mt5 posRel hover_bubble">
                                        <span class="fc_error fs13 vm">@PageLiteralsHelper.GetLiteral("LIT_LIMITE_EXCEDIDO", pageId, portalConfig)</span>
                                        <span class="info_tooltip ml5 vm">
                                            <div class="bubble_tooltip bottom hide">
                                                @string.Format(PageLiteralsHelper.GetLiteral("LIT_LIMITE_EXCEDIDO_DEST_DESC", pageId, portalConfig), Model.HomeMembresyResumDataModel.HighlightedOffersCounter.TotalInitialByFeature)
                                            </div>
                                        </span>
                                    </div>
                                }
                            </div>
                        </div>
                    }
                    else
                    {
                        <div>
                            <h3 class="mb15">@PageLiteralsHelper.GetLiteral("LIT_OFFER_HIGHLIGHTED", pageId, portalConfig)</h3>
                            <div class="posRel">
                                <div style="height:180px">
                                    <div class="chartjs-size-monitor" style="position: absolute; inset: 0px; overflow: hidden; pointer-events: none; visibility: hidden; z-index: -1;">
                                        <div class="chartjs-size-monitor-expand" style="position:absolute;left:0;top:0;right:0;bottom:0;overflow:hidden;pointer-events:none;visibility:hidden;z-index:-1;">
                                            <div style="position:absolute;width:1000000px;height:1000000px;left:0;top:0"></div>
                                        </div>
                                        <div class="chartjs-size-monitor-shrink" style="position:absolute;left:0;top:0;right:0;bottom:0;overflow:hidden;pointer-events:none;visibility:hidden;z-index:-1;">
                                            <div style="position:absolute;width:200%;height:200%;left:0; top:0"></div>
                                        </div>
                                    </div>
                                    <canvas id="chart-offer-dest" width="227" height="180" style="display: block; width: 227px; height: 180px;" class="chartjs-render-monitor"></canvas>
                                </div>
                                <div id="legend-offer-dest" class="info_donut"></div>
                            </div>

                            @if (Model.HomeMembresyResumDataModel.HighlightedOffersCounter.UnitsExceded)
                            {
                                <div class="dIB mAuto mt10 posRel hover_bubble">
                                    <span class="fc_error fs13 vm">@PageLiteralsHelper.GetLiteral("LIT_LIMITE_EXCEDIDO", pageId, portalConfig)</span>
                                    <span class="info_tooltip ml5 vm">
                                        <div class="bubble_tooltip bottom hide">
                                            @string.Format(PageLiteralsHelper.GetLiteral("LIT_LIMITE_EXCEDIDO_DEST_DESC", pageId, portalConfig), Model.HomeMembresyResumDataModel.HighlightedOffersCounter.TotalInitialByFeature)
                                        </div>
                                    </span>
                                </div>
                            }
                        </div>

                        <div>
                            <h3 class="mb15">@PageLiteralsHelper.GetLiteral("LIT_OFFER_URGENT", pageId, portalConfig)</h3>
                            <div class="posRel">
                                <div style="height:180px">
                                    <div class="chartjs-size-monitor" style="position: absolute; inset: 0px; overflow: hidden; pointer-events: none; visibility: hidden; z-index: -1;">
                                        <div class="chartjs-size-monitor-expand" style="position:absolute;left:0;top:0;right:0;bottom:0;overflow:hidden;pointer-events:none;visibility:hidden;z-index:-1;">
                                            <div style="position:absolute;width:1000000px;height:1000000px;left:0;top:0"></div>
                                        </div>
                                        <div class="chartjs-size-monitor-shrink" style="position:absolute;left:0;top:0;right:0;bottom:0;overflow:hidden;pointer-events:none;visibility:hidden;z-index:-1;">
                                            <div style="position:absolute;width:200%;height:200%;left:0; top:0"></div>
                                        </div>
                                    </div>
                                    <canvas id="chart-offer-urg" width="227" height="180" style="display: block; width: 227px; height: 180px;" class="chartjs-render-monitor"></canvas>
                                </div>
                                <div id="legend-offer-urg" class="info_donut"></div>
                            </div>

                            @if (Model.HomeMembresyResumDataModel.UrgentOffersCounter.UnitsExceded)
                            {
                                <div class="dIB mAuto mt10 posRel hover_bubble">
                                    <span class="fc_error fs13 vm">@PageLiteralsHelper.GetLiteral("LIT_LIMITE_EXCEDIDO", pageId, portalConfig)</span>
                                    <span class="info_tooltip ml5 vm">
                                        <div class="bubble_tooltip bottom hide">
                                            @string.Format(PageLiteralsHelper.GetLiteral("LIT_LIMITE_EXCEDIDO_URG_DESC", pageId, portalConfig), Model.HomeMembresyResumDataModel.UrgentOffersCounter.TotalInitialByFeature)
                                        </div>
                                    </span>
                                </div>
                            }
                        </div>
                    }

                </div>

                @if (Model.CanManageOffers)
                {
                    <div class="tc">
                        <a class="b_primary wAuto big w100_m" href="@Url.Action("Index", "CompanyOffersPublish")">@PageLiteralsHelper.GetLiteral("BTN_PUBLICAR_OFERTA", pageId, portalConfig)</a>
                    </div>
                }

                <header class="header_block bt1 mtB ptB btBox_m mB_neg_m title_m hide_m">
                    <h2 class="dIB">@PageLiteralsHelper.GetLiteral("LIT_EST", pageId, portalConfig)</h2>
                    <span class="fc_aux fs13 ml10">@PageLiteralsHelper.GetLiteral("LAST_30_DAYS", pageId, portalConfig)</span>
                    <a id="showStadistics" class="mlAuto fs13_m" href="@Url.Action("Index", "CompanyReports")">
                        @Html.Raw(PageLiteralsHelper.GetLiteral("LIT_VER_ESTADISTICAS", pageId, portalConfig))
                    </a>
                </header>

                <div id="withDataLineChart" class="grid2 full_m hide_m">
                    <div>
                        <div class="dFlex mb15">
                            <h3 class="mrAuto">@PageLiteralsHelper.GetLiteral("LINK_OF_ACTIVAS", pageId, portalConfig)</h3>
                            <p id="totalMailingOffers" class="fc_aux fs13"></p>
                        </div>
                        <div class="canvas" style="height:180px">
                            <div class="chartjs-size-monitor" style="position: absolute; inset: 0px; overflow: hidden; pointer-events: none; visibility: hidden; z-index: -1;">
                                <div class="chartjs-size-monitor-expand" style="position:absolute;left:0;top:0;right:0;bottom:0;overflow:hidden;pointer-events:none;visibility:hidden;z-index:-1;">
                                    <div style="position:absolute;width:1000000px;height:1000000px;left:0;top:0"></div>
                                </div>
                                <div class="chartjs-size-monitor-shrink" style="position:absolute;left:0;top:0;right:0;bottom:0;overflow:hidden;pointer-events:none;visibility:hidden;z-index:-1;">
                                    <div style="position:absolute;width:200%;height:200%;left:0; top:0"></div>
                                </div>
                            </div>
                            <div class="loading hide"><div><span></span><span></span><span></span><span></span></div><div><span></span><span></span><span></span><span></span></div><div><span></span><span></span><span></span><span></span></div></div>
                            <canvas id="chart01" width="350" height="180" style="display: block; width: 350px; height: 180px;" class="chartjs-render-monitor"></canvas>
                        </div>
                    </div>
                    <div>
                        <div class="dFlex mb15">
                            <h3 class="mrAuto">@PageLiteralsHelper.GetLiteral("LIT_POSTULATE", pageId, portalConfig)</h3>
                            <p id="totalMailingMatches" class="fc_aux fs13"></p>
                        </div>
                        <div class="canvas" style="height:180px">
                            <div class="chartjs-size-monitor" style="position: absolute; inset: 0px; overflow: hidden; pointer-events: none; visibility: hidden; z-index: -1;">
                                <div class="chartjs-size-monitor-expand" style="position:absolute;left:0;top:0;right:0;bottom:0;overflow:hidden;pointer-events:none;visibility:hidden;z-index:-1;">
                                    <div style="position:absolute;width:1000000px;height:1000000px;left:0;top:0"></div>
                                </div>
                                <div class="chartjs-size-monitor-shrink" style="position:absolute;left:0;top:0;right:0;bottom:0;overflow:hidden;pointer-events:none;visibility:hidden;z-index:-1;">
                                    <div style="position:absolute;width:200%;height:200%;left:0; top:0"></div>
                                </div>
                            </div>
                            <div class="loading hide"><div><span></span><span></span><span></span><span></span></div><div><span></span><span></span><span></span><span></span></div><div><span></span><span></span><span></span><span></span></div></div>
                            <canvas id="chart02" width="350" height="180" style="display: block; width: 350px; height: 180px;" class="chartjs-render-monitor"></canvas>
                        </div>
                    </div>
                </div>

                <div id="withOutDataLineChart" class="grid2 full_m hide hide_m">
                    <div class="fl w_50 w_100r pr20px pr0_ri">
                        <p class="fs16 mb15">@PageLiteralsHelper.GetLiteral("LINK_OF_ACTIVAS", pageId, portalConfig)</p>
                        <p class="fc_gray">@PageLiteralsHelper.GetLiteral("LIT_WITHOUT_EST", pageId, portalConfig)</p>
                    </div>
                    <div class="fl w_50 w_100r pl20px pl0px_r">
                        <p class="fs16 mb15">@PageLiteralsHelper.GetLiteral("LIT_POSTULATE", pageId, portalConfig)</p>
                        <p class="fc_gray">@PageLiteralsHelper.GetLiteral("LIT_WITHOUT_EST_CAND", pageId, portalConfig)</p>
                    </div>
                </div>


            </section>

            @if (Model.LastOfferList.Any())
            {
                <section class="box pb20 pt20 pAll0_m bb1_m">
                    <header class="header_block mb0 mb0_m title_m">
                        <h2 class="fs16_m">@PageLiteralsHelper.GetLiteral("LIT_LAST_OFFERS", pageId, portalConfig)</h2>
                        <a href="@Url.Action("Index", "CompanyOffers" )" class="mlAuto fs13_m tr_m">@PageLiteralsHelper.GetLiteral("LIT_VER_TODAS_LAS_OFERTAS", pageId, portalConfig)</a>
                    </header>
                    <div class="table_data flex bt0_m">
                        <header class="bb0 hide_m">
                            <div class="w50">@PageLiteralsHelper.GetLiteral("GRID_HEADER_1", pageId, portalConfig)</div>
                            <div class="tc_fx w25">@PageLiteralsHelper.GetLiteral("GRID_HEADER_4", pageId, portalConfig)</div>
                            <div class="tc_fx w25">@PageLiteralsHelper.GetLiteral("GRID_HEADER_5", pageId, portalConfig)</div>
                        </header>
                        <div class="tbody pAllB_m">
                            @foreach (var offer in Model.LastOfferList.OrderByDescending(c => c.datelastup).Take(3))
                            {
                                Html.RenderPartial("CompanyPage/Offers/_MembershipOffer", offer, new ViewDataDictionary {
                                    { "portalConfig", portalConfig },
                                    { "ShowTotalNotViewed", Model.ShowTotalNotViewed }
                                });
                            }
                        </div>
                    </div>
                </section>
            }
            else
            {
                <div class="box_info dFlex">
                    <span class="icon i_info fx_none mr10"></span>
                    <div class="pl10">
                        <p class="mt5">@Html.Raw(PageLiteralsHelper.GetLiteral("TEXTO_SIN_OFETAS_HOME", pageId, portalConfig))</p>
                    </div>
                </div>
            }
        </div>

        <div class="w35 fr w100_m hide_m">
            @if (Model.HasQrCode)
            {
                @Html.Partial("QRCode/_CompanyQRCodeCard", new ViewDataDictionary { { "isWide", true }, { "portalConfig", portalConfig }, { "companyName", companyCredentials.CompanyName } });
            }

            @if (Model.HasVision)
            {
                Html.RenderPartial("CompanyPage/_MembershipHomeVision", Model.VisionSummaryDataModel, new ViewDataDictionary { { "portalConfig", portalConfig }, { "companyCredentials", companyCredentials } });
            }
            else
            {
                Html.RenderPartial("CompanyPage/_CompanyResourcesBanner", Model.VisionSummaryDataModel, new ViewDataDictionary { { "portalConfig", portalConfig }, { "companyCredentials", companyCredentials } });
            }
        </div>


    </main>


    <div class="fdo_e clear pt0i">
        <div class="container">
            <div class="pt20i bgBlueMbl">
                <div class="fl w_100r homeCAdv resume">
                    <div class="fl w_63 w_100r">
                        <div class="fl100 boxWhite boxShadow p0 mb20 mb15_r o_vis">
                            <div class="reclutamiento p30 p15_r tc">

                                @if (Model.HomeMembresyResumDataModel.ActiveOffersCounter.ConsumedOffers > Model.HomeMembresyResumDataModel.ActiveOffersCounter.TotalInitialByFeature)
                                {
                                    <div class="tl mb25">
                                        <div class="hA">
                                            <div class="imgA">
                                                <span class="icoI">
                                                    <span class="icon icoInfo"></span>
                                                </span>
                                            </div>
                                            <div class="infoA">
                                                <span class="d_ib">Aviso importante</span>
                                                <span class="stA fs_16">@PageLiteralsHelper.GetLiteral("LIT_ANNOUNCEMENT_OFFERS_EXCEEDED", pageId, portalConfig)</span>
                                            </div>
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

@section CustomScriptsFooterSection
{
    @Scripts.Render("~/bundles/js/company/companyhome")

    <script type="text/javascript">


        var gtm_data = {
            supportUrl : '@portalConfig.ComercialInformationUrl',
            company_info : {
                email : '@companyCredentials.Email',
                last_name : '@companyCredentials.Name.Replace("'", "")',
                IdWeb : '@companyCredentials.IdCompany',
                hasATS : '@companyCredentials.HasATS',
                phone : '@companyCredentials.Phone.Replace("'", "")',
                Portal: '@companyCredentials.PortalId',
                IsMembresy: '@Model.HaveMembresyActive',
                IdType: '@companyCredentials.IdType',
                Membresy: '@Model.ProductComercialName'
            }
        }

        initializeCompanyHome({
            listDoughnut:
            [{
                id: 'chart-offer-pub',
                legendId: 'legend-offer-pub',
                data: [@Model.HomeMembresyResumDataModel.ActiveOffersCounter.PercentageOffers, @Model.HomeMembresyResumDataModel.ActiveOffersCounter.PercentageWithoutOffers],
                @if (Model.HomeMembresyResumDataModel.ActiveOffersCounter.ConsumedOffers != 0)
                {
                    if (Model.HomeMembresyResumDataModel.ActiveOffersCounter.ConsumedOffers > Model.HomeMembresyResumDataModel.ActiveOffersCounter.TotalInitialByFeature)
                    {
                        @:backgroundColor: ["#a8272d", "#edeef1"],
                    }
                    else
                    {
                        @:backgroundColor: ["#91b54e", "#edeef1"],
                    }
                }
                else
                {
                    @:backgroundColor: ["#edeef1"],
                }
                borderWidth: 3,
                cutoutPercentage:75,
                actualValue: "@StringToolsHelper.ChangeGroupPointForDecimalPoint(Model.HomeMembresyResumDataModel.ActiveOffersCounter.ConsumedOffers.ToString("N0"), portalConfig.PortalId)",
                @if (Model.HomeMembresyResumDataModel.ActiveOffersCounter.FeatureUnlimited)
                {
                    @:limitValue: 'sin límites'
                }
                else
                {
                    @:limitValue: '@string.Format("{0} {1}", PageLiteralsHelper.GetLiteral("LIT_DE", pageId, portalConfig), StringToolsHelper.ChangeGroupPointForDecimalPoint(Model.HomeMembresyResumDataModel.ActiveOffersCounter.TotalInitialByFeature.ToString("N0"), portalConfig.popup_apply_responsive_max_alert))'
                }
            },
                {
                id: 'chart-offer-pubXN',
                legendId: 'legend-offer-pubXN',
                data: [@Model.HomeMembresyResumDataModel.PublishOffersCounter.PercentageOffers, @Model.HomeMembresyResumDataModel.PublishOffersCounter.PercentageWithoutOffers],
                @if (Model.HomeMembresyResumDataModel.PublishOffersCounter.ConsumedOffers != 0)
                {
                    @:backgroundColor: ["#1e82c4", "#edeef1"],
                }
                else
                {
                    @:backgroundColor: ["#edeef1"],
                }
                borderWidth: 3,
                cutoutPercentage:75,
                actualValue: "@StringToolsHelper.ChangeGroupPointForDecimalPoint(Model.HomeMembresyResumDataModel.PublishOffersCounter.ConsumedOffers.ToString("N0"), portalConfig.PortalId)",
                 limitValue: '@string.Format("{0} {1}", PageLiteralsHelper.GetLiteral("LIT_DE", pageId, portalConfig), StringToolsHelper.ChangeGroupPointForDecimalPoint(Model.HomeMembresyResumDataModel.PublishOffersCounter.TotalInitialByFeature.ToString("N0"), portalConfig.popup_apply_responsive_max_alert))'
                },
                {
                id:'chart-offer-dest',
                legendId: 'legend-offer-dest',
                data: [@Model.HomeMembresyResumDataModel.HighlightedOffersCounter.PercentageOffers,@Model.HomeMembresyResumDataModel.HighlightedOffersCounter.PercentageWithoutOffers],
                @if (Model.HomeMembresyResumDataModel.HighlightedOffersCounter.ConsumedOffers != 0)
                {
                    if (Model.HomeMembresyResumDataModel.HighlightedOffersCounter.ConsumedOffers > Model.HomeMembresyResumDataModel.HighlightedOffersCounter.TotalInitialByFeature)
                    {
                        @:backgroundColor: ["#a8272d", "#edeef1"],
                    }
                    else
                    {
                        @:backgroundColor: ["#ff9b00", "#edeef1"],
                    }
                }
                else
                {
                    @:backgroundColor: ["#edeef1"],
                }
                borderWidth: 3,
                @if (Model.HomeMembresyResumDataModel.PublishOffersCounter.TotalInitialByFeature > 0)
                {
                    @:cutoutPercentage: 60,
                }
                else
                {
                    @:cutoutPercentage: 75,
                }
                actualValue: "@StringToolsHelper.ChangeGroupPointForDecimalPoint(Model.HomeMembresyResumDataModel.HighlightedOffersCounter.ConsumedOffers.ToString("N0"), portalConfig.PortalId)",
                @if (Model.HomeMembresyResumDataModel.HighlightedOffersCounter.FeatureUnlimited)
                {
                    @:limitValue: 'sin límites'
                }
                else
                {
                    @:limitValue: '@string.Format("{0} {1}", PageLiteralsHelper.GetLiteral("LIT_DE", pageId, portalConfig), StringToolsHelper.ChangeGroupPointForDecimalPoint(Model.HomeMembresyResumDataModel.HighlightedOffersCounter.TotalInitialByFeature.ToString("N0"), portalConfig.PortalId))'
                }
             },
             {
                id:'chart-offer-urg',
                legendId: 'legend-offer-urg',
                data: [@Model.HomeMembresyResumDataModel.UrgentOffersCounter.PercentageOffers,@Model.HomeMembresyResumDataModel.UrgentOffersCounter.PercentageWithoutOffers],
                @if (Model.HomeMembresyResumDataModel.UrgentOffersCounter.ConsumedOffers != 0)
                {
                    if (Model.HomeMembresyResumDataModel.UrgentOffersCounter.ConsumedOffers > Model.HomeMembresyResumDataModel.UrgentOffersCounter.TotalInitialByFeature)
                    {
                        @:backgroundColor: ["#a8272d", "#edeef1"],
                    }
                    else
                    {
                        @:backgroundColor: ["#ff5a00", "#edeef1"],
                    }
                }
                else
                {
                    @:backgroundColor: ["#edeef1"],
                }
                borderWidth: 3,
                @if (Model.HomeMembresyResumDataModel.PublishOffersCounter.TotalInitialByFeature > 0)
                {
                    @:cutoutPercentage: 60,
                }
                else
                {
                    @:cutoutPercentage: 75,
                }
                actualValue: "@StringToolsHelper.ChangeGroupPointForDecimalPoint(Model.HomeMembresyResumDataModel.UrgentOffersCounter.ConsumedOffers.ToString("N0"), portalConfig.PortalId)",
                @if (Model.HomeMembresyResumDataModel.UrgentOffersCounter.FeatureUnlimited)
                {
                    @:limitValue: 'sin límites'
                }
                else
                {
                    @:limitValue: '@string.Format("{0} {1}", PageLiteralsHelper.GetLiteral("LIT_DE", pageId, portalConfig), StringToolsHelper.ChangeGroupPointForDecimalPoint(Model.HomeMembresyResumDataModel.UrgentOffersCounter.TotalInitialByFeature.ToString("N0"), portalConfig.PortalId))'
                }
             }],
            IdCompany : @companyCredentials.IdCompany,
            PortalId : @portalConfig.PortalId,
            TotalText: '@PageLiteralsHelper.GetLiteral("P_TOTAL", pageId, portalConfig)'
        });


    </script>

}

