@model Redarbor.Company.WebUI.Models.LoginDataModel
@using Redarbor.Company.WebUI.Helpers;
@using Redarbor.Company.WebUI.Helpers.Html;
@using System.Web.Optimization;


@using Redarbor.Common.Entities.Enums;
@{
    Layout = null;

    int PageId = Html.SetCurrentPage((int)PageEnum.CompanyLogin);
    var portalConfig = PortalConfigHelper.GetPortalConfiguration();
}

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <link rel="shortcut icon" type="image/x-icon" href="@Url.Content(string.Format("{0}img/favicon.ico{1}", portalConfig.PrefixPathImage, portalConfig.SufixPathImage))" />

    <link rel="apple-touch-icon" sizes="120x120" href="@Url.Content(string.Format("{0}img/apple-touch-icon-120x120.png{1}", portalConfig.PrefixPathImage, portalConfig.SufixPathImage))" />
    <link rel="apple-touch-icon" href="@Url.Content(string.Format("{0}img/apple-touch-icon.png{1}", portalConfig.PrefixPathImage, portalConfig.SufixPathImage))" />

    <title>@PageLiteralsHelper.GetLiteral("HEAD_TITLE_COMPUTRABAJO_EMPRESA_PRIVADO", PageId, portalConfig)</title>
    @Styles.Render(string.Format("~/bundles/css/pem_new{0}", portalConfig.staticVirtualBundle))

    @Scripts.Render("~/bundles/js/jquery")

    @if (portalConfig.AEPortalConfig.HasHubSpot)
    {
        <!-- Start of HubSpot Embed Code -->
        <script type="text/javascript" id="hs-script-loader" async defer src="//js.hs-scripts.com/8889978.js?businessUnitId=660018"></script>
        <!-- End of HubSpot Embed Code -->
    }

</head>

<body>
    <main>
        @using (Html.BeginForm())
        {
            @Html.AntiForgeryToken()
            @Html.HiddenFor(m => m.IsFromMailPreRegisterEncrypted)
            <header class="pbB ptB tc">
                <a title="@PageLiteralsHelper.GetLiteral("LIT_LOGIN", PageId, portalConfig)" class="dIB" href="@portalConfig.url_web">
                    <img class="w70_m" srcset="@Url.Content(string.Format("{0}img/logoportal.svg{1}", portalConfig.PrefixPathImage, portalConfig.SufixPathImage))" alt="@PageLiteralsHelper.GetLiteral("ALT_IMG_LOGO_PORTAL", PageId, portalConfig)">
                </a>
            </header>

            <section class="box mAuto w35 mb0_m w100_m">
                <header class="fc_base mbB mB_neg mtB_neg title mb0_m mB_neg_m title_m">
                    <h1 class="fwB tc">@PageLiteralsHelper.GetLiteral("LIT_H1_ACCESO_EMPRESAS_REGISTRADAS", PageId, portalConfig)</h1>
                </header>
                @if (Model.ShowMessageUserVerified)
                {
                    <div class="box_ok">
                        @PageLiteralsHelper.GetLiteral("USER_INACTIVE", PageId, portalConfig)
                    </div>
                }

                <div class="field_input mb5">

                    <label class="fwB">@PageLiteralsHelper.GetLiteral("LIT_LOGIN_EMAIL_O_USUARIO", PageId, portalConfig)</label>
                    @Html.TextBoxFor(m => m.UserName, new { @class = "cm-3", @placeholder = @PageLiteralsHelper.GetLiteral("LIT_LOGIN_EMAIL_O_USUARIO", PageId, portalConfig) })
                    @Html.ValidationMessageFor(m => m.UserName, "", new { @class = "field-validation-valid" })

                </div>
                <a class="mbB dB" href="@Url.Action("", "RecoverEmailNit")">@PageLiteralsHelper.GetLiteral("LIT_NOT_REMEMBER_EMAIL", PageId, portalConfig)</a>

                <div class="field_input mb5">
                    <label class="fwB">@PageLiteralsHelper.GetLiteral("LIT_LOGIN_CLAVE", PageId, portalConfig)</label>
                    @Html.PasswordFor(m => m.Password, new { @id = "fiesta", autocomplete = "new-password", @class = "cm-3", @placeholder = @PageLiteralsHelper.GetLiteral("LIT_LOGIN_CLAVE", PageId, portalConfig) })
                    @Html.ValidationMessageFor(m => m.Password, "", new { @class = "field-validation-valid" })
                </div>
                <div class="field_input mb5">
                    @Html.ValidationMessage("DefaultError", new { @class = "field-validation-valid" })
                </div>
                <a class="mbB dB" href="@Url.Action("Index", "RecoverPassword")">@PageLiteralsHelper.GetLiteral("LIT_LOGIN_NO_RECUERDA_CLAVE", PageId, portalConfig)</a>


                <div class="field_on_off mt25">
                    <label>@PageLiteralsHelper.GetLiteral("LIT_LOGIN_PERMANECER_CONECTADO", PageId, portalConfig)</label>


                    @Html.CheckBoxFor(m => m.KeepMeLoggedIn, new { @checked = true })

                    <label class="switch dIB ml5" for="KeepMeLoggedIn" style="width: 70px;">
                        <span class="disc"></span>
                        <span class="on">Sí</span>
                        <span class="off">No</span>
                    </label>

                </div>


                <input type="submit" value="Entrar" class="b_primary w100">
            </section>
            <div class="mt20 tc bg_white_m mt0_m pAllB_m pt0_m">
                <p>@PageLiteralsHelper.GetLiteral("LIT_LOGIN_SI_NO_ESTA_REGISTRADO", PageId, portalConfig),</p>
                <p>@PageLiteralsHelper.GetLiteral("LIT_REGISTER_AS", PageId, portalConfig)<a href="@string.Format("{0}/Acceso/", portalConfig.url_candidate)">@PageLiteralsHelper.GetLiteral("LIT_CANDIDATO", PageId, portalConfig)</a>@PageLiteralsHelper.GetLiteral("LIT_OR_AS", PageId, portalConfig)&nbsp;<a href="@Url.Action("Index", "Register")">@PageLiteralsHelper.GetLiteral("LIT_COMPANY", PageId, portalConfig)</a></p>
            </div>
        }
    </main>

    @{ Html.RenderPartial("_GTMandAnalytics", new ViewDataDictionary { { "portalConfig", portalConfig } }); }
</body>
</html>