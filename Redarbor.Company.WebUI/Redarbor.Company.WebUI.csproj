<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.Default.props" Condition="Exists('$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.Default.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{C9EF820C-A441-40C0-B241-DD4DF3316BE7}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Redarbor.Company.WebUI</RootNamespace>
    <AssemblyName>Redarbor.Company.WebUI</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <UseIISExpress>true</UseIISExpress>
    <Use64BitIISExpress>false</Use64BitIISExpress>
    <IISExpressSSLPort>62358</IISExpressSSLPort>
    <IISExpressAnonymousAuthentication>enabled</IISExpressAnonymousAuthentication>
    <IISExpressWindowsAuthentication>disabled</IISExpressWindowsAuthentication>
    <IISExpressUseClassicPipelineMode>false</IISExpressUseClassicPipelineMode>
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <TargetFrameworkProfile />
    <TypeScriptToolsVersion>2.6</TypeScriptToolsVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <LangVersion>latest</LangVersion>
    <MvcBuildViews>false</MvcBuildViews>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <LangVersion>latest</LangVersion>
    <ExcludeGeneratedDebugSymbol>true</ExcludeGeneratedDebugSymbol>
    <LegacyPublishPropertiesPageEnabled>true</LegacyPublishPropertiesPageEnabled>
    <MvcBuildViews>true</MvcBuildViews>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AdvancedStringBuilder, Version=0.1.0.0, Culture=neutral, PublicKeyToken=e818a2fc08933ddb, processorArchitecture=MSIL">
      <HintPath>..\packages\AdvancedStringBuilder.0.1.0\lib\net45\AdvancedStringBuilder.dll</HintPath>
    </Reference>
    <Reference Include="Antlr3.Runtime, Version=3.4.1.9004, Culture=neutral, PublicKeyToken=eb42632606e9261f, processorArchitecture=MSIL">
      <HintPath>..\packages\Antlr.3.4.1.9004\lib\Antlr3.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Autofac, Version=4.8.0.0, Culture=neutral, PublicKeyToken=17863af14b0044da, processorArchitecture=MSIL">
      <HintPath>..\packages\Autofac.4.8.0\lib\net45\Autofac.dll</HintPath>
    </Reference>
    <Reference Include="Autofac.Integration.Mvc, Version=4.0.0.0, Culture=neutral, PublicKeyToken=17863af14b0044da, processorArchitecture=MSIL">
      <HintPath>..\packages\Autofac.Mvc5.4.0.2\lib\net45\Autofac.Integration.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="AutoMapper, Version=6.2.2.0, Culture=neutral, PublicKeyToken=be96cd2c38ef1005, processorArchitecture=MSIL">
      <HintPath>..\packages\AutoMapper.6.2.2\lib\net45\AutoMapper.dll</HintPath>
    </Reference>
    <Reference Include="AWSSDK.Core, Version=3.3.0.0, Culture=neutral, PublicKeyToken=885c28607f98e604, processorArchitecture=MSIL">
      <HintPath>..\packages\AWSSDK.Core.3.3.24.3\lib\net45\AWSSDK.Core.dll</HintPath>
    </Reference>
    <Reference Include="AWSSDK.S3, Version=3.3.0.0, Culture=neutral, PublicKeyToken=885c28607f98e604, processorArchitecture=MSIL">
      <HintPath>..\packages\AWSSDK.S3.3.3.19\lib\net45\AWSSDK.S3.dll</HintPath>
    </Reference>
    <Reference Include="BouncyCastle.Crypto, Version=1.8.9.0, Culture=neutral, PublicKeyToken=0e99375e54769942">
      <HintPath>..\packages\BouncyCastle.1.8.9\lib\BouncyCastle.Crypto.dll</HintPath>
    </Reference>
    <Reference Include="BouncyCastle.Cryptography, Version=2.0.0.0, Culture=neutral, PublicKeyToken=072edcf4a5328938, processorArchitecture=MSIL">
      <HintPath>..\packages\BouncyCastle.Cryptography.2.5.1\lib\net461\BouncyCastle.Cryptography.dll</HintPath>
    </Reference>
    <Reference Include="CommonPaymentLibrary, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>Lib\CommonPaymentLibrary.dll</HintPath>
    </Reference>
    <Reference Include="Dapper, Version=2.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Dapper.2.0.123\lib\net461\Dapper.dll</HintPath>
    </Reference>
    <Reference Include="Dapper.Contrib, Version=2.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Dapper.Contrib.2.0.78\lib\net461\Dapper.Contrib.dll</HintPath>
    </Reference>
    <Reference Include="Enyim.Caching, Version=2.16.0.0, Culture=neutral, PublicKeyToken=cec98615db04012e, processorArchitecture=MSIL">
      <HintPath>..\packages\EnyimMemcached.2.16.0\lib\net35\Enyim.Caching.dll</HintPath>
    </Reference>
    <Reference Include="EPPlus, Version=4.5.2.1, Culture=neutral, PublicKeyToken=ea159fdaa78159a1, processorArchitecture=MSIL">
      <HintPath>..\packages\EPPlus.4.5.2.1\lib\net40\EPPlus.dll</HintPath>
    </Reference>
    <Reference Include="Fasterflect, Version=2.1.3.0, Culture=neutral, PublicKeyToken=38d18473284c1ca7, processorArchitecture=MSIL">
      <HintPath>..\packages\fasterflect.2.1.3\lib\net40\Fasterflect.dll</HintPath>
    </Reference>
    <Reference Include="Google.Protobuf, Version=3.30.0.0, Culture=neutral, PublicKeyToken=a7d26565bac4d604, processorArchitecture=MSIL">
      <HintPath>..\packages\Google.Protobuf.3.30.0\lib\net45\Google.Protobuf.dll</HintPath>
    </Reference>
    <Reference Include="itextsharp, Version=5.5.13.3, Culture=neutral, PublicKeyToken=8354ae6d2174ddca, processorArchitecture=MSIL">
      <HintPath>..\packages\iTextSharp.5.5.13.3\lib\itextsharp.dll</HintPath>
    </Reference>
    <Reference Include="K4os.Compression.LZ4, Version=1.3.8.0, Culture=neutral, PublicKeyToken=2186fa9121ef231d, processorArchitecture=MSIL">
      <HintPath>..\packages\K4os.Compression.LZ4.1.3.8\lib\net462\K4os.Compression.LZ4.dll</HintPath>
    </Reference>
    <Reference Include="K4os.Compression.LZ4.Streams, Version=1.3.8.0, Culture=neutral, PublicKeyToken=2186fa9121ef231d, processorArchitecture=MSIL">
      <HintPath>..\packages\K4os.Compression.LZ4.Streams.1.3.8\lib\net462\K4os.Compression.LZ4.Streams.dll</HintPath>
    </Reference>
    <Reference Include="K4os.Hash.xxHash, Version=1.0.8.0, Culture=neutral, PublicKeyToken=32cd54395057cec3, processorArchitecture=MSIL">
      <HintPath>..\packages\K4os.Hash.xxHash.1.0.8\lib\net462\K4os.Hash.xxHash.dll</HintPath>
    </Reference>
    <Reference Include="log4net, Version=2.0.8.0, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a, processorArchitecture=MSIL">
      <HintPath>..\packages\log4net.2.0.8\lib\net45-full\log4net.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Http.Abstractions, Version=2.2.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Http.Abstractions.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Http.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Http.Features, Version=2.2.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Http.Features.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Http.Features.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=8.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.8.0.0\lib\net462\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.Extensions.Caching.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Caching.Abstractions.3.1.8\lib\netstandard2.0\Microsoft.Extensions.Caching.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Caching.Memory, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Caching.Memory.3.1.8\lib\netstandard2.0\Microsoft.Extensions.Caching.Memory.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Configuration.Abstractions.5.0.0\lib\net461\Microsoft.Extensions.Configuration.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection, Version=8.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.DependencyInjection.8.0.0\lib\net462\Microsoft.Extensions.DependencyInjection.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection.Abstractions, Version=8.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.DependencyInjection.Abstractions.8.0.0\lib\net462\Microsoft.Extensions.DependencyInjection.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.FileProviders.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.FileProviders.Abstractions.5.0.0\lib\net461\Microsoft.Extensions.FileProviders.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Hosting.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Hosting.Abstractions.5.0.0\lib\net461\Microsoft.Extensions.Hosting.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Logging.Abstractions.3.1.8\lib\netstandard2.0\Microsoft.Extensions.Logging.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.ObjectPool, Version=********, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.ObjectPool.5.0.11\lib\net461\Microsoft.Extensions.ObjectPool.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Options, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Options.3.1.8\lib\netstandard2.0\Microsoft.Extensions.Options.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Primitives, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Primitives.5.0.0\lib\net461\Microsoft.Extensions.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="MySql.Data, Version=9.3.0.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d, processorArchitecture=MSIL">
      <HintPath>..\packages\MySql.Data.9.3.0\lib\net462\MySql.Data.dll</HintPath>
    </Reference>
    <Reference Include="MySqlConnector, Version=1.0.0.0, Culture=neutral, PublicKeyToken=d33d3e53aa5f8c92, processorArchitecture=MSIL">
      <HintPath>..\packages\MySqlConnector.1.0.0\lib\net471\MySqlConnector.dll</HintPath>
    </Reference>
    <Reference Include="netstandard">
      <Private>True</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="PayPal, Version=1.9.1.0, Culture=neutral, PublicKeyToken=5b4afc1ccaef40fb, processorArchitecture=MSIL">
      <HintPath>..\packages\PayPal.1.9.1\lib\net451\PayPal.dll</HintPath>
    </Reference>
    <Reference Include="PayPalCoreSDK, Version=1.7.1.0, Culture=neutral, PublicKeyToken=5b4afc1ccaef40fb, processorArchitecture=MSIL">
      <HintPath>..\packages\PayPalCoreSDK.1.7.1\lib\net451\PayPalCoreSDK.dll</HintPath>
    </Reference>
    <Reference Include="PresentationCore" />
    <Reference Include="RabbitMQ.Client, Version=*******, Culture=neutral, PublicKeyToken=89e7d7c5feba84ce, processorArchitecture=MSIL">
      <HintPath>..\packages\RabbitMQ.Client.6.2.2\lib\net461\RabbitMQ.Client.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.AccessControl.ServiceLibrary, Version=1.4.2.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.AccessControl.ServiceLibrary.1.4.2\lib\netstandard2.0\Redarbor.AccessControl.ServiceLibrary.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Api.Contracts, Version=3.0.4.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Api.Contracts.3.0.4\lib\netstandard2.0\Redarbor.Api.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Api.Impl, Version=3.1.4.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Api.Impl.3.1.4\lib\netstandard2.0\Redarbor.Api.Impl.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Cache, Version=1.3.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Cache.2.0.2\lib\netstandard2.0\Redarbor.Cache.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Cache.Disk, Version=1.3.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Cache.Disk.2.0.1\lib\netstandard2.0\Redarbor.Cache.Disk.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Cache.Runtime, Version=1.3.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Cache.Runtime.2.0.1\lib\net461\Redarbor.Cache.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Candidates.Consumer, Version=2.0.4.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Candidates.Consumer.2.0.4\lib\net461\Redarbor.Candidates.Consumer.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.DeviceAuthenticate.Consumer, Version=1.2.8.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.DeviceAuthenticate.Consumer.1.2.8\lib\net461\Redarbor.DeviceAuthenticate.Consumer.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Dictionaries.Consumer, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Dictionaries.Consumer.4.6.2\lib\netstandard2.0\Redarbor.Dictionaries.Consumer.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Dictionary.Contracts, Version=5.1.7.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Dictionary.Contracts.5.1.7\lib\netstandard2.0\Redarbor.Dictionary.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Dictionary.Impl, Version=5.1.7.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Dictionary.Impl.5.1.7\lib\netstandard2.0\Redarbor.Dictionary.Impl.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.DiskCache.Contracts, Version=1.1.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.DiskCache.Contracts.1.1.0\lib\net461\Redarbor.DiskCache.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Encryption.Contracts, Version=1.0.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Encryption.Contracts.1.0.1\lib\netstandard2.0\Redarbor.Encryption.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Encryption.Impl, Version=1.0.2.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Encryption.Impl.1.0.2\lib\netstandard2.0\Redarbor.Encryption.Impl.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Extensions.NetStandard, Version=1.0.12.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Extensions.NetStandard.1.0.12\lib\netstandard2.0\Redarbor.Extensions.NetStandard.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.GlobalConfiguration.Library, Version=1.1.3.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.GlobalConfiguration.Library.1.1.3\lib\netstandard2.0\Redarbor.GlobalConfiguration.Library.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Jobads.Chat.Listener.Abstractions, Version=0.3.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Jobads.Chat.Listener.Abstractions.1.0.4\lib\netstandard2.0\Redarbor.Jobads.Chat.Listener.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.KillerQuestions.Consumer, Version=1.0.3.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.KillerQuestions.Consumer.1.0.3\lib\netstandard2.0\Redarbor.KillerQuestions.Consumer.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Kpi.Consumer, Version=3.2.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Kpi.Consumer.3.2.0\lib\netstandard2.0\Redarbor.Kpi.Consumer.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Kpi.Consumer.Abstractions, Version=3.2.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Kpi.Consumer.Abstractions.3.2.1\lib\netstandard2.0\Redarbor.Kpi.Consumer.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Matches.Elastic.Consumer, Version=2.1.42.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Matches.Elastic.Consumer.2.1.42\lib\netstandard2.0\Redarbor.Matches.Elastic.Consumer.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Payments.Consumer, Version=4.4.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Payments.Consumer.4.4.1\lib\netstandard2.0\Redarbor.Payments.Consumer.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Quiz.Contract.ServiceLibrary, Version=1.0.48.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Quiz.Contract.ServiceLibrary.1.0.48\lib\net452\Redarbor.Quiz.Contract.ServiceLibrary.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.RabbitMQ, Version=1.0.0.17, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.RabbitMQ.1.0.0.17\lib\netstandard2.0\Redarbor.RabbitMQ.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.RabbitMQ.Abstractions, Version=1.0.0.17, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.RabbitMQ.Abstractions.1.0.0.17\lib\netstandard2.0\Redarbor.RabbitMQ.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.RabbitMQ.Extensions, Version=1.0.0.17, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.RabbitMQ.Extensions.1.0.0.17\lib\netstandard2.0\Redarbor.RabbitMQ.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.RabbitMQ.Extensions.Autofac, Version=1.0.0.16, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.RabbitMQ.Extensions.Autofac.1.0.0.16\lib\netstandard2.0\Redarbor.RabbitMQ.Extensions.Autofac.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.RabbitMQ.Model, Version=1.0.0.17, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.RabbitMQ.Model.1.0.0.17\lib\netstandard2.0\Redarbor.RabbitMQ.Model.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Timeline.Company, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Timeline.Company.1.1.1\lib\netstandard2.0\Redarbor.Timeline.Company.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Timeline.Consumer.Abstractions, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Timeline.Consumer.Abstractions.2.1.1\lib\netstandard2.0\Redarbor.Timeline.Consumer.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Tools.Exceptions.Consumer, Version=2.1.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Tools.Exceptions.Consumer.2.1.0\lib\netstandard2.0\Redarbor.Tools.Exceptions.Consumer.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.Tools.Infraestructure.Core.MySQL, Version=2.3.6.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.Tools.Infraestructure.Core.MySQL.2.3.6\lib\netstandard2.0\Redarbor.Tools.Infraestructure.Core.MySQL.dll</HintPath>
    </Reference>
    <Reference Include="Redarbor.WebCache.Contracts, Version=3.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Redarbor.WebCache.Contracts.3.0.0\lib\netstandard2.0\Redarbor.WebCache.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel" />
    <Reference Include="System.ComponentModel.Annotations, Version=4.2.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ComponentModel.Annotations.4.7.0\lib\net461\System.ComponentModel.Annotations.dll</HintPath>
    </Reference>
    <Reference Include="System.Configuration.ConfigurationManager, Version=8.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Configuration.ConfigurationManager.8.0.0\lib\net462\System.Configuration.ConfigurationManager.dll</HintPath>
    </Reference>
    <Reference Include="System.Configuration.Install" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Data.OracleClient" />
    <Reference Include="System.Device" />
    <Reference Include="System.Diagnostics.DiagnosticSource, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.DiagnosticSource.8.0.1\lib\net462\System.Diagnostics.DiagnosticSource.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing.Design" />
    <Reference Include="System.IdentityModel" />
    <Reference Include="System.IO.Compression.FileSystem" />
    <Reference Include="System.IO.Pipelines, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Pipelines.5.0.2\lib\net461\System.IO.Pipelines.dll</HintPath>
    </Reference>
    <Reference Include="System.Management" />
    <Reference Include="System.Memory, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.5\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime" />
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.Security.AccessControl, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.AccessControl.6.0.0\lib\net461\System.Security.AccessControl.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Permissions, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Permissions.6.0.0\lib\net461\System.Security.Permissions.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Principal.Windows, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Principal.Windows.5.0.0\lib\net461\System.Security.Principal.Windows.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.Text.Encodings.Web, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encodings.Web.7.0.0\lib\net462\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Json, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Json.7.0.3\lib\net462\System.Text.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Channels, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Channels.4.7.1\lib\net461\System.Threading.Channels.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Transactions" />
    <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net47\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Helpers, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.Helpers.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http.Common, Version=4.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Web.Http.Common.4.0.20126.16343\lib\net40\System.Web.Http.Common.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Mvc, Version=5.2.7.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.2.7\lib\net45\System.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Optimization, Version=1.1.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Web.Optimization.1.1.3\lib\net40\System.Web.Optimization.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Razor.3.2.7\lib\net45\System.Web.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="WebActivatorEx, Version=2.0.0.0, Culture=neutral, PublicKeyToken=7b26dc2a43f6a0d4, processorArchitecture=MSIL">
      <HintPath>..\packages\WebActivatorEx.2.0.3\lib\net40\WebActivatorEx.dll</HintPath>
    </Reference>
    <Reference Include="WebGrease, Version=1.6.5135.21930, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\WebGrease.1.6.0\lib\WebGrease.dll</HintPath>
    </Reference>
    <Reference Include="WindowsBase" />
    <Reference Include="Worldpay.Sdk, Version=1.2.0.1, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>Lib\Worldpay.Sdk.dll</HintPath>
    </Reference>
    <Reference Include="WorldPayNewtonsoft.Json, Version=2.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>Lib\WorldPayNewtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="ZstdSharp, Version=0.8.5.0, Culture=neutral, PublicKeyToken=8d151af33a4ad5cf, processorArchitecture=MSIL">
      <HintPath>..\packages\ZstdSharp.Port.0.8.5\lib\net462\ZstdSharp.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Microsoft.Web.Infrastructure">
      <HintPath>..\packages\Microsoft.Web.Infrastructure.1.0.0.0\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="App_Data\mysql-services\common\schemas\ct-comun\dev\init_dev_db.sql" />
    <Content Include="App_Data\mysql-services\common\schemas\ct-comun\init\dumps\V0.0.0__ct_comun_init.sql" />
    <Content Include="App_Data\mysql-services\common\schemas\repo-settings\dev\init_dev_db.sql" />
    <Content Include="App_Data\mysql-services\common\schemas\repo-settings\init\dumps\V0.0.0__repo_settings_init.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocolaux\dev\init_dev_db.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocolaux\init\dumps\V0.0.0__aux_init.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocolaux\migrate\V01\V01.0001.0__create_table_info_apply_time.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocolaux\migrate\V01\V01.0002.0__create_sps.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocoldata\dev\init_dev_db.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocoldata\init\dumps\V0.0.0__data_init.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocoldata\init\migrate\V01\V01.0001.0__alter_table_candidate_scheduled_mail_leaves.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocollctr\dev\init_dev_db.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocollctr\dev\populate\V0.1.001__computrabajo_co_lctr.dtmatch.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocollctr\init\dumps\V0.0.0__lctr_init.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocollctr\migrate\V01\V01.0001.0__alter_table.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocollctr\migrate\V01\V01.0002.0__create_sps.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocol\dev\init_dev_db.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocol\dev\populate\V0.1.001__computrabajo_co.pro_product.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocol\dev\populate\V0.1.002__computrabajo_co.company_domains.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocol\dev\populate\V0.1.003__computrabajo_co.company_products.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocol\dev\populate\V0.1.004__computrabajo_co.company_products_features.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocol\dev\populate\V0.1.005__computrabajo_co.company_saved_filters.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocol\dev\populate\V0.1.006__computrabajo_co.description_literalsfeatures_landing.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocol\dev\populate\V0.1.007__computrabajo_co.dtcompany.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocol\dev\populate\V0.1.008__computrabajo_co.dtuser.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocol\dev\populate\V0.1.009__pro_product_portal.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocol\dev\populate\V0.1.010__pro_product_portal_features.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocol\dev\populate\V0.1.011__pro_product_portal_pages.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocol\dev\populate\V0.1.012__pro_product_portal_temporality.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocol\dev\populate\V0.1.013__pro_product.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocol\dev\populate\V0.1.014__portal.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocol\dev\populate\V0.1.015__dtoperacioncompra.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocol\dev\populate\V0.1.016__dtinvoice_lines.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocol\dev\populate\V0.1.017___dtinvoice.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocol\dev\populate\V0.1.018__companylogoshome.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocol\dev\populate\V0.1.019__portal_config_currency.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocol\dev\populate\V0.1.020__permissions.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocol\dev\populate\V0.1.021__portal_config_url_apis.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocol\dev\populate\V0.1.022__portal_config_semantics.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocol\dev\populate\V0.1.023__pro_product_subgroups.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocol\dev\populate\V0.1.024__items_combo.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocol\dev\populate\V0.1.025__seed_company_prooducts.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocol\init\dumps\V0.0.0__master_init.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocol\migrate\V01\V01.0001.0__alter_tables.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocol\migrate\V01\V01.0004.0__modify_table_and_sp_crud_entity_invoice.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocol\migrate\V01\V01.0003.0__create_sps.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocol\migrate\V01\V01.0002.0__create_table_CompanyFreemiumOffers_Test.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocol\migrate\V01\V01.0007.0__create_sps_invoiceentity.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocol\migrate\V01\V01.0006.0__alter_table_dt_invoiceentity.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocol\migrate\V01\V01.0005.0__get_user_misuse_list.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\Literals\populate_db_literals_03.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\Literals\populate_db_literals_02.sql" />
    <Content Include="App_Data\mysql-services\master\schemas\Literals\populate_db_literals_01.sql" />
    <Content Include="App_Data\mysql-services\repos\schemas\ae\dev\populate\V0.1.001__repo_ae_initalize.sql" />
    <Content Include="App_Data\mysql-services\repos\schemas\ae\migrate\V01\V01.0001.0__alter_table_dt_invoiceentity.sql" />
    <Content Include="App_Data\mysql-services\repos\schemas\ae\migrate\V01\V01.0002.0__create_sps_invoiceentity.sql" />
    <Content Include="App_Data\mysql-services\repos\schemas\authenticate\dev\init_dev_db.sql" />
    <Content Include="App_Data\mysql-services\repos\schemas\authenticate\init\dumps\V0.0.0__authenticate_init.sql" />
    <Content Include="App_Data\mysql-services\repos\schemas\companies\dev\init_dev_db.sql" />
    <Content Include="App_Data\mysql-services\repos\schemas\companies\init\dumps\V0.0.0__companies_init.sql" />
    <Content Include="App_Data\mysql-services\repos\schemas\ct_user\dev\init_dev_db.sql" />
    <Content Include="App_Data\mysql-services\repos\schemas\ct_user\init\dumps\V0.0.0__ct_user_init.sql" />
    <Content Include="App_Data\mysql-services\repos\schemas\offers\dev\init_dev_db.sql" />
    <Content Include="App_Data\mysql-services\repos\schemas\offers\dev\populate\V0.1.001__repo_offers.offersl.sql" />
    <Content Include="App_Data\mysql-services\repos\schemas\offers\dev\populate\V0.1.002__repo_offers.offerintegrators.sql" />
    <Content Include="App_Data\mysql-services\repos\schemas\offers\dev\populate\V0.1.003__repo_offers.occ-offers.sql" />
    <Content Include="App_Data\mysql-services\repos\schemas\offers\init\dumps\V0.0.0__offers_init.sql" />
    <Content Include="App_Data\mysql-services\repos\schemas\offers\migrate\V01\V01.0004.0__create_sps.sql" />
    <Content Include="App_Data\mysql-services\repos\schemas\offers\migrate\V01\V01.0003.0__alter_table_OfferSkills.sql" />
    <Content Include="App_Data\mysql-services\repos\schemas\offers\migrate\V01\V01.0002.0__create_table_MappingMasterSKillsMasterSkillsCommon.sql" />
    <Content Include="App_Data\mysql-services\repos\schemas\offers\migrate\V01\V01.0001.0__create_sp-offergetlistmulti_v2.sql" />
    <Content Include="App_Data\mysql-services\repos\schemas\offers\migrate\V01\V01.0006.0__offer_benefits.sql" />
    <Content Include="App_Data\mysql-services\repos\schemas\offers\migrate\V01\V01.0005.0__adapt_offers_to_occ.sql" />
    <Content Include="App_Data\mysql-services\repos\schemas\offers_aux\dev\init_dev_db.sql" />
    <Content Include="App_Data\mysql-services\repos\schemas\offers_aux\init\dumps\V0.0.0__offers_aux_init.sql" />
    <Content Include="App_Data\mysql-services\repos\schemas\offers_aux\migrate\V01\V01.0001.0__create_sps.sql" />
    <Content Include="App_Data\mysql-services\repos\schemas\products\dev\init_dev_db.sql" />
    <Content Include="App_Data\mysql-services\repos\schemas\products\init\dumps\V0.0.0__products_init.sql" />
    <Content Include="App_Data\mysql-services\repos\schemas\ae\dev\init_dev_db.sql" />
    <Content Include="App_Data\mysql-services\repos\schemas\ae\init\dumps\V0.0.0__repo_ae_init.sql" />
    <Content Include="App_Data\mysql-services\repos\schemas\repo_killer_questions\dev\init_dev_db.sql" />
    <Content Include="App_Data\mysql-services\repos\schemas\repo_killer_questions\init\dumps\V0.0.0__repo_killer_questions_init.sql" />
    <Content Include="App_Data\mysql-services\repos\schemas\tracking\dev\init_dev_db.sql" />
    <Content Include="App_Data\mysql-services\repos\schemas\tracking\init\dumps\V0.0.0__tracking_init.sql" />
    <Content Include="c\js\company\bbddManageFolders.js" />
    <Content Include="c\js\company\bbddManageFoldersNewPem.js" />
    <Content Include="c\js\company\Cart\CompanyMultiCartNew.js" />
    <Content Include="c\js\company\Cart\multiPurchaseEditCompanyData.js" />
    <Content Include="c\js\company\csatSurvey.js" />
    <Content Include="c\js\company\cvlistnewpem.js" />
    <Content Include="c\js\company\contact.js" />
    <Content Include="c\js\company\documentIdentify.js" />
    <Content Include="c\js\company\mobile\csatSurveyMobile.js" />
    <Content Include="c\js\company\mobile\videoCandidateMobile.js" />
    <Content Include="c\js\company\videoCandidate.js" />
    <Content Include="c\js\company\mobile\commercialRequestMobile.js" />
    <Content Include="c\js\company\mobile\contactMobile.js" />
    <Content Include="c\js\company\newMatchesNotifications.js" />
    <Content Include="c\js\company\offerPreRegister.js" />
    <Content Include="c\js\company\vision\aboutCompany.js" />
    <Content Include="c\v2\css\pem_new.css" />
    <Content Include="c\v2\img\img_popup.png" />
    <Content Include="c\v2\img\importar-ofertas.svg" />
    <Content Include="c\v2\img\bg_search.svg" />
    <Content Include="c\v2\img\bg_send_disabled.svg" />
    <Content Include="c\v2\img\bg_send_w.svg" />
    <Content Include="c\v2\img\i_cv.svg" />
    <Content Include="c\v2\img\i_date.svg" />
    <Content Include="c\v2\img\i_ok.svg" />
    <Content Include="c\v2\img\i_search.svg" />
    <Content Include="Views\Shared\Partials\PopUpsMobile\_ContactPopUpMobile.cshtml" />
    <Content Include="Views\Shared\Partials\PopUpsMobile\_NotFeaturePopUpMobile.cshtml" />
    <Content Include="c\js\company\mobile\featurespostpublishMobile.js" />
    <Content Include="c\js\company\mobile\offerpostpublishMobile.js" />
    <Content Include="c\js\company\mobile\offerpublishMobile.js" />
    <Content Include="c\js\cookieManagement.js" />
    <Content Include="c\js\company\landing-convert-mobile.js" />
    <Content Include="c\js\company\mobile\accountuser_mobile.js" />
    <Content Include="c\js\company\mobile\configurationCompanyMobile.js" />
    <Content Include="c\js\company\mobile\matchMobile.js" />
    <Content Include="c\js\company\mobile\offeraiMobile.js" />
    <Content Include="c\js\company\mobile\vision\aboutCompanyMobile.js" />
    <Content Include="c\js\company\mobile\vision\benefitsMobile.js" />
    <Content Include="c\js\company\mobile\vision\interviewcommentsMobile.js" />
    <Content Include="c\js\company\mobile\vision\requestValuationMobile.js" />
    <Content Include="c\js\company\mobile\starFilterMobile.js" />
    <Content Include="c\js\company\mobile\vision\awardsMobile.js" />
    <Content Include="c\js\company\mobile\vision\commentsMobile.js" />
    <Content Include="c\js\company\mobile\vision\companyNewsDetailMobile.js" />
    <Content Include="c\js\company\mobile\vision\logoMobile.js" />
    <Content Include="c\js\company\mobile\vision\newsMobile.js" />
    <Content Include="c\js\company\mobile\vision\visionMobile.js" />
    <Content Include="c\js\company\bannersManagement.js" />
    <Content Include="c\v2\css\results_talentview_3D.css" />
    <Content Include="c\v2\img\bg.svg" />
    <Content Include="c\v2\img\bg_calendar.svg" />
    <Content Include="c\v2\img\coet.svg" />
    <Content Include="c\v2\img\CTsmile120.png" />
    <Content Include="c\v2\img\diamon.svg" />
    <Content Include="c\v2\img\i_payment_calendar_white.svg" />
    <Content Include="Views\CompanyMatches\EditorTemplates\CompanyMatchesMySelectionMobile.cshtml" />
    <Content Include="Views\CompanyVisionNews\Index.Mobile.cshtml" />
    <Content Include="Views\CompanyVisionNewsDetail\Index.Mobile.cshtml" />
    <Content Include="Views\CompanyVisionAwards\Index.Mobile.cshtml" />
    <Content Include="Views\CompanyVisionAboutCompany\Index.Mobile.cshtml" />
    <Content Include="Views\CompanyVisionLogo\Index.Mobile.cshtml" />
    <Content Include="Views\CompanyConfigurationEditCompany\Index.Mobile.cshtml" />
    <Content Include="Views\CompanyConfigurationEditData\Index.Mobile.cshtml" />
    <Content Include="Views\CompanyConfigurationSubMenu\Menu.Mobile.cshtml" />
    <Content Include="Views\CompanyVisionValuationRequest\Index.Mobile.cshtml" />
    <Content Include="Views\CompanyVisionComments\Index.Mobile.cshtml" />
    <Content Include="Views\CompanyOfferAI\Selection.Mobile.cshtml" />
    <Content Include="Views\CompanyOfferAI\Index.Mobile.cshtml" />
    <Content Include="Views\Account\Blocked.Mobile.cshtml">
      <Generator>RazorGenerator</Generator>
    </Content>
    <Content Include="Views\Account\Used.Mobile.cshtml" />
    <Content Include="Views\CompanyConfigurationAccounts\Index.Mobile.cshtml" />
    <Content Include="Views\CompanyConfigurationAccounts\Partials\_PopupAddUser.Mobile.cshtml" />
    <Content Include="Views\CompanyConfigurationAccounts\Partials\_PopupEditUser.Mobile.cshtml" />
    <Content Include="Views\CompanyConfigurationAccounts\Partials\_PopupActionsUser.Mobile.cshtml" />
    <Content Include="Views\CompanyConfigurationAccounts\Partials\_PopupDeleteUser.Mobile.cshtml" />
    <Content Include="Views\CompanyConfigurationAccounts\Partials\_PopupAllFilters.Mobile.cshtml" />
    <Content Include="Views\CompanyConfigurationAccounts\Partials\_CantAddUser.cshtml" />
    <Content Include="Views\CompanyVisionInterviewsComments\Index.Mobile.cshtml" />
    <Content Include="Views\CompanyExportToPDF\ExportPdf.cshtml" />
    <Content Include="Views\CompanyExportToPDF\ExportPdfDos.cshtml" />
    <Content Include="Views\CompanyLanding\ConvertPacks.Mobile.cshtml" />
    <Content Include="c\v2\css\fonts\nexa_font\nexa_font.woff2" />
    <Content Include="Views\CompanyOffersPublish\Index.Mobile.cshtml" />
    <Content Include="Views\CompanyFeaturesPostPublish\index.Mobile.cshtml" />
    <Content Include="Views\CompanyOffersPostPublish\Index.Mobile.cshtml" />
    <Content Include="Views\MiArea\Partials\_ChatPopupPartial.cshtml" />
    <Content Include="c\js\company\CandidateComparator\candidateComparator.js" />
    <Content Include="c\js\company\Cart\CompanyMultiCart.js" />
    <Content Include="c\js\company\comercial-request.js" />
    <Content Include="c\js\company\conversations\adaptative\adaptativeConversations.js" />
    <Content Include="c\js\company\conversations\responsive\chat.js" />
    <Content Include="c\js\company\conversations\responsive\conversationPopUp.js" />
    <Content Include="c\js\company\conversations\responsive\responsiveConversations.js" />
    <Content Include="c\js\company\Match\box_link.js" />
    <Content Include="c\js\company\mobile\convertToCompletePopUpMobile.js" />
    <Content Include="c\js\company\mobile\cvDetailCommonMobile.js" />
    <Content Include="c\js\company\mobile\globalCommonMobile.js" />
    <Content Include="c\js\company\mobile\homeMobile.js" />
    <Content Include="c\js\company\mobile\offerlistMobile.js" />
    <Content Include="c\js\company\mobile\cvlistMobile.js" />
    <Content Include="c\js\company\mobile\matchListOfferMobile.js" />
    <Content Include="c\js\company\mobile\menuMobile.js" />
    <Content Include="c\js\company\offer\offerai.js" />
    <Content Include="c\js\company\show_menu.js" />
    <Content Include="c\js\company\newlogin.js" />
    <Content Include="c\js\company\simplifiedPreRegister.js" />
    <Content Include="c\js\company\simplifiedValidateLogin.js" />
    <Content Include="c\js\company\simplifiedValidateRegister.js" />
    <Content Include="c\js\ConvertToCompletePopUp.js" />
    <Content Include="c\js\form.autocomplete.newpem.js" />
    <Content Include="c\js\menutools.js" />
    <Content Include="c\js\company\box_show_hide.js" />
    <Content Include="c\js\company\landinginforegister.js" />
    <Content Include="c\js\company\PostPublish\box_fix.js" />
    <Content Include="c\js\company\PostPublish\postpublishsteptwo.js" />
    <Content Include="c\js\company\simplifiedRegister.js" />
    <Content Include="c\js\company\formSF.js" />
    <Content Include="c\js\company\groupedlandings.js" />
    <Content Include="c\js\company\hide_box_after_time.js" />
    <Content Include="c\js\company\login.js" />
    <Content Include="c\js\company\PostPublish\featurespostpublish.js" />
    <Content Include="c\js\company\PostPublish\offerpostpublish.js" />
    <Content Include="c\js\company\password.js" />
    <Content Include="c\js\company\companyhome.js" />
    <Content Include="c\js\company\popup.js" />
    <Content Include="c\js\company\publicLanding.js" />
    <Content Include="c\js\company\sherlock.js" />
    <Content Include="c\js\company\vision\companyNewsDetail.js" />
    <Content Include="c\js\cookieConsent.js" />
    <Content Include="c\js\cookies.js" />
    <Content Include="c\js\cookieService.js" />
    <Content Include="c\js\googleTagManager.js" />
    <Content Include="c\js\jquery.mailcheck.min.js" />
    <Content Include="c\js\redarbor.messageconfirm.js" />
    <Content Include="c\js\redarbor.utils.js" />
    <Content Include="c\js\company\configuration\invoice.js" />
    <Content Include="c\js\company\Match\ExclusionMatches.js" />
    <Content Include="c\js\analytics.js" />
    <Content Include="c\js\chart.js" />
    <Content Include="c\js\clientstorage.js" />
    <Content Include="c\js\company\customFolders.js" />
    <Content Include="c\js\company\configurationcompany.js" />
    <Content Include="c\js\company\cvdownload.js" />
    <Content Include="c\js\company\cvlist.js" />
    <Content Include="c\js\company\editcredits.js" />
    <Content Include="c\js\company\home.js" />
    <Content Include="c\js\company\landing.js" />
    <Content Include="c\js\company\master.js" />
    <Content Include="c\js\company\match.js" />
    <Content Include="c\js\company\messages\messageslist.js" />
    <Content Include="c\js\company\offerlist.js" />
    <Content Include="c\js\company\offerpublish.js" />
    <Content Include="c\js\company\accountuser.js" />
    <Content Include="c\js\company\offer\offermembership.js" />
    <Content Include="c\js\company\report.js" />
    <Content Include="c\js\company\trackingaction.js" />
    <Content Include="c\js\company\validations\validationsbyportal.js" />
    <Content Include="c\js\company\vision\comments.js" />
    <Content Include="c\js\company\vision\awards.js" />
    <Content Include="c\js\company\vision\interviewcomments.js" />
    <Content Include="c\js\company\vision\requestValuation.js" />
    <Content Include="c\js\company\vision\interviewstatistics.js" />
    <Content Include="c\js\company\vision\logo.js" />
    <Content Include="c\js\company\vision\vision.js" />
    <Content Include="c\js\company\vision\talentescape.js" />
    <Content Include="c\js\company\vision\escaperisk.js" />
    <Content Include="c\js\company\vision\attractiveness.js" />
    <Content Include="c\js\company\vision\companydetail.js" />
    <Content Include="c\js\company\vision\photos.js" />
    <Content Include="c\js\company\vision\news.js" />
    <Content Include="c\js\company\vision\rankings.js" />
    <Content Include="c\js\company\vision\valuations.js" />
    <Content Include="c\js\form.autocomplete.js" />
    <Content Include="c\js\jquery.chosen.js" />
    <Content Include="c\js\jquery.lazy.js" />
    <Content Include="c\js\jquery.lazy.min.js" />
    <Content Include="c\js\jquery.lazy.plugins.js" />
    <Content Include="c\js\jquery.lazy.plugins.min.js" />
    <Content Include="c\js\company\company.js" />
    <Content Include="c\js\sessionheartbeat.js" />
    <Content Include="c\js\show_tooltips.js" />
    <Content Include="c\js\timeUse.js" />
    <Content Include="c\js\track.js" />
    <Content Include="c\js\company\vision\benefits.js" />
    <Content Include="c\js\validation-rules.js" />
    <Content Include="c\v2\css\cookies.css" />
    <Content Include="c\v2\css\landing_saleforce_mobile.css" />
    <Content Include="c\v2\css\landing.css" />
    <Content Include="c\v2\css\chosen.css" />
    <Content Include="c\v2\css\gestion-ofertas_v2.css" />
    <Content Include="c\v2\css\landing_inforegister.css" />
    <Content Include="c\v2\css\landing_inforegister_mobile.css" />
    <Content Include="c\v2\css\landing_productos.css" />
    <Content Include="c\v2\css\landing_saleforce.css" />
    <Content Include="c\v2\css\pem.css" />
    <Content Include="c\v2\css\pem_min.css" />
    <Content Include="c\v2\css\print_CV.css" />
    <Content Include="c\v2\css\print_offer.css" />
    <Content Include="c\v2\css\publica.css" />
    <Content Include="c\v2\css\vision.css" />
    <Content Include="c\v2\img\400.svg" />
    <Content Include="c\v2\img\ae-header.svg" />
    <Content Include="c\v2\img\ahorro-tiempo.jpg" />
    <Content Include="c\v2\img\apple-touch-icon-120x120.png" />
    <Content Include="c\v2\img\apple-touch-icon.png" />
    <Content Include="c\v2\img\atrae.svg" />
    <Content Include="c\v2\img\banner-AE_Blog-Recursos.jpg" />
    <Content Include="c\v2\img\bg_landing_inf.svg" />
    <Content Include="c\v2\img\bg_landing_sup.svg" />
    <Content Include="c\v2\img\bg_login_empresa.jpg" />
    <Content Include="c\v2\img\bg_photo.png" />
    <Content Include="c\v2\img\bg_register_ae.jpg" />
    <Content Include="c\v2\img\bg_register_ae_mbl.jpg" />
    <Content Include="c\v2\img\bg_save_filter.jpg" />
    <Content Include="c\v2\img\bg_select.svg" />
    <Content Include="c\v2\img\buscador-candidatos.svg" />
    <Content Include="c\v2\img\buscador.jpg" />
    <Content Include="c\v2\img\buscador_cv.svg" />
    <Content Include="c\v2\img\candidatos-cualificados.jpg" />
    <Content Include="c\v2\img\chat.gif" />
    <Content Include="c\v2\img\chicazul.png" />
    <Content Include="c\v2\img\circle_rate.png" />
    <Content Include="c\v2\img\circle_rate.svg" />
    <Content Include="c\v2\img\circulos\1.jpg" />
    <Content Include="c\v2\img\circulos\2.jpg" />
    <Content Include="c\v2\img\circulos\3.jpg" />
    <Content Include="c\v2\img\computrabajo-150x30.svg" />
    <Content Include="c\v2\img\contratar-candidatos.png" />
    <Content Include="c\v2\img\datepicker\ui-bg_diagonals-thick_18_b81900_40x40.png" />
    <Content Include="c\v2\img\datepicker\ui-bg_diagonals-thick_20_666666_40x40.png" />
    <Content Include="c\v2\img\datepicker\ui-bg_flat_10_000000_40x100.png" />
    <Content Include="c\v2\img\datepicker\ui-bg_glass_100_f6f6f6_1x400.png" />
    <Content Include="c\v2\img\datepicker\ui-bg_glass_100_fdf5ce_1x400.png" />
    <Content Include="c\v2\img\datepicker\ui-bg_glass_65_ffffff_1x400.png" />
    <Content Include="c\v2\img\datepicker\ui-bg_gloss-wave_35_f6a828_500x100.png" />
    <Content Include="c\v2\img\datepicker\ui-bg_highlight-soft_100_eeeeee_1x100.png" />
    <Content Include="c\v2\img\datepicker\ui-bg_highlight-soft_75_ffe45c_1x100.png" />
    <Content Include="c\v2\img\datepicker\ui-icons_222222_256x240.png" />
    <Content Include="c\v2\img\datepicker\ui-icons_228ef1_256x240.png" />
    <Content Include="c\v2\img\datepicker\ui-icons_ef8c08_256x240.png" />
    <Content Include="c\v2\img\datepicker\ui-icons_ffd27a_256x240.png" />
    <Content Include="c\v2\img\datepicker\ui-icons_ffffff_256x240.png" />
    <Content Include="c\v2\img\edVal.png" />
    <Content Include="c\v2\img\empty_user_CT.svg" />
    <Content Include="c\v2\img\eval-desenfoc.jpg" />
    <Content Include="c\v2\img\eval-recomienda-desenfoc.jpg" />
    <Content Include="c\v2\img\familia.jpg" />
    <Content Include="c\v2\img\favicon.ico" />
    <Content Include="c\v2\img\fdo_flecha1.gif" />
    <Content Include="c\v2\img\financieros.jpg" />
    <Content Include="c\v2\img\fondo-filtro1.jpg" />
    <Content Include="c\v2\img\fondo-filtro2.jpg" />
    <Content Include="c\v2\img\fondoempresa.jpg" />
    <Content Include="c\v2\img\fondopacks.jpg" />
    <Content Include="c\v2\img\fondo_404.jpg" />
    <Content Include="c\v2\img\fondo_ciudad.jpg" />
    <Content Include="c\v2\img\fondo_home.jpg" />
    <Content Include="c\v2\img\footer\Facebook.png" />
    <Content Include="c\v2\img\footer\goolge_plus.png" />
    <Content Include="c\v2\img\footer\social.png" />
    <Content Include="c\v2\img\footer\tweet.png" />
    <Content Include="c\v2\img\forma-landing.png" />
    <Content Include="c\v2\img\freemium2.png" />
    <Content Include="c\v2\img\google.svg" />
    <Content Include="c\v2\img\grado-desenfoc.jpg" />
    <Content Include="c\v2\img\grado-riesgo-desenfoc.jpg" />
    <Content Include="c\v2\img\holmes-foto-banner-JPG.jpg" />
    <Content Include="c\v2\img\holmes-foto-landing-JPG.jpg" />
    <Content Include="c\v2\img\icon-mapa-mundi-compress.png" />
    <Content Include="c\v2\img\icon-productos.png" />
    <Content Include="c\v2\img\icon-productos.svg" />
    <Content Include="c\v2\img\icon-soluciones_sherlock.svg" />
    <Content Include="c\v2\img\icons-landing-holmes.svg" />
    <Content Include="c\v2\img\icon_checked.svg" />
    <Content Include="c\v2\img\icon_warning.svg" />
    <Content Include="c\v2\img\img_user_empty.svg" />
    <Content Include="c\v2\img\iniciar-chat.svg" />
    <Content Include="c\v2\img\i_select.svg" />
    <Content Include="c\v2\img\landing\alpura.svg" />
    <Content Include="c\v2\img\landing\banner_promo_homeae-min.png" />
    <Content Include="c\v2\img\landing\bbva.svg" />
    <Content Include="c\v2\img\landing\bg-landing-AE.png" />
    <Content Include="c\v2\img\landing\cocacolafemsa.svg" />
    <Content Include="c\v2\img\landing\CT-big.svg" />
    <Content Include="c\v2\img\landing\forma-landing-AE-2.svg" />
    <Content Include="c\v2\img\landing\forma-landing-AE.svg" />
    <Content Include="c\v2\img\landing\grupomodelo.svg" />
    <Content Include="c\v2\img\landing\lala.svg" />
    <Content Include="c\v2\img\landing\landing-pre-registro.svg" />
    <Content Include="c\v2\img\landing\sprite-landing-AE.svg" />
    <Content Include="c\v2\img\landing\trio-landing-AE.png" />
    <Content Include="c\v2\img\loading.gif" />
    <Content Include="c\v2\img\logo-holmes.svg" />
    <Content Include="c\v2\img\logoct.svg" />
    <Content Include="c\v2\img\logoct_neg.svg" />
    <Content Include="c\v2\img\logoportal.svg" />
    <Content Include="c\v2\img\logoportalwhite.svg" />
    <Content Include="c\v2\img\logo_empty.svg" />
    <Content Include="c\v2\img\logo_servicio_empleo_v2.png" />
    <Content Include="c\v2\img\lupa.png" />
    <Content Include="c\v2\img\mails.svg" />
    <Content Include="c\v2\img\nophoto_1.jpg" />
    <Content Include="c\v2\img\nophoto_2.jpg" />
    <Content Include="c\v2\img\nophoto_3.jpg" />
    <Content Include="c\v2\img\numero_preguntas.jpg" />
    <Content Include="c\v2\img\organiza.svg" />
    <Content Include="c\v2\img\outlook.svg" />
    <Content Include="c\v2\img\p.gif" />
    <Content Include="c\v2\img\photo_news.svg" />
    <Content Include="c\v2\img\preguntas_filtrado.jpg" />
    <Content Include="c\v2\img\proceso-facil.jpg" />
    <Content Include="c\v2\img\puntuacion_preguntas.jpg" />
    <Content Include="c\v2\img\qr-ae.svg" />
    <Content Include="c\v2\img\rating_kq.svg" />
    <Content Include="c\v2\img\riesgo-desenfoc.jpg" />
    <Content Include="c\v2\img\salud.jpg" />
    <Content Include="c\v2\img\sGestCan.png" />
    <Content Include="c\v2\img\sherlockHR.png" />
    <Content Include="c\v2\img\sherlock_IA23_1.svg" />
    <Content Include="c\v2\img\sh_1.jpg" />
    <Content Include="c\v2\img\sh_2.jpg" />
    <Content Include="c\v2\img\sh_3.jpg" />
    <Content Include="c\v2\img\simbolos_home_emp_ar.png" />
    <Content Include="c\v2\img\simbolos_home_emp_bo.png" />
    <Content Include="c\v2\img\simbolos_home_emp_cl.png" />
    <Content Include="c\v2\img\simbolos_home_emp_co.png" />
    <Content Include="c\v2\img\simbolos_home_emp_cr.png" />
    <Content Include="c\v2\img\simbolos_home_emp_cu.png" />
    <Content Include="c\v2\img\simbolos_home_emp_do.png" />
    <Content Include="c\v2\img\simbolos_home_emp_ec.png" />
    <Content Include="c\v2\img\simbolos_home_emp_es.png" />
    <Content Include="c\v2\img\simbolos_home_emp_gt.png" />
    <Content Include="c\v2\img\simbolos_home_emp_hn.png" />
    <Content Include="c\v2\img\simbolos_home_emp_mx.png" />
    <Content Include="c\v2\img\simbolos_home_emp_ni.png" />
    <Content Include="c\v2\img\simbolos_home_emp_pa.png" />
    <Content Include="c\v2\img\simbolos_home_emp_pe.png" />
    <Content Include="c\v2\img\simbolos_home_emp_pr.png" />
    <Content Include="c\v2\img\simbolos_home_emp_py.png" />
    <Content Include="c\v2\img\simbolos_home_emp_sv.png" />
    <Content Include="c\v2\img\simbolos_home_emp_uy.png" />
    <Content Include="c\v2\img\simbolos_home_emp_ve.png" />
    <Content Include="c\v2\img\skype2.png" />
    <Content Include="c\v2\img\soporte-formacion.jpg" />
    <Content Include="c\v2\img\sprite.png" />
    <Content Include="c\v2\img\sprite.svg" />
    <Content Include="c\v2\img\sprite_banderas.png" />
    <Content Include="c\v2\img\sprite_basic.svg" />
    <Content Include="c\v2\img\sprite_card.png" />
    <Content Include="c\v2\img\sprite_landing_SH.svg" />
    <Content Include="c\v2\img\sprite_menu_lateral.svg" />
    <Content Include="c\v2\img\sprite_pago.svg" />
    <Content Include="c\v2\img\sprite_pem.svg" />
    <Content Include="c\v2\img\sprite_pem_min.svg" />
    <Content Include="c\v2\img\sprite_rrss.svg" />
    <Content Include="c\v2\img\sprite_test.png" />
    <Content Include="c\v2\img\sprite_tmp.png" />
    <Content Include="c\v2\img\sprite_val.png" />
    <Content Include="c\v2\img\stars.svg" />
    <Content Include="c\v2\img\stars_heart_rate.png" />
    <Content Include="c\v2\img\stars_heart_rate.svg" />
    <Content Include="c\v2\img\stars_rate.png" />
    <Content Include="c\v2\img\stars_rate.svg" />
    <Content Include="c\v2\img\tarjeta_cvv.png" />
    <Content Include="c\v2\img\telef.svg" />
    <Content Include="c\v2\img\triple_test.svg" />
    <Content Include="c\v2\img\user_empty.svg" />
    <Content Include="c\v2\img\vacaciones.jpg" />
    <Content Include="c\v2\img\val-promo-banner.jpg" />
    <Content Include="c\v2\img\val-promo-popup.jpg" />
    <Content Include="c\v2\img\ventajas.jpg" />
    <Content Include="c\v2\img\verifica.svg" />
    <Content Include="c\v2\img\verificacion-cuenta.svg" />
    <Content Include="c\v2\img\whatsapp2.png" />
    <Content Include="c\v2\img\whs.svg" />
    <Content Include="c\v2\img\chosen-sprite%402x.png" />
    <Content Include="c\v2\img\chosen-sprite.png" />
    <Content Include="Global.asax" />
    <Content Include="bundleconfig.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="App_Data\mysql-services\common\schemas\ct-comun\flyway_dev.conf" />
    <Content Include="App_Data\mysql-services\common\schemas\repo-settings\flyway_dev.conf" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocolaux\flyway_dev.conf" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocollctr\flyway_dev.conf" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocol\flyway_dev.conf" />
    <Content Include="App_Data\mysql-services\repos\schemas\companies\flyway_dev.conf" />
    <Content Include="App_Data\mysql-services\repos\schemas\offers\flyway_dev.conf" />
    <Content Include="App_Data\mysql-services\repos\schemas\offers_aux\flyway_dev.conf" />
    <Content Include="App_Data\mysql-services\repos\schemas\products\flyway_dev.conf" />
    <Content Include="App_Data\compareDB\compareDB.ps1" />
    <Content Include="App_Data\mysql-services\master\schemas\computrabajocoldata\flyway_dev.conf" />
    <Content Include="App_Data\mysql-services\repos\schemas\authenticate\flyway_dev.conf" />
    <Content Include="App_Data\mysql-services\repos\schemas\tracking\flyway_dev.conf" />
    <Content Include="App_Data\mysql-services\repos\schemas\ct_user\flyway_dev.conf" />
    <Content Include="App_Data\mysql-services\repos\schemas\repo_killer_questions\flyway_dev.conf" />
    <Content Include="App_Data\mysql-services\repos\schemas\ae\flyway_dev.conf" />
    <None Include="c\js\jquery-3.1.1.intellisense.js" />
    <Content Include="c\js\bootstrap.js" />
    <Content Include="c\js\bootstrap.min.js" />
    <Content Include="c\js\jquery-3.1.1.js" />
    <Content Include="c\js\jquery-3.1.1.min.js" />
    <Content Include="c\js\jquery-3.1.1.slim.js" />
    <Content Include="c\js\jquery-3.1.1.slim.min.js" />
    <Content Include="c\js\jquery-migrate-1.2.1.js" />
    <Content Include="c\js\jquery-migrate-1.2.1.min.js" />
    <Content Include="c\js\jquery-ui.js" />
    <Content Include="c\js\jquery-ui.min.js" />
    <Content Include="c\js\jquery.qtip.min.js" />
    <Content Include="c\js\jquery.unobtrusive-ajax.js" />
    <Content Include="c\js\jquery.unobtrusive-ajax.min.js" />
    <None Include="c\js\jquery.validate-vsdoc.js" />
    <Content Include="c\js\jquery.validate.js" />
    <Content Include="c\js\jquery.validate.min.js" />
    <Content Include="c\js\jquery.validate.unobtrusive.js" />
    <Content Include="c\js\jquery.validate.unobtrusive.min.js" />
    <Content Include="c\js\jquery.blockUI.js" />
    <Content Include="c\js\jquery.cookie.js" />
    <Content Include="Lib\CommonPaymentLibrary.dll" />
    <Content Include="Lib\Worldpay.Sdk.dll" />
    <Content Include="Lib\WorldPayNewtonsoft.Json.dll" />
    <Compile Include="Configuration\DictionariesApiConfiguration.cs" />
    <Compile Include="Configuration\KillerQuestionsApiConfiguration.cs" />
    <Compile Include="Configuration\RedarborCacheConfiguration.cs" />
    <Compile Include="Controllers\CompanyExportToPDFController.cs" />
    <Compile Include="Controllers\CompanyBBDDCvDetailController.cs" />
    <Compile Include="Controllers\CompanyConversationsController.cs" />
    <Compile Include="Controllers\CompanyProductTraceController.cs" />
    <Compile Include="Controllers\CompanyMatchCvDetailController.cs" />
    <Content Include="Readme.txt" />
    <Content Include="Views\CompanyMatches\DisplayTemplates\SaveFilterPopUp.cshtml" />
    <Content Include="Views\Shared\Partials\Configuration\_trackingActions.cshtml" />
    <Content Include="Views\Shared\DisplayTemplates\contentProductPackV2.cshtml" />
    <Content Include="Views\Shared\Partials\Company\Cart\_OfferProductPackCart.cshtml" />
    <Content Include="Views\Shared\DisplayTemplates\productContentGroupPopUp.cshtml" />
    <Content Include="Views\Shared\Partials\CompanyPage\Offers\_MPOffer.cshtml" />
    <Content Include="Views\Shared\Partials\Offer\_OfferListBoxes.cshtml" />
    <Content Include="Views\CompanyLanding\LandingOfferPublish.cshtml" />
    <Content Include="Views\Shared\Partials\Product\_ProductPackListHomePrivate.cshtml" />
    <Content Include="Views\Shared\Partials\Product\_ProductPackListHomePublic.cshtml" />
    <Content Include="Views\CompanyLanding\End2019.cshtml" />
    <Content Include="Views\Shared\Partials\Offer\_OfferListBoxes2019.cshtml" />
    <Content Include="Views\CompanyLanding\ConvertPacks.cshtml" />
    <Content Include="Views\Shared\EditorTemplates\CvsMultifilterTags.cshtml" />
    <Content Include="Views\CompanyMatches\EditorTemplates\MatchesFilters.cshtml" />
    <Content Include="Views\CompanyLanding\Welcome.cshtml" />
    <Content Include="Views\CompanyLanding\NotAllowed.cshtml" />
    <Content Include="c\v2\img\Thumbs.db" />
    <Content Include="Views\HealthCheck\status.cshtml" />
    <Content Include="Views\CompanyWorldPay\Index.cshtml" />
    <Content Include="Views\Shared\Layouts\_LayoutPayment.cshtml" />
    <Content Include="Views\Shared\DisplayTemplates\LandingProducts.cshtml" />
    <Content Include="Views\CompanyReports\Index.cshtml" />
    <Content Include="Views\Shared\Partials\Company\_CompanyChartReports.cshtml" />
    <Content Include="Views\CompanyInvoices\Index.cshtml" />
    <Content Include="Views\CompanyInvoices\Invoice.cshtml" />
    <Content Include="Views\CompanyAbuse\Index.cshtml" />
    <Content Include="Views\Shared\Layouts\_LayoutSimple.cshtml" />
    <Content Include="Views\Company\Sherlock.cshtml" />
    <Content Include="Views\Shared\DisplayTemplates\SaveFilterPopUp.cshtml" />
    <Content Include="Views\ServerStatus\Index.cshtml" />
    <Content Include="Views\Shared\Layouts\_LayoutMasterNewPem.cshtml" />
    <Content Include="Views\Shared\DisplayTemplates\PopUp.cshtml" />
    <Content Include="Views\Shared\DisplayTemplates\Pager.cshtml" />
    <Content Include="Views\Shared\EditorTemplates\DependingDropdownGroupModel.cshtml" />
    <Content Include="Views\Shared\EditorTemplates\DependingDropdownModel.cshtml" />
    <Content Include="Views\Shared\Partials\Offer\_KillerQuestionForm.cshtml" />
    <Content Include="Views\Shared\Partials\_VideoPopUp.cshtml" />
    <Content Include="Views\Shared\DisplayTemplates\notFeaturePopUp.cshtml" />
    <Content Include="Views\Contact\Holmes.cshtml" />
    <Content Include="Views\Shared\Partials\Match\_InfoCanNotViewMatches.cshtml" />
    <Content Include="Views\Shared\Partials\_SupportPopUp.cshtml" />
    <Content Include="Views\Shared\Partials\_SupportPopUpNewPem.cshtml" />
    <Content Include="Views\Shared\Partials\Configuration\_InfoExceededLimitUsers.cshtml" />
    <Content Include="Views\UnusualActivity\Index.cshtml" />
    <Content Include="Views\CompanyLanding\WelcomeMemPack.cshtml" />
    <Content Include="Views\Shared\Partials\Product\_LandingMemPackProducts.cshtml" />
    <Content Include="Views\Go\Index.cshtml" />
    <Content Include="Views\DeviceAuthentication\CompanyBlocked.cshtml" />
    <Content Include="Views\DeviceAuthentication\DeviceBlocked.cshtml" />
    <Content Include="Views\DeviceAuthentication\Index.cshtml" />
    <Content Include="Views\Shared\Layouts\_LayoutDeviceVerification.cshtml" />
    <Content Include="Views\Shared\Layouts\_LayoutBlocked.cshtml" />
    <Content Include="Views\Shared\DisplayTemplates\contentLandingProductPack.cshtml" />
    <Content Include="Views\Shared\DisplayTemplates\contentLandingProducts.cshtml" />
    <Content Include="Views\Shared\DisplayTemplates\contentLandingProductGroupPopUp.cshtml" />
    <Content Include="Views\Shared\DisplayTemplates\SendEmail.cshtml" />
    <Content Include="Views\Shared\DisplayTemplates\ConvertToCompletePopUp.cshtml" />
    <Content Include="Views\CompanyCartMessagesPayment\PaymentOK.cshtml" />
    <Content Include="Views\CompanyCartMessagesPayment\PaymentKO.cshtml" />
    <Content Include="Views\CompanyConfigurationDevices\Index.cshtml" />
    <Content Include="Views\CompanyConfigurationDevices\DisplayTemplates\DeviceAuthorizationViewModel.cshtml" />
    <Content Include="Views\Register\simplifiedRegister.Mobile.cshtml" />
    <Content Include="Views\Register\simplifiedRegister.cshtml" />
    <Content Include="Views\Shared\Layouts\_LayoutRegisterMobile.cshtml" />
    <Content Include="Views\Shared\Partials\_FooterSimpleMobile.cshtml" />
    <Content Include="Views\Shared\Layouts\_LayoutRegister.cshtml" />
    <Content Include="Views\Shared\Partials\_FooterSimple.cshtml" />
    <Content Include="Views\RecoverPassword\Index.Mobile.cshtml" />
    <Content Include="Views\CompanyOffersPostPublish\CantPublish.cshtml" />
    <Content Include="Views\CompanyFeaturesPostPublish\index.cshtml" />
    <Content Include="Views\CompanyFeaturesPostPublish\Published.cshtml" />
    <Content Include="Views\Shared\Partials\Published\_ConfirmationPublished.cshtml" />
    <Content Include="Views\Shared\Partials\Published\_ConfirmationPublishedVoucher.cshtml" />
    <Content Include="Views\Shared\Partials\_CookiesConsent.cshtml" />
    <Content Include="Views\Shared\Partials\Published\_ConfirmationPublished.Mobile.cshtml" />
    <Content Include="Views\CompanyFeaturesPostPublish\Published.Mobile.cshtml" />
    <Content Include="Views\Shared\Layouts\_LayoutMasterMobile.cshtml" />
    <Content Include="Views\Shared\Partials\Published\_ConfirmationPublishedVoucher.Mobile.cshtml" />
    <Content Include="Views\CompanyOffersPublish\Published.Mobile.cshtml" />
    <Content Include="Views\Shared\Partials\_NewLogin.cshtml" />
    <Content Include="Views\Shared\Partials\_NewFooter.cshtml" />
    <Content Include="Views\Home\ProductsAndServices.cshtml" />
    <Content Include="Views\Home\About.cshtml" />
    <Content Include="Views\Shared\Layouts\_LayoutHome.cshtml" />
    <Content Include="Views\Home\PublicHome.cshtml" />
    <Content Include="Views\Shared\Partials\Home\_ProductsHomeMultipack.cshtml" />
    <Content Include="Views\Shared\Partials\Product\_ProductContentFeatures.cshtml" />
    <Content Include="Views\Shared\Partials\Home\_ProductsHome.cshtml" />
    <Content Include="Views\Shared\Partials\_MobileMenu.cshtml" />
    <Content Include="Views\Home\PublicHome.Mobile.cshtml" />
    <Content Include="Views\Shared\Layouts\_LayoutHomeMobile.cshtml" />
    <Content Include="Views\Home\About.Mobile.cshtml" />
    <Content Include="Views\Home\ProductsAndServices.Mobile.cshtml" />
    <Content Include="Views\Shared\Partials\Home\_ProductsHome.Mobile.cshtml" />
    <Content Include="Views\Shared\Partials\Home\_ProductsHomeMultipack.Mobile.cshtml" />
    <Content Include="Views\Shared\Partials\Product\_ProductContentFeatures.Mobile.cshtml" />
    <Content Include="Views\CompanyPostPublishStepTwo\index.cshtml" />
    <Content Include="Views\CompanyOffersPostPublish\Index.cshtml" />
    <Content Include="Views\Shared\DisplayTemplates\PopUp.Mobile.cshtml" />
    <Content Include="Views\Shared\Partials\Offer\_KillerQuestionFormNew.cshtml" />
    <Content Include="Views\Shared\Partials\Offer\_KillerQuestionsNew.cshtml" />
    <Content Include="Views\Shared\Partials\Offer\_KillerQuestionDataNew.cshtml" />
    <Content Include="Views\Shared\Partials\Menus\_MenuNewPem.cshtml" />
    <Content Include="Views\CompanyPostPublishStepTwo\Published.cshtml" />
    <Content Include="Views\CompanyPostPublishStepTwo\Published.Mobile.cshtml" />
    <Content Include="Views\Shared\Partials\Company\Cart\_PostPublishProductsCart.cshtml" />
    <Content Include="Views\Shared\Partials\Home\_SecondaryMenu.cshtml" />
    <Content Include="Views\Shared\Partials\_Header.cshtml" />
    <Content Include="Views\MultiPurchaseCart\Index.cshtml" />
    <Content Include="Views\MultiPurchaseCartMessages\Index.cshtml" />
    <Content Include="Views\MultiPurchaseCartMessages\WPView.cshtml" />
    <Content Include="Views\PayPalMultiPurchase\Index.cshtml" />
    <Content Include="Views\Shared\Partials\Company\Cart\_MultiPurchaseCartProducts.cshtml" />
    <Content Include="Views\Shared\Partials\Company\Cart\_MultiPurchaseEditCompanyData.cshtml" />
    <Content Include="Views\CandidateComparator\Index.cshtml" />
    <Content Include="Views\Shared\Layouts\_LayoutPaymentNewPem.cshtml" />
    <Content Include="Views\Shared\Partials\Company\Cart\_MultiPurchasePayMethod.cshtml" />
    <Content Include="Views\Shared\Partials\_GTMandAnalytics.cshtml" />
    <Content Include="Views\Company\MembershipHome.cshtml" />
    <Content Include="Views\Shared\Partials\CompanyPage\Offers\_MembershipOffer.cshtml" />
    <Content Include="Views\Shared\Partials\CompanyPage\_MembershipHomeVision.cshtml" />
    <Content Include="Views\Shared\Partials\CompanyPage\Products\_MultiPacks.cshtml" />
    <Content Include="Views\Shared\DisplayTemplates\compareProductsPopUp.cshtml" />
    <Content Include="Views\Shared\Partials\_AlertNewPem.cshtml" />
    <Content Include="Views\Shared\Partials\CompanyPage\_RenewMembership.cshtml" />
    <Content Include="Views\Shared\Partials\_PopUpConvertToCompleteComp.cshtml" />
    <Content Include="Views\Shared\Partials\_PopUpConvertToCompleteCompOLD.cshtml" />
    <Content Include="Views\Shared\Partials\CompanyPage\Products\_AvailableUnitsPurchased.cshtml" />
    <Content Include="Views\Shared\Partials\CompanyPage\Products\_SlimProducts.cshtml" />
    <Content Include="Views\Shared\Partials\CompanyPage\Products\_Packs.cshtml" />
    <Content Include="Views\Shared\Partials\CompanyPage\_CompanyResourcesBanner.cshtml" />
    <Content Include="Views\Shared\DisplayTemplates\defaultPagerNewPem.cshtml" />
    <Content Include="Views\Shared\Layouts\_LayoutSimpleNewPem.cshtml" />
    <Content Include="Views\Shared\Layouts\_LayoutCompanyNewPem.cshtml" />
    <Content Include="Views\Shared\Partials\PopupVerifyCode\_PopupVerifyCode.cshtml" />
    <Content Include="Views\Shared\Partials\PopupVerifyCode\_PopupVerifyCode.Script.cshtml" />
    <Content Include="Views\Shared\Partials\PopUps\_ActionNotAllowedPopUp.cshtml" />
    <Content Include="Views\Shared\Partials\PopUps\_DeleteOfferPopUp.cshtml" />
    <Content Include="Views\Shared\Partials\PopUps\_InfoSelectAnyOfferPopUp.cshtml" />
    <Content Include="Views\Shared\Partials\PopUps\_FeatureActionPopUp.cshtml" />
    <Content Include="Views\Shared\Partials\_ContactButtons.cshtml" />
    <Content Include="Views\CompanyCartMessagesPayment\PaymentReasonsKO.cshtml" />
    <Content Include="Views\Shared\Partials\PopUps\_DeleteChatPopUp.cshtml" />
    <Content Include="Views\CompanyMatchCvDetail\Index.cshtml" />
    <Content Include="Views\CompanyBBDDCvDetail\Index.cshtml" />
    <Content Include="Views\Shared\Partials\PopUps\_AddCVCustomFolderPopUp.cshtml" />
    <Content Include="Views\Shared\Partials\PopUps\_EditCVCustomFolderPopUp.cshtml" />
    <Content Include="Views\Shared\Partials\PopUps\_DelCVCustomFolderPopUp.cshtml" />
    <Content Include="Views\Shared\Partials\PopUps\_DeleteRatingPopUp.cshtml" />
    <Content Include="Views\Shared\Partials\PopUps\_DeleteCVCommentPopUp.cshtml" />
    <Content Include="Views\Shared\Partials\PopUps\_ChatInfoAlertPopUp.cshtml" />
    <Content Include="Views\CompanyMatchCvDetail\Print.cshtml" />
    <Content Include="Views\Shared\Partials\PopUps\_DiscardCVMatchPopUp.cshtml" />
    <Content Include="Views\Shared\Partials\PopUps\_DeleteCVMatchPopUp.cshtml" />
    <Content Include="Views\Shared\Partials\PopUps\_BBDDAddCVCustomFolderPopUp.cshtml" />
    <Content Include="Views\Shared\Partials\PopUps\_BBDDEditCVCustomFolderPopUp.cshtml" />
    <Content Include="Views\Shared\Partials\PopUps\_BBDDDelCVCustomFolderPopUp.cshtml" />
    <Content Include="Views\Shared\DisplayTemplates\defaultPopUpNewPem.cshtml" />
    <Content Include="Views\Shared\DisplayTemplates\notFeaturePopUpNewPem.cshtml" />
    <Content Include="Views\Shared\Partials\Contact\_ComercialRequestPopUp.cshtml" />
    <Content Include="Views\Shared\Partials\PopUps\_UserNotCreditAvailablePopUp.cshtml" />
    <Content Include="Views\Shared\Partials\PopUps\_DeleteCVBBDDPopUp.cshtml" />
    <Content Include="Views\Shared\Partials\PopUps\_maxCvFolderPopUp.cshtml" />
    <Content Include="Views\Shared\Partials\PopUps\_SelectAProductMultiCartPopUp.cshtml" />
    <Content Include="Views\Shared\Partials\PopUps\_DeleteFilterExclusionMatchesPopUp.cshtml" />
    <Content Include="Views\Shared\Partials\PopUps\_CandidatePhotoReportedPopUp.cshtml" />
    <Content Include="Views\Register\SimplifiedValidatePage.cshtml" />
    <Content Include="Views\Shared\Partials\PopUps\_RecoverPasswordPopUp.cshtml" />
    <Content Include="Views\Shared\Partials\SimplifiedValidatePage\_LoginContainer.cshtml" />
    <Content Include="Views\Shared\Partials\SimplifiedValidatePage\_PasswordContainer.cshtml" />
    <Content Include="Views\Shared\Partials\SimplifiedValidatePage\_RecoverPasswordContainer.cshtml" />
    <Content Include="Views\Shared\Partials\SimplifiedValidatePage\_RegisterContainer.cshtml" />
    <Content Include="Views\Shared\Partials\_FooterSimpleBlack.cshtml" />
    <Content Include="Views\Shared\Partials\PopUps\_ContactNewLoginPopUp.cshtml" />
    <Content Include="Views\Shared\Partials\_PopUpConvertToCompleteCompFromBasic.cshtml" />
    <Content Include="Views\CompanyOfferAI\Index.cshtml" />
    <Content Include="Views\Shared\Partials\Boxes\_OfferAIGenerationBox.cshtml" />
    <Content Include="Views\CompanyOfferAI\Selection.cshtml" />
    <Content Include="Views\CompanyProductTrace\Index.cshtml" />
    <Content Include="Views\Register2\simplifiedRegister.cshtml" />
    <Content Include="Views\Register2\SimplifiedValidatePage.cshtml" />
    <Content Include="Views\Shared\Partials\SimplifiedValidatePage\_RegisterContainer2.cshtml" />
    <Content Include="Views\Shared\Partials\QRCode\_CompanyQRCodeCard.cshtml" />
    <Content Include="Views\Register2\simplifiedRegister.Mobile.cshtml" />
    <Content Include="Views\Shared\Partials\_PopUpAddGeolocalizationOffer.cshtml" />
    <Content Include="Views\Shared\Partials\Published\_ConfirmationBasicPublished.cshtml" />
    <Content Include="Views\Shared\Partials\_Register.cshtml" />
    <Content Include="Views\Register2\InfoRegister.cshtml" />
    <Content Include="Views\Register2\InfoRegister.Mobile.cshtml" />
    <Content Include="Views\Shared\Partials\Home\_CompanyDescriptionPopup.cshtml" />
    <Content Include="Views\Shared\Partials\_RegisterMobile.cshtml" />
    <Content Include="Views\Register2\InfoRegisterOld.cshtml" />
    <Content Include="Views\Register2\InfoRegisterOld.Mobile.cshtml" />
    <Content Include="Views\PreRegister\simplifiedPreRegister.cshtml" />
    <Content Include="Views\Shared\Partials\PreRegister\_PreRegister.cshtml" />
    <Content Include="Views\Shared\Partials\PreRegister\_PreRegisterMobile.cshtml" />
    <Content Include="Views\CompanyCvs\EditorTemplates\BBDDManageFolders.cshtml" />
    <Content Include="Views\Shared\Partials\PopUps\_BBDDCvProdPopUp.cshtml" />
    <Content Include="Views\Shared\Partials\PopUps\_UnblockBBDDCvPopUp.cshtml" />
    <Content Include="Views\PreRegister\simplifiedPreRegister.Mobile.cshtml" />
    <Content Include="Views\CompanyConversations\Index.cshtml" />
    <Content Include="Views\Shared\Partials\Conversation\_MessageConversationHeader.cshtml" />
    <Content Include="Views\CompanyConversations\Index.Mobile.cshtml" />
    <Content Include="Views\Shared\Partials\Conversation\_ConversationBoxMessages.Mobile.cshtml" />
    <Content Include="Views\Shared\Partials\Conversation\_ConversationMessages.Mobile.cshtml" />
    <Content Include="c\v2\css\fonts\CTFont_xs.woff2" />
    <Content Include="Views\Shared\Partials\Conversation\_ConversationBoxMessages.cshtml" />
    <Content Include="Views\Shared\Partials\Conversation\_ConversationMessages.cshtml" />
    <Content Include="Views\Shared\Partials\Conversation\_ConversationOffersSection.Mobile.cshtml" />
    <Content Include="Views\Shared\Partials\Conversation\_ConversationOffers.cshtml" />
    <Content Include="Views\Shared\Layouts\_LayoutChatMobile.cshtml" />
    <Content Include="Views\Shared\Partials\Conversation\_ConversationCandidatesSection.Mobile.cshtml" />
    <Content Include="Views\Shared\Partials\Conversation\_ConversationCandidates.Mobile.cshtml" />
    <Content Include="Views\Shared\Partials\Conversation\_ConversationOffers.Mobile.cshtml" />
    <Content Include="Views\Shared\Partials\Conversation\_ConversationCandidatesByOfferSection.Mobile.cshtml" />
    <Content Include="Views\Shared\Partials\Conversation\_ConversationCandidatesByOffer.Mobile.cshtml" />
    <Content Include="Views\Shared\Partials\Conversation\_ConversationOffersSection.cshtml" />
    <Content Include="Views\Shared\Partials\Conversation\_ConversationCandidatesSection.cshtml" />
    <Content Include="Views\Shared\Partials\Conversation\_ConversationCandidates.cshtml" />
    <Content Include="Views\Shared\Partials\Conversation\_ConversationCandidatesByOfferSection.cshtml" />
    <Content Include="Views\Shared\Partials\Conversation\_ConversationCandidatesByOffer.cshtml" />
    <Content Include="Views\Shared\Partials\PopUps\Conversation\_ConversationBoxMessages.cshtml" />
    <Content Include="Views\Shared\Partials\PopUps\Conversation\_ConversationMessages.cshtml" />
    <Content Include="Views\Shared\Partials\PopUps\Conversation\_LimitConversationMatchDetailPopUp.cshtml" />
    <Content Include="Views\Shared\Partials\PopUps\Conversation\_OpenConversationMatchDetailPopUp.cshtml" />
    <Content Include="Views\Shared\Partials\Menus\_NewMenuMobile.cshtml" />
    <Content Include="Views\Shared\Partials\_HeaderMobile.cshtml" />
    <Content Include="Views\Company\MembershipHome.Mobile.cshtml" />
    <Content Include="Views\Shared\Partials\Menus\_FooterMenuMobile.cshtml" />
    <Content Include="Views\Shared\Partials\Menus\_MobileSubMenu.cshtml" />
    <Content Include="Views\CompanyCvs\Index.Mobile.cshtml" />
    <Content Include="Views\CompanyCvs\EditorTemplates\CompanyListCvsMembershipMobile.cshtml" />
    <Content Include="Views\Shared\Partials\Candidate\_CandidateListItemMobile.cshtml" />
    <Content Include="Views\CompanyCvs\EditorTemplates\BBDDManageFoldersMobile.cshtml" />
    <Content Include="Views\Shared\Partials\PopUps\_BBDDcvPopup.cshtml" />
    <Content Include="Views\Shared\Partials\Conversation\_ConversationBoxMessagesByPage.cshtml" />
    <Content Include="Views\Shared\Partials\Conversation\_ConversationBoxMessagesByPage.Mobile.cshtml" />
    <Content Include="Views\CompanyVisionSubMenu\Menu.Mobile.cshtml" />
    <Content Include="Views\CompanyReviewsSubMenu\Menu.Mobile.cshtml" />
    <Content Include="Views\CompanyMatchCvDetail\Index.Mobile.cshtml" />
    <Content Include="Views\Shared\Partials\CompanyPage\Offers\_MembershipOfferMobile.cshtml" />
    <Content Include="Views\Shared\Partials\CompanyPage\Offers\_MPOfferMobile.cshtml" />
    <Content Include="Views\Shared\Partials\CompanyPage\Products\_PacksMobile.cshtml" />
    <Content Include="Views\Shared\Layouts\_LayoutSimpleMobile.cshtml" />
    <Content Include="Views\Shared\Partials\PopUpsMobile\_DeleteRatingPopUpMobile.cshtml" />
    <Content Include="Views\Shared\Partials\PopUpsMobile\_DeleteCVCommentPopUpMobile.cshtml" />
    <Content Include="Views\Shared\Partials\PopUpsMobile\_ViewDetailPackPopUpMobile.cshtml" />
    <Content Include="Views\Shared\Layouts\_LayoutMasterMobilePemMin.cshtml" />
    <Content Include="Views\MiArea\Index.Mobile.cshtml" />
    <Content Include="Views\Shared\Partials\Home\_CompanyDescriptionPopupMobile.cshtml" />
    <Content Include="Views\Shared\Partials\CompanyPage\_RenewMembershipMobile.cshtml" />
    <Content Include="Views\Shared\Partials\_ContactButtonsMobile.cshtml" />
    <Content Include="Views\Shared\Partials\PopUpsMobile\_ChatInfoAlertPopUpMobile.cshtml" />
    <Content Include="Views\CompanyReportsSubMenu\Menu.Mobile.cshtml" />
    <Content Include="Views\Shared\Partials\_PopUpConvertToCompleteCompMobile.cshtml" />
    <Content Include="Views\Shared\Partials\_PopUpConvertToCompleteCompFromBasicMobile.cshtml" />
    <Content Include="Views\Shared\Partials\PopUpsMobile\_atencionComercial.Mobile.cshtml" />
    <Content Include="Views\CompanyLanding\Welcome.Mobile.cshtml" />
    <Content Include="Views\CompanyBBDDCvDetail\Index.Mobile.cshtml" />
    <Content Include="Views\Shared\Partials\PopUpsMobile\_UserNotCreditAvailablePopUpMobile.cshtml" />
    <Content Include="Views\Shared\Partials\PopUpsMobile\_UnblockBBDDCvPopUpMobile.cshtml" />
    <Content Include="Views\Shared\Partials\PopUpsMobile\_BBDDCvProdPopUpMobile.cshtml" />
    <Content Include="Views\Shared\Partials\PopUpsMobile\_maxCvFolderPopUpMobile.cshtml" />
    <Content Include="Views\Shared\Partials\PopUpsMobile\_popupNotViewCvMobile.cshtml" />
    <Content Include="Views\Shared\Partials\PopUpsMobile\_popupNotAllCvMobile.cshtml" />
    <Content Include="Views\Shared\Partials\PopUpsMobile\_popupAdvertMobile.cshtml" />
    <Content Include="Views\CompanyMatches\Index.Mobile.cshtml" />
    <Content Include="Views\Shared\Partials\Match\_MatchesMobile.cshtml" />
    <Content Include="Views\CompanyMatches\EditorTemplates\CompanyMatchesMembershipPackMobile.cshtml" />
    <Content Include="Views\Shared\Partials\Cv\_OffersListCvsMobile.cshtml" />
    <Content Include="Views\Shared\Partials\PopUpsMobile\_PopupVerifyCodeMobile.cshtml" />
    <Content Include="Views\CompanyDefaultCvs\Index.Mobile.cshtml" />
    <Content Include="Views\Contact\Index.Mobile.cshtml" />
    <Content Include="Views\CompanyOffers\Index.Mobile.cshtml" />
    <Content Include="Views\Shared\Partials\Home\_CompanyListOffersMobile.cshtml" />
    <Content Include="Views\Shared\Partials\Offer\_OfferRow.cshtml" />
    <Content Include="Views\CompanyVisionBenefits\Index.Mobile.cshtml" />
    <Content Include="Views\Shared\Partials\Match\_CompanyMatchesBasicMobile.cshtml" />
    <Content Include="Views\Shared\Partials\Match\_InfoCanNotViewMatchesMobile.cshtml" />
    <Content Include="Views\CompanyMatches\EditorTemplates\MatchesFiltersMobile.cshtml" />
    <Content Include="Views\Shared\Partials\PopUpsMobile\_ActionNotAllowedPopUpMobile.cshtml" />
    <Content Include="Views\Shared\Partials\PopUpsMobile\_DeleteOfferPopUpMobile.cshtml" />
    <Content Include="Views\Shared\Partials\PopUpsMobile\_InfoSelectAnyOfferPopUpMobile.cshtml" />
    <Content Include="Views\Shared\Partials\PopUpsMobile\_FeatureActionPopUpMobile.cshtml" />
    <Content Include="Views\Shared\Partials\PopUpsMobile\_UserNotCreditAvailablePopUp.cshtml" />
    <Content Include="Views\Shared\Partials\_AlertNewPemMobile.cshtml" />
    <Content Include="Views\Shared\Partials\PopUps\Conversation\_ConversationBoxMessagesByPage.cshtml" />
    <Content Include="Views\Shared\Partials\Vision\_VisionNewsItemMobile.cshtml" />
    <Content Include="Views\Shared\Partials\PopUpsMobile\_ErrorPopUpUrlMobile.cshtml" />
    <Content Include="Views\Shared\Partials\PopUpsMobile\_SendValidateUserMailPopUpMobile.cshtml" />
    <Content Include="Views\Shared\Partials\PopUpsMobile\_DeleteNewsPopUpMobile.cshtml" />
    <Content Include="Views\Shared\Partials\PopUpsMobile\_ErrorUrlNewsPopUpMobile.cshtml" />
    <Content Include="Views\Shared\Partials\PopUpsMobile\_ErrorSizePopUpMobile.cshtml" />
    <Content Include="Views\Shared\Partials\PopUpsMobile\_ErrorDimensionPopUpMobile.cshtml" />
    <Content Include="Views\Shared\Partials\Vision\_VisionAwardsItemMobile.cshtml" />
    <Content Include="Views\Shared\Partials\_PopUpAddGeolocalizationOfferMobile.cshtml" />
    <Content Include="Views\Shared\Partials\Configuration\_UserEditOrInsert.Mobile.cshtml" />
    <Content Include="Views\Shared\Partials\PopUps\_DeleteChatPopUpMobile.cshtml" />
    <Content Include="Views\Shared\Partials\Vision\_VisionCommentItemMobile.cshtml" />
    <Content Include="Views\Shared\Partials\Vision\_VisionInterviewCommentItemMobile.cshtml" />
    <Content Include="Views\Shared\Partials\_VideoPopUpMobile.cshtml" />
    <Content Include="Views\Shared\Partials\PopUps\_VideoPresentationPopup.cshtml" />
    <Content Include="Views\Shared\Partials\PopUpsMobile\_VideoPresentationPopupMobile.cshtml" />
    <Content Include="Views\Shared\Partials\PopUps\_BBDDAtencionComercialCVsPopupMobile.cshtml" />
    <Content Include="Views\Shared\Partials\Contact\_ComercialRequestPopUpOldPem.cshtml" />
    <Content Include="Views\Shared\Partials\PopUps\_BBDDcvPopupMobile.cshtml" />
    <Content Include="Views\Shared\Partials\Company\Cart\_MultiPurchaseCartProductsPrime.cshtml" />
    <Content Include="Views\Shared\Partials\PopUpsMobile\_PrimePopupMobile.cshtml" />
    <Content Include="Views\Shared\Partials\Published\_ConfirmationBasicPublished.Mobile.cshtml" />
    <Content Include="Views\Shared\Partials\Contact\_ComercialRequestPopUpOldPem.cshtml" />
    <Content Include="Views\Shared\Partials\PopUpsMobile\_BannerHomeAEMobile.cshtml" />
    <Content Include="Views\Shared\Partials\PopUps\_BannerHomeAE.cshtml" />
    <Content Include="Views\Shared\Partials\PopUpsMobile\_AddSkillPopUpMobile.cshtml" />
    <Content Include="Views\Shared\Partials\PopUpsMobile\_PopUpInfoOfertaDestacada.cshtml" />
    <Content Include="Views\Shared\Partials\PopUpsMobile\_PopUpInfoOfertaUrgente.cshtml" />
    <Content Include="Views\Shared\Partials\PopUpsMobile\_PopUpInfoOfertaFlash.cshtml" />
    <Content Include="Views\Shared\Partials\PopUpsMobile\_PopUpInfoOcultarEmpresa.cshtml" />
    <Content Include="Views\Shared\Partials\PopUpsMobile\_PopUpLanguages.cshtml" />
    <Content Include="Views\Shared\Partials\PopUpsMobile\_KillerQuestionFormNewMobile.cshtml" />
    <Content Include="Views\Shared\Partials\PopUpsMobile\_AddPopUpInfoKQ.cshtml" />
    <Content Include="Views\Shared\Partials\Offer\_KillerQuestionsNewMobile.cshtml" />
    <Content Include="Views\Shared\Partials\Offer\_KillerQuestionDataNewMobile.cshtml" />
    <Content Include="Views\Shared\Partials\PopUpsMobile\_PopUpInfoCvs.cshtml" />
    <Content Include="Views\Shared\Partials\PopUpsMobile\_PopUpConvertToComplete.cshtml" />
    <Content Include="Views\Shared\Partials\PopUpsMobile\_PopPublishOffer.cshtml" />
    <Content Include="Views\Shared\Partials\PopUpsMobile\_PopUpLanguagesPostPublish.cshtml" />
    <Content Include="Views\PreRegister\ImportConsent.cshtml" />
    <Content Include="Views\PreRegister\ImportOffers.cshtml" />
    <Content Include="Views\Shared\Partials\PreRegister\_ImportOffersTable.cshtml" />
    <Content Include="Views\Shared\Partials\PreRegister\_ImportOfferRow.cshtml" />
    <Content Include="Views\Shared\Partials\PreRegister\_PopUpImportOfferDetail.cshtml" />
    <Content Include="Views\Shared\Partials\PreRegister\_PopUpImportEndMessage.cshtml" />
    <Content Include="Views\Shared\Partials\PreRegister\_PopUpImportOfferValidateDetail.cshtml" />
    <Content Include="Views\Shared\Partials\PopUpsMobile\_ErrorPopUpAlertsMobile.cshtml" />
    <Content Include="Views\Shared\Partials\PopUps\_ErrorPopUpAlerts.cshtml" />
    <Content Include="Views\Shared\Partials\PopUpsMobile\_PopUpInfoExpressOffer.cshtml" />
    <Content Include="Views\Company\FreemiumHomeV2.cshtml" />
    <Content Include="Views\Shared\Partials\CompanyPage\Products\_MultiPacksV2.cshtml" />
    <Content Include="Views\CompanyCvs\IndexNewPem.cshtml" />
    <Content Include="Views\CompanyCvs\IndexNewPem.Mobile.cshtml" />
    <Content Include="Views\Company\FreemiumHomeV2.Mobile.cshtml" />
    <Content Include="Views\RecoverEmailNit\Index.cshtml" />
    <Content Include="Views\RecoverEmailNit\RecoverEmail.cshtml" />
    <Content Include="Views\RecoverEmailNit\Index.Mobile.cshtml" />
    <Content Include="Views\RecoverEmailNit\RecoverEmail.Mobile.cshtml" />
    <Content Include="Views\CompanyCvs\EditorTemplates\CompanyCvsFilters.cshtml" />
    <Content Include="Views\CompanyCvs\EditorTemplates\BBDDManageFoldersNewPem.cshtml" />
    <Content Include="Views\Shared\EditorTemplates\CvsMultifilterTagsNewPem.cshtml" />
    <Content Include="Views\Shared\Partials\Candidate\_CandidateListItemNewPem.cshtml" />
    <Content Include="Views\Shared\Partials\PopUps\_GenericInfoPopUp.cshtml" />
    <Content Include="Views\Shared\Partials\PopUps\_DeleteCVsFilter.cshtml" />
    <Content Include="Views\Shared\Partials\PopUps\_CreateCVsFilterPopUp.cshtml" />
    <Content Include="Views\CompanyOffersPublish\CantPublish.Mobile.cshtml" />
    <Content Include="Views\Contact\NoTokens.cshtml" />
    <Content Include="Views\Contact\NoTokens.Mobile.cshtml" />
    <Content Include="Views\Company\SubscriptionHome.cshtml" />
    <Content Include="Views\Shared\Partials\PopUps\_CTHROnboardingPopUp.cshtml" />
    <Content Include="Views\Shared\Partials\PopUps\_DisableMatchesOfferMailPopUp.cshtml" />
    <Content Include="Views\Shared\Partials\PopUps\_DisableMatchesOfferMailPopUpMobile.cshtml" />
    <Content Include="Views\Shared\Partials\PopUpsMobile\_PopUpShare.cshtml" />
    <Content Include="Views\Shared\Partials\Company\Cart\_MultiPurchaseCartProductsNew.cshtml" />
    <Content Include="Views\Shared\Partials\Company\Cart\_MultiPurchaseEditCompanyDataNew.cshtml" />
    <Content Include="Views\MultiPurchaseCart\IndexNew.cshtml" />
    <Content Include="Views\Shared\Partials\Candidate\_CandidateListItemB.cshtml" />
    <Content Include="Views\Shared\Partials\Candidate\_CandidateListItemBMobile.cshtml" />
    <Content Include="Views\Shared\Partials\_PopUpConvertToEssential.cshtml" />
    <Content Include="Views\Shared\Partials\_PopUpConvertToEssentialMobile.cshtml" />
    <Content Include="Views\Shared\Partials\Candidate\_CandidateListItemBNewPem.cshtml" />
    <Content Include="Views\Shared\Partials\_VideoPopUpColombia.cshtml" />
    <Content Include="Views\Shared\Partials\_VideoPopUpColombiaMobile.cshtml" />
    <Content Include="Views\Shared\Partials\PopUps\_CSATSurvey.cshtml" />
    <Content Include="Views\Shared\Partials\PopUpsMobile\_CSATSurveyMobile.cshtml" />
    <None Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
    <None Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
    <Content Include="Web.STAGING.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Web.BETA.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Web.PRODUCTION.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="App_Start\BundleConfig.cs" />
    <Compile Include="App_Start\RouteConfig.cs" />
    <Compile Include="Configuration\DeviceAuthenticateConfiguration.cs" />
    <Compile Include="Configuration\GlobalConfigurationConfiguration.cs" />
    <Compile Include="Configuration\RedarborKpiConsumerConfiguration.cs" />
    <Compile Include="Controllers\BlockingPageController.cs" />
    <Compile Include="Controllers\CompanyAbuseController.cs" />
    <Compile Include="Controllers\CompanyCartFinalizePurchaseController.cs" />
    <Compile Include="Controllers\CompanyCartMessagesPaymentController.cs" />
    <Compile Include="Controllers\CompanyConfigurationDevicesController.cs" />
    <Compile Include="Controllers\CompanyConfigurationEditCreditsController.cs" />
    <Compile Include="Controllers\CompanyFeaturesPostPublishController.cs" />
    <Compile Include="Controllers\CompanyInvoicesController.cs" />
    <Compile Include="Controllers\CompanyMatchesExclusionController.cs" />
    <Compile Include="Controllers\CompanyCartTransferPaymentController.cs" />
    <Compile Include="Controllers\CompanyCommentsCvController.cs" />
    <Compile Include="Controllers\CompanyOfferAIController.cs" />
    <Compile Include="Controllers\CompanyOffersPostPublishController.cs" />
    <Compile Include="Controllers\CompanyPostPublishStepTwoController.cs" />
    <Compile Include="Controllers\CompanyRatingsCvController.cs" />
    <Compile Include="Controllers\CompanyReportsController.cs" />
    <Compile Include="Controllers\CompanyReportsSubMenuController.cs" />
    <Compile Include="Controllers\CompanyReviewsSubMenuController.cs" />
    <Compile Include="Controllers\CompanyVisionInterviewsCommentsController.cs" />
    <Compile Include="Controllers\CompanyConfigurationSubMenuController.cs" />
    <Compile Include="Controllers\CompanyVisionSubMenuController.cs" />
    <Compile Include="Controllers\CompanyWorldPayController.cs" />
    <Compile Include="Controllers\DeviceAuthenticationController.cs" />
    <Compile Include="Controllers\GoController.cs" />
    <Compile Include="Controllers\GoogleTagManagerController.cs" />
    <Compile Include="Controllers\HealthCheckController.cs" />
    <Compile Include="Controllers\HolmesContactController.cs" />
    <Compile Include="Controllers\HomeLegacyController.cs" />
    <Compile Include="Controllers\LandingController.cs" />
    <Compile Include="Controllers\LoginLegacyController.cs" />
    <Compile Include="Controllers\MiAreaController.cs" />
    <Compile Include="Controllers\MobileSubMenuControllerBase.cs" />
    <Compile Include="Controllers\MultiPurchaseCartController.cs" />
    <Compile Include="Controllers\MultiPurchaseCartMessagesController.cs" />
    <Compile Include="Controllers\CandidateComparatorController.cs" />
    <Compile Include="Controllers\PasswordController.cs" />
    <Compile Include="Controllers\LogoutController.cs" />
    <Compile Include="Controllers\PayPalMultiPurchaseController.cs" />
    <Compile Include="Controllers\PixelController.cs" />
    <Compile Include="Controllers\PostalCodeController.cs" />
    <Compile Include="Controllers\PreRegisterController.cs" />
    <Compile Include="Controllers\RecoverEmailNitController.cs" />
    <Compile Include="Controllers\Register2Controller.cs" />
    <Compile Include="Controllers\QRCodeController.cs" />
    <Compile Include="Controllers\ResumePaymentController.cs" />
    <Compile Include="Controllers\ServerStatusController.cs" />
    <Compile Include="Controllers\TimeUseController.cs" />
    <Compile Include="Controllers\TvsController.cs" />
    <Content Include="c\js\company\offer\offerproducttrace.js" />
    <Compile Include="Controllers\UnsuscribeMailManagerController.cs" />
    <Compile Include="Enums\ApplicationDateFromEnum.cs" />
    <Compile Include="Enums\ChatNavigationPointEnum.cs" />
    <Compile Include="Enums\CommentsStatusEnum.cs" />
    <Compile Include="Enums\CompanyContactTypeEnum.cs" />
    <Compile Include="Enums\CompanyRejectionIdEnum.cs" />
    <Compile Include="Enums\CompetenceTestKeyEnum.cs" />
    <Compile Include="Enums\CvOrderEnum.cs" />
    <Compile Include="Enums\CvsSkillsGroupEnum.cs" />
    <Compile Include="Enums\CvsVisibilityEnum.cs" />
    <Compile Include="Enums\CvVisualizationStatusEnum.cs" />
    <Compile Include="Enums\DateFormatEnum.cs" />
    <Compile Include="Enums\DropDownListEnum.cs" />
    <Compile Include="Enums\CompentencesTestRuleKeysEnum.cs" />
    <Compile Include="Enums\EntityCountersEnum.cs" />
    <Compile Include="Enums\ExcludedEnum.cs" />
    <Compile Include="Enums\GenderEnum.cs" />
    <Compile Include="Enums\HasAdequacyEnum.cs" />
    <Compile Include="Enums\HasTalentViewTestEnum.cs" />
    <Compile Include="Enums\HasCompetenceTestEnum.cs" />
    <Compile Include="Enums\HasDisabilityStatusEnum.cs" />
    <Compile Include="Enums\ItemsComboTypeEnum.cs" />
    <Compile Include="Enums\MatchesVisibilityEnum.cs" />
    <Compile Include="Enums\PhotoFilterEnum.cs" />
    <Compile Include="Enums\RatingValuationsStatusEnum.cs" />
    <Compile Include="Enums\StudyingStatusEnum.cs" />
    <Compile Include="Enums\StudyStatusEnum.cs" />
    <Compile Include="Enums\TypeGraphicsValuationsEnum.cs" />
    <Compile Include="Controllers\CompanyVisionInterviewsStatisticsController.cs" />
    <Compile Include="Helpers\AccessControlHelper.cs" />
    <Compile Include="Helpers\RequestDeviceRecorder.cs" />
    <Compile Include="Helpers\CompanyAbuseHelper.cs" />
    <Compile Include="Enums\WorkingStatusEnum.cs" />
    <Compile Include="Helpers\DeviceValidationHelper.cs" />
    <Compile Include="Helpers\DictionaryExtensionsHelper.cs" />
    <Compile Include="Helpers\Html\PageHelper.cs" />
    <Compile Include="Helpers\IRequestDeviceRecorder.cs" />
    <Compile Include="Helpers\Mappings\CandidateReadSearchDataModelMapping.cs" />
    <Compile Include="Helpers\Mappings\CompanyCardTokenMapping.cs" />
    <Compile Include="Helpers\RequestOrigin.cs" />
    <Compile Include="Helpers\UserEmailLocatorHelper.cs" />
    <Compile Include="Helpers\PayUHelper.cs" />
    <Compile Include="Helpers\PopUpAddGeolocalizationOfferHelper.cs" />
    <Compile Include="Helpers\PopUpConverToCompleteDescHelper.cs" />
    <Compile Include="Helpers\PostalCodeHelper.cs" />
    <Compile Include="Helpers\ProductDataModelHelper.cs" />
    <Compile Include="Helpers\PromotionHelper.cs" />
    <Compile Include="Models\Analytics\GTMandAnalyticsModel.cs" />
    <Compile Include="Models\CandidateComparator\CandidateComparatorDataModel.cs" />
    <Compile Include="Models\CandidateComparator\OfferCandidateComparatorDataModel.cs" />
    <Compile Include="Models\CandidateComparator\OfferComparatorDataModel.cs" />
    <Compile Include="Models\CandidateComparator\RichPropertyModel.cs" />
    <Compile Include="Models\CanPublishParamsModel.cs" />
    <Compile Include="Models\CaptchaAbuseDataModel.cs" />
    <Compile Include="Models\CompanyBlockedDataModel.cs" />
    <Compile Include="Models\Company\Candidate\Cv\CvDetailPackAndBasicModel.cs" />
    <Compile Include="Models\Company\Candidate\QuizTalentviewExportDataModel.cs" />
    <Compile Include="Models\Company\Cart\CompanyCartFinalizePurchaseDataModel.cs" />
    <Compile Include="Models\Company\Cart\CompanyCartProductTemporalityDataModel.cs" />
    <Compile Include="Models\Company\Cart\CreditCardModel.cs" />
    <Compile Include="Models\Company\Cart\EditDataCompanyCartDataModel.cs" />
    <Compile Include="Models\Company\Cart\Messages\CompanyCartMessagesPaymentModel.cs" />
    <Compile Include="Models\Company\Cart\Messages\MultiPurchaseCartMessagesDataModel.cs" />
    <Compile Include="Models\Company\Cart\Payments\CompanyCardTokenModel.cs" />
    <Compile Include="Models\Company\Cart\WorldPayCardModel.cs" />
    <Compile Include="Models\Company\Configuration\ConfigurationInvoiceDataModel.cs" />
    <Compile Include="Models\Company\Configuration\DeviceManagementDataModel.cs" />
    <Compile Include="Models\Company\Configuration\DeviceAuthorizationViewModel.cs" />
    <Compile Include="Models\Company\Configuration\DtInvoiceDataModel.cs" />
    <Compile Include="Models\Company\Configuration\InvoiceDataModel.cs" />
    <Compile Include="Models\Company\Configuration\PurchaseOperationDataModel.cs" />
    <Compile Include="Models\Company\Conversations\CandidatesByOfferParamsDataModel.cs" />
    <Compile Include="Models\Company\Conversations\ContentMessageDataModel.cs" />
    <Compile Include="Models\Company\Conversations\ConversationDataModel.cs" />
    <Compile Include="Models\Company\Conversations\ConversationNavigationControlDataModel.cs" />
    <Compile Include="Models\Company\Conversations\ConversationPageControlDataModel.cs" />
    <Compile Include="Models\Company\Conversations\IndexConversationsRequestDataModel.cs" />
    <Compile Include="Models\Company\Conversations\MessageDataModel.cs" />
    <Compile Include="Models\Company\Conversations\MessagesByPageParamsDataModel.cs" />
    <Compile Include="Models\Company\Conversations\OfferConversationDetailDataModel.cs" />
    <Compile Include="Models\Company\Conversations\OfferListConversationsDataModel.cs" />
    <Compile Include="Models\Company\EmailLink\EmailLink.cs" />
    <Compile Include="Models\Company\EmailLink\EmailLinkBase.cs" />
    <Compile Include="Models\Company\EmailLink\EmailLinkOutlook.cs" />
    <Compile Include="Models\Company\EmailLink\EmailLinkGmail.cs" />
    <Compile Include="Models\Company\EmailLink\VerificationByCodeModel.cs" />
    <Compile Include="Models\Company\Home\BannerHomeDataModel.cs" />
    <Compile Include="Models\Company\Home\CompanyDescriptionDataModel.cs" />
    <Compile Include="Models\Company\Home\HomeConfigurationDataModelMobile.cs" />
    <Compile Include="Models\Company\Home\HomeCvsPackAndBasicDataModel.cs" />
    <Compile Include="Models\Company\Home\HomeFeaturesPostPublishDataModel.cs" />
    <Compile Include="Models\Company\Home\HomeConversationsDataModel.cs" />
    <Compile Include="Models\Company\Home\HomeOfferPostPublishDataModel.cs" />
    <Compile Include="Models\Company\Home\HomeOfferSuggestFilterModel.cs" />
    <Compile Include="Models\Company\Landings\LandingEnd2019DataModel.cs" />
    <Compile Include="Models\Company\Landings\LandingOfferPublishDataModel.cs" />
    <Compile Include="Models\Company\Landings\LandingWelcomeMemPackDataModel.cs" />
    <Compile Include="Models\Company\Landings\LandingWelcomeDataModel.cs" />
    <Compile Include="Models\Company\Landings\LandingNoMpDataModel.cs" />
    <Compile Include="Models\Company\Match\Cv\CVDownloaderConfigDataModel.cs" />
    <Compile Include="Models\Company\MultifiltersDataModel.cs" />
    <Compile Include="Models\Company\Offer\OfferPackAndBasicDataModel.cs" />
    <Compile Include="Models\Company\Product\ProductTraceDateTimeModel.cs" />
    <Compile Include="Models\Company\Product\ProductTraceSearchModel.cs" />
    <Compile Include="Models\Company\Product\CompanyProductTraceDataModel.cs" />
    <Compile Include="Models\Company\Offer\OfferAIDataModel.cs" />
    <Compile Include="Models\Company\Offer\OfferPostPublishDataModel.cs" />
    <Compile Include="Models\Company\Offer\OfferPostPublishStepTwoDataModel.cs" />
    <Compile Include="Models\Company\Product\CompanyAvailableUnitsPurchasedDataModel.cs" />
    <Compile Include="Models\Company\Product\CompanyProductOfferDataModel.cs" />
    <Compile Include="Models\Company\Product\LandingProductDataModel.cs" />
    <Compile Include="Models\Company\Product\LandingProductsContentDataModel.cs" />
    <Compile Include="Models\Company\Product\LandingContentProductsDataModel.cs" />
    <Compile Include="Models\Company\Product\LandingProductsDataModel.cs" />
    <Compile Include="Models\Company\Product\ProductContentDataModel.cs" />
    <Compile Include="Models\Company\Product\ProductDataModel.cs" />
    <Compile Include="Models\Company\Product\ProductSubGroupsDataModel.cs" />
    <Compile Include="Models\Company\Product\ProductTraceDataModel.cs" />
    <Compile Include="Models\Company\Shared\MobileSubmenuItemModel.cs" />
    <Compile Include="Models\Company\Shared\LineChartViewModel.cs" />
    <Compile Include="Models\Company\Shared\DependingDropdownModel.cs" />
    <Compile Include="Models\Company\Shared\EmailDataModel.cs" />
    <Compile Include="Models\Company\Shared\MobileSubmenuModel.cs" />
    <Compile Include="Models\Company\Shared\PopUpConvertToCompleteDataModel.cs" />
    <Compile Include="Models\Company\Shared\PopUpErrorAlertsDataModel.cs" />
    <Compile Include="Models\Company\Vision\Interviews\VisionInterviewDataModel.cs" />
    <Compile Include="Models\Company\Vision\Interviews\VisionInterviewItemResumDataModel.cs" />
    <Compile Include="Models\Company\Vision\Interviews\VisionInterviewQuestionDataModel.cs" />
    <Compile Include="Models\Company\Vision\Valuations\VisionValuationsGraphicsDataModel.cs" />
    <Compile Include="Models\Company\Vision\VisionAboutCompanyDataModel.cs" />
    <Compile Include="Models\Company\Vision\VisionCommentsDataModel.cs" />
    <Compile Include="Models\Company\Vision\VisionAwardsItemModel.cs" />
    <Compile Include="Models\Company\Vision\VisionAwardsModel.cs" />
    <Compile Include="Models\Company\Vision\VisionCompanyBenefitDataModel.cs" />
    <Compile Include="Models\Company\Vision\VisionCompanyDetailDataModel.cs" />
    <Compile Include="Models\Company\Vision\VisionAttractivenessModel.cs" />
    <Compile Include="Models\Company\Vision\VisionInterviewsCommentsDataModel.cs" />
    <Compile Include="Models\Company\Vision\VisionNewsDetailDataModel.cs" />
    <Compile Include="Models\Company\Vision\VisionPortalBenefitCategoryDataModel.cs" />
    <Compile Include="Models\Company\Vision\VisionPortalBenefitDataModel.cs" />
    <Compile Include="Models\Company\Vision\VisionRequestValuationDataModel.cs" />
    <Compile Include="Models\Company\Vision\VisionLogoDataModel.cs" />
    <Compile Include="Models\Company\Vision\VisionTalentEscapeDetailModel.cs" />
    <Compile Include="Models\Company\Vision\VisionTalentEscapeModel.cs" />
    <Compile Include="Models\Company\Vision\Shared\VisionCounterStatisticsDataModel.cs" />
    <Compile Include="Models\Company\Vision\Shared\VisionGraphicDataModel.cs" />
    <Compile Include="Models\Company\Vision\Shared\VisionGraphicLineDataModel.cs" />
    <Compile Include="Models\Company\Vision\Shared\VisionGraphicValueDataModel.cs" />
    <Compile Include="Models\Company\Vision\VisionEscapeRiskModel.cs" />
    <Compile Include="Models\Company\Vision\VisionBannerModel.cs" />
    <Compile Include="Models\Company\Vision\VisionCompanyValuationDataModel.cs" />
    <Compile Include="Models\Company\Vision\VisionInterviewsStatisticsDataModel.cs" />
    <Compile Include="Models\Company\Vision\VisionPhotosModel.cs" />
    <Compile Include="Models\Company\Vision\VisionRatingDetailDataModel.cs" />
    <Compile Include="Models\Company\Vision\Valuations\VisionValuationsDataModel.cs" />
    <Compile Include="Models\Company\Vision\Valuations\VisionRatingsDataModel.cs" />
    <Compile Include="Models\Company\Vision\Valuations\VisionValuationsPuntuations.cs" />
    <Compile Include="Models\Contact\ContactSubjectDataModel.cs" />
    <Compile Include="Models\Contact\ContactComboDataModel.cs" />
    <Compile Include="Models\Contact\NoTokensDataModel.cs" />
    <Compile Include="Models\DeviceBlockedDataModel.cs" />
    <Compile Include="Models\DeviceValidationDataModel.cs" />
    <Compile Include="Models\General\Chart\ChartViewAxesYModel.cs" />
    <Compile Include="Controllers\AccountController.cs" />
    <Compile Include="Controllers\CompanyConfigurationAccountsController.cs" />
    <Compile Include="Controllers\CompanyBaseController.cs" />
    <Compile Include="Controllers\CompanyCartPaymentFormController.cs" />
    <Compile Include="Controllers\CompanyConfigurationController.cs" />
    <Compile Include="Controllers\CompanyConfigurationEditCompanyController.cs" />
    <Compile Include="Controllers\CompanyConfigurationEditDataController.cs" />
    <Compile Include="Controllers\CompanyController.cs" />
    <Compile Include="Controllers\CompanyCvDownloaderController.cs" />
    <Compile Include="Controllers\CompanyCvsController.cs" />
    <Compile Include="Controllers\CompanyDefaultCvsController.cs" />
    <Compile Include="Controllers\CompanyVisionAboutCompanyController.cs" />
    <Compile Include="Controllers\CompanyVisionAttractivenessController.cs" />
    <Compile Include="Controllers\CompanyVisionAwardsController.cs" />
    <Compile Include="Controllers\CompanyVisionBenefitsController.cs" />
    <Compile Include="Controllers\CompanyVisionCommentsController.cs" />
    <Compile Include="Controllers\CompanyVisionEscapeRiskController.cs" />
    <Compile Include="Controllers\CompanyVisionLogoController.cs" />
    <Compile Include="Controllers\CompanyVisionNewsDetailController.cs" />
    <Compile Include="Controllers\CompanyVisionNewsController.cs" />
    <Compile Include="Controllers\CompanyVisionPhotosController.cs" />
    <Compile Include="Controllers\CompanyVisionRankingController.cs" />
    <Compile Include="Controllers\CompanyVisionTalentEscapeController.cs" />
    <Compile Include="Controllers\CompanyVisionValuationRequestController.cs" />
    <Compile Include="Controllers\CompanyVisionValuationsController.cs" />
    <Compile Include="Controllers\CompanyConfigurationInvoiceController.cs" />
    <Compile Include="Controllers\CompanyLandingController.cs" />
    <Compile Include="Controllers\CompanyMatchesController.cs" />
    <Compile Include="Controllers\CompanyMessagesController.cs" />
    <Compile Include="Controllers\CompanyOffersController.cs" />
    <Compile Include="Controllers\CompanyOffersPublishController.cs" />
    <Compile Include="Controllers\CompanyRejectedController.cs" />
    <Compile Include="Controllers\CompanyReportController.cs" />
    <Compile Include="Controllers\CompanyTrackingActionController.cs" />
    <Compile Include="Controllers\ContactController.cs" />
    <Compile Include="Controllers\HelpController.cs" />
    <Compile Include="Controllers\HomeController.cs" />
    <Compile Include="Controllers\LoginController.cs" />
    <Compile Include="Controllers\PaymentWorldPayController.cs" />
    <Compile Include="Controllers\RecoverPasswordController.cs" />
    <Compile Include="Controllers\RegisterController.cs" />
    <Compile Include="Controllers\TrackButtonController.cs" />
    <Compile Include="Enums\FileTypeEnum.cs" />
    <Compile Include="Enums\OfferActionEnum.cs" />
    <Compile Include="Enums\PageButtonsEnums.cs" />
    <Compile Include="Enums\ResultEnum.cs" />
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="App_Start\AutoMapperConfig.cs" />
    <Compile Include="Helpers\CompanyHelper.cs" />
    <Compile Include="Helpers\DictionaryHelper.cs" />
    <Compile Include="Helpers\EncryptationHelper.cs" />
    <Compile Include="Helpers\Html\AutocompleteHelpers.cs" />
    <Compile Include="Helpers\Html\IsSelectedHelper.cs" />
    <Compile Include="Helpers\Log4netTraceListener.cs" />
    <Compile Include="Helpers\MemcachedSessionStateStore.cs" />
    <Compile Include="Helpers\PageLiteralsHelper.cs" />
    <Compile Include="Helpers\PortalConfigHelper.cs" />
    <Compile Include="Helpers\ReflectionRegistrator.cs" />
    <Compile Include="Helpers\SecurityHelper.cs" />
    <Compile Include="Models\Company\Match\MatchExclusionDataModel.cs" />
    <Compile Include="Models\Company\Candidate\CandidateByCategoryDataModel.cs" />
    <Compile Include="Models\Company\Candidate\CandidateByEmploymentTypeDataModel.cs" />
    <Compile Include="Models\Company\Candidate\CandidateByLocalizationDataModel.cs" />
    <Compile Include="Models\Company\Candidate\CandidateReadDataModel.cs" />
    <Compile Include="Helpers\StringToolsHelper.cs" />
    <Compile Include="Models\Company\Cart\CompanyCartPaymentFormDataModel.cs" />
    <Compile Include="Models\Company\CompanyRejectedDataModel.cs" />
    <Compile Include="Models\Company\Candidate\CandidateReadSearchDataModel.cs" />
    <Compile Include="Models\Company\Candidate\Competences\CompetenceDataModel.cs" />
    <Compile Include="Models\Company\Candidate\Cv\CommentCvDataModel.cs" />
    <Compile Include="Models\Company\Candidate\Cv\CvDataModel.cs" />
    <Compile Include="Models\Company\Candidate\Cv\FormationByCvDataModel.cs" />
    <Compile Include="Models\Company\Candidate\Cv\RatingCvDataModel.cs" />
    <Compile Include="Models\Company\Candidate\Cv\SkillByCvDataModel.cs" />
    <Compile Include="Models\Company\Candidate\Cv\LanguageByCvDataModel.cs" />
    <Compile Include="Models\Company\Candidate\Cv\ExperienceByCvDataModel.cs" />
    <Compile Include="Models\Company\Cv\FoldersCvDataModel.cs" />
    <Compile Include="Models\Company\Cv\OffersListDataModel.cs" />
    <Compile Include="Models\Company\Cv\PackCvDataModel.cs" />
    <Compile Include="Models\Company\Home\HomeMatchesDataModel.cs" />
    <Compile Include="Models\Company\Candidate\Cv\CvDetailDataModel.cs" />
    <Compile Include="Models\Company\Match\FoldersDataModel.cs" />
    <Compile Include="Models\Company\Configuration\TrackingActions\CompanyTrackingActionsDataModel.cs" />
    <Compile Include="Models\Company\Configuration\TrackingActions\TrackingActionsDataModel.cs" />
    <Compile Include="Models\Company\Match\Cv\CompanyCvDownloaderListDataModel.cs" />
    <Compile Include="Models\Company\Match\Cv\CvDownloaderDataModel.cs" />
    <Compile Include="Models\Company\Vision\VisionBenefitsDataModel.cs" />
    <Compile Include="Models\Company\Vision\VisionRankingDataModel.cs" />
    <Compile Include="Models\Company\Vision\VisionNewsPrintListDataModel.cs" />
    <Compile Include="Models\Company\Vision\VisionNewsPrintListItemDataModel.cs" />
    <Compile Include="Models\Company\Vision\VisionSummaryDataModel.cs" />
    <Compile Include="Models\Company\Offer\CantPublishDataModel.cs" />
    <Compile Include="Models\Company\CompanyFilesDataModel.cs" />
    <Compile Include="Models\Company\Configuration\CompanyReportDataModelcs.cs" />
    <Compile Include="Models\Company\Candidate\CandidateDataModel.cs" />
    <Compile Include="Models\Company\Configuration\CompanyEditDataChangeEmailDataModel.cs" />
    <Compile Include="Models\Company\Configuration\CompanyEditDataChangePasswordDataModel.cs" />
    <Compile Include="Models\Company\Configuration\ConfigurationEditDataDataModel.cs" />
    <Compile Include="Models\Company\Configuration\AccountUserDataModel.cs" />
    <Compile Include="Models\Company\Cart\CompanyCartDataModel.cs" />
    <Compile Include="Models\Company\Cart\CompanyCartProductDataModel.cs" />
    <Compile Include="Models\Company\Home\HomeChartReportDataModel.cs" />
    <Compile Include="Models\Company\Home\HomeCvsDataModel.cs" />
    <Compile Include="Models\Company\Offer\KillerQuestionDataDataModel.cs" />
    <Compile Include="Models\Company\Offer\KillerQuestionDataModel.cs" />
    <Compile Include="Models\Company\Home\HomeMessagesDataModel.cs" />
    <Compile Include="Models\Company\Match\MatchDataModel.cs" />
    <Compile Include="Models\Company\Messages\ContentMessageDataModel.cs" />
    <Compile Include="Models\Company\Messages\ConversationDataModel.cs" />
    <Compile Include="Models\Company\Messages\MessageDataModel.cs" />
    <Compile Include="Models\Company\Offer\OfferCandidateDataModel.cs" />
    <Compile Include="Models\Company\Offer\OfferPrintDataModel.cs" />
    <Compile Include="Models\Company\Offer\PublishedDataModel.cs" />
    <Compile Include="Models\Company\Product\ProProductDataModel.cs" />
    <Compile Include="Models\Company\RatedPriceDataModel.cs" />
    <Compile Include="Models\Company\Shared\AlertDataModel.cs" />
    <Compile Include="Models\Company\Product\CompanyProductDataModel.cs" />
    <Compile Include="Models\Company\Product\CompanyProductFeatureDataModel.cs" />
    <Compile Include="Models\Company\CompanyDataModel.cs" />
    <Compile Include="Models\Company\Home\HomeConfigurationDataModel.cs" />
    <Compile Include="Models\Company\Home\HomeCounterDataModel.cs" />
    <Compile Include="Models\Company\Home\HomeDataModel.cs" />
    <Compile Include="Models\Company\Home\HomeMembresyResumDataModel.cs" />
    <Compile Include="Models\Company\Home\HomeOfferDataModel.cs" />
    <Compile Include="Models\Company\Home\HomeOfferPublishDataModel.cs" />
    <Compile Include="Models\Company\Configuration\InvoiceUnifiedDataModel.cs" />
    <Compile Include="Models\Company\Shared\KpiDataModel.cs" />
    <Compile Include="Models\Company\Landings\LandingExpiredMembershipDataModel.cs" />
    <Compile Include="Models\Company\Offer\OfferDataModel.cs" />
    <Compile Include="Models\Company\Offer\OfferLanguageDataModel.cs" />
    <Compile Include="Models\Company\Product\ProductFeatureDataModel.cs" />
    <Compile Include="Models\Company\Product\ProductFeatureDescriptionDataModel.cs" />
    <Compile Include="Models\Company\Product\ProductGroupDataModel.cs" />
    <Compile Include="Models\Company\Shared\ChartDataDataModel.cs" />
    <Compile Include="Models\Company\Shared\PopUpDataModel.cs" />
    <Compile Include="Models\Company\UserDataModel.cs" />
    <Compile Include="Models\Company\Configuration\UserDeleteDataModel.cs" />
    <Compile Include="Models\Company\Configuration\EditCredits\CompanyCreditsInfoDataModel.cs" />
    <Compile Include="Models\Company\Configuration\EditCredits\ControlFeatureUnitsDataModel.cs" />
    <Compile Include="Models\Company\Configuration\EditCredits\ControlGlobalFeatureUnitsDataModel.cs" />
    <Compile Include="Models\Company\Configuration\EditCredits\EditCreditsDataModel.cs" />
    <Compile Include="Models\Company\Configuration\EditCredits\UserEditCreditsDataModel.cs" />
    <Compile Include="Models\Contact\ContactDataModel.cs" />
    <Compile Include="Models\CvsCountersDataModel.cs" />
    <Compile Include="Models\FileDataModel.cs" />
    <Compile Include="Models\GoParamsModel.cs" />
    <Compile Include="Models\HealthCheckStatus.cs" />
    <Compile Include="Models\Home\ContentProductPackDataModelV2.cs" />
    <Compile Include="Models\Home\ContentProductPackDataModel.cs" />
    <Compile Include="Models\Home\ContentProductPackFeatureDataModel.cs" />
    <Compile Include="Models\Home\HomeDataModel.cs" />
    <Compile Include="Models\IconSuggestModel.cs" />
    <Compile Include="Models\InternalModels\ComercialAndTechnicalSupportInternalModel.cs" />
    <Compile Include="Models\InternalModels\CompanyComercialInternalModel.cs" />
    <Compile Include="Models\Landings\ContentLandingProductPackDataModel.cs" />
    <Compile Include="Models\Landings\ContentLandingProductPackDataModelV2.cs" />
    <Compile Include="Models\Landings\ContentLandingProductPackFeatureDataModel.cs" />
    <Compile Include="Models\Landings\LandingInfoRegisterDataModel.cs" />
    <Compile Include="Models\LoginDataModel.cs" />
    <Compile Include="Models\PageLiteralsDataModel.cs" />
    <Compile Include="Models\PagerDataModel.cs" />
    <Compile Include="Models\PasswordDataModel.cs" />
    <Compile Include="Models\Payments\PaymentReasonsKOModel.cs" />
    <Compile Include="Models\RecoverEmailNitDataModel.cs" />
    <Compile Include="Models\RecoverPasswordDataModel.cs" />
    <Compile Include="Models\RegisterDataModel.cs" />
    <Compile Include="Models\SelectListItemWithIcon.cs" />
    <Compile Include="Models\ServerStatusDataModel.cs" />
    <Compile Include="Models\SimplifiedRegister\ImportConsentModel.cs" />
    <Compile Include="Models\SimplifiedRegister\ImportOfferDetailModel.cs" />
    <Compile Include="Models\SimplifiedRegister\ImportOfferModel.cs" />
    <Compile Include="Models\SimplifiedRegister\PopUpImportOfferDetailModel.cs" />
    <Compile Include="Models\SimplifiedRegister\PreRegisterDataModel.cs" />
    <Compile Include="Models\SimplifiedRegister\SimplifiedRegisterDataModel.cs" />
    <Compile Include="Models\SimplifiedRegister\RegisterDataModel.cs" />
    <Compile Include="Models\ValidateLoginDataModel.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Security\RedarborAuthorizeAttribute.cs" />
    <Compile Include="Security\RedarborAuthorizeOverrideAttribute.cs" />
    <Compile Include="Validation\Attributes\CheckSpacesInDescriptionAttribute.cs" />
    <Compile Include="Validation\Attributes\EmailCheckerAttribute.cs" />
    <Compile Include="Validation\Attributes\NoEmailAllowedAttribute.cs" />
    <Compile Include="Validation\Attributes\IsValidFormatTextAttribute.cs" />
    <Compile Include="Validation\Attributes\RequiredFileAttribute.cs" />
    <Compile Include="Validation\Attributes\MinMaxSalaryAttribute.cs" />
    <Compile Include="Validation\Attributes\MinMaxVacanciesAttribute.cs" />
    <Compile Include="Validation\Attributes\NoSpacesAllowedAttribute.cs" />
    <Compile Include="Validation\Attributes\NoSurrogateTextAllowed.cs" />
    <Compile Include="Validation\Attributes\NitCheckerAttribute.cs" />
    <Compile Include="Validation\Attributes\PostalCodeAttribute.cs" />
    <Compile Include="Validation\Attributes\ShortGreaterThanAttribute.cs" />
    <Compile Include="Validation\Attributes\NoUrAllowedAttribute.cs" />
    <Compile Include="Validation\Attributes\NoEmojisAllowedAttribute.cs" />
    <Compile Include="Validation\Attributes\RequiredAttributes.cs" />
    <Compile Include="Validation\Attributes\ShortLowerThanAttribute.cs" />
    <Compile Include="Validation\Attributes\TrimSpacesAttribute.cs" />
    <Compile Include="Validation\CaptchaValidationService.cs" />
    <Compile Include="Validation\ICaptchaValidationService.cs" />
    <Compile Include="Validation\RedarborModelValidationProvider.cs" />
    <Compile Include="Validation\RedarborModelValidator.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\web.config" />
    <None Include="packages.config">
      <SubType>Designer</SubType>
    </None>
    <Content Include="Views\Shared\Layouts\_LayoutMaster.cshtml" />
    <Content Include="Views\Shared\Error.cshtml" />
    <Content Include="c\js\jquery-3.1.1.min.map" />
    <Content Include="c\js\jquery-3.1.1.slim.min.map" />
    <Content Include="Views\_ViewStart.cshtml" />
    <Content Include="Views\Home\Index.cshtml" />
    <Content Include="Views\Login\Index.cshtml" />
    <Content Include="Views\Shared\Layouts\_LayoutCompany.cshtml" />
    <Content Include="Views\Account\Used.cshtml" />
    <Content Include="Views\Shared\HttpError404.cshtml" />
    <Content Include="Views\Shared\Partials\_Login.cshtml" />
    <Content Include="Views\Shared\Partials\_Logout.cshtml" />
    <Content Include="Views\Shared\Partials\Product\_Product.cshtml" />
    <Content Include="Views\Shared\Partials\Product\_ProductPromotion.cshtml" />
    <Content Include="Views\Account\Blocked.cshtml">
      <Generator>RazorGenerator</Generator>
    </Content>
    <Content Include="Views\Shared\Partials\_Alert.cshtml" />
    <Content Include="Views\CompanyConfiguration\Index.cshtml" />
    <Content Include="Views\CompanyConfigurationEditCompany\Index.cshtml" />
    <Content Include="Views\CompanyLanding\ExpiredMembership.cshtml" />
    <Content Include="Views\Shared\DisplayTemplates\membresyOfferResum.cshtml" />
    <None Include="Properties\PublishProfiles\FolderProfile.pubxml" />
    <Content Include="Views\Shared\DisplayTemplates\membresyMailingCounterGraph.cshtml" />
    <Content Include="Views\CompanyOffers\Index.cshtml" />
    <Content Include="Views\Shared\Partials\Offer\_OfferRowMobile.cshtml" />
    <Content Include="Views\Shared\DisplayTemplates\defaultPager.cshtml" />
    <Content Include="Views\Shared\Partials\_FooterCompany.cshtml" />
    <Content Include="Views\CompanyOffersPublish\Index.cshtml" />
    <Content Include="Views\Shared\Partials\Home\_CompanyListOffers.cshtml" />
    <Content Include="Views\Shared\Partials\Offer\_OfferEditOther.cshtml" />
    <Content Include="Views\Shared\Partials\Product\_ProductLanding.cshtml" />
    <Content Include="Views\Shared\Partials\Product\_ProductExpiredMembershipLanding.cshtml" />
    <Content Include="Views\CompanyOffersPublish\CantPublish.cshtml" />
    <Content Include="Views\CompanyLanding\End.cshtml" />
    <Content Include="Views\Shared\Partials\Product\_ProductGroupLanding.cshtml" />
    <Content Include="Views\Shared\Partials\Configuration\_InvoiceUnified.cshtml" />
    <Content Include="Views\Shared\DisplayTemplates\chartReportGraph.cshtml" />
    <Content Include="Views\CompanyOffers\Print.cshtml" />
    <Content Include="Views\CompanyConfigurationEditData\Index.cshtml" />
    <Content Include="Views\Help\Index.cshtml" />
    <Content Include="Views\Shared\DisplayTemplates\contentProductPack.cshtml" />
    <Content Include="Views\Contact\Index.cshtml" />
    <Content Include="Views\RecoverPassword\Index.cshtml" />
    <Content Include="Views\CompanyOffersPublish\Published.cshtml" />
    <Content Include="Views\CompanyCvs\Index.cshtml" />
    <Content Include="Views\CompanyCvs\EditorTemplates\CompanyListCvsMembership.cshtml" />
    <Content Include="Views\Shared\Partials\Candidate\_CandidateListItem.cshtml" />
    <Content Include="Views\Shared\Partials\Company\_FileUpload.cshtml" />
    <Content Include="Views\CompanyReport\Index.cshtml" />
    <Content Include="Views\Shared\Partials\Configuration\_CompanyReport.cshtml" />
    <Content Include="Views\CompanyReport\DownloadReport.cshtml" />
    <Content Include="Views\CompanyMessages\Index.cshtml" />
    <Content Include="Views\Shared\Partials\Message\_MessageConversation.cshtml" />
    <Content Include="Views\Shared\Partials\Message\_MessageMessages.cshtml" />
    <Content Include="Views\Shared\Partials\Message\_MessageBoxMessages.cshtml" />
    <Content Include="Views\CompanyMatches\EditorTemplates\CompanyMatchesMembershipPack.cshtml" />
    <Content Include="Views\Shared\Partials\Match\_CompanyMatchesBasic.cshtml" />
    <Content Include="Views\Shared\Partials\Match\_Matches.cshtml" />
    <Content Include="Views\CompanyConfigurationAccounts\Index.cshtml" />
    <Content Include="Views\Shared\Partials\Configuration\_UserEditOrInsert.cshtml" />
    <Content Include="Views\Shared\Partials\Configuration\_UserEditRow.cshtml" />
    <Content Include="Views\CompanyTrackingAction\Index.cshtml" />
    <Content Include="Views\Shared\DisplayTemplates\defaultPopUp.cshtml" />
    <Content Include="Views\CompanyCvDownloader\Index.cshtml" />
    <Content Include="Views\Shared\Partials\CvDownloader\_CompanyCvDownloaderList.cshtml" />
    <Content Include="Views\CompanyVisionAboutCompany\Index.cshtml" />
    <Content Include="Views\CompanyVisionLogo\Index.cshtml" />
    <Content Include="Views\CompanyVisionPhotos\Index.cshtml" />
    <Content Include="Views\CompanyVisionBenefits\Index.cshtml" />
    <Content Include="Views\CompanyVisionAwards\Index.cshtml" />
    <Content Include="Views\CompanyVisionComments\Index.cshtml" />
    <Content Include="Views\CompanyVisionValuationRequest\Index.cshtml" />
    <Content Include="Views\CompanyVisionValuations\Index.cshtml" />
    <Content Include="Views\CompanyVisionRanking\Index.cshtml" />
    <Content Include="Views\CompanyVisionAttractiveness\Index.cshtml" />
    <Content Include="Views\CompanyDefaultCvs\Index.cshtml" />
    <Content Include="Views\Shared\Partials\Cv\_OffersListCvs.cshtml" />
    <Content Include="Views\CompanyCvDownloader\Extract.cshtml" />
    <Content Include="Views\CompanyCvDownloader\DownloadCV.cshtml" />
    <Content Include="Views\Shared\Partials\Company\Cart\_PayMethod.cshtml" />
    <Content Include="Views\Shared\Partials\Company\Cart\_OfferProductCart.cshtml" />
    <Content Include="Views\CompanyRejected\Index.cshtml" />
    <Content Include="Views\CompanyCartPaymentForm\Index.cshtml" />
    <Content Include="Views\Shared\Partials\Offer\_KillerQuestions.cshtml" />
    <Content Include="Views\Shared\Partials\Offer\_KillerQuestionData.cshtml" />
    <Content Include="Views\CompanyConfigurationInvoice\Index.cshtml" />
    <Content Include="Views\Shared\Partials\Cv\_listCvDetailSearch.cshtml" />
    <Content Include="Views\CompanyConfigurationEditCredits\Index.cshtml" />
    <Content Include="Views\Shared\Partials\Message\_MessageConversationHeader.cshtml" />
    <Content Include="Views\CompanyMatches\Index.cshtml" />
    <Content Include="Views\CompanyVisionNews\Index.cshtml" />
    <Content Include="Views\Shared\Partials\Vision\_VisionNewsItem.cshtml" />
    <Content Include="Views\CompanyVisionNewsDetail\Index.cshtml" />
    <Content Include="Views\CompanyVisionEscapeRisk\Index.cshtml" />
    <Content Include="Views\Shared\Partials\Vision\_VisionRankingItem.cshtml" />
    <Content Include="Views\Shared\Partials\Vision\_VisionBanner.cshtml" />
    <Content Include="Views\CompanyVisionTalentEscape\Index.cshtml" />
    <Content Include="Views\Shared\Partials\Vision\_VisionTalentEscapeFromItem.cshtml" />
    <Content Include="Views\Shared\Partials\Vision\_VisionTalentEscapeToItem.cshtml" />
    <Content Include="Views\Shared\Partials\Vision\_VisionCounterValuationItem.cshtml" />
    <Content Include="Views\Shared\Partials\Vision\_VisionGraphicValuationsItem.cshtml" />
    <Content Include="Views\CompanyVisionInterviewsStatistics\Index.cshtml" />
    <Content Include="Views\Shared\Partials\Vision\_VisionCounterStatistics.cshtml" />
    <Content Include="Views\Shared\Partials\Vision\_VisionGraphicStatistics.cshtml" />
    <Content Include="Views\Shared\Partials\Vision\_VisionCommentItem.cshtml" />
    <Content Include="Views\Shared\Partials\Vision\_VisionAwardsItem.cshtml" />
    <Content Include="Views\CompanyMatchesExclusion\Index.cshtml" />
    <Content Include="Views\CompanyCartTransferPayment\Index.cshtml" />
    <Content Include="Views\CompanyVisionInterviewsComments\Index.cshtml" />
    <Content Include="Views\Shared\Partials\Vision\_VisionInterviewCommentItem.cshtml" />
    <Content Include="Views\HealthCheck\Index.cshtml" />
    <Content Include="Views\Password\Index.cshtml" />
    <Content Include="Views\CompanyCartFinalizePurchase\Index.cshtml" />
    <Content Include="Views\Shared\Partials\Company\Cart\_EditDataCompanyCart.cshtml" />
    <Content Include="Views\Shared\Partials\_OldMenu.cshtml" />
    <Content Include="Views\Shared\Partials\Menus\_NewMenu.cshtml" />
    <Content Include="Views\Shared\Partials\Vision\_Vision.cshtml" />
    <Content Include="Views\Shared\DisplayTemplates\tooltipConditions.cshtml" />
    <Content Include="Views\Shared\Partials\_MessageRejected.cshtml" />
    <Content Include="Web.config">
      <SubType>Designer</SubType>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="App_Data\mysql-services\appsettings\schemas\repo_settings\migrate\V01\" />
    <Folder Include="App_Data\mysql-services\common\schemas\ct-comun\dev\populate\" />
    <Folder Include="App_Data\mysql-services\common\schemas\ct-comun\migrate\V01\" />
    <Folder Include="App_Data\mysql-services\common\schemas\repo-settings\migrate\V01\" />
    <Folder Include="App_Data\mysql-services\master\schemas\computrabajocoldata\migrate\V01\" />
    <Folder Include="App_Data\mysql-services\repos\schemas\authenticate\migrate\V01\" />
    <Folder Include="App_Data\mysql-services\repos\schemas\companies\migrate\V01\" />
    <Folder Include="App_Data\mysql-services\repos\schemas\ct_user\migrate\V01\" />
    <Folder Include="App_Data\mysql-services\repos\schemas\products\migrate\V01\" />
    <Folder Include="App_Data\mysql-services\repos\schemas\repo_killer_questions\migrate\V01\" />
    <Folder Include="App_Data\mysql-services\repos\schemas\tracking\migrate\V01\" />
    <Folder Include="Views\CTA\" />
    <Folder Include="Views\Logout\" />
    <Folder Include="Views\Shared\Components\" />
    <Folder Include="Views\TimeUse\" />
    <Folder Include="Views\UnsuscribeMailManager\" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Readarbor.Core.Mailing.Library\Redarbor.Core.Mailing.Library.csproj">
      <Project>{1e662c4f-f5a9-42b8-9374-df716e1b7fab}</Project>
      <Name>Redarbor.Core.Mailing.Library</Name>
    </ProjectReference>
    <ProjectReference Include="..\Readarbor.Master.Entities\Redarbor.Master.Entities.csproj">
      <Project>{72B93BA2-C177-4DDF-9F27-E08151323719}</Project>
      <Name>Redarbor.Master.Entities</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Computrabajo.Impl.ServiceLibrary\Redarbor.Core.Computrabajo.Impl.ServiceLibrary.csproj">
      <Project>{aa731e64-6689-4765-80ee-5beefec24849}</Project>
      <Name>Redarbor.Core.Computrabajo.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Contracts.ServiceLibrary\Redarbor.Core.Contracts.ServiceLibrary.csproj">
      <Project>{229A1E28-531D-4235-93C3-F4B6CB79E8E2}</Project>
      <Name>Redarbor.Core.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Impl.ServiceLibrary\Redarbor.Core.Impl.ServiceLibrary.csproj">
      <Project>{EED50644-9F5C-4943-8D44-3E13CFD32073}</Project>
      <Name>Redarbor.Core.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.SEO.Impl.ServiceLibrary\Redarbor.Core.SEO.Impl.ServiceLibrary.csproj">
      <Project>{7D78AFEE-FA13-4685-9954-A2D1FBA8AC30}</Project>
      <Name>Redarbor.Core.SEO.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.DataAnalytics.ServiceLibrary\Redarbor.DataAnalytics.ServiceLibrary.csproj">
      <Project>{4a1d2af2-1a82-466b-9fad-1028da78cb9d}</Project>
      <Name>Redarbor.DataAnalytics.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Geolocation.Impl.ServiceLibrary\Redarbor.Geolocation.Impl.ServiceLibrary.csproj">
      <Project>{D8611359-3573-4A01-B5C6-46BF89760C49}</Project>
      <Name>Redarbor.Geolocation.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Offer.Impl.ServiceLibrary\Redarbor.Offer.Impl.ServiceLibrary.csproj">
      <Project>{7F8B5DFC-EBE2-4064-8C09-83594443EFB3}</Project>
      <Name>Redarbor.Offer.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.OperacionCompra.Impl.ServiceLibrary\Redarbor.PurchaseOperation.Impl.ServiceLibrary.csproj">
      <Project>{71BFBA88-21F1-4CD2-94C7-A8B4F4332421}</Project>
      <Name>Redarbor.PurchaseOperation.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Professions.Impl.ServiceLibrary\Redarbor.Professions.Impl.ServiceLibrary.csproj">
      <Project>{BD2D8A5D-7DDE-4A77-BA89-E0C5207D7CC6}</Project>
      <Name>Redarbor.Professions.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Surveys.Contracts.ServiceLibrary\Redarbor.Surveys.Contracts.ServiceLibrary.csproj">
      <Project>{16fab7d7-2a25-470a-8953-f4d67429a3e0}</Project>
      <Name>Redarbor.Surveys.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Surveys.Impl.ServiceLibrary\Redarbor.Surveys.Impl.ServiceLibrary.csproj">
      <Project>{19f44f80-010f-41ef-87b2-b4b92cfba525}</Project>
      <Name>Redarbor.Surveys.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.User.Impl.ServiceLibrary\Redarbor.User.Impl.ServiceLibrary.csproj">
      <Project>{12C9FC58-7B88-459B-A79D-99CD2D3313A1}</Project>
      <Name>Redarbor.User.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Vision.Impl.ServiceLibrary\Redarbor.Vision.Impl.ServiceLibrary.csproj">
      <Project>{FCAF5226-0B43-400E-8E62-3E4689A12F65}</Project>
      <Name>Redarbor.Vision.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.WebChannel.Contracts.ServiceLibrary\Redarbor.WebChannel.Contracts.ServiceLibrary.csproj">
      <Project>{01f0e65d-65f4-482a-b91c-ef7dae504b09}</Project>
      <Name>Redarbor.WebChannel.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.WebChannel.Entities\Redarbor.WebChannel.Entities.csproj">
      <Project>{0411A288-3DF0-48E1-AB1B-980B02DFDD36}</Project>
      <Name>Redarbor.WebChannel.Entities</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.WebChannel.Impl.ServiceLibrary\Redarbor.WebChannel.Impl.ServiceLibrary.csproj">
      <Project>{2735e5fd-4cbf-4c8b-acc1-4b8d08dd8569}</Project>
      <Name>Redarbor.WebChannel.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Candidate.Contracts.ServiceLibrary\Redarbor.Candidate.Contracts.ServiceLibrary.csproj">
      <Project>{adeb0b9a-1f18-49c5-a6ec-20becdab8eaf}</Project>
      <Name>Redarbor.Candidate.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Candidate.Impl.ServiceLibrary\Redarbor.Candidate.Impl.ServiceLibrary.csproj">
      <Project>{a12d1341-ad71-43b8-8c88-beffad211078}</Project>
      <Name>Redarbor.Candidate.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Candidate.Library\Redarbor.Candidate.Library.csproj">
      <Project>{bd9fd999-b6fe-430d-9ea6-332064163eb7}</Project>
      <Name>Redarbor.Candidate.Library</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.CandidateComparator.Contracts.ServiceLibrary\Redarbor.CandidateComparator.Contracts.ServiceLibrary.csproj">
      <Project>{8665062A-A151-4875-BCC5-BD1368615420}</Project>
      <Name>Redarbor.CandidateComparator.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.CandidateComparator.Impl.ServiceLibrary\Redarbor.CandidateComparator.Impl.ServiceLibrary.csproj">
      <Project>{10d17e84-5607-48d2-8746-7626f37f3937}</Project>
      <Name>Redarbor.CandidateComparator.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.CommentCv.Contracts.ServiceLibrary\Redarbor.CommentCv.Contracts.ServiceLibrary.csproj">
      <Project>{afb3a08d-aede-49e6-bfe6-585ec43a3eef}</Project>
      <Name>Redarbor.CommentCv.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.CommentCv.Impl.ServiceLibrary\Redarbor.CommentCv.Impl.ServiceLibrary.csproj">
      <Project>{c29e2cde-54a5-4587-b24b-f35a48f9fe32}</Project>
      <Name>Redarbor.CommentCv.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.CommentCv.Library\Redarbor.CommentCv.Library.csproj">
      <Project>{093db295-1250-45b7-af03-ab6649bb2bd4}</Project>
      <Name>Redarbor.CommentCv.Library</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Common.Entities\Redarbor.Common.Entities.csproj">
      <Project>{3da7e832-19ae-4c35-85f8-5faf5a4dabeb}</Project>
      <Name>Redarbor.Common.Entities</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Company.Contracts.ServiceLibrary\Redarbor.Company.Contracts.ServiceLibrary.csproj">
      <Project>{76df1704-a73e-40ed-bbe7-69473d962cb1}</Project>
      <Name>Redarbor.Company.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Company.Impl.ServiceLibrary\Redarbor.Company.Impl.ServiceLibrary.csproj">
      <Project>{04514890-294f-47c8-a92a-a790d850cb10}</Project>
      <Name>Redarbor.Company.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Company.Library\Redarbor.Company.Library.csproj">
      <Project>{8738fc41-64c2-4664-923c-9486f8d4b607}</Project>
      <Name>Redarbor.Company.Library</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Configuration.Library\Redarbor.Configuration.Library.csproj">
      <Project>{bd90ad61-e12d-4cbd-be3a-81a1a31fddb2}</Project>
      <Name>Redarbor.Configuration.Library</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Computrabajo.Contracts.ServiceLibrary\Redarbor.Core.Computrabajo.Contracts.ServiceLibrary.csproj">
      <Project>{9BE71737-B615-4227-BB44-C5ECEC3DCED3}</Project>
      <Name>Redarbor.Core.Computrabajo.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Computrabajo.Impl.ServiceLibrary\Redarbor.Core.Computrabajo.Impl.ServiceLibrary.csproj">
      <Project>{aa731e64-6689-4765-80ee-5beefec24849}</Project>
      <Name>Redarbor.Core.Computrabajo.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Elastic.Library\Redarbor.Core.Elastic.Library.csproj">
      <Project>{66d471b3-da38-4a79-820b-028cde8bc335}</Project>
      <Name>Redarbor.Core.Elastic.Library</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Event.Contracts.ServiceLibrary\Redarbor.Core.Event.Contracts.ServiceLibrary.csproj">
      <Project>{B64D2170-BAAA-4F60-90DA-0AA195F23CBA}</Project>
      <Name>Redarbor.Core.Event.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Event.Impl.ServiceLibrary\Redarbor.Core.Event.Impl.ServiceLibrary.csproj">
      <Project>{99dc9216-beac-441f-8c52-678550e0d0a8}</Project>
      <Name>Redarbor.Core.Event.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.PDFTools.Impl.ServiceLibrary\Redarbor.Core.PDFTools.Impl.ServiceLibrary.csproj">
      <Project>{67D59130-E2D1-4455-B41E-C0192904FE2A}</Project>
      <Name>Redarbor.Core.PDFTools.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Shared.Contracts.ServiceLibrary\Redarbor.Core.Shared.Contracts.ServiceLibrary.csproj">
      <Project>{21E3ABEA-6637-49C2-8684-E0F842DE8506}</Project>
      <Name>Redarbor.Core.Shared.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Shared.Impl.ServiceLibrary\Redarbor.Core.Shared.Impl.ServiceLibrary.csproj">
      <Project>{d6387058-eb7b-42f7-aa9a-9e73e2a217dd}</Project>
      <Name>Redarbor.Core.Shared.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Timeline.Contracts.ServiceLibrary\Redarbor.Core.Timeline.Contracts.ServiceLibrary.csproj">
      <Project>{e90199e2-5625-46c2-8853-a3e877326f25}</Project>
      <Name>Redarbor.Core.Timeline.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Timeline.Impl.ServiceLibrary\Redarbor.Core.Timeline.Impl.ServiceLibrary.csproj">
      <Project>{5be33fb7-1fd3-4eab-bd5f-6939d23861cf}</Project>
      <Name>Redarbor.Core.Timeline.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.TrackingActions.Contracts.ServiceLibrary\Redarbor.Core.TrackingActions.Contracts.ServiceLibrary.csproj">
      <Project>{a7ec02a5-033a-4ede-ad08-8acbcd6dea55}</Project>
      <Name>Redarbor.Core.TrackingActions.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.TrackingActions.Impl.ServiceLibrary\Redarbor.Core.TrackingActions.Impl.ServiceLibrary.csproj">
      <Project>{219b0007-b45d-493e-af04-4ca73812f496}</Project>
      <Name>Redarbor.Core.TrackingActions.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.ItemsCombo.Contracts.ServiceLibrary\Redarbor.ItemsCombo.Contracts.ServiceLibrary.csproj">
      <Project>{16547152-518b-4a2d-b005-adfaf285ba2c}</Project>
      <Name>Redarbor.ItemsCombo.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.ItemsCombo.Impl.ServiceLibrary\Redarbor.ItemsCombo.Impl.ServiceLibrary.csproj">
      <Project>{77f1cd5f-a4e6-4fe5-b8cb-3b38eb3c1e63}</Project>
      <Name>Redarbor.ItemsCombo.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Message.Jobads.Chat.Impl.ServiceLibrary\Redarbor.Message.Jobads.Chat.Impl.ServiceLibrary.csproj">
      <Project>{3fbdfa22-95e4-4d99-9321-29aff141a719}</Project>
      <Name>Redarbor.Message.Jobads.Chat.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Offer.Elastic.Contracts.ServiceLibrary\Redarbor.Offer.Elastic.Contracts.ServiceLibrary.csproj">
      <Project>{5AD0B129-917F-480E-9067-6784E7216441}</Project>
      <Name>Redarbor.Offer.Elastic.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.PaymentCredentials.Contracts.ServiceLibrary\Redarbor.PaymentCredentials.Contracts.ServiceLibrary.csproj">
      <Project>{5f74d73b-fc2c-4693-8b01-595cd55bd4f0}</Project>
      <Name>Redarbor.PaymentCredentials.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.PaymentCredentials.Impl.ServiceLibrary\Redarbor.PaymentCredentials.Impl.ServiceLibrary.csproj">
      <Project>{99f9d339-62ff-4a74-8fcb-7ae32336372f}</Project>
      <Name>Redarbor.PaymentCredentials.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.PostalCode.Contracts.ServiceLibrary\Redarbor.PostalCode.Contracts.ServiceLibrary.csproj">
      <Project>{5E111B30-115A-44E3-8EFC-10E3D9606C3B}</Project>
      <Name>Redarbor.PostalCode.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.PostalCode.Impl.ServiceLibrary\Redarbor.PostalCode.Impl.ServiceLibrary.csproj">
      <Project>{9D476B4D-F590-4307-9ABE-C7AD627EED49}</Project>
      <Name>Redarbor.PostalCode.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Procs.Domain\Redarbor.Procs.Domain.csproj">
      <Project>{F5287777-BDF4-4D56-B30D-6B7462400E93}</Project>
      <Name>Redarbor.Procs.Domain</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Professions.Library\Redarbor.Professions.Library.csproj">
      <Project>{b0ea1edd-4ce9-4814-85a2-6e82d9671262}</Project>
      <Name>Redarbor.Professions.Library</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.PurchaseOperationCtPayment.Library\Redarbor.PurchaseOperationCtPayment.Library.csproj">
      <Project>{9F29690F-5F2F-46DC-A5A4-BB5D0377DFA8}</Project>
      <Name>Redarbor.PurchaseOperationCtPayment.Library</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Repo.KillerQuestions.Library\Redarbor.Repo.KillerQuestions.Library.csproj">
      <Project>{0b08c4cf-6d00-452f-9a14-fa50d207f1bb}</Project>
      <Name>Redarbor.Repo.KillerQuestions.Library</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Repo.Offer.Library\Redarbor.Repo.Offer.Library.csproj">
      <Project>{fbb2e0b0-e906-4a02-b4ca-0c6545fd1734}</Project>
      <Name>Redarbor.Repo.Offer.Library</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Repo.Trackings.Library\Redarbor.Repo.Trackings.Library.csproj">
      <Project>{59200A52-1BFB-47B0-A163-0588FDDACE21}</Project>
      <Name>Redarbor.Repo.Trackings.Library</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.TokenPayments.Impl.ServiceLibrary\Redarbor.PaymentToken.Impl.ServiceLibrary.csproj">
      <Project>{e5f52713-2c6f-4f51-9323-8bfa3aa4eb9c}</Project>
      <Name>Redarbor.PaymentToken.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Tokens.Contracts.ServiceLibrary\Redarbor.PaymentToken.Contracts.ServiceLibrary.csproj">
      <Project>{d5e5aeca-5677-42d0-8ee7-38501a69e41b}</Project>
      <Name>Redarbor.PaymentToken.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.TrackingRepo.Contracts.ServiceLibrary\Redarbor.TrackingRepo.Contracts.ServiceLibrary.csproj">
      <Project>{56290547-988D-4B89-8219-C3E686D14D9B}</Project>
      <Name>Redarbor.TrackingRepo.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.TrackingRepo.Impl.ServiceLibrary\Redarbor.TrackingRepo.Impl.ServiceLibrary.csproj">
      <Project>{7750ec4f-8e8c-4695-8f71-c0536e2482c9}</Project>
      <Name>Redarbor.TrackingRepo.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Vision.Contracts.ServiceLibrary\Redarbor.Vision.Contracts.ServiceLibrary.csproj">
      <Project>{6ba9cf36-3c7c-461f-9b3b-98f5b3314eb2}</Project>
      <Name>Redarbor.Vision.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Vision.Library\Redarbor.Vision.Library.csproj">
      <Project>{eb022f8c-e2a0-41f0-aac3-5a274444dffd}</Project>
      <Name>Redarbor.Vision.Library</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.CustomFolders.Contracts.ServiceLibrary\Redarbor.CustomFolders.Contracts.ServiceLibrary.csproj">
      <Project>{01f6f213-01e4-4e7d-902d-02126e7cb19b}</Project>
      <Name>Redarbor.CustomFolders.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.CustomFolders.Impl.ServiceLibrary\Redarbor.CustomFolders.Impl.ServiceLibrary.csproj">
      <Project>{6caaf2d2-9ca5-48c4-bc4e-f9544f321499}</Project>
      <Name>Redarbor.CustomFolders.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.CustomFolders.Library\Redarbor.CustomFolders.Library.csproj">
      <Project>{ec3ab21c-3922-421f-9ff0-9578490f78f9}</Project>
      <Name>Redarbor.CustomFolders.Library</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Folders.Contracts.ServiceLibrary\Redarbor.Folders.Contracts.ServiceLibrary.csproj">
      <Project>{8359D5A1-4FC4-46CC-BD69-25D31D929F45}</Project>
      <Name>Redarbor.Folders.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Folders.Impl.ServiceLibrary\Redarbor.Folders.Impl.ServiceLibrary.csproj">
      <Project>{95d6d352-09f1-4590-a826-74166b99a172}</Project>
      <Name>Redarbor.Folders.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Folders.Library\Redarbor.Folders.Library.csproj">
      <Project>{3cd23e55-0bd2-4897-8828-75a7021df0e8}</Project>
      <Name>Redarbor.Folders.Library</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Geolocation.Contracts.ServiceLibrary\Redarbor.Geolocation.Contracts.ServiceLibrary.csproj">
      <Project>{6db7d0e8-b5c5-42ea-81c1-0e1418cbd36a}</Project>
      <Name>Redarbor.Geolocation.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Master.Contracts.ServiceLibrary\Redarbor.Master.Contracts.ServiceLibrary.csproj">
      <Project>{e0dd7a84-2cdb-4704-b37a-a0367924112f}</Project>
      <Name>Redarbor.Master.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Cache.Contracts.ServiceLibrary\Redarbor.Core.Cache.Contracts.ServiceLibrary.csproj">
      <Project>{e683967c-b674-4450-b9a6-6030fd65e0f4}</Project>
      <Name>Redarbor.Core.Cache.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Cache.Impl.ServiceLibrary\Redarbor.Core.Cache.Impl.ServiceLibrary.csproj">
      <Project>{6539a9a2-be8c-47d4-adbb-b0c863f49c54}</Project>
      <Name>Redarbor.Core.Cache.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Resolver.Contracts.ServiceLibrary\Redarbor.Core.Resolver.Contracts.ServiceLibrary.csproj">
      <Project>{27f82506-248d-4117-9dc1-2198a2187fbb}</Project>
      <Name>Redarbor.Core.Resolver.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Resolver.Impl.ServiceLibrary\Redarbor.Core.Resolver.Impl.ServiceLibrary.csproj">
      <Project>{62db8e3d-0cbf-49bb-b06c-eb64709cf099}</Project>
      <Name>Redarbor.Core.Resolver.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Counter.Contracts.ServiceLibrary\Redarbor.Core.Counters.Contracts.ServiceLibrary.csproj">
      <Project>{eec65db1-974d-4172-ba9a-735d63153264}</Project>
      <Name>Redarbor.Core.Counters.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Counter.Impl.ServiceLibrary\Redarbor.Core.Counters.Impl.ServiceLibrary.csproj">
      <Project>{451fa585-dde8-43e2-931d-9365af99ce6b}</Project>
      <Name>Redarbor.Core.Counters.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Counter.Library\Redarbor.Core.Counters.Library.csproj">
      <Project>{f664dd8c-d556-452f-9d30-ce07627cba58}</Project>
      <Name>Redarbor.Core.Counters.Library</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Curriculum.Contracts.ServiceLibrary\Redarbor.Curriculum.Contracts.ServiceLibrary.csproj">
      <Project>{61935dcf-ffa4-42ef-b825-877dc3b71bbf}</Project>
      <Name>Redarbor.Curriculum.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Curriculum.Impl.ServiceLibrary\Redarbor.Curriculum.Impl.ServiceLibrary.csproj">
      <Project>{41f5aabf-63a7-465a-a698-0c18142c1095}</Project>
      <Name>Redarbor.Curriculum.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Curriculum.Library\Redarbor.Curriculum.Library.csproj">
      <Project>{5f5c88ed-ae86-432b-8700-530a36d97bb4}</Project>
      <Name>Redarbor.Curriculum.Library</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.DIRegister.AssemblyDiscovery.ServiceLibrary\Redarbor.DIRegister.AssemblyDiscovery.ServiceLibrary.csproj">
      <Project>{7db913eb-ab01-4a1a-9e8d-1f9d5f7e88cf}</Project>
      <Name>Redarbor.DIRegister.AssemblyDiscovery.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.DIRegister.Autofac.ServiceLibrary\Redarbor.DIRegister.Autofac.ServiceLibrary.csproj">
      <Project>{b9f54550-cf57-4e5d-8383-769df8f18568}</Project>
      <Name>Redarbor.DIRegister.Autofac.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.DIRegister.WebUI.ServiceLibrary\Redarbor.DIRegister.WebUI.ServiceLibrary.csproj">
      <Project>{7c03cb2b-d18f-4367-af29-cc7e4d49f753}</Project>
      <Name>Redarbor.DIRegister.WebUI.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Elastic.Entities\Redarbor.Elastic.Entities.csproj">
      <Project>{2eed9e8d-b4f4-4797-a1be-9068a549c3a0}</Project>
      <Name>Redarbor.Elastic.Entities</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Extensions.Library\Redarbor.Extensions.Library.csproj">
      <Project>{a30c078b-2f28-42b0-84e7-e02f9c30e27e}</Project>
      <Name>Redarbor.Extensions.Library</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Invoice.Contracts.ServiceLibrary\Redarbor.Invoice.Contracts.ServiceLibrary.csproj">
      <Project>{ffc8183e-9d73-46ad-890f-1f4c8bfa187b}</Project>
      <Name>Redarbor.Invoice.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Invoice.Impl.ServiceLibrary\Redarbor.Invoice.Impl.ServiceLibrary.csproj">
      <Project>{2777dfe6-0a6c-4887-87b4-0418a7074ece}</Project>
      <Name>Redarbor.Invoice.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Kpi.Contracts.ServiceLibrary\Redarbor.Core.Kpi.Contracts.ServiceLibrary.csproj">
      <Project>{69fa56cc-33a8-445a-aa62-6bb70ed1bc70}</Project>
      <Name>Redarbor.Core.Kpi.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Kpi.Impl.ServiceLibrary\Redarbor.Core.Kpi.Impl.ServiceLibrary.csproj">
      <Project>{f773520f-2ef0-44d7-8878-28428e6218b5}</Project>
      <Name>Redarbor.Core.Kpi.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Kpi.Library\Redarbor.Core.Kpi.Library.csproj">
      <Project>{169ae7cd-0345-49b0-8a4a-8ff19445bc90}</Project>
      <Name>Redarbor.Core.Kpi.Library</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Literals.Contracts.ServiceLibrary\Redarbor.Core.Literals.Contracts.ServiceLibrary.csproj">
      <Project>{4384e179-9e10-436d-91b7-2319e4ccbc8e}</Project>
      <Name>Redarbor.Core.Literals.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Literals.Library\Redarbor.Core.Literals.Library.csproj">
      <Project>{ffcac4ec-7b1d-44cb-a061-2d81247493c8}</Project>
      <Name>Redarbor.Core.Literals.Library</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Literals.ServiceLibrary\Redarbor.Core.Literals.Impl.ServiceLibrary.csproj">
      <Project>{5F13D107-D55F-43F4-BBC3-8EC4F249B8F0}</Project>
      <Name>Redarbor.Core.Literals.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Managers.Library\Redarbor.Managers.Library.csproj">
      <Project>{717a5ca8-1168-4d9e-ae18-c456fef8c5bc}</Project>
      <Name>Redarbor.Managers.Library</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Master.CRM.Entities\Redarbor.Master.CRM.Entities.csproj">
      <Project>{6371e846-0e14-4921-b882-f22093008997}</Project>
      <Name>Redarbor.Master.CRM.Entities</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Master.Impl.ServiceLibrary\Redarbor.Master.Impl.ServiceLibrary.csproj">
      <Project>{3FEC6BB8-E0B9-4783-8877-F4F79CC7D73B}</Project>
      <Name>Redarbor.Master.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Match.Contracts.ServiceLibrary\Redarbor.Match.Contracts.ServiceLibrary.csproj">
      <Project>{28676d69-97f1-49d3-9e92-c0eb95d45426}</Project>
      <Name>Redarbor.Match.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Match.Impl.ServiceLibrary\Redarbor.Match.Impl.ServiceLibrary.csproj">
      <Project>{a45070d7-9e74-4142-bab7-f4087fc991a4}</Project>
      <Name>Redarbor.Match.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Match.Library\Redarbor.Match.Library.csproj">
      <Project>{dc9fe17b-5e6b-45be-9b2e-d894667efc09}</Project>
      <Name>Redarbor.Match.Library</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Mailing.Contracts.ServiceLibrary\Redarbor.Core.Mailing.Contracts.ServiceLibrary.csproj">
      <Project>{CDF5549A-6741-4FDE-A925-550CCB4D896B}</Project>
      <Name>Redarbor.Core.Mailing.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Mailing.Impl.ServiceLibrary\Redarbor.Core.Mailing.Impl.ServiceLibrary.csproj">
      <Project>{8e69ec0c-a7c0-4c5b-9388-d2ce82e36797}</Project>
      <Name>Redarbor.Core.Mailing.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Offer.Contracts.ServiceLibrary\Redarbor.Offer.Contracts.ServiceLibrary.csproj">
      <Project>{5df02fd7-adc3-4aa2-b8ad-f5f2d42c506f}</Project>
      <Name>Redarbor.Offer.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Offer.Library\Redarbor.Offer.Library.csproj">
      <Project>{f9322cf1-6cd6-41f4-8421-482195f3918b}</Project>
      <Name>Redarbor.Offer.Library</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.OperacionCompra.Contracts.ServiceLibrary\Redarbor.PurchaseOperation.Contracts.ServiceLibrary.csproj">
      <Project>{20E076C5-9277-4DA3-A256-85F6420DD617}</Project>
      <Name>Redarbor.PurchaseOperation.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Payment.Contracts.ServiceLibrary\Redarbor.Payment.Contracts.ServiceLibrary.csproj">
      <Project>{60e5d47d-0161-4c19-b60a-6f353b2ce937}</Project>
      <Name>Redarbor.Payment.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Payment.Impl.ServiceLibrary\Redarbor.Payment.Impl.ServiceLibrary.csproj">
      <Project>{9d0e3452-aa5b-43ff-824f-fd41c2b4d409}</Project>
      <Name>Redarbor.Payment.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Payment.Library\Redarbor.Payment.Library.csproj">
      <Project>{85bc0525-6086-4059-b9a9-915f03f89014}</Project>
      <Name>Redarbor.Payment.Library</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Products.Contracts.ServiceLibrary\Redarbor.Products.Contracts.ServiceLibrary.csproj">
      <Project>{7e7dfd45-2a07-4347-aad6-836b215f129b}</Project>
      <Name>Redarbor.Products.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Products.Impl.ServiceLibrary\Redarbor.Products.Impl.ServiceLibrary.csproj">
      <Project>{e53e025f-0bca-4e70-8efd-6c50ca9c246d}</Project>
      <Name>Redarbor.Products.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Products.Library\Redarbor.Products.Library.csproj">
      <Project>{b0546e25-69e2-4b31-9f4c-1f8c74c00586}</Project>
      <Name>Redarbor.Products.Library</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Professions.Contracts.ServiceLibrary\Redarbor.Professions.Contracts.ServiceLibrary.csproj">
      <Project>{739cd2cb-db89-4e3e-bb9d-37d31713a273}</Project>
      <Name>Redarbor.Professions.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.PurchaseOperation.Library\Redarbor.PurchaseOperation.Library.csproj">
      <Project>{c8fd4328-45c0-4dd7-bfc8-15d2df79db19}</Project>
      <Name>Redarbor.PurchaseOperation.Library</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.RatingCv.Contracts.ServiceLibrary\Redarbor.RatingCv.Contracts.ServiceLibrary.csproj">
      <Project>{2128b8bb-0460-44a2-a624-da77a6b10392}</Project>
      <Name>Redarbor.RatingCv.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.RatingCv.Impl.ServiceLibrary\Redarbor.RatingCv.Impl.ServiceLibrary.csproj">
      <Project>{415ac519-9d3b-4d66-bf2c-da932b104bc8}</Project>
      <Name>Redarbor.RatingCv.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.RatingCv.Library\Redarbor.RatingCv.Library.csproj">
      <Project>{59a4dadf-effe-4225-bdff-c50d6a956743}</Project>
      <Name>Redarbor.RatingCv.Library</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.ProProduct.Contracts.ServiceLibrary\Redarbor.ProProduct.Contracts.ServiceLibrary.csproj">
      <Project>{95891B27-0478-45A5-9AC9-642942960E51}</Project>
      <Name>Redarbor.ProProduct.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.ProProduct.Impl.ServiceLibrary\Redarbor.ProProduct.Impl.ServiceLibrary.csproj">
      <Project>{6208d59d-2662-4e7d-bf7e-b25c202a79b0}</Project>
      <Name>Redarbor.ProProduct.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Report.Contracts.ServiceLibrary\Redarbor.Report.Contracts.ServiceLibrary.csproj">
      <Project>{3572c4e9-878a-4048-8752-8a7c5f4f6f64}</Project>
      <Name>Redarbor.Report.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Report.Impl.ServiceLibrary\Redarbor.Report.Impl.ServiceLibrary.csproj">
      <Project>{2cda2b2e-5e0c-49fe-8506-cc34c5b177ae}</Project>
      <Name>Redarbor.Report.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Report.Library\Redarbor.Report.Library.csproj">
      <Project>{50043350-ae28-4269-88d6-e765a71ff7ad}</Project>
      <Name>Redarbor.Report.Library</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.SEO.Contracts.ServiceLibrary\Redarbor.Core.SEO.Contracts.ServiceLibrary.csproj">
      <Project>{34dee7e6-6f70-4f76-bead-27e058716d79}</Project>
      <Name>Redarbor.Core.SEO.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.SEO.Library\Redarbor.Core.SEO.Library.csproj">
      <Project>{1c97cfd3-d43b-49d5-afe6-505eb2e1e492}</Project>
      <Name>Redarbor.Core.SEO.Library</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Stack.Contracts.ServiceLibrary\Redarbor.Core.Stack.Contracts.ServiceLibrary.csproj">
      <Project>{89aacf3b-ac86-48cc-97e9-15bc5c6543e7}</Project>
      <Name>Redarbor.Core.Stack.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Stack.Impl.ServiceLibrary\Redarbor.Core.Stack.Impl.ServiceLibrary.csproj">
      <Project>{7df7bbf8-215d-494a-bb30-9fd1e13ead8b}</Project>
      <Name>Redarbor.Core.Stack.Impl.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Stack.Library\Redarbor.Core.Stack.Library.csproj">
      <Project>{9924f3e7-6df9-4a28-b85f-4dc59a919e9e}</Project>
      <Name>Redarbor.Core.Stack.Library</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.TrackingActions.Library\Redarbor.Core.TrackingActions.Library.csproj">
      <Project>{37adceee-0f75-46cb-a439-5118e36da5fb}</Project>
      <Name>Redarbor.Core.TrackingActions.Library</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.User.Contracts.ServiceLibrary\Redarbor.User.Contracts.ServiceLibrary.csproj">
      <Project>{79e50e19-d3c9-4d95-890c-ec9e9b1a5b1c}</Project>
      <Name>Redarbor.User.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Users.Library\Redarbor.Users.Library.csproj">
      <Project>{777498b3-dafa-49f1-8058-ed3b11c15e10}</Project>
      <Name>Redarbor.Users.Library</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Web.Library\Redarbor.Web.Library.csproj">
      <Project>{35da4d6e-7d61-4d9a-a05a-e7c562abb889}</Project>
      <Name>Redarbor.Web.Library</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Web.UI.Library\Redarbor.Web.UI.Library.csproj">
      <Project>{1fa1076c-a8ba-489f-b263-e5d81079c004}</Project>
      <Name>Redarbor.Web.UI.Library</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{4A0DDDB5-7A95-4FBF-97CC-616D07737A77}" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Connected Services\" />
  </ItemGroup>
  <ItemGroup>
    <Analyzer Include="..\packages\AWSSDK.S3.3.3.19\analyzers\dotnet\cs\AWSSDK.S3.CodeAnalysis.dll" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.targets" Condition="Exists('$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.targets')" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>False</AutoAssignPort>
          <DevelopmentServerPort>62358</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:62360/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
      <UserProperties bundleconfig_1json__JsonSchema="" />
    </VisualStudio>
  </ProjectExtensions>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\BuildBundlerMinifier.3.2.435\build\BuildBundlerMinifier.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\BuildBundlerMinifier.3.2.435\build\BuildBundlerMinifier.targets'))" />
  </Target>
  <Import Project="..\packages\BuildBundlerMinifier.3.2.435\build\BuildBundlerMinifier.targets" Condition="Exists('..\packages\BuildBundlerMinifier.3.2.435\build\BuildBundlerMinifier.targets')" />
  <Target Name="MvcBuildViews" AfterTargets="AfterBuild" Condition="'$(MvcBuildViews)'=='true'">
    <AspNetCompiler VirtualPath="temp" PhysicalPath="$(WebProjectOutputDir)" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>