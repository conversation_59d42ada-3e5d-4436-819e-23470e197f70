using Autofac.Integration.Mvc;
using Redarbor.Core.Timeline.Impl.ServiceLibrary.Extensions;
using Redarbor.AccessControl.ServiceLibrary.CrossCutting.Configurations;
using Redarbor.AccessControl.ServiceLibrary.CrossCutting.Repositories;
using Redarbor.AccessControl.ServiceLibrary.Repositories;
using Redarbor.AccessControl.ServiceLibrary.Services;
using Redarbor.Company.WebUI.Helpers;
using Redarbor.Company.WebUI.Validation;
using Redarbor.Configuration.Library;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary.Entity.ExceptionsConsumer;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary.Enums.ExceptionsConsumer;
using Redarbor.Core.Computrabajo.Impl.ServiceLibrary;
using Redarbor.Core.Literals.Library.DomainServicesImplementations;
using Redarbor.Core.Literals.ServiceLibrary;
using Redarbor.Core.Resolver.Contracts.ServiceLibrary;
using Redarbor.Core.Resolver.ServiceLibrary;
using Redarbor.Core.Shared.Impl.ServiceLibrary;
using Redarbor.DIRegister.AssemblyDiscovery.ServiceLibrary.DTO;
using Redarbor.Extensions.Library.Extensions;
using Redarbor.Master.Contracts.ServiceLibrary;
using Redarbor.Master.Impl.ServiceLibrary;
using Redarbor.Match.Impl.ServiceLibrary.MatchElasticConsumer;
using Redarbor.Matches.Elastic.Consumer.Services;
using Redarbor.Web.Library.MVC;
using Redarbor.Web.UI.Library.Controllers;
using Redarbor.Web.UI.Library.Exceptions;
using Redarbor.Web.UI.Library.ViewEngine;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net;
using System.Reflection;
using System.Web;
using System.Web.Helpers;
using System.Web.Mvc;
using System.Web.Optimization;
using System.Web.Routing;
using System.Web.Security;
using Redarbor.Message.Jobads.Chat.Impl.ServiceLibrary;
using Redarbor.DataAnalytics.ServiceLibrary.ServiceContracts;
using Redarbor.Surveys.Contracts.ServiceLibrary;
using Redarbor.Tools.Infraestructure.Core.MySQL.Data.MySQL;
using Redarbor.Professions.Contracts.ServiceLibrary;

namespace Redarbor.Company.WebUI
{
    public class MvcApplication : HttpApplication
    {
        public static void RegisterModelBinders()
        {
            RegisterGlobalizedModelBinderFor<DateTime>(default(DateTime));
            RegisterGlobalizedModelBinderFor<DateTime?>(default(DateTime));
            RegisterGlobalizedModelBinderFor<Decimal>(default(Decimal));
            RegisterGlobalizedModelBinderFor<Decimal?>(default(Decimal));
        }

        public static void RegisterGlobalizedModelBinderFor<T>(T sameAsTypeParameter)
        {
            ModelBinders.Binders.Add(sameAsTypeParameter.GetType(), new CurrentUICultureModelBinder<T>());
        }

        public static void RegisterGlobalFilters(GlobalFilterCollection filters)
        {

        }

        private static void RegisterDependencies()
        {
            var registerDefinition = BuildRegisterDefinition();
            var registrator = new ReflectionRegistrator();

            registrator.EnableVerboseTrace();
            registrator.RegisterDependencies(registerDefinition);
        }

        private static RegisterDefinition BuildRegisterDefinition()
        {            
            return new RegisterDefinition
            {
                ExecutingAssembly = Assembly.GetExecutingAssembly(),
                ConfigurationLifetimeScope = LifetimeScopes.SingleInstance,
                DbContextLifetimeScope = LifetimeScopes.InstancePerLifetimeScope,
                IgnoreTypes = new List<Type>() { typeof(IPortalResolverService),
                    typeof(IApplicationIdResolverService),
                    BuilderExtensions.GetTypeJobadsChatService(),
                    BuilderLiteralExtensions.GetTypeLiteralsService(),
                    BuilderLiteralExtensions.GetTypeLiteralsConsumerService(),
                    BuilderExtensionTimeline.GetTypeITimelineService(),
                    BuilderExtensionTimeline.GetTimelineMessageService(),
                    BuilderExtensionTimeline.GetRedarborTimelineConsumerService(),
                    BuilderExtensionTimeline.GetRedarborTimelineConsumerConfiguration(),
                    typeof(Redarbor.Cache.Services.IRedarborCacheService),
                    typeof(Redarbor.Cache.Configuration.IRedarborCacheConfiguration),
                    typeof(Redarbor.Cache.Disk.Services.IRedarborCacheDiskService),
                    typeof(Redarbor.Cache.Runtime.Services.IRedarborCacheRuntimeService),
                    typeof(Redarbor.Dictionaries.Consumer.Services.IRedarborDictionaryService),
                    typeof(Redarbor.Dictionaries.Consumer.Services.IDictionariesApiService),
                    typeof(Redarbor.Dictionaries.Consumer.Configuration.IDictionariesApiConfiguration),
                    typeof(Redarbor.KillerQuestions.Consumer.Services.IRedarborKillerQuestionsService),
                    typeof(Redarbor.KillerQuestions.Consumer.Services.IKillerQuestionApiService),
                    typeof(IDatabaseContext),
                    typeof(IProfessionService)

                },
                AdditionalEntryServices = new List<Type>()
                {
                    typeof(ConfigurationService),
                    typeof(PortalByHostService),
                    typeof(IExceptionPublisherNoPortalDependencyService),
                    typeof(ConnectionStringResolverService),
                    typeof(PortalConfigurationService),
                    typeof(LiteralsRecoverAndPersist),
                    typeof(AccessControlManagementService),
                    typeof(AccessControlManagementRepository),
                    typeof(AccessControlConfiguration),
                    typeof(CaptchaService),
                    typeof(CaptchaConfiguration),
                    typeof(CaptchaValidationService),
                    typeof(AccessSpecificControl),
                    typeof(CaptchaRepository),
                    typeof(CommonExceptionRepository),
                    typeof(AccessControlRulesService),
                    typeof(MatchElasticConsumerService),
                    typeof(BlockingPageConfiguration),
                    typeof(BlockingPageService),
                    typeof(BlockingPageRepository),
                    typeof(QRCodeGeneratorService),
                    typeof(RequestDeviceRecorder),
                    typeof(IDataAnalyticsService),
                    typeof(ISurveysService)
                },
                DefaultServiceLifetimeScope = LifetimeScopes.InstancePerDependency,
                InstanciateSingleInstanceServicesAfterRegister = true,
                InstanciateAllServicesAfterRegister = false
            };
        }

        protected void Application_BeginRequest()
        {
        }

        private static void InizializeExceptions()
        {

            var exceptionApiEndPoint = ConfigurationManager.AppSettings["ExceptionApiEndPoint"] ?? string.Empty;
            var environment = ConfigurationManager.AppSettings["ENVIRONMENT"] ?? string.Empty;

            ExceptionsConfigurator.InicializeExceptionsConfigurator(new ConfigurationExceptionModel()
            {
                apiEndPoint = exceptionApiEndPoint,
                appEnum = AppEnum.AreaEmpresa,
                areaEnum = ExceptionAreaEnum.Empresa,
                bussinessEnum = ExceptionBusinessEnum.CompuTrabajo,
                appTypeEnum = ExceptionAppTypeEnum.Web,
                //quizas la IP tb no el Dns
                ipAddress = Dns.GetHostName(),
                environmentEnum = environment.ParseToEnum(EnvironmentEnum.Development),
                SaveInElastic = ConfigurationManager.AppSettings["ExceptionSaveInElastic"]?.ToBoolean() ?? true
            });
        }


        protected void Application_Start()
        {
            AntiForgeryConfig.CookieName = "__RequestVerificationTokenAE";

            string site_id = Path.GetFileName(Path.GetDirectoryName(HttpRuntime.AppDomainAppPath));
            RedarborEnvironment.InitializeCurrentForApplication($"{ConfigurationManager.AppSettings["APPLICATION_ID"]}_{site_id}",
                mainDirectory: ConfigurationManager.AppSettings["ENVIRONMENT_FOLDER_PATH"]);

            SetDefaultCulture();

            //HostingEnvironment.RegisterVirtualPathProvider(new EmbeddedVirtualPathProvider());

            AreaRegistration.RegisterAllAreas();
            RegisterGlobalFilters(GlobalFilters.Filters);
            RouteConfig.RegisterRoutes(RouteTable.Routes);

            ServicePointManager.SecurityProtocol |= SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12;

            ViewEngines.Engines.Clear();
            ViewEngines.Engines.Add(new RedarborRazorViewEngine());

            var provider = ModelValidatorProviders.Providers.FirstOrDefault(p => p.GetType() == typeof(DataAnnotationsModelValidatorProvider));
            if (provider != null)
                ModelValidatorProviders.Providers.Remove(provider);
            ModelValidatorProviders.Providers.Add(new RedarborModelValidationProvider());

            RegisterDependencies();

            BundleConfig.RegisterBundles(BundleTable.Bundles);

            AutoMapperConfig.Initialize();

            InizializeExceptions();
            AdaptorMatchElasticConsumerConfigurator.Initializer(ConfigurationManager.AppSettings["ApiMatchElastic"]);
            InitialApplicationTracking();
        }

        private void InitialApplicationTracking()
        {
            try
            {
                var buildVersion = ConfigurationManager.AppSettings["BUILD_VERSION"];
                var portalId = GetPortalIdByDefault();
                var exceptionPublisherService = (ExceptionPublisherNoPortalDependencyService)DependencyResolver.Current.GetService(typeof(IExceptionPublisherNoPortalDependencyService));
                exceptionPublisherService.PublishInfo(new Exception($"Application AE running with slot {portalId} and release version {buildVersion}"),
                                                        "Global.asax", "PublishInfo");
            }
            catch (Exception ex)
            {
                Trace.TraceError($"Application could not be started. (InitialApplicationTracking) {ex}");
            }
        }

        private short GetPortalIdByDefault()
        {
            short portalId = 0;

            var portalIdByDefault = ConfigurationManager.AppSettings["ID_PORTAL"];

            if (!string.IsNullOrEmpty(portalIdByDefault))
            {
                short.TryParse(portalIdByDefault, out portalId);
            }

            if (portalId <= 0)
            {
                var fileName = Path.GetFileName(Path.GetDirectoryName(HttpRuntime.AppDomainAppPath)).ToUpper();
                var PortalSetByDefault = ConfigurationManager.AppSettings[$"{fileName}_PATH_DOMAIN"];

                if (!string.IsNullOrEmpty(PortalSetByDefault))
                {
                    short.TryParse(PortalSetByDefault, out portalId);
                }
            }

            return portalId;
        }


        private static void SetDefaultCulture()
        {
            var defaultCulture = "es-MX";
            var defaultUICulture = "es";
            var isBestJobsByIdPortalDefault = RedarborEnvironment.IsBestJobsByIdPortalDefault(ConfigurationManager.AppSettings["ID_PORTAL"]);

            if (RedarborEnvironment.HasCultureEnglish(ConfigurationManager.AppSettings["CULTURE_ENGLISH_PATH_DOMAIN"],
                 HttpRuntime.AppDomainAppPath)
                || isBestJobsByIdPortalDefault)
            {
                defaultCulture = "en-US";
                defaultUICulture = "en";
            }

            System.Globalization.CultureInfo.DefaultThreadCurrentCulture = new System.Globalization.CultureInfo(defaultCulture);
            System.Globalization.CultureInfo.DefaultThreadCurrentUICulture = new System.Globalization.CultureInfo(defaultUICulture);
        }

        protected void Application_EndRequest(object sender, EventArgs ev)
        {
        }

        protected void Application_Error(object sender, EventArgs e)
        {
            Exception exception = Server.GetLastError();
            Response.Clear();

            if (exception == null) { return; }

            if (Request.RawUrl != null && Request.RawUrl.StartsWith("/.well-known/", StringComparison.OrdinalIgnoreCase))
            {
                return;
            }

            if (HttpContext.Current == null || AutofacDependencyResolver.Current == null)
            {
                // errors in Application_Start will end up here   
                Trace.TraceError($"Application could not be started.{exception}");
            }
            else
            {
                if (exception is FormsAuthenticationExpiredException)
                    ApplyFormsAuthenticationExpiredExceptionTraitament();
                else if (exception.Message.Contains("The anti-forgery token could not be decrypted."))
                    ApplyAntiForgeryExceptionTraitament();
                else
                {
                    ApplyHttpExceptionTraitament(exception);
                    Trace.TraceError($"Application_Error: {exception}");
                    Server.ClearError();
                    LogException(exception);
                }
            }
        }

        protected void Session_Start(Object sender, EventArgs e)
        {
            /* When using cookie - based session state, ASP.NET does not allocate 
             * storage for session data until the Session object is used.
             * As a result, a new session ID is generated for each page request 
             * until the session object is accessed.
             * If your application requires a static session ID for the entire session, 
             * you can either implement the Session_Start method in the application's Global.asax file 
             * and store data in the Session object to fix the session ID, or you can use code in another 
             * part of your application to explicitly store data in the Session object.*/

            Session["init"] = 0;
        }

        private void ApplyFormsAuthenticationExpiredExceptionTraitament()
        {
            FormsAuthentication.SignOut();
            FormsAuthentication.RedirectToLoginPage();
            Server.ClearError();
        }

        private void ApplyAntiForgeryExceptionTraitament()
        {
            var formsAuthenticationService = ((FormsAuthenticationService)AutofacDependencyResolver.Current.GetService(typeof(IFormsAuthenticationService)));

            formsAuthenticationService.LogOff();
            FormsAuthentication.RedirectToLoginPage();

            Server.ClearError();
        }

        private void ApplyHttpExceptionTraitament(Exception exception)
        {
            if (exception is HttpException)
            {
                switch (((HttpException)exception).GetHttpCode())
                {
                    case 404:
                        // page not found
                        ExecuteActionError(exception, "HttpError404");
                        Trace.TraceError($"Exception 404 {exception.Message}");
                        break;
                }
            }
        }

        private void LogException(Exception exception)
        {
            if (exception.Message.Contains("File does not exist"))
            {
                Trace.TraceError(string.Format(CultureInfo.InvariantCulture, "File does not exist. Requested url: {0}", Request.RawUrl));
            }
            else
            {
                var extradata = new Dictionary<string, string>
                {
                    { "UrlReferrer", Request.UrlReferrer != null ? Request.UrlReferrer.ToString() : string.Empty }
                };
                var exceptionPublisherService = ((ExceptionPublisherService)AutofacDependencyResolver.Current
                    .GetService(typeof(IExceptionPublisherService)));

                if (CheckWarningExceptions(exception))
                {
                    exceptionPublisherService.PublishWarning(exception, HttpContext.Current.Request.AppRelativeCurrentExecutionFilePath, extradata);
                }
                else
                {
                    Trace.TraceError(exception.ToString());
                    exceptionPublisherService.Publish(exception, HttpContext.Current.Request.AppRelativeCurrentExecutionFilePath, "", false, extradata);
                }
            }
        }

        private void ExecuteActionError(Exception exception, string actionError = "UnHandled")
        {
            var routeData = new RouteData();
            routeData.Values["controller"] = "Errors";
            routeData.Values["action"] = actionError;
            routeData.Values["exception"] = exception;

            IController errorsController = new ErrorsController();
            var rc = new RequestContext(new HttpContextWrapper(Context), routeData);
            errorsController.Execute(rc);
        }

        private bool CheckWarningExceptions(Exception exception)
        {
            return exception.Message.Contains("was not found or does not implement IController")
                || exception.Message.Contains("No se encuentra el controlador de la ruta")
                || exception.Message.Contains("The provided anti-forgery token was meant for user")
                || exception.Message.Contains("El token antifalsificación proporcionado se diseñó para el usuario")
                || exception.Message.Contains("La cookie antifalsificación requerida")
                || exception.Message.Contains("The required anti-forgery form field")
                || exception.Message.Contains("A potentially dangerous Request.Form value was detected from the client")
                || exception.Message.Contains("El campo de formulario y el token de la cookie antifalsficación no coinciden")
                || exception.Message.Contains("A potentially dangerous Request.QueryString value was detected from the client")
                || Request.UrlReferrer == null;
        }
    }
}
