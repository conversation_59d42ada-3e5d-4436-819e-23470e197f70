CREATE DATABASE  IF NOT EXISTS `repo_tracking` /*!40100 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci */ /*!80016 DEFAULT ENCRYPTION='N' */;
USE `repo_tracking`;
-- MySQL dump 10.13  Distrib 8.0.41, for Win64 (x86_64)
--
-- Host: pre-repo-srv.ct.local    Database: repo_tracking
-- ------------------------------------------------------
-- Server version	8.0.26

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
SET @MYSQLDUMP_TEMP_LOG_BIN = @@SESSION.SQL_LOG_BIN;
SET @@SESSION.SQL_LOG_BIN= 0;
--
-- Table structure for table `ApplicationRequests`
--

DROP TABLE IF EXISTS `ApplicationRequests`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ApplicationRequests` (
  `Id` bigint NOT NULL AUTO_INCREMENT,
  `CreatedOn` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `IdCompany` int NOT NULL,
  `IdOffer` int NOT NULL,
  `Status` int NOT NULL DEFAULT '0',
  `Request` longtext NOT NULL,
  `ElapsedMilliseconds` int NOT NULL DEFAULT '0',
  `IdPortal` tinyint NOT NULL DEFAULT '0',
  `IdApp` int NOT NULL DEFAULT '0',
  `Controller` varchar(255) DEFAULT NULL,
  `Method` varchar(255) DEFAULT NULL,
  `HttpMethod` varchar(20) DEFAULT NULL,
  `RequestUri` text,
  PRIMARY KEY (`Id`),
  KEY `idx_group1` (`Controller`,`Method`),
  KEY `idx_createdon` (`CreatedOn`),
  KEY `IDX_APPID` (`IdApp`)
) ENGINE=InnoDB AUTO_INCREMENT=1003872882 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
/*!50100 PARTITION BY KEY ()
PARTITIONS 12 */;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `CvViewed`
--

DROP TABLE IF EXISTS `CvViewed`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `CvViewed` (
  `IdPortal` tinyint NOT NULL,
  `IdCompany` int NOT NULL,
  `IdOffer` int NOT NULL,
  `IdUser` int NOT NULL,
  `IdLead` int DEFAULT NULL,
  `IdCv` bigint NOT NULL,
  `CvName` varchar(200) DEFAULT NULL,
  `CvProfession` varchar(200) DEFAULT NULL,
  `OfferName` varchar(300) DEFAULT NULL,
  `IdOriginTracking` tinyint DEFAULT NULL,
  `DateViewedInt` bigint DEFAULT NULL,
  PRIMARY KEY (`IdPortal`,`IdCompany`,`IdOffer`,`IdUser`,`IdCv`),
  KEY `cvViewedIndex` (`IdPortal`,`IdCompany`,`IdUser`),
  KEY `pk_idlead_idportal` (`IdLead`,`IdPortal`),
  KEY `idx_DateViewedInt` (`DateViewedInt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `CvVisualizationControl`
--

DROP TABLE IF EXISTS `CvVisualizationControl`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `CvVisualizationControl` (
  `IdCompany` int NOT NULL,
  `IdCandidate` int NOT NULL,
  `IdPortal` tinyint NOT NULL,
  `IdType` tinyint NOT NULL,
  `IdCv` int NOT NULL,
  `DateInt` int NOT NULL,
  `StatusId` tinyint NOT NULL,
  `IdOffer` int NOT NULL DEFAULT '0',
  `IsCompanyVisible` tinyint(1) NOT NULL DEFAULT '0',
  `CreationDate` timestamp NULL DEFAULT NULL,
  `IP` varchar(255) DEFAULT NULL,
  `IdApp` tinyint DEFAULT NULL,
  `IdCompanyUser` int DEFAULT NULL,
  PRIMARY KEY (`IdCompany`,`IdCandidate`,`IdPortal`,`IdType`,`IdCv`),
  KEY `idx_select` (`IdCandidate`,`IdPortal`,`DateInt`,`IdType`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `CvVisualizationCounters`
--

DROP TABLE IF EXISTS `CvVisualizationCounters`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `CvVisualizationCounters` (
  `IdCandidate` int NOT NULL,
  `IdPortal` tinyint NOT NULL,
  `DateInt` int NOT NULL,
  `Total` int NOT NULL,
  PRIMARY KEY (`IdCandidate`,`IdPortal`,`DateInt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `CvVisualizationHistory`
--

DROP TABLE IF EXISTS `CvVisualizationHistory`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `CvVisualizationHistory` (
  `IdCompany` int NOT NULL,
  `IdCandidate` int NOT NULL,
  `IdPortal` tinyint NOT NULL,
  `IdType` tinyint NOT NULL,
  `DateShowed` int NOT NULL,
  `IsCompanyVisible` tinyint NOT NULL,
  PRIMARY KEY (`IdCompany`,`IdCandidate`,`IdType`,`IdPortal`,`DateShowed`),
  KEY `idx_type_candidate` (`IdType`,`IdCandidate`),
  KEY `idx_portal_date_cand` (`IdPortal`,`DateShowed`,`IdCandidate`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `CvVisualizationRaw`
--

DROP TABLE IF EXISTS `CvVisualizationRaw`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `CvVisualizationRaw` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `IdCompany` int NOT NULL,
  `IdCandidate` int NOT NULL,
  `IdPortal` tinyint NOT NULL,
  `IdType` tinyint NOT NULL,
  `IdOffer` int NOT NULL,
  `IsCompanyVisible` tinyint NOT NULL,
  `CreationDate` timestamp NULL DEFAULT NULL,
  `IP` varchar(255) DEFAULT NULL,
  `IdApp` tinyint DEFAULT NULL,
  `IdCompanyUser` int DEFAULT NULL,
  `DateInt` int DEFAULT NULL,
  `IdCv` bigint DEFAULT NULL,
  PRIMARY KEY (`Id`),
  KEY `IDX_PORTAL_DATEINT` (`IdPortal`,`DateInt`)
) ENGINE=InnoDB AUTO_INCREMENT=18235 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `CvVisualizationSearchFilters`
--

DROP TABLE IF EXISTS `CvVisualizationSearchFilters`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `CvVisualizationSearchFilters` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `IdCompany` int NOT NULL,
  `IdCandidate` int NOT NULL,
  `IdPortal` tinyint NOT NULL,
  `Query` varchar(255) NOT NULL,
  `IdsCategory` varchar(255) NOT NULL,
  `IdsProvince` varchar(255) NOT NULL,
  `DateInt` int NOT NULL,
  PRIMARY KEY (`Id`),
  UNIQUE KEY `UK_CvVisualizationSearchFilters` (`IdCompany`,`IdCandidate`,`IdPortal`,`DateInt`),
  KEY `idx_portal_candidate` (`IdPortal`,`IdCandidate`)
) ENGINE=InnoDB AUTO_INCREMENT=24705180 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci AVG_ROW_LENGTH=4096;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `CvVisualizationType`
--

DROP TABLE IF EXISTS `CvVisualizationType`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `CvVisualizationType` (
  `Id` int NOT NULL,
  `Name` varchar(50) NOT NULL,
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `CvVisualizationsTotals`
--

DROP TABLE IF EXISTS `CvVisualizationsTotals`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `CvVisualizationsTotals` (
  `IdCandidate` int NOT NULL,
  `IdPortal` tinyint NOT NULL,
  `TotalShowed` int NOT NULL,
  `TotalNewShowed` int NOT NULL,
  `CompanyDateLastShowed` int NOT NULL,
  `CandidateDateLastShowed` int NOT NULL,
  `NotificationSentAt` int NOT NULL DEFAULT '0',
  PRIMARY KEY (`IdCandidate`,`IdPortal`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `LeadStageTimeLapse`
--

DROP TABLE IF EXISTS `LeadStageTimeLapse`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `LeadStageTimeLapse` (
  `IdCompany` bigint NOT NULL,
  `IdCv` bigint NOT NULL,
  `IdOffer` bigint NOT NULL,
  `IdLead` bigint NOT NULL,
  `IdPortal` tinyint NOT NULL,
  `IdStage` tinyint NOT NULL,
  `DateUpdate` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`IdCompany`,`IdCv`,`IdOffer`,`IdLead`,`IdPortal`,`IdStage`),
  KEY `idx_1` (`IdLead`,`IdPortal`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `LeadStagesHistory`
--

DROP TABLE IF EXISTS `LeadStagesHistory`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `LeadStagesHistory` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `IdLead` bigint NOT NULL,
  `DateInt` bigint NOT NULL,
  `IdStage` tinyint NOT NULL,
  `MovementType` tinyint NOT NULL,
  PRIMARY KEY (`Id`),
  KEY `IDX_LEAD_DATE_STAGE` (`IdLead`,`DateInt`,`IdStage`)
) ENGINE=InnoDB AUTO_INCREMENT=********* DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `MatchOfferMail`
--

DROP TABLE IF EXISTS `MatchOfferMail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `MatchOfferMail` (
  `Id` bigint NOT NULL AUTO_INCREMENT,
  `IdMatchesOfferMailSettings` bigint NOT NULL,
  `IdMatch` bigint NOT NULL,
  `IdCandidate` int NOT NULL,
  `IdOffer` int NOT NULL,
  `IdPortal` tinyint NOT NULL,
  `IdCompany` int NOT NULL,
  `MatchDate` datetime NOT NULL,
  `MatchDateInt` int NOT NULL,
  `IdStatus` tinyint NOT NULL,
  PRIMARY KEY (`Id`),
  KEY `idx_MatchOfferMail_IdOffer_IdCompany_MatchDateInt_PortalId` (`IdMatchesOfferMailSettings`,`MatchDateInt`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci AVG_ROW_LENGTH=54;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `MatchesOfferMailSettings`
--

DROP TABLE IF EXISTS `MatchesOfferMailSettings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `MatchesOfferMailSettings` (
  `Id` bigint NOT NULL AUTO_INCREMENT,
  `IdOffer` int NOT NULL,
  `IdCompany` int NOT NULL,
  `IdPortal` tinyint NOT NULL,
  `IdUser` bigint NOT NULL,
  `CreatedOn` datetime NOT NULL,
  `ModifiedOn` datetime DEFAULT NULL,
  `IdStatus` tinyint NOT NULL DEFAULT '1',
  `CreatedOnInt` int NOT NULL,
  `AvailableMails` int NOT NULL,
  `ConsumedMails` int NOT NULL DEFAULT '0',
  PRIMARY KEY (`Id`),
  UNIQUE KEY `uidx_MatchesOffer_OfferId_CompanyId_PortalId` (`IdOffer`,`IdCompany`,`IdPortal`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci AVG_ROW_LENGTH=80;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `OfferMatchForNotification`
--

DROP TABLE IF EXISTS `OfferMatchForNotification`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `OfferMatchForNotification` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `IdMatch` bigint NOT NULL,
  `IdCandidate` int NOT NULL,
  `IdOffer` int NOT NULL,
  `IdPortal` tinyint NOT NULL,
  `IdCompany` int NOT NULL,
  `MatchDate` datetime NOT NULL,
  `MatchDateInt` int NOT NULL,
  `IdStatus` tinyint NOT NULL,
  PRIMARY KEY (`Id`),
  KEY `idx_MatchForNotification_IdOffer_IdCompany_MatchDateInt_PortalId` (`IdOffer`,`IdCompany`,`IdPortal`,`MatchDateInt`),
  KEY `idx_MatchForNotification_Portal_Company_MatchDate_Status` (`IdPortal`,`IdCompany`,`MatchDateInt`,`IdStatus`)
) ENGINE=InnoDB AUTO_INCREMENT=3426 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `OfferMatchesSummarySubscription`
--

DROP TABLE IF EXISTS `OfferMatchesSummarySubscription`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `OfferMatchesSummarySubscription` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `IdOffer` int NOT NULL,
  `IdCompany` int NOT NULL,
  `IdPortal` tinyint NOT NULL,
  `IdUser` int NOT NULL,
  `IdType` tinyint NOT NULL,
  `CreatedOn` datetime NOT NULL,
  `ModifiedOn` datetime NOT NULL,
  `IdStatus` tinyint NOT NULL DEFAULT '2',
  `CreatedOnInt` int NOT NULL,
  PRIMARY KEY (`Id`),
  UNIQUE KEY `uidx_OfferMatches_OfferId_CompanyId_PortalId` (`IdOffer`,`IdCompany`,`IdPortal`),
  KEY `idx_OfferMatches_OfferId_CompanyId_PortalId_IdType` (`IdOffer`,`IdCompany`,`IdPortal`,`IdType`)
) ENGINE=InnoDB AUTO_INCREMENT=42 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `OfferStatisticsData`
--

DROP TABLE IF EXISTS `OfferStatisticsData`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `OfferStatisticsData` (
  `IdOffer` int NOT NULL,
  `IdCompany` int NOT NULL,
  `IdPortal` tinyint NOT NULL,
  `HasKillerQuestions` bit(1) NOT NULL DEFAULT b'0',
  `HasAdequacy` bit(1) NOT NULL DEFAULT b'0',
  `HasExclusiveFilters` bit(1) NOT NULL DEFAULT b'0',
  `NumFolders` tinyint NOT NULL DEFAULT '0',
  `NumTests` tinyint NOT NULL DEFAULT '0',
  `NumTestsSent` int NOT NULL DEFAULT '0',
  `NumTestsFinished` int NOT NULL DEFAULT '0',
  `NumPersonalizedTest` tinyint NOT NULL DEFAULT '0',
  `NumPersonalizedTestSent` int NOT NULL DEFAULT '0',
  `NumPersonalizedTestFinished` int NOT NULL DEFAULT '0',
  `NumVideoInterviews` tinyint NOT NULL DEFAULT '0',
  `NumVideoInterviewsSent` int NOT NULL DEFAULT '0',
  `NumVideoInterviewsFinished` int NOT NULL DEFAULT '0',
  `NumInterviewKits` tinyint NOT NULL DEFAULT '0',
  `NumIntegrators` tinyint NOT NULL DEFAULT '0',
  `NumSocialNetworks` tinyint NOT NULL DEFAULT '0',
  `PublishedOnCT` bit(1) NOT NULL DEFAULT b'0',
  `PublishedOnIndeed` bit(1) NOT NULL DEFAULT b'0',
  `PublishedOnLinkedIn` bit(1) NOT NULL DEFAULT b'0',
  `IdStatus` tinyint NOT NULL DEFAULT '0',
  `TotalLeads` int NOT NULL DEFAULT '0',
  `TotalLeadsFromCT` int NOT NULL DEFAULT '0',
  `TotalLeadsFromLinkedIn` int NOT NULL DEFAULT '0',
  `TotalLeadsFromIndeed` int NOT NULL DEFAULT '0',
  `TotalLeadsFromFacebookRRSS` int NOT NULL DEFAULT '0',
  `TotalLeadsFromTwitterRRSS` int NOT NULL DEFAULT '0',
  `TotalLeadsFromLinkedInRRSS` int NOT NULL DEFAULT '0',
  `TotalLeadsFromOtherAggregators` int NOT NULL DEFAULT '0',
  `TotalLeadsFromFacebookJobs` int NOT NULL DEFAULT '0',
  PRIMARY KEY (`IdOffer`),
  KEY `Idx_CompanyPortal` (`IdPortal`,`IdCompany`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `OfferStatisticsStack`
--

DROP TABLE IF EXISTS `OfferStatisticsStack`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `OfferStatisticsStack` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `IdOffer` int NOT NULL,
  `IdType` tinyint NOT NULL DEFAULT '1',
  `IdStatus` tinyint NOT NULL DEFAULT '0',
  `CreatedOn` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `NumHits` int NOT NULL DEFAULT '1',
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IdOffer_UNIQUE` (`IdOffer`,`IdType`)
) ENGINE=InnoDB AUTO_INCREMENT=8173746 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `OfferViewed`
--

DROP TABLE IF EXISTS `OfferViewed`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `OfferViewed` (
  `IdPortal` tinyint NOT NULL,
  `IdCompany` bigint NOT NULL,
  `IdUser` bigint NOT NULL,
  `IdOffer` bigint NOT NULL,
  `OfferName` varchar(200) NOT NULL,
  `OfferCity` varchar(100) NOT NULL DEFAULT '',
  `DateViewedInt` bigint NOT NULL,
  PRIMARY KEY (`IdPortal`,`IdCompany`,`IdUser`,`IdOffer`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `OnlineNotificationTrackingCompany`
--

DROP TABLE IF EXISTS `OnlineNotificationTrackingCompany`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `OnlineNotificationTrackingCompany` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `IdApplication` int NOT NULL,
  `ApplicationDescription` varchar(250) NOT NULL,
  `IdNotificationChannel` int NOT NULL DEFAULT '0',
  `NotificationChannelDescription` varchar(250) NOT NULL,
  `IdNotificationType` int NOT NULL DEFAULT '0',
  `NotificationTypeDescription` varchar(250) NOT NULL,
  `IdPortal` int NOT NULL DEFAULT '0',
  `IdNewsletter` int NOT NULL DEFAULT '0',
  `EmailTo` varchar(250) DEFAULT NULL,
  `Subject` varchar(250) DEFAULT NULL,
  `From` varchar(250) DEFAULT NULL,
  `Origin` mediumtext,
  `IdStatus` tinyint NOT NULL DEFAULT '1',
  `StatusDescription` varchar(250) NOT NULL,
  `StatusReason` varchar(250) DEFAULT NULL,
  `CreatedOnUtc` datetime DEFAULT CURRENT_TIMESTAMP,
  `UpdatedOnUtc` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `ConsumeType` tinyint DEFAULT NULL,
  `ConsumeTypeDescription` varchar(50) DEFAULT NULL,
  `ExtraInformation` text,
  `ContentBody` text,
  `IdCompany` int NOT NULL DEFAULT '0',
  `IdUser` int DEFAULT '0',
  PRIMARY KEY (`Id`),
  KEY `INDEX_ONTRACK_COMPANY_1` (`IdApplication`,`IdPortal`,`IdNewsletter`,`IdCompany`)
) ENGINE=InnoDB AUTO_INCREMENT=91194 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `TimeUseByUser`
--

DROP TABLE IF EXISTS `TimeUseByUser`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `TimeUseByUser` (
  `IdUser` bigint NOT NULL,
  `IdPortal` tinyint NOT NULL,
  `IdCompany` int NOT NULL,
  `DateInt` int NOT NULL,
  `IdApp` tinyint NOT NULL,
  `TimeInSeconds` int NOT NULL DEFAULT '0',
  PRIMARY KEY (`IdUser`,`IdPortal`,`IdCompany`,`DateInt`,`IdApp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dic_new_matches_notification_type`
--

DROP TABLE IF EXISTS `dic_new_matches_notification_type`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dic_new_matches_notification_type` (
  `Id` int NOT NULL,
  `Name` varchar(50) NOT NULL,
  `Description` varchar(500) DEFAULT NULL,
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping routines for database 'repo_tracking'
--
/*!50003 DROP FUNCTION IF EXISTS `fn_now_by_portal` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` FUNCTION `fn_now_by_portal`(_idportal tinyint) RETURNS datetime
    DETERMINISTIC
BEGIN 
DECLARE l_offset int; 
DECLARE l_now DATETIME;
DECLARE l_time_zone CHAR(64);

SET SESSION TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

CASE _idportal
WHEN 0 THEN SELECT 'etc/UTC' INTO l_time_zone;

  WHEN 1 THEN SELECT 'America/Bogota' INTO l_time_zone;
  WHEN 2 THEN SELECT 'America/Mexico_City' INTO l_time_zone;
  WHEN 3 THEN SELECT 'America/Lima' INTO l_time_zone;
  WHEN 4 THEN SELECT 'America/Santiago' INTO l_time_zone;
  WHEN 5 THEN SELECT 'America/Argentina/Buenos_Aires' INTO l_time_zone;
  WHEN 6 THEN SELECT 'America/Guayaquil' INTO l_time_zone;
  WHEN 7 THEN SELECT 'America/Caracas' INTO l_time_zone;
  WHEN 8 THEN SELECT 'America/Costa_Rica' INTO l_time_zone;
  WHEN 9 THEN SELECT 'America/Guatemala' INTO l_time_zone;
  WHEN 10 THEN SELECT 'America/El_Salvador' INTO l_time_zone;
  WHEN 11 THEN SELECT 'America/Montevideo' INTO l_time_zone;
  WHEN 12 THEN SELECT 'America/Asuncion' INTO l_time_zone;
  WHEN 13 THEN SELECT 'America/Panama' INTO l_time_zone;
  WHEN 14 THEN SELECT 'America/Tegucigalpa' INTO l_time_zone;
  WHEN 15 THEN SELECT 'America/Managua' INTO l_time_zone;
  WHEN 16 THEN SELECT 'America/Santo_Domingo' INTO l_time_zone;
  WHEN 17 THEN SELECT 'America/La_Paz' INTO l_time_zone;
  WHEN 18 THEN SELECT 'America/Havana' INTO l_time_zone;
  WHEN 19 THEN SELECT 'America/Puerto_Rico' INTO l_time_zone;
  WHEN 37 THEN SELECT 'Europe/Madrid' INTO l_time_zone;

	WHEN 20 THEN SELECT 'Australia/Canberra' INTO l_time_zone;
	WHEN 21 THEN SELECT 'Canada/Eastern' INTO l_time_zone;
	WHEN 22 THEN SELECT 'Europe/Dublin' INTO l_time_zone;
	WHEN 23 THEN SELECT 'Antarctica/McMurdo' INTO l_time_zone;
	WHEN 24 THEN SELECT 'Africa/Johannesburg' INTO l_time_zone;
	WHEN 25 THEN SELECT 'Europe/London' INTO l_time_zone;
	WHEN 26 THEN SELECT 'EST' INTO l_time_zone;
	WHEN 27 THEN SELECT 'Asia/Singapore' INTO l_time_zone;
	WHEN 28 THEN SELECT 'Asia/Calcutta' INTO l_time_zone;
	WHEN 29 THEN SELECT 'Asia/Kuala_Lumpur' INTO l_time_zone;
	WHEN 30 THEN SELECT 'Asia/Manila' INTO l_time_zone;
	WHEN 31 THEN SELECT 'Africa/Nairobi' INTO l_time_zone;
	WHEN 32 THEN SELECT 'Asia/Jakarta' INTO l_time_zone;
	WHEN 33 THEN SELECT 'Asia/Hong_Kong' INTO l_time_zone;

	WHEN 34 THEN SELECT 'Europe/Madrid' INTO l_time_zone;
	WHEN 35 THEN SELECT 'Europe/Madrid' INTO l_time_zone;

  	WHEN 36 THEN SELECT 'Europe/Istanbul' INTO l_time_zone;
	WHEN 38 THEN SELECT 'Africa/Casablanca' INTO l_time_zone;
     WHEN 99 THEN SELECT 'America/Mexico_City' INTO l_time_zone;
ELSE SELECT 'etc/UTC' INTO l_time_zone;
END CASE;


select CONVERT_TZ(now(),'SYSTEM',l_time_zone) INTO l_now;
SET SESSION TRANSACTION ISOLATION LEVEL REPEATABLE READ ;

RETURN l_now; 
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP FUNCTION IF EXISTS `fn_now_int_format_by_portal` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` FUNCTION `fn_now_int_format_by_portal`(_idportal tinyint, _format VARCHAR(50)) RETURNS bigint
    DETERMINISTIC
BEGIN 
	SET SESSION TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
	RETURN DATE_FORMAT(fn_now_by_portal(_idportal),_format); 
    SET SESSION TRANSACTION ISOLATION LEVEL REPEATABLE READ ;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `AddMatchesOfferMailSettings` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`%` PROCEDURE `AddMatchesOfferMailSettings`(IN _idOffer INT, IN _idCompany INT, IN _idPortal INT, IN _idUser BIGINT, IN _idStatus INT, IN _availableMails INT)
BEGIN



  INSERT INTO MatchesOfferMailSettings (IdOffer, IdCompany, IdPortal, IdUser, CreatedOn, IdStatus, CreatedOnInt, AvailableMails )

  VALUES (_idOffer, _idCompany, _idPortal, _idUser, fn_now_by_portal(_idPortal), _idStatus, CAST(DATE_FORMAT(fn_now_by_portal(_idPortal), '%Y%m%d') AS SIGNED), _availableMails);



END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `AddMatchOfferMail` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`%` PROCEDURE `AddMatchOfferMail`(IN _idMatchesOfferMailSettings BIGINT, IN _idMatch BIGINT, IN _idCandidate INT, IN _idOffer INT, IN _idPortal TINYINT, IN _idCompany INT, IN _matchDate DATETIME, _idStatus TINYINT)
BEGIN



  INSERT INTO MatchOfferMail (IdMatchesOfferMailSettings, IdMatch, IdCandidate, IdOffer, IdPortal, IdCompany, MatchDate, MatchDateInt, IdStatus)

  VALUES (_idMatchesOfferMailSettings, _idMatch, _idCandidate, _idOffer, _idPortal, _idCompany, _matchDate, CAST(DATE_FORMAT(_matchDate, '%Y%m%d') AS SIGNED), _idStatus);



END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `AeGetMatchesSummaryActiveSubscriptionsForUser` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`%` PROCEDURE `AeGetMatchesSummaryActiveSubscriptionsForUser`(IN _idUser INT, IN _idPortal INT, IN _idCompany INT)
BEGIN



SELECT * FROM `OfferMatchesSummarySubscription` WHERE `IdUser` = _idUser AND `IdCompany` = _idCompany AND `IdPortal` = _idPortal AND `IdStatus` = 2;



END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `AeGetMatchesSummarySubscription` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`%` PROCEDURE `AeGetMatchesSummarySubscription`(IN _idUser INT, IN _idOffer INT, IN _idCompany INT, IN _idPortal INT)
BEGIN



SELECT * FROM `OfferMatchesSummarySubscription` WHERE `IdUser` = _idUser AND `IdOffer` = _idOffer AND `IdCompany` = _idCompany AND `IdPortal` = _idPortal AND `IdStatus` = 2;



END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `AeInvalidateMatchesForNotification` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`%` PROCEDURE `AeInvalidateMatchesForNotification`(IN _idOffer INT, IN _idCompany INT, IN _idPortal INT)
BEGIN

UPDATE `OfferMatchForNotification` SET `IdStatus` = 3 WHERE `IdOffer` = _idOffer AND `IdCompany` = _idCompany AND `IdPortal` = _idPortal AND `IdStatus` = 1;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `AeUpsertMatchesSummarySubscription` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`%` PROCEDURE `AeUpsertMatchesSummarySubscription`(IN _idOffer INT, IN _idCompany INT, IN _idPortal INT, IN _idUser INT, IN _type INT, IN _idStatus INT)
BEGIN



-- IdStatus = 2 : ACTIVADO

-- IdStatus = 6 : DESACTIVADO 

-- Siguiendo la convención de valores de estado StatusEnum



IF _idStatus <> 2 THEN



UPDATE `OfferMatchesSummarySubscription` SET `ModifiedOn` = `fn_now_by_portal`(_idPortal), `IdStatus` = _idStatus WHERE `IdOffer` =  _idOffer AND `IdCompany` = _idCompany AND `IdPortal` = _idPortal;



ELSE



INSERT INTO `OfferMatchesSummarySubscription` ( `IdOffer`, `IdCompany`, `IdPortal`, `IdUser`, `IdType`, `CreatedOn`, `ModifiedOn`, `IdStatus`, `CreatedOnInt`)

VALUES (_idOffer, _idCompany, _idPortal, _idUser, _type, `fn_now_by_portal`(_idPortal), `fn_now_by_portal`(_idPortal), _idStatus, CAST(DATE_FORMAT(`fn_now_by_portal`(_idPortal), '%Y%m%d') AS SIGNED))

ON DUPLICATE KEY UPDATE `IdType` = _type, `ModifiedOn` = `fn_now_by_portal`(_idPortal), `IdStatus` = _idStatus;



END IF;



END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `ApiRequestInsert` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `ApiRequestInsert`(
	IN _idCompany int(11),
	IN _idOffer int(11),
	IN _status int(11),
	IN _request longtext,
	IN _elapsedMilliseconds INT(11),
	IN _idPortal TINYINT(3),
    IN _idApp INT(11),
	IN _controller VARCHAR(255),
	IN _method VARCHAR(255),
	IN _httpMethod VARCHAR(20),
	IN _requestUri TEXT)
BEGIN

	INSERT INTO ApplicationRequests
	(
		IdCompany,
		IdOffer,
		Status,
		Request,
		ElapsedMilliseconds,
		IdPortal,
        IdApp,
		Controller,
		Method,
		HttpMethod,
		RequestUri
	)
	VALUES
	(
		_idCompany,
		_idOffer,
		_status,
		_request,
		_elapsedMilliseconds,
		_idPortal,
        _idApp,
		_controller,
		_method,
		_httpMethod,
		_requestUri
	);
 
	SELECT LAST_INSERT_ID();
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `ApiRequestUpdate` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `ApiRequestUpdate`(
	IN _id BIGINT(20),
	IN _status INT(11),	
	IN _elapsedMilliseconds INT(11))
BEGIN

  UPDATE ApplicationRequests 
     SET Status = _status,
         ElapsedMilliseconds = _elapsedMilliseconds
   WHERE Id = _id; 

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `ConsumedMatchesOfferMailSettingsById` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`%` PROCEDURE `ConsumedMatchesOfferMailSettingsById`(IN _id BIGINT, IN _units INT, IN _idPortal INT)
BEGIN



   UPDATE MatchesOfferMailSettings SET ConsumedMails = (ConsumedMails + _units), AvailableMails = (AvailableMails - _units), ModifiedOn = fn_now_by_portal(_idPortal)

   WHERE Id = _id;



END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `CvViewedDelete` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `CvViewedDelete`(
    IN _idLead BIGINT(20),
    IN _idPortal TINYINT(4)
  )
BEGIN
  DELETE FROM CvViewed
  WHERE IdLead = _idLead
    AND IdPortal = _idPortal;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `CvViewedGetMaxDateInt` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `CvViewedGetMaxDateInt`(
IN _IdPortal tinyint(3),
IN _IdCompany bigint,
IN _IdUser bigint
)
Begin

SELECT MAX(`DateViewedInt`) 
FROM `CvViewed`
WHERE IdCompany = _IdCompany
AND IdUser = _IdUser
AND IdPortal = _IdPortal;

 
 END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `CvViewedTrackingInsert` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `CvViewedTrackingInsert`(
IN _IdPortal tinyint(3),
IN _IdCompany bigint,
IN _IdOffer bigint,
IN _IdUser bigint,
IN _IdLead bigint,
IN _IdCv bigint,
IN _CvName varchar(200),
IN _CvProfession varchar(200),
IN _OfferName varchar(300),
IN _IdOriginTracking bigint,
IN _DateViewedInt bigint
)
Begin

INSERT INTO CvViewed
	(
	  IdPortal,
	  IdCompany,
	  IdOffer,
	  IdUser,
      IdLead,
	  IdCv,
      CvName,
	  CvProfession,
      OfferName,
	  IdOriginTracking,
      DateViewedInt
  )
  VALUES
  (
      _IdPortal,
	  _IdCompany,
	  _IdOffer,
	  _IdUser,
	  _IdLead,
	  _IdCv,
	  _CvName,
	  _CvProfession,
      _OfferName,
	  _IdOriginTracking,
	  _DateViewedInt 
  ) ON DUPLICATE KEY UPDATE DateViewedInt = _DateViewedInt, CvProfession = _CvProfession;
 
 END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `CvViewedUpdateCv` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `CvViewedUpdateCv`(
    IN _idLead BIGINT(20),
    IN _idCv BIGINT(20),
    IN _idPortal TINYINT(4)
  )
BEGIN
  UPDATE CvViewed SET
    IdCv = _idCv
  WHERE IdLead = _idLead
    AND IdPortal = _idPortal;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `CvVisualizationControlCountByType` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `CvVisualizationControlCountByType`(
	_candidateId 	INT(11),
    _portalId 		TINYINT(4),
    _typeId			TINYINT(4)
)
BEGIN

SELECT COUNT(*) AS Counter
FROM CvVisualizationHistory
WHERE IdCandidate = _candidateId
	AND IdType = _typeId
;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `CvVisualizationControlExists` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `CvVisualizationControlExists`(
	IN _idCompany INT(11),
	IN _idCandidate INT(11), 
	IN _idPortal TINYINT(3), 
	IN _idType TINYINT(3),
	IN _idCv INT(11),
	IN _dateInt INT(11)
)
BEGIN

  SELECT 
	IdCompany,
    IdCandidate,
    IdPortal,
    IdType,
    IdCv,
    DateInt
  FROM 
	  CvVisualizationControl
  WHERE
	IdCompany = _idCompany AND
    IdCandidate = _idCandidate AND
    IdPortal = _idPortal AND
    IdType = _idType AND
    
    DateInt = _dateInt
    ;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `CvVisualizationControlGetCompanies` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `CvVisualizationControlGetCompanies`(
	_candidateId			INT(11),
    _portalId				TINYINT(4),
    _offset					INT(11),
    _limit					INT(11)
)
BEGIN
SET SESSION TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;	

SELECT 
	IdCompany,
	IdType,
	DateInt as DateShowed,
	IsCompanyVisible
FROM CvVisualizationControl
WHERE 1
	AND IdCandidate = _candidateId 
	AND IdPortal = _portalId
ORDER BY DateInt DESC , IdCompany ASC
LIMIT _offset, _limit
;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `CvVisualizationControlGetCompaniesByDate` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`ats`@`80.32.111.238` PROCEDURE `CvVisualizationControlGetCompaniesByDate`(
  IN `_idCandidate` INT(11),
	IN `_idPortal` TINYINT(3),
	IN `_dateInt` INT(11),
	IN `_idType` TINYINT(3)
)
BEGIN

DECLARE SQL_VAR text; 
DECLARE WHERE_VAR text;
DECLARE FIELDS_VAR text; 
DECLARE ORDER_BY_VAR text; 

SET SQL_VAR = ''; 
SET WHERE_VAR  = ''; 
SET ORDER_BY_VAR = ''; 

SET FIELDS_VAR = ' 
	IdCompany,
	IdType,
	DateInt, 
  IdOffer, 
  IsCompanyVisible';

SET  WHERE_VAR =  CONCAT(WHERE_VAR , ' WHERE IdCandidate = ', _idCandidate,' AND
    IdPortal = ', _idPortal,' AND
    DateInt >= ', _dateInt);	
  
IF _idType < 99 THEN
	SET WHERE_VAR = CONCAT(WHERE_VAR , ' AND IdType = ', _idType );
END IF;

SET ORDER_BY_VAR = CONCAT(ORDER_BY_VAR, ' ORDER BY DateInt DESC');
  
			
SET  SQL_VAR = CONCAT ('SELECT ',  FIELDS_VAR , ' FROM CvVisualizationControl ' );
SET  SQL_VAR = CONCAT (SQL_VAR ,  WHERE_VAR);
SET  SQL_VAR = CONCAT (SQL_VAR ,  ORDER_BY_VAR);
		
set @todaLaQuery = SQL_VAR;

PREPARE smpt FROM @todaLaQuery;
EXECUTE smpt;
DEALLOCATE PREPARE smpt;
	
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `CvVisualizationControlGetCompaniesByDate_v2` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `CvVisualizationControlGetCompaniesByDate_v2`(
	IN `_idCandidate` INT(11),
	IN `_idPortal` TINYINT(3),
	IN `_numDays` TINYINT(3),
	IN `_idType` TINYINT(3)
)
BEGIN

DECLARE SQL_VAR text; 
DECLARE WHERE_VAR text;
DECLARE FIELDS_VAR text; 
DECLARE ORDER_BY_VAR text; 

SET SQL_VAR = ''; 
SET WHERE_VAR  = ''; 
SET ORDER_BY_VAR = ''; 

SET FIELDS_VAR = ' 
	IdCompany,
	IdType,
	DateInt, 
  IdOffer, 
  IsCompanyVisible';

SET  WHERE_VAR =  CONCAT(WHERE_VAR , ' WHERE IdCandidate = ', _idCandidate,' AND
    IdPortal = ', _idPortal,' AND
    DateInt >= DATE_FORMAT(DATE_SUB(fn_now_by_portal(', _idPortal, '), INTERVAL ', _numDays ,' DAY), ''%Y%m%d'')');	
  
IF _idType < 99 THEN
	SET WHERE_VAR = CONCAT(WHERE_VAR , ' AND IdType = ', _idType );
END IF;

SET ORDER_BY_VAR = CONCAT(ORDER_BY_VAR, ' ORDER BY DateInt DESC');
  
			
SET  SQL_VAR = CONCAT ('SELECT ',  FIELDS_VAR , ' FROM CvVisualizationControl ' );
SET  SQL_VAR = CONCAT (SQL_VAR ,  WHERE_VAR);
SET  SQL_VAR = CONCAT (SQL_VAR ,  ORDER_BY_VAR);
		
set @todaLaQuery = SQL_VAR;

PREPARE smpt FROM @todaLaQuery;
EXECUTE smpt;
DEALLOCATE PREPARE smpt;
	
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `CvVisualizationControlGetCompaniesByType` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `CvVisualizationControlGetCompaniesByType`(
	_candidateId			INT(11),
    _portalId				TINYINT(4),
    _typeId					TINYINT(4),
    _offset					INT(11),
    _limit					INT(11)
)
BEGIN
SET SESSION TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

SELECT 
	IdCompany,
	IdType,
	DateInt as DateShowed,
	IsCompanyVisible
FROM CvVisualizationControl
WHERE 1
	AND IdCandidate = _candidateId 
	AND IdPortal = _portalId
    AND IdType = _typeId
ORDER BY DateInt DESC , IdCompany ASC
LIMIT _offset, _limit
;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `CvVisualizationControlGetCompaniesByTypePaginate` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `CvVisualizationControlGetCompaniesByTypePaginate`(
	_candidateId			INT(11),
    _portalId				TINYINT(4),
    _companyId				INT(11),
    _dateInt				INT(11),
    _typeId					TINYINT(4),
    _limit					INT(11)
)
BEGIN
	
SELECT 
	IdCompany,
	IdType,
	DateInt as DateShowed,
	IsCompanyVisible
FROM CvVisualizationControl 
WHERE 
	IdPortal = _portalId AND 
	IdCandidate = _candidateId AND
	IdCompany > _companyId AND 
    IdType = _typeId AND
    DateInt <= _dateInt
ORDER BY DateInt DESC	
LIMIT _limit
;
    
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `CvVisualizationControlGetCompaniesPaginate` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `CvVisualizationControlGetCompaniesPaginate`(
	_candidateId			INT(11),
    _portalId				TINYINT(4),
    _companyId				INT(11),
    _dateInt				INT(11),
    _limit					INT(11)
)
BEGIN

    
SELECT 
	IdCompany,
	IdType,
	DateInt as DateShowed,
	IsCompanyVisible
FROM CvVisualizationControl 
WHERE 
	IdPortal = _portalId AND 
	IdCandidate = _candidateId AND
	IdCompany > _companyId AND 
    DateInt <= _dateInt
ORDER BY DateInt DESC	
LIMIT _limit
;
    
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `CvVisualizationControlGetLast` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `CvVisualizationControlGetLast`(
	_candidateId			INT(11),
    _portalId				TINYINT(4),
    _limit					INT(11)
)
BEGIN
	
SELECT 
	IdCandidate,
	IdCompany,
	IdType,
	DateInt as DateShowed,
	IsCompanyVisible
FROM CvVisualizationControl 
WHERE 
	IdPortal = _portalId AND 
	IdCandidate = _candidateId
ORDER BY DateInt ASC, IdCompany DESC
LIMIT 1
;
    
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `CvVisualizationControlGetLastByType` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `CvVisualizationControlGetLastByType`(
	_candidateId			INT(11),
    _portalId				TINYINT(4),
    _typeId					TINYINT(4),
    _limit					INT(11)
)
BEGIN
	
SELECT 
	IdCompany,
	IdType,
	DateInt as DateShowed,
	IsCompanyVisible
FROM CvVisualizationControl 
WHERE 
	IdPortal = _portalId AND 
	IdCandidate = _candidateId AND
    IdType = _typeId
ORDER BY DateInt ASC, IdCompany DESC
LIMIT 1
;
    
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `CvVisualizationControlGetTotalsByCandidate` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `CvVisualizationControlGetTotalsByCandidate`(
	IN `_idPortal` TINYINT(3),
	IN `_daysFrom` INT(11)
)
BEGIN

	SELECT IdCandidate, COUNT(*) as Total
	FROM CvVisualizationControl
	WHERE 1=1
		AND IdPortal = _idPortal
		AND DateInt >= DATE_FORMAT(DATE_SUB(fn_now_by_portal(_idPortal), INTERVAL _daysFrom DAY), '%Y%m%d')
		AND IdType = 0 
	GROUP BY IdCandidate;
	
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `CvVisualizationControlGetTotalsByCandidateV2` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `CvVisualizationControlGetTotalsByCandidateV2`(
	IN `_idPortal` TINYINT,
	IN `_daysFrom` INT,
	IN `_candidateIds` TEXT


)
BEGIN

	DECLARE SQL_VAR TEXT;  
	SET SQL_VAR = CONCAT('SELECT IdCandidate, COUNT(*) as Total FROM CvVisualizationControl ');
	SET SQL_VAR = CONCAT(SQL_VAR, 'WHERE 1=1 AND IdPortal = ',_idPortal,' ');
	SET SQL_VAR = CONCAT(SQL_VAR, 'AND DateInt >= DATE_FORMAT(DATE_SUB(fn_now_by_portal(',_idPortal,'), INTERVAL ',_daysFrom,' DAY), ''%Y%m%d'') ');
	SET SQL_VAR = CONCAT(SQL_VAR, 'AND IdCandidate IN (', _candidateIds, ') ');
	SET SQL_VAR = CONCAT(SQL_VAR, 'GROUP BY IdCandidate;');
	
	set @todaLaQuery = SQL_VAR;
	PREPARE smpt FROM @todaLaQuery; 
	EXECUTE smpt; 
	DEALLOCATE PREPARE smpt; 
	
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `CvVisualizationControlInsert` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `CvVisualizationControlInsert`(
  IN _idCompany INT(11),
  IN _idCandidate INT(11), 
  IN _idPortal TINYINT(3), 
  IN _idType TINYINT(3),
  IN _idCv INT(11),
  IN _dateInt INT(11),
  IN _statusId TINYINT(3)
)
BEGIN

  INSERT INTO CvVisualizationControl(IdCompany, IdCandidate, IdPortal, IdType, IdCv, DateInt, StatusId) 
  VALUES (_idCompany, _idCandidate, _idPortal, _idType, 0, _dateInt, _statusId)
  ON DUPLICATE KEY UPDATE
     DateInt = _dateInt
	,StatusId = _statusId
    ;

SELECT 
	CASE ROW_COUNT()
WHEN 1 THEN 1 
WHEN 2 THEN 0 
WHEN 0 THEN 0 
END as Rowcount;


END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `CvVisualizationControlInsert_v2` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `CvVisualizationControlInsert_v2`(
  IN _idCompany INT(11),
  IN _idCandidate INT(11), 
  IN _idPortal TINYINT(3), 
  IN _idType TINYINT(3),
  IN _idCv INT(11),
  IN _dateInt INT(11),
  IN _statusId TINYINT(3),
  IN _idOffer INT(11),
  IN _isCompanyVisible TINYINT(1)
)
BEGIN
  INSERT INTO CvVisualizationControl(IdCompany, IdCandidate, IdPortal, IdType, IdCv, DateInt, StatusId, IdOffer, isCompanyVisible, CreationDate) 
  VALUES (_idCompany, _idCandidate, _idPortal, _idType, 0, _dateInt, _statusId, _idOffer, _isCompanyVisible, fn_now_by_portal(_idPortal))
  ON DUPLICATE KEY UPDATE
     DateInt = _dateInt,
	   StatusId = _statusId,
     CreationDate = fn_now_by_portal(_idPortal);


  INSERT INTO CvVisualizationRaw(IdCompany, IdCandidate, IdPortal, IdType, IdOffer, isCompanyVisible, CreationDate)

  VALUES (_idCompany, _idCandidate, _idPortal, _idType, _idOffer, _isCompanyVisible, fn_now_by_portal(_idPortal));



END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `CvVisualizationControlInsert_v3` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `CvVisualizationControlInsert_v3`(

  IN _idCompany INT(11),

  IN _idCandidate INT(11), 

  IN _idPortal TINYINT(3), 

  IN _idType TINYINT(3),

  IN _idCv INT(11),

  IN _dateInt INT(11),

  IN _statusId TINYINT(3),

  IN _idOffer INT(11),

  IN _isCompanyVisible TINYINT(1),

  IN _creationDate TIMESTAMP,

  IN _ip VARCHAR(255),

  IN _idApp TINYINT(3),

  IN _idCompanyUser INT(11)

)
BEGIN

  INSERT INTO CvVisualizationControl(IdCompany, IdCandidate, IdPortal, IdType, IdCv, DateInt, StatusId, IdOffer, isCompanyVisible, CreationDate, IP, IdApp, IdCompanyUser) 

  VALUES (_idCompany, _idCandidate, _idPortal, _idType, 0, _dateInt, _statusId, _idOffer, _isCompanyVisible, _creationDate, _ip, _idApp, _idCompanyUser)

  ON DUPLICATE KEY UPDATE

     DateInt = _dateInt,

	   StatusId = _statusId,

     CreationDate = _creationDate,

     IP = _ip,

     IdApp = _idApp,

     IdCompanyUser = _idCompanyUser;



  INSERT INTO CvVisualizationRaw(IdCompany, IdCandidate, IdPortal, IdType, IdOffer, isCompanyVisible, CreationDate, IP, IdApp, IdCompanyUser, DateInt) 

  VALUES (_idCompany, _idCandidate, _idPortal, _idType, _idOffer, _isCompanyVisible, _creationDate, _ip, _idApp, _idCompanyUser, _dateInt);



END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `CvVisualizationCountersGetByCandidates` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `CvVisualizationCountersGetByCandidates`(

	IN `_idPortal` TINYINT,

	IN `_dateLimit` INT,

	IN `_dateFrom` INT,

	IN `_candidateIds` TEXT

)
BEGIN



	DECLARE SQL_VAR TEXT;  

	SET SQL_VAR = CONCAT('SELECT IdCandidate, Total FROM CvVisualizationCounters ');

	SET SQL_VAR = CONCAT(SQL_VAR, 'WHERE IdPortal = ',_idPortal,' ');

    SET SQL_VAR = CONCAT(SQL_VAR, 'AND DateInt >= ',_dateFrom,' AND DateInt < ', _dateLimit,' ');

	SET SQL_VAR = CONCAT(SQL_VAR, 'AND IdCandidate IN (', _candidateIds, ') ');

		

	set @todaLaQuery = SQL_VAR;

	PREPARE smpt FROM @todaLaQuery; 

	EXECUTE smpt; 

	DEALLOCATE PREPARE smpt; 

	

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `CvVisualizationCountersGetByDate` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `CvVisualizationCountersGetByDate`(

	IN `_idPortal` TINYINT,

   IN `_dateFrom` INT,

   IN `_dateLimit` INT,

   IN `_idCandidate` INT,

   IN `_bulksize` INT

)
BEGIN



	SELECT IdCandidate, Total, DateInt

	FROM CvVisualizationCounters

	WHERE IdPortal = _idPortal

		AND DateInt < _dateLimit

		AND (DateInt > _dateFrom OR (DateInt = _dateFrom AND idcandidate > _idCandidate))

	ORDER BY DateInt, IdCandidate

	LIMIT _bulksize;

	

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `CvVisualizationCountersGetSomeCandidates` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `CvVisualizationCountersGetSomeCandidates`(

    IN `_idPortal` TINYINT,

   IN `_dateFrom` INT,

   IN `_dateLimit` INT,

   IN `_idCandidate` INT,

   IN `_module` INT,

   IN `_residue` INT,

   IN `_bulksize` INT

)
BEGIN

    SELECT IdCandidate, Total, DateInt

    FROM CvVisualizationCounters

    WHERE IdPortal = _idPortal

        AND DateInt < _dateLimit

        AND (DateInt > _dateFrom OR (DateInt = _dateFrom AND idcandidate > _idCandidate))

        AND idcandidate MOD _module = _residue

    ORDER BY DateInt, IdCandidate

    LIMIT _bulksize;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `CvVisualizationCountersInsert` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `CvVisualizationCountersInsert`(IN _idCandidate INT(11), 
  IN _idPortal TINYINT(3), 
  IN _dateInt INT(11),
  IN _total INT(11))
BEGIN
  INSERT INTO CvVisualizationCounters(IdCandidate, IdPortal, DateInt, Total) 
  VALUES (_idCandidate, _idPortal, _dateInt, _total)
  ON DUPLICATE KEY UPDATE
    Total = Total + _total;
  SELECT LAST_INSERT_ID();
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `CvVisualizationCountersTotalDates` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `CvVisualizationCountersTotalDates`(
	IN `_idCandidate` INT(11),
	IN `_idPortal` TINYINT(3),
	IN `_dateInt` INT(11)
)
BEGIN
  SELECT 
	DateInt,
	Total
  FROM 
	  CvVisualizationCounters
  WHERE
	IdCandidate = _idCandidate AND
    IdPortal = _idPortal AND
    DateInt >= _dateInt;
    
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `CvVisualizationCountersTotalDates_v2` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `CvVisualizationCountersTotalDates_v2`(
	IN `_idCandidate` INT(11),
	IN `_idPortal` TINYINT(3),
	IN `_numDays` TINYINT(3)

)
BEGIN
  SELECT 
	DateInt,
	Total
  FROM 
	  CvVisualizationCounters
  WHERE
	IdCandidate = _idCandidate AND
    IdPortal = _idPortal AND
    DateInt >= DATE_FORMAT(DATE_SUB(fn_now_by_portal(_idPortal), INTERVAL _numDays DAY), '%Y%m%d');
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `CvVisualizationCountrolTotalByType` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `CvVisualizationCountrolTotalByType`(
	IN _idCandidate 	INT(11),
	IN _idPortal 		TINYINT(4),
	IN _dateInt 		INT(11),
    IN _idType			TINYINT(4)
)
BEGIN

	SELECT 
		DateInt, 
		COUNT(0) Total
	FROM CvVisualizationControl 
	WHERE IdCandidate = _idCandidate 
		AND IdPortal = _idPortal
		AND IdType = _idType
		AND DateInt >= _dateInt
	GROUP BY DateInt
	;
    
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `CvVisualizationGetCandidateDate` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `CvVisualizationGetCandidateDate`(
	IN _candidateId INT(11),
    IN _portalId INT(11)
)
BEGIN
	SELECT
		CandidateDateLastShowed
    FROM CvVisualizationsTotals    
    WHERE  IdCandidate = _candidateId
    AND IdPortal = _portalId;


END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `CvVisualizationGetControlDates` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `CvVisualizationGetControlDates`(
	IN _candidateId INT(11),
    IN _companyId INT(11),
    IN _portalId INT(11)
)
BEGIN
	SELECT
		DateInt
    FROM CvVisualizationControl    
    WHERE  IdCandidate = _candidateId
    AND IdCompany = _companyId
    AND IdPortal = _portalId;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `CvVisualizationGetSearchFilters` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `CvVisualizationGetSearchFilters`(
	IN _candidateId INT(11),
    IN _portalId INT(11)
)
BEGIN
	SELECT
		IdsCategory,
		`Query`,
		DateInt
    FROM CvVisualizationSearchFilters
    
    WHERE  IdCandidate = _candidateId
    AND IdPortal = _portalId;
    
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `CvVisualizationHistoryInsert` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `CvVisualizationHistoryInsert`(IN _idCompany INT(11),
  IN _idCandidate INT(11), 
  IN _idPortal TINYINT(3), 
  IN _idType INT(11),
  IN _dateShowed INT(11),
  IN _isCompanyVisible TINYINT(3))
BEGIN
  INSERT INTO CvVisualizationHistory(IdCompany, IdCandidate, IdPortal, IdType, DateShowed, IsCompanyVisible) 
  VALUES (_idCompany, _idCandidate, _idPortal, _idType, _dateShowed, _isCompanyVisible)
  ON DUPLICATE KEY UPDATE
    IsCompanyVisible = _isCompanyVisible;
  SELECT LAST_INSERT_ID();
  call CvVisualizationControlInsert(_idCompany,_idCandidate, _idPortal, _idType,0,_dateShowed,0);
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `CvVisualizationHistoryInsert_v2` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `CvVisualizationHistoryInsert_v2`(IN _idCompany INT(11),
  IN _idCandidate INT(11), 
  IN _idPortal TINYINT(3), 
  IN _idType INT(11),
  IN _dateShowed INT(11))
BEGIN
  INSERT INTO CvVisualizationHistory(IdCompany, IdCandidate, IdPortal, IdType, DateShowed, IsCompanyVisible) 
  VALUES (_idCompany, _idCandidate, _idPortal, _idType, _dateShowed, 1);
  SELECT LAST_INSERT_ID();
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `CvVisualizationSearchFiltersGet` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `CvVisualizationSearchFiltersGet`(IN _idCompany INT(11),
  IN _idCandidate INT(11), 
  IN _idPortal TINYINT(3), 
  IN _dateInt INT(11))
BEGIN
  SELECT 
    Id,
	  IdCompany,
    IdCandidate,
    IdPortal,
    Query,
    IdsCategory,
    IdsProvince,
    DateInt
  FROM 
	  CvVisualizationSearchFilters
  WHERE
	  IdCompany = _idCompany AND
    IdCandidate = _idCandidate AND
    IdPortal = _idPortal AND
    DateInt = _dateInt;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `CvVisualizationSearchFiltersGetSearches` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`ats`@`80.32.111.238` PROCEDURE `CvVisualizationSearchFiltersGetSearches`(
	IN `_idCandidate` INT(11),
	IN `_idPortal` TINYINT(3),
	IN `_dateInt` INT(11)
)
BEGIN
  SELECT 
	cv.`Query` Search, 
	cv.IdsCategory Category,
	cv.IdsProvince Provinces
  FROM CvVisualizationSearchFilters cv
  WHERE
	IdPortal = _idPortal AND
	IdCandidate = _idCandidate AND
    DateInt >= _dateInt;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `CvVisualizationSearchFiltersGetSearches_v2` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `CvVisualizationSearchFiltersGetSearches_v2`(
	IN `_idCandidate` INT(11),
	IN `_idPortal` TINYINT(3),
	IN `_numDays` TINYINT(3)
)
BEGIN
  SELECT 
	cv.`Query` Search, 
	cv.IdsCategory Category,
	cv.IdsProvince Provinces
  FROM CvVisualizationSearchFilters cv
  WHERE
	IdPortal = _idPortal AND
	IdCandidate = _idCandidate AND
	DateInt >= DATE_FORMAT(DATE_SUB(fn_now_by_portal(_idPortal), INTERVAL _numDays DAY), '%Y%m%d');
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `CvVisualizationSearchFiltersInsert` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `CvVisualizationSearchFiltersInsert`(IN _idCompany INT(11), 
  IN _idCandidate INT(11), 
  IN _idPortal TINYINT(3), 
  IN _query VARCHAR(255), 
  IN _idsCategory VARCHAR(255),
  IN _idsProvince VARCHAR(255),
  IN _dateInt INT(11))
BEGIN
  INSERT INTO repo_tracking.CvVisualizationSearchFilters(IdCompany, IdCandidate, IdPortal, Query, IdsCategory, IdsProvince, DateInt) 
  VALUES (_idCompany, _idCandidate, _idPortal, _query, _idsCategory, _idsProvince, _dateInt)
  ON DUPLICATE KEY UPDATE
    Query = _query,
    IdsCategory = _idsCategory,
    IdsProvince = _idsProvince;
  SELECT LAST_INSERT_ID();
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `CvVisualizationsTotalsGetDateShowed` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `CvVisualizationsTotalsGetDateShowed`(
	IN `_idCandidate` INT(11),
	IN `_idPortal` TINYINT(3)
)
BEGIN

  SELECT 
		TotalNewShowed newShowed,
		CandidateDateLastShowed lastShowed
  FROM 
	  	CvVisualizationsTotals
  WHERE
		IdPortal = _idPortal AND
		IdCandidate = _idCandidate;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `CvVisualizationsTotalsGetNewShowed` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `CvVisualizationsTotalsGetNewShowed`(
    IN _portalId INT(11),
    IN _fromDate INT(11),
    IN _limit INT(11)
)
BEGIN

SELECT
	IdCandidate, 
	IdPortal,
	TotalNewShowed,
	CompanyDateLastShowed
FROM CvVisualizationsTotals
WHERE  
	IdPortal = _portalId AND
    CompanyDateLastShowed >= DATE_FORMAT(DATE_SUB(fn_now_by_portal(_portalId), INTERVAL 7 DAY), '%Y%m%d') AND
    TotalNewShowed > 0 AND 
    IdCandidate > 0 AND 
    NotificationSentAt < _fromDate
LIMIT _limit
;
    
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `CvVisualizationsTotalsGetNewShowedByCandidate` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `CvVisualizationsTotalsGetNewShowedByCandidate`(
    IN _portalId INT(11),
    IN _candidate_id INT(11),
    IN _fromDate INT(11),
    IN _limit INT(11)
)
BEGIN
  SELECT
    IdCandidate, 
        IdPortal,
        TotalNewShowed,
        CompanyDateLastShowed

    FROM CvVisualizationsTotals
    
    WHERE  IdPortal = _portalId
    AND TotalNewShowed > 0
    AND IdCandidate = _candidate_id
    AND NotificationSentAt < _fromDate
    LIMIT _limit;
    
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `CvVisualizationsTotalsInsert` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `CvVisualizationsTotalsInsert`(IN _idCandidate INT(11), IN _idPortal TINYINT(3), IN _totalShowed INT(11), IN _totalNewShowed INT(11), IN _companyDateLastShowed INT(11), IN _candidateDateLastShowed INT(11))
BEGIN
  INSERT INTO repo_tracking.CvVisualizationsTotals(IdCandidate, IdPortal, TotalShowed, TotalNewShowed, CompanyDateLastShowed, CandidateDateLastShowed) 
  VALUES (_idCandidate, _idPortal, _totalShowed, _totalNewShowed, _companyDateLastShowed, _candidateDateLastShowed)
  ON DUPLICATE KEY UPDATE
    TotalShowed = TotalShowed + _totalShowed,
    TotalNewShowed = TotalNewShowed + _totalNewShowed,
    CompanyDateLastShowed =  _companyDateLastShowed;
  SELECT LAST_INSERT_ID();
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `CvVisualizationsTotalsUpdateDateShowed` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `CvVisualizationsTotalsUpdateDateShowed`(
	IN `_idCandidate` INT(11),
	IN `_idPortal` TINYINT(3),
	IN `_dateInt` INT(11)
)
BEGIN
	UPDATE
		CvVisualizationsTotals
	SET
		TotalNewShowed = 0, 
		CandidateDateLastShowed = _dateInt
	WHERE
		IdPortal = _idPortal AND
		IdCandidate = _idCandidate;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `CvVisualizationsTotalsUpdateNotificationDate` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `CvVisualizationsTotalsUpdateNotificationDate`(
  IN `_candidateId` INT(11),
  IN `_portalId` TINYINT(3),
  IN `_dateInt` INT(11)
)
BEGIN
  UPDATE
    CvVisualizationsTotals
  SET
    NotificationSentAt = _dateInt
  WHERE
    IdPortal = _portalId AND
    IdCandidate = _candidateId;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `CvVisualizationsTotalsUpdateTotalNewShowed` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`ats`@`80.32.111.238` PROCEDURE `CvVisualizationsTotalsUpdateTotalNewShowed`(
	IN `_idCandidate` INT(11),
	IN `_idPortal` TINYINT(3),
	IN `_dateInt` INT(11)
)
BEGIN

DECLARE newShowed INT;
DECLARE lastShowed INT;

  SELECT 
		TotalNewShowed, CandidateDateLastShowed INTO newShowed, lastShowed
  FROM 
	  	CvVisualizationsTotals
  WHERE
		IdPortal = _idPortal AND
		IdCandidate = _idCandidate;
	
	UPDATE
		CvVisualizationsTotals
	SET
		TotalNewShowed = 0, 
		CandidateDateLastShowed = _dateInt
	WHERE
		IdPortal = _idPortal AND
		IdCandidate = _idCandidate;
	
	SELECT newShowed, lastShowed;
	
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `CvVisualizationsTotalsUpdateTotalNewShowed_v2` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `CvVisualizationsTotalsUpdateTotalNewShowed_v2`(
	IN `_idCandidate` INT(11),
	IN `_idPortal` TINYINT(3)
)
BEGIN

DECLARE newShowed INT;
DECLARE lastShowed INT;

  SELECT 
		TotalNewShowed, CandidateDateLastShowed INTO newShowed, lastShowed
  FROM 
	  	CvVisualizationsTotals
  WHERE
		IdPortal = _idPortal AND
		IdCandidate = _idCandidate;
	
	UPDATE
		CvVisualizationsTotals
	SET
		TotalNewShowed = 0, 
		CandidateDateLastShowed = DATE_FORMAT(fn_now_by_portal(_idPortal), '%Y%m%d')
	WHERE
		IdPortal = _idPortal AND
		IdCandidate = _idCandidate;
	
	SELECT newShowed, lastShowed;
	
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `CvVisualizationTotalVisualizationsFrom` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `CvVisualizationTotalVisualizationsFrom`(
	IN _candidateId INT(11),
    IN _portalId INT(11),
    IN _fromDate INT(11)
)
BEGIN

SELECT
	IdCandidate, 
	DateInt, 
	IdType as TypeId, 
	count(0) as TotalVisualizations
FROM CvVisualizationControl    
WHERE  
	IdCandidate = _candidateId
	AND IdPortal = _portalId
	AND DateInt > _fromDate
GROUP BY DateInt, IdType
;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `CvVisualizationTotalVisualizationsInterval` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `CvVisualizationTotalVisualizationsInterval`(
	IN _candidateId 	INT(11),
    IN _portalId 		INT(11),
    IN _fromDate 		INT(11),
    IN _toDate			INT(11)
)
BEGIN

SELECT
	IdCandidate, 
	DateInt, 
	IdType as TypeId, 
	count(0) as TotalVisualizations
FROM CvVisualizationControl    
WHERE  
	IdCandidate = _candidateId
	AND IdPortal = _portalId
	AND DateInt > _fromDate 
    AND DateInt <= _toDate
GROUP BY DateInt, IdType
;


END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `GetAllMatchOfferMailByStatusByPortal` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`%` PROCEDURE `GetAllMatchOfferMailByStatusByPortal`(IN _idPortal TINYINT, IN _idStatus TINYINT)
BEGIN



  SELECT * FROM MatchOfferMail WHERE IdPortal = _idPortal AND IdStatus = _idStatus

  ORDER BY IdMatchesOfferMailSettings, MatchDate;



END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `GetLastCvVieweds` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `GetLastCvVieweds`(
IN _IdPortal tinyint(3),
IN _IdCompany bigint,
IN _IdUser bigint,
IN _Limit int
)
Begin

SELECT 
	`IdPortal`,
	`IdCompany`,
    `IdOffer`,
    `IdUser`,
    `IdLead`,
    `IdCv`,
    `CvName`,
    `CvProfession`,
    `OfferName`,
    `IdOriginTracking`,
    `DateViewedInt`
FROM `CvViewed`
WHERE IdCompany = _IdCompany
AND IdUser = _IdUser
AND IdPortal = _IdPortal
ORDER BY DateViewedInt desc
LIMIT _Limit;
 
 END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `GetLastOfferVieweds` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `GetLastOfferVieweds`(
IN _IdPortal tinyint(3),
IN _IdCompany bigint,
IN _IdUser bigint,
IN _Limit int
)
Begin

SELECT `IdPortal`,
	`IdCompany`,
    `IdOffer`,
    `IdUser`,
    `OfferName`,
    `OfferCity`,
    `DateViewedInt`
FROM `OfferViewed`
WHERE IdCompany = _IdCompany
AND IdUser = _IdUser
AND IdPortal = _IdPortal
ORDER BY DateViewedInt desc
LIMIT _Limit;
 
 END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `GetLeadStageTimeLapse` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `GetLeadStageTimeLapse`(
IN _IdCompany bigint(20),
IN _IdPortal tinyint(3),
IN _IdCv bigint(20),
IN _IdOffer bigint(20),
IN _IdLead bigint(20)
)
Begin
Select 
	 IdCompany,
	 IdCv,
	 IdPortal,
	 IdOffer,
	 IdLead,
	 IdStage,
	 DateUpdate	
 From LeadStageTimeLapse
 where IdCompany = _IdCompany and IdPortal = _IdPortal and IdCv = _IdCv and IdOffer = _IdOffer and IdLead = _IdLead; 
 
 END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `GetMatchesOfferMailSettings` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`%` PROCEDURE `GetMatchesOfferMailSettings`(IN _idOffer INT, IN _idCompany INT, IN _idPortal INT)
BEGIN



   select * from MatchesOfferMailSettings

   WHERE IdOffer = _idOffer and IdCompany = _idCompany and IdPortal = _idPortal;



END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `GetMatchesOfferMailSettingsById` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`%` PROCEDURE `GetMatchesOfferMailSettingsById`(IN _id BIGINT)
BEGIN



   

   SELECT * FROM MatchesOfferMailSettings WHERE Id = _id;

   



END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `InsertLeadStageTimeLapse` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `InsertLeadStageTimeLapse`(
IN _IdCompany bigint(20),
IN _IdCv bigint(20),
IN _IdPortal tinyint(3),
IN _IdOffer bigint(20),
IN _IdLead bigint(20),
IN _IdStage tinyint(3)
)
Begin

INSERT INTO LeadStageTimeLapse
	(
	 IdCompany,
	 IdCv,
	 IdPortal,
	 IdOffer,
	 IdLead,
	 IdStage,
     DateUpdate
  )
  VALUES
  (
  _IdCompany,
  _IdCv,
  _IdPortal,
  _IdOffer,
  _IdLead,
  _IdStage,
	  fn_now_int_format_by_portal(_IdPortal, '%Y%m%d%H%i%S')	  
  ) ON DUPLICATE KEY UPDATE IdStage = _IdStage, DateUpdate = fn_now_int_format_by_portal(_IdPortal, '%Y%m%d%H%i%S');
 
 END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `LeadSHFindAllByIdCvCompanyIdPortalId` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `LeadSHFindAllByIdCvCompanyIdPortalId`(
    IN _idCv BIGINT(20),
    IN _companyId int,
    IN _portalId int)
BEGIN
    SELECT Id,IdCv,IdPortal,IdCompany,IdOffer,IdStage,IdPreviousStage,IdSource,IdSourceType,IdStatus,
           LabelNew,LabelSmartRecruiting,DateCreate,DateStage,LastMovementType,LastManualMovementStage,IsFlash,KqAvgScore
           KqMaxAvg,KqExcluded,AdequacyPercent,ExcludedByFilters,LastMovementType,IsFlash,LastManualMovementStage,IdMatchCT,
           IdCandidateProcessStatusCT, IdAttachedFileCT 
    FROM LeadSH
    WHERE IdCv = _idCv
      AND IdPortal =_portalId
      AND IdCompany =_companyId;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `LeadStagesHistoryInsert` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `LeadStagesHistoryInsert`(
IN _idLead BIGINT, 
IN _idStage TINYINT(4),
IN _movementType TINYINT(4),
IN _idPortal TINYINT(4))
BEGIN

	INSERT INTO LeadStagesHistory(IdLead, DateInt, IdStage, MovementType)
    VALUES (_idLead, fn_now_int_format_by_portal(_idPortal, '%Y%m%d%H%i%S'),_idStage, _movementType);

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `LeadStageTimeLapseUpdateCv` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `LeadStageTimeLapseUpdateCv`(
    IN _idLead BIGINT(20),
    IN _idCv BIGINT(20),
    IN _idPortal TINYINT(4)
  )
BEGIN
  UPDATE LeadStageTimeLapse SET
    IdCv = _idCv
  WHERE IdLead = _idLead
    AND IdPortal = _idPortal;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `MatchesManagerAddMatchForNotification` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`%` PROCEDURE `MatchesManagerAddMatchForNotification`(IN _idMatch INT, IN _idCandidate INT, IN _idOffer INT, IN _idPortal INT, IN _idCompany INT, IN _matchDate DATETIME, IN _matchDateInt INT)
BEGIN



INSERT INTO `OfferMatchForNotification`

(`IdMatch`, `IdCandidate`, `IdOffer`, `IdPortal`, `IdCompany`, `MatchDate`, `MatchDateInt`, `IdStatus`)



VALUES (_idMatch, _idCandidate, _idOffer, _idPortal, _idCompany, _matchDate, _matchDateInt, 1); -- <-- Inicializando el Match en estado Pendiente = 1



END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `MatchesManagerAddMatchForNotification_v2` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`%` PROCEDURE `MatchesManagerAddMatchForNotification_v2`(IN _idMatch BIGINT, IN _idCandidate INT, IN _idOffer INT, IN _idPortal INT, IN _idCompany INT, IN _matchDate DATETIME, IN _matchDateInt INT)
BEGIN



INSERT INTO `OfferMatchForNotification`

(`IdMatch`, `IdCandidate`, `IdOffer`, `IdPortal`, `IdCompany`, `MatchDate`, `MatchDateInt`, `IdStatus`)



VALUES (_idMatch, _idCandidate, _idOffer, _idPortal, _idCompany, _matchDate, _matchDateInt, 1); -- <-- Inicializando el Match en estado Pendiente = 1



END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `OfferViewedGetMaxDateInt` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `OfferViewedGetMaxDateInt`(
IN _IdPortal tinyint(3),
IN _IdCompany bigint,
IN _IdUser bigint
)
Begin

SELECT MAX(`DateViewedInt`) 
FROM `OfferViewed`
WHERE IdCompany = _IdCompany
AND IdUser = _IdUser
AND IdPortal = _IdPortal;

 
 END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `OfferViewedTrackingInsert` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `OfferViewedTrackingInsert`(
IN _IdPortal tinyint(3),
IN _IdCompany bigint,
IN _IdOffer bigint,
IN _IdUser bigint,
IN _OfferName varchar(200),
IN _OfferCity varchar(300),
IN _DateViewedInt bigint
)
Begin

INSERT INTO OfferViewed
	(
	IdPortal,
	  IdCompany,
	  IdOffer,
	  IdUser,
      OfferName,
	  OfferCity,
      DateViewedInt
  )
  VALUES
  (
      _IdPortal,
	  _IdCompany,
	  _IdOffer,
	  _IdUser,
	  _OfferName,
	  _OfferCity,
	  _DateViewedInt 
  ) ON DUPLICATE KEY UPDATE DateViewedInt = _DateViewedInt;
 
 END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `PrcTerminatorVisualizationDelete` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `PrcTerminatorVisualizationDelete`(

	IN `_id_candidate` INT

)
BEGIN



	DELETE FROM CvVisualizationsTotals WHERE IdCandidate = _id_candidate;

	DELETE FROM CvVisualizationSearchFilters WHERE IdCandidate = _id_candidate;

	DELETE FROM CvVisualizationRaw WHERE IdCandidate = _id_candidate;

	DELETE FROM CvVisualizationHistory WHERE IdCandidate = _id_candidate;

	DELETE FROM CvVisualizationCounters WHERE IdCandidate = _id_candidate;

	DELETE FROM CvVisualizationControl WHERE IdCandidate = _id_candidate;



  END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `PrcToAnalyticsOfferData` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `PrcToAnalyticsOfferData`(_IdCompany INT, _IdPortal TINYINT)
BEGIN

	select
		IdOffer,
        IdCompany,
        IdPortal,
        HasKillerQuestions,
        HasAdequacy,
        HasExclusiveFilters,
        NumFolders,
        NumTests,
        NumPersonalizedTest,
        NumVideoInterviews,
        NumInterviewKits,
        NumIntegrators,
        NumSocialNetworks,
        PublishedOnCT,
        PublishedOnIndeed,
        PublishedOnLinkedin
	from
		OfferStatisticsData
	where
		IdCompany=_IdCompany
        and IdPortal= _IdPortal
        and IdStatus=2;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `PrcToAnalyticsTimeUseByUser` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `PrcToAnalyticsTimeUseByUser`(_date_start INT )
BEGIN

	select
		IdUser,
        IdPortal,
        IdCompany,
        DateInt,
        IdApp,
        TimeInSeconds
	from
		TimeUseByUser
	where
		DateInt>=_date_start;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `PrcTotalOffersSubscribedToNotifications` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`%` PROCEDURE `PrcTotalOffersSubscribedToNotifications`(IN _idPortal TINYINT)
BEGIN



SELECT `IdType`, `fn_now_by_portal`(`IdPortal`) AS `Date`, DATE_FORMAT(`fn_now_by_portal`(`IdPortal`), '%Y%m%d') AS `DateInt`, COUNT(`IdOffer`) as `Total`

FROM `OfferMatchesSummarySubscription` 

WHERE `IdPortal` = _idPortal

GROUP BY `IdPortal`, `IdType`;



END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `PrcVisualizationCvVisualizationRawTop1ByDay` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`%` PROCEDURE `PrcVisualizationCvVisualizationRawTop1ByDay`(

	_DateInt					int,

    _IdPortal					int

)
BEGIN





SET SESSION TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;





SELECT 

	Id

FROM 

	CvVisualizationRaw

WHERE

	DateInt=_DateInt

    and IdPortal=_IdPortal

Order by 

	Id asc

limit 1;



SET SESSION TRANSACTION ISOLATION LEVEL REPEATABLE READ ;



END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `prc_OnlineNotificationTrackingCompany_Add` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `prc_OnlineNotificationTrackingCompany_Add`( IN _IdApplication int,

 IN _ApplicationDescription varchar(250),

 IN _IdNotificationChannel int,

 IN _NotificationChannelDescription varchar(250),

 IN _IdNotificationType int,

 IN _NotificationTypeDescription varchar(250),

 IN _IdPortal int,

 IN _IdNewsletter int,

 IN _EmailTo varchar(250),

 IN _Subject varchar(250),

 IN _From varchar(250),

 IN _Origin text,

 IN _IdStatus tinyint,

 IN _StatusDescription varchar(250),

 IN _StatusReason varchar(250),

 IN _ConsumeType tinyint,

 IN _ConsumeTypeDescription varchar(50),

 IN _ExtraInformation text,

 IN _ContentBody text,

 IN _IdCompany int,

 IN _IdUser int)
BEGIN



INSERT INTO OnlineNotificationTrackingCompany (IdApplication,ApplicationDescription,IdNotificationChannel,NotificationChannelDescription,IdNotificationType,

NotificationTypeDescription,IdPortal,IdNewsletter,EmailTo,Subject,`From`,Origin,IdStatus,StatusDescription,StatusReason,CreatedOnUtc,

ConsumeType,ConsumeTypeDescription,ExtraInformation,ContentBody,IdCompany,IdUser)	

VALUES (_IdApplication,_ApplicationDescription,_IdNotificationChannel,_NotificationChannelDescription,_IdNotificationType,

_NotificationTypeDescription,_IdPortal,_IdNewsletter,_EmailTo,_Subject,_From,_Origin,_IdStatus,_StatusDescription,_StatusReason,UTC_TIMESTAMP(),

_ConsumeType,_ConsumeTypeDescription,_ExtraInformation,_ContentBody,_IdCompany,_IdUser);

 

  

select LAST_INSERT_ID();



END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `prc_OnlineNotificationTrackingCompany_Add_v2` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `prc_OnlineNotificationTrackingCompany_Add_v2`(IN _IdApplication INT, IN _ApplicationDescription VARCHAR(250), IN _IdNotificationChannel INT, IN _NotificationChannelDescription VARCHAR(250), IN _IdNotificationType INT, IN _NotificationTypeDescription VARCHAR(250), IN _IdPortal INT, IN _IdNewsletter INT, IN _EmailTo VARCHAR(250), IN _Subject VARCHAR(250), IN _From VARCHAR(250), IN _Origin MEDIUMTEXT, IN _IdStatus TINYINT, IN _StatusDescription VARCHAR(250), IN _StatusReason VARCHAR(250), IN _ConsumeType TINYINT, IN _ConsumeTypeDescription VARCHAR(50), IN _ExtraInformation TEXT, IN _ContentBody TEXT, IN _IdCompany INT, IN _IdUser INT)
BEGIN



INSERT INTO OnlineNotificationTrackingCompany (IdApplication,ApplicationDescription,IdNotificationChannel,NotificationChannelDescription,IdNotificationType,

NotificationTypeDescription,IdPortal,IdNewsletter,EmailTo,Subject,`From`,Origin,IdStatus,StatusDescription,StatusReason,CreatedOnUtc,

ConsumeType,ConsumeTypeDescription,ExtraInformation,ContentBody,IdCompany,IdUser)	

VALUES (_IdApplication,_ApplicationDescription,_IdNotificationChannel,_NotificationChannelDescription,_IdNotificationType,

_NotificationTypeDescription,_IdPortal,_IdNewsletter,_EmailTo,_Subject,_From,_Origin,_IdStatus,_StatusDescription,_StatusReason,UTC_TIMESTAMP(),

_ConsumeType,_ConsumeTypeDescription,_ExtraInformation,_ContentBody,_IdCompany,_IdUser);

 

  

select LAST_INSERT_ID();



END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `SetLostMatchesOfferMailSettings` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`%` PROCEDURE `SetLostMatchesOfferMailSettings`(IN _idOffer INT, IN _idCompany INT, IN _idPortal INT, IN _idStatus INT, IN _consumedMails INT)
BEGIN



   UPDATE MatchesOfferMailSettings SET IdStatus = _idStatus, ModifiedOn = fn_now_by_portal(_idPortal), AvailableMails = 0, ConsumedMails =  _consumedMails

   WHERE IdOffer = _idOffer and IdCompany = _idCompany and IdPortal = _idPortal;

   



END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `ShCvVisualizationControlRawInsert` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `ShCvVisualizationControlRawInsert`(
  IN _idCompany INT(11),
  IN _idPortal TINYINT(3), 
  IN _idType TINYINT(3),
  IN _idCv BIGINT(11),
  IN _dateInt INT(11),
  IN _statusId TINYINT(3),
  IN _idOffer INT(11),
  IN _isCompanyVisible TINYINT(1),
  IN _creationDate TIMESTAMP,
  IN _ip VARCHAR(255),
  IN _idApp TINYINT(3),
  IN _idCompanyUser INT(11)
)
BEGIN

    INSERT INTO CvVisualizationRaw(IdCompany, IdCandidate, IdPortal, IdType, IdOffer, isCompanyVisible, CreationDate, IP, IdApp, IdCompanyUser, DateInt, IdCv) 
  VALUES (_idCompany, 0, _idPortal, _idType, _idOffer, _isCompanyVisible, _creationDate, _ip, _idApp, _idCompanyUser, _dateInt, _idCv);

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `ShOfferStatisticsAddToStack` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `ShOfferStatisticsAddToStack`(	
    IN _idOffer INT(11),
    IN _type TINYINT(4)
)
BEGIN

INSERT INTO OfferStatisticsStack (IdOffer, IdType)
VALUES (_idOffer, _type)
ON DUPLICATE KEY UPDATE CreatedOn = Now(), NumHits = NumHits + 1;



END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `ShOfferStatisticsDataInsert` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `ShOfferStatisticsDataInsert`(
  IN _OfferId int(11),
  IN _CompanyId int(11),
  IN _PortalId tinyint(4),
  IN _HasKillerQuestions bit(1),
  IN _HasAdequacy bit(1),
  IN _HasExclusiveFilters bit(1),
  IN _NumFolders tinyint(4),
  IN _NumTests tinyint(4),
  IN _NumPersonalizedTest tinyint(4),
  IN _NumVideoInterviews tinyint(4),
  IN _NumInterviewKits tinyint(4),
  IN _NumIntegrators tinyint(4),
  IN _PublishedOnCT bit(1),
  IN _PublishedOnIndeed bit(1),
  IN _PublishedOnLinkedIn bit(1),
  IN _StatusId tinyint(4)
)
BEGIN


INSERT INTO OfferStatisticsData(
	IdOffer,
	IdCompany,
	IdPortal,
	HasKillerQuestions,
	HasAdequacy,
	HasExclusiveFilters,
	NumFolders,
	NumTests,
	NumPersonalizedTest,
	NumVideoInterviews,
	NumInterviewKits,
	NumIntegrators,
	PublishedOnCT,
	PublishedOnIndeed,
	PublishedOnLinkedIn,
	IdStatus
)
VALUES (
	_OfferId,
	_CompanyId,
	_PortalId,
	_HasKillerQuestions,
	_HasAdequacy,
	_HasExclusiveFilters,
	_NumFolders,
	_NumTests,
	_NumPersonalizedTest,
	_NumVideoInterviews,
	_NumInterviewKits,
	_NumIntegrators,
	_PublishedOnCT,
	_PublishedOnIndeed,
	_PublishedOnLinkedIn,
	_StatusId
)  
ON DUPLICATE KEY UPDATE
	HasKillerQuestions = _HasKillerQuestions,
	HasAdequacy = _HasAdequacy,
	HasExclusiveFilters = _HasExclusiveFilters,
	NumFolders = _NumFolders,
	NumTests = _NumTests,
	NumPersonalizedTest = _NumPersonalizedTest,
	NumVideoInterviews = _NumVideoInterviews,
	NumInterviewKits = _NumInterviewKits,
	NumIntegrators = _NumIntegrators,
	PublishedOnCT = _PublishedOnCT,
	PublishedOnIndeed = _PublishedOnIndeed,
	PublishedOnLinkedIn = _PublishedOnLinkedIn,
	IdStatus = _StatusId;
 
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `ShOfferStatisticsDataSumLeadsFromCT` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `ShOfferStatisticsDataSumLeadsFromCT`(
  IN _OfferId int(11),
  IN _NumHits int(11)
)
BEGIN


	UPDATE OfferStatisticsData
	SET 
    
    TotalLeads = (TotalLeads + _NumHits),
    TotalLeadsFromCT = (TotalLeadsFromCT + _NumHits)
    WHERE IdOffer = _OfferId;
    
 
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `ShOfferStatisticsDataSumLeadsFromFacebookJobs` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `ShOfferStatisticsDataSumLeadsFromFacebookJobs`(
  IN _OfferId int(11),
  IN _NumHits int(11)
)
BEGIN


	UPDATE OfferStatisticsData
	SET 
    TotalLeads = (TotalLeads + _NumHits),
    TotalLeadsFromFacebookJobs = (TotalLeadsFromFacebookJobs + _NumHits)
    WHERE IdOffer = _OfferId;
    
 
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `ShOfferStatisticsDataSumLeadsFromFacebookRRSS` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `ShOfferStatisticsDataSumLeadsFromFacebookRRSS`(
  IN _OfferId int(11),
  IN _NumHits int(11)
)
BEGIN


	UPDATE OfferStatisticsData
	SET 
    
    TotalLeads = (TotalLeads + _NumHits),
    TotalLeadsFromFacebookRRSS = (TotalLeadsFromFacebookRRSS + _NumHits)
    WHERE IdOffer = _OfferId;
    
 
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `ShOfferStatisticsDataSumLeadsFromIndeed` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `ShOfferStatisticsDataSumLeadsFromIndeed`(IN _OfferId int(11), IN _NumHits int(11))
BEGIN


	UPDATE OfferStatisticsData
	SET 
    
    TotalLeads = (TotalLeads + _NumHits),
    TotalLeadsFromIndeed = (TotalLeadsFromIndeed + _NumHits)
    WHERE IdOffer = _OfferId;
    
 
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `ShOfferStatisticsDataSumLeadsFromLinkedIn` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `ShOfferStatisticsDataSumLeadsFromLinkedIn`(
  IN _OfferId int(11),
  IN _NumHits int(11)
)
BEGIN


	UPDATE OfferStatisticsData
	SET 
    
    TotalLeads = (TotalLeads + _NumHits),
    TotalLeadsFromLinkedIn = (TotalLeadsFromLinkedIn + _NumHits)
    WHERE IdOffer = _OfferId;
    
 
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `ShOfferStatisticsDataSumLeadsFromLinkedInRRSS` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `ShOfferStatisticsDataSumLeadsFromLinkedInRRSS`(
  IN _OfferId int(11),
  IN _NumHits int(11)
)
BEGIN


	UPDATE OfferStatisticsData
	SET 
    
    TotalLeads = (TotalLeads + _NumHits),
    TotalLeadsFromLinkedInRRSS = (TotalLeadsFromLinkedInRRSS + _NumHits)
    WHERE IdOffer = _OfferId;
    
 
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `ShOfferStatisticsDataSumLeadsFromOtherAggregators` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `ShOfferStatisticsDataSumLeadsFromOtherAggregators`(
  IN _OfferId int(11),
  IN _NumHits int(11)
)
BEGIN


	UPDATE OfferStatisticsData
	SET 
    
    TotalLeads = (TotalLeads + _NumHits),
    TotalLeadsFromOtherAggregators = (TotalLeadsFromOtherAggregators + _NumHits)
    WHERE IdOffer = _OfferId;
    
 
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `ShOfferStatisticsDataSumLeadsFromTwitterRRSS` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `ShOfferStatisticsDataSumLeadsFromTwitterRRSS`(
  IN _OfferId int(11),
  IN _NumHits int(11)
)
BEGIN


	UPDATE OfferStatisticsData
	SET 
    
    TotalLeads = (TotalLeads + _NumHits),
    TotalLeadsFromTwitterRRSS = (TotalLeadsFromTwitterRRSS + _NumHits)
    WHERE IdOffer = _OfferId;
    
 
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `ShOfferStatisticsDataSumPersonalizedTestFinished` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `ShOfferStatisticsDataSumPersonalizedTestFinished`(
  IN _OfferId int(11),
  IN _NumHits int(11)
)
BEGIN


	UPDATE OfferStatisticsData
	SET NumPersonalizedTestFinished = (NumPersonalizedTestFinished + _NumHits)
    WHERE IdOffer = _OfferId;
    
 
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `ShOfferStatisticsDataSumPersonalizedTestSent` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `ShOfferStatisticsDataSumPersonalizedTestSent`(
  IN _OfferId int(11),
  IN _NumHits int(11)
)
BEGIN


	UPDATE OfferStatisticsData
	SET NumPersonalizedTestSent = (NumPersonalizedTestSent + _NumHits)
    WHERE IdOffer = _OfferId;
    
 
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `ShOfferStatisticsDataSumSocialNetwork` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `ShOfferStatisticsDataSumSocialNetwork`(
  IN _OfferId int(11),
  IN _NumHits int(11)
)
BEGIN


	UPDATE OfferStatisticsData
	SET NumSocialNetworks = (NumSocialNetworks + _NumHits)
    WHERE IdOffer = _OfferId;
    
 
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `ShOfferStatisticsDataSumTestsFinished` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `ShOfferStatisticsDataSumTestsFinished`(
  IN _OfferId int(11),
  IN _NumHits int(11)
)
BEGIN


	UPDATE OfferStatisticsData
	SET NumTestsFinished = (NumTestsFinished + _NumHits)
    WHERE IdOffer = _OfferId;
    
 
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `ShOfferStatisticsDataSumTestsSent` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `ShOfferStatisticsDataSumTestsSent`(
  IN _OfferId int(11),
  IN _NumHits int(11)
)
BEGIN


	UPDATE OfferStatisticsData
	SET NumTestsSent = (NumTestsSent + _NumHits)
    WHERE IdOffer = _OfferId;
    
 
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `ShOfferStatisticsDataSumVideoInterviewsFinished` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `ShOfferStatisticsDataSumVideoInterviewsFinished`(
  IN _OfferId int(11),
  IN _NumHits int(11)
)
BEGIN


	UPDATE OfferStatisticsData
	SET NumVideoInterviewsFinished = (NumVideoInterviewsFinished + _NumHits)
    WHERE IdOffer = _OfferId;
    
 
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `ShOfferStatisticsDataSumVideoInterviewsSent` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `ShOfferStatisticsDataSumVideoInterviewsSent`(
  IN _OfferId int(11),
  IN _NumHits int(11)
)
BEGIN


	UPDATE OfferStatisticsData
	SET NumVideoInterviewsSent = (NumVideoInterviewsSent + _NumHits)
    WHERE IdOffer = _OfferId;
    
 
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `ShOfferStatisticsDeleteFromStack` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `ShOfferStatisticsDeleteFromStack`(	
    IN _id INT
)
BEGIN

DELETE
FROM OfferStatisticsStack
WHERE Id = _id;


END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `ShOfferStatisticsGetOffersFromStack` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `ShOfferStatisticsGetOffersFromStack`(	 
    IN _pageNumber INT, 
	IN _pageSize INT,
	IN _module INT, 
	IN _residue INT
)
BEGIN
DECLARE _InitRegister INT; 


IF (_module = 0 OR _residue >= _module ) THEN
	SET _module=1;
	SET _residue=0;
END IF;



SELECT Id, IdOffer, IdType, NumHits
FROM OfferStatisticsStack
WHERE 1=1
	AND IdStatus = 0 
	AND (1=0 OR MOD(Id , _module) = _residue)
ORDER BY Id ASC
LIMIT _pageSize;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `ShOfferStatisticsUpdateFromStack` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `ShOfferStatisticsUpdateFromStack`(	
    IN _id INT,
    IN _statusId TINYINT(4)
)
BEGIN

UPDATE OfferStatisticsStack
SET IdStatus = _statusId
WHERE Id = _id;


END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `sys_kleanup` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `sys_kleanup`()
BEGIN
DECLARE _limit int;
IF _limite=0 then 
	SET _limit=10000000;
ELSE 
	SET _limit=_limite;
END IF;

delete from ApplicationRequests where CreatedOn < DATE_SUB(NOW(), INTERVAL 32 DAY);

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `TimeUseByUserAdd` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `TimeUseByUserAdd`(
IN _idUser bigint(20),
IN _idPortal tinyint,
IN _idCompany int, 
IN _dateInt int,
IN _idApp tinyint,
IN _timeInSeconds int)
BEGIN
	INSERT INTO TimeUseByUser(IdUser,IdPortal,IdCompany,DateInt,IdApp,TimeInSeconds)
    VALUES (_idUser,_idPortal,_idCompany,_dateInt,_idApp,_timeInSeconds)
    ON DUPLICATE KEY UPDATE
    TimeInSeconds = _timeInSeconds;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `TimeUseByUserSum` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `TimeUseByUserSum`(
IN _idUser bigint(20),
IN _idPortal tinyint,
IN _idCompany int, 
IN _dateInt int,
IN _idApp tinyint,
IN _timeInSeconds int)
BEGIN
	INSERT INTO TimeUseByUser(IdUser,IdPortal,IdCompany,DateInt,IdApp,TimeInSeconds)
    VALUES (_idUser,_idPortal,_idCompany,_dateInt,_idApp,_timeInSeconds)
    ON DUPLICATE KEY UPDATE
    TimeInSeconds = TimeInSeconds + _timeInSeconds;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `UpdateMatchesOfferMailSettings` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`%` PROCEDURE `UpdateMatchesOfferMailSettings`(IN _idOffer INT, IN _idCompany INT, IN _idPortal INT, IN _idUser BIGINT, IN _idStatus INT)
BEGIN



   UPDATE MatchesOfferMailSettings SET IdUser = _idUser, ModifiedOn = fn_now_by_portal(_idPortal), IdStatus = _idStatus

   WHERE IdOffer = _idOffer and IdCompany = _idCompany and IdPortal = _idPortal;



END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `UpdateStatusByUserMatchesOfferMailSettings` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`%` PROCEDURE `UpdateStatusByUserMatchesOfferMailSettings`(IN _idOffer INT, IN _idCompany INT, IN _idPortal INT, IN _idUser BIGINT, IN _idStatus INT)
BEGIN



   UPDATE MatchesOfferMailSettings SET IdUser = _idUser, ModifiedOn = fn_now_by_portal(_idPortal), IdStatus = _idStatus

   WHERE IdOffer = _idOffer and IdCompany = _idCompany and IdPortal = _idPortal;



END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `UpdateStatusMatchesOfferMailSettings` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`%` PROCEDURE `UpdateStatusMatchesOfferMailSettings`(IN _idOffer INT, IN _idCompany INT, IN _idPortal INT, IN _idStatus INT)
BEGIN



   UPDATE MatchesOfferMailSettings SET IdStatus = _idStatus, ModifiedOn = fn_now_by_portal(_idPortal)

   WHERE IdOffer = _idOffer and IdCompany = _idCompany and IdPortal = _idPortal;



END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `UpdateStatusMatchOfferMail` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`%` PROCEDURE `UpdateStatusMatchOfferMail`(IN _idMatchOfferMail BIGINT, IN _idStatus TINYINT)
BEGIN



  UPDATE MatchOfferMail SET IdStatus = _idStatus WHERE Id = _idMatchOfferMail;



END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
SET @@SESSION.SQL_LOG_BIN = @MYSQLDUMP_TEMP_LOG_BIN;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-03-07 10:24:16
