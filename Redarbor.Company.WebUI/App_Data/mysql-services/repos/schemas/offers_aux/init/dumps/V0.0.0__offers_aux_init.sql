CREATE DATABASE  IF NOT EXISTS `repo_offers_aux` /*!40100 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci */ /*!80016 DEFAULT ENCRYPTION='N' */;
USE `repo_offers_aux`;
-- MySQL dump 10.13  Distrib 8.0.40, for Win64 (x86_64)
--
-- Host: 127.0.0.1    Database: repo_offers_aux
-- ------------------------------------------------------
-- Server version	8.0.39

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `OfferActionsHistory`
--

DROP TABLE IF EXISTS `OfferActionsHistory`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `OfferActionsHistory` (
  `IdOffer` int NOT NULL,
  `IdUser` int NOT NULL,
  `Dateint` bigint NOT NULL,
  `DateTime` datetime NOT NULL,
  `TypeAction` int NOT NULL,
  KEY `IdxOffer` (`IdOffer`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `OfferConsistencyErrors`
--

DROP TABLE IF EXISTS `OfferConsistencyErrors`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `OfferConsistencyErrors` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `IdOfferCT` int NOT NULL,
  `IdPortal` tinyint NOT NULL,
  `IsntElastic` tinyint NOT NULL,
  `IsntRead` tinyint NOT NULL,
  `CreatedOn` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `CreatedOnInt` int NOT NULL DEFAULT '0',
  PRIMARY KEY (`Id`),
  KEY `idx_createdonInt` (`CreatedOnInt`)
) ENGINE=InnoDB AUTO_INCREMENT=429741 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `OfferCtIntegratorVersion`
--

DROP TABLE IF EXISTS `OfferCtIntegratorVersion`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `OfferCtIntegratorVersion` (
  `Id` int NOT NULL DEFAULT '0',
  `IdOffer` int NOT NULL DEFAULT '0',
  `IdIntegrator` tinyint NOT NULL DEFAULT '0',
  `PublicationDays` tinyint DEFAULT '0',
  `UrlRewrite` varchar(500) DEFAULT '',
  `CreatedOn` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `CreatedBy` int NOT NULL DEFAULT '1',
  `UpdatedOn` datetime DEFAULT NULL,
  `DeletedOn` datetime DEFAULT NULL,
  `IsPayment` tinyint(1) DEFAULT '0',
  `IdStatus` tinyint NOT NULL DEFAULT '1',
  `ClientIpAdd` varchar(20) DEFAULT '',
  `ClientIpMod` varchar(20) DEFAULT '',
  `IsHighlighted` tinyint DEFAULT '0',
  `IsUrgent` tinyint DEFAULT '0',
  `IsFlash` tinyint(1) NOT NULL DEFAULT '0',
  `ShowContactEmail` tinyint NOT NULL DEFAULT '0',
  `ContactEmail` varchar(100) DEFAULT '',
  `ShowContactPhone` tinyint DEFAULT '0',
  `ContactPhone` varchar(30) DEFAULT '',
  `ShowContactAddress` tinyint(1) DEFAULT '0',
  `ContactAddress` varchar(250) DEFAULT '',
  `TotalModeratedTimes` int NOT NULL DEFAULT '0',
  `ModerationTime` datetime DEFAULT NULL,
  `ModeratedBy` int NOT NULL DEFAULT '0',
  `IdProduct` int NOT NULL DEFAULT '0',
  `IdCompanyProduct` int NOT NULL DEFAULT '0',
  `PublicationTime` datetime DEFAULT NULL,
  `ExpirationTime` datetime DEFAULT NULL,
  `ExternalApplicationEmail` varchar(100) DEFAULT NULL,
  `ExternalReference` varchar(255) DEFAULT NULL,
  `AutoPublished` tinyint NOT NULL DEFAULT '0',
  `RenewStatus` tinyint NOT NULL DEFAULT '1',
  `TotalApplied` int DEFAULT '0',
  `Hidden` tinyint(1) DEFAULT '0',
  `LastVersionAdd` datetime DEFAULT NULL,
  `Version` int NOT NULL DEFAULT '0',
  PRIMARY KEY (`Id`,`IdOffer`,`IdIntegrator`,`Version`),
  KEY `ix1` (`IdOffer`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `OfferHistory`
--

DROP TABLE IF EXISTS `OfferHistory`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `OfferHistory` (
  `Id` int NOT NULL,
  `IdPortal` tinyint NOT NULL DEFAULT '0',
  `Cargo` varchar(500) NOT NULL,
  `Complement` varchar(100) DEFAULT '',
  `Title` varchar(500) NOT NULL,
  `IdCategory` tinyint NOT NULL,
  `Description` varchar(6000) NOT NULL,
  `IdCountry` tinyint unsigned NOT NULL,
  `IdLocalization` tinyint NOT NULL,
  `IdCity` smallint NOT NULL,
  `IdOtherLocalizations` varchar(100) DEFAULT '',
  `IdEmploymentType` tinyint NOT NULL,
  `IdContractType` tinyint NOT NULL,
  `StartTime` date DEFAULT NULL,
  `Vacancies` smallint DEFAULT '0',
  `ExperienceYears` tinyint NOT NULL DEFAULT '0',
  `MinAge` tinyint NOT NULL DEFAULT '0',
  `MaxAge` tinyint NOT NULL DEFAULT '0',
  `IdStudy` tinyint NOT NULL DEFAULT '0',
  `ResidenceChange` bit(1) NOT NULL DEFAULT b'0',
  `Travel` bit(1) NOT NULL DEFAULT b'0',
  `Disability` tinyint NOT NULL DEFAULT '0',
  `ShowSalary` bit(1) NOT NULL DEFAULT b'0',
  `Salary` int NOT NULL,
  `HiddenCompany` bit(1) DEFAULT b'0',
  `HiddenCompanyName` varchar(50) NOT NULL DEFAULT '',
  `IdCompany` int NOT NULL DEFAULT '0',
  `IdParentCompany` int NOT NULL DEFAULT '0',
  `IdRolCreatedBy` tinyint NOT NULL,
  `DateLastUp` datetime DEFAULT NULL,
  `IdStatus` tinyint NOT NULL DEFAULT '2',
  `HasKillerQuestions` bit(1) NOT NULL DEFAULT b'0',
  `IdCargo` int NOT NULL DEFAULT '0',
  `CargoResultado` tinyint NOT NULL DEFAULT '0',
  `CreatedOn` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `CreatedBy` int NOT NULL DEFAULT '1',
  `DeletedOn` datetime DEFAULT NULL,
  `UpdatedOn` datetime DEFAULT NULL,
  PRIMARY KEY (`Id`,`IdPortal`),
  KEY `IdxCountry` (`IdPortal`,`IdCountry`),
  KEY `IdxLocalization` (`IdPortal`,`IdLocalization`),
  KEY `IdxCity` (`IdPortal`,`IdCity`),
  KEY `IdxContractType` (`IdPortal`,`IdContractType`),
  KEY `IdxStudy` (`IdPortal`,`IdStudy`),
  KEY `IdxCompany` (`IdPortal`,`IdCompany`),
  KEY `IdxCargoResult` (`IdPortal`,`CargoResultado`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `OfferIntegratorsHistory`
--

DROP TABLE IF EXISTS `OfferIntegratorsHistory`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `OfferIntegratorsHistory` (
  `IdOffer` int NOT NULL,
  `IdPortal` tinyint NOT NULL,
  `IdIntegrator` tinyint NOT NULL,
  `PublicationDays` tinyint DEFAULT '0',
  `UrlRewrite` varchar(500) DEFAULT '',
  `CreatedOn` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `CreatedBy` int NOT NULL DEFAULT '1',
  `UpdatedOn` datetime DEFAULT NULL,
  `DeletedOn` datetime DEFAULT NULL,
  `IsPayment` tinyint(1) DEFAULT '0',
  `IdStatus` tinyint NOT NULL DEFAULT '1',
  `ClientIpAdd` varchar(20) DEFAULT '',
  `ClientIpMod` varchar(20) DEFAULT '',
  `IsHighlighted` bit(1) DEFAULT b'0',
  `IsUrgent` bit(1) DEFAULT b'0',
  `IsFlash` tinyint(1) NOT NULL DEFAULT '0',
  `ShowContactEmail` bit(1) NOT NULL DEFAULT b'0',
  `ContactEmail` varchar(100) DEFAULT '',
  `ShowContactPhone` bit(1) DEFAULT b'0',
  `ContactPhone` varchar(30) DEFAULT '',
  `ShowContactAddress` tinyint(1) DEFAULT '0',
  `ContactAddress` varchar(250) DEFAULT '',
  `TotalModeratedTimes` int NOT NULL DEFAULT '0',
  `ModerationTime` datetime DEFAULT NULL,
  `ModeratedBy` int NOT NULL DEFAULT '0',
  `IdProduct` int NOT NULL DEFAULT '0',
  `IdCompanyProduct` int NOT NULL DEFAULT '0',
  `PublicationTime` datetime DEFAULT NULL,
  `ExpirationTime` datetime DEFAULT NULL,
  `ExternalApplicationEmail` varchar(100) DEFAULT NULL,
  `ExternalReference` varchar(255) DEFAULT NULL,
  `AutoPublished` tinyint NOT NULL DEFAULT '0',
  `RenewStatus` tinyint NOT NULL DEFAULT '1',
  PRIMARY KEY (`IdOffer`,`IdPortal`,`IdIntegrator`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `OfferVersion`
--

DROP TABLE IF EXISTS `OfferVersion`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `OfferVersion` (
  `Id` int NOT NULL,
  `IdPortal` tinyint NOT NULL DEFAULT '0',
  `Cargo` varchar(500) NOT NULL,
  `Complement` varchar(100) DEFAULT '',
  `Title` varchar(200) NOT NULL,
  `IdCategory` tinyint NOT NULL,
  `Description` varchar(6000) NOT NULL,
  `IdCountry` tinyint unsigned NOT NULL,
  `IdLocalization` tinyint NOT NULL,
  `IdCity` smallint NOT NULL,
  `IdOtherLocalizations` varchar(100) DEFAULT '',
  `IdEmploymentType` tinyint NOT NULL,
  `IdContractType` tinyint NOT NULL,
  `StartTime` date DEFAULT NULL,
  `Vacancies` smallint DEFAULT '0',
  `ExperienceYears` tinyint NOT NULL DEFAULT '0',
  `MinAge` tinyint NOT NULL DEFAULT '0',
  `MaxAge` tinyint NOT NULL DEFAULT '0',
  `IdStudy` tinyint NOT NULL DEFAULT '0',
  `ResidenceChange` tinyint NOT NULL DEFAULT '0',
  `Travel` tinyint NOT NULL DEFAULT '0',
  `Disability` tinyint NOT NULL DEFAULT '0',
  `ShowSalary` tinyint NOT NULL DEFAULT '0',
  `Salary` int NOT NULL,
  `HiddenCompany` bit(1) DEFAULT b'0',
  `HiddenCompanyName` varchar(50) NOT NULL DEFAULT '',
  `IdCompany` int NOT NULL DEFAULT '0',
  `IdParentCompany` int NOT NULL DEFAULT '0',
  `IdRolCreatedBy` tinyint NOT NULL,
  `DateLastUp` datetime DEFAULT NULL,
  `IdStatus` tinyint NOT NULL DEFAULT '2',
  `HasKillerQuestions` tinyint NOT NULL DEFAULT '0',
  `IdCargo` int NOT NULL DEFAULT '0',
  `CargoResultado` tinyint NOT NULL DEFAULT '0',
  `CreatedOn` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `CreatedBy` int NOT NULL DEFAULT '1',
  `DeletedOn` datetime DEFAULT NULL,
  `UpdatedOn` datetime DEFAULT NULL,
  `LastVersionAdd` datetime DEFAULT NULL,
  `Version` int NOT NULL DEFAULT '0',
  PRIMARY KEY (`Id`,`IdPortal`,`Version`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `x_company_products_001`
--

DROP TABLE IF EXISTS `x_company_products_001`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `x_company_products_001` (
  `id` int NOT NULL AUTO_INCREMENT,
  `product_id` int NOT NULL DEFAULT '0',
  `company_id` int NOT NULL DEFAULT '0',
  `portal_id` tinyint DEFAULT '0',
  `group_id` tinyint NOT NULL DEFAULT '0',
  `status_id` tinyint NOT NULL DEFAULT '2',
  PRIMARY KEY (`id`),
  KEY `UK_company_products_company_id` (`company_id`,`portal_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci CONNECTION='mysql://lector:<EMAIL>:3306/computrabajo_01/company_products';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `x_company_products_002`
--

DROP TABLE IF EXISTS `x_company_products_002`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `x_company_products_002` (
  `id` int NOT NULL AUTO_INCREMENT,
  `product_id` int NOT NULL DEFAULT '0',
  `company_id` int NOT NULL DEFAULT '0',
  `portal_id` tinyint DEFAULT '0',
  `group_id` tinyint NOT NULL DEFAULT '0',
  `status_id` tinyint NOT NULL DEFAULT '2',
  PRIMARY KEY (`id`),
  KEY `UK_company_products_company_id` (`company_id`,`portal_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci CONNECTION='mysql://lector:<EMAIL>:3306/computrabajo_02/company_products';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `x_company_products_bj01`
--

DROP TABLE IF EXISTS `x_company_products_bj01`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `x_company_products_bj01` (
  `id` int NOT NULL AUTO_INCREMENT,
  `product_id` int NOT NULL DEFAULT '0',
  `company_id` int NOT NULL DEFAULT '0',
  `portal_id` tinyint DEFAULT '0',
  `group_id` tinyint NOT NULL DEFAULT '0',
  `status_id` tinyint NOT NULL DEFAULT '2',
  PRIMARY KEY (`id`),
  KEY `UK_company_products_company_id` (`company_id`,`portal_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci CONNECTION='mysql://lector:<EMAIL>:3306/bestjobs_01/company_products';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `x_company_products_bj02`
--

DROP TABLE IF EXISTS `x_company_products_bj02`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `x_company_products_bj02` (
  `id` int NOT NULL AUTO_INCREMENT,
  `product_id` int NOT NULL DEFAULT '0',
  `company_id` int NOT NULL DEFAULT '0',
  `portal_id` tinyint DEFAULT '0',
  `group_id` tinyint NOT NULL DEFAULT '0',
  `status_id` tinyint NOT NULL DEFAULT '2',
  PRIMARY KEY (`id`),
  KEY `UK_company_products_company_id` (`company_id`,`portal_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci CONNECTION='mysql://lector:<EMAIL>:3306/bestjobs_02/company_products';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `x_company_products_col`
--

DROP TABLE IF EXISTS `x_company_products_col`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `x_company_products_col` (
  `id` int NOT NULL AUTO_INCREMENT,
  `product_id` int NOT NULL DEFAULT '0',
  `company_id` int NOT NULL DEFAULT '0',
  `portal_id` tinyint DEFAULT '0',
  `group_id` tinyint NOT NULL DEFAULT '0',
  `status_id` tinyint NOT NULL DEFAULT '2',
  PRIMARY KEY (`id`),
  KEY `UK_company_products_company_id` (`company_id`,`portal_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci CONNECTION='mysql://lector:<EMAIL>:3306/computrabajo_co/company_products';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `x_company_products_mex`
--

DROP TABLE IF EXISTS `x_company_products_mex`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `x_company_products_mex` (
  `id` int NOT NULL AUTO_INCREMENT,
  `product_id` int NOT NULL DEFAULT '0',
  `company_id` int NOT NULL DEFAULT '0',
  `portal_id` tinyint DEFAULT '0',
  `group_id` tinyint NOT NULL DEFAULT '0',
  `status_id` tinyint NOT NULL DEFAULT '2',
  PRIMARY KEY (`id`),
  KEY `UK_company_products_company_id` (`company_id`,`portal_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci CONNECTION='mysql://lector:<EMAIL>:3306/computrabajo_mx/company_products';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `z_company_products`
--

DROP TABLE IF EXISTS `z_company_products`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `z_company_products` (
  `id` int NOT NULL,
  `product_id` int NOT NULL DEFAULT '0',
  `company_id` int NOT NULL DEFAULT '0',
  `portal_id` tinyint NOT NULL DEFAULT '0',
  `group_id` tinyint NOT NULL DEFAULT '0',
  `status_id` tinyint NOT NULL DEFAULT '2',
  PRIMARY KEY (`id`,`portal_id`),
  KEY `UK_company_products_company_id` (`company_id`,`portal_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `z_ofertas`
--

DROP TABLE IF EXISTS `z_ofertas`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `z_ofertas` (
  `idoffer` bigint NOT NULL,
  PRIMARY KEY (`idoffer`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


--
-- Dumping routines for database 'repo_offers_aux'
--
/*!50003 DROP FUNCTION IF EXISTS `functionnowbyportal` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_ENGINE_SUBSTITUTION' */ ;

DELIMITER ;;
CREATE DEFINER=`root`@`localhost` FUNCTION `functionnowbyportal`(_idportal tinyint) RETURNS datetime
    DETERMINISTIC
BEGIN 
DECLARE l_offset int; 
DECLARE l_now DATETIME;
DECLARE l_time_zone CHAR(64);

SET SESSION TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

CASE _idportal
WHEN 0 THEN SELECT 'etc/UTC' INTO l_time_zone;

  WHEN 1 THEN SELECT 'America/Bogota' INTO l_time_zone;
  WHEN 2 THEN SELECT 'America/Mexico_City' INTO l_time_zone;
  WHEN 3 THEN SELECT 'America/Lima' INTO l_time_zone;
  WHEN 4 THEN SELECT 'America/Santiago' INTO l_time_zone;
  WHEN 5 THEN SELECT 'America/Argentina/Buenos_Aires' INTO l_time_zone;
  WHEN 6 THEN SELECT 'America/Guayaquil' INTO l_time_zone;
  WHEN 7 THEN SELECT 'America/Caracas' INTO l_time_zone;
  WHEN 8 THEN SELECT 'America/Costa_Rica' INTO l_time_zone;
  WHEN 9 THEN SELECT 'America/Guatemala' INTO l_time_zone;
  WHEN 10 THEN SELECT 'America/El_Salvador' INTO l_time_zone;
  WHEN 11 THEN SELECT 'America/Montevideo' INTO l_time_zone;
  WHEN 12 THEN SELECT 'America/Asuncion' INTO l_time_zone;
  WHEN 13 THEN SELECT 'America/Panama' INTO l_time_zone;
  WHEN 14 THEN SELECT 'America/Tegucigalpa' INTO l_time_zone;
  WHEN 15 THEN SELECT 'America/Managua' INTO l_time_zone;
  WHEN 16 THEN SELECT 'America/Santo_Domingo' INTO l_time_zone;
  WHEN 17 THEN SELECT 'America/La_Paz' INTO l_time_zone;
  WHEN 18 THEN SELECT 'America/Havana' INTO l_time_zone;
  WHEN 19 THEN SELECT 'America/Puerto_Rico' INTO l_time_zone;
  WHEN 37 THEN SELECT 'Europe/Madrid' INTO l_time_zone;

	WHEN 20 THEN SELECT 'Australia/Canberra' INTO l_time_zone;
	WHEN 21 THEN SELECT 'Canada/Eastern' INTO l_time_zone;
	WHEN 22 THEN SELECT 'Europe/Dublin' INTO l_time_zone;
	WHEN 23 THEN SELECT 'Antarctica/McMurdo' INTO l_time_zone;
	WHEN 24 THEN SELECT 'Africa/Johannesburg' INTO l_time_zone;
	WHEN 25 THEN SELECT 'Europe/London' INTO l_time_zone;
	WHEN 26 THEN SELECT 'EST' INTO l_time_zone;
	WHEN 27 THEN SELECT 'Asia/Singapore' INTO l_time_zone;
	WHEN 28 THEN SELECT 'Asia/Calcutta' INTO l_time_zone;
	WHEN 29 THEN SELECT 'Asia/Kuala_Lumpur' INTO l_time_zone;
	WHEN 30 THEN SELECT 'Asia/Manila' INTO l_time_zone;
	WHEN 31 THEN SELECT 'Africa/Nairobi' INTO l_time_zone;
	WHEN 32 THEN SELECT 'Asia/Jakarta' INTO l_time_zone;
	WHEN 33 THEN SELECT 'Asia/Hong_Kong' INTO l_time_zone;

	WHEN 34 THEN SELECT 'Europe/Madrid' INTO l_time_zone;
	WHEN 35 THEN SELECT 'Europe/Madrid' INTO l_time_zone;

  	WHEN 36 THEN SELECT 'Europe/Istanbul' INTO l_time_zone;
	WHEN 38 THEN SELECT 'Africa/Casablanca' INTO l_time_zone;

    WHEN 99 THEN SELECT 'America/Mexico_City' INTO l_time_zone;
ELSE SELECT 'etc/UTC' INTO l_time_zone;
END CASE;
select CONVERT_TZ(now(),'SYSTEM',l_time_zone) INTO l_now;




SET SESSION TRANSACTION ISOLATION LEVEL REPEATABLE READ ;


RETURN l_now; 
END ;;
DELIMITER ;

/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `AeOfferCTIntegratorVersionInsert` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `AeOfferCTIntegratorVersionInsert`(
	IN _idOffer INT, IN _IdPortal INT)
BEGIN

 DECLARE SET_VERSION INT;
 SET SET_VERSION = 2;
 SELECT MAX(Version+1) INTO SET_VERSION FROM OfferCtIntegratorVersion WHERE IdOffer=_idOffer;
 
 If isNull(SET_VERSION) THEN  SET SET_VERSION = 0;
 END IF;
 
    INSERT INTO OfferCtIntegratorVersion(
   `Id`, `IdOffer`,`IdIntegrator`,`PublicationDays`,`UrlRewrite`,`CreatedOn`,`CreatedBy`,`UpdatedOn`,`DeletedOn`,`IsPayment`,`IdStatus`,`ClientIpAdd`,`ClientIpMod`,
`IsHighlighted`,`IsUrgent`,`IsFlash`,`ShowContactEmail`,`ContactEmail`,`ShowContactPhone`,`ContactPhone`,`ShowContactAddress`,`ContactAddress`,`TotalModeratedTimes`,
`ModerationTime`,`ModeratedBy`,`IdProduct`,`IdCompanyProduct`,`PublicationTime`,`ExpirationTime`,`ExternalApplicationEmail`,`ExternalReference`,`AutoPublished`,`RenewStatus`,
`TotalApplied`,`Hidden`, `LastVersionAdd`, `Version`)
SELECT 
	`Id`, `IdOffer`,    `IdIntegrator`,    `PublicationDays`,    `UrlRewrite`,    `CreatedOn`,    `CreatedBy`,    `UpdatedOn`,    `DeletedOn`,    `IsPayment`,    `IdStatus`,
    `ClientIpAdd`,    `ClientIpMod`,    `IsHighlighted`,    `IsUrgent`,    `IsFlash`,    `ShowContactEmail`,    `ContactEmail`,    `ShowContactPhone`,    `ContactPhone`,
    `ShowContactAddress`,    `ContactAddress`,    `TotalModeratedTimes`,    `ModerationTime`,    `ModeratedBy`,    `IdProduct`,    `IdCompanyProduct`,    `PublicationTime`,
    `ExpirationTime`,    `ExternalApplicationEmail`,    `ExternalReference`,    `AutoPublished`,    `RenewStatus`,    `TotalApplied`,    `Hidden`    , functionnowbyportal(_idPortal), SET_VERSION
FROM `repo_offers`.`OfferIntegrators`
	 WHERE IdOffer = _idOffer AND IdIntegrator = 1;
 
END;;
DELIMITER ;

/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `AeOfferVersionInsert` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `AeOfferVersionInsert`(
	IN _idOffer INT,
    IN _idPortal TINYINT(3))
BEGIN

 DECLARE SET_VERSION INT;
 SET SET_VERSION = 2;
 SELECT MAX(Version+1) INTO SET_VERSION FROM OfferVersion WHERE Id=_idOffer;
 
 If isNull(SET_VERSION) THEN  SET SET_VERSION = 0;
 END IF;
 
    INSERT INTO OfferVersion(
        Id, IdPortal, Cargo, Complement, Title, IdCategory, Description, IdCountry, IdLocalization, IdCity, IdOtherLocalizations, IdEmploymentType, IdContractType,
		StartTime, Vacancies, ExperienceYears, MinAge, MaxAge, IdStudy, ResidenceChange, Travel, Disability, ShowSalary, Salary, HiddenCompany, HiddenCompanyName, IdCompany, 
        IdParentCompany, IdRolCreatedBy, DateLastUp, HasKillerQuestions, IdCargo, CreatedOn, CreatedBy, DeletedOn, UpdatedOn, LastVersionAdd, Version)
    
    SELECT 
		Id, IdPortal, Cargo, Complement, Title, IdCategory, Description, IdCountry, IdLocalization, IdCity, IdOtherLocalizations, IdEmploymentType, IdContractType,
		StartTime, Vacancies, ExperienceYears, MinAge, MaxAge, IdStudy, ResidenceChange, Travel, Disability, ShowSalary, Salary, HiddenCompany, HiddenCompanyName, IdCompany, 
        IdParentCompany, IdRolCreatedBy, DateLastUp, HasKillerQuestions, IdCargo, CreatedOn, CreatedBy, DeletedOn, UpdatedOn,  functionnowbyportal(_idPortal), SET_VERSION
	  FROM repo_offers.Offer
	 WHERE Id = _idOffer AND IdPortal = _idPortal;
 
END;;

DELIMITER;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `GetOfferLastVersion` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;

CREATE DEFINER=`root`@`localhost` PROCEDURE `GetOfferLastVersion`(
	IN _idoffer INT)
BEGIN 
    SELECT 
        Id,        
				Idportal,
				Title,
        Idcategory,
        Description,
        Idcountry,
        Idlocalization,
        Idcity,        
        Idemploymenttype,
        Idcontracttype,
				Experienceyears,
				Minage,
        Maxage,
				Idstudy,
				Residencechange,
        Travel,
        Disability,
        Salary,
        ShowSalary,      
				Hiddencompany,
				HiddenCompanyName
    FROM OfferVersion    
    WHERE 1 = 1 
		AND Id = _idoffer
	ORDER BY Version desc
	LIMIT 1;
		  
END;;

DELIMITER;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `OfferActionsHistoryInsert` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;

CREATE DEFINER=`root`@`localhost` PROCEDURE `OfferActionsHistoryInsert`(
    IN _idPortal INT, 
    IN _idOffer INT, 
    IN _idUser INT,  
    IN _dateint BIGINT(20), 
    IN _typeAction INT
)
BEGIN
  INSERT INTO OfferActionsHistory(IdOffer,IdUser,Dateint,DateTime,TypeAction)
  VALUES (_idOffer,_idUser,_dateint,functionnowbyportal(_idPortal),_typeAction);   

END;;

DELIMITER;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `OfferHistoryGetGroupedSalaryProcessByIdcountry` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;

CREATE DEFINER=`root`@`localhost` PROCEDURE `OfferHistoryGetGroupedSalaryProcessByIdcountry`(
	IN `_idPortal` TINYINT,
	IN `_idCountry` INT,
	IN `_lastId` INT,
	IN `_limit` INT
)
BEGIN

SELECT 
	oh.Id,
	oh.idportal, 
	oh.idcategory, 
	oh.salary, 
	oh.idcompany, 
	oh.idcargo , 
	oh.CreatedOn, 
	oh.showsalary, 
	oh.hiddencompany, 
	oh.IdCargo
FROM OfferHistory oh 
	INNER JOIN OfferIntegratorsHistory oih
	ON oh.Id = oih.IdOffer AND oh.IdPortal = oih.IdPortal
WHERE 
	oh.idportal = _idPortal
	AND oh.idcountry = _idCountry 
	AND oih.IdIntegrator = 1
	AND oh.id > _lastId
LIMIT _limit;
	
END;;

DELIMITER;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `OfferHistoryInsert` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;

CREATE DEFINER=`root`@`localhost` PROCEDURE `OfferHistoryInsert`(
	IN _idOffer INT,
    IN _idPortal TINYINT(3))
BEGIN
 
    INSERT INTO OfferHistory(
        Id, IdPortal, Cargo, Complement, Title, IdCategory, Description, IdCountry, IdLocalization, IdCity, IdOtherLocalizations, IdEmploymentType, IdContractType,
		StartTime, Vacancies, ExperienceYears, MinAge, MaxAge, IdStudy, ResidenceChange, Travel, Disability, ShowSalary, Salary, HiddenCompany, HiddenCompanyName, IdCompany, 
        IdParentCompany, IdRolCreatedBy, DateLastUp, HasKillerQuestions, IdCargo, CreatedOn, CreatedBy, DeletedOn, UpdatedOn)
    
    SELECT 
		Id, IdPortal, Cargo, Complement, Title, IdCategory, Description, IdCountry, IdLocalization, IdCity, IdOtherLocalizations, IdEmploymentType, IdContractType,
		StartTime, Vacancies, ExperienceYears, MinAge, MaxAge, IdStudy, ResidenceChange, Travel, Disability, ShowSalary, Salary, HiddenCompany, HiddenCompanyName, IdCompany, 
        IdParentCompany, IdRolCreatedBy, DateLastUp, HasKillerQuestions, IdCargo, CreatedOn, CreatedBy, DeletedOn, UpdatedOn
	  FROM repo_offers.Offer
	 WHERE Id = _idOffer AND IdPortal = _idPortal;
 
END;;

DELIMITER;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `OfferIntegratorsHistoryDeleteByIdOffer` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;


CREATE DEFINER=`root`@`localhost` PROCEDURE `OfferIntegratorsHistoryDeleteByIdOffer`(
  IN _idOffer INT,
  IN _idPortal TINYINT(3),
  IN _idIntegrator TINYINT(4))
BEGIN

	DELETE FROM OfferIntegratorsHistory WHERE IdOffer = _idOffer AND IdPortal = _idPortal AND IdIntegrator = _idIntegrator;
  
END;;

DELIMITER;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `OfferIntegratorsHistoryInsert` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;

CREATE DEFINER=`root`@`localhost` PROCEDURE `OfferIntegratorsHistoryInsert`(
	IN _idOffer INT,
    IN _idIntegrator TINYINT(4))
BEGIN
 
    INSERT INTO OfferIntegratorsHistory(
        Id,
		IdOffer,
		IdIntegrator,
		PublicationDays,
		UrlRewrite,
		CreatedOn,
		CreatedBy,
		UpdatedOn,
		DeletedOn,
		IsPayment,
		IdStatus,
		ClientIpAdd,
		ClientIpMod,
		IsHighlighted,
		IsUrgent,
		IsFlash,
		ShowContactEmail,
		ContactEmail,
		ShowContactPhone,
		ContactPhone,
		ShowContactAddress,
		ContactAddress, 
		TotalModeratedTimes,
		ModerationTime,
		ModeratedBy,
		IdProduct,
		IdCompanyProduct,
		PublicationTime,
		ExpirationTime,
		ExternalApplicationEmail,
		ExternalReference,
		AutoPublished,
		RenewStatus)    
    SELECT 
		Id,
		IdOffer,
		IdIntegrator,
		PublicationDays,
		UrlRewrite,
		CreatedOn,
		CreatedBy,
		UpdatedOn,
		DeletedOn,
		IsPayment,
		IdStatus,
		ClientIpAdd,
		ClientIpMod,
		IsHighlighted,
		IsUrgent,
		IsFlash,
		ShowContactEmail,
		ContactEmail,
		ShowContactPhone,
		ContactPhone,
		ShowContactAddress,
		ContactAddress, 
		TotalModeratedTimes,
		ModerationTime,
		ModeratedBy,
		IdProduct,
		IdCompanyProduct,
		PublicationTime,
		ExpirationTime,
		ExternalApplicationEmail,
		ExternalReference,
		AutoPublished,
		RenewStatus
	  FROM repo_offers.OfferIntegrators
	 WHERE IdOffer = _idOffer AND IdIntegrator = _idIntegrator;
 
END;;

DELIMITER;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `ReportsCompanyGetOfferHistoryTypeAction` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;

CREATE DEFINER=`root`@`localhost` PROCEDURE `ReportsCompanyGetOfferHistoryTypeAction`(
    _idOffer INT,
    _typeAction INT
	)
BEGIN
    SELECT DateTime 		
    FROM OfferActionsHistory   
    WHERE IdOffer = _idOffer
      AND TypeAction = _typeAction
      ORDER BY Dateint desc;
END;;

DELIMITER;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `ReportsCompanyGetOfferHistoryTypeAction_V2` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;

CREATE DEFINER=`root`@`localhost` PROCEDURE `ReportsCompanyGetOfferHistoryTypeAction_V2`(
    IN _idOffers  TEXT,
    IN _typeAction INT
	)
BEGIN
  SET @_Query = CONCAT(
		'SELECT IdOffer , max(DateTime) as DateTime
		  FROM OfferActionsHistory  
		  WHERE IdOffer in (', _idOffers, ')
		  AND TypeAction in (', _typeAction , ')
		 GROUP BY IdOffer;');
    PREPARE smpt FROM @_Query;
    EXECUTE smpt;
    DEALLOCATE PREPARE smpt; 
END;;

DELIMITER;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `ShOfferCTIntegratorVersionInsert` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;

CREATE DEFINER=`root`@`localhost` PROCEDURE `ShOfferCTIntegratorVersionInsert`(
	IN _idOffer INT, IN _IdPortal INT)
BEGIN

 DECLARE SET_VERSION INT;
 SET SET_VERSION = 2;
 SELECT MAX(Version+1) INTO SET_VERSION FROM OfferCtIntegratorVersion WHERE IdOffer=_idOffer;
 
 If isNull(SET_VERSION) THEN  SET SET_VERSION = 0;
 END IF;
 
    INSERT INTO OfferCtIntegratorVersion(
   `Id`, `IdOffer`,`IdIntegrator`,`PublicationDays`,`UrlRewrite`,`CreatedOn`,`CreatedBy`,`UpdatedOn`,`DeletedOn`,`IsPayment`,`IdStatus`,`ClientIpAdd`,`ClientIpMod`,
`IsHighlighted`,`IsUrgent`,`IsFlash`,`ShowContactEmail`,`ContactEmail`,`ShowContactPhone`,`ContactPhone`,`ShowContactAddress`,`ContactAddress`,`TotalModeratedTimes`,
`ModerationTime`,`ModeratedBy`,`IdProduct`,`IdCompanyProduct`,`PublicationTime`,`ExpirationTime`,`ExternalApplicationEmail`,`ExternalReference`,`AutoPublished`,`RenewStatus`,
`TotalApplied`,`Hidden`, `LastVersionAdd`, `Version`)
SELECT 
	`Id`, `IdOffer`,    `IdIntegrator`,    `PublicationDays`,    `UrlRewrite`,    `CreatedOn`,    `CreatedBy`,    `UpdatedOn`,    `DeletedOn`,    `IsPayment`,    `IdStatus`,
    `ClientIpAdd`,    `ClientIpMod`,    `IsHighlighted`,    `IsUrgent`,    `IsFlash`,    `ShowContactEmail`,    `ContactEmail`,    `ShowContactPhone`,    `ContactPhone`,
    `ShowContactAddress`,    `ContactAddress`,    `TotalModeratedTimes`,    `ModerationTime`,    `ModeratedBy`,    `IdProduct`,    `IdCompanyProduct`,    `PublicationTime`,
    `ExpirationTime`,    `ExternalApplicationEmail`,    `ExternalReference`,    `AutoPublished`,    `RenewStatus`,    `TotalApplied`,    `Hidden`    , functionnowbyportal(_idPortal), SET_VERSION
FROM `repo_offers`.`OfferIntegrators`
	 WHERE IdOffer = _idOffer AND IdIntegrator = 1;
 
END;;

DELIMITER;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `ShOfferVersionInsert` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;

CREATE DEFINER=`root`@`localhost` PROCEDURE `ShOfferVersionInsert`(
	IN _idOffer INT,
    IN _idPortal TINYINT(3))
BEGIN

 DECLARE SET_VERSION INT;
 SET SET_VERSION = 2;
 SELECT MAX(Version+1) INTO SET_VERSION FROM OfferVersion WHERE Id=_idOffer;
 
 If isNull(SET_VERSION) THEN  SET SET_VERSION = 0;
 END IF;
 
    INSERT INTO OfferVersion(
        Id, IdPortal, Cargo, Complement, Title, IdCategory, Description, IdCountry, IdLocalization, IdCity, IdOtherLocalizations, IdEmploymentType, IdContractType,
		StartTime, Vacancies, ExperienceYears, MinAge, MaxAge, IdStudy, ResidenceChange, Travel, Disability, ShowSalary, Salary, HiddenCompany, HiddenCompanyName, IdCompany, 
        IdParentCompany, IdRolCreatedBy, DateLastUp, HasKillerQuestions, IdCargo, CreatedOn, CreatedBy, DeletedOn, UpdatedOn, LastVersionAdd, Version)
    
    SELECT 
		Id, IdPortal, Cargo, Complement, Title, IdCategory, Description, IdCountry, IdLocalization, IdCity, IdOtherLocalizations, IdEmploymentType, IdContractType,
		StartTime, Vacancies, ExperienceYears, MinAge, MaxAge, IdStudy, ResidenceChange, Travel, Disability, ShowSalary, Salary, HiddenCompany, HiddenCompanyName, IdCompany, 
        IdParentCompany, IdRolCreatedBy, DateLastUp, HasKillerQuestions, IdCargo, CreatedOn, CreatedBy, DeletedOn, UpdatedOn,  functionnowbyportal(_idPortal), SET_VERSION
	  FROM repo_offers.Offer
	 WHERE Id = _idOffer AND IdPortal = _idPortal;
 
END;;

DELIMITER;

/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-03-06 20:50:43
