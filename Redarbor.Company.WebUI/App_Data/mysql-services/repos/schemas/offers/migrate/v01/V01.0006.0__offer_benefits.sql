DELIMITER $$

CREATE TABLE `repo_offers`.`OfferBenefits` (
  `Id` INT NOT NULL AUTO_INCREMENT,
  `IdOffer` INT NOT NULL,
  `Benefit` VARCHAR(100) NOT NULL,
  PRIMARY KEY (`Id`),
  INDEX `Idx_IdOffer` (`idOffer`))$$

CREATE DEFINER=`root`@`localhost` PROCEDURE `ae_OfferBenefits_GetByOfferId`(
  IN _idoffer INT
)
BEGIN
  SELECT
   Id, IdOffer, Benefit
  FROM OfferBenefits
  WHERE IdOffer = _idoffer;
END$$

DELIMITER ;