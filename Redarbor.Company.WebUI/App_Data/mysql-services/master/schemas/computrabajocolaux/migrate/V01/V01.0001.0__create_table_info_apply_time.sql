DROP TABLE IF EXISTS `info_apply_time`;

CREATE TABLE `info_apply_time` (
  `idportal` int NOT NULL DEFAULT '0',
  `iduser` int NOT NULL DEFAULT '0',
  `idoffer` bigint NOT NULL DEFAULT '0',
  `applyTotalTime` int NOT NULL DEFAULT '0',
  `fetchApplyResponseTime` int NOT NULL DEFAULT '0',
  `fetchApplyInfoResponseTime` int NOT NULL DEFAULT '0',
  `clickToApplyFetchTime` int NOT NULL DEFAULT '0',
  `totalFetchTime` int NOT NULL DEFAULT '0',
  `connectionQuality` varchar(50) DEFAULT '',
  `idversion` int NOT NULL DEFAULT '0',
  `createdAt` datetime DEFAULT NULL,
  `isBeta` tinyint DEFAULT NULL,
  PRIMARY KEY (`idportal`,`iduser`,`idoffer`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 AVG_ROW_LENGTH=57 ROW_FORMAT=COMPACT