
ALTER TABLE dtinvoiceentity ADD taxregime TINYINT, ALGORITHM=INSTANT;
ALTER TABLE dtinvoiceentity ADD zipcode VARCHAR(10), ALGORITHM=INSTANT;

DELIMITER //

CREATE DEFINER=`root`@`localhost` PROCEDURE `sp_dtinvoiceentity_Update_v2`(
	_idinvoiceentity int,
	_idcompany int,
	_companyname varchar(500),
	_completeaddress longtext,
	_idcountry int,
	_tieneactividadempresarial tinyint,
	_nit varchar(50),
	_updatedby int,
	_idstatus int,
    _contactfullname varchar(250),
    _contactemail varchar(75), 
    _contacttelephone1 varchar(50),
    _zipcode varchar(10)
)
Begin
	Update dtinvoiceentity
	Set
		companyname = _companyname,
		completeaddress = _completeaddress,
		idcountry = _idcountry,
		tieneactividadempresarial = _tieneactividadempresarial,
		nit = _nit,
		updatedby = _updatedby,
		updatedon = now(),
		idstatus = _idstatus,
		contactemail = _contactemail,
		contactfullname = _contactfullname,
		contacttelephone1 = _contacttelephone1,
        zipcode = _zipcode
		Where		
			idinvoiceentity = _idinvoiceentity
		and idcompany = _idcompany;
END//

CREATE DEFINER=`root`@`localhost` PROCEDURE `sp_dtinvoiceentity_Insert_Portal_v2`(
	_idcompany INT, 
    _companyname VARCHAR(500), 
    _completeaddress LONGTEXT, 
    _idcountry INT, 
    _tieneactividadempresarial TINYINT, 
    _nit VARCHAR(50),
    _contactfullname VARCHAR(250),
    _contactemail VARCHAR(75),
    _contacttelephone1 VARCHAR(50),
    _idportal TINYINT,
    _createdby INT,
    _zipcode VARCHAR(10)
)
Begin

	Insert Into dtinvoiceentity

		(idcompany, companyname, completeaddress, idcountry, tieneactividadempresarial, nit, createdon, contactfullname, contactemail, contacttelephone1, createdby, zipcode)

	Values

		(_idcompany, _companyname, _completeaddress, _idcountry, _tieneactividadempresarial, _nit, fn_now_by_portal(_idportal), _contactfullname, _contactemail, _contacttelephone1, _createdby, _zipcode);

	SELECT LAST_INSERT_ID();

END//

CREATE DEFINER=`root`@`localhost` PROCEDURE `sp_dtinvoiceentity_SelectAllByCompany_v2`(
	_idcompany int
)
Begin
	Select
		idinvoiceentity,
		idcompany,
		companyname,
		completeaddress,
		idcountry,
		tieneactividadempresarial,
		nit,
		idstatus,
        zipcode,
		createdon,
		updatedon,
		deletedon,
		contactfullname,
		contactemail,
		contacttelephone1
		From dtinvoiceentity
		Where idcompany = _idcompany
		and idstatus = 2
		ORDER BY COALESCE(updatedon, createdon) desc
		Limit 1;
END//

DELIMITER ;