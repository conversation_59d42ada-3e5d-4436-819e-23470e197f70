DROP procedure IF EXISTS `ae_company_has_ats_v2`;

DELIMITER $$
CREATE DEFINER=`root`@`localhost` PROCEDURE `ae_company_has_ats_v2`(
    IN _idCompany INT
    )
BEGIN
 	SELECT has_ats, idcompany, idportal
    FROM dtcompany
 	WHERE idcompany = _idcompany;
 END$$

DELIMITER ;

DROP procedure IF EXISTS `api_candidate_dcandidatebypostalcode_select_by_candidate_v2`;

DELIMITER $$
CREATE DEFINER=`root`@`localhost` PROCEDURE `api_candidate_dcandidatebypostalcode_select_by_candidate_v2`(
 	_idcandidate int
)
BEGIN
 	SELECT 
 		idpostalcode as ZipCodeId
 FROM dtcandidatebypostalcode
 	WHERE
 		idcandidate = _idcandidate
 ORDER BY createdon DESC;
 END$$
DELIMITER ;

;

DROP procedure IF EXISTS `api_candidate_dtcandidate_get_v1`;

DELIMITER $$
CREATE DEFINER=`root`@`localhost` PROCEDURE `api_candidate_dtcandidate_get_v1`(
	IN _id INT,
	IN _idPortal INT
	)
BEGIN
 	SELECT
    c.idcandidate AS Id,
  	c.iduser as UserId,
    c.name as Name,
  	c.surname as Surname,
    c.idcargo AS DesiredPositionId,
    c.wishedjob AS DesiredPosition,
  	c.birthdate AS BirthDate,
  	c.idcountry AS CountryId,
  	c.idlocalization AS ProvinceId,
  	c.idcity AS CityId,
  	c.phone1 AS Phone1,
  	c.photo AS Photo,
    c.ididentificationtype AS NinTypeId,
    c.nit AS Nin,
    c.idportal AS Portal,
  	c.idstatus AS StatusId,
    c.minimumSalary AS MinimumSalary,
    c.address AS Address,
    c.phone1_verification_status AS Phone1VerificationStatus,
    c.car AS HasCar,
    c.drivelicence AS DriveLicenseId,
    c.residencechange AS CanChangeResidence,
    c.travel AS CanTravel,
    c.idemploymentstatus AS EmploymentStatusId, 
    c.idmaritalstatus AS MaritalStatusId,
    c.nationality AS NationalityId,
    c.postcode AS PostCode
FROM
  dtcandidate c
WHERE
  idcandidate= _id
  AND _idportal=_idPortal;

 END$$
DELIMITER ;

DROP procedure IF EXISTS `api_candidate_dtcandidate_select_all_by_userid_v5`;

DELIMITER $$
CREATE DEFINER=`root`@`localhost` PROCEDURE `api_candidate_dtcandidate_select_all_by_userid_v5`(
	IN `_userId` INT,
	IN `_portalId` TINYINT
)
BEGIN

  SET SESSION TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
  
  SELECT 
  	c.idcandidate AS Id,
  	c.iduser as UserId,
    c.name,
  	c.surname,
    c.idcargo AS DesiredPositionId,
    c.wishedjob AS DesiredPosition,
  	c.idgender,
  	c.birthdate,
  	c.idcountry AS CountryId,
  	c.idlocalization AS LocalizationId,
  	c.idcity AS CityId,
  	c.phone1,
  	c.phone2,
  	c.photo,
    c.ididentificationtype AS IdentificationTypeId,
    c.nit,
    c.idportal AS PortalId,
  	c.createdby,
  	c.createdon,
  	c.updatedon,
  	c.deletedon,
  	c.idstatus AS StatusId,
    c.experienceyears,
    c.minimumSalary AS MinSalary,
    c.idcity,
    c.address,
    c.idgender AS GenderId,
    c.phone1_verification_status AS Phone1VerificationStatus,
    c.idtestsettings_videointerview as IdTestSettingsVideoInterview,
    c.product_id AS ProductId,
    c.code_presentation_video AS CodePresentationVideo,
    c.premium_status,
    c.car AS HasCar,
    c.disability,
    c.drivelicence,
    c.residencechange AS CanChangeResidence,
    c.travel AS CanTravel,
    c.skype_name,
    c.idemploymentstatus AS EmploymentStatusId, 
    c.idmaritalstatus AS MaritalStatusId,
    c.nationality,
    c.idphonetype1,
    c.idphonetype2,
    c.postcode,
    IFNULL(t.status, 0) AS tcresult,
    IFNULL(t.updatedon_int,0) AS tcupdatedcon,
    IFNULL(tt.status, 0) AS TripleTestStatus,
    IFNULL(tt.updatedon_int,0) AS TripleTestUpdatedOn,
    deletedon,
    disability,
	drivelicence,
    skype_name AS Skype,
    idphonetype1,
    idphonetype2,
    c.employabilityIndex
  FROM 
  	dtcandidate c
  	LEFT JOIN dtcandidate_testcompetences t
  		ON t.idportal = c.idportal AND t.idcandidate = c.idcandidate
    LEFT JOIN dtcandidate_tripletest tt
  		ON tt.idportal = c.idportal AND tt.idcandidate = c.idcandidate
  WHERE
  	c.iduser = _userId
	AND c.idportal = _portalId;
  
  SET SESSION TRANSACTION ISOLATION LEVEL REPEATABLE READ;

END$$
DELIMITER ;

DROP procedure IF EXISTS `api_candidate_dtcandidate_updatevideopresentation`;

DELIMITER $$
CREATE DEFINER=`root`@`localhost` PROCEDURE `api_candidate_dtcandidate_updatevideopresentation`(IN _idcandidate INT, IN _hasvideopresentation BIT(1), IN _idportal TINYINT)
BEGIN
UPDATE 
  dtcandidate
SET 
  hasvideopresentation = _hasvideopresentation,
  updatedon = fn_now_by_portal(_idportal)
WHERE
	idcandidate=_idcandidate AND idportal = _idportal;
END$$
DELIMITER ;


DROP procedure IF EXISTS `api_candidate_dtcv_select_by_candidate_v3`;

DELIMITER $$
CREATE DEFINER=`root`@`localhost` PROCEDURE `api_candidate_dtcv_select_by_candidate_v3`(
	IN _idcandidate INT,
	IN _iduser INT
	)
BEGIN
  SELECT 
   cv.idcv AS Id,
   cv.idcandidate AS CandidateId,
   cv.tittleadd AS Title,
   cv.shortdescription AS ShortDescription,
   cv.longdescription AS LongDescription,
   cv.percentcvcompleted AS PercentCvCompleted,
   cv.updatedon AS UpdatedOn,
   cv.createdon AS CreatedOn
 FROM dtcv cv
	WHERE idcandidate=_idcandidate
	AND iduser = _iduser;

END$$
DELIMITER ;


DROP procedure IF EXISTS `api_candidate_dtcv_select_by_id_v2`;

DELIMITER $$
CREATE DEFINER=`root`@`localhost` PROCEDURE `api_candidate_dtcv_select_by_id_v2`(
IN _idcv INT
)
BEGIN
 	 SELECT 
   cv.idcv AS Id,
   cv.idcandidate AS CandidateId,
   cv.tittleadd AS TittleAdd,
   cv.shortdescription AS ShortDescription,
   cv.longdescription AS LongDescription,
   cv.idstatus AS StatusId,
   cv.updatedon AS UpdatedOn,
   cv.createdon AS CreatedOn   
 FROM dtcv cv
	WHERE idcv=_idcv;

 END$$
DELIMITER ;



DROP procedure IF EXISTS `api_dtuser_info_by_candidate`;

DELIMITER $$
CREATE DEFINER=`root`@`localhost` PROCEDURE `api_dtuser_info_by_candidate`(
  _idcandidate INT,
  _idportal   TINYINT(3)
  )
BEGIN
	
  SET SESSION TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
  
  SELECT 
  u.iduser,   
  u.verifiedmail, 
  u.current_cv_step as current_cv_step,
  c.idstatus as candidateStatus, 
  cv.idcv, 
  cv.principal,
  cv.idstatus as cvStatus 
  FROM dtcandidate c
  INNER JOIN dtuser u on c.iduser = u.iduser
  INNER JOIN dtcv cv on cv.idcandidate = c.idcandidate
  WHERE c.idcandidate =  _idcandidate
  AND u.idportal =  _idportal
  AND u.idstatus < 3;

  SET SESSION TRANSACTION ISOLATION LEVEL REPEATABLE READ;

END$$
DELIMITER ;


DROP procedure IF EXISTS `api_user_dtuser_count_users_by_idcompany_v1`;

DELIMITER $$
CREATE DEFINER=`root`@`localhost` PROCEDURE `api_user_dtuser_count_users_by_idcompany_v1`(IN _idcompany int)
BEGIN
	select count(1)
	from  dtuser 
	where idcompany = _idcompany And idstatus not in (4,88,99) ;	
END$$
DELIMITER ;


DROP procedure IF EXISTS `api_user_dtuser_delete_portal`;

DELIMITER $$
CREATE DEFINER=`root`@`localhost` PROCEDURE `api_user_dtuser_delete_portal`(
	_iduser int,
	_idportal tinyint,
	_idusertype tinyint
)
Begin
  DECLARE rowsAffected INT;

	UPDATE dtuser
	SET 
		deletedon = fn_now_by_portal(_idportal),
		idstatus = 4
	WHERE
		iduser = _iduser 
		AND idportal = _idportal 
		AND idusertype = _idusertype;

  SELECT ROW_COUNT() into rowsAffected;
  
  IF (rowsAffected = 0) THEN
    CALL ct_user.sys_trace('api_user_dtuser_delete_portal', 'OUT',0,concat('-_iduser:',_iduser , '-_idportal:', _idportal, '-_idusertype:', _idusertype));  
  END IF;
END$$
DELIMITER ;

DROP procedure IF EXISTS `api_user_dtuser_get_by_id_v2`;

DELIMITER $$
CREATE DEFINER=`root`@`localhost` PROCEDURE `api_user_dtuser_get_by_id_v2`(
  _userId INT
  )
Begin
	Select 
 		iduser,
    idportal,
    username,
    password,
    idcompany,
    idparentcompany,
    idorigin,
    idusertype,
    contactname,
    email,
    telephone,
    fax,
    createdon,
    createdby,
    updatedon,
    deletedon,
    lastlogin,
    principal,
    ndrstatus,
    idstatus,
    user_role as IdUserRole,
    current_cv_step as CurrentCvStep,
    idapp,
    idsource,
    idoriginform,
    verifiedmail as MailVerified,
	avatar_path as AvatarPath
  From dtuser
  where iduser = _userId;
 END$$
DELIMITER ;

DROP procedure IF EXISTS `api_user_dtuser_set_password_v2`;

DELIMITER $$
CREATE DEFINER=`root`@`localhost` PROCEDURE `api_user_dtuser_set_password_v2`(
	in _email varchar(100),
	in _newpassword varchar(100),
  in _idportal TINYINT
)
BEGIN	

	UPDATE 
     dtuser
	SET 
      password = _newpassword
	WHERE 	email = _email
    and idportal = _idportal;

END$$
DELIMITER ;


DROP procedure IF EXISTS `api_user_sp_dtuser_count_email_distinct_active_v1`;

DELIMITER $$
CREATE DEFINER=`root`@`localhost` PROCEDURE `api_user_sp_dtuser_count_email_distinct_active_v1`(
               _email varchar(250),
               _idportal TINYINT
)
BEGIN
     
SELECT EXISTS (
	(select iduser 
	  from  dtuser  
	  where username =  _email
	and idstatus not in (4,88,99)
	and idportal= _idportal
	limit 1)
UNION all 
	(select iduser 
	  from  dtuser  
	  where email =  _email
	and idstatus not in (4,88,99) 
	and idportal= _idportal
	limit 1)
);

END$$
DELIMITER ;


DROP procedure IF EXISTS `bc_companymaster_dtcompany_company_exists_by_nit`;

DELIMITER $$
CREATE DEFINER=`root`@`localhost` PROCEDURE `bc_companymaster_dtcompany_company_exists_by_nit`(IN _nit varchar(500),
  IN _idportal TINYINT)
BEGIN


SELECT 
	nit
FROM 
	dtcompany
WHERE
	nit = _nit AND
  idportal = _idportal
  ;
END$$
DELIMITER ;


DROP procedure IF EXISTS `bc_usermaster_dtuser_user_exists_by_email`;

DELIMITER $$
CREATE DEFINER=`root`@`localhost` PROCEDURE `bc_usermaster_dtuser_user_exists_by_email`(IN _email varchar(500),
  IN _idportal TINYINT)
BEGIN

SELECT 
	iduser
FROM 
	dtuser
WHERE
	email=_email
  and idportal=_idportal
  and (idstatus < 3 OR idstatus IN (5,6));
END$$
DELIMITER ;


DROP procedure IF EXISTS `candidate_skills_get_by_cv`;

DELIMITER $$
CREATE DEFINER=`root`@`localhost` PROCEDURE `candidate_skills_get_by_cv`(
			IN _idsCv MEDIUMTEXT)
Begin
 
 
SELECT concat('
  SELECT 
    skcv.idcommonskill AS idskill,
    skcv.idcv,
    sk.value,
    sk.idskilltype
  FROM 
    dtskillbycv skcv
  LEFT JOIN
    msskill sk
  ON
    sk.idskill = skcv.idskill
  WHERE
    skcv.idcv IN(', _idsCv,');') into @seleccion;
 
 -- select @seleccion   ;

PREPARE accion FROM @seleccion   ;
SET SESSION TRANSACTION ISOLATION LEVEL READ UNCOMMITTED ;
EXECUTE accion;
DEALLOCATE PREPARE accion;
SET SESSION TRANSACTION ISOLATION LEVEL REPEATABLE READ ;

END$$
DELIMITER ;


DROP procedure IF EXISTS `company_user_select_v6`;

DELIMITER $$
CREATE DEFINER=`root`@`localhost` PROCEDURE `company_user_select_v6`(
    IN _idcompany INT
    )
BEGIN
 	SELECT 
        comp.idcompany,
		comp.idportal,
		comp.idparent,
		comp.comercialname,
		comp.companyname,
		comp.nit,
		comp.address,
		comp.idlocalization,
		comp.idcountry,
		comp.idcity,
		comp.idotherlocalizations,
		comp.url,
		comp.urlrewrite,
		comp.idindustry,
		comp.idemploymentnumber,
		comp.idcompanytype,
		comp.description,
		comp.logopath,
		comp.createdon,
		comp.createdby,
		comp.idcompanystatus,
		comp.ispayment,
		comp.contactname,
		comp.contactsurname,
		comp.idcontactposition,
		comp.idtelephonetype1,
		comp.contacttelephone1,
		comp.idtelephonetype2,
		comp.contacttelephone2,
		comp.contactemail,
		comp.ndrstatusemail,
		comp.ndrstatusemailcontact,
		comp.idstatus,
		comp.ishighlighted,
		comp.hiddencompany,
		comp.condiciones,
		comp.activeoffers,
        comp.client_ip_add,
        comp.client_ip_mod,
        comp.numoffers,
		comp.postalcode,
        comp.updatedon,
        comp.show_prehome,
        comp.blocked,
        comp.blocked_type,
        comp.last_date_blocked_update,
        comp.date_last_login,
        comp.product_id,
        comp.numoffers_total,
        comp.id_company_rejection,
        comp.bannerpath,
        comp.ats_service_enabled,
        comp.config_promotions,
        comp.config_notifications,
        comp.reliability,
        comp.idmaster,    
        comp.companyMision,
        comp.companyValues,
        user.iduser,
        user.idportal,
        user.username,
        user.password,
        user.idcompany,
        user.idparentcompany,
        user.idorigin,
        user.idusertype,
        user.contactname,
        user.email,
        user.telephone,
        user.fax,
        user.createdon,
        user.createdby,
        user.updatedon,
        user.deletedon,
        user.lastlogin,
        user.principal,
        user.ndrstatus,
        user.idstatus AS useridstatus,
        user.user_role,
        user.current_cv_step,
        user.idapp,
        user.idsource,
        comp.has_ats,
        comp.has_ats > 1 as has_pandape_ats,
        comp.cvVisualizationPrivacy,
        user.verifiedmail,
        comp.RegisterAction,
        comp.StatusImportOffer
    FROM dtcompany comp 
    INNER JOIN dtuser user ON comp.idcompany = user.idcompany
 	WHERE comp.idcompany = _idcompany  and user.user_role = 1;
 END$$
DELIMITER ;

DROP procedure IF EXISTS `get_adecuation_dtskillcommon_bycv_data`;

DELIMITER $$
CREATE DEFINER=`root`@`localhost` PROCEDURE `get_adecuation_dtskillcommon_bycv_data`(_idcandidates varchar(5000))
BEGIN
DECLARE SQL_VAR text;   
  SET SQL_VAR = '
      select 
            c.idcandidate as idcandidate,
            s.idcommonskill as idskill
      from dtcv c 
      inner join dtskillbycv s on s.idcv = c.idcv
      where c.idcandidate in (';
  SET  SQL_VAR = CONCAT(SQL_VAR, _idcandidates ,') order by idcandidate;'); 
 
set @todaLaQuery = SQL_VAR; 

PREPARE smpt FROM @todaLaQuery; 
EXECUTE smpt; 
DEALLOCATE PREPARE smpt;
END$$
DELIMITER ;

DROP procedure IF EXISTS `gl_candidate_selectbypk_v13`;

DELIMITER $$
CREATE DEFINER=`root`@`localhost` PROCEDURE `gl_candidate_selectbypk_v13`(_idcandidate int)
BEGIN

SET SESSION TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
SELECT 
	c.idcandidate,
	c.iduser,
	c.name,
	c.surname,
	c.birthdate,
	c.age,
	c.idgender,
	c.idcity,
	c.idlocalization,
	c.idcountry,
	c.idotherlocalizations,
	c.address,
	c.postcode,
	c.countrypermission,
	c.phone1,
	c.idphonetype1,
	c.phone2,
	c.idphonetype2,
	c.idmaritalstatus,
	c.nationality,
	c.idlegalsituation,
	c.drivelicence,
	c.nit,
	c.ididentificationtype,
	c.disability,
	c.experienceyears,
	c.photo,
	c.car,
	c.idprivacylevel,
	c.idemploymentstatus,
	c.minimumsalary,
	c.wishedsalary,
	c.salarydefault,
	c.residencechange,
	c.travel,
	c.wishedjob,
	c.createdon,
	c.createdby,
	c.updatedon,
	c.datelastup,
	c.deletedon,
	c.idstatus,
  c.client_ip_add,
  c.client_ip_mod,
  c.idportal,
  c.idmilitaryservice,
  c.idcargo,
  c.idrace,
  c.phone1_verification_status,
  c.phone2_verification_status,
  c.skype_name,
  c.idtestsettings_videointerview,
  c.code_presentation_video,
  c.product_id, 
  dtuser.idstatus as userstatus,
  cp.idpostalcode,
  t.status as competence_test,
  tt.status as talent_view,
  tt.visible as test_competences_talent_view_visible,
  c.idtestsettings_videointerview

FROM 
	dtcandidate c
  INNER JOIN dtuser ON c.iduser = dtuser.iduser
  LEFT JOIN dtcandidatebypostalcode cp ON cp.idcandidate = c.idcandidate
  LEFT JOIN dtcandidate_testcompetences t
  ON t.idportal = c.idportal AND t.idcandidate = c.idcandidate
  LEFT JOIN dtcandidate_tripletest tt
  ON tt.idportal = c.idportal AND tt.idcandidate = c.idcandidate

WHERE
	c.idcandidate=_idcandidate;

SET SESSION TRANSACTION ISOLATION LEVEL REPEATABLE READ ;

END$$
DELIMITER ;


DROP procedure IF EXISTS `gl_candidate_selectbypk_v14`;

DELIMITER $$
CREATE DEFINER=`root`@`localhost` PROCEDURE `gl_candidate_selectbypk_v14`(_idcandidate int)
BEGIN

SET SESSION TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

SELECT 
	dtcandidate.idcandidate,
	dtcandidate.iduser,
	name,
	surname,
	birthdate,
	age,
	idgender,
	idcity,
	idlocalization,
	idcountry,
	idotherlocalizations,
	address,
	postcode,
	countrypermission,
	phone1,
	idphonetype1,
	phone2,
	idphonetype2,
	idmaritalstatus,
	nationality,
	idlegalsituation,
	drivelicence,
	nit,
	ididentificationtype,
	disability,
	experienceyears,
	photo,
	car,
	idprivacylevel,
	idemploymentstatus,
	minimumsalary,
	wishedsalary,
	salarydefault,
	residencechange,
	travel,
	wishedjob,
	dtcandidate.createdon,
	dtcandidate.createdby,
	dtcandidate.updatedon,
	datelastup,
	dtcandidate.deletedon,
	dtcandidate.idstatus,
  client_ip_add,
  client_ip_mod,
  dtcandidate.idportal,
  idmilitaryservice,
  idcargo,
  idrace,
  phone1_verification_status,
  phone2_verification_status,
  skype_name,
  idtestsettings_videointerview,
  code_presentation_video,
  product_id, 
  test_competences_visible,
  dtuser.idstatus as userstatus,
  cp.idpostalcode,
  has_video_presentation
FROM 
	dtcandidate
  INNER JOIN dtuser ON dtcandidate.iduser = dtuser.iduser
  LEFT JOIN dtcandidatebypostalcode cp ON cp.idcandidate = dtcandidate.idcandidate
WHERE
	dtcandidate.idcandidate=_idcandidate;

SET SESSION TRANSACTION ISOLATION LEVEL REPEATABLE READ ;

END$$
DELIMITER ;

DROP procedure IF EXISTS `info_apply_time_insert_v2`;

DELIMITER $$
CREATE DEFINER=`root`@`localhost` PROCEDURE `info_apply_time_insert_v2`(
  _idportal tinyint(4),
  _iduser int,
  _idoffer bigint,
  _applyTotalTime int,
  _fetchApplyResponseTime int,
  _fetchApplyInfoResponseTime int,
  _clickToApplyFetchTime int,
  _totalFetchTime int,
  _connectionQuality varchar(50),
  _idversion int,
  _isBeta tinyint
)
BEGIN

	INSERT INTO info_apply_time (idportal, iduser, idoffer, applyTotalTime, fetchApplyResponseTime, fetchApplyInfoResponseTime, 
    clickToApplyFetchTime, totalFetchTime, connectionQuality, idversion, createdAt, isBeta)
	VALUES (_idportal, _iduser, _idoffer, _applyTotalTime, _fetchApplyResponseTime, _fetchApplyInfoResponseTime, 
    _clickToApplyFetchTime, _totalFetchTime, _connectionQuality, _idversion, NOW(), _isBeta)
  ON DUPLICATE KEY UPDATE applyTotalTime = _applyTotalTime, fetchApplyResponseTime = _fetchApplyResponseTime, fetchApplyInfoResponseTime = _fetchApplyInfoResponseTime, 
    clickToApplyFetchTime = _clickToApplyFetchTime, totalFetchTime = _totalFetchTime, connectionQuality = _connectionQuality, idversion = _idversion,
    createdAt = NOW(), isBeta = _isBeta;
  
END$$
DELIMITER ;


DROP procedure IF EXISTS `sp_dtcompany_getAts`;

DELIMITER $$
CREATE DEFINER=`root`@`localhost` PROCEDURE `sp_dtcompany_getAts`(
  _idportal tinyint(4),
  _iduser int,
  _idoffer bigint,
  _applyTotalTime int,
  _fetchApplyResponseTime int,
  _fetchApplyInfoResponseTime int,
  _clickToApplyFetchTime int,
  _totalFetchTime int,
  _connectionQuality varchar(50),
  _idversion int,
  _isBeta tinyint
)
BEGIN

	INSERT INTO info_apply_time (idportal, iduser, idoffer, applyTotalTime, fetchApplyResponseTime, fetchApplyInfoResponseTime, 
    clickToApplyFetchTime, totalFetchTime, connectionQuality, idversion, createdAt, isBeta)
	VALUES (_idportal, _iduser, _idoffer, _applyTotalTime, _fetchApplyResponseTime, _fetchApplyInfoResponseTime, 
    _clickToApplyFetchTime, _totalFetchTime, _connectionQuality, _idversion, NOW(), _isBeta)
  ON DUPLICATE KEY UPDATE applyTotalTime = _applyTotalTime, fetchApplyResponseTime = _fetchApplyResponseTime, fetchApplyInfoResponseTime = _fetchApplyInfoResponseTime, 
    clickToApplyFetchTime = _clickToApplyFetchTime, totalFetchTime = _totalFetchTime, connectionQuality = _connectionQuality, idversion = _idversion,
    createdAt = NOW(), isBeta = _isBeta;
  
END$$
DELIMITER ;


DROP procedure IF EXISTS `sp_dtskillsbycv_GetByCvs`;

DELIMITER $$
CREATE DEFINER=`root`@`localhost` PROCEDURE `sp_dtskillsbycv_GetByCvs`(_idcvs TEXT)
BEGIN
    SELECT 
        skcv.idcommonskill AS idskill,
        skcv.idcv,
        sk.value,
        sk.idskilltype
    FROM 
        dtskillbycv skcv
    LEFT JOIN
        msskill sk
    ON
        sk.idskill = skcv.idskill
    WHERE
        FIND_IN_SET(skcv.idcv, _idcvs) > 0;
END$$
DELIMITER ;