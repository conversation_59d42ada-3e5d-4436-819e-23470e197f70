using System.Collections.Generic;

namespace Redarbor.AE.Offer.API.Consumer.Models.Request
{
    public class OfferKillerQuestionRequestModel
    {
        public int Id { get; set; }

        public short IdType { get; set; }

        public string Title { get; set; } = string.Empty;

        public short Order { get; set; }

        public short Score { get; set; }
        public bool AutoExclude { get; set; }

        public IEnumerable<OfferKillerQuestionDataRequestModel> KillerQuestionData { get; set; } = new List<OfferKillerQuestionDataRequestModel>();
    }
}
