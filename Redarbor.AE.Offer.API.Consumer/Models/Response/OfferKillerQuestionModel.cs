using System;
using System.Collections.Generic;
using System.Text;

namespace Redarbor.AE.Offer.API.Consumer.Models.Response
{
    public class OfferKillerQuestionModel
    {
        public int Id { get; set; }
        public int? CTId { get; set; } = null;
        public short PortalId { get; set; }
        public short StatusId { get; set; }
        public short Type { get; set; }
        public string Title { get; set; } = string.Empty;
        public DateTime CreatedOn { get; set; }
        public short Score { get; set; }
        public short AutoExclude { get; set; } = 0;
        public List<OfferKillerQuestionDataModel> ClosedQuestionOption { get; set; }
    }
}
