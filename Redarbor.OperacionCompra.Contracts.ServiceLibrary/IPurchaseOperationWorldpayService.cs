using Redarbor.Common.Entities.Configuration;
using Redarbor.Master.Entities.Payment;
using Redarbor.Master.Entities.Product;
using Redarbor.Master.Entities.PurchaseOperation;

namespace Redarbor.PurchaseOperation.Contracts.ServiceLibrary
{
    public interface IPurchaseOperationWorldpayService
    {
         void ProcessTransaction(PortalConfig portalConfig, PurchaseOperationWorldPayEntity purchaseOperationWorldPay, bool isCRM = false);
        PurchaseOperationWorldPayEntity LoadWorldPayEntity(PaymentEntity paymentEntity, ProProductEntity product, PurchaseOperationEntity purchaseOperation);

        PurchaseOperationWorldPayEntity LoadWorldPayEntity(MultiPaymentEntity paymentEntity, MultiPurchaseOperationEntity purchaseOperation);
    }
}
