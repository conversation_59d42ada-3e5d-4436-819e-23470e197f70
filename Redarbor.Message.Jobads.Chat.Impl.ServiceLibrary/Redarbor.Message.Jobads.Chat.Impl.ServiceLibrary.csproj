<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{3FBDFA22-95E4-4D99-9321-29AFF141A719}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Redarbor.Message.Jobads.Chat.Impl.ServiceLibrary</RootNamespace>
    <AssemblyName>Redarbor.Message.Jobads.Chat.Impl.ServiceLibrary</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Numerics" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="BuilderExtensions.cs" />
    <Compile Include="Exceptions\MessageJobadsChatException.cs" />
    <Compile Include="IMessageJobadsChatService.cs" />
    <Compile Include="Mappings\JobadsChatMapping.cs" />
    <Compile Include="MessageJobadsChatService.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Autofac">
      <Version>3.5.0</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.Bcl.AsyncInterfaces">
      <Version>7.0.0</Version>
    </PackageReference>
    <PackageReference Include="Newtonsoft.Json">
      <Version>13.0.3</Version>
    </PackageReference>
    <PackageReference Include="Redarbor.Jobads.Chat.Consumer">
      <Version>1.0.16</Version>
    </PackageReference>
    <PackageReference Include="Redarbor.Jobads.Chat.Consumer.Abstractions">
      <Version>1.0.13</Version>
    </PackageReference>
    <PackageReference Include="Redarbor.Jobads.Chat.Consumer.Extensions.Autofac">
      <Version>1.0.10</Version>
    </PackageReference>
    <PackageReference Include="Redarbor.Jobads.Chat.Listener.Abstractions">
      <Version>1.0.4</Version>
    </PackageReference>
    <PackageReference Include="Refit">
      <Version>7.0.0</Version>
    </PackageReference>
    <PackageReference Include="System.Buffers">
      <Version>4.5.1</Version>
    </PackageReference>
    <PackageReference Include="System.IO">
      <Version>4.3.0</Version>
    </PackageReference>
    <PackageReference Include="System.Memory">
      <Version>4.5.5</Version>
    </PackageReference>
    <PackageReference Include="System.Net.Http">
      <Version>4.3.4</Version>
    </PackageReference>
    <PackageReference Include="System.Net.Http.Json">
      <Version>7.0.1</Version>
    </PackageReference>
    <PackageReference Include="System.Numerics.Vectors">
      <Version>4.5.0</Version>
    </PackageReference>
    <PackageReference Include="System.Runtime">
      <Version>4.3.0</Version>
    </PackageReference>
    <PackageReference Include="System.Runtime.CompilerServices.Unsafe">
      <Version>6.0.0</Version>
    </PackageReference>
    <PackageReference Include="System.Security.Cryptography.Algorithms">
      <Version>4.3.0</Version>
    </PackageReference>
    <PackageReference Include="System.Security.Cryptography.Encoding">
      <Version>4.3.0</Version>
    </PackageReference>
    <PackageReference Include="System.Security.Cryptography.Primitives">
      <Version>4.3.0</Version>
    </PackageReference>
    <PackageReference Include="System.Security.Cryptography.X509Certificates">
      <Version>4.3.0</Version>
    </PackageReference>
    <PackageReference Include="System.Text.Encodings.Web">
      <Version>7.0.0</Version>
    </PackageReference>
    <PackageReference Include="System.Text.Json">
      <Version>7.0.3</Version>
    </PackageReference>
    <PackageReference Include="System.Threading.Tasks.Extensions">
      <Version>4.5.4</Version>
    </PackageReference>
    <PackageReference Include="System.ValueTuple">
      <Version>4.5.0</Version>
    </PackageReference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Readarbor.Master.Entities\Redarbor.Master.Entities.csproj">
      <Project>{72B93BA2-C177-4DDF-9F27-E08151323719}</Project>
      <Name>Redarbor.Master.Entities</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Cache.Contracts.ServiceLibrary\Redarbor.Core.Cache.Contracts.ServiceLibrary.csproj">
      <Project>{E683967C-B674-4450-B9A6-6030FD65E0F4}</Project>
      <Name>Redarbor.Core.Cache.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Computrabajo.Contracts.ServiceLibrary\Redarbor.Core.Computrabajo.Contracts.ServiceLibrary.csproj">
      <Project>{9BE71737-B615-4227-BB44-C5ECEC3DCED3}</Project>
      <Name>Redarbor.Core.Computrabajo.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Core.Shared.Contracts.ServiceLibrary\Redarbor.Core.Shared.Contracts.ServiceLibrary.csproj">
      <Project>{21E3ABEA-6637-49C2-8684-E0F842DE8506}</Project>
      <Name>Redarbor.Core.Shared.Contracts.ServiceLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\Redarbor.Extensions.Library\Redarbor.Extensions.Library.csproj">
      <Project>{A30C078B-2F28-42B0-84E7-E02F9C30E27E}</Project>
      <Name>Redarbor.Extensions.Library</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>