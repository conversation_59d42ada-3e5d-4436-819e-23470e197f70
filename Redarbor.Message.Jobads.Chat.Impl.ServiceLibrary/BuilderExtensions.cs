using Autofac;
using Redarbor.Jobads.Chat.Consumer.Services;
using Redarbor.Jobads.Consumer.Extensions.Autofac;
using System;
using System.Configuration;

namespace Redarbor.Message.Jobads.Chat.Impl.ServiceLibrary
{
    public static class BuilderExtensions
    {
        public static ContainerBuilder AddRedarborJobadsChatConsumerService(this ContainerBuilder containerbuilder)
        {
            string apiJobads = ConfigurationManager.AppSettings["JOBADS_API_ENDPOINT"] ?? string.Empty;
            if (string.IsNullOrEmpty(apiJobads))
                return containerbuilder;

            return containerbuilder.AddRedarborJobadsChatConsumerServices(apiJobads);
        }

        public static Type GetTypeJobadsChatService()
        {
            return typeof(IJobadsChatService);
        }
    }
}
