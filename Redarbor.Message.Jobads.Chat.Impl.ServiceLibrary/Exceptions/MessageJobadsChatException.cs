using System;

namespace Redarbor.Message.Jobads.Chat.Impl.ServiceLibrary.Exceptions
{

    public class MessageJobadsChatException : Exception
    {
        private const string ERROR_MESSAGE = "Message Jobads Chat Exception";

        public MessageJobadsChatException() : base(ERROR_MESSAGE)
        {
        }

        public MessageJobadsChatException(string message) : base(message)
        {
        }

        public MessageJobadsChatException(string message, Exception innerException) : base(message, innerException)
        {
        }
    }
}
