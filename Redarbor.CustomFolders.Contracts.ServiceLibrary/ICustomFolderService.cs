using Redarbor.Master.Entities.CustomFolder;
using System.Collections.Generic;

namespace Redarbor.CustomFolders.Contracts.ServiceLibrary
{
    public interface ICustomFolderService
    {
        int GetMaxLenghtNameFolder();
        List<CustomFolderEntity> GetCustomFolders(CustomFoldersFilterEntity customFoldersFilter);
        List<int> GetCvsByFolderCustom(CustomFoldersFilterEntity customFolder);
        List<int> GetCvsViewedsByIdcompany(int companyId, string cvs);

        CustomFolderEntity GetCustomFolderByIdFolder(CustomFoldersFilterEntity customFoldersFilter);

        int GetFolderCustomByIdCv(CustomFoldersFilterEntity customFoldersFilter);

        bool AddCustomFolder(CustomFoldersFilterEntity customFoldersFilter);
        bool DeleteCvsByFolder(CustomFoldersFilterEntity customFoldersFilter);        
        bool UpdateCustomFolder(CustomFoldersFilterEntity customFoldersFilter);
        bool DeleteCustomFolder(CustomFoldersFilterEntity customFoldersFilter);

        void AddCvToFolder(CustomFoldersFilterEntity customFoldersFilter);      
        void DeleteCustomFolderCVsInSearch(CustomFoldersFilterEntity customFoldersFilter);
        short GetResponseAddCvsFolderAndExecute(string idCvsEncrypted, string idFolderPreviewEncrypted, string idFolderNewEncrypted, int idCompany, int userId, short portalId, short maxCvsByFolders, short environment, bool withTotal);
        bool CheckAndAddCustomFolder(CustomFoldersFilterEntity customFoldersFilterEntity);
        bool DeleteCustomCvsByFolderEncrypted(string idFolderEncrypted, string cvsEncrypted, int idUser, short portalId, bool WithTotal);
        bool CheckAndUpdateCustomFolder(CustomFoldersFilterEntity customFoldersFilterEntity, string idfd);
        bool CheckAndDeleteCustomFolder(CustomFoldersFilterEntity customFoldersFilterEntity, string idFolderEncrypted);
    }
}
