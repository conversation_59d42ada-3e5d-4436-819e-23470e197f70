namespace Redarbor.Company.Library.DomainServiceContracts
{
    public interface ICompanyCheckIdentifierRecoverAndPersist
    {
        bool ExistsIdentifier(string nit, short portalId);
        bool IsValidNit(string nit, short portalId, bool withStatus = false);
        bool IsOtherCountryNit(string nit, short portalId, int countryId);
        short GetCompanyRegisterAction(string nit, short portalId, int countryId);
	}
}
