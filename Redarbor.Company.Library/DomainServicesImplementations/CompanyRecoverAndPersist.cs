using MySql.Data.MySqlClient;
using Redarbor.Company.Library.DomainServiceContracts;
using Redarbor.Company.Library.DTO;
using Redarbor.Company.Library.Entities;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Core.Resolver.Contracts.ServiceLibrary;
using Redarbor.Core.Resolver.Contracts.ServiceLibrary.Enums;
using Redarbor.Extensions.Library.Extensions;
using Redarbor.Master.Entities.Company;
using Redarbor.Master.Entities.Enums;
using Redarbor.Master.Entities.Product;
using Redarbor.Master.Entities.Searchs;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Text;
using System.Threading.Tasks;

namespace Redarbor.Company.Library.DomainServicesImplementations
{
    public class CompanyRecoverAndPersist : ICompanyRecoverAndPersist
    {
        private readonly IConnectionStringResolverService _connectionStringResolverService;
        private readonly IExceptionPublisherService _exceptionPublisherService;

        public CompanyRecoverAndPersist(IConnectionStringResolverService connectionStringResolverService,
            IExceptionPublisherService exceptionPublisherService)
        {
            _connectionStringResolverService = connectionStringResolverService;
            _exceptionPublisherService = exceptionPublisherService;
        }

        public Tuple<int, int> Insert(CompanyEntity company)
        {
            var tupleCompanyUser = new Tuple<int, int>(0, 0);
            var companyId = 0;
            var userId = 0;

            if (company == null)
                return tupleCompanyUser;

            BuildCompany(company);

            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
            {
                using (MySqlCommand command = new MySqlCommand("companyuserinsert_v3", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_idportal", company.PortalId));
                    command.Parameters.Add(new MySqlParameter("_statusId", company.StatusId));
                    command.Parameters.Add(new MySqlParameter("_companyStatusId", company.CompanyStatusId));
                    command.Parameters.Add(new MySqlParameter("_idparent", company.ParentId));
                    command.Parameters.Add(new MySqlParameter("_urlrewrite", company.Url));
                    command.Parameters.Add(new MySqlParameter("_comercialname", company.ComercialName));
                    command.Parameters.Add(new MySqlParameter("_companyname", company.CompanyName));
                    command.Parameters.Add(new MySqlParameter("_nit", company.Nit));
                    command.Parameters.Add(new MySqlParameter("_address", company.Address.StreetAddress));
                    command.Parameters.Add(new MySqlParameter("_postalcode", company.Address.ZipCode));
                    command.Parameters.Add(new MySqlParameter("_idcity", company.Address.CityId));
                    command.Parameters.Add(new MySqlParameter("_idlocalization", company.Address.LocationId));
                    command.Parameters.Add(new MySqlParameter("_idcountry", company.Address.CountryId));
                    command.Parameters.Add(new MySqlParameter("_url", company.Url));
                    command.Parameters.Add(new MySqlParameter("_idindustry", company.IndustryId));
                    command.Parameters.Add(new MySqlParameter("_idemploymentnumber", company.EmploymentNumber));
                    command.Parameters.Add(new MySqlParameter("_idcompanytype", company.TypeId));
                    command.Parameters.Add(new MySqlParameter("_description", company.Description));
                    command.Parameters.Add(new MySqlParameter("_logopath", company.LogoPath));
                    command.Parameters.Add(new MySqlParameter("_createdby", company.CreatedBy));
                    command.Parameters.Add(new MySqlParameter("_contactname", company.ContactInformation.Name));
                    command.Parameters.Add(new MySqlParameter("_contactsurname", company.ContactInformation.Surname));
                    command.Parameters.Add(new MySqlParameter("_idcontactposition", company.ContactInformation.PositionId));
                    command.Parameters.Add(new MySqlParameter("_idtelephonetype1", company.ContactInformation.PhoneNumbers[0].TypeId));
                    command.Parameters.Add(new MySqlParameter("_contacttelephone1", company.ContactInformation.PhoneNumbers[0].Number));
                    command.Parameters.Add(new MySqlParameter("_idtelephonetype2", company.ContactInformation.PhoneNumbers[1].TypeId));
                    command.Parameters.Add(new MySqlParameter("_contacttelephone2", company.ContactInformation.PhoneNumbers[1].Number));
                    command.Parameters.Add(new MySqlParameter("_contactemail", company.ContactInformation.Email));
                    command.Parameters.Add(new MySqlParameter("_condiciones", company.ConditionsId));
                    command.Parameters.Add(new MySqlParameter("_idotherlocalizations", company.OtherLocationsId));
                    command.Parameters.Add(new MySqlParameter("_idorigin", company.User.OriginId));
                    command.Parameters.Add(new MySqlParameter("_idusertype", company.User.TypeId));
                    command.Parameters.Add(new MySqlParameter("_username", company.User.Username));
                    command.Parameters.Add(new MySqlParameter("_password", company.User.Password));
                    command.Parameters.Add(new MySqlParameter("_idcompany", company.User.CompanyId));
                    command.Parameters.Add(new MySqlParameter("_idparentcompany", company.User.ParentCompanyId));
                    command.Parameters.Add(new MySqlParameter("_telephone", company.User.PhoneNumbers));
                    command.Parameters.Add(new MySqlParameter("_email", company.User.Email));
                    command.Parameters.Add(new MySqlParameter("_client_ip_add", company.ClientIpAdd));
                    command.Parameters.Add(new MySqlParameter("_client_ip_mod", company.ClientIpMod));
                    command.Parameters.Add(new MySqlParameter("_user_role", company.User.RoleId));
                    command.Parameters.Add(new MySqlParameter("_product_id", company.ProductId));
                    command.Parameters.Add(new MySqlParameter("_has_ats", company.HasATS));
                    command.Parameters.Add(new MySqlParameter("_vacancies_id", company.VacanciesPerYearId));


                    try
                    {
                        connection.Open();

                        using (var reader = command.ExecuteReader())
                        {
                            if (reader != null)
                            {
                                while (reader.Read())
                                {
                                    try
                                    {
                                        companyId = reader.GetAsInt("lastCompanyId");
                                        userId = reader.GetAsInt("lastUserId");
                                        tupleCompanyUser = new Tuple<int, int>(companyId, userId);
                                    }
                                    catch (Exception ex)
                                    {
                                        _exceptionPublisherService.Publish(ex, "CompanyRecoverAndPersist", "Insert");
                                    }
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Trace.TraceError($"CompanyRecoveryAndPersist - Insert, ex: {ex.ToString()}");
                        _exceptionPublisherService.Publish(ex, "CompanyRecoveryAndPersist", "Insert");
                    }
                }
            }
            return tupleCompanyUser;
        }

        public int GetIdCandidateByCompanyNit(int companyId, int portalId, string nit)
        {
            int id = 0;
            using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.ReadMaster)))
            {
                using (var command = new MySqlCommand("getIdCandidateByNitPortalCompany", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    try
                    {
                        command.Parameters.Add(new MySqlParameter("_company_id", companyId));
                        command.Parameters.Add(new MySqlParameter("_portal_id", portalId));
                        command.Parameters.Add(new MySqlParameter("_nit", nit));


                        connection.Open();
                        id = command.ExecuteScalar().ToInt();
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyRecoverAndPersist", "GetIdCandidateByCompanyNit");
                    }
                }
            }
            return id;
        }

        public void Update(CompanyEntity company)
        {
            if (company == null)
                return;

            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
            {
                using (MySqlCommand command = new MySqlCommand("companies_dtcompany_update_v3", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_idcompany", company.Id));
                    command.Parameters.Add(new MySqlParameter("_idportal", company.PortalId));
                    command.Parameters.Add(new MySqlParameter("_idparent", company.ParentId));
                    command.Parameters.Add(new MySqlParameter("_statusId", company.StatusId));
                    command.Parameters.Add(new MySqlParameter("_companyStatusId", company.CompanyStatusId));
                    command.Parameters.Add(new MySqlParameter("_urlrewrite", company.UrlRewrite));
                    command.Parameters.Add(new MySqlParameter("_comercialname", company.ComercialName));
                    command.Parameters.Add(new MySqlParameter("_companyname", company.CompanyName));
                    command.Parameters.Add(new MySqlParameter("_nit", company.Nit));
                    command.Parameters.Add(new MySqlParameter("_address", company.Address.StreetAddress));
                    command.Parameters.Add(new MySqlParameter("_postalcode", company.Address.ZipCode));
                    command.Parameters.Add(new MySqlParameter("_idcity", company.Address.CityId));
                    command.Parameters.Add(new MySqlParameter("_idlocalization", company.Address.LocationId));
                    command.Parameters.Add(new MySqlParameter("_idcountry", company.Address.CountryId));
                    command.Parameters.Add(new MySqlParameter("_url", company.Url));
                    command.Parameters.Add(new MySqlParameter("_idindustry", company.IndustryId));
                    command.Parameters.Add(new MySqlParameter("_idemploymentnumber", company.EmploymentNumber));
                    command.Parameters.Add(new MySqlParameter("_idcompanytype", company.TypeId));
                    command.Parameters.Add(new MySqlParameter("_description", company.Description));
                    command.Parameters.Add(new MySqlParameter("_logopath", company.LogoPath));
                    command.Parameters.Add(new MySqlParameter("_createdby", company.CreatedBy));
                    command.Parameters.Add(new MySqlParameter("_username", company.ContactInformation.Username));
                    command.Parameters.Add(new MySqlParameter("_contactname", company.ContactInformation.Name));
                    command.Parameters.Add(new MySqlParameter("_contactsurname", company.ContactInformation.Surname));
                    command.Parameters.Add(new MySqlParameter("_idcontactposition", company.ContactInformation.PositionId));
                    command.Parameters.Add(new MySqlParameter("_idtelephonetype1", company.ContactInformation.PhoneNumbers[0].TypeId));
                    command.Parameters.Add(new MySqlParameter("_contacttelephone1", company.ContactInformation.PhoneNumbers[0].Number));
                    command.Parameters.Add(new MySqlParameter("_idtelephonetype2", company.ContactInformation.PhoneNumbers[1].TypeId));
                    command.Parameters.Add(new MySqlParameter("_contacttelephone2", company.ContactInformation.PhoneNumbers[1].Number));
                    command.Parameters.Add(new MySqlParameter("_contactemail", company.ContactInformation.Email));
                    command.Parameters.Add(new MySqlParameter("_condiciones", company.ConditionsId));
                    command.Parameters.Add(new MySqlParameter("_client_ip_add", company.ClientIpAdd));
                    command.Parameters.Add(new MySqlParameter("_client_ip_mod", company.ClientIpMod));

                    try
                    {
                        connection.Open();

                        command.ExecuteNonQuery();
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyRecoverAndPersist", "Update");
                    }
                }
            }
        }

        public RatedPriceEntity GetVat(RatedPriceEntity ratedPrice)
        {
            if (ratedPrice.CountryId == 0)
            {
                ratedPrice.VATNumber = 0;
                return ratedPrice;
            }

            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
            {
                using (MySqlCommand command = new MySqlCommand("companies_get_iva_by_idcountry", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    command.Parameters.AddWithValue("_idcountry", ratedPrice.CountryId);
                    try
                    {
                        connection.Open();
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader != null && reader.Read())
                            {
                                ratedPrice.VATNumber = reader.GetAsShort("standard_rate");
                                ratedPrice.VATLiteral = reader.GetAsString("literal_rate");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyRepository", "TryGetIva");
                    }
                }
            }

            return ratedPrice;
        }

        public CompanyEntity GetByPK(CompanySearchSpecifications specifications)
        {
            if (specifications == null || specifications.Id == 0)
                return new CompanyEntity();

            CompanyEntity company = new CompanyEntity();
            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
            {
                using (MySqlCommand command = new MySqlCommand("company_user_select_v6", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    command.Parameters.Add(new MySqlParameter("_idcompany", specifications.Id));

                    connection.Open();

                    using (var reader = command.ExecuteReader())
                    {
                        if (reader != null)
                        {
                            while (reader.Read())
                            {
                                try
                                {
                                    company.Id = reader.GetAsInt("idcompany");
                                    company.MasterId = reader.GetAsInt("idmaster");
                                    company.User = new Master.Entities.Users.UserEntity()
                                    {
                                        Id = reader.GetAsInt("iduser"),
                                        AppId = reader.GetAsInt("idapp"),
                                        CompanyId = reader.GetAsInt("idcompany"),
                                        ContactName = reader.GetAsString("contactname"),
                                        CreatedBy = reader.GetAsInt("createdby"),
                                        CreatedOn = reader.GetAsDateTime("createdon"),
                                        CurrentCvStep = reader.GetAsShort("current_cv_step"),
                                        DeletedOn = reader.GetAsDateTime("deletedon"),
                                        Email = reader.GetAsString("email"),
                                        Fax = reader.GetAsString("fax"),
                                        LastLoginOn = reader.GetAsDateTime("lastlogin"),
                                        NdrStatusId = reader.GetAsInt("ndrstatus"),
                                        OriginId = reader.GetAsInt("idorigin"),
                                        ParentCompanyId = reader.GetAsInt("idparentcompany"),
                                        Password = reader.GetAsString("password"),
                                        PhoneNumbers = reader.GetAsString("telephone"),
                                        PortalId = reader.GetAsShort("idportal"),
                                        Principal = reader.GetAsShort("principal"),
                                        RoleId = reader.GetAsShort("user_role"),
                                        StatusId = reader.GetAsInt("useridstatus"),
                                        TypeId = reader.GetAsInt("idusertype"),
                                        UpdatedOn = reader.GetAsDateTime("updatedon"),
                                        Username = reader.GetAsString("username"),
                                        VerifiedMail = reader.GetAsShort("verifiedmail")
                                    };
                                    company.ParentId = reader.GetAsInt("idparent");
                                    company.ComercialName = reader.GetAsString("comercialname");
                                    company.CompanyName = reader.GetAsString("companyname");
                                    company.Nit = reader.GetAsString("nit");
                                    company.Address = new AddressEntity()
                                    {
                                        StreetAddress = reader.GetAsString("address"),
                                        CityId = reader.GetAsInt("idcity"),
                                        CountryId = reader.GetAsInt("idcountry"),
                                        LocationId = reader.GetAsInt("idlocalization"),
                                        ZipCode = reader.GetAsString("postalcode"),
                                    };
                                    company.ContactInformation = new ContactInformationEntity
                                    {
                                        Name = reader.GetAsString("contactname"),
                                        Surname = reader.GetAsString("contactsurname"),
                                        PositionId = reader.GetAsInt("idcontactposition"),
                                        Email = reader.GetAsString("contactemail"),
                                        Username = reader.GetAsString("username"),
                                        PhoneNumbers = new List<PhoneNumberEntity>()
                                            {
                                                new PhoneNumberEntity()
                                                {
                                                    TypeId = reader.GetAsShort("idtelephonetype1"),
                                                    Number = reader.GetAsString("contacttelephone1")
                                                },
                                                new PhoneNumberEntity()
                                                {
                                                    TypeId = reader.GetAsShort("idtelephonetype2"),
                                                    Number = reader.GetAsString("contacttelephone2")
                                                }
                                            }
                                    };
                                    company.Url = reader.GetAsString("url");
                                    company.UrlRewrite = reader.GetAsString("urlrewrite");
                                    company.IndustryId = reader.GetAsInt("idindustry");
                                    company.EmploymentNumber = reader.GetAsInt("idemploymentnumber");
                                    company.TypeId = reader.GetAsInt("idcompanytype");
                                    company.Description = reader.GetAsString("description");
                                    company.CreatedOn = reader.GetAsDateTime("createdon");
                                    company.CreatedBy = reader.GetAsInt("createdby");
                                    company.CompanyStatusId = reader.GetAsInt("idcompanystatus");
                                    company.IsPayment = reader.GetAsBoolean("ispayment");
                                    company.NdrStatusEmailId = reader.GetAsShort("ndrstatusemail");
                                    company.NdrStatusEmailContact = reader.GetAsInt("ndrstatusemailcontact");
                                    company.StatusId = reader.GetAsShort("idstatus");
                                    company.ConditionsId = reader.GetAsInt("condiciones");
                                    company.Ishighlighted = reader.GetAsBoolean("ishighlighted");
                                    company.Hidden = reader.GetAsBoolean("hiddencompany");
                                    company.NumOffers = reader.GetAsInt("numoffers");
                                    company.PortalId = reader.GetAsShort("idportal");
                                    company.ActiveOffers = reader.GetAsBoolean("activeoffers");
                                    company.ClientIpAdd = reader.GetAsString("client_ip_add");
                                    company.ClientIpMod = reader.GetAsString("client_ip_mod");
                                    company.UpdatedOn = reader.GetAsDateTime("updatedon");
                                    company.ShowPreHome = reader.GetAsBoolean("show_prehome");
                                    company.Blocked = reader.GetAsShort("blocked");
                                    company.BlockedType = reader.GetAsShort("blocked_type");
                                    company.LastDateBlockedUpdate = reader.GetAsDateTime("last_date_blocked_update");
                                    company.DateLastLogin = reader.GetAsDateTime("date_last_login");
                                    company.ProductId = reader.GetAsInt("product_id");
                                    company.NumOffersTotal = reader.GetAsInt("numoffers_total");
                                    company.BannerPath = reader.GetAsString("bannerpath");
                                    company.AtsServiceEnabled = reader.GetAsBoolean("ats_service_enabled");
                                    company.CompanyRejectionId = reader.GetAsInt("id_company_rejection");
                                    company.ConfigPromotions = reader.GetAsShort("config_promotions");
                                    company.ConfigNotifications = reader.GetAsShort("config_notifications");
                                    company.companyMision = reader.GetAsString("companyMision");
                                    company.companyValues = reader.GetAsString("companyValues");
                                    company.LogoPath = reader.GetAsString("logopath");
                                    company.HasATS = reader.GetAsBoolean("has_ats");
                                    company.HasPandapeATS = reader.GetAsBoolean("has_pandape_ats");
                                    company.CvVisualizationPrivacy = reader.GetAsShort("cvVisualizationPrivacy");
                                    company.RegisterAction = reader.GetAsShort("RegisterAction");
                                    company.StatusImportOffer = reader.GetAsShort("StatusImportOffer");

                                }
                                catch (Exception ex)
                                {
                                    Trace.TraceError($"CompanyRecoverAndPersist - GetByPk ex: {ex.ToString()}");
                                    _exceptionPublisherService.Publish(ex, "CompanyRecoverAndPersist", "GetByPK");
                                    return new CompanyEntity();
                                }
                            }
                        }
                    }
                }
            }
            return company;
        }

        public CompanyDetailEntity GetCompanyDetail(int idcompany, short idPortal = 0)
        {
            try
            {
                var companyDetail = new CompanyDetailEntity();
                using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, idPortal)))
                {
                    using (var command = new MySqlCommand("sp_dtcompany_SelectByPK_v5", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.Add(new MySqlParameter("_idcompany", idcompany));

                        connection.Open();

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                companyDetail.IdCompany = reader.GetAsInt("idcompany");
                                companyDetail.PortalId = reader.GetAsShort("idportal");
                                companyDetail.ComercialName = reader.GetAsString("comercialname");
                                companyDetail.CompanyName = reader.GetAsString("companyname");
                                companyDetail.Nit = reader.GetAsString("nit");
                                companyDetail.Address = reader.GetAsString("address");
                                companyDetail.IdLocalization = reader.GetAsShort("idlocalization");
                                companyDetail.IdCountry = reader.GetAsShort("idcountry");
                                companyDetail.IdCity = reader.GetAsShort("idcity");
                                companyDetail.IdIndustry = reader.GetAsShort("idindustry");
                                companyDetail.IdEmploymentNumber = reader.GetAsShort("idemploymentnumber");
                                companyDetail.Postalcode = reader.GetAsString("postalcode");
                            }
                        }
                    }
                }

                return companyDetail;
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "CompanyRecoverAndPersist", "GetCompanyDetail");
                return new CompanyDetailEntity();
            }
        }

        public DateTime DateLastUpdate(int companyId)
        {
            using (MySqlConnection conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
            {
                using (MySqlCommand cmd = new MySqlCommand("company_get_date_last_update", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;

                    try
                    {
                        cmd.Parameters.Add(new MySqlParameter("_idcompany", companyId));

                        conn.Open();

                        return cmd.ExecuteScalar().ToDateTime();
                    }
                    catch (System.Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyRepository", "DateLastUpdate");
                        return DateTime.Now;
                    }
                }
            }
        }

        public string GetCompanyComercialName(int idCompany, short portalId = 0)
        {
            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
            {
                using (MySqlCommand command = new MySqlCommand("company_get_comercial_name", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_idcompany", idCompany));

                    try
                    {
                        connection.Open();

                        return command.ExecuteScalar().ToStringSafely();
                    }
                    catch (Exception ex)
                    {
                        var extradata = new Dictionary<string, string>
                        {
                            { "idCompany", idCompany.ToString() },
                            { "portalId", portalId.ToString() }
                        };
                        _exceptionPublisherService.Publish(ex, "CompanyRepository", "GetCompanyComercialName",false, extradata);
                        return string.Empty;
                    }
                }
            }
        }

        public DateTime CompanyMaxProductExpirationDate(int companyId)
        {
            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
            {
                using (MySqlCommand command = new MySqlCommand("company_get_product_expiration_date", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    command.Parameters.Add(new MySqlParameter("_companyId", companyId));

                    try
                    {
                        connection.Open();

                        return command.ExecuteScalar().ToDateTime();
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyRepository", "CompanyMaxProductExpirationDate");
                        return DateTime.MinValue;
                    }
                }
            }
        }

        public int CountAllFilesReport(short portalId, int companyId)
        {
            if (portalId <= 0 || companyId <= 0)
                return 0;

            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
            {
                using (MySqlCommand command = new MySqlCommand("count_company_files", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    try
                    {
                        command.Parameters.Add(new MySqlParameter("_portal_id", portalId));
                        command.Parameters.Add(new MySqlParameter("_company_id", companyId));

                        connection.Open();
                        return command.ExecuteScalar().ToInt();
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyRepository", "SearchCompanyFiles");
                        return 0;
                    }
                }
            }
        }

        public bool SaveConfigurationEmails(CompanyEntity company)
        {
            try
            {
                using (var conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
                {
                    using (var cmd = new MySqlCommand("update_config_email_by_company", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.Add(new MySqlParameter("_idcompany", company.Id));
                        cmd.Parameters.Add(new MySqlParameter("_config_notifications", company.ConfigNotifications));
                        cmd.Parameters.Add(new MySqlParameter("_config_promotions", company.ConfigPromotions));

                        conn.Open();
                        cmd.ExecuteNonQuery();
                        return true;
                    }
                }
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "CompanyRepository", "SaveConfigurationEmails");
            }

            return false;
        }

        public bool SaveConfigCvVisualization(CompanyEntity company)
        {
            try
            {
                using (var conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
                {
                    using (var cmd = new MySqlCommand("ae_cvVisualizationConfigUpdate", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.Add(new MySqlParameter("_idcompany", company.Id));
                        cmd.Parameters.Add(new MySqlParameter("_cvVisualizationPrivacy", company.CvVisualizationPrivacy));

                        conn.Open();
                        cmd.ExecuteNonQuery();
                        return true;
                    }
                }
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "CompanyRepository", "SaveConfigCvVisualization");
            }

            return false;
        }

        public short GetConfigNotificationByPk(int companyId)
        {
            using (var conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
            {
                using (var cmd = new MySqlCommand("company_get_notification_value", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;

                    try
                    {
                        cmd.Parameters.Add(new MySqlParameter("_idcompany", companyId));

                        conn.Open();
                        return cmd.ExecuteScalar().ToShort();
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyRepository", "GetConfigNotificationByPk");
                        return 0;
                    }
                }
            }
        }

        public void UpdateFollowers(int idCompany, short idportal)
        {
            try
            {
                using (var conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Data)))
                {
                    using (var cmd = new MySqlCommand("prc_dtcandidate_followers_set_new_offers", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.Add(new MySqlParameter("_idcompany", idCompany));
                        cmd.Parameters.Add(new MySqlParameter("_idportal", idportal));

                        conn.Open();
                        cmd.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "CompanyRepository", "UpdateFollowers");
            }
        }

        public short GetSizeIdByIdNEmployer(short employmentNumberId)
        {
            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
            {
                using (MySqlCommand command = new MySqlCommand("get_company_size_id", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    command.Parameters.Add(new MySqlParameter("_idemploymentnumber", employmentNumberId));

                    try
                    {
                        connection.Open();
                        return command.ExecuteScalar().ToShort();
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyRepository", "TryGetSizeIdByIdNEmployer");
                        return 0;
                    }
                }
            }
        }

        public int GetCompanyFollowersCount(int idCompany, short portalId)
        {
            try
            {
                using (var conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Data)))
                {
                    using (var command = new MySqlCommand("get_company_followers_count", conn))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("_idcompany", idCompany);
                        command.Parameters.AddWithValue("_idportal", portalId);

                        conn.Open();

                        return command.ExecuteScalar().ToInt();
                    }
                }
            }
            catch (Exception e)
            {
                _exceptionPublisherService.Publish(e, "CompanyRepository", "GetCompanyFollowersNumber");
                return 0;
            }
        }

        public bool UpdateAboutCompany(CompanyAboutEntity company)
        {
            try
            {
                using (var conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
                {
                    using (var cmd = new MySqlCommand("bw_dtcompany_UpdateAboutCompany", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.Add(new MySqlParameter("_idcompany", company.IdCompany));
                        cmd.Parameters.Add(new MySqlParameter("_idportal", company.PortalId));
                        cmd.Parameters.Add(new MySqlParameter("_description", company.Description));
                        cmd.Parameters.Add(new MySqlParameter("_companyMision", company.CompanyMision));
                        cmd.Parameters.Add(new MySqlParameter("_companyValues", company.CompanyValues));
                        conn.Open();
                        cmd.ExecuteNonQuery();

                        return true;
                    }
                }
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "CompanyRepository", "UpdateAboutCompany");
                return false;
            }
        }

        public bool UpdateDetail(CompanyDetailEntity companyDetail, short portalId)
        {
            try
            {
                var columnsToUpdate = new List<string>();

                if (!string.IsNullOrEmpty(companyDetail.ComercialName))
                    columnsToUpdate.Add($"comercialname = '{companyDetail.ComercialName}'");
                if (!string.IsNullOrEmpty(companyDetail.CompanyName))
                    columnsToUpdate.Add($"companyname = '{companyDetail.CompanyName}'");
                if (!string.IsNullOrEmpty(companyDetail.Nit))
                    columnsToUpdate.Add($"nit = '{companyDetail.Nit}'");
                if (companyDetail.IdCountry != 0)
                    columnsToUpdate.Add($"idcountry = {companyDetail.IdCountry}");
                if (companyDetail.IdCity != 0)
                    columnsToUpdate.Add($"idcity = {companyDetail.IdCity}");
                if (companyDetail.IdLocalization != 0)
                    columnsToUpdate.Add($"idlocalization = '{companyDetail.IdLocalization}'");
                if (!string.IsNullOrEmpty(companyDetail.Address))
                    columnsToUpdate.Add($"address = '{companyDetail.Address}'");
                if (companyDetail.IdIndustry != 0)
                    columnsToUpdate.Add($"idindustry = {companyDetail.IdIndustry}");
                if (companyDetail.IdEmploymentNumber != 0)
                    columnsToUpdate.Add($"idemploymentnumber = {companyDetail.IdEmploymentNumber}");
                if (!string.IsNullOrEmpty(companyDetail.Postalcode))
                    columnsToUpdate.Add($"postalcode = '{companyDetail.Postalcode}'");
                if (!string.IsNullOrEmpty(companyDetail.ClientIpMod))
                    columnsToUpdate.Add($"client_ip_mod = '{companyDetail.ClientIpMod}'");

                var sCommand = new StringBuilder("Update dtcompany SET ");
                sCommand.AppendLine(string.Join(",", columnsToUpdate));
                sCommand.AppendLine($" Where idportal = {portalId} And idcompany = {companyDetail.IdCompany}");

                using (var conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
                {
                    using (var cmd = new MySqlCommand(sCommand.ToString(), conn))
                    {
                        cmd.CommandType = CommandType.Text;
                        conn.Open();
                        cmd.ExecuteNonQuery();

                        return true;
                    }
                }
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "CompanyRepository", "UpdateDetail");
                return false;
            }
        }

        public int ShouldSeeCompuAdvisorTab(int companyId, short portalId)
        {
            try
            {
                using (var conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
                {
                    using (var cmd = new MySqlCommand("compuadvisor_get_can_have_compuadvisor", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.Add(new MySqlParameter("_idcompany", companyId));
                        cmd.Parameters.Add(new MySqlParameter("_idportal", portalId));
                        conn.Open();
                        int result = cmd.ExecuteScalar().ToInt();
                        if (result == 0)
                            return -1;
                        return result;
                    }
                }
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "CompanyRepository", "ShouldSeeCompuAdvisorTab");
                return -1;
            }
        }

        public bool InsertCompanyGuIdControl(short idportal, short idapp, int companyid, string uid, DateTime lastdate, string ip, long idUser, string userAgent)
        {
            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.RepoAuthenticate)))
            {
                using (MySqlCommand command = new MySqlCommand("AeCustomerGuidControlInsert", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    try
                    {
                        command.Parameters.Add(new MySqlParameter("_idPortal", idportal));
                        command.Parameters.Add(new MySqlParameter("_idApp", idapp));
                        command.Parameters.Add(new MySqlParameter("_idCustomer", companyid));
                        command.Parameters.Add(new MySqlParameter("_idUser", idUser));
                        command.Parameters.Add(new MySqlParameter("_clientIP", ip));
                        command.Parameters.Add(new MySqlParameter("_uid", uid));
                        command.Parameters.Add(new MySqlParameter("_userAgent", userAgent));
                        command.Parameters.Add(new MySqlParameter("_dateLastControl", lastdate));
                        connection.Open();

                        command.ExecuteNonQuery();
                    }
                    catch (System.Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyRecoverAndPersist", "InsertCompanyGuIdControl");
                        return false;
                    }
                }
            }
            return true;
        }

        public async Task<bool> InsertCompanyGuIdControlAsync(CompanyGuIdControlEntity companyGuIdControlEntity)
        {
            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.RepoAuthenticate)))
            {
                using (MySqlCommand command = new MySqlCommand("AeCustomerGuidControlInsert", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    try
                    {
                        command.Parameters.Add(new MySqlParameter("_idPortal", companyGuIdControlEntity.PortalId));
                        command.Parameters.Add(new MySqlParameter("_idApp", companyGuIdControlEntity.IdApp));
                        command.Parameters.Add(new MySqlParameter("_idCustomer", companyGuIdControlEntity.CompanyId));
                        command.Parameters.Add(new MySqlParameter("_idUser", companyGuIdControlEntity.IdUser));
                        command.Parameters.Add(new MySqlParameter("_clientIP", companyGuIdControlEntity.Ip));
                        command.Parameters.Add(new MySqlParameter("_uid", companyGuIdControlEntity.Uid));
                        command.Parameters.Add(new MySqlParameter("_userAgent", companyGuIdControlEntity.UserAgent));
                        command.Parameters.Add(new MySqlParameter("_dateLastControl", companyGuIdControlEntity.LastDate));
                        connection.Open();

                        await command.ExecuteNonQueryAsync();
                    }
                    catch (System.Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyRecoverAndPersist", "InsertCompanyGuIdControl");
                        return false;
                    }
                }
            }
            return true;
        }

        public int UpdateDateLastLogin(int companyId, short portalId)
        {
            using (var conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
            {
                using (var cmd = new MySqlCommand("gl_company_update_last_login", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    try
                    {
                        cmd.Parameters.Add(new MySqlParameter("_idcompany", companyId));
                        cmd.Parameters.Add(new MySqlParameter("_idportal", portalId));

                        conn.Open();
                        return cmd.ExecuteNonQuery().ToInt();
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyRecoverAndPersist", "UpdateDateLastLogin", false, null, portalId);
                        return -1;
                    }
                }
            }
        }

        public async Task<int> UpdateDateLastLoginAsync(int companyId, short portalId)
        {
            using (var conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
            {
                using (var cmd = new MySqlCommand("gl_company_update_last_login", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    try
                    {
                        cmd.Parameters.Add(new MySqlParameter("_idcompany", companyId));
                        cmd.Parameters.Add(new MySqlParameter("_idportal", portalId));

                        conn.Open();
                        return await cmd.ExecuteNonQueryAsync();
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyRecoverAndPersist", "UpdateDateLastLogin", false, null, portalId);
                        return -1;
                    }
                }
            }
        }

        public string GetLogoPath(int companyId)
        {
            using (MySqlConnection conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
            {
                using (MySqlCommand cmd = new MySqlCommand("sp_dtcompany_GetLogoByPk", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    try
                    {
                        cmd.Parameters.Add(new MySqlParameter("_idcompany", companyId));

                        conn.Open();
                        return cmd.ExecuteScalar().ToStringSafely();
                    }
                    catch (System.Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyRecoverAndPersist", "GetLogoPath");
                        return string.Empty;
                    }
                }
            }
        }

        public List<string> GetCompanyDomains(int idCompany)
        {
            var domains = new List<string>();
            using (var conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
            {
                using (var cmd = new MySqlCommand("sp_company_domains_select_by_company", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    try
                    {
                        cmd.Parameters.Add(new MySqlParameter("_idcompany", idCompany));
                        conn.Open();
                        using (var reader = cmd.ExecuteReader())
                        {
                            if (reader != null)
                            {
                                while (reader.Read())
                                {
                                    domains.Add(reader.GetString("domain"));
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyRecoverAndPersist", "GetCompanyDomains");
                    }
                }
            }
            return domains;
        }

        public bool UpdateActiveOffers(int idcompany, int numOffers, int activeOffers, short idPortal)
        {
            try
            {
                using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, idPortal)))
                {
                    connection.Open();

                    using (var command = new MySqlCommand("sp_dtcompany_UpdateActiveOffers", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.Add(new MySqlParameter("_idcompany", idcompany));
                        command.Parameters.Add(new MySqlParameter("_activeoffers", activeOffers));
                        command.Parameters.Add(new MySqlParameter("_numoffers", numOffers));
                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "CompanyRecoverAndPersist", "UpdateActiveOffers", false, null, idPortal);
                return false;
            }

            return true;
        }

        public void UpdateShowPrehome(int companyId, short portalId, int valueShowPrehome)
        {
            using (var conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
            {
                using (var command = new MySqlCommand("sp_company_update_show_prehome", conn))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    try
                    {
                        command.Parameters.Add(new MySqlParameter("_idcompany", companyId));
                        command.Parameters.Add(new MySqlParameter("_idportal", portalId));
                        command.Parameters.Add(new MySqlParameter("_show_prehome", valueShowPrehome));
                        conn.Open();
                        command.ExecuteNonQuery();
                    }
                    catch (Exception ex)
                    {
                        Trace.TraceError($"CompanyRecoverAndPersist - UpdateShowPrehome: {ex.ToString()}");
                        _exceptionPublisherService.Publish(ex, "CompanyRecoverAndPersist", "UpdateShowPrehome");
                    }
                }
            }
        }

        public bool GetVisibilitySection(int companyId, string section)
        {
            using (var conex = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
            {
                using (var command = new MySqlCommand("get_visibility_by_company", conex))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    try
                    {
                        command.Parameters.Add(new MySqlParameter("_companyid", companyId));
                        command.Parameters.Add(new MySqlParameter("_field", section));

                        conex.Open();
                        return command.ExecuteScalar().ToBoolean();
                    }
                    catch (Exception ex)
                    {
                        Trace.TraceError($"CompanyRecoverAndPersist - GetVisibilitySection: ex: {ex.ToString()}");
                        _exceptionPublisherService.Publish(ex, "CompanyRecoverAndPersist", "GetVisibilitySection");
                        return false;
                    }
                }
            }
        }

        private void BuildCompany(CompanyEntity company)
        {
            BuildContactImformation(company);
            BuildPhoneNumbers(company);
        }

        private void BuildContactImformation(CompanyEntity company)
        {
            if (company.ContactInformation == null)
            {
                company.ContactInformation = new ContactInformationEntity();
            }
        }

        private void BuildPhoneNumbers(CompanyEntity company)
        {
            if (company.ContactInformation.PhoneNumbers == null)
            {
                company.ContactInformation.PhoneNumbers = new List<PhoneNumberEntity>();
            }

            for (int i = company.ContactInformation.PhoneNumbers.Count; i < 2; i++)
            {
                company.ContactInformation.PhoneNumbers.Add(new PhoneNumberEntity());
            }
        }

        public bool UpdateCompanyEmail(int idCompany, string newEmail, short idPortal)
        {
            try
            {
                using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
                {
                    connection.Open();

                    using (var command = new MySqlCommand("sp_dtcompany_UpdateEmail_Portal", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.Add(new MySqlParameter("_idcompany", idCompany));
                        command.Parameters.Add(new MySqlParameter("_email", newEmail));
                        command.Parameters.Add(new MySqlParameter("_idportal", idPortal));
                        command.ExecuteNonQuery();

                        return true;
                    }
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyRecoverAndPersist - UpdateCompanyEmail: ex: {ex.ToString()}");
                _exceptionPublisherService.Publish(ex, "CompanyRecoverAndPersist", "UpdateCompanyEmail");
                return false;
            }
        }

        public CompanyRejection GetRejection(int rejectionId, short portalId)
        {
            var result = new CompanyRejection();
            using (var conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
            {
                using (var command = new MySqlCommand("ae_company_rejections_getByPK", conn))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    try
                    {
                        command.Parameters.Add(new MySqlParameter("_id", rejectionId));
                        command.Parameters.Add(new MySqlParameter("_portalid", portalId));

                        conn.Open();

                        using (var dtr = command.ExecuteReader())
                        {
                            if (dtr != null && dtr.Read())
                            {
                                result.Reason = dtr.GetString("reason");
                                result.Solution = dtr.GetString("solution");
                                result.Action = dtr.GetInt16("action");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Trace.TraceError($"CompanyRecoverAndPersist - GetRejection: ex: {ex.ToString()}");
                        _exceptionPublisherService.Publish(ex, "CompanyRecoverAndPersist", "GetRejection");
                    }
                }
            }

            return result;
        }

        public CompanyCustomer GetCompanyCustomer(int idCompany, short idPortal)
        {
            var companyCustomer = new CompanyCustomer();
            using (MySqlConnection conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.WhiteLabel, idPortal)))
            {
                using (var command = new MySqlCommand("customer_get_all_by_portal_idcompany", conn))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    try
                    {
                        command.Parameters.Add(new MySqlParameter("_portal_id", idPortal));
                        command.Parameters.Add(new MySqlParameter("_company_id", idCompany));

                        conn.Open();

                        using (var dtr = command.ExecuteReader())
                        {
                            if (dtr != null && dtr.Read())
                            {
                                companyCustomer = new CompanyCustomer()
                                {
                                    Id = dtr.GetAsInt("id"),
                                    IntegrationName = dtr.GetAsString("integration_name"),
                                    Subdomain = dtr.GetAsString("subdomain"),
                                    PortalId = dtr.GetAsShort("portal_id"),
                                    StatusId = dtr.GetAsShort("status_id"),
                                    Header = dtr.GetAsString("header"),
                                    Footer = dtr.GetAsString("footer"),
                                    DateAdd = dtr.GetAsDateTime("date_add"),
                                    DateMod = dtr.GetAsDateTime("date_mod"),
                                    DateDel = dtr.GetAsDateTime("date_del"),
                                    HasCourses = dtr.GetAsBoolean("has_courses"),
                                    HasOffers = dtr.GetAsBoolean("has_offers"),
                                    DefaultPageId = dtr.GetAsShort("default_page_id")
                                };
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Trace.TraceError($"CompanyRecoverAndPersist - GetCompanyCustomer: ex: {ex.ToString()}");
                        _exceptionPublisherService.Publish(ex, "CompanyRecoverAndPersist", "GetCompanyCustomer");
                    }
                }
            }

            return companyCustomer;
        }

        public string GetCompanyCRMNavisionId(int idCompany, short idPortal)
        {
            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, idPortal)))
            {
                using (MySqlCommand command = new MySqlCommand("ae_dtcompany_crm_get_nav_id", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_idcompany", idCompany));

                    try
                    {
                        connection.Open();

                        return command.ExecuteScalar().ToStringSafely();
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyRepository", "GetCompanyCRMNavisionId");
                        return string.Empty;
                    }
                }
            }
        }

        public CompanyProductCV GetByCompanyCv(int companyId, int idcv)
        {
            var companyProductCv = new CompanyProductCV();
            using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.ReadMaster)))
            {
                using (var command = new MySqlCommand("ae_company_product_cv_getby_company_cv", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    try
                    {
                        command.Parameters.Add(new MySqlParameter("_company_id", companyId));
                        command.Parameters.Add(new MySqlParameter("_cv_id", idcv));

                        connection.Open();

                        using (var reader = command.ExecuteReader())
                        {
                            if (reader != null &&
                                reader.Read())
                            {
                                companyProductCv.CompanyId = reader.GetInt32("company_id");
                                companyProductCv.CvId = reader.GetInt32("cv_id");
                                companyProductCv.CandidateId = reader.GetInt32("candidate_id");
                                companyProductCv.UserId = reader.GetInt32("user_id");
                                companyProductCv.OfferId = reader.GetInt32("offer_id");
                                companyProductCv.DateAdd = reader.GetDateTime("date_add");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyRecoverAndPersist", "GetByCompanyCv");
                    }
                }
            }
            return companyProductCv;
        }

        public Dictionary<int, short> GetIdCompanyByNit(string nit, int portalId, int countryId, string listRegisterAction)
        {
            Dictionary<int, short> dicCompanyByNit = new Dictionary<int, short>();
            using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
            {
                using (var command = new MySqlCommand("getIdCompanyByNitV4", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    try
                    {
                        command.Parameters.Add(new MySqlParameter("_portal_id", portalId));
                        command.Parameters.Add(new MySqlParameter("_nit", nit));
                        command.Parameters.Add(new MySqlParameter("_id_country", countryId));
                        command.Parameters.Add(new MySqlParameter("_list_register_action", listRegisterAction));
                        connection.Open();
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader != null && reader.Read())
                            {
                                dicCompanyByNit.Add(reader.GetInt32("idcompany"), reader.GetAsShort("RegisterAction"));
                            }
                        }
                       
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyRecoverAndPersist", "GetIdCompanyByNit");
                    }
                }
            }
            return dicCompanyByNit;
        }

        public void UpdateRegisterActionCompany(CompanyEntity company)
        {
            if (company == null)
                return;

            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
            {
                using (MySqlCommand command = new MySqlCommand("companies_dtcompany_registeraction_update_V4", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_idcompany", company.Id));
                    command.Parameters.Add(new MySqlParameter("_idportal", company.PortalId));
                    command.Parameters.Add(new MySqlParameter("_registeraction", company.RegisterAction));
                    command.Parameters.Add(new MySqlParameter("_companyuserdefault", company.User.Username));
                    try
                    {
                        connection.Open();

                        command.ExecuteNonQuery();
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyRecoverAndPersist", "UpdateRegisterActionCompany");
                    }
                }
            }
        }

        public void DisabledOldCompanyUser(CompanyEntity company)
        {
            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
            {
                using (MySqlCommand command = new MySqlCommand("companies_dtuser_disabled_user", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_idcompany", company.Id));
                    command.Parameters.Add(new MySqlParameter("_idportal", company.PortalId));
                    try
                    {
                        connection.Open();

                        command.ExecuteNonQuery();
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyRecoverAndPersist", "DisabledOldCompanyUser");
                    }
                }
            }
        }

        public int HasUrlFriendly(int companyId)
        {
            using (var conex = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
            {
                using (var command = new MySqlCommand("ae_company_has_url_friendly", conex))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    try
                    {
                        command.Parameters.Add(new MySqlParameter("_company_id", companyId));
                        conex.Open();

                        return command.ExecuteScalar().ToInt();
                    }
                    catch (Exception ex)
                    {
                        Trace.TraceError($"CompanyRecoverAndPersist - HasUrlFriendly: ex: {ex.ToString()}");
                        _exceptionPublisherService.Publish(ex, "CompanyRecoverAndPersist", "HasUrlFriendly");
                        return 0;
                    }
                }
            }
        }

        public bool ChangeStatusCompany(short portalId, int companyId, CompanyStatusEnum companyStatus, StatusEnum status, short rejectionId)
        {
            using (var conex = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
            {
                using (MySqlCommand command = new MySqlCommand("bw_dtcompany_ChangeCompanyStatus_Portal_v2", conex))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    try
                    {
                        command.Parameters.Add(new MySqlParameter("_idcompany", companyId));
                        command.Parameters.Add(new MySqlParameter("_idcompanystatus", (short)companyStatus));
                        command.Parameters.Add(new MySqlParameter("_idstatus", (short)status));
                        command.Parameters.Add(new MySqlParameter("_idportal", portalId));
                        command.Parameters.Add(new MySqlParameter("_id_company_rejection", rejectionId));

                        conex.Open();

                        command.ExecuteNonQuery();
                    }
                    catch (Exception ex)
                    {
                        Trace.TraceError($"CompanyRecoverAndPersist - ChangeStatusCompany: ex: {ex}");
                        _exceptionPublisherService.Publish(ex, "CompanyRecoverAndPersist", "ChangeStatusCompany");
                        return false;
                    }
                }
            }

            return true;
        }

        public bool UpdateDateLastModerationAccept(int idCompany, short idPortal)
        {
            using (MySqlConnection conex = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
            {
                using (MySqlCommand command = new MySqlCommand("bw_company_tracking_update_date_last_moderation_accept", conex))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    try
                    {
                        command.Parameters.Add(new MySqlParameter("_idcompany", idCompany));
                        command.Parameters.Add(new MySqlParameter("_idportal", idPortal));
                        command.Parameters.Add(new MySqlParameter("_dt", DateTime.Now));

                        conex.Open();
                        command.ExecuteNonQuery();
                    }
                    catch (Exception ex)
                    {
                        Trace.TraceError($"CompanyRecoverAndPersist - UpdateDateLastModerationAccept: ex: {ex}");
                        _exceptionPublisherService.Publish(ex, "CompanyRecoverAndPersist", "UpdateDateLastModerationAccept");
                        return false;
                    }
                }
            }
            return true;
        }

        public bool CompanyOk(int idCompany, short idPortal)
        {
            using (MySqlConnection conex = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
            {
                using (MySqlCommand command = new MySqlCommand("bw_moderation_company_stats_company_ok", conex))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    try
                    {
                        command.Parameters.Add(new MySqlParameter("_idcompany", idCompany));
                        command.Parameters.Add(new MySqlParameter("_idportal", idPortal));

                        conex.Open();
                        command.ExecuteNonQuery();
                    }
                    catch (Exception ex)
                    {
                        Trace.TraceError($"CompanyRecoverAndPersist - CompanyOk: ex: {ex}");
                        _exceptionPublisherService.Publish(ex, "CompanyRecoverAndPersist", "CompanyOk");
                        return false;
                    }
                }
            }
            return true;
        }
      
        public bool UpdateLastModerationCompany(int idCompany, short idPortal, int idModerator)
        {
            using (var l_conex = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
            {
                using (var l_command = new MySqlCommand("ae_company_update_last_moderation_time", l_conex))
                {
                    l_command.CommandType = CommandType.StoredProcedure;
                    try
                    {
                        l_command.Parameters.Add(new MySqlParameter("_portalId", idPortal));
                        l_command.Parameters.Add(new MySqlParameter("_companyId", idCompany));
                        l_command.Parameters.Add(new MySqlParameter("_idModerator", idModerator));
                        l_conex.Open();

                        l_command.ExecuteNonQuery();

                    }
                    catch (Exception ex)
                    {
                        Trace.TraceError($"CompanyRecoverAndPersist - UpdateLastModerationCompany: ex: {ex}");
                        _exceptionPublisherService.Publish(ex, "CompanyRecoverAndPersist", "UpdateLastModerationCompany");
                        return false;
                    }
                }
            }
            return true;
        }
        public async Task<bool> InsertStackCompanyAsync(int idCompany, short idPortal)
        {
            using (var l_conex = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, idPortal)))
            {                                                       
                using (var l_command = new MySqlCommand("ae_dtstack_companies_insert", l_conex))
                {
                    l_command.CommandType = CommandType.StoredProcedure;
                    try
                    {
                        l_command.Parameters.Add(new MySqlParameter("_idcompany", idCompany));
                        l_conex.Open();
                        await l_command.ExecuteNonQueryAsync();
                    }
                    catch (Exception ex)
                    {
                        Trace.TraceError($"CompanyRecoverAndPersist - InsertStackCompany: ex: {ex}");
                        _exceptionPublisherService.Publish(ex, "CompanyRecoverAndPersist", "InsertStackCompany");
                        return false;
                    }
                }
            }
            return true;
        }

        public async Task<DateTime> DateLastUpdateAsync(CompanySearchSpecifications specifications)
        {
            using (MySqlConnection conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, specifications.PortalId)))
            {
                using (MySqlCommand cmd = new MySqlCommand("company_get_date_last_update", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;

                    try
                    {
                        cmd.Parameters.Add(new MySqlParameter("_idcompany", specifications.Id));

                        conn.Open();

                        return (await cmd.ExecuteScalarAsync()).ToDateTime();
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyRepository", "DateLastUpdateAsync", false, null, specifications.PortalId);
                        return DateTime.Now;
                    }
                }
            }
        }

        public async Task<CompanyEntity> GetByPKAsync(CompanySearchSpecifications specifications)
        {
            if (specifications == null || specifications.Id == 0 || specifications.PortalId == 0)
                return new CompanyEntity();

            CompanyEntity company = new CompanyEntity();
            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, specifications.PortalId)))
            {
                using (MySqlCommand command = new MySqlCommand("company_user_select_v5", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    command.Parameters.Add(new MySqlParameter("_idcompany", specifications.Id));

                    connection.Open();

                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        if (reader != null)
                        {
                            while (reader.Read())
                            {
                                try
                                {
                                    company.Id = reader.GetAsInt("idcompany");
                                    company.MasterId = reader.GetAsInt("idmaster");
                                    company.User = new Master.Entities.Users.UserEntity()
                                    {
                                        Id = reader.GetAsInt("iduser"),
                                        AppId = reader.GetAsInt("idapp"),
                                        CompanyId = reader.GetAsInt("idcompany"),
                                        ContactName = reader.GetAsString("contactname"),
                                        CreatedBy = reader.GetAsInt("createdby"),
                                        CreatedOn = reader.GetAsDateTime("createdon"),
                                        CurrentCvStep = reader.GetAsShort("current_cv_step"),
                                        DeletedOn = reader.GetAsDateTime("deletedon"),
                                        Email = reader.GetAsString("email"),
                                        Fax = reader.GetAsString("fax"),
                                        LastLoginOn = reader.GetAsDateTime("lastlogin"),
                                        NdrStatusId = reader.GetAsInt("ndrstatus"),
                                        OriginId = reader.GetAsInt("idorigin"),
                                        ParentCompanyId = reader.GetAsInt("idparentcompany"),
                                        Password = reader.GetAsString("password"),
                                        PhoneNumbers = reader.GetAsString("telephone"),
                                        PortalId = reader.GetAsShort("idportal"),
                                        Principal = reader.GetAsShort("principal"),
                                        RoleId = reader.GetAsShort("user_role"),
                                        StatusId = reader.GetAsInt("idstatus"),
                                        TypeId = reader.GetAsInt("idusertype"),
                                        UpdatedOn = reader.GetAsDateTime("updatedon"),
                                        Username = reader.GetAsString("username"),
                                        VerifiedMail = reader.GetAsShort("verifiedmail")
                                    };
                                    company.ParentId = reader.GetAsInt("idparent");
                                    company.ComercialName = reader.GetAsString("comercialname");
                                    company.CompanyName = reader.GetAsString("companyname");
                                    company.Nit = reader.GetAsString("nit");
                                    company.Address = new AddressEntity()
                                    {
                                        StreetAddress = reader.GetAsString("address"),
                                        CityId = reader.GetAsInt("idcity"),
                                        CountryId = reader.GetAsInt("idcountry"),
                                        LocationId = reader.GetAsInt("idlocalization"),
                                        ZipCode = reader.GetAsString("postalcode"),
                                    };
                                    company.ContactInformation = new ContactInformationEntity
                                    {
                                        Name = reader.GetAsString("contactname"),
                                        Surname = reader.GetAsString("contactsurname"),
                                        PositionId = reader.GetAsInt("idcontactposition"),
                                        Email = reader.GetAsString("contactemail"),
                                        Username = reader.GetAsString("username"),
                                        PhoneNumbers = new List<PhoneNumberEntity>()
                                            {
                                                new PhoneNumberEntity()
                                                {
                                                    TypeId = reader.GetAsShort("idtelephonetype1"),
                                                    Number = reader.GetAsString("contacttelephone1")
                                                },
                                                new PhoneNumberEntity()
                                                {
                                                    TypeId = reader.GetAsShort("idtelephonetype2"),
                                                    Number = reader.GetAsString("contacttelephone2")
                                                }
                                            }
                                    };
                                    company.Url = reader.GetAsString("url");
                                    company.UrlRewrite = reader.GetAsString("urlrewrite");
                                    company.IndustryId = reader.GetAsInt("idindustry");
                                    company.EmploymentNumber = reader.GetAsInt("idemploymentnumber");
                                    company.TypeId = reader.GetAsInt("idcompanytype");
                                    company.Description = reader.GetAsString("description");
                                    company.CreatedOn = reader.GetAsDateTime("createdon");
                                    company.CreatedBy = reader.GetAsInt("createdby");
                                    company.CompanyStatusId = reader.GetAsInt("idcompanystatus");
                                    company.IsPayment = reader.GetAsBoolean("ispayment");
                                    company.NdrStatusEmailId = reader.GetAsShort("ndrstatusemail");
                                    company.NdrStatusEmailContact = reader.GetAsInt("ndrstatusemailcontact");
                                    company.StatusId = reader.GetAsShort("idstatus");
                                    company.ConditionsId = reader.GetAsInt("condiciones");
                                    company.Ishighlighted = reader.GetAsBoolean("ishighlighted");
                                    company.Hidden = reader.GetAsBoolean("hiddencompany");
                                    company.NumOffers = reader.GetAsInt("numoffers");
                                    company.PortalId = reader.GetAsShort("idportal");
                                    company.ActiveOffers = reader.GetAsBoolean("activeoffers");
                                    company.ClientIpAdd = reader.GetAsString("client_ip_add");
                                    company.ClientIpMod = reader.GetAsString("client_ip_mod");
                                    company.UpdatedOn = reader.GetAsDateTime("updatedon");
                                    company.ShowPreHome = reader.GetAsBoolean("show_prehome");
                                    company.Blocked = reader.GetAsShort("blocked");
                                    company.BlockedType = reader.GetAsShort("blocked_type");
                                    company.LastDateBlockedUpdate = reader.GetAsDateTime("last_date_blocked_update");
                                    company.DateLastLogin = reader.GetAsDateTime("date_last_login");
                                    company.ProductId = reader.GetAsInt("product_id");
                                    company.NumOffersTotal = reader.GetAsInt("numoffers_total");
                                    company.BannerPath = reader.GetAsString("bannerpath");
                                    company.AtsServiceEnabled = reader.GetAsBoolean("ats_service_enabled");
                                    company.CompanyRejectionId = reader.GetAsInt("id_company_rejection");
                                    company.ConfigPromotions = reader.GetAsShort("config_promotions");
                                    company.ConfigNotifications = reader.GetAsShort("config_notifications");
                                    company.companyMision = reader.GetAsString("companyMision");
                                    company.companyValues = reader.GetAsString("companyValues");
                                    company.LogoPath = reader.GetAsString("logopath");
                                    company.HasATS = reader.GetAsBoolean("has_ats");
                                    company.HasPandapeATS = reader.GetAsBoolean("has_pandape_ats");
                                    company.CvVisualizationPrivacy = reader.GetAsShort("cvVisualizationPrivacy");
                                    company.RegisterAction = reader.GetAsShort("RegisterAction");
                                    company.StatusImportOffer = reader.GetAsShort("StatusImportOffer");

                                }
                                catch (Exception ex)
                                {
                                    Trace.TraceError($"CompanyRecoverAndPersist - GetByPkAsync ex: {ex.ToString()}");
                                    _exceptionPublisherService.Publish(ex, "CompanyRecoverAndPersist", "GetByPkAsync", false, null, specifications.PortalId);
                                    return new CompanyEntity();
                                }
                            }
                        }
                    }
                }
            }
            return company;
        }

        public bool UpdateStatusImportOffer(int idCompany, short idportal,short statusImportOffer)
        {
            try
            {
                using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master,idportal)))
                {
                    using (var cmd = new MySqlCommand("company_update_statusImportOffer_v1", connection))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.Add(new MySqlParameter("_idcompany", idCompany));
                        cmd.Parameters.Add(new MySqlParameter("_idportal", idportal));
                        cmd.Parameters.Add(new MySqlParameter("_statusImportOffer", statusImportOffer));

                        connection.Open();
                        cmd.ExecuteNonQuery();
                        return true;
                    }
                }
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "CompanyRepository", "UpdateStatusImportOffer");
                return false;
            }
        }

        public short GetStatusImportOffer(int idCompany, short idPortal)
        {
            short statusImportOffer = 0;
            var query = "SELECT StatusImportOffer " +
                        "FROM dtcompany " +
                        "WHERE IdCompany = @idCompany " +
                        "AND IdPortal = @idPortal ";

            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, idPortal)))
            {
                using (MySqlCommand command = new MySqlCommand(query, connection))
                {
                    command.CommandType = CommandType.Text;
                    command.Parameters.Add(new MySqlParameter("@idCompany", idCompany));
                    command.Parameters.Add(new MySqlParameter("@idPortal", idPortal));

                    try
                    {
                        connection.Open();

                        var result = command.ExecuteScalar();

                        if (result != null)
                        {
                            short.TryParse(result.ToString(), out statusImportOffer);
                        }

                        return statusImportOffer;
                    }
                    catch (Exception ex)
                    {
                        Trace.TraceError($"CompanyRecoverAndPersist - GetStatusImportOffer: {ex}");
                        _exceptionPublisherService.Publish(ex, "CompanyRecoverAndPersist", "GetStatusImportOffer", false, null, idPortal);
                        return statusImportOffer;
                    }
                }
            }
        }

        public CompanyEntity GetCompanyByNit(string nit, short idPortal = 0)
        {
            try
            {
                var company = new CompanyEntity();
                using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, idPortal)))
                {
                    using (var command = new MySqlCommand("sp_dtcompany_SelectByNit", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.Add(new MySqlParameter("_nit", nit));
                        command.Parameters.Add(new MySqlParameter("_idportal", idPortal));

                        connection.Open();

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                company.Id = reader.GetAsInt("idcompany");
                                company.PortalId = reader.GetAsShort("idportal");
                                company.Nit = reader.GetAsString("nit");
                                company.HasATS = reader.GetAsBoolean("has_ats");
                                company.CompanyUserDefault = reader.GetAsString("contactemail");
                            }
                        }
                    }
                }

                return company;
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "CompanyRecoverAndPersist", "GetCompanyByNit");
                return new CompanyEntity();
            }
        }

        public short GetRegisterAction(int idCompany, short idPortal)
        {
            try
            {
                short registerAction = 0;
                var query = @"SELECT RegisterAction 
                              FROM dtcompany 
                              WHERE IdCompany = @idCompany AND IdPortal = @idPortal";

                using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, idPortal)))
                {
                    using (MySqlCommand command = new MySqlCommand(query, connection))
                    {
                        command.CommandType = CommandType.Text;
                        command.Parameters.Add(new MySqlParameter("@idCompany", idCompany));
                        command.Parameters.Add(new MySqlParameter("@idPortal", idPortal));

                        try
                        {
                            connection.Open();

                            var result = command.ExecuteScalar();

                            if (result != null)
                            {
                                short.TryParse(result.ToString(), out registerAction);
                            }

                            return registerAction;
                        }
                        catch (Exception ex)
                        {
                            Trace.TraceError($"CompanyRecoverAndPersist - GetRegisterAction: {ex}");
                            _exceptionPublisherService.Publish(ex, "CompanyRecoverAndPersist", "GetRegisterAction", false, null, idPortal);
                            return registerAction;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "CompanyRecoverAndPersist", "GetRegisterAction");
                return 0;
            }
        }

        public CompanyHasAtsPersistentLayerDTO GetHasAts(CompanySearchSpecifications s)
        {
            var result = new CompanyHasAtsPersistentLayerDTO();

            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, s.PortalId)))
            {
                using (MySqlCommand command = new MySqlCommand("ae_company_has_ats_v2", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    command.Parameters.Add(new MySqlParameter("_idCompany", s.Id));

                    connection.Open();

                    using (var reader = command.ExecuteReader())
                    {
                        if (reader != null)
                        {
                            if (reader.Read())
                            {
                                try
                                {
                                    result.CompanyId = reader.GetAsInt("idcompany");
                                    result.HasATS = reader.GetAsBoolean("has_ats");   
                                    result.PortalId = reader.GetAsShort("idportal");
                                }
                                catch (Exception ex)
                                {
                                    Trace.TraceError($"CompanyRecoverAndPersist - GetHasAts ex: {ex.ToString()}");
                                    _exceptionPublisherService.Publish(ex, "CompanyRecoverAndPersist", "GetHasAts", false, null, s.PortalId);
                                    result = null;
                                }
                            }
                        }
                    }
                }
            }
            return result;
        }

        public string GetUrlRewrite(int idCompany)
        {
            string res = string.Empty;
            using (MySqlConnection conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
            {
                using (MySqlCommand command = new MySqlCommand("sp_dtcompany_SelectURLRewrite", conn))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    try
                    {
                        command.Parameters.Add(new MySqlParameter("_idcompany", idCompany));

                        conn.Open();

                        var obj = command.ExecuteScalar();
                        if (obj != null)
                        {
                            res = obj.ToString();
                        }
                    }
                    catch (Exception ex)
                    {
                        res = string.Empty;
                        Trace.TraceError($"CompanyFileRecoverAndPersist - GetURLRewrite ex: {ex.ToString()}");
                        _exceptionPublisherService.Publish(ex, "CompanyFileRecoverAndPersist", "GetURLRewrite");
                    }
                }
            }
            return res;
        }
    }
}
