using System;

namespace Redarbor.Company.Library.Entities
{
    [Serializable]
    public class CompanyGuIdControlEntity
    {
        public short PortalId { get; set; }
        public short IdApp { get; set; }
        public int CompanyId { get; set; }
        public string Uid { get; set; }
        public DateTime LastDate { get; set; }
        public string Ip { get; set; }
        public long IdUser { get; set; }
        public string UserAgent { get; set; }
        public CompanyGuIdControlEntity(short portalId, short idApp, int companyId, string uid, DateTime lastDate, string ip, long idUser, string userAgent)
        {
            PortalId = portalId;
            IdApp = idApp;
            CompanyId = companyId;
            Uid = uid;
            LastDate = lastDate;
            Ip = ip;
            IdUser = idUser;
            UserAgent = userAgent;
        }
    }
}
