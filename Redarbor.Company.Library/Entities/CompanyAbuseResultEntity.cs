using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Redarbor.Company.Library.Entities
{
    public class CompanyAbuseResultEntity
    {
        public int IdCompany { get; set; }
        public int IdUser { get; set; }
        public short IdPortal { get; set; }
        public string ClientIp { get; set; }
        public int TotalTry { get; set; }
        public int TotalSuccess { get; set; }
        public int TotalFail { get; set; }
        public int TotalShow { get; set; }
        public DateTime DateLastShow { get; set; }
    }
}
