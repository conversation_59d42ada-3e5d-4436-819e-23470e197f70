using Redarbor.Core.Cache.Contracts.ServiceLibrary;
using Redarbor.Core.Shared.Contracts.ServiceLibrary;
using Redarbor.CustomFolders.Contracts.ServiceLibrary;
using Redarbor.CustomFolders.Library.DomainServiceContracts;
using Redarbor.Extensions.Library.DI;
using Redarbor.Master.Entities.CustomFolder;
using Redarbor.Master.Entities.Enums;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Redarbor.CustomFolders.Impl.ServiceLibrary
{
    [RegisterStatefulService]
    public class CustomFolderService : ICustomFolderService
    {        
        private readonly ICustomFoldersRecoverAndPersist _customFoldersRecoverAndPersist;
        private readonly IEncryptionService _encryptionService;
        private readonly ITempCache _tempCache;
        const int MAX_LENGHT_NAME_FOLDER = 100;

        public CustomFolderService(ICustomFoldersRecoverAndPersist customFoldersRecoverAndPersist,
            IEncryptionService encryptionService,
            ITempCache tempCache)
        {
            _customFoldersRecoverAndPersist = customFoldersRecoverAndPersist;
            _encryptionService = encryptionService;
            _tempCache = tempCache;
        }

        public int GetMaxLenghtNameFolder()
        {
            return MAX_LENGHT_NAME_FOLDER;
        }

        public bool AddCustomFolder(CustomFoldersFilterEntity customFoldersFilter)
        {
            bool result = false;

            if ((customFoldersFilter.Environment == (short)CustomFoldersEnvironmentEnum.Matches
                && customFoldersFilter.IdOffer > 0)
                || (customFoldersFilter.Environment == (short)CustomFoldersEnvironmentEnum.CvBBDD && customFoldersFilter.IdUser > 0)
                && customFoldersFilter.IdPortal > 0
                && !string.IsNullOrEmpty(customFoldersFilter.Name)
                && customFoldersFilter.Position > 0)
            {
                result = _customFoldersRecoverAndPersist.AddCustomFolder(customFoldersFilter);

                if (result) {
                    _tempCache.Remove(GetCacheKeyCustomFolders(customFoldersFilter));
                }

            }
            return result;
        }

        public void AddCvToFolder(CustomFoldersFilterEntity customFoldersFilter)
        {
            if (customFoldersFilter.IdCv > 0
               && customFoldersFilter.Id > 0
               && customFoldersFilter.IdPortal > 0
               && customFoldersFilter.IdUser > 0)
            {
                _customFoldersRecoverAndPersist.AddCvToFolder(customFoldersFilter);
                
                _tempCache.Remove(GetCacheKeyCustomFolders(customFoldersFilter));                
                _tempCache.Remove(GetCacheKeyCvsInCustomFolders(customFoldersFilter));

                if (customFoldersFilter.FolderIdPrev > 0)
                {
                    customFoldersFilter.Id = customFoldersFilter.FolderIdPrev;                    
                    _tempCache.Remove(GetCacheKeyCvsInCustomFolders(customFoldersFilter));
                }
            }
        }        

        public bool DeleteCustomFolder(CustomFoldersFilterEntity customFoldersFilter)
        {
            if (customFoldersFilter.IdPortal > 0
                && customFoldersFilter.Id > 0)
            {
                if (_customFoldersRecoverAndPersist.DeleteCustomFolder(customFoldersFilter))
                {                      
                    _tempCache.Remove(GetCacheKeyCustomFolders(customFoldersFilter));
                    return true;
                }
            }
            return false;
        }

        public bool UpdateCustomFolder(CustomFoldersFilterEntity customFoldersFilter)
        {
            if (customFoldersFilter.Id > 0
                 && customFoldersFilter.IdPortal > 0
                 && !string.IsNullOrEmpty(customFoldersFilter.Name)
                 && (customFoldersFilter.Environment == (short)CustomFoldersEnvironmentEnum.Matches
                 && customFoldersFilter.IdOffer > 0
                 || customFoldersFilter.Environment == (short)CustomFoldersEnvironmentEnum.CvBBDD
                 && customFoldersFilter.IdUser > 0))
            {
                if(_customFoldersRecoverAndPersist.UpdateCustomFolder(customFoldersFilter))
                {                    
                    _tempCache.Remove(GetCacheKeyCustomFolders(customFoldersFilter));
                    return true;
                }
            }

            return false;
        }


        public void DeleteCustomFolderCVsInSearch(CustomFoldersFilterEntity customFoldersFilter)
        {
            if (customFoldersFilter.IdPortal > 0
              && customFoldersFilter.Id > 0 && customFoldersFilter.Environment > 0)
            {                
                _tempCache.Remove(GetCacheKeyCustomFolders(customFoldersFilter));
                _customFoldersRecoverAndPersist.DeleteCustomFolderCVsInSearch(customFoldersFilter);
            }
        }

        public bool DeleteCvsByFolder(CustomFoldersFilterEntity customFoldersFilter)
        {
            if (customFoldersFilter.Id > 0
                && customFoldersFilter.Environment == (short) CustomFoldersEnvironmentEnum.CvBBDD
               && !string.IsNullOrEmpty(customFoldersFilter.IdCvs)
               && customFoldersFilter.IdPortal > 0
               && customFoldersFilter.IdUser > 0)
            {

                if (_customFoldersRecoverAndPersist.DeleteCvsByFolder(customFoldersFilter))
                {                    
                    _tempCache.Remove(GetCacheKeyCustomFolders(customFoldersFilter));                    
                    _tempCache.Remove(GetCacheKeyCvsInCustomFolders(customFoldersFilter));

                    return true;
                }
            }

            return false;
        }

        public CustomFolderEntity GetCustomFolderByIdFolder(CustomFoldersFilterEntity customFoldersFilter)
        {
            var result = new CustomFolderEntity();

            if (customFoldersFilter.Id > 0 
                && customFoldersFilter.IdPortal > 0
                && (customFoldersFilter.IdOffer > 0 || customFoldersFilter.IdUser > 0))
                result = _customFoldersRecoverAndPersist.GetCustomFolderByIdFolder(customFoldersFilter);

            return result;
        }

        public List<CustomFolderEntity> GetCustomFolders(CustomFoldersFilterEntity customFoldersFilter)
        {
            List<CustomFolderEntity> result = new List<CustomFolderEntity>();

            if ((customFoldersFilter.Environment == (short)CustomFoldersEnvironmentEnum.Matches
                && customFoldersFilter.IdOffer > 0)
                || (customFoldersFilter.Environment == (short)CustomFoldersEnvironmentEnum.CvBBDD
                && customFoldersFilter.IdUser > 0)
                && customFoldersFilter.IdPortal > 0)
            {
                var key = GetCacheKeyCustomFolders(customFoldersFilter);

                result = _tempCache.Get<List<CustomFolderEntity>>(key);

                if (result == null || result.Count <= 0)
                {
                    if (customFoldersFilter.WithTotal)
                        result = _customFoldersRecoverAndPersist.GetCustomFoldersWithCount(customFoldersFilter);
                    else
                        result = _customFoldersRecoverAndPersist.GetCustomFolders(customFoldersFilter);

                    foreach (var item in result)
                    {
                        FillAdditionalInfo(item);
                    }

                    _tempCache.Add(key, result, new TimeSpan(0, 15, 0));
                }
                
            }
            return result;
        }        

        public List<int> GetCvsByFolderCustom(CustomFoldersFilterEntity customFolder)
        {
            var result = new List<int>();

            if (customFolder.Environment == (short)CustomFoldersEnvironmentEnum.CvBBDD
                && customFolder.Id > 0
                && customFolder.IdPortal > 0
                && customFolder.IdUser > 0)
            {
                var key = GetCacheKeyCvsInCustomFolders(customFolder);

                 result = _tempCache.Get<List<int>>(key);

                if (result == null || result.Count == 0)
                {
                    result = _customFoldersRecoverAndPersist.GetCvsByFolderCustom(customFolder);
                    _tempCache.Add(key, result, new TimeSpan(0, 10, 0));
                }
            }

            return result;
        }

        public List<int> GetCvsViewedsByIdcompany(int companyId, string cvs)
        {
            var result = new List<int>();

            if (!string.IsNullOrEmpty(cvs) && companyId > 0)
                result = _customFoldersRecoverAndPersist.GetCvsViewedsByIdcompany(companyId, cvs);

            return result;
        }

        public int GetFolderCustomByIdCv(CustomFoldersFilterEntity customFoldersFilter)
        {
            var result = 0;

            if (!string.IsNullOrEmpty(customFoldersFilter.IdCvs)
                && customFoldersFilter.IdPortal > 0
                && customFoldersFilter.IdUser > 0)
                result = _customFoldersRecoverAndPersist.GetFolderCustomByIdCv(customFoldersFilter);

            return result;
        }

        private string GetCacheKeyCustomFolders(CustomFoldersFilterEntity customFoldersFilter)
        {
            var customFolderKey = customFoldersFilter.WithTotal ? "CustomFolderCount" : "CustomFolder";
            return $"{customFolderKey}_idportal_{customFoldersFilter.IdPortal}_iduser_{customFoldersFilter.IdUser}_idoffer_{customFoldersFilter.IdOffer}_environment_{customFoldersFilter.Environment}";
        }

        private string GetCacheKeyCvsInCustomFolders(CustomFoldersFilterEntity customFoldersFilter)
        {
            return $"CvsInCustomFolder_idportal_{customFoldersFilter.IdPortal}_iduser_{customFoldersFilter.IdUser}_idoffer_{customFoldersFilter.IdOffer}_idfolder_{customFoldersFilter.Id}_environment_{customFoldersFilter.Environment}";
        }

        private void FillAdditionalInfo(CustomFolderEntity item)
        {
            item.IdEncrypted = _encryptionService.Encrypt((item.Id).ToString());
            item.PositionEncrypted = _encryptionService.Encrypt((item.Position).ToString());
        }


        private bool CanUpdateOrRemoveFolder(CustomFoldersFilterEntity customFoldersFilterEntity, int idFolder)
        {
            return GetCustomFolderByIdFolder(customFoldersFilterEntity).Id == idFolder;
        }

        public short GetResponseAddCvsFolderAndExecute(string idCvsEncrypted, string idFolderPreviewEncrypted, string idFolderNewEncrypted, int idCompany, int userId, short portalId, short maxFolders, short environtment, bool withTotal)
        {
            if (!string.IsNullOrEmpty(idCvsEncrypted)
                && !string.IsNullOrEmpty(idFolderNewEncrypted)
                && idCompany > 0
                && userId > 0
                && portalId > 0
                && maxFolders > 0
                && environtment > 0)
            {
                var cvs = DecryptIdCvs(idCvsEncrypted);
                int.TryParse(_encryptionService.Decrypt(idFolderPreviewEncrypted), out var idFolderPreview);
                int.TryParse(_encryptionService.Decrypt(idFolderNewEncrypted), out var idFolderNew);

                return CheckAndExecuteAddCvs(cvs, idFolderPreview, idFolderNew, idCompany, userId, portalId, maxFolders, environtment, withTotal);
            }

            return (short)(short)FolderCustomStatusResponseEnum.NOT_CAN_ADD_CVS;
        }

        private short CheckAndExecuteAddCvs(string cvs, int idFolderPreview, int idFolderNew, int idCompany, int userId, short portalId, short maxCvsByFolders, short environment, bool withTotal)
        {
            var cvsVieweds = GetCvsViewedsByIdcompany(idCompany, cvs);
            var result = (short)FolderCustomStatusResponseEnum.NOT_CAN_ADD_CVS;

            if (cvsVieweds.Any())
            {
                cvsVieweds = cvsVieweds.Distinct().ToList();

                int numCvsInFolder = GetCvsByFolderCustom(new CustomFoldersFilterEntity()
                {
                    Environment = environment,
                    IdPortal = portalId,
                    IdUser = userId,
                    Id = idFolderNew,
                    WithTotal = withTotal
                }).Count();

                if (numCvsInFolder >= maxCvsByFolders)
                    result = (short)FolderCustomStatusResponseEnum.PASS_LIMIT;
                else
                    result = InsertCvsToFilter(cvs, idFolderPreview, idFolderNew, userId, portalId, maxCvsByFolders, environment, cvsVieweds, numCvsInFolder, withTotal);
            }

            return result;
        }

        private short InsertCvsToFilter(string cvs, int idFolderPreview, int idFolderNew, int userId, short portalId, short maxCvsByFolders, short environment, List<int> cvsVieweds, int numCvsInFolder, bool withTotal)
        {
            var cvsToInsert = GetCvsNoExistInFolders(cvsVieweds, idFolderPreview, userId, idFolderNew, portalId, environment);

            if (cvsToInsert.Any())
            {
                if (maxCvsByFolders >= (numCvsInFolder + cvsToInsert.Count))
                {
                    foreach (var item in cvsToInsert)
                    {
                        AddCvToFolder(new CustomFoldersFilterEntity()
                        {
                            Environment = environment,
                            IdPortal = portalId,
                            IdUser = userId,
                            Id = idFolderNew,
                            FolderIdPrev = idFolderPreview,
                            IdCv = item,
                            WithTotal = withTotal
                        });
                    }

                    return (cvs.Split(',').ToList().Count > cvsToInsert.Count)
                        ? (short)FolderCustomStatusResponseEnum.NOTALLCVS
                        : (short)FolderCustomStatusResponseEnum.ALLCVS;
                }
                else
                {
                    return (short)FolderCustomStatusResponseEnum.PASS_LIMIT;
                }
            }

            return (short)FolderCustomStatusResponseEnum.EXIST_OR_NOT_VIEWED;
        }

        public List<int> GetCvsInOtherFolders(CustomFoldersFilterEntity customFoldersFilter)
        {
            if (customFoldersFilter.Environment == (short)CustomFoldersEnvironmentEnum.CvBBDD
                && !string.IsNullOrEmpty(customFoldersFilter.IdCvs)
                && customFoldersFilter.IdUser > 0
                && customFoldersFilter.IdPortal > 0)
            {
                return _customFoldersRecoverAndPersist.GetCvsInOtherFolders(customFoldersFilter);
            }

            return new List<int>();
        }

        private List<int> GetCvsNoExistInFolders(List<int> cvsVieweds, int idFolderPreview, int idUser, int idFolderNew, short portalId, short environment)
        {
            var result = new List<int>();

            if (idFolderPreview == 0)
            {
                var cvsListExist = GetCvsInOtherFolders(new CustomFoldersFilterEntity()
                {
                    Environment = environment,
                    IdPortal = portalId,
                    IdUser = idUser,
                    IdCvs = String.Join(", ", cvsVieweds)
                });

                if (cvsListExist.Any())
                {
                    cvsVieweds.ForEach(v =>
                    {
                        if (!cvsListExist.Exists(e => e == v))
                            result.Add(v);
                    });
                }
                else
                    result = cvsVieweds;
            }
            else
                result = cvsVieweds;

            return result;
        }

        private string DecryptIdCvs(string idcvs)
        {
            var result = string.Empty;
            var listEncrypted = idcvs.Split(',').ToList();

            foreach (var item in listEncrypted)
            {
                int.TryParse(_encryptionService.Decrypt(item), out var idcv);
                result = $"{result}, {idcv}";
            }

            return result.Trim(',');
        }

        public bool CheckAndAddCustomFolder(CustomFoldersFilterEntity customFoldersFilterEntity)
        {
            if (customFoldersFilterEntity.IdPortal > 0
                && !string.IsNullOrEmpty(customFoldersFilterEntity.Name)
                && (customFoldersFilterEntity.IdUser > 0 && customFoldersFilterEntity.Environment == (short)CustomFoldersEnvironmentEnum.CvBBDD)
                || customFoldersFilterEntity.IdOffer > 0 && customFoldersFilterEntity.Environment == (short)CustomFoldersEnvironmentEnum.Matches)
            {
                customFoldersFilterEntity.Position = (short)GetPostion(customFoldersFilterEntity);

                return CheckPosition(customFoldersFilterEntity, customFoldersFilterEntity.Position)
                        && AddCustomFolder(customFoldersFilterEntity);
            }

            return false;
        }

        private static bool CheckPosition(CustomFoldersFilterEntity customFoldersFilterEntity, int position)
        {
            return position > 0 && customFoldersFilterEntity.Environment == (short)CustomFoldersEnvironmentEnum.CvBBDD
                               || customFoldersFilterEntity.Environment == (short)CustomFoldersEnvironmentEnum.Matches
                               && (position == (short)CompanyCustomFoldersMatchEnum.Folder1)
                               || position == (short)CompanyCustomFoldersMatchEnum.Folder2;
        }

        private int GetPostion(CustomFoldersFilterEntity customFoldersFilterEntity)
        {
            var position = 0;

            if (customFoldersFilterEntity.Environment == (short)CustomFoldersEnvironmentEnum.Matches)
            {
                var customFolders = GetCustomFolders(new CustomFoldersFilterEntity()
                {
                    Environment = (short)CustomFoldersEnvironmentEnum.Matches,
                    IdOffer = customFoldersFilterEntity.IdOffer,
                    IdPortal = customFoldersFilterEntity.IdPortal,
                    WithTotal = customFoldersFilterEntity.WithTotal
                });

                if (customFolders.Any())
                {
                    if (customFolders.Count() != 2)
                    {
                        if (customFolders.Exists(c => c.Position == (short)CompanyCustomFoldersMatchEnum.Folder1))
                            position = (short)CompanyCustomFoldersMatchEnum.Folder2;

                        else if (customFolders.Exists(c => c.Position == (short)CompanyCustomFoldersMatchEnum.Folder2))
                            position = (short)CompanyCustomFoldersMatchEnum.Folder1;
                    }
                }
            }
            else if (customFoldersFilterEntity.Environment == (short)CustomFoldersEnvironmentEnum.CvBBDD)
            {
                var customFolders = GetCustomFolders(new CustomFoldersFilterEntity()
                {
                    Environment = (short)CustomFoldersEnvironmentEnum.CvBBDD,
                    IdUser = customFoldersFilterEntity.IdUser,
                    IdPortal = customFoldersFilterEntity.IdPortal,
                    WithTotal = customFoldersFilterEntity.WithTotal
                });

                position = customFolders.Any() ? customFolders.Max(c => c.Position) + 1 : 1;
            }

            return position;
        }

        public bool DeleteCustomCvsByFolderEncrypted(string idFolderEncrypted, string cvsEncrypted, int idUser, short portalId, bool withTotal)
        {
            if (!string.IsNullOrEmpty(idFolderEncrypted)
                && !string.IsNullOrEmpty(cvsEncrypted))
            {
                var cvs = DecryptIdCvs(cvsEncrypted);
                int.TryParse(_encryptionService.Decrypt(idFolderEncrypted), out var idFolder);

                return DeleteCvsByFolder(new CustomFoldersFilterEntity() {
                    Id = idFolder,
                    IdCvs = cvs,
                    IdUser = idUser,
                    IdPortal = portalId,
                    WithTotal = withTotal,
                    Environment  = (short) CustomFoldersEnvironmentEnum.CvBBDD });
            }

            return false;
        }

        public bool CheckAndUpdateCustomFolder(CustomFoldersFilterEntity customFoldersFilter, string idfd)
        {
            if (!string.IsNullOrEmpty(idfd)
                && customFoldersFilter.IdPortal > 0
                 && !string.IsNullOrEmpty(customFoldersFilter.Name)
                 && (customFoldersFilter.Environment == (short)CustomFoldersEnvironmentEnum.Matches
                 && customFoldersFilter.IdOffer > 0
                 || customFoldersFilter.Environment == (short)CustomFoldersEnvironmentEnum.CvBBDD
                 && customFoldersFilter.IdUser > 0))
            {
                int.TryParse(_encryptionService.Decrypt(idfd), out var idFolder);
                customFoldersFilter.Id = idFolder;

                if (idFolder > 0
                    && CanUpdateOrRemoveFolder(customFoldersFilter, idFolder))
                    return UpdateCustomFolder(customFoldersFilter);                
            }

            return false;
        }

        public bool CheckAndDeleteCustomFolder(CustomFoldersFilterEntity customFoldersFilterEntity, string idFolderEncrypted)
        {
            if (!string.IsNullOrEmpty(idFolderEncrypted)
                && customFoldersFilterEntity.IdPortal > 0
                && (customFoldersFilterEntity.Environment == (short)CustomFoldersEnvironmentEnum.CvBBDD
                && customFoldersFilterEntity.IdUser > 0
                || customFoldersFilterEntity.Environment == (short)CustomFoldersEnvironmentEnum.Matches
                && customFoldersFilterEntity.IdOffer > 0))
            {
                int idFolder = 0;
                int.TryParse(_encryptionService.Decrypt(idFolderEncrypted), out idFolder);
                customFoldersFilterEntity.Id = idFolder;

                if (idFolder > 0 
                    && CanUpdateOrRemoveFolder(customFoldersFilterEntity, idFolder))
                {
                    if (DeleteCustomFolder(customFoldersFilterEntity))
                    {
                        if(customFoldersFilterEntity.Environment == (short) CustomFoldersEnvironmentEnum.CvBBDD)
                            DeleteCustomFolderCVsInSearch(customFoldersFilterEntity);
                       
                        return true;
                    }
                }
            }

            return false;
        }
    }
}
