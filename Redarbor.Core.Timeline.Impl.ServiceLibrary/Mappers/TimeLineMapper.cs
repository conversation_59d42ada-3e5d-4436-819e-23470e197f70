using Redarbor.Core.Timeline.Contracts.ServiceLibrary.DTOs;
using Redarbor.Timeline.Candidate.Entities;
using Redarbor.Timeline.Consumer.Abstractions.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Redarbor.Core.Timeline.Impl.ServiceLibrary.Mappers
{
    internal static class TimeLineMapper
    {
        internal static TimelineMessageResponseDTO Map(this TimelineMessageGetResponseModel response)
        {
            var result = new TimelineMessageResponseDTO() { Success = response.Success(), Errors = response.Errors };
            if (response.TimelineMessages.Count() > 0)
            {
                string concatRouting = string.Join(";", response.TimelineMessages.Select(m => m.GetRouting()));
                string concatId = string.Join(";", response.TimelineMessages.Select(m => m.Id));
                result.Routing = concatRouting;
                result.ElasticId = concatId;
            }
            return result;
        }

        internal static CandidateTimelineMatchUpdatedMessageData Map(this MatchUpdatedMessageDataDTO d)
        {
            if (MatchUpdatedMessageDataDTOConditions(d))
            {
                return new CandidateTimelineMatchUpdatedMessageData(d.IdObject, d.MatchId, d.OfferId, d.OfferTitle, d.CompanyId, d.CompanyName, d.MatchStatusId, d.IdApp, d.DateInt, d.IdPortal, d.CityName);
            }

            return null;
        }

        private static bool MatchUpdatedMessageDataDTOConditions(MatchUpdatedMessageDataDTO d)
        {
            return d != null
                && d.IdObject > 0
                && d.OfferId > 0
                && d.MatchId > 0
                && d.CompanyId > 0
                && d.MatchStatusId > 0
                && !string.IsNullOrEmpty(d.CompanyName)
                && !string.IsNullOrEmpty(d.OfferTitle)
                && d.CityName != null;
        }
    }
}