using Dapper;
using Redarbor.MatchesSummary.Domain.Models;
using Redarbor.MatchesSummaryProcess.Application.Abstractions;
using Redarbor.Procs.Domain.Enums;
using System.Data;
using System.Threading.Tasks;

namespace Redarbor.MatchesSummaryProcess.Infrastructure.Repositories
{
    public class UserRepository : IUserRepository
    {
        private readonly IMasterConnectionsRepository _masterConnectionsRepository;

        public UserRepository(IMasterConnectionsRepository masterConnectionsRepository)
        {
            _masterConnectionsRepository = masterConnectionsRepository;
        }

        public async Task<User?> GetUserAsync(int userId, PortalEnum portal, int companyId)
        {
            using var masterConnection = await _masterConnectionsRepository.GetMasterConnectionAsync(portal);
            if (masterConnection.State != ConnectionState.Open) masterConnection.Open();
            var sql = @"SELECT * FROM `dtuser` WHERE `iduser` = @UserId AND `idportal` = @PortalId AND `idcompany` = @CompanyId";
            return await masterConnection.QuerySingleOrDefaultAsync<User>(sql, new { UserId = userId, PortalId = portal, CompanyId = companyId });
        }
    }
}
