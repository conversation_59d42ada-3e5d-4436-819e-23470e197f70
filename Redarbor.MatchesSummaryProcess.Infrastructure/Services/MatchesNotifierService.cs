using Microsoft.Extensions.Logging;
using Redarbor.MatchesSummary.Domain.Enums;
using Redarbor.MatchesSummary.Domain.Models;
using Redarbor.MatchesSummaryProcess.Application.Abstractions;
using Redarbor.MatchesSummaryProcess.Application.Models;
using Redarbor.Procs.Domain.Enums;
using Redarbor.RabbitMQ.Abstractions;
using Redarbor.RabbitMQ.Core;
using Redarbor.Tools.Exceptions.Consumer;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Redarbor.MatchesSummaryProcess.Infrastructure.Services
{
    public class MatchesNotifierService : IMatchesNotifierService
    {
        private readonly IExceptionsConsumerService _exceptionsConsumerService;
        private readonly ISubscriptionsRepository _subscriptionsRepository;
        private readonly ICandidatesRepository _candidatesRepository;
        private readonly ILogger<IMatchesNotifierService> _logger;
        private readonly IMatchesRepository _matchesRepository;
        private readonly ApplicationSettingsData _settingsData;
        private readonly ICompanyRepository _companyRepository;
        private readonly IOfferRepository _offerRepository;
        private readonly IUserRepository _userRepository;
        private readonly IKpiRepository _kpiRepository;
        private readonly IProducerRabbitMQ _rabbit;


        public MatchesNotifierService(
            IExceptionsConsumerService exceptionsConsumerService,
            ISubscriptionsRepository subscriptionsRepository,
            ICandidatesRepository candidatesRepository,
            ILogger<IMatchesNotifierService> logger,
            ApplicationSettingsData settingsData,
            ICompanyRepository companyRepository,
            IMatchesRepository matchesRepository,
            IOfferRepository offerRepository,
            IUserRepository userRepository,
            IKpiRepository kpiRepository)
        {
            _exceptionsConsumerService = exceptionsConsumerService;
            _subscriptionsRepository = subscriptionsRepository;
            _candidatesRepository = candidatesRepository;
            _matchesRepository = matchesRepository;
            _companyRepository = companyRepository;
            _offerRepository = offerRepository;
            _userRepository = userRepository;
            _kpiRepository = kpiRepository;
            _settingsData = settingsData;
            _logger = logger;

            RabbitMQBuilder.AddRedarborRabbitMQ(x => x.SetApiEndpoint(_settingsData.RabbitMqEndpoint));
            _rabbit = new ProducerRabbitMQBuilder().Create();
        }

        public async Task ProcessSubscriptionsAsync()
        {
            foreach (var portal in _settingsData.OperatingPortals.OrderByDescending(p => BaseMatchesSummary.GetNowAtPortal(p)))
            {
                await ProcessPortalNotificationsASync(portal);
            }
        }

        private async Task ProcessPortalNotificationsASync(PortalEnum portal)
        {
            var activeSubscriptions = (await _subscriptionsRepository.GetSubscriptionsAsync(portal)).ToList();

            if (!activeSubscriptions.Any())
            {
                _logger.LogDebug("No subscriptions found for offers in portal {portal}...", portal);
                return;
            }

            _logger.LogInformation("Got {count} offer matches notifications subscriptions in {portal}", activeSubscriptions.Count, portal);

            if (_settingsData.NotificationsSettings.TestConfiguration?.TestCompanies.ContainsKey(portal) == true)
            {
                var testSubscriptions = await GenerateSubscriptionsFromConfigurationAsync(portal);
                activeSubscriptions.AddRange(testSubscriptions.Where(x => !activeSubscriptions.Any(y => x.IdPortal == y.IdPortal
                    && x.IdCompany == y.IdCompany
                    && x.IdOffer == y.IdOffer)));
            }

            foreach (var n in _settingsData.NotificationsSettings.Notifications)
            {
                await ProcessNotificationsAsync(portal, activeSubscriptions, n);
            }
        }

        private async Task<IEnumerable<OfferMatchesSummarySubscription>> GenerateSubscriptionsFromConfigurationAsync(PortalEnum portal)
        {
            var subscriptions = new List<OfferMatchesSummarySubscription>();
            var companyIds = _settingsData.NotificationsSettings.TestConfiguration!.TestCompanies.GetValueOrDefault(portal);
            if (companyIds?.Any() != true)
            {
                return subscriptions;
            }

            var partialSubscriptionObjects = await _matchesRepository.GetGroupedMatchesAsSubcriptionsASync(portal, companyIds, GetDaysToSearchMatches(portal, MatchesSummaryNotificationType.Daily));

            foreach (var subscription in partialSubscriptionObjects)
            {
                subscription.IdStatus = SubscriptionStatus.Active;
                subscription.CreatedOn = DateTime.UtcNow;
                subscription.ModifiedOn = DateTime.UtcNow;
                subscription.Idtype = MatchesSummaryNotificationType.Daily;
                subscriptions.Add(subscription);
            }

            _logger.LogInformation("Overriding Database Settings. {n} subscriptions have been added for testing from appsettings", subscriptions.Count);
            return subscriptions;
        }

        private async Task ProcessNotificationsAsync(
            PortalEnum portal,
            ICollection<OfferMatchesSummarySubscription> activeSubscriptions,
            NotificationConfig notificationConfig)
        {
            if (BaseMatchesSummary.CanStartSendingNotificaionsOnPortal(portal, notificationConfig.Time, notificationConfig.Day))
            {
                _logger.LogInformation("Processing {type} offer matches notifications...", notificationConfig.Type);
                await ProcessNotificationsAsync(
                    activeSubscriptions
                    .Where(x => x.Idtype == notificationConfig.Type)
                    .ToList());
            }
        }

        private async Task ProcessNotificationsAsync(ICollection<OfferMatchesSummarySubscription> activeSubscriptions)
        {
            foreach (var subscriptionGroup in activeSubscriptions.GroupBy(x => (x.IdUser, x.IdCompany, x.IdPortal)))
            {
                var summaries = new List<BaseMatchesSummary>();
                foreach (var subscription in subscriptionGroup)
                {
                    try
                    {
                        var summary = await ProcessNotificationAsync(subscription);
                        if (summary is not null) summaries.Add(summary);
                    }
                    catch (Exception e)
                    {
                        _logger.LogError("An exception occured: {@error}", e);
                        await _exceptionsConsumerService.AddExceptionAsync(new Tools.Exceptions.Consumer.Models.ExceptionModel
                        {
                            Class = GetType().FullName,
                            Message = e.Message,
                            Method = nameof(ProcessNotificationsAsync),
                            ExceptionLevel = Tools.Exceptions.Consumer.Enums.ExceptionLevel.Error,
                            StackTrace = e.StackTrace,
                        });
                    }
                }

                if (!summaries.Any()) continue;

                var grouped = summaries.ToLookup(x => x.NotificationType);
                var company = await GetCompanyAsync(subscriptionGroup.Key.IdCompany, subscriptionGroup.Key.IdPortal);
                var contexts = await GetNotificationContextsAsync(subscriptionGroup, company.ComercialName);

                foreach (var context in contexts)
                {
                    foreach (var x in grouped.ToList())
                    {
                        await PackAndSendAsync(context, x.ToList());
                    }
                }
            }
        }

        private async Task<IEnumerable<MatchesNotificationContext>> GetNotificationContextsAsync(IGrouping<(int IdUser, int IdCompany, PortalEnum IdPortal), OfferMatchesSummarySubscription> subscriptionGroup, string comercialName)
        {
            var (idUser, idCompany, portal) = subscriptionGroup.Key;
            var user = await GetUserAsync(subscriptionGroup.Key.IdUser, subscriptionGroup.Key.IdPortal, subscriptionGroup.Key.IdCompany);

            var userEmail = user?.Email;
            IEnumerable<int> testCompanies = new List<int>();
            if (_settingsData.NotificationsSettings.TestConfiguration?.TestCompanies.TryGetValue(subscriptionGroup.Key.IdPortal, out testCompanies!) == true
                && testCompanies.Any(x => x == subscriptionGroup.Key.IdCompany)
                && _settingsData.NotificationsSettings.TestConfiguration.Recipients != null)
            {
                userEmail = _settingsData.NotificationsSettings.TestConfiguration.Recipients;

                _logger.LogInformation("Overriding userEmail due to configurationSetting for {email}, for company with id {companyId} in portal {portalId}",
                _settingsData.NotificationsSettings.TestConfiguration?.Recipients,
                idCompany,
                portal
                );
            }

            var emails = userEmail?.Split(';').Where(x => !string.IsNullOrWhiteSpace(x)).ToList();

            var contexts = new List<MatchesNotificationContext>();

            if (emails?.Any() == true)
            {
                contexts.AddRange(emails.Select(email => new MatchesNotificationContext
                {
                    EmailTo = email.Trim(),
                    UserName = user?.UserName ?? email.Trim(),
                    IdUser = user?.IdUser ?? default,
                    IdPortal = portal,
                    IdCompany = idCompany,
                    ComercialName = comercialName
                }));
            }

            return contexts;
        }

        private async Task<Company> GetCompanyAsync(int idCompany, PortalEnum idPortal)
        {
            _logger.LogInformation("Getting Company ...");
            var company = await _companyRepository.GetCompanyAsync(idCompany, idPortal);
            _logger.LogInformation("Got Company {id}", company.IdCompany);
            return company;
        }

        private async Task<User?> GetUserAsync(int idUser, PortalEnum idPortal, int idCompany)
        {
            _logger.LogInformation("Getting User ...");
            var user = await _userRepository.GetUserAsync(idUser, idPortal, idCompany);
            _logger.LogInformation("Got User {id}", user?.IdUser);
            return user;
        }

        private async Task PackAndSendAsync(MatchesNotificationContext notificationContext, ICollection<BaseMatchesSummary> summaries)
        {
            _logger.LogInformation("Sending notification to user with Id {userId} ...", notificationContext.IdUser);

            var notification = new MatchesNotification(summaries, notificationContext);

            await AddKpiAsync(notificationContext, notification.NotificationType);
            SendNotification(notification);
        }

        private async Task<BaseMatchesSummary?> ProcessNotificationAsync(OfferMatchesSummarySubscription subscription)
        {
            var pastDays = GetDaysToSearchMatches(subscription);

            _logger.LogInformation("Getting Offer ...");
            var offer = await _offerRepository.GetOfferAsync(subscription.IdOffer, subscription.IdPortal, subscription.IdCompany);
            _logger.LogInformation("Got Offer {id}", subscription.IdOffer);

            if (offer == null)
            {
                _logger.LogInformation("Offer {id} is not active. Skipping...", subscription.IdOffer);
                return null;
            }

            var matches = (await _matchesRepository.GetPendingOfferMatchesForOfferAsync(
                subscription.IdOffer,
                subscription.IdCompany,
                subscription.IdPortal,
                pastDays))
                .GroupBy(x => new { x.IdMatch, x.IdCandidate, x.IdPortal, x.IdCompany, x.IdOffer })
                .Select(x => x.First()).ToList();

            if (!matches.Any())
            {
                _logger.LogInformation("There are no new matches for offer {offerId}", subscription.IdOffer);
                return null;
            }

            _logger.LogInformation("Got {count} new matches for offer {id} and user {userId}", matches.Count, subscription.IdOffer, subscription.IdUser);

            var summary = subscription.Idtype switch
            {
                MatchesSummaryNotificationType.Daily => await GetDailyMatchesSummary(subscription, matches, offer),
                MatchesSummaryNotificationType.Weekly => await GetWeeklyMatchesSummary(subscription, matches, offer),
                _ => throw new NotImplementedException($"Notification rate {subscription.Idtype} is not expected.")
            };

            _logger.LogInformation("{@summary}", summary);
            if (await _matchesRepository.MarkAsSentAsync(
                subscription.IdPortal,
                subscription.IdCompany,
                subscription.IdOffer,
                matches.Select(x => x.IdMatch).ToList()))
            {
                _logger.LogInformation("Adding offer matches to notification to user with Id {userId} ...", subscription.IdUser);
                return summary;
            }
            else
            {
                _logger.LogInformation("Skipping offer matches from notification because the Matches have been discovered already.");
                return null;
            }
        }

        private static int[] GetDaysToSearchMatches(OfferMatchesSummarySubscription subscription)
        {
            return GetDaysToSearchMatches(subscription.IdPortal, subscription.Idtype);
        }

        private static int[] GetDaysToSearchMatches(PortalEnum portal, MatchesSummaryNotificationType type)
        {
            var nowAtPortal = BaseMatchesSummary.GetNowAtPortal(portal);

            var pastDays = type == MatchesSummaryNotificationType.Daily
                ? nowAtPortal.DayOfWeek switch
                {
                    DayOfWeek.Monday => new int[] {
                        GetDateInt(portal, -1),
                        GetDateInt(portal, -2),
                    },
                    _ => new int[] {
                        GetDateInt(portal, -1),
                    }
                }
                : type == MatchesSummaryNotificationType.Weekly
                    ? Enumerable.Range(1, 7).Select(x => GetDateInt(portal, -x)).ToArray()
                    : throw new NotImplementedException();

            return pastDays;
        }

        private static int GetDateInt(PortalEnum idPortal, int days)
        {
            return int.Parse(BaseMatchesSummary.GetNowAtPortal(idPortal).Date.AddDays(days).ToString("yyyyMMdd"));
        }

        private async Task AddKpiAsync(MatchesNotificationContext notificationContext, MatchesSummaryNotificationType notificationType)
        {

            await _kpiRepository.AddKpiAsync(notificationType switch
            {
                MatchesSummaryNotificationType.Daily => Kpi.DAILY_MATCHES_SUMMARY_NOTIFICATION,
                MatchesSummaryNotificationType.Weekly => Kpi.WEEKLY_MATCHES_SUMMARY_NOTIFICATION,
                _ => throw new NotImplementedException(),
            }, notificationContext.IdPortal, notificationContext.IdCompany);
        }


        private async Task<BaseMatchesSummary> GetWeeklyMatchesSummary(OfferMatchesSummarySubscription subscription, ICollection<Match> matches, Offer offer)
        {
            var summary = await GetDailyMatchesSummary(subscription, matches, offer); //// Read comment below ↓ 
            summary.NotificationType = MatchesSummaryNotificationType.Weekly;
            summary.UtmCampaign = _settingsData.NotificationsSettings.Notifications.First(x => x.Type == MatchesSummaryNotificationType.Weekly).UtmCampaign;
            return summary;
        }

        //// HEADSUP!!!
        //// This has been commented out because it is not used at the moment.
        //// Currently we are using the same template for both Daily and Weekly notifications.
        //// If we want to use different templates, we can uncomment this method and use it.
        //// But we need to take into account that the matches detail links cause the data to be too long for the column that saves them in the database.
        //// AND we'd need to change the database column to be able to save longer data
        ////
        //private async Task<BaseMatchesSummary> GetWeeklyMatchesSummary(OfferMatchesSummarySubscription subscription, ICollection<Match> matches)
        //{
        //    _logger.LogInformation("Getting Offer ...");
        //    var offer = await _offerRepository.GetOfferAsync(subscription.IdOffer, subscription.IdPortal, subscription.IdCompany);
        //    _logger.LogInformation("Got Offer {id}", offer.Id);

        //    var summary = new WeeklyMatchesSummary(
        //        offerTitle: offer.Title,
        //        userId: subscription.IdUser,
        //        portal: subscription.IdPortal,
        //        offerId: subscription.IdOffer,
        //        companyId: subscription.IdCompany,
        //        subscriptionId: subscription.Id,
        //        domainPrefix: _settingsData.DomainPrefix,
        //        newsletterId: _settingsData.NewsletterId,
        //        utmCampaign: _settingsData.NotificationsSettings.Notifications.First(x => x.Type == MatchesSummaryNotificationType.Weekly).UtmCampaign);

        //    var candidates = await _candidatesRepository.GetCandidatesWithIdsAsync(matches.Select(x => x.IdCandidate), subscription.IdPortal);
        //    summary.AddMatches(matches, candidates);

        //    return summary;
        //}

        private async Task<BaseMatchesSummary> GetDailyMatchesSummary(OfferMatchesSummarySubscription subscription, ICollection<Match> matches, Offer offer)
        {
            _logger.LogInformation("Creating Daily Matches Summary for Offer {offer}", subscription.IdOffer);

            _logger.LogInformation("Getting Total matches ...");
            var totalMatches = await _matchesRepository.GetOfferMatchesTotalASync(subscription.IdOffer, subscription.IdCompany, subscription.IdPortal);
            _logger.LogInformation("The Offer {id} has {totalMatches} matches.", offer.Id, totalMatches);

            return new DailyMatchesSummary(
                userId: subscription.IdUser,
                portal: subscription.IdPortal,
                offerId: subscription.IdOffer,
                companyId: subscription.IdCompany,
                offerTitle: offer.Title,
                newMatches: matches.Count,
                domainPrefix: _settingsData.DomainPrefix,
                newsletterId: _settingsData.NewsletterId,
                totalMatches: totalMatches,
                subscriptionId: subscription.Id,
                utmCampaign: _settingsData.NotificationsSettings.Notifications.First(x => x.Type == MatchesSummaryNotificationType.Daily).UtmCampaign);
        }

        private async void SendNotification(MatchesNotification notification)
        {
            var tcs = new TaskCompletionSource<bool>();
            var task = tcs.Task;

            await Task.Factory.StartNew(async () =>
            {
                try
                {
                    _rabbit.Publish(
                    notification,
                    new RabbitMQ.Model.ProducerDeliverEventArgs()
                    {
                        AppId = _settingsData.AppId,
                        QueueId = _settingsData.RabbitQueueId,
                    });
                    tcs.SetResult(true);
                }
                catch (Exception e)
                {

                    _logger.LogError("An exception occured: {@error}", e);
                    await _exceptionsConsumerService.AddExceptionAsync(new Tools.Exceptions.Consumer.Models.ExceptionModel
                    {
                        Class = GetType().FullName,
                        Message = e.Message,
                        Method = nameof(SendNotification),
                        ExceptionLevel = Tools.Exceptions.Consumer.Enums.ExceptionLevel.Error,
                        StackTrace = e.StackTrace,
                    });

                    tcs.SetResult(false);
                }
            });

            await task;
        }
    }
}
