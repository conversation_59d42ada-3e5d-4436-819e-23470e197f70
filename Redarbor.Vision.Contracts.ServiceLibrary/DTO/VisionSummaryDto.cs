using System.Collections.Generic;

namespace Redarbor.Vision.Contracts.ServiceLibrary.DTO
{
    public class VisionSummaryDTO
    {
        public string CompanyComercialName { get; set; } = string.Empty;
        public string GeneralValuationScoreStars { get; set; } = string.Empty;
        public string RecomendedPercent { get; set; } = string.Empty;
        public string CompanyFollowers { get; set; } = string.Empty;
        public string TotalValuations { get; set; } = string.Empty;
        public string CEOScoreValuation { get; set; } = string.Empty;
        public string TotalCEOValuations { get; set; } = string.Empty;
        public string RecommendationsNumber { get; set; } = string.Empty;
        public string TotalCompanyViews { get; set; } = string.Empty;
        public string CurrentPosition { get; set; } = string.Empty;
        public string TotalCompaniesRanking { get; set; } = string.Empty;
        public string CurrMovPos { get; set; } = string.Empty;
        public string CompanyLastUpdate { get; set; } = string.Empty;
        public string CompanyPhotosLit { get; set; } = string.Empty;
        public string CompanyBenefitsLit { get; set; } = string.Empty;
        public string TotalBenefitsLit { get; set; } = string.Empty;
        public string BenefitPercentLit { get; set; } = string.Empty;
        public string TotalAnsweredComments { get; set; } = string.Empty;
        public string TotalCommentsLit { get; set; } = string.Empty;
        public string UnansweredCommentsLit { get; set; } = string.Empty;
        public string CommentPercentLit { get; set; } = string.Empty;
        public string GeneralEvaluationScore { get; set; } = string.Empty;
        public string GeneralEvaluationScoreVariation { get; set; } = string.Empty;
        public string EnvironmentAverageDetail { get; set; } = string.Empty;
        public string EnvironmentAverageDetailVariation { get; set; } = string.Empty;
        public string SalaryAverageDetail { get; set; } = string.Empty;
        public string SalaryAverageDetailVariation { get; set; } = string.Empty;
        public string OportunitiesAverageDetail { get; set; } = string.Empty;
        public string OportunitiesAverageDetailVariation { get; set; } = string.Empty;
        public string PhDoughnutClass { get; set; } = string.Empty;
        public string GeneralStarsWidth { get; set; } = string.Empty;
        public string CeoStarsWidth { get; set; } = string.Empty;
        public List<string> LblGraf = new List<string>();
        public List<string> ValuesGraf = new List<string>();
        public string AboutCompanyGrafHClass { get; set; } = string.Empty;
        public string PhotosGrafHClass { get; set; } = string.Empty;
        public string BenefitsGrafHClass { get; set; } = string.Empty;
        public string CommentsGrafHClass { get; set; } = string.Empty;
        public bool HaveCompanyViews { get; set; } = false;
        public bool PhRankingDetail { get; set; }
        public bool PhTypeRankAsc { get; set; }
        public bool PhTypeRankIg { get; set; }
        public bool PhTypeRankDesc { get; set; }
        public bool PhRankingNoData { get; set; }
        public bool HaveAttractivenessData { get; set; }
        public bool PhProgressCompanyInfoGreen { get; set; }
        public bool PhProgressCompanyInfoGray { get; set; }
        public bool PhProgressCompanyPhotosGreen { get; set; }
        public bool PhProgressCompanyPhotosGray { get; set; }
        public bool PhBenefitsIconValGreenOk { get; set; }
        public bool PhBenefitsIconValGrayOk { get; set; }
        public bool PhUnansweredComments { get; set; }
        public bool PhCommentIconValGrayOk { get; set; }
        public bool PhCommentIconValGreenOk { get; set; }
        public string DoughnutCircle { get; set; } = string.Empty;
        public string MovPos { get; set; } = string.Empty;
        public string AttractivenessIndex { get; set; } = string.Empty;
        public string AttractivenessIndexCSSClass { get; set; } = string.Empty;
        public string InfoPercentWith { get; set; } = string.Empty;
        public int MiniumPhotosCompanyMonth { get; set; }
        public string PhotosPercentWith { get; set; } = string.Empty;
        public string BenefitsPercentWith { get; set; } = string.Empty;
        public string CommentPercentWith { get; set; } = string.Empty;
        public string GeneralEvaluationScorePosition { get; set; } = string.Empty;
        public string GeneralEvaluationScoreIcon { get; set; } = string.Empty;
        public string IcoEnviomentPos { get; set; } = string.Empty;
        public string IcoEnvioment { get; set; } = string.Empty;
        public string IcoSalaryPos { get; set; } = string.Empty;
        public string IcoSalary { get; set; } = string.Empty;
        public string IcoOportunitiesPos { get; set; } = string.Empty;
        public string IcoOportunities { get; set; } = string.Empty;
        public string NewsInMonth { get; set; }
        public string TotalNewsInMonth { get; set; } = "2";
        public int NewsBarWidth { get; set; }
        
        public int TotalUnanswered { get;set; }
    }
}
