using MySql.Data.MySqlClient;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Core.Mailing.Library.DomainServiceContracts;
using Redarbor.Core.Resolver.Contracts.ServiceLibrary;
using Redarbor.Core.Resolver.Contracts.ServiceLibrary.Enums;
using Redarbor.Master.Entities.Mailing;
using System;
using System.Data;

namespace Redarbor.Core.Mailing.Library.DomainServicesImplementations
{
    public class MailingRecoverAndPersist : IMailingRecoverAndPersist
    {
        private readonly IConnectionStringResolverService _connectionStringResolverService;
        private readonly IExceptionPublisherService _exceptionPublisherService;

        public MailingRecoverAndPersist(IConnectionStringResolverService connectionStringResolverService,
            IExceptionPublisherService exceptionPublisherService)
        {
            _connectionStringResolverService = connectionStringResolverService;
            _exceptionPublisherService = exceptionPublisherService;
        }

        public int Add(MailingNotification mailingNotification)
        {
            var id = 0;

            using (var conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.MasterAux, mailingNotification.PortalId)))
            {
                using (var command = new MySqlCommand("sp_dtemailnotification_Insert_V3", conn))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    try
                    {
                        command.Parameters.Add(new MySqlParameter("_idportal", mailingNotification.PortalId));
                        command.Parameters.Add(new MySqlParameter("_idcompany", mailingNotification.CompanyId));
                        command.Parameters.Add(new MySqlParameter("_idoffer", mailingNotification.OfferId));
                        command.Parameters.Add(new MySqlParameter("_idcandidate", mailingNotification.CandidateId));
                        command.Parameters.Add(new MySqlParameter("_iduser", mailingNotification.UserId));
                        command.Parameters.Add(new MySqlParameter("_idmailing", mailingNotification.MailingId));
                        command.Parameters.Add(new MySqlParameter("_idoperacioncompra", mailingNotification.PurchaseOperationId));
                        command.Parameters.Add(new MySqlParameter("_notificationstatus", mailingNotification.NotificationStatus));
                        command.Parameters.Add(new MySqlParameter("_priority", mailingNotification.Priority));
                        command.Parameters.Add(new MySqlParameter("_idnewsletter", mailingNotification.NewsletterId));
                        command.Parameters.Add(new MySqlParameter("_idnotificationtype", mailingNotification.NotificationTypeId));
                        command.Parameters.Add(new MySqlParameter("_contactemailfrom", mailingNotification.ContactEmailFrom));
                        command.Parameters.Add(new MySqlParameter("_contactemailbody", mailingNotification.ContactEmailBody));
                        command.Parameters.Add(new MySqlParameter("_contactemailsubject", mailingNotification.ContactEmailSubject));
                        command.Parameters.Add(new MySqlParameter("_contactemailip", mailingNotification.ContactEmailIp));
                        command.Parameters.Add(new MySqlParameter("_contactemailcountry", mailingNotification.ContactEmailCountry));
                        command.Parameters.Add(new MySqlParameter("_fechaenvio", mailingNotification.SendingDate));
                        command.Parameters.Add(new MySqlParameter("_fechalectura", mailingNotification.ReadingDate));
                        command.Parameters.Add(new MySqlParameter("_idstatus", mailingNotification.StatusId));
                        command.Parameters.Add(new MySqlParameter("_nombre", mailingNotification.Name));
                        command.Parameters.Add(new MySqlParameter("_emailto", mailingNotification.EmailTo));
                        command.Parameters.Add(new MySqlParameter("_iduserencriptado", mailingNotification.EncryptedUserId));
                        command.Parameters.Add(new MySqlParameter("_claveencriptada", mailingNotification.EncryptedKey));
                        command.Parameters.Add(new MySqlParameter("_nombreoferta", mailingNotification.OfferName));
                        command.Parameters.Add(new MySqlParameter("_createdby", mailingNotification.CreatedBy));
                        command.Parameters.Add(new MySqlParameter("_id_cv_download", mailingNotification.DownloadCvId));
                        command.Parameters.Add(new MySqlParameter("_numoffers", mailingNotification.OffersNum));
                        command.Parameters.Add(new MySqlParameter("_grapestoken", mailingNotification.GrapesToken));
                        conn.Open();

                        var result = command.ExecuteScalar();

                        if (result != null)
                        {
                            int.TryParse(result.ToString(), out id);
                        }
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "MailingRecoverAndPersist", "Add", false, null, mailingNotification.PortalId);
                    }
                }
            }

            return id;
        }

        public int AddOnlineNotificationStack(MailingNotification mailingNotification)
        {
            var id = 0;

            using (var conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.MasterAux, mailingNotification.PortalId)))
            {
                using (var command = new MySqlCommand("ae_Insert_OnlineNotificationStack", conn))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    try
                    {
                        command.Parameters.Add(new MySqlParameter("_idportal", mailingNotification.PortalId));
                        command.Parameters.Add(new MySqlParameter("_idcompany", mailingNotification.CompanyId));
                        command.Parameters.Add(new MySqlParameter("_idoffer", mailingNotification.OfferId));
                        command.Parameters.Add(new MySqlParameter("_idmatch", mailingNotification.MatchId));
                        command.Parameters.Add(new MySqlParameter("_idcandidate", mailingNotification.CandidateId));
                        command.Parameters.Add(new MySqlParameter("_iduser", mailingNotification.UserId));
                        command.Parameters.Add(new MySqlParameter("_notificationstatus", mailingNotification.NotificationStatus));
                        command.Parameters.Add(new MySqlParameter("_idnewsletter", mailingNotification.NewsletterId));
                        command.Parameters.Add(new MySqlParameter("_emailto", mailingNotification.EmailTo));
                        command.Parameters.Add(new MySqlParameter("_nombreoferta", mailingNotification.OfferName));
                        conn.Open();

                        var result = command.ExecuteScalar();

                        if (result != null)
                        {
                            int.TryParse(result.ToString(), out id);
                        }
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "MailingRecoverAndPersist", "Add", false, null, mailingNotification.PortalId);
                    }
                }
            }

            return id;
        }

        public void AddMatchNotification(MobileMatchNotificationEntity notification)
        {
            using (var conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Data, (short) notification.PortalId)))
            {
                using (var command = new MySqlCommand("ae_match_notification_insert_v2", conn))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    try
                    {
                        command.Parameters.Add(new MySqlParameter("_device_token", notification.DeviceToken));
                        command.Parameters.Add(new MySqlParameter("_user_id", notification.UserId));
                        command.Parameters.Add(new MySqlParameter("_candidate_id", notification.CandidateId));
                        command.Parameters.Add(new MySqlParameter("_match_id", notification.MatchId));
                        command.Parameters.Add(new MySqlParameter("_offer_id", notification.OfferId));
                        command.Parameters.Add(new MySqlParameter("_offer_title", notification.OfferTitle));
                        command.Parameters.Add(new MySqlParameter("_process_status_id", notification.ProcessStatusId));
                        command.Parameters.Add(new MySqlParameter("_notification_status_id", notification.StatusId));
                        command.Parameters.Add(new MySqlParameter("_portal_id", notification.PortalId));
                        command.Parameters.Add(new MySqlParameter("_app_id", notification.AppId));
                        command.Parameters.Add(new MySqlParameter("_device_type_id", notification.DeviceTypeId));

                        conn.Open();
                        command.ExecuteNonQuery();
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "MailingRecoverAndPersist", "AddMatchNotification",false, null, (short) notification.PortalId);
                    }
                }
            }

        }
    }
}
