using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Redarbor.EmailSender.Contracts.ServiceLibrary.DTO
{
    [Serializable]
    public class EmailSenderDTO
    {
        public string Host { get; set; } = string.Empty;
        public int Port { get; set; }
        public string User { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        public string From { get; set; } = string.Empty;
        public string To { get; set; } = string.Empty;
        public string Subject { get; set; } = string.Empty;
        public string Body {  get; set; } = string.Empty;
        public string Alias { get; set; } = string.Empty;

    }
}
