using Redarbor.Company.OCC.WebUI.Models.Landings;

namespace Redarbor.Company.OCC.WebUI.Models.SimplifiedRegister
{
    public class SimplifiedRegisterDataModel
    {
        public LoginDataModel LoginDataModel { get; set; } = new LoginDataModel();
        public RegisterDataModel RegisterDataModel { get; set; }
        public bool IsCheckedAdditionalConditions { get; set; }
		public ValidateLoginDataModel ValidateLoginDataModel { get; set; }
        public LandingInfoRegisterDataModel LandingInfoRegisterDataModel { get; set; } = new LandingInfoRegisterDataModel();    
        public bool OriginIsSemLanding { get; set; } = false;
        public PreRegisterDataModel PreRegisterDataModel { get; set; } = new PreRegisterDataModel();
    }
}