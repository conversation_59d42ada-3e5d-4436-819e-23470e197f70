using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Web.Mvc;

namespace Redarbor.Company.OCC.WebUI.Models.SimplifiedRegister
{
    public class PreRegisterDataModel
    {
        public string Nit { get; set; } = string.Empty;
        public string CompanySocial { get; set; } = string.Empty;
        public string ComercialName { get; set; } = string.Empty;
        public short CountryId { get; set; }
        public short LocalizationId { get; set; }
        public short CityId { get; set; }
        public int? PostalCodeId { get; set; }
        public string Address { get; set; } = string.Empty;
        public short IndustryId { get; set; }
        public short VacanciesPerYearId { get; set; }
        public short NumberEmployeesId { get; set; }
        public int CourtesyProductsId { get; set; }
        public int CourtesyProductsTemporalityId { get; set; }
        public string Email { get; set; } = string.Empty;
        [Required(ErrorMessage = "LIT_REQUIRED")]
        [StringLength(50, MinimumLength = 4, ErrorMessage = "LIT_ERR_LENGTH_BETWEEN_4_50")]
        public string Password { get; set; } = string.Empty;
        public bool ShowRegisterPassword { get; set; } = false;
        public string CompanyPhone { get; set; } = string.Empty;
        public string ContactFullName { get; set; } = string.Empty;
        public int UserId { get; set; }
        public string CompanyIdPreRegisterEncrypt { get; set; }
        public int CompanyIdPreRegister { get; set; }
        public string IdAccount { get; set; } = string.Empty;
        public string IdSalesForceUser { get; set; } = string.Empty;
        public short StatusImportOffer { get; set; }
        public DateTime? SendEmailMailPublishLastOffer { get; set; }
        public DateTime? WhenSendMailReminderImportOffer { get; set; }
        public short CountTimesSendMailReminderImportOffer { get; set; }
        public int IdCompany { get; set; }
    }
}