using Redarbor.Common.Entities.Constants;
using System.ComponentModel.DataAnnotations;

namespace Redarbor.Company.OCC.WebUI.Models
{
    public class LoginDataModel
    {
        [Required(ErrorMessage = "LIT_ERR_USER_REQUIRED")]
        [RegularExpression(RegularExpressions.EMAIL_REGEX, ErrorMessage = "LIT_ERR_EMAIL_INVALID")]
        [StringLength(50, ErrorMessage = "LIT_ERR_LENGTH_MAX_50")]
        public string UserName { get; set; }
        [Required(ErrorMessage = "LIT_ERR_PASSWORD_REQUIRED")]
        [StringLength(20, MinimumLength = 4, ErrorMessage = "LIT_ERR_LENGTH_4_20")]
        public string Password { get; set; }
        public bool KeepMeLoggedIn { get; set; } = true;
        public bool ShowMessageUserVerified { get; set; } = false;
        public string IsFromMailPreRegisterEncrypted { get; set; } = string.Empty;    
	}
}