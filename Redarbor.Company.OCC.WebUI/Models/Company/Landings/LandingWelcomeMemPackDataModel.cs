using System.Collections.Generic;
using Redarbor.Company.OCC.WebUI.Helpers;
using Redarbor.Company.OCC.WebUI.Models.Company.EmailLink;
using Redarbor.Company.OCC.WebUI.Models.Company.Product;

namespace Redarbor.Company.OCC.WebUI.Models.Company.Landings
{
    public class LandingWelcomeMemPackDataModel
    {
        public List<LandingProductDataModel> ListLandingProductDataModel { get; set; } = new List<LandingProductDataModel>();
        public int AvailablesServiceBasic { get; set; }
        public int IdCompany { get; set; }
        public string GtmUniqueConversionId
        {
            get { return EncryptationHelper.Encrypt(IdCompany.ToString()); }
        }


        public VerificationByCodeModel VerificationByCodeModel { get; set; }
    }
}