using Redarbor.Company.OCC.WebUI.Validation.Attributes;
using System;
using System.Collections.Generic;

namespace Redarbor.Company.OCC.WebUI.Models.Company.Offer
{
    public class KillerQuestionDataModel
    {
        public int Id { get; set; }
        public string IdEncrypted { get; set; } = string.Empty;
        public int OfferId { get; set; }
        public string OfferIdEncrypted { get; set; } = string.Empty;
        public int CompanyId { get; set; }
        public short PortalId { get; set; }
        public short Type { get; set; }
        public string TypeDesc { get; set; }
        public short IdStatus { get; set; }
        [NoEmojisAllowed(ErrorMessage = "LIT_ERROR_INSERT_EMOJIS")]
        [NoUrAllowed(ErrorMessage = "LIT_ERROR_INSERT_URL")]
		[NoSurrogateTextAllowed(ErrorMessage = "LIT_TEXT_STYLE_ERROR")]
		public string Title { get; set; } = string.Empty;
        [NoEmojisAllowed(ErrorMessage = "LIT_ERROR_INSERT_EMOJIS")]
        [NoUrAllowed(ErrorMessage = "LIT_ERROR_INSERT_URL")]
		[NoSurrogateTextAllowed(ErrorMessage = "LIT_TEXT_STYLE_ERROR")]
		public string DuplicateOfferTitle { get; set; } = string.Empty;
        public string Answer { get; set; } = string.Empty;
        public short Order { get; set; }
        public DateTime CreatedOn { get; set; } = DateTime.MinValue;
        public DateTime UpdatedOn { get; set; } = DateTime.MinValue;
        public short Score { get; set; }
        public short ScoreMax { get; set; }
        public List<KillerQuestionDataDataModel> KillerData { get; set; } = new List<KillerQuestionDataDataModel>();
        public DateTime DeletedOn { get; set; } = DateTime.MinValue;
        public short Action { get; set; }
        public short AutoExclude { get; set; }

        public int Position { get; set; } = 0;
        public bool IsNew => string.IsNullOrEmpty(IdEncrypted);
       
    }
}