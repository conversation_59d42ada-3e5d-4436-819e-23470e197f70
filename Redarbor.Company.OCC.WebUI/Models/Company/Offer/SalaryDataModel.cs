using Redarbor.Company.OCC.WebUI.Validation.Attributes;
using System.ComponentModel.DataAnnotations;

namespace Redarbor.Company.OCC.WebUI.Models.Company.Offer
{
    public class SalaryDataModel
    {
       /* [Required(ErrorMessage = "REQUIRED_FIELD")]
        [MinMaxSalary(ErrorMessage = "ALERT_SALARY_RANG")]
        public decimal Minimum { get; set; }

        [Required(ErrorMessage = "REQUIRED_FIELD")]
        [MinMaxSalary(ErrorMessage = "ALERT_SALARY_RANG")]
        public decimal Maximum { get; set; }

        public bool HideSalary { get; set; }

        public bool IncludeComission { get; set; }*/

    }
}