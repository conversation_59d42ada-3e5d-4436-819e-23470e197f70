using System;
using System.Collections.Generic;

namespace Redarbor.Company.OCC.WebUI.Models.Company.Product
{
    public class CompanyProductDataModel
    {
        public List<CompanyProductFeatureDataModel> Features { get; set; } = new List<CompanyProductFeatureDataModel>();
        public int ProductId { get; set; }
        public int Id { get; set; }
        public int IdCompany { get; set; }
        public short PortalId { get; set; }
        public DateTime DateAdd { get; set; } = DateTime.MinValue;
        public string ClientIP { get; set; } = string.Empty;
        public short GroupId { get; set; }
        public short StatusId { get; set; }
        public int ExpirationDays { get; set; }
        public DateTime DateActivation { get; set; } = DateTime.MinValue;
        public DateTime DateExpiration { get; set; } = DateTime.MinValue;
        public short PaymentOriginId { get; set; }
        public short SourceId { get; set; }
        public DateTime DateMod { get; set; }
        public int IdUser { get; set; }
        public float Price { get; set; }
    }
}