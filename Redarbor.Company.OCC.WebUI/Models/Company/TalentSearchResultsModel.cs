using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using Redarbor.Company.OCC.WebUI.Models.Company.Candidate;
using Redarbor.Core.Elastic.Library;

namespace Redarbor.Company.OCC.WebUI.Models.Company;

[Serializable]
public class TalentSearchResultsModel
{
    public string Query { get; set; }
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 20;
    public int TotalResults { get; set; }
    public Dictionary<string, List<FacetResult>> Facets { get; set; }
    public List<CandidateReadSearchDataModel> Candidates { get; set; } = new List<CandidateReadSearchDataModel>();
    public int? SelectedLocationId { get; set; }
    public int? SelectedCategoryId { get; set; }
    public string? SelectedSalaryRange { get; set; }
    public List<SelectListItem> LocationFilters { get; set; } = new();
    public List<SelectListItem> SalaryFilters { get; set; } = new();
    public List<SelectListItem> CategoryFilters { get; set; } = new();
    public string SelectedOrder { get; set; } = "relevance";
    public List<SelectListItem> OrderOptions { get; set; } = new();
    public List<SelectListItem> ResultsPerPageOptions { get; set; } = new();

    // Pagination data for shared Pager template
    public Models.PagerDataModel PagerData { get; set; } = new();

    // Literal properties for UI text
    public string SearchTextLabel { get; set; } = "¿Qué tipo de talento buscas?";
    public string SearchTextPlaceholder { get; set; } = "Puesto, palabra clave, frase exacta";
    public string ClearFiltersButton { get; set; } = "Limpiar filtros";
    public string LocationFilterLabel { get; set; } = "Localidad";
    public string SalaryFilterLabel { get; set; } = "Sueldo";
    public string CategoryFilterLabel { get; set; } = "Áreas de experiencia";
    public string LanguagesFilterLabel { get; set; } = "Idiomas";
    public string EducationLevelFilterLabel { get; set; } = "Nivel académico";
    public string UpdateDateFilterLabel { get; set; } = "Fecha de actualización";
    public string ResultsPerPageLabel { get; set; } = "Resultados por página";
    public string SortByRelevanceText { get; set; } = "Ordenar por Relevancia";
    public string SortRelevanceText { get; set; } = "Relevancia";
    public string SortMostRecentText { get; set; } = "Más recientes";
    public string SortLeastRecentText { get; set; } = "Menos recientes";
    public string SortLocationAZText { get; set; } = "Localidad A-Z";
    public string SortLocationZAText { get; set; } = "Localidad Z-A";
    public string SortLowestSalaryText { get; set; } = "Menor salario";
    public string SortHighestSalaryText { get; set; } = "Mayor salario";
    public string PaginationPreviousText { get; set; } = "Previo";
    public string PaginationNextText { get; set; } = "Siguiente";
    public string DateFilter6MonthsText { get; set; } = "Hace 6 meses";
    public string DateFilter2MonthsText { get; set; } = "Hace 2 meses";
    public string DateFilter1MonthText { get; set; } = "Hace 1 mes";
    public string DateFilter15DaysText { get; set; } = "Hace 15 días";
    public string DateFilter5DaysText { get; set; } = "Hace 5 días";
    public string DateFilterTodayText { get; set; } = "Hoy";
    public string DateFilterAllText { get; set; } = "Todos";
    public string CandidateViewedStatusText { get; set; } = "Visualizado";
    public string SalaryNotSpecifiedText { get; set; } = "No especificado";
    public string ResultsCountOfText { get; set; } = "de";
    public string ShowMoreOptionsText { get; set; } = "Ver más";
    public string HideMoreOptionsText { get; set; } = "Ver menos";
    public string GeneralErrorMessage { get; set; } = "¡Algo sucedió! Por favor inténtalo de nuevo.";

    /// <summary>
    /// Gets location filters with their candidate counts from facets
    /// </summary>
    public List<LocationFilterWithCount> LocationFiltersWithCounts
    {
        get
        {
            var result = new List<LocationFilterWithCount>();
            
            // Defensive check for LocationFilters
            if (LocationFilters == null || LocationFilters.Count == 0) 
                return result;

            try
            {
                // Get localization facets with defensive checks
                List<FacetResult> locationFacets = null;
                if (Facets != null && Facets.ContainsKey("localization") && Facets["localization"] != null)
                {
                    locationFacets = Facets["localization"];
                }

                foreach (var locationFilter in LocationFilters)
                {
                    // Defensive checks for locationFilter
                    if (locationFilter == null || string.IsNullOrEmpty(locationFilter.Value) || string.IsNullOrEmpty(locationFilter.Text))
                        continue;

                    var count = 0;
                    if (locationFacets != null && locationFacets.Count > 0)
                    {
                        var facet = locationFacets.FirstOrDefault(f => f != null && f.Key == locationFilter.Value);
                        count = facet?.Count ?? 0;
                    }

                    result.Add(new LocationFilterWithCount
                    {
                        Id = locationFilter.Value ?? string.Empty,
                        Name = locationFilter.Text ?? string.Empty,
                        Count = Math.Max(0, count) // Ensure count is never negative
                    });
                }

                // Sort by count descending, then by name for consistent ordering
                return result.OrderByDescending(x => x.Count).ThenBy(x => x.Name).ToList();
            }
            catch (Exception)
            {
                // If anything goes wrong, return empty list to prevent view crashes
                return new List<LocationFilterWithCount>();
            }
        }
    }

    /// <summary>
    /// Initializes the PagerData property based on current pagination values
    /// </summary>
    public void InitializePagerData()
    {
        PagerData = new Models.PagerDataModel
        {
            TotalRows = TotalResults,
            PageSelected = PageNumber,
            PageSizeSelected = PageSize
        };
    }
}

/// <summary>
/// Represents a location filter option with candidate count
/// </summary>
[Serializable]
public class LocationFilterWithCount
{
    public string Id { get; set; }
    public string Name { get; set; }
    public int Count { get; set; }
}