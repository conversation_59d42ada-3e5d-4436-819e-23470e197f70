using Redarbor.Common.Entities.Constants;
using Redarbor.Company.OCC.WebUI.Models.Company.Cart.Payments;
using Redarbor.Company.OCC.WebUI.Validation.Attributes;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Web.Mvc;
namespace Redarbor.Company.OCC.WebUI.Models.Company.Cart
{
    public class EditDataCompanyCartDataModel
    {

        public string LitPrice { get; set; }
        public string LitPriceWithVAT { get; set; }
        public string LitVatLiteral { get; set; }
        public string LitVatNumber { get; set; }
        public bool PriceWithoutTaxes { get; set; } = false;
        public bool PriceWithTaxes { get; set; } = false;
        public string InfoPago { get; set; } = string.Empty;
        public short PayMethodSelected { get; set; }
        [Required(ErrorMessage = "LIT_ERR_COMPANY_NAME_REQUIRED")]
        [StringLength(250, ErrorMessage = "LIT_BETWEEN_3_250", MinimumLength = 3)]
        [NoEmojisAllowed(ErrorMessage = "LIT_ERROR_INSERT_EMOJIS")]
        [NoSurrogateTextAllowed(ErrorMessage = "LIT_TEXT_STYLE_ERROR")]
        [RegularExpression(RegularExpressions.AT_LEAST_ONE_LETTER, ErrorMessage = "LIT_ERR_CONTAIN_A_LETTER")]
        public string CompanyName { get; set; }
        public string Nit { get; set; }
        public List<SelectListItem> Countries { get; set; }
        public int CountryIdSelected { get; set; } = 0;
        public int CountryId { get; set; } = 0;
        public int CityIdSelected { get; set; } = 0;
        public int LocalizationIdSelected { get; set; } = 0;
        [Required(ErrorMessage = "LIT_ERR_ADDRESS_REQUIRED")]
        [StringLength(200, ErrorMessage = "LIT_ERR_LENGTH_MAX_200")]
        [NoEmojisAllowed(ErrorMessage = "LIT_ERROR_INSERT_EMOJIS")]
        [NoSurrogateTextAllowed(ErrorMessage = "LIT_TEXT_STYLE_ERROR")]
        [RegularExpression(RegularExpressions.AT_LEAST_ONE_LETTER, ErrorMessage = "LIT_ERR_CONTAIN_A_LETTER")]
        public string Address { get; set; }
        [Required(ErrorMessage = "LIT_ERR_CONTACT_NAME_REQUIRED")]
        [StringLength(100, ErrorMessage = "LIT_ERR_LENGTH_MAX_100")]
        [NoEmojisAllowed(ErrorMessage = "LIT_ERROR_INSERT_EMOJIS")]
        [NoSurrogateTextAllowed(ErrorMessage = "LIT_TEXT_STYLE_ERROR")]
        [RegularExpression(RegularExpressions.AT_LEAST_ONE_LETTER, ErrorMessage = "LIT_ERR_CONTAIN_A_LETTER")]
        public string ContactName { get; set; }
        [Required(ErrorMessage = "LIT_ERR_CONTACT_PHONE_REQUIRED")]
        [NoEmojisAllowed(ErrorMessage = "LIT_ERROR_INSERT_EMOJIS")]
        [NoSurrogateTextAllowed(ErrorMessage = "LIT_TEXT_STYLE_ERROR")]
        [RegularExpression(RegularExpressions.AT_LEAST_ONE_NUMBER, ErrorMessage = "LIT_ERR_CONTAIN_A_NUMBER")]
        public string ContactPhone { get; set; } = string.Empty;
        [Required(ErrorMessage = "LIT_ERR_EMAIL_REQUIRED")]
        [EmailAddress(ErrorMessage = "LIT_ERR_EMAIL_INVALID")]
        [StringLength(50, ErrorMessage = "LIT_ERR_LENGTH_MAX_50")]
        [NoEmojisAllowed(ErrorMessage = "LIT_ERROR_INSERT_EMOJIS")]
        [NoSurrogateTextAllowed(ErrorMessage = "LIT_TEXT_STYLE_ERROR")]
        [RegularExpression(RegularExpressions.EMAIL_REGEX, ErrorMessage = "LIT_ERR_EMAIL_INVALID")]
        public string ContactEmail { get; set; }
        public bool HasVat { get; set; } = false;
        public int OfferId { get; set; } = 0;
        public bool CanEdit { get; set; } = true;
        public string PageRedirect { get; set; } = string.Empty;
        public string ProductId { get; set; } = string.Empty;
        public string ProductName { get; set; } = string.Empty;
        public bool IsCompanyInCRM { get; set; }

        public CompanyCardTokenModel CompanyCard { get; set; }
        public string IdTokenEncryptedSelected { get; set; }
        public bool HasCompanyCard
        {
            get
            {
                return !(CompanyCard is null);
            }
        }


    }
}