using Redarbor.Company.OCC.WebUI.Models.Company.Product;
using Redarbor.Master.Entities.Enums;
using System.Collections.Generic;

namespace Redarbor.Company.OCC.WebUI.Models.Company.Cart
{
    public class CompanyCartProductDataModel
    {
        public ProductDataModel Product { get; set; } = new ProductDataModel();
        public string ProductSelected { get; set; } = string.Empty;
        public int PositionId { get; set; } = -1;
        public bool HasVat { get; set; } = false;
        public int TemporalitySelected { get; set; } = (int)TemporalityEnum.Year;
        public List<CompanyCartProductTemporalityDataModel> Temporalities { get; set; } = new List<CompanyCartProductTemporalityDataModel>();
        public bool IsChecked { get; set; }
    }
}