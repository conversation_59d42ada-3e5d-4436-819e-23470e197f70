using Redarbor.Common.Entities.Constants;
using Redarbor.Master.Entities.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Web.Mvc;

namespace Redarbor.Company.OCC.WebUI.Models.Company
{
    public class UserDataModel
    {
        public long Id { get; set; }
        public short PortalId { get; set; }
        public string Username { get; set; } = string.Empty;
        [Required(ErrorMessage = "PASSWORD_REQUIRED")] // Lit--> CODE_PASS_OBLIG  page-> (int)PageEnum.CompanyAccounts;
        [StringLength(50, MinimumLength = 4, ErrorMessage = "LIT_ERR_LENGTH_4_50")]
        public string Password { get; set; } = string.Empty;
        public int CompanyId { get; set; }
        public int ParentCompanyId { get; set; }
        public int OriginId { get; set; }
        public int TypeId { get; set; }
        [Required(ErrorMessage = "INFORM_NAME")] // Lit--> CODE_NOMBRE_OBLIG  page-> (int)PageEnum.CompanyAccounts;
        [StringLength(250, ErrorMessage = "MAX_250_CHAR")]
        public string ContactName { get; set; } = string.Empty;
        [Required(ErrorMessage = "EMAIL_REQUIRED")] // Lit--> CODE_EMAIL_OBLIG  page-> (int)PageEnum.CompanyAccounts;
        [StringLength(75, ErrorMessage = "MAX_75_CHAR")] // Lit--> LIT_ERROR_MAXIMO_75_CARACTERES  page-> (int)PageEnum.CompanyAccounts;
        [RegularExpression(RegularExpressions.EMAIL_REGEX, ErrorMessage = "CODE_EMAIL_FORMATO")] // Lit--> CODE_EMAIL_FORMATO  page-> (int)PageEnum.CompanyAccounts;
        [Remote("CheckEmail", "CompanyConfigurationAccounts", AdditionalFields = "IdEncrypt")]
        public string Email { get; set; } = string.Empty;
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        [StringLength(20, ErrorMessage = "MAX_20_CHAR")] // Lit--> CODE_TEL1_MAXLONG  page-> (int)PageEnum.CompanyAccounts;
        [RegularExpression("^[0-9-]*$", ErrorMessage = "PHONE_FORMAT_INCORRECT")] // Lit--> CODE_TEL1_FORMATO  page-> (int)PageEnum.CompanyAccounts;
        public string PhoneNumbers { get; set; } = string.Empty;
        public string Fax { get; set; } = string.Empty;
        public DateTime CreatedOn { get; set; } = DateTime.UtcNow;
        public long CreatedBy { get; set; }
        public DateTime UpdatedOn { get; set; } = DateTime.UtcNow;
        public DateTime DeletedOn { get; set; } = DateTime.UtcNow;
        public DateTime LastLoginOn { get; set; } = DateTime.UtcNow;
        public Int16 Principal { get; set; }
        public int NdrStatusId { get; set; }
        public int StatusId { get; set; }
        public Int16 RoleId { get; set; } = (short)UserRoleEnum.EMPTY;
        public long AppId { get; set; }
        public long CandidateId { get; set; }
        public long CurriculumId { get; set; }

        public string UserRoleLiteral
        {
            get
            {
                var literal = string.Empty;

                switch (RoleId)
                {
                    case (short)UserRoleEnum.ADMINISTRADOR_PRINCIPAL:
                        literal = "LIT_ADMINISTRADOR_PRINCIPAL";
                        break;
                    case (short)UserRoleEnum.ADMINISTRADOR:
                        literal = "LIT_ADMINISTRADOR";
                        break;
                    case (short)UserRoleEnum.GESTOR:
                        literal = "LIT_GESTOR";
                        break;
                    case (short)UserRoleEnum.GESTOR_SENIOR:
                        literal = "LIT_GESTOR_SENIOR";
                        break;
                    case (short)UserRoleEnum.SUPERVISOR:
                        literal = "LIT_SUPERVISOR";
                        break;
                    case (short)UserRoleEnum.COMPUADVISOR:
                        literal = "LIT_COMPUADVISOR";
                        break;
                    case (short)UserRoleEnum.SELECCIONADOR:
                        literal = "LIT_SELECCIONADOR";
                        break;
                }
                return literal;
            }
        }
        public string IdEncrypt { get; set; } = string.Empty;
        [Required(ErrorMessage = "MAIL_REQUIRED")] // Lit--> CODE_EMAIL_OBLIG  page-> (int)PageEnum.CompanyAccounts;
        [StringLength(75, ErrorMessage = "MAX_75_CHAR")] // Lit--> LIT_ERROR_MAXIMO_75_CARACTERES  page-> (int)PageEnum.CompanyAccounts;
        [Remote("CheckEmailWithDomain", "CompanyConfigurationAccounts", AdditionalFields = "DomainCompanySelected,Email")]
        public string PrefixEmail { get; set; } = string.Empty;
        public string DomainEmail { get; set; } = string.Empty;
        public string DomainCompanySelected { get; set; } = string.Empty;
        public List<SelectListItem> DomainsCompany { get; set; } = new List<SelectListItem>();
        [Required(ErrorMessage = "Debes informar el Rol")] //Falta hacer este como literal no estaba en bj
        public string RolSelected { get; set; } = string.Empty;
        public List<SelectListItem> Rols { get; set; } = new List<SelectListItem>();
        public bool IsNew { get; set; }
        public bool HasContent { get; set; }
        public bool IsValidToAdd { get; set; }
    }
}