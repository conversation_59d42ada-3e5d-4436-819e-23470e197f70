using Redarbor.Master.Entities.Enums;
using System.Collections.Generic;
using System.Web.Mvc;

namespace Redarbor.Company.OCC.WebUI.Models.Company.Configuration
{
    public class AccountUserDataModel
    {
        public IEnumerable<UserDataModel> Users { get; set; }
        public bool ExceededLimitUsers { get; set; }
        public int TotalUsers { get; set; }
        public int LimitUsers { get; set; }
        public bool ShowContactPhone { get; set; }
        public UserRoleEnum Role { get; set; }
        public bool HasFeature { get; set; } = false;
        public List<SelectListItem> UsersOrders { get; set; }        
        public int UserOrderSelected { get; set; }
        public List<SelectListItem> RoleFilter { get; set; }
        public string RoleFilterSelected { get; set; } = string.Empty;
        public string FilterMail { get; set; } = string.Empty;
        public string FilterUserName { get; set; } = string.Empty;
        public List<SelectListItem> UsersItems { get; set; }
        public string UsersItemsSelected { get; set; } = string.Empty;
        public string AllUsersFeatDecript { get; set; } = string.Empty;
        public string RoleAdministratorPrincFeatures { get; set; } = string.Empty;
        public string RoleGestorFeatures { get; set; } = string.Empty;
        public string RoleAdministradorFeatures { get; set; } = string.Empty;
        public string RoleGestorSeniorFeatures { get; set; } = string.Empty;
        public string RoleSupervisorFeatures { get; set; } = string.Empty;
        public string RoleCompuadvisorFeatures { get; set; } = string.Empty;
        public string RoleEntrevistadorFeatures { get; set; } = string.Empty;
        public string RoleSeleccionadorFeatures { get; set; } = string.Empty;
        public bool IsCompanyMembership { get; set; }

        public AccountUserDataModel() { }        
    }
}