using Redarbor.Company.OCC.WebUI.Helpers;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Web.Mvc;

namespace Redarbor.Company.OCC.WebUI.Validation.Attributes
{
    public class PostalCodeAttribute : ValidationAttribute, IClientValidatable
    {
        public string PostalCodeHolderName { get; set; }
        public string ChildModelName { get; set; }

        public PostalCodeAttribute(string postalCodeHolderName, string childModelName)
        {
            PostalCodeHolderName = postalCodeHolderName;
            ChildModelName = $"{childModelName}_" ;
        }

        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            if(value == null) 
            {
                return ValidationResult.Success;
            }
            var portalConfig = PortalConfigHelper.GetPortalConfiguration();

            if (portalConfig == null)
            {
                return ValidationResult.Success;
            }

            var property = validationContext.ObjectType.GetProperty(PostalCodeHolderName);
            if (property == null)
                return new ValidationResult(ErrorMessage, new[] { validationContext.MemberName });

            int postalCodeId = (int)property.GetValue(validationContext.ObjectInstance);

            if (value.ToString() == "0" && postalCodeId == 0)
            {
                return ValidationResult.Success;
            }

            if (!PostalCodeHelper.GetPostalCodeInfo(portalConfig.PortalId, postalCodeId))
                return new ValidationResult(ErrorMessage, new[] { validationContext.MemberName });

            return ValidationResult.Success;
        }

        public IEnumerable<ModelClientValidationRule> GetClientValidationRules(ModelMetadata metadata, ControllerContext context)
        {
            var rule = new ModelClientValidationRule
            {
                ValidationType = "postalcode",
                ErrorMessage = ErrorMessage
            };
            rule.ValidationParameters["postalcodeholdername"] = PostalCodeHolderName;
            rule.ValidationParameters["childname"] = ChildModelName;
            yield return rule;
        }
    }
}