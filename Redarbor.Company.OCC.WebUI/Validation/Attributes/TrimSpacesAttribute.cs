using Redarbor.Common.Entities.Constants;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;
using System.Web.Mvc;

namespace Redarbor.Company.OCC.WebUI.Validation.Attributes
{
    public class TrimSpacesAttribute : ValidationAttribute, IClientValidatable
    {
        public TrimSpacesAttribute() { }

        public IEnumerable<ModelClientValidationRule> GetClientValidationRules(ModelMetadata metadata, ControllerContext context)
        {
            var rule = new ModelClientValidationRule
            {
                ValidationType = "trimspaces"
            };

            rule.ValidationParameters.Add("trimspaces", RegularExpressions.CONTAINS_MULTIPLE_SPACES);
            yield return rule;
        }
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            if (value == null || !(value is string))
                return ValidationResult.Success;

            var cleanedValue = Regex.Replace(value.ToString(), RegularExpressions.CONTAINS_MULTIPLE_SPACES, " ").Trim();

            var property = validationContext.ObjectType.GetProperty(validationContext.MemberName);
            if (property != null && property.CanWrite)
            {
                property.SetValue(validationContext.ObjectInstance, cleanedValue, null);
            }

            return ValidationResult.Success;
        }
    }
}