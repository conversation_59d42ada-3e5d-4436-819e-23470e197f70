using Redarbor.Common.Entities.Enums;
using Redarbor.Company.Contracts.ServiceLibrary;
using Redarbor.Company.Contracts.ServiceLibrary.Enums;
using Redarbor.Company.OCC.WebUI.Controllers.ServiceHelpers;
using Redarbor.Company.OCC.WebUI.Helpers;
using Redarbor.Company.OCC.WebUI.Models.Company.Configuration;
using Redarbor.Company.OCC.WebUI.Security;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Web.UI.Library.Controllers;
using System;
using System.Diagnostics;
using System.Threading.Tasks;
using System.Web.Mvc;

namespace Redarbor.Company.OCC.WebUI.Controllers
{
    [RedarborAuthorize]
    [RoutePrefix("cuenta/datos-facturacion")]
    public class CompanyBillingController : RedarborController
    {
        private readonly ICompanyBillingServiceHelper _companyBillingServiceHelper;
        private readonly IExceptionPublisherService _exceptionPublisherService;
        private readonly IPortalConfigurationService _portalConfigurationService;
        private readonly ICompanyCheckIdentifierService _companyCheckIdentifierService;
        public CompanyBillingController(ICompanyBillingServiceHelper companyBillingServiceHelper
            , IExceptionPublisherService exceptionPublisherService
            , IPortalConfigurationService portalConfigurationService
            , ICompanyCheckIdentifierService companyCheckIdentifierService)
        {
            _companyBillingServiceHelper = companyBillingServiceHelper;
            _exceptionPublisherService = exceptionPublisherService;
            _portalConfigurationService = portalConfigurationService;
            _companyCheckIdentifierService = companyCheckIdentifierService;
        }

        [Route]
        public ActionResult Index()
        {
            InvoiceDataModel model = new();
            try
            {
                var portalConfig = _portalConfigurationService.GetPortalConfiguration();
                var companyCredentials = SecurityHelper.GetCompanyCredentials();

                model = _companyBillingServiceHelper.GetByCompany(companyCredentials, portalConfig);
                return View(model);
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyBillingController Index-Get {ex}");
                _exceptionPublisherService.Publish(ex, "CompanyBillingController", "Index-Get");
                return RedirectToAction("Index", "CompanyBilling");
            }
        }

        [Route("SaveContact")]
        [HttpPost]
        public async Task<ActionResult> SaveContact(InvoiceDataModel model)
        {
            try
            {
                ModelState.Remove("CompanyName");
                ModelState.Remove("ZipCode");
                ModelState.Remove("Nit");
                ModelState.Remove("TaxRegimeId");
                if (ModelState.IsValid)
                {
                    var savedInvoice = await _companyBillingServiceHelper.SaveInvoice(model);
                    if (savedInvoice)
                    {
                        return RedirectToAction("index");
                    }
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyBillingController SaveContact {ex}");
                _exceptionPublisherService.Publish(ex, "CompanyBillingController", "SaveContact");
                return RedirectToAction("Index", "CompanyBilling");
            }

            return RedirectToAction("index");
        }

        [Route("SaveTaxInfo")]
        [HttpPost]
        public async Task<ActionResult> SaveTaxInfo(InvoiceDataModel model)
        {
            try
            {
                ModelState.Remove("ContactEmail");
                if (ModelState.IsValid)
                {
                    var savedInvoice = await _companyBillingServiceHelper.SaveInvoice(model);
                    if (savedInvoice)
                    {
                        return RedirectToAction("index");
                    }
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"CompanyBillingController SaveTaxInfo {ex}");
                _exceptionPublisherService.Publish(ex, "CompanyBillingController", "SaveTaxInfo");
                return RedirectToAction("Index", "CompanyBilling");
            }

            return RedirectToAction("index");
        }

        public JsonResult CheckIdentifier()
        {
            var portalConfig = _portalConfigurationService.GetPortalConfiguration();
            var rfc = Request.QueryString["Nit"] ?? string.Empty;
            var companyCredentials = SecurityHelper.GetCompanyCredentials();
            var user = CompanyHelper.GetUserEntity(companyCredentials.UserId, portalConfig.PortalId);
            var companyEntity = CompanyHelper.GetCompany(portalConfig.PortalId, companyCredentials);
            int checkIdentifierId = _companyCheckIdentifierService.CheckIdentifier(rfc, companyEntity.Address.CountryId, portalConfig, companyCredentials.IdCompany);

            if (checkIdentifierId == (int)NitResultEnum.IncorrectNit)
            {
                var literal = PageLiteralsHelper.GetLiteral("CODE_NIT_INCORRECT", (short)PageEnum.CompanyRegister, portalConfig);
                return Json(literal, JsonRequestBehavior.AllowGet);
            }

            if (checkIdentifierId == (int)NitResultEnum.DuplicateNit)
            {
                if (companyEntity.Nit != rfc)
                {
                    var literal = PageLiteralsHelper.GetLiteral("CODE_NIT_DUPLICATE", (short)PageEnum.CompanyRegister, portalConfig);
                    return Json(literal, JsonRequestBehavior.AllowGet);
                }
            }

            return Json(true, JsonRequestBehavior.AllowGet);
        }
        public JsonResult IsEmailAvailable()
        {
            var email = Request.QueryString["ContactEmail"] ?? string.Empty;
            var portalConfig = _portalConfigurationService.GetPortalConfiguration();

            return Json(!CompanyHelper.ExistsMail(email, portalConfig.PortalId), JsonRequestBehavior.AllowGet);
        }

    }
}
