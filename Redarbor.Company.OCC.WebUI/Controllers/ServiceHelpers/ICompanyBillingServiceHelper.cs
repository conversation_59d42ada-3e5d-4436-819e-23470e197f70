using Redarbor.Common.Entities.Configuration;
using Redarbor.Company.OCC.WebUI.Models.Company.Configuration;
using Redarbor.Master.Contracts.ServiceLibrary.DTO;
using System.Threading.Tasks;

namespace Redarbor.Company.OCC.WebUI.Controllers.ServiceHelpers
{
    public interface ICompanyBillingServiceHelper
    {

        InvoiceDataModel GetByCompany(CompanyCredentials companyCredentials, PortalConfig portalConfig);

        Task<bool> SaveInvoice(InvoiceDataModel invoiceDataModel);
    }
}