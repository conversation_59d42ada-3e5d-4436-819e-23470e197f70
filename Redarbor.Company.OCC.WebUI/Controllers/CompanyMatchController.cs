
using Redarbor.Company.Contracts.ServiceLibrary;
using Redarbor.Company.OCC.WebUI.Security;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Core.Contracts.ServiceLibrary;
using Redarbor.Master.Contracts.ServiceLibrary;
using Redarbor.Offer.Contracts.ServiceLibrary;
using Redarbor.Products.Contracts.ServiceLibrary;
using Redarbor.ProductSubGroups.Contracts.ServiceLibrary;
using System.Threading.Tasks;
using System.Web.Mvc;

namespace Redarbor.Company.OCC.WebUI.Controllers
{
   
    [RedarborAuthorize]
    public class CompanyMatchController : CompanyBaseController
    {
        public CompanyMatchController(IProductService productService,
            IProductSubGroupsService productSubGroupsService,
            IOfferService offerService,
            ICompanyProductService companyProductService,
            ICompanyService companyService,
            IPortalConfigurationService portalConfigurationService,
            IDictionaryService dictionaryService,
            ISecurityService securityService,
            IExceptionPublisherService exceptionPublisherService) : base(productService, productSubGroupsService, offerService, companyProductService, companyService, portalConfigurationService, dictionaryService, securityService, exceptionPublisherService)
        {
          
        }

        [Route]
        public async Task<ActionResult> Index()
        {
           
            return View();
        }

    }
}