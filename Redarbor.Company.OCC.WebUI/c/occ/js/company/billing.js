document.addEventListener('DOMContentLoaded', function () {
    setupEditToggle('editTaxInformation', 'taxForm', 'taxEditForm');
    setupEditToggle('editContactInformation', 'contactForm', 'contactEditForm');
    setupCancelToggle('cancelTax', 'taxForm', 'taxEditForm', 'editTaxInformation');
    setupCancelToggle('cancelContact', 'contactForm', 'contactEditForm', 'editContactInformation');
    initializeDynamicPanels();
});

function setupEditToggle(buttonId, formId, editFormId) {
    const button = document.getElementById(buttonId);
    if (button) {
        button.onclick = () => {
            const form = document.getElementById(formId);
            const editForm = document.getElementById(editFormId);
            if (form && editForm) {
                editForm.classList.toggle('hidden');
                form.classList.toggle('hidden');
                button.classList.toggle('hidden');
            }
        };
    }
}

function setupCancelToggle(buttonCancelId, formId, editFormId, buttonEditId) {
    const buttonCancel = document.getElementById(buttonCancelId);
    if (buttonCancel) {
        buttonCancel.onclick = () => {
            const form = document.getElementById(formId);
            const editForm = document.getElementById(editFormId);
            const buttonEdit = document.getElementById(buttonEditId);
            if (form && editForm) {
                editForm.classList.toggle('hidden');
                form.classList.toggle('hidden');
                buttonEdit.classList.toggle('hidden');
            }
        };
    }
}

function initializeDynamicPanels() {
    const triggers = document.querySelectorAll('[data-panel-open]');
    if (triggers) {
        triggers.forEach(trigger => {
            trigger.onclick = () => handlePanelTriggerClick(trigger);
        });
    }
}

function handlePanelTriggerClick(trigger) {
    const targetId = trigger.getAttribute('data-panel-open');
    if (targetId) {
        const template = getPanelTemplate(targetId);
        if (template) {
            appendPanelToDOM(template);
            requestAnimationFrame(() => {
                const panelWrapper = document.getElementById(targetId);
                if (panelWrapper) {
                    animatePanelOpen(panelWrapper);
                    setupPanelCloseEvents(panelWrapper);
                }
            });
        }
    } 
}

function getPanelTemplate(targetId) {
    const templateId = `template${capitalize(targetId)}`;
    return document.getElementById(templateId);
}

function capitalize(text) {
    return text.charAt(0).toUpperCase() + text.slice(1);
}

function appendPanelToDOM(template) {
    const clone = template.content.cloneNode(true);
    document.body.appendChild(clone);
}

function animatePanelOpen(panelWrapper) {
    const panelContent = panelWrapper.querySelector('aside');
    if (panelContent) {
        panelWrapper.classList.remove('hidden');
        document.body.classList.add('overflow-hidden');
        setTimeout(() => {
            panelWrapper.classList.replace('opacity-0', 'opacity-100');
            panelContent.classList.remove('translate-x-full');
        }, 10);
    }
}

function setupPanelCloseEvents(panelWrapper) {
    panelWrapper.onclick = (event) => {
        if (event.target === panelWrapper) {
            closeSidePanel(panelWrapper);
        }
    };
    const closeBtn = panelWrapper.querySelector('[data-panel-close]');
    if (closeBtn) {
        closeBtn.onclick = () => closeSidePanel(panelWrapper);
    }
    const escHandler = (event) => {
        if (event.key === 'Escape' || event.keyCode === 27) {
            closeSidePanel(panelWrapper);
            document.removeEventListener('keydown', escHandler);
        }
    };
    document.addEventListener('keydown', escHandler);
}

function closeSidePanel(panelWrapper) {
    const panelContent = panelWrapper.querySelector('aside');
    if (panelContent) {
        panelContent.classList.add('translate-x-full');
        panelWrapper.classList.replace('opacity-100', 'opacity-0');
        document.body.classList.remove("overflow-hidden");
        setTimeout(function () {
            panelWrapper.remove();
        }, 300);
    }
}