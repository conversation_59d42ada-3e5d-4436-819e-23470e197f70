SessionHeartbeat = (function () {
    var keepSessionAliveUrl = null;
    var timeout = 300000; // 5m

    function setupSessionHeartbeat(actionUrl) {
        keepSessionAliveUrl = actionUrl;
        checkToKeepSessionAlive();
    }

    function checkToKeepSessionAlive() {
        setTimeout(function () { keepSessionAlive(); }, timeout);
    }

    function keepSessionAlive() {
        if (keepSessionAliveUrl != null) {
            $.ajax({
                type: "POST",
                url: keepSessionAliveUrl,
                success: function (data) {
                    clientMovedSinceLastTimeout = false;
                    checkToKeepSessionAlive();
                },
                error: function (data) {
                    console.log("Error posting to " & keepSessionAliveUrl);
                }
            });
        }
    }

    return {
        Setup: setupSessionHeartbeat
    };

})();