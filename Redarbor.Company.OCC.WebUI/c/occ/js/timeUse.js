TimeUseCheckToAdd= (function () {
    var timeUseByUserActionUrl = null;
    var timeUseTimeout = 60000; // 60s
    var maxTimesToCheckTime = 10;
    var timesUpdateOrAddTime = 0;

    function setupTimeUseCheckToAdd(actionUrl) {
        timeUseByUserActionUrl = actionUrl;
        timesUpdateOrAddTime = 1;
        timeUseCheckToAddAction();
    }

    function checkTimeUseCheckToAdd() {
        timesUpdateOrAddTime++;

        if(timesUpdateOrAddTime > maxTimesToCheckTime)
            return false;        

        setTimeout(function () { timeUseCheckToAddAction(); }, timeUseTimeout);
    }

    function timeUseCheckToAddAction() {
        if (timeUseByUserActionUrl != null) {
            $.ajax({
                type: "POST",
                url: timeUseByUserActionUrl,
                data: JSON.stringify({ elapsedTimeMilliseconds: timeUseTimeout}),
                contentType: "application/json",
                dataType: "json",
                success: function (data) {  
                    checkTimeUseCheckToAdd();
                },
                error: function (data) {
                    console.log("Error posting to " & timeUseByUserActionUrl);
                }
            });
        }
    }

    return {
        Setup: setupTimeUseCheckToAdd
    };

})();