<?xml version="1.0" encoding="utf-8"?>
<configuration xmlns:xdt="http://schemas.microsoft.com/XML-Document-Transform">
	<appSettings>
		<add key="PAYPAL_TEST" value="0"
			 xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
		<add key="ENVIRONMENT" value="BETA"
			 xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
		<add key="ID_PORTAL" value="41"
			 xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
		<add key="SHOW_ADSENSE" value="true"
			 xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
		<add key="TEST_NEW_WORLDPAY" value="false"
			 xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
		<add key="PAYMENT_MODE_RBS" value="0"
			 xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
		<add key="API_CANDIDATES" value="http://api-candidates-v8.ct.local/"
			 xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
		<!-- ojo lo usa el nugget de candidatos -->
		<add key="PATH_CLOUD_FRONT_CDN" value="https://cp.ct-stc.com/ae"
			 xdt:Transform="Insert" xdt:Locator="Match(key)"/>
		<!-- Insert -->
		<add key="USE_CDN_VERSIONED_PATH" value="true"
			 xdt:Transform="Insert" xdt:Locator="Match(key)"/>
		<!-- Insert -->
		<add key="PaymentOperationEndPoint" value="http://api-payments.ct.local/"
			 xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />

		<add key="OnlyMemcacheIfEnabled" value="true" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />

		<add key="COMERCIAL_INFORMATION_URL" value="https://trk.prdt.computrabajo.com/l/831543/2020-07-03/j724" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
		<add key="NewSuggestByCT" value="true" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>

		<add key="ApiAuthenticateUrl" value="http://api-authenticate.ct.local/" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>

		<add key="ApiMatchElastic" value="http://api-matches-v8.ct.local/" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
		<add key="ApiQueueRabbit" value="http://api-queues.ct.local" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
		<add key="KINESIS_API_ENDPOINT" value="http://api-kinesis.ct.local/"
			   xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
		<add key="RABBITMQ_API_ENDPOINT" value="http://api-queues.ct.local/"
				 xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>

		<add key="URL_PANDAPE_ATS" value="https://ats.pandape.com/" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>

		<add key="API_OFFER_KPI_ELASTIC" value="http://api-offers.occ.local/" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />

		<!-- CHATGPT URL API PRODUCCION-->
		<add key="OFFER_AI_CREATE" value="https://gce6ocim6axtptvnq3exmpjhgq0bcemu.lambda-url.us-east-1.on.aws/v1/offer/create" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />

		<add key="JOBADS_API_ENDPOINT" value="http://api-jobads.ct.local/" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
		<!-- TIMELINE-->
		<add key="TIMELINE_URL_API" value="http://api-timeline.ct.local/" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />

		<add key="OFFERS_AE_URL_API" value="http://beta-api-offers-ae.ct.local/" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
		<add key="COMPANY_AE_URL_API" value="http://beta-api-ae.ct.local/" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />		

		<!-- Add Redarbor.AppSettings.Tool-->
		<add key="APPSETTINGS_URL_API" value="http://api-settings.occ.local" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
		<add key="URL_WEB_API_CANDIDATE_BBDD" value="http://beta-api-candidates-core.ct.local" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
		<add key="URL_WEB_API_USER_BBDD" value="http://beta-api-users.ct.local" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
		<add key="FULL_CANDIDATE_KEY_BY_API" value="BETA_USE_API_CANDIDATE_CORE_GET_FULL_CANDIDATE" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
		<add key="CANDIDATE_BY_ID_KEY_BY_API" value="BETA_USE_API_CANDIDATE_CORE_GET_CANDIDATE_BY_ID" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
		<add key="PRINCIPAL_CANDIDATE_FILE_KEY_BY_API" value="BETA_USE_API_CANDIDATE_CORE_GET_PRINCIPAL_CANDIDATE_FILE" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
		<add key="TEST_COMPETENCES_VISIBLE_KEY_BY_API" value="BETA_USE_API_CANDIDATE_CORE_GET_TEST_COMPETENCES_VISIBLE" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
		<add key="GET_CANDIDATE_PHOTO_KEY_BY_API" value="BETA_USE_API_CANDIDATE_CORE_GET_CANDIDATE_PHOTO" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
		<add key="GET_CANDIDATE_COMPLETE_CV_KEY_BY_API" value="BETA_USE_API_CANDIDATE_CORE_GET_CANDIDATE_COMPLETE_CV" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
		<add key="GET_CANDIDATE_FILE_BY_ID_KEY_BY_API" value="BETA_USE_API_CANDIDATE_CORE_GET_CANDIDATE_FILE_BY_ID" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
		<add key="GET_IDUSER_BY_CANDIDATEID_KEY_BY_API" value="BETA_USE_API_CANDIDATE_CORE_GET_USERID_BY_CANDIDATEID" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
		<add key="GET_USER_KEY_BY_API" value="BETA_USE_API_USER_GET_USER" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
		<add key="GET_MANAGERS_BY_IDCOMPANY_KEY_BY_API" value="BETA_USE_API_USER_GET_MANAGERS_BY_IDCOMPANY" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
		<add key="GET_USERID_BY_EMAIL_KEY_BY_API" value="BETA_USE_API_USER_GET_USERID_BY_EMAIL" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
		<add key="GET_EMAIL_BY_USER_ID_KEY_BY_API" value="BETA_USE_API_USER_GET_EMAIL_BY_USERID" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
		<add key="URL_API_HUB" value="http://api-hub.ct.local" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
		<add key="API_KILLERQUESTIONS_URL_API" value="http://beta-api-kq-core.ct.local" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />	
		<!--Hasta que no esté entorno no tenemos api-->

    <add key="USE_API_LITERALS" value="true"  xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="URL_API_LITERALS" value="http://beta-api-literals.occ.local/"  xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
	</appSettings>
	<connectionStrings>
    <add name="Commondbconnectionstring"
			 connectionString="server=mysql-master.occ.local;Uid=usr_ae;PersistSecurityInfo=False;database=repo_ae;Pwd=************;port=3306;ConnectionTimeout=15;ConvertZeroDateTime=True;DefaultCommandTimeout=300;ProcedureCacheSize=100;CacheServerProperties=True;SslMode=Required"
			 xdt:Transform="SetAttributes" xdt:Locator="Match(name)"
         />
		<add name="RepoAuthenticatedbconnectionstring"
			 connectionString="server=repo-17.occ.local;Uid=usr_ae;PersistSecurityInfo=False;database=repo_authenticate;Pwd=************;port=3306;ConnectionTimeout=15;ConvertZeroDateTime=True;DefaultCommandTimeout=300;ProcedureCacheSize=100;CacheServerProperties=True;SslMode=Required"
			 xdt:Transform="SetAttributes" xdt:Locator="Match(name)"
         />
		<add name="CTTracking"
			 connectionString=""
			 xdt:Transform="SetAttributes" xdt:Locator="Match(name)"
         />
		<add name="RepoCompaniesConnectionString"
			connectionString="server=repo-1.occ.local;Uid=usr_ae;PersistSecurityInfo=False;database=repo_companies;Pwd=************;port=3306;ConnectionTimeout=15;ConvertZeroDatetime=True; DefaultCommandTimeout=300;"
			xdt:Transform="SetAttributes" xdt:Locator="Match(name)"
        />
    <add name="RepoOffers"
			connectionString="server=repo-1.occ.local;Uid=usr_ae;PersistSecurityInfo=False;database=repo_offers;Pwd=************;port=3306;ConnectionTimeout=15;ConvertZeroDateTime=True;DefaultCommandTimeout=300;ProcedureCacheSize=100;CacheServerProperties=True;SslMode=Required"
			xdt:Transform="SetAttributes" xdt:Locator="Match(name)"
        />
	</connectionStrings>
	<system.web>
		<compilation xdt:Transform="RemoveAttributes(debug)" />
		<httpRuntime maxUrlLength="10999" maxQueryStringLength="2097151" maxRequestLength="51200"  targetFramework="4.6.1" />
	</system.web>
	<system.webServer>
		<security>
			<requestFiltering>
				<requestLimits maxUrl="10999" maxQueryString="2097151" />
			</requestFiltering>
		</security>
	</system.webServer>

</configuration>
