using Autofac.Integration.Mvc;
using Common.PaymentLibrary.Enums;
using Redarbor.Core.Contracts.ServiceLibrary;
using Redarbor.Core.Contracts.ServiceLibrary.Enums;
using Redarbor.Core.Impl.ServiceLibrary;
using Redarbor.Dictionaries.Consumer.Enums;
using Redarbor.Extensions.Library.Extensions;

namespace Redarbor.Company.OCC.WebUI.Helpers
{
    public static class DictionaryHelper
    {
        private static IDictionaryService _dictionaryService;
        private static IDictionaryService DictionaryService
        {
            get
            {
                if (_dictionaryService is null) _dictionaryService = ((DictionaryService)AutofacDependencyResolver.Current
                .GetService(typeof(IDictionaryService)));

                return _dictionaryService;
            }
        }

        public static string GetMonthDescription(int month, short portalId)
        {
            return DictionaryService.GetDictionaryValue(DictionaryEnum.MONTHS, month.ToString(), portalId);
        }

        public static string GetLocation(int? localizacionId, int? cityId, int? countryId, short portalId)
        {
            string value = string.Empty;
            if (cityId > 0 && localizacionId > 1)
            {
                value = DictionaryService.GetDictionaryValue(DictionaryEnum.CITIES_BY_LOCALIZATION, localizacionId.ToInt(), cityId.ToString(), portalId);
            }
            else if (localizacionId > 1 && countryId > 0)
            {
                value = DictionaryService.GetDictionaryValue(DictionaryEnum.LOCALIZATION_BY_COUNTRY, (int)countryId, localizacionId.ToString(), portalId);
            }
            else if (countryId > 0)
            {
                value = DictionaryService.GetDictionaryValue(DictionaryEnum.COUNTRY, countryId.ToString(), portalId);
            }
            else
            {
                value = "Sin especificar";
            }

            return value;
        }

       
       
    }
}