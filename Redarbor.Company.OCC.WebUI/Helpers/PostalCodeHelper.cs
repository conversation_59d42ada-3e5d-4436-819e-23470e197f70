using Autofac.Integration.Mvc;
using Redarbor.PostalCode.Contracts.ServiceLibrary;
using Redarbor.PostalCode.Impl.ServiceLibrary;

namespace Redarbor.Company.OCC.WebUI.Helpers
{
    public static class PostalCodeHelper
    {
        private static IPostalCodeService _service;

        private static IPostalCodeService Service =>
        _service ?? (_service = ((PostalCodeService)AutofacDependencyResolver.Current
                                                             .GetService(typeof(IPostalCodeService))));


        public static bool GetPostalCodeInfo(short portalId, int postalCodeId)
        {
           return Service.ExistPostalCode(portalId, postalCodeId);
        }
    }
}