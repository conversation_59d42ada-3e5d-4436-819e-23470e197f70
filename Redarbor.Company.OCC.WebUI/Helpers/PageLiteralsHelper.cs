using Autofac.Integration.Mvc;
using Redarbor.Common.Entities.Configuration;
using Redarbor.Core.Literals.Contracts.ServiceLibrary;
using Redarbor.Core.Literals.ServiceLibrary;
using Redarbor.Managers.Library;
using System.Diagnostics;

namespace Redarbor.Company.OCC.WebUI.Helpers
{
    public static class PageLiteralsHelper
    {
        public static string GetLiteral(string literalId, int pageId, PortalConfig portalConfig)
        {
            var literalText = RedarborLiteralsManager.Current.GetLiteral(pageId, literalId, portalConfig);
            if (!string.IsNullOrEmpty(literalText))
            {
                return literalText;
            }

            if (RedarborLiteralsManager.Current.IsAlreadySearched(pageId, literalId, portalConfig))
            {
                Trace.TraceWarning($"Literal {literalId} not found for page {pageId} for language id {portalConfig.alternate_idioma}");
                return literalId;
            }

            ILiteralsService literalService = (LiteralsApiService)AutofacDependencyResolver.Current.GetService(typeof(ILiteralsService));
            var literalValue = RedarborLiteralsManager.Current.GetLiteral(literalService, pageId, literalId, portalConfig);

            return !string.IsNullOrWhiteSpace(literalValue) ? literalValue : literalId;
        }
    }
}
