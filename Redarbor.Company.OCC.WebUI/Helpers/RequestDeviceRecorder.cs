using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Core.Kpi.Contracts.ServiceLibrary;
using Redarbor.Core.Kpi.Contracts.ServiceLibrary.Enums;
using Redarbor.Master.Entities.Enums;
using System;
using System.Diagnostics;
using System.Web;

namespace Redarbor.Company.OCC.WebUI.Helpers
{
    public class RequestDeviceRecorder : IRequestDeviceRecorder
    {
        private readonly HttpRequestBase _request;
        private readonly IExceptionPublisherService _exceptionPublisherService;
        private readonly IKpiService _kpiService;


        public RequestDeviceRecorder(IKpiService kpiService, HttpRequestBase request, IExceptionPublisherService exceptionPublisherService)
        {
            _request = request;
            _kpiService = kpiService;
            _exceptionPublisherService = exceptionPublisherService;
        }

        public void OfferPublishRegisterDeviceKpi(short productGroup, short portalId)
        {
            switch (productGroup)
            {
                case (short)ProductGroupsEnum.Freemium:
                    RecordKpi(mobileKpi: KpiEnum.CT_COMPANY_PUBLISH_OFFER_FREMIUM_FROM_MOBILE, desktopKpi: KpiEnum.CT_COMPANY_PUBLISH_OFFER_FREMIUM_FROM_DESKTOP, portalId: portalId);
                    break;
                case (short)ProductGroupsEnum.Packs:
                    RecordKpi(mobileKpi: KpiEnum.CT_COMPANY_PUBLISH_OFFER_PACK_FROM_MOBILE, desktopKpi: KpiEnum.CT_COMPANY_PUBLISH_OFFER_PACK_FROM_DESKTOP, portalId: portalId);
                    break;
                case (short)ProductGroupsEnum.Membership:
                    RecordKpi(mobileKpi: KpiEnum.CT_COMPANY_PUBLISH_OFFER_MEMBERSHIP_FROM_MOBILE, desktopKpi: KpiEnum.CT_COMPANY_PUBLISH_OFFER_MEMBERSHIP_FROM_DESKTOP, portalId: portalId);
                    break;              
            }
        }

        public void RecordMultiPurchaseCartKpi(short portalId)
        {
            RecordKpi(
                mobileKpi: KpiEnum.CT_COMPANY_MULTIPURCHASE_CART_FROM_MOBILE,
                desktopKpi: KpiEnum.CT_COMPANY_MULTIPURCHASE_CART_FROM_DESKTOP, portalId: portalId);
        }

        public void RecordPaymentKoKpi(short portalId)
        {
            RecordKpi(
                mobileKpi: KpiEnum.CT_PAYMENT_KO_FROM_MOBILE,
                desktopKpi: KpiEnum.CT_PAYMENT_KO_FROM_DESKTOP, portalId: portalId);
        }

        public void RecordPaymentOkKpi(short portalId)
        {
            RecordKpi(
                mobileKpi: KpiEnum.CT_PAYMENT_OK_FROM_MOBILE,
                desktopKpi: KpiEnum.CT_PAYMENT_OK_FROM_DESKTOP, portalId: portalId);
        }

        public void RecordConvertToCompleteKpi(short portalId)
        {
            RecordKpi(
                mobileKpi: KpiEnum.CT_COMPANY_CONVERT_OFFER_TO_COMPLETE_FROM_MOBILE,
                desktopKpi: KpiEnum.CT_COMPANY_CONVERT_OFFER_TO_COMPLETE_FROM_DESKTOP, portalId: portalId);
        }

        private void RecordKpi(KpiEnum mobileKpi, KpiEnum desktopKpi, short portalId)
        {
            try
            {
                var kpi = _request.IsFromMobile()
                ? mobileKpi
                : desktopKpi;

                _kpiService.Add((short)kpi, portalId);
            }
            catch (Exception ex)
            {
                Trace.TraceError($"RequestDeviceRecorder -- RecordKpi {ex}");
                _exceptionPublisherService.Publish(ex, "RequestDeviceRecorder", "RecordKpi");
            }
        }
    }
}