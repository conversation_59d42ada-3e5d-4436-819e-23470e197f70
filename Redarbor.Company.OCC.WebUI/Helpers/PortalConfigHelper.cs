using Autofac.Integration.Mvc;
using Redarbor.Common.Entities.Configuration;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Core.Computrabajo.Impl.ServiceLibrary;

namespace Redarbor.Company.OCC.WebUI.Helpers
{
    public static class PortalConfigHelper
    {
        private static IPortalConfigurationService _service;

        private static IPortalConfigurationService Service =>
        _service ?? (_service = ((PortalConfigurationService)AutofacDependencyResolver.Current
                                                             .GetService(typeof(IPortalConfigurationService))));

        public static PortalConfig GetPortalConfiguration()
        {
            return Service.GetPortalConfiguration();            
        }
    }
}