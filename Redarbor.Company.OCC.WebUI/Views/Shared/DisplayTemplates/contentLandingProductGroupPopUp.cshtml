@model Redarbor.Company.OCC.WebUI.Models.Company.Product.LandingContentProductsDataModel
@using Redarbor.Company.OCC.WebUI.Helpers;
@using Redarbor.Common.Entities.Enums;
@using Redarbor.Master.Entities.Enums;
@using System.Text.RegularExpressions;
@using Redarbor.Company.OCC.WebUI.Models.Company.Product;

@{
    var PageId = (int)PageEnum.PopUpPacksComparative;
    var portalConfig = PortalConfigHelper.GetPortalConfiguration();
    var products = (List<ProductDataModel>)ViewData["products"];
    string buttonOrigin = string.Empty;
}
<div class="popup w50 hide js_box_informacion_det">
    <div>
        <ul class="cols tc tlayoutFix mb15">
            <li></li>
            @foreach (var productContentDataModel in Model.LandingProductsContentDataModel)
            {
                <li class="pl15 pr15 vt">
                    <p class="fs18 fwB mb15">@productContentDataModel.Title</p>
                    <p class="tc mb15">
                        @if (productContentDataModel.ProductId == (int)ProductEnum.FreemiumService)
                        {
                            <span class="fs22 fwB">
                                @productContentDataModel.FreeOffers
                            </span>
                            if (productContentDataModel.FreeOffers > 1)
                            {
                                <span class="fc_aux">@PageLiteralsHelper.GetLiteral("LIT_FREE_OFFERS", PageId, portalConfig)</span>
                            }
                            else
                            {
                                <span class="fc_aux">@PageLiteralsHelper.GetLiteral("LIT_FREE_OFFER", PageId, portalConfig)</span>
                            }
                        }
                        else
                        {
                            @PageLiteralsHelper.GetLiteral("LIT_DESDE", PageId, portalConfig)
                            <span class="fs22 fwB">
                                @Html.Raw(productContentDataModel.Price)
                            </span>
                            @PageLiteralsHelper.GetLiteral("LIT_POROFERTA", PageId, portalConfig)
                        }
                    </p>

                    @if (productContentDataModel.ProductId == (int)ProductEnum.FreemiumService)
                    {
                        <a class="b_primary mb15 w100" href="/Registro">@PageLiteralsHelper.GetLiteral("LIT_PUBLISH", PageId, portalConfig)</a>
                    }
                    else
                    {
                        <a class="b_primary mb15 w100" href="/Registro">@PageLiteralsHelper.GetLiteral("LIT_COMPRAR", PageId, portalConfig)</a>
                                            }
                </li>
            }
        </ul>
        @if (Model.LandingProductsContentDataModel.Count > 0 && 
            Model.LandingProductsContentDataModel.First() != null && 
            Model.LandingProductsContentDataModel.First().FeaturesDescriptions != null)
        {
            foreach (var featureDescription in Model.LandingProductsContentDataModel.First().FeaturesDescriptions)
            {
            <ul class="cols bb1 tLayoutFix pb15 pt15 tc">

                <li class="tl">@featureDescription.Literal</li>

               @foreach (var product in Model.LandingProductsContentDataModel)
            {
                foreach (var productfeature in product.FeaturesDescriptions)
                {
                    if (productfeature.Priority == featureDescription.Priority)
                    {
                        if (productfeature.Available == 1)
                        {
                            <li class="plr15"><span title="Contiene" class="icon i_ok fn d_ib"></span></li>
                        }
                        else
                        {
                            <li class="plr15"><span title="No Contiene" class="icon i_ko fn d_ib"></span></li>

                        }
                        break;
                    }
                }
            }
            </ul>
            }
        }
    </div>
    <a class="icon i_close js_hide_informacion_det"></a>
</div>

