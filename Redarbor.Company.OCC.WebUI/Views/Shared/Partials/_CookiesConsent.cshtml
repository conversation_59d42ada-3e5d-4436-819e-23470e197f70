@using Redarbor.Company.OCC.WebUI.Helpers
@using Redarbor.Common.Entities.Enums

@{
    var PageId = (int)ViewData["pageId"];
    var portalConfig = PortalConfigHelper.GetPortalConfiguration();
}

<link rel="stylesheet" type="text/css" href="~/c/v2/css/cookies.css" />
<script>
    window.cookiesData = {
        domain: '@portalConfig.cookie_domain',
        content: {
            message: '@PageLiteralsHelper.GetLiteral("LIT_COOKIES_USING", PageId, portalConfig)',
            dismiss: '@PageLiteralsHelper.GetLiteral("LIT_COOKIES_AGREE", PageId, portalConfig)',
            link: '@PageLiteralsHelper.GetLiteral("LIT_COOKIES_INFO", PageId, portalConfig)',
            href: '@<EMAIL>("LIT_URL_COOKIE_LINK", PageId, portalConfig)'
        },
        isNotPeru: '@(portalConfig.PortalId != (short)PortalEnum.ComputrabajoPeru)'.toLowerCase() === 'true'
    };
</script>
@Scripts.Render("~/bundles/js/commonPublic")