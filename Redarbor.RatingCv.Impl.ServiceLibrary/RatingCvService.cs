using Redarbor.Core.Cache.Contracts.ServiceLibrary;
using Redarbor.Master.Entities.Cv;
using Redarbor.RatingCv.Contracts.ServiceLibrary;
using Redarbor.RatingCv.Library.DomainServiceContracts;
using System;
using System.Collections.Generic;
using System.Linq;
using Redarbor.Core.Shared.Contracts.ServiceLibrary;
using Redarbor.Candidates.Consumer.Domain.Services;

namespace Redarbor.RatingCv.Impl.ServiceLibrary
{
    public class RatingCvService : IRatingCvService
    {
        private readonly ITempCache _tempCache;
        private readonly IRatingCvRecoverAndPersist _ratingCvRecoverAndPersist;
        private readonly IStringToolsService _stringToolsService;
        private readonly ICandidatesConsumerService _candidatesConsumerService;

        public RatingCvService(ITempCache tempCache,
            IRatingCvRecoverAndPersist ratingCvRecoverAndPersist,
            IStringToolsService stringToolsService,
            ICandidatesConsumerService candidatesConsumerService)
        {
            _tempCache = tempCache;
            _ratingCvRecoverAndPersist = ratingCvRecoverAndPersist;
            _stringToolsService = stringToolsService;
            _candidatesConsumerService = candidatesConsumerService;
        }

        public RatingCvEntity GetHighRatingByCandidate(int idCandidate, int idCompany)
        {
            if (idCompany > 0
                && idCandidate > 0)
            {
                return _ratingCvRecoverAndPersist.GetHighRatingByCandidate(idCandidate, idCompany);
            }

            return new RatingCvEntity();
        }

        public Dictionary<long, int> GetHighRatingByCandidatesList(List<int> candidateIds, int idCompany, short rating)
        {
            var result = new Dictionary<long, int>();

            if (idCompany > 0
                && candidateIds.Any())
            {
                result = _ratingCvRecoverAndPersist.GetHighRatingByCandidatesList(_stringToolsService.ConvertListIntToStringSql(candidateIds), idCompany, rating);
            }

            return result;
        }

        public List<int> GetIdsByRating(int idCompany, short rating)
        {
            if(idCompany > 0 
                && rating != 0)
            {
                var dictionary = GetHighRatingsCvsByCompanyAndRating(idCompany, rating);

                if (dictionary.Any())
                    return dictionary.Keys.ToList();
            }

            return new List<int>();
        }

        public Dictionary<int, RatingCvEntity> GetHighRatingsCvsByCompanyAndRating(int idCompany, short rating)
        {
            if (idCompany > 0
                && rating != 0)
                return GetSpecificRatingValue(GetHighRatingsCvsByCompany(idCompany), rating);

            return new Dictionary<int, RatingCvEntity>();
        }

        private Dictionary<int, RatingCvEntity> GetSpecificRatingValue(Dictionary<int, RatingCvEntity> dictionary, short rating)
        {
            if (dictionary.Any() && rating > 0)
                return dictionary.Where(r => r.Value.Rating >= rating).ToDictionary(k => k.Key, v => v.Value);
            else
                return dictionary;
        }

        public Dictionary<int, RatingCvEntity> GetHighRatingsCvsByCompany(int idCompany)
        {
            var result = new Dictionary<int, RatingCvEntity>();

            if (idCompany > 0)
            {
                var key = $"HIGHRATING_CV_COMPANY_{idCompany}";
                var cache = _tempCache.Get<Dictionary<int, RatingCvEntity>>(key);

                if (cache != null)
                {
                    var maxCacheValue = DateTime.MinValue;
                    var maxValue = DateTime.MinValue;

                    if (cache.Any())
                    {
                        maxCacheValue = cache.Values.Max(c => c.DateMod);
                        maxValue = _ratingCvRecoverAndPersist.GetMaxDateHighlightedRatings(idCompany);
                    }

                    if (maxValue == maxCacheValue
                    && maxValue > DateTime.MinValue
                    && maxCacheValue > DateTime.MinValue)
                        return cache;
                }


                result = _ratingCvRecoverAndPersist.GetResultHighRatingsCvsByCompany(idCompany);

                if (result.Any())
                    _tempCache.Add(key, result, new TimeSpan(0, 30, 0));
            }

            return result;
        }

        public List<RatingCvEntity> GetRatingsCvsByCompanyAndCandidate(int idCandidate, int idCompany, short portalId)
        {
            var result = new List<RatingCvEntity>();

            if (idCompany > 0
                && idCandidate > 0)
            {
                var key = $"RATING_CV_COMPANY_{idCompany}_CANDIDATEID_{idCandidate}";
                var cache = _tempCache.Get<List<RatingCvEntity>>(key, portalId);

                if (cache != null)
                {
                    var maxCacheValue = DateTime.MinValue;
                    var maxValue = DateTime.MinValue;

                    if (cache.Any())
                    {
                        maxCacheValue = cache.Max(c => c.CreatedOn);
                        maxValue = _ratingCvRecoverAndPersist.GetMaxDateRatings(idCandidate, idCompany, portalId);

                        if (maxValue == maxCacheValue
                        && maxValue > DateTime.MinValue
                        && maxCacheValue > DateTime.MinValue)
                        {
                            return cache;
                        }
                    }
                }

                result = _ratingCvRecoverAndPersist.GetRatingsCvsByCompanyAndCandidate(idCandidate, idCompany, portalId);

                _tempCache.Add(key, result, new TimeSpan(0, 30, 0), portalId);
            }

            return result;
        }

        public bool InsertRatingCv(RatingCvEntity rating)
        {
            var result = false;

            if (IsCorrectRatingDataToInsert(rating))
            {
                result = _ratingCvRecoverAndPersist.InsertRatingCv(rating);
                _candidatesConsumerService.InsertInElastic(new List<int>() { rating.CandidateId }, rating.PortalId);

                var highRating = GetHighRatingByCandidate(rating.CandidateId, rating.CompanyId);

                if (highRating.Rating < rating.Rating
                  || highRating.UserId == 0)
                {
                    result = _ratingCvRecoverAndPersist.InsertHighestRatingCv(rating);
                }
            }

            return result;
        }


        public List<int> GetIdsByRatingCompanyAndOffer(short rating, int idOffer, int idCompany)
        {
            var result = new List<int>();

            if (idCompany > 0 && idOffer > 0)
            {
                var key = $"HIGHRATING_CV_COMPANY_{idCompany}_rating_{rating}_idoffer_{idOffer}";
                var cache = _tempCache.Get<Dictionary<int, RatingCvEntity>>(key);

                if (cache != null)
                {
                    var maxCacheValue = DateTime.MinValue;
                    var maxValue = DateTime.MinValue;

                    if (cache.Any())
                    {
                        maxCacheValue = cache.Values.Max(c => c.DateMod);
                        maxValue = _ratingCvRecoverAndPersist.GetMaxDateHighlightedRatingsAndOffer(idCompany, idOffer);
                    }

                    if (maxValue == maxCacheValue
                    && maxValue > DateTime.MinValue
                    && maxCacheValue > DateTime.MinValue)
                        return cache.Keys.ToList();
                }

                var resultWithDateMod = GetSpecificRatingValue(_ratingCvRecoverAndPersist.GetResultHighRatingsCvsByCompanyAndIdOffer(idCompany, idOffer), rating);

                if (resultWithDateMod.Any())
                {
                    _tempCache.Add(key, resultWithDateMod, new TimeSpan(0, 30, 0));
                    result = resultWithDateMod.Keys.ToList();
                }
            }

            return result;
        }


        public bool RemoveRatingCv(RatingCvEntity rating)
        {
            var result = false;

            if (IsCorrectRatingDataToDelete(rating))
            {
                result = _ratingCvRecoverAndPersist.RemoveRatingCv(rating);

                if (result)
                {
                    _candidatesConsumerService.InsertInElastic(new List<int>() { rating.CandidateId }, rating.PortalId);
                    var highRating = GetHighRatingByCandidate(rating.CandidateId, rating.CompanyId);

                    if (highRating != null
                        && highRating.UserId == 0)
                    {
                        _tempCache.Remove($"HIGHRATING_CV_COMPANY_{rating.CompanyId}");

                        var maxRating = _ratingCvRecoverAndPersist.GetMaxRating(rating.CandidateId, rating.CompanyId, rating.PortalId);

                        if (maxRating != null
                            && maxRating.Rating > 0)
                            _ratingCvRecoverAndPersist.InsertHighestRatingCv(maxRating);
                    }

                    _tempCache.Remove($"RATING_CV_COMPANY_{rating.CompanyId}_CANDIDATEID_{rating.CandidateId}");
                }
            }

            return result;
        }


        private static bool IsCorrectRatingDataToDelete(RatingCvEntity rating)
        {
            return rating != null
                          && rating.CandidateId > 0
                          && rating.CompanyId > 0
                          && rating.UserId > 0;
        }

        private static bool IsCorrectRatingDataToInsert(RatingCvEntity rating)
        {
            return rating != null
                    && rating.CandidateId > 0
                    && rating.CompanyId > 0
                    && rating.CvId > 0
                    && !string.IsNullOrEmpty(rating.NameAuthor)
                    && rating.UserId > 0
                    && rating.PortalId > 0
                    && rating.Rating > 0
                    && rating.Rating <= 5;
        }
    }
}
