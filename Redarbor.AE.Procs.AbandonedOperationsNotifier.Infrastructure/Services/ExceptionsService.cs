using Microsoft.Extensions.Logging;
using Redarbor.AE.Procs.AbandonedOperationsNotifier.Application.Abstractions;
using Redarbor.Tools.Exceptions.Consumer;
using System;
using System.Diagnostics;
using System.Threading.Tasks;

namespace Redarbor.AE.Procs.AbandonedOperationsNotifier.Infrastructure.Services
{
    public class ExceptionsService : IExceptionsService
    {
        private ILogger<IExceptionsService> _logger;

        private IExceptionsConsumerService _exceptionsConsumerService;

        public ExceptionsService(ILogger<IExceptionsService> logger, IExceptionsConsumerService exceptionsConsumerService)
        {
            _logger = logger;
            _exceptionsConsumerService = exceptionsConsumerService;
        }

        public async Task ProcessExceptionASync(Exception exception)
        {
            _logger.LogError(exception, "An error occurred while processing the abandoned operations:{message}", exception.Message);

            var caller = new StackTrace()?.GetFrame(1)?.GetMethod();
            await _exceptionsConsumerService.AddExceptionAsync(exception, caller);
        }
    }
}
