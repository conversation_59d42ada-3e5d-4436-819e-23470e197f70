using MySql.Data.MySqlClient;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Extensions.Library.Extensions;
using Redarbor.Master.Entities.Enums;
using Redarbor.Master.Entities.Offers;
using Redarbor.Master.Entities.Product;
using Redarbor.Repo.Offer.Library.Configuration;
using Redarbor.Repo.Offer.Library.DomainServiceContracts;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Linq;

namespace Redarbor.Repo.Offer.Library.DomainServicesImplementations
{
    public class OfferIntegratorRecoverAndPersist : IOfferIntegratorRecoverAndPersist
    {
        private readonly IRepoOfferConfiguration _repoOfferConfiguration;
        private readonly IExceptionPublisherService _exceptionPublisherService;

        public OfferIntegratorRecoverAndPersist(IRepoOfferConfiguration repoOfferConfiguration
                                              , IExceptionPublisherService exceptionPublisherService)
        {
            _repoOfferConfiguration = repoOfferConfiguration;
            _exceptionPublisherService = exceptionPublisherService;
        }

        public List<OfferIntegratorEntity> GetIntegratorsByOffer(int offerId, short portalId = 0)
        {
            try
            {
                using (var connection = new MySqlConnection(_repoOfferConfiguration.RepoOfferConnectionString))
                {
                    using (var command = new MySqlCommand("OfferIntegratorsGetByOffer", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.Add(new MySqlParameter("_idoffer", offerId));
                        connection.Open();

                        var integrators = new List<OfferIntegratorEntity>();
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                integrators.Add(new OfferIntegratorEntity
                                {
                                    Id = reader.GetAsInt("Id"),
                                    IdOffer = reader.GetAsInt("IdOffer"),
                                    IdIntegrator = reader.GetAsShort("IdIntegrator"),
                                    publicationdays = reader.GetAsShort("PublicationDays"),
                                    urlrewrite = reader.GetAsString("UrlRewrite"),
                                    createdon = reader.GetAsDateTime("CreatedOn"),
                                    createdby = reader.GetAsInt("CreatedBy"),
                                    updatedon = reader.GetAsDateTime("UpdatedOn"),
                                    deletedon = reader.GetAsDateTime("DeletedOn"),
                                    idstatus = reader.GetAsShort("IdStatus"),
                                    client_ip_add = reader.GetAsString("ClientIpAdd"),
                                    client_ip_mod = reader.GetAsString("ClientIpMod"),
                                    ishighlighted = reader.GetAsShort("IsHighlighted"),
                                    isurgent = reader.GetAsShort("IsUrgent"),
                                    Isflash = reader.GetAsShort("IsFlash"),
                                    IsPayment = reader.GetAsShort("IsPayment"),
                                    show_contact_email = reader.GetAsShort("ShowContactEmail"),
                                    contact_email = reader.GetAsString("ContactEmail"),
                                    show_phone_offer = reader.GetAsShort("ShowContactPhone"),
                                    contact_phone = reader.GetAsString("ContactPhone"),
                                    show_contact_address = reader.GetAsShort("ShowContactAddress"),
                                    contact_address = reader.GetAsString("ContactAddress"),
                                    TotalModeratedTimes = reader.GetAsInt("TotalModeratedTimes"),
                                    moderationtime = reader.GetAsDateTime("ModerationTime"),
                                    moderatedby = reader.GetAsInt("ModeratedBy"),
                                    idproduct = reader.GetAsInt("IdProduct"),
                                    idcompanyproduct = reader.GetAsInt("IdCompanyProduct"),
                                    publicationtime = reader.GetAsDateTime("PublicationTime"),
                                    expirationtime = reader.GetAsDateTime("ExpirationTime"),
                                    ExternalApplicationEmail = reader.GetAsString("ExternalApplicationEmail"),
                                    ExternalReference = reader.GetAsString("ExternalReference"),
                                    autopublished = reader.GetAsShort("AutoPublished"),
                                    RenewOfferStatus = reader.GetAsShort("RenewStatus"),
                                    total_applied = reader.GetAsInt("TotalApplied"),
                                    Hidden = reader.GetAsShort("Hidden")
                                });
                            }
                        }

                        return integrators;
                    }
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"OfferIntegratorRecoverAndPersist - GetIntegratorsByOffer {ex}");
                _exceptionPublisherService.PublishInCommon(ex, "OfferIntegratorRecoverAndPersist", "GetIntegratorsByOffer", null, portalId);
                throw;
            }
        }

        public int GetOfferIntegratorStatus(int offerId, int idIntegrator, short portalId)
        {
            try
            {
                using (var connection = new MySqlConnection(_repoOfferConfiguration.RepoOfferConnectionString))
                {
                    using (var command = new MySqlCommand("OfferIntegratorGetIdStatusByIdOfferIntegrator", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.Add(new MySqlParameter("_idoffer", offerId));
                        command.Parameters.Add(new MySqlParameter("_idintegrator", idIntegrator));

                        connection.Open();

                        return command.ExecuteScalar().ToInt();
                    }
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"OfferIntegratorRecoverAndPersist - GetOfferIntegratorStatus: {ex}");
                _exceptionPublisherService.PublishInCommon(ex, "OfferIntegratorRecoverAndPersist", "GetOfferIntegratorStatus", null, portalId);
                throw;
            }
        }

        public bool Insert(OfferIntegratorEntity offerIntegrator, short idPortal)
        {
            try
            {
                using (var connection = new MySqlConnection(_repoOfferConfiguration.RepoOfferConnectionString))
                {
                    using (var command = new MySqlCommand("OfferIntegratorsInsert_V2", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.Add(new MySqlParameter("_idoffer", offerIntegrator.IdOffer));
                        command.Parameters.Add(new MySqlParameter("_idIntegrator", offerIntegrator.IdIntegrator));
                        command.Parameters.Add(new MySqlParameter("_publicationDays", offerIntegrator.publicationdays));
                        command.Parameters.Add(new MySqlParameter("_urlRewrite", offerIntegrator.urlrewrite));
                        command.Parameters.Add(new MySqlParameter("_createdOn", offerIntegrator.createdon));
                        command.Parameters.Add(new MySqlParameter("_createdBy", offerIntegrator.createdby));
                        command.Parameters.Add(new MySqlParameter("_isPayment", offerIntegrator.IsPayment));
                        command.Parameters.Add(new MySqlParameter("_idStatus", offerIntegrator.idstatus));
                        command.Parameters.Add(new MySqlParameter("_clientIpAdd", offerIntegrator.client_ip_add));
                        command.Parameters.Add(new MySqlParameter("_isHighlighted", offerIntegrator.ishighlighted));
                        command.Parameters.Add(new MySqlParameter("_isUrgent", offerIntegrator.isurgent));
                        command.Parameters.Add(new MySqlParameter("_isFlash", offerIntegrator.Isflash));
                        command.Parameters.Add(new MySqlParameter("_showContactEmail", offerIntegrator.show_contact_email));
                        command.Parameters.Add(new MySqlParameter("_contactEmail", offerIntegrator.contact_email));
                        command.Parameters.Add(new MySqlParameter("_showContactPhone", offerIntegrator.show_phone_offer));
                        command.Parameters.Add(new MySqlParameter("_contactPhone", offerIntegrator.contact_phone));
                        command.Parameters.Add(new MySqlParameter("_showContactAddress", offerIntegrator.show_contact_address));
                        command.Parameters.Add(new MySqlParameter("_contactAddress", offerIntegrator.contact_address));
                        command.Parameters.Add(new MySqlParameter("_idProduct", offerIntegrator.idproduct));
                        command.Parameters.Add(new MySqlParameter("_idCompanyProduct", offerIntegrator.idcompanyproduct));
                        command.Parameters.Add(new MySqlParameter("_publicationTime", offerIntegrator.publicationtime));
                        command.Parameters.Add(new MySqlParameter("_expirationTime", offerIntegrator.expirationtime));
                        command.Parameters.Add(new MySqlParameter("_externalApplicationEmail", offerIntegrator.ExternalApplicationEmail));
                        command.Parameters.Add(new MySqlParameter("_externalReference", offerIntegrator.ExternalReference));
                        command.Parameters.Add(new MySqlParameter("_autoPublished", offerIntegrator.autopublished));
                        command.Parameters.Add(new MySqlParameter("_renewStatus", offerIntegrator.RenewOfferStatus));
                        command.Parameters.Add(new MySqlParameter("_idPortal", idPortal));
                        command.Parameters.Add(new MySqlParameter("_hidden", offerIntegrator.Hidden));
                        command.Parameters.Add(new MySqlParameter("_idGroup", offerIntegrator.IdGroup));

                        connection.Open();

                        command.ExecuteNonQuery();

                        return true;
                    }
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"OfferIntegratorRecoverAndPersist - Insert: {ex}");
                _exceptionPublisherService.PublishInCommon(ex, "OfferIntegratorRecoverAndPersist", "Insert");
                throw;
            }
        }

        public bool Update(OfferIntegratorEntity offerIntegrator, short idPortal)
        {
            try
            {
                using (var connection = new MySqlConnection(_repoOfferConfiguration.RepoOfferConnectionString))
                {
                    using (var command = new MySqlCommand("OfferIntegratorsUpdate_V2", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.Add(new MySqlParameter("_idoffer", offerIntegrator.IdOffer));
                        command.Parameters.Add(new MySqlParameter("_idIntegrator", offerIntegrator.IdIntegrator));
                        command.Parameters.Add(new MySqlParameter("_publicationDays", offerIntegrator.publicationdays));
                        command.Parameters.Add(new MySqlParameter("_urlRewrite", offerIntegrator.urlrewrite));
                        command.Parameters.Add(new MySqlParameter("_isPayment", offerIntegrator.IsPayment));
                        command.Parameters.Add(new MySqlParameter("_idStatus", offerIntegrator.idstatus));
                        command.Parameters.Add(new MySqlParameter("_clientIpMod", offerIntegrator.client_ip_mod));
                        command.Parameters.Add(new MySqlParameter("_isHighlighted", offerIntegrator.ishighlighted));
                        command.Parameters.Add(new MySqlParameter("_isUrgent", offerIntegrator.isurgent));
                        command.Parameters.Add(new MySqlParameter("_isFlash", offerIntegrator.Isflash));
                        command.Parameters.Add(new MySqlParameter("_showContactEmail", offerIntegrator.show_contact_email));
                        command.Parameters.Add(new MySqlParameter("_contactEmail", offerIntegrator.contact_email));
                        command.Parameters.Add(new MySqlParameter("_showContactPhone", offerIntegrator.show_phone_offer));
                        command.Parameters.Add(new MySqlParameter("_contactPhone", offerIntegrator.contact_phone));
                        command.Parameters.Add(new MySqlParameter("_showContactAddress", offerIntegrator.show_contact_address));
                        command.Parameters.Add(new MySqlParameter("_contactAddress", offerIntegrator.contact_address));
                        command.Parameters.Add(new MySqlParameter("_idProduct", offerIntegrator.idproduct));
                        command.Parameters.Add(new MySqlParameter("_idCompanyProduct", offerIntegrator.idcompanyproduct));
                        command.Parameters.Add(new MySqlParameter("_publicationTime", offerIntegrator.publicationtime));
                        command.Parameters.Add(new MySqlParameter("_expirationTime", offerIntegrator.expirationtime));
                        command.Parameters.Add(new MySqlParameter("_externalApplicationEmail", offerIntegrator.ExternalApplicationEmail));
                        command.Parameters.Add(new MySqlParameter("_externalReference", offerIntegrator.ExternalReference));
                        command.Parameters.Add(new MySqlParameter("_autoPublished", offerIntegrator.autopublished));
                        command.Parameters.Add(new MySqlParameter("_renewStatus", offerIntegrator.RenewOfferStatus));
                        command.Parameters.Add(new MySqlParameter("_idPortal", idPortal));
                        command.Parameters.Add(new MySqlParameter("_hidden", offerIntegrator.Hidden));
                        command.Parameters.Add(new MySqlParameter("_idGroup", offerIntegrator.IdGroup));

                        connection.Open();

                        command.ExecuteNonQuery();

                        return true;
                    }
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"OfferIntegratorRecoverAndPersist - Update: {ex}");
                _exceptionPublisherService.PublishInCommon(ex, "OfferIntegratorRecoverAndPersist", "Update");
                throw;
            }
        }

        public bool UpdateOfferHighlighted(int idoffer, short ishighlighted, short idportal, short idIntegrator)
        {
            try
            {
                using (var connection = new MySqlConnection(_repoOfferConfiguration.RepoOfferConnectionString))
                {
                    using (var command = new MySqlCommand("OfferIntegratorsUpdateHighlight", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.Add(new MySqlParameter("_idoffer", idoffer));
                        command.Parameters.Add(new MySqlParameter("_ishighlighted", ishighlighted));
                        command.Parameters.Add(new MySqlParameter("_idportal", idportal));
                        command.Parameters.Add(new MySqlParameter("_idintegrator", idIntegrator));

                        connection.Open();

                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"OfferIntegratorRecoverAndPersist - UpdateOfferHighlighted: {ex}");
                _exceptionPublisherService.PublishInCommon(ex, "OfferIntegratorRecoverAndPersist", "UpdateOfferHighlighted", null, idportal);
                throw;
            }

            return true;
        }

        public bool UpdateOfferUrgent(int idoffer, short isurgent, short idportal, short idIntegrator)
        {
            try
            {
                using (var connection = new MySqlConnection(_repoOfferConfiguration.RepoOfferConnectionString))
                {
                    using (var command = new MySqlCommand("OfferIntegratorsUpdateUrgent", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.Add(new MySqlParameter("_idoffer", idoffer));
                        command.Parameters.Add(new MySqlParameter("_isurgent", isurgent));
                        command.Parameters.Add(new MySqlParameter("_idportal", idportal));
                        command.Parameters.Add(new MySqlParameter("_idintegrator", idIntegrator));

                        connection.Open();
                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"OfferIntegratorRecoverAndPersist - UpdateOfferUrgent: {ex}");
                _exceptionPublisherService.PublishInCommon(ex, "OfferIntegratorRecoverAndPersist", "UpdateOfferUrgent", null, idportal);
                throw;
            }

            return true;
        }

        public bool UpdateOfferStatus(int idoffer, short idofferstatus, short idstatus, short idPortal, short idIntegrator)
        {
            try
            {
                using (var connection = new MySqlConnection(_repoOfferConfiguration.RepoOfferConnectionString))
                {
                    using (var command = new MySqlCommand("OfferIntegratorsUpdateStatusIntegrator", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.Add(new MySqlParameter("_idoffer", idoffer));
                        command.Parameters.Add(new MySqlParameter("_idportal", idPortal));
                        command.Parameters.Add(new MySqlParameter("_idintegrator", idIntegrator));
                        command.Parameters.Add(new MySqlParameter("_idstatus", idstatus));

                        connection.Open();
                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"OfferIntegratorRecoverAndPersist - UpdateOfferStatus: {ex}");
                _exceptionPublisherService.PublishInCommon(ex, "OfferIntegratorRecoverAndPersist", "UpdateOfferStatus");
                throw;
            }
            return true;
        }

        public bool UpdateProductIdAndProductCompanyId(int idoffer, int idProductNew, int companyProductIdNew, short idIntegrator, short idGroup, short productClass)
        {
            try
            {
                using (var connection = new MySqlConnection(_repoOfferConfiguration.RepoOfferConnectionString))
                {
                    using (var command = new MySqlCommand("OfferIntegratorsUpdateProductCompanyProduct_V2", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.Add(new MySqlParameter("_idoffer", idoffer));
                        command.Parameters.Add(new MySqlParameter("_idproductnew", idProductNew));
                        command.Parameters.Add(new MySqlParameter("_idcompanyproductnew", companyProductIdNew));
                        command.Parameters.Add(new MySqlParameter("_idintegrator", idIntegrator));
                        command.Parameters.Add(new MySqlParameter("_idGroup", idGroup));
                        command.Parameters.Add(new MySqlParameter("_productClass", productClass));
                        connection.Open();

                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"OfferIntegratorRecoverAndPersist - UpdateProductIdAndProductCompanyId: {ex}");
                _exceptionPublisherService.PublishInCommon(ex, "OfferIntegratorRecoverAndPersist", "UpdateProductIdAndProductCompanyId");
                throw;
            }

            return true;
        }

        public bool UpdateOfferToComplete(OfferEntity offer, OfferIntegratorEntity offerIntegrator, CompanyProductEntity companyProduct, short offerExpirationDays)
        {
            try
            {
                using (var conn = new MySqlConnection(_repoOfferConfiguration.RepoOfferConnectionString))
                {
                    using (var cmd = new MySqlCommand("ae_UpdateOfferToComplete_V3", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        cmd.Parameters.AddWithValue("_idoffer", offer.idoffer);
                        cmd.Parameters.AddWithValue("_ispayment", offerIntegrator.IsPayment);
                        cmd.Parameters.AddWithValue("_idcompanyproduct", companyProduct.Id);
                        cmd.Parameters.AddWithValue("_idproduct", companyProduct.ProductId);
                        cmd.Parameters.AddWithValue("_expirationdays", offerExpirationDays);
                        cmd.Parameters.AddWithValue("_idportal", offer.idportal);
                        cmd.Parameters.AddWithValue("_idintegrator", offerIntegrator.IdIntegrator);
                        cmd.Parameters.AddWithValue("_idstatus", offerIntegrator.idstatus);
                        cmd.Parameters.AddWithValue("_idGroup", companyProduct.GroupId);
                        cmd.Parameters.AddWithValue("_productClass", offer.ProductClass);

                        conn.Open();
                        cmd.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"OfferIntegratorRecoverAndPersist - UpdateToComplete: {ex}");
                _exceptionPublisherService.PublishInCommon(ex, "OfferIntegratorRecoverAndPersist", "UpdateOfferToComplete");
                throw;
            }

            return true;
        }

        public bool RenewOffer(int idOffer, DateTime newDateExpirationTime, OfferRenewStatusEnum newRenewOfferStatus, short idPortal, short idIntegrator,short idGroup,short productClass)
        {
            try
            {
                using (var conn = new MySqlConnection(_repoOfferConfiguration.RepoOfferConnectionString))
                {

                    conn.Open();

                    using (var command = new MySqlCommand("OfferIntegratorsRenew_V2", conn))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.Add(new MySqlParameter("_idoffer", idOffer));
                        command.Parameters.Add(new MySqlParameter("_expirationdays", newDateExpirationTime));
                        command.Parameters.Add(new MySqlParameter("_idportal", idPortal));
                        command.Parameters.Add(new MySqlParameter("_newrenewofferstatus", (short)newRenewOfferStatus));
                        command.Parameters.Add(new MySqlParameter("_idintegrator", idIntegrator));
                        command.Parameters.Add(new MySqlParameter("_idGroup", idGroup));
                        command.Parameters.Add(new MySqlParameter("_productClass", productClass));
                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"OfferIntegratorRecoverAndPersist - RenewOffer: {ex}");
                _exceptionPublisherService.PublishInCommon(ex, "OfferIntegratorRecoverAndPersist", "RenewOffer");
                throw;
            }

            return true;
        }

        public bool SetDateLastUp(int idOffer, short expirationDays, short idPortal, short idIntegrator)
        {
            try
            {
                using (var connection = new MySqlConnection(_repoOfferConfiguration.RepoOfferConnectionString))
                {
                    connection.Open();

                    using (var command = new MySqlCommand("OfferIntegratorsUpdateDateLastFromPostModeration", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.Add(new MySqlParameter("_idoffer", idOffer));
                        command.Parameters.Add(new MySqlParameter("_expirationdays", expirationDays));
                        command.Parameters.Add(new MySqlParameter("_idportal", idPortal));
                        command.Parameters.Add(new MySqlParameter("_idintegrator", idIntegrator));

                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"OfferIntegratorRecoverAndPersist - SetDateLastUp: {ex}");
                _exceptionPublisherService.PublishInCommon(ex, "OfferIntegratorRecoverAndPersist", "SetDateLastUp");
                throw;
            }

            return true;
        }

        public bool ReactivateOffer(int offerId, int companyId, int statusId, int offerStatusId, short portalId, short idIntegrator)
        {
            try
            {
                using (var connection = new MySqlConnection(_repoOfferConfiguration.RepoOfferConnectionString))
                {
                    connection.Open();

                    using (var command = new MySqlCommand("OfferIntegratorsReactivate", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.Add(new MySqlParameter("_idoffer", offerId));
                        command.Parameters.Add(new MySqlParameter("_idcompany", companyId));
                        command.Parameters.Add(new MySqlParameter("_idofferstatus", offerStatusId));
                        command.Parameters.Add(new MySqlParameter("_idstatus", statusId));
                        command.Parameters.Add(new MySqlParameter("_idportal", portalId));
                        command.Parameters.Add(new MySqlParameter("_idintegrator", idIntegrator));

                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"OfferIntegratorRecoverAndPersist - ReactivateOffer: {ex}");
                _exceptionPublisherService.PublishInCommon(ex, "OfferIntegratorRecoverAndPersist", "ReactivateOffer", null, portalId);
                throw;
            }

            return true;
        }

        public bool SetStatus(int offerId, int companyId, short idStatus, short portalId, short idIntegrator)
        {
            try
            {
                using (var connection = new MySqlConnection(_repoOfferConfiguration.RepoOfferConnectionString))
                {
                    connection.Open();

                    using (var command = new MySqlCommand("OfferIntegratorsChangeIdStatus", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.Add(new MySqlParameter("_idoffer", offerId));
                        command.Parameters.Add(new MySqlParameter("_idcompany", companyId));
                        command.Parameters.Add(new MySqlParameter("_idofferstatus", idStatus));
                        command.Parameters.Add(new MySqlParameter("_idportal", portalId));
                        command.Parameters.Add(new MySqlParameter("_idintegrator", idIntegrator));

                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"OfferIntegratorRecoverAndPersist - SetStatus: {ex}");
                _exceptionPublisherService.PublishInCommon(ex, "OfferIntegratorRecoverAndPersist", "SetStatus", null, portalId);
                throw;
            }

            return true;
        }

        public bool UpdateProductAndStatusOffer(int idOffer, int idCompany, short idPortal, short idOfferStatus, int idCompanyProduct, int idProduct, short idGroup, short productClass)
        {
            try
            {
                using (var connection = new MySqlConnection(_repoOfferConfiguration.RepoOfferConnectionString))
                {
                    connection.Open();

                    using (var command = new MySqlCommand("UpdateProductAndStatusOffer_V2", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.Add(new MySqlParameter("_idoffer", idOffer));
                        command.Parameters.Add(new MySqlParameter("_idcompany", idCompany));
                        command.Parameters.Add(new MySqlParameter("_idofferstatus", idOfferStatus));
                        command.Parameters.Add(new MySqlParameter("_idportal", idPortal));
                        command.Parameters.Add(new MySqlParameter("_idcompanyproduct", idCompanyProduct));
                        command.Parameters.Add(new MySqlParameter("_idproduct", idProduct));
                        command.Parameters.Add(new MySqlParameter("_idGroup", idGroup));
                        command.Parameters.Add(new MySqlParameter("_productClass", productClass));
                        command.Parameters.Add(new MySqlParameter("_idintegrator", (short)OfferIntegratorEnum.CompuTrabajo));

                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"OfferIntegratorRecoverAndPersist - SetPendingOffer: {ex}");
                _exceptionPublisherService.PublishInCommon(ex, "OfferIntegratorRecoverAndPersist", "UpdateProductAndStatusOffer");
                throw;
            }

            return true;
        }

        public List<OfferIntegratorEntity> GetIntegratorsByRangeOffers(int minIdOffer, int maxIdOffer)
        {
            try
            {
                var offersIntegratorEntity = new List<OfferIntegratorEntity>();

                using (var connection = new MySqlConnection(_repoOfferConfiguration.RepoOfferConnectionString))
                {
                    using (var command = new MySqlCommand("GetIntegratorsByRangeOffers", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.Add(new MySqlParameter("_minIdOffer", minIdOffer));
                        command.Parameters.Add(new MySqlParameter("_maxIdOffer", maxIdOffer));
                        connection.Open();

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                offersIntegratorEntity.Add(new OfferIntegratorEntity
                                {
                                    Id = reader.GetAsInt("Id"),
                                    IdOffer = reader.GetAsInt("IdOffer"),
                                    IdIntegrator = reader.GetAsShort("IdIntegrator"),
                                    publicationdays = reader.GetAsShort("PublicationDays"),
                                    urlrewrite = reader.GetAsString("UrlRewrite"),
                                    createdon = reader.GetAsDateTime("CreatedOn"),
                                    createdby = reader.GetAsInt("CreatedBy"),
                                    updatedon = reader.GetAsDateTime("UpdatedOn"),
                                    deletedon = reader.GetAsDateTime("DeletedOn"),
                                    idstatus = reader.GetAsShort("IdStatus"),
                                    client_ip_add = reader.GetAsString("ClientIpAdd"),
                                    client_ip_mod = reader.GetAsString("ClientIpMod"),
                                    ishighlighted = reader.GetAsShort("IsHighlighted"),
                                    isurgent = reader.GetAsShort("IsUrgent"),
                                    Isflash = reader.GetAsShort("IsFlash"),
                                    IsPayment = reader.GetAsShort("IsPayment"),
                                    show_contact_email = reader.GetAsShort("ShowContactEmail"),
                                    contact_email = reader.GetAsString("ContactEmail"),
                                    show_phone_offer = reader.GetAsShort("ShowContactPhone"),
                                    contact_phone = reader.GetAsString("ContactPhone"),
                                    show_contact_address = reader.GetAsShort("ShowContactAddress"),
                                    contact_address = reader.GetAsString("ContactAddress"),
                                    TotalModeratedTimes = reader.GetAsInt("TotalModeratedTimes"),
                                    moderationtime = reader.GetAsDateTime("ModerationTime"),
                                    moderatedby = reader.GetAsInt("ModeratedBy"),
                                    idproduct = reader.GetAsInt("IdProduct"),
                                    idcompanyproduct = reader.GetAsInt("IdCompanyProduct"),
                                    publicationtime = reader.GetAsDateTime("PublicationTime"),
                                    expirationtime = reader.GetAsDateTime("ExpirationTime"),
                                    ExternalApplicationEmail = reader.GetAsString("ExternalApplicationEmail"),
                                    ExternalReference = reader.GetAsString("ExternalReference"),
                                    autopublished = reader.GetAsShort("AutoPublished"),
                                    RenewOfferStatus = reader.GetAsShort("RenewStatus"),
                                    total_applied = reader.GetAsInt("TotalApplied"),
                                    IdGroup = reader.GetAsShort("IdGroup"),
                                });
                            }
                        }

                        return offersIntegratorEntity;
                    }
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"OfferIntegratorRecoverAndPersist - GetIntegratorsByRangeOffers {ex}");
                _exceptionPublisherService.PublishInCommon(ex, "OfferIntegratorRecoverAndPersist", "GetIntegratorsByRangeOffers");
                throw;
            }
        }

        public List<OfferIntegratorEntity> GetIntegratorsByOffers(List<int> idOffers, short idIntegrator = 0)
        {
            try
            {
                var offersIntegratorEntity = new List<OfferIntegratorEntity>();

                if (!idOffers.Any()) return offersIntegratorEntity;

                //var sCommand = " SELECT Id, IdOffer, IdIntegrator,PublicationDays,UrlRewrite,CreatedOn,CreatedBy,UpdatedOn,DeletedOn,IsPayment,IdStatus,ClientIpAdd,ClientIpMod,IsHighlighted,IsUrgent,IsFlash,ShowContactEmail,ContactEmail,ShowContactPhone,ContactPhone,ShowContactAddress,ContactAddress, TotalModeratedTimes,ModerationTime,ModeratedBy,IdProduct,IdCompanyProduct,PublicationTime,ExpirationTime,ExternalApplicationEmail,ExternalReference,AutoPublished,RenewStatus,TotalApplied, Hidden " +
                var sCommand = " SELECT Id, IdOffer, IdIntegrator,PublicationDays,UrlRewrite,CreatedOn,CreatedBy,UpdatedOn,DeletedOn,IsPayment,IdStatus,ClientIpAdd,ClientIpMod,IsHighlighted,IsUrgent,IsFlash,ShowContactEmail,ContactEmail,ShowContactPhone,ContactPhone,ShowContactAddress,ContactAddress, TotalModeratedTimes,ModerationTime,ModeratedBy,IdProduct,IdCompanyProduct,PublicationTime,ExpirationTime,ExternalApplicationEmail,ExternalReference,AutoPublished,RenewStatus,TotalApplied,IdGroup " +
                $" FROM OfferIntegrators WHERE IdOffer IN ({string.Join(", ", idOffers)}) ";


                if (idIntegrator != 0)
                    sCommand = sCommand + $" AND IdIntegrator = {idIntegrator}";

                using (var connection = new MySqlConnection(_repoOfferConfiguration.RepoOfferConnectionString))
                {
                    using (var command = new MySqlCommand(sCommand, connection))
                    {
                        command.CommandType = CommandType.Text;
                        connection.Open();

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                offersIntegratorEntity.Add(new OfferIntegratorEntity
                                {
                                    Id = reader.GetAsInt("Id"),
                                    IdOffer = reader.GetAsInt("IdOffer"),
                                    IdIntegrator = reader.GetAsShort("IdIntegrator"),
                                    publicationdays = reader.GetAsShort("PublicationDays"),
                                    urlrewrite = reader.GetAsString("UrlRewrite"),
                                    createdon = reader.GetAsDateTime("CreatedOn"),
                                    createdby = reader.GetAsInt("CreatedBy"),
                                    updatedon = reader.GetAsDateTime("UpdatedOn"),
                                    deletedon = reader.GetAsDateTime("DeletedOn"),
                                    idstatus = reader.GetAsShort("IdStatus"),
                                    client_ip_add = reader.GetAsString("ClientIpAdd"),
                                    client_ip_mod = reader.GetAsString("ClientIpMod"),
                                    ishighlighted = reader.GetAsShort("IsHighlighted"),
                                    isurgent = reader.GetAsShort("IsUrgent"),
                                    Isflash = reader.GetAsShort("IsFlash"),
                                    IsPayment = reader.GetAsShort("IsPayment"),
                                    show_contact_email = reader.GetAsShort("ShowContactEmail"),
                                    contact_email = reader.GetAsString("ContactEmail"),
                                    show_phone_offer = reader.GetAsShort("ShowContactPhone"),
                                    contact_phone = reader.GetAsString("ContactPhone"),
                                    show_contact_address = reader.GetAsShort("ShowContactAddress"),
                                    contact_address = reader.GetAsString("ContactAddress"),
                                    TotalModeratedTimes = reader.GetAsInt("TotalModeratedTimes"),
                                    moderationtime = reader.GetAsDateTime("ModerationTime"),
                                    moderatedby = reader.GetAsInt("ModeratedBy"),
                                    idproduct = reader.GetAsInt("IdProduct"),
                                    idcompanyproduct = reader.GetAsInt("IdCompanyProduct"),
                                    publicationtime = reader.GetAsDateTime("PublicationTime"),
                                    expirationtime = reader.GetAsDateTime("ExpirationTime"),
                                    ExternalApplicationEmail = reader.GetAsString("ExternalApplicationEmail"),
                                    ExternalReference = reader.GetAsString("ExternalReference"),
                                    autopublished = reader.GetAsShort("AutoPublished"),
                                    RenewOfferStatus = reader.GetAsShort("RenewStatus"),
                                    IdGroup = reader.GetAsShort("IdGroup")
                                });
                            }
                        }

                        return offersIntegratorEntity;
                    }
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"OfferIntegratorRecoverAndPersist - GetIntegratorsByOffers {ex}");
                _exceptionPublisherService.PublishInCommon(ex, "OfferIntegratorRecoverAndPersist", "GetIntegratorsByOffers");
                throw;
            }
        }

        public bool InsertOfferVersion(int offerId, short portalId)
        {
            try
            {
                using (var connection = new MySqlConnection(_repoOfferConfiguration.RepoOfferAuxConnectionString))
                {
                    using (var command = new MySqlCommand("AeOfferCTIntegratorVersionInsert", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        connection.Open();
                        command.Parameters.Add(new MySqlParameter("_idoffer", offerId));
                        command.Parameters.Add(new MySqlParameter("_idPortal", portalId));
                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.PublishInCommon(ex, "OfferIntegratorRecoverAndPersist", "InsertOfferVersion");
                return false;
            }

            return true;
        }

        public List<OfferIntegratorEntity> GetIntegratorsByBulkOffers(IEnumerable<int> offerIds)
        {
            var integrators = new List<OfferIntegratorEntity>();
            try
            {
                using (var connection = new MySqlConnection(_repoOfferConfiguration.RepoOfferConnectionString))
                {
                    using (var command = new MySqlCommand("OfferIntegratorsGetByBulkOffers_V2", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.Add(new MySqlParameter("_idintegrator", (short)OfferIntegratorEnum.CompuTrabajo));
                        command.Parameters.Add(new MySqlParameter("_offers_ids", string.Join(",", offerIds)));
                        connection.Open();

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                integrators.Add(new OfferIntegratorEntity
                                {
                                    Id = reader.GetAsInt("Id"),
                                    IdOffer = reader.GetAsInt("IdOffer"),
                                    IdIntegrator = reader.GetAsShort("IdIntegrator"),
                                    publicationdays = reader.GetAsShort("PublicationDays"),
                                    urlrewrite = reader.GetAsString("UrlRewrite"),
                                    createdon = reader.GetAsDateTime("CreatedOn"),
                                    createdby = reader.GetAsInt("CreatedBy"),
                                    updatedon = reader.GetAsDateTime("UpdatedOn"),
                                    deletedon = reader.GetAsDateTime("DeletedOn"),
                                    idstatus = reader.GetAsShort("IdStatus"),
                                    client_ip_add = reader.GetAsString("ClientIpAdd"),
                                    client_ip_mod = reader.GetAsString("ClientIpMod"),
                                    ishighlighted = reader.GetAsShort("IsHighlighted"),
                                    isurgent = reader.GetAsShort("IsUrgent"),
                                    Isflash = reader.GetAsShort("IsFlash"),
                                    IsPayment = reader.GetAsShort("IsPayment"),
                                    show_contact_email = reader.GetAsShort("ShowContactEmail"),
                                    contact_email = reader.GetAsString("ContactEmail"),
                                    show_phone_offer = reader.GetAsShort("ShowContactPhone"),
                                    contact_phone = reader.GetAsString("ContactPhone"),
                                    show_contact_address = reader.GetAsShort("ShowContactAddress"),
                                    contact_address = reader.GetAsString("ContactAddress"),
                                    TotalModeratedTimes = reader.GetAsInt("TotalModeratedTimes"),
                                    moderationtime = reader.GetAsDateTime("ModerationTime"),
                                    moderatedby = reader.GetAsInt("ModeratedBy"),
                                    idproduct = reader.GetAsInt("IdProduct"),
                                    idcompanyproduct = reader.GetAsInt("IdCompanyProduct"),
                                    publicationtime = reader.GetAsDateTime("PublicationTime"),
                                    expirationtime = reader.GetAsDateTime("ExpirationTime"),
                                    ExternalApplicationEmail = reader.GetAsString("ExternalApplicationEmail"),
                                    ExternalReference = reader.GetAsString("ExternalReference"),
                                    autopublished = reader.GetAsShort("AutoPublished"),
                                    RenewOfferStatus = reader.GetAsShort("RenewStatus"),
                                    total_applied = reader.GetAsInt("TotalApplied"),
                                    Hidden = reader.GetAsShort("Hidden"),
                                    IdGroup = reader.GetAsShort("IdGroup"),
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.PublishInCommon(ex, "OfferIntegratorRecoverAndPersist", "GetIntegratorsByBulkOffers");
            }

            return integrators;
        }

        public bool UpdateOfferFeatures(OfferFeaturesEntity offerFeatures)
        {
            try
            {
                using (var connection = new MySqlConnection(_repoOfferConfiguration.RepoOfferConnectionString))
                {
                    using (var command = new MySqlCommand("ae_UpdateOfferFeatures_v2", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.Add(new MySqlParameter("_idoffer", offerFeatures.IdOffer));
                        command.Parameters.Add(new MySqlParameter("_ishighlighted", offerFeatures.IsHighlighted));
                        command.Parameters.Add(new MySqlParameter("_isurgent", offerFeatures.IsUrgent));
                        command.Parameters.Add(new MySqlParameter("_isflash", offerFeatures.IsFlash));
                        command.Parameters.Add(new MySqlParameter("_flaghiddencompanyname", string.IsNullOrEmpty(offerFeatures.HiddenCompanyName) ? "0" : "1"));
                        command.Parameters.Add(new MySqlParameter("_hiddencompanyname", offerFeatures.HiddenCompanyName));
                        command.Parameters.Add(new MySqlParameter("_flagcontactaddress", string.IsNullOrEmpty(offerFeatures.ContactAddress) ? "0" : "1"));
                        command.Parameters.Add(new MySqlParameter("_contactaddress", offerFeatures.ContactAddress));
                        command.Parameters.Add(new MySqlParameter("_flagemail", string.IsNullOrEmpty(offerFeatures.Email) ? "0" : "1"));
                        command.Parameters.Add(new MySqlParameter("_email", offerFeatures.Email));
                        command.Parameters.Add(new MySqlParameter("_flagphone", string.IsNullOrEmpty(offerFeatures.Phone) ? "0" : "1"));
                        command.Parameters.Add(new MySqlParameter("_phone", offerFeatures.Phone));
                        command.Parameters.Add(new MySqlParameter("_idportal", offerFeatures.IdPortal));
                        command.Parameters.Add(new MySqlParameter("_haskillerquestions", offerFeatures.HasKillerQuestions));

                        connection.Open();

                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"OfferIntegratorRecoverAndPersist - UpdateOfferFeatures: {ex}");
                _exceptionPublisherService.PublishInCommon(ex, "OfferIntegratorRecoverAndPersist", "UpdateOfferFeatures");
                throw;
            }

            return true;
        }

        public void SetDeletedOn(int id, int offerId, short portalId)
        {
            try
            {
                using (var connection = new MySqlConnection(_repoOfferConfiguration.RepoOfferConnectionString))
                {
                    using (var command = new MySqlCommand("OfferIntegratorSetDeletedOn", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.Add(new MySqlParameter("_id", id));
                        command.Parameters.Add(new MySqlParameter("_offerId", offerId));

                        connection.Open();

                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"OfferIntegratorRecoverAndPersist - SetDeletedOn: {ex}");
                _exceptionPublisherService.PublishInCommon(ex, "OfferIntegratorRecoverAndPersist", "SetDeletedOn", null, portalId);
            }
        }

        public bool UpdatePostPublishOfferIntegrator(int idOffer, int idCompany, short idPortal, short idStatus, int idCompanyProduct, int idProduct, bool isPayment, short offerExpirationDays, short idGroup, short productClass)
        {
            try
            {
                using (var connection = new MySqlConnection(_repoOfferConfiguration.RepoOfferConnectionString))
                {
                    connection.Open();

                    using (var command = new MySqlCommand("ae_UpdatePostPublishOfferIntegrator_v3", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.Add(new MySqlParameter("_idoffer", idOffer));
                        command.Parameters.Add(new MySqlParameter("_idcompany", idCompany));
                        command.Parameters.Add(new MySqlParameter("_idofferstatus", idStatus));
                        command.Parameters.Add(new MySqlParameter("_idportal", idPortal));
                        command.Parameters.Add(new MySqlParameter("_idcompanyproduct", idCompanyProduct));
                        command.Parameters.Add(new MySqlParameter("_idproduct", idProduct));
                        command.Parameters.Add(new MySqlParameter("_idintegrator", (short)OfferIntegratorEnum.CompuTrabajo));
                        command.Parameters.Add(new MySqlParameter("_ispayment", isPayment));
                        command.Parameters.Add(new MySqlParameter("_offerexpirationdays", offerExpirationDays));
                        command.Parameters.Add(new MySqlParameter("_idGroup", idGroup));
                        command.Parameters.Add(new MySqlParameter("_productClass", productClass));

                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"OfferIntegratorRecoverAndPersist - UpdatePostPublishOfferIntegrator: {ex}");
                _exceptionPublisherService.PublishInCommon(ex, "OfferIntegratorRecoverAndPersist", "UpdatePostPublishOfferIntegrator");
                throw;
            }

            return true;
        }

        public bool UpdateOfferPostPublish(OfferFeaturesEntity offerFeatures, short productClass)
        {
            try
            {
                using (var connection = new MySqlConnection(_repoOfferConfiguration.RepoOfferConnectionString))
                {
                    connection.Open();

                    using (var command = new MySqlCommand("ae_UpdateOffer_V3", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.Add(new MySqlParameter("_idoffer", offerFeatures.IdOffer));
                        command.Parameters.Add(new MySqlParameter("_ishighlighted", offerFeatures.IsHighlighted));
                        command.Parameters.Add(new MySqlParameter("_isurgent", offerFeatures.IsUrgent));
                        command.Parameters.Add(new MySqlParameter("_isflash", offerFeatures.IsFlash));
                        command.Parameters.Add(new MySqlParameter("_flaghiddencompanyname", string.IsNullOrEmpty(offerFeatures.HiddenCompanyName) ? "0" : "1"));
                        command.Parameters.Add(new MySqlParameter("_hiddencompanyname", offerFeatures.HiddenCompanyName));
                        command.Parameters.Add(new MySqlParameter("_flagcontactaddress", string.IsNullOrEmpty(offerFeatures.ContactAddress) ? "0" : "1"));
                        command.Parameters.Add(new MySqlParameter("_contactaddress", offerFeatures.ContactAddress));
                        command.Parameters.Add(new MySqlParameter("_flagemail", string.IsNullOrEmpty(offerFeatures.Email) ? "0" : "1"));
                        command.Parameters.Add(new MySqlParameter("_email", offerFeatures.Email));
                        command.Parameters.Add(new MySqlParameter("_flagphone", string.IsNullOrEmpty(offerFeatures.Phone) ? "0" : "1"));
                        command.Parameters.Add(new MySqlParameter("_phone", offerFeatures.Phone));
                        command.Parameters.Add(new MySqlParameter("_haskillerquestions", offerFeatures.HasKillerQuestions));
                        command.Parameters.Add(new MySqlParameter("_idcompany", offerFeatures.IdCompany));
                        command.Parameters.Add(new MySqlParameter("_idofferstatus", offerFeatures.IdOfferStatus));
                        command.Parameters.Add(new MySqlParameter("_idportal", offerFeatures.IdPortal));
                        command.Parameters.Add(new MySqlParameter("_idcompanyproduct", offerFeatures.OfferIdCompanyProduct));
                        command.Parameters.Add(new MySqlParameter("_idproduct", offerFeatures.OfferIdProduct));
                        command.Parameters.Add(new MySqlParameter("_ispayment", offerFeatures.IsPayment));
                        command.Parameters.Add(new MySqlParameter("_expirationtime", offerFeatures.ExpirationTime));
                        command.Parameters.Add(new MySqlParameter("_idGroup", offerFeatures.OfferGroupToConsume));
                        command.Parameters.Add(new MySqlParameter("_productClass", productClass));

                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"OfferIntegratorRecoverAndPersist - UpdateOffer: {ex}");
                _exceptionPublisherService.PublishInCommon(ex, "OfferIntegratorRecoverAndPersist", "UpdateOfferPostPublish");
                throw;
            }

            return true;
        }
    }
}
