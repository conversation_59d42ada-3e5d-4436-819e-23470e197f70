using System;
using System.Diagnostics;
using Redarbor.Configuration.Library;
using Redarbor.Extensions.Library.DI;
using Redarbor.Extensions.Library.Exceptions;

namespace Redarbor.Repo.Offer.Library.Configuration
{
    [RegisterConfiguration]
    public class RepoOfferConfiguration : IRepoOfferConfiguration
    {
        private readonly RedarborEnvironment _currentConfig;

        public string RepoOfferConnectionString { get; private set; }
        public string RepoOfferAuxConnectionString { get; private set; }

        public RepoOfferConfiguration()
        {
            try
            {
                _currentConfig = LoadCurrentConfig();
                LoadCustomSettings();
            }

            catch (Exception anyException)
            {
                Trace.TraceError("Error initializing configuration in Redarbor.Repo.Offer.Library: " + anyException.Message);
                throw new ConfigurationInitializationException("Error initializing configuration in Redarbor.Repo.Offer.Library: " + anyException.Message, anyException);
            }
        }

        private static RedarborEnvironment LoadCurrentConfig()
        {
            if (!RedarborEnvironment.IsInitialized)
            {
                Trace.TraceError("Not initialized in Redarbor.Repo.Offer.Library");
                throw new ConfigurationInitializationException("Not initialized in Redarbor.Repo.Offer.Library.Configuration.");
            }

            return RedarborEnvironment.Current;
        }

        private void LoadCustomSettings()
        {
            RepoOfferConnectionString = RedarborEnvironment.Current.GetDataConnectionString("Repo_Offers");
            RepoOfferAuxConnectionString = RedarborEnvironment.Current.GetDataConnectionString("Repo_Offers_Aux");
        }
    }
}