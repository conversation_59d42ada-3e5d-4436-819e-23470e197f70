using MySql.Data.MySqlClient;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Core.Resolver.Contracts.ServiceLibrary;
using Redarbor.Core.Resolver.Contracts.ServiceLibrary.Enums;
using Redarbor.Extensions.Library.Extensions;
using Redarbor.Master.Entities.Product;
using Redarbor.Products.Library.DomainServiceContracts;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;

namespace Redarbor.Products.Library.DomainServicesImplementations
{
    public class ProductSubGroupsRecoverAndPersist : IProductSubGroupsRecoverAndPersist
    {
        private readonly IConnectionStringResolverService _connectionStringResolverService;
        private readonly IExceptionPublisherService _exceptionPublisherService;

        public ProductSubGroupsRecoverAndPersist(IConnectionStringResolverService connectionStringResolverService,
            IExceptionPublisherService exceptionPublisherService)
        {
            _connectionStringResolverService = connectionStringResolverService;
            _exceptionPublisherService = exceptionPublisherService;
        }

        public List<ProductSubGroupsEntity> Get(short portalId)
        {
            var productsSubGroups = new List<ProductSubGroupsEntity>();
            try
            {
                using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
                {
                    using (var command = new MySqlCommand("sp_productSubGroupsGet", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.Add(new MySqlParameter("_portalId", portalId));
                        connection.Open();

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader != null && reader.Read())
                            {
                                productsSubGroups.Add(new ProductSubGroupsEntity()
                                {
                                    Id = reader.GetAsInt("Id"),
                                    PortalId = reader.GetAsShort("PortalId"),
                                    Name = reader.GetAsString("Name")
                                });
                            }
                        }
                    }
                }
                return productsSubGroups;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"ProductSubGroupsRecoverAndPersist - Get {ex}");
                _exceptionPublisherService.Publish(ex, "ProductSubGroupsRecoverAndPersist", "Get()");
                return productsSubGroups;
            }
        }
    }
}
