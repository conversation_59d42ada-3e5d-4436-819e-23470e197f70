using MySql.Data.MySqlClient;
using MySqlX.XDevAPI.Common;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Core.Resolver.Contracts.ServiceLibrary;
using Redarbor.Core.Resolver.Contracts.ServiceLibrary.Enums;
using Redarbor.Extensions.Library.Extensions;
using Redarbor.Master.Entities.Cv;
using Redarbor.Master.Entities.Enums;
using Redarbor.Master.Entities.Product;
using Redarbor.Products.Contracts.ServiceLibrary.DTO;
using Redarbor.Products.Library.Configuration;
using Redarbor.Products.Library.DomainServiceContracts;
using Redarbor.Products.Library.DTOs;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Threading.Tasks;

namespace Redarbor.Products.Library.DomainServicesImplementations
{
    public class CompanyProductRecoverAndPersist : ICompanyProductRecoverAndPersist
    {
        private readonly IConnectionStringResolverService _connectionStringResolverService;
        private readonly IProductConfiguration _productConfiguration;
        private readonly IExceptionPublisherService _exceptionPublisherService;

        public CompanyProductRecoverAndPersist(IConnectionStringResolverService connectionStringResolverService,
            IProductConfiguration productConfiguration,
            IExceptionPublisherService exceptionPublisherService)
        {
            _connectionStringResolverService = connectionStringResolverService;
            _productConfiguration = productConfiguration;
            _exceptionPublisherService = exceptionPublisherService;
        }

        public int GetCompanyProductIdByCompanyId(int companyId, short portalId)
        {
            int companyProductId = 0;

            using (var conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
            {
                using (var cmd = new MySqlCommand("get_companyproductId_By_IdCompany", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;

                    try
                    {
                        cmd.Parameters.Add(new MySqlParameter("_company_id", companyId));
                        cmd.Parameters.Add(new MySqlParameter("_idportal", portalId));

                        conn.Open();
                        companyProductId = cmd.ExecuteScalar().ToInt();
                    }
                    catch (Exception ex)
                    {
                        conn.Close();
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "GetCompanyProductIdByCompanyId", false, null, portalId);
                    }
                }
            }

            return companyProductId;
        }

        public int GetCompanyProductIdLastMembresyExpired(int companyId, short portalId)
        {
            int companyProductId = 0;

            using (var conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
            {
                using (var cmd = new MySqlCommand("ae_getCompanyProductsIdLastMembresyExpired", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;

                    try
                    {
                        cmd.Parameters.Add(new MySqlParameter("_idcompany", companyId));
                        cmd.Parameters.Add(new MySqlParameter("_idportal", portalId));

                        conn.Open();
                        companyProductId = cmd.ExecuteScalar().ToInt();
                    }
                    catch (Exception ex)
                    {
                        conn.Close();
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "GetCompanyProductIdLastMembresyExpired", false, null, portalId);
                    }
                }
            }

            return companyProductId;
        }

        public List<CompanyProductEntity> SelectByCompany(int idCompany, int portalId, int type)
        {
            using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, (short)portalId)))
            {
                using (var command = new MySqlCommand("gl_company_products_select_by_company_v6", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_company_id", idCompany));
                    command.Parameters.Add(new MySqlParameter("_portal_id", portalId));
                    command.Parameters.Add(new MySqlParameter("_typeId", type));

                    try
                    {
                        var products = new List<CompanyProductEntity>();

                        connection.Open();

                        using (var reader = command.ExecuteReader())
                        {
                            if (reader != null)
                            {
                                while (reader.Read())
                                {
                                    products.Add(new CompanyProductEntity
                                    {
                                        Id = reader.GetAsInt("id"),
                                        ProductId = reader.GetAsInt("product_id"),
                                        IdCompany = reader.GetAsInt("company_id"),
                                        PortalId = reader.GetAsShort("portal_id"),
                                        DateAdd = reader.GetAsDateTime("date_add"),
                                        DateMod = reader.GetAsDateTime("date_mod"),
                                        IdUser = reader.GetAsInt("user_id"),
                                        Price = reader.GetAsFloat("price"),
                                        ServiceTypeId = reader.GetAsShort("service_type_id"),
                                        SourceId = reader.GetAsShort("source_id"),
                                        PaymentOriginId = reader.GetAsShort("payment_origin_id"),
                                        DateExpiration = reader.GetAsDateTime("date_expiration"),
                                        DateActivation = reader.GetAsDateTime("date_activation"),
                                        ExpirationDays = reader.GetAsInt("expiration_days"),
                                        StatusId = reader.GetAsShort("status_id"),
                                        GroupId = reader.GetAsShort("group_id"),
                                        ClientIP = reader.GetAsString("client_ip"),
                                        LimitationMultiplier = reader.GetAsShort("limitation_multiplier"),
                                        SubGroupId = reader.GetAsShort("subgroup_id"),
                                        SelfRenewing = reader.GetAsBoolean("self_renewing"),
                                        TemporalityId = reader.GetAsShort("temporality_id")
                                    });
                                }
                            }
                        }
                        return products;
                    }
                    catch (Exception ex)
                    {
                        connection.Close();
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "TrySelectByCompanyFromDB", false, null, (short)portalId);
                        return new List<CompanyProductEntity>();
                    }
                }
            }
        }

        public DateTime SelectCompanyProductDateMod(int idCompany, int type, short portalId = 0)
        {
            using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
            {
                using (var command = new MySqlCommand("ae_company_product_get_date_last_update_v3", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_idcompany", idCompany));
                    command.Parameters.Add(new MySqlParameter("_typeId", type));

                    try
                    {
                        connection.Open();

                        return command.ExecuteScalar().ToDateTime();
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "SelectCompanyProductDateMod", false, null, portalId);
                        return DateTime.MinValue;
                    }
                }
            }
        }

        public DateTime SelectLastDateModProductFeatureUserByCompanyAndFeature(int idCompany, int idAmbit, short idPortal)
        {
            using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, idPortal)))
            {
                using (var command = new MySqlCommand("selectlastdatemodproductfeatureuserbycompanyandfeature", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_idcompany", idCompany));
                    command.Parameters.Add(new MySqlParameter("_idambit", idAmbit));

                    try
                    {
                        connection.Open();

                        return command.ExecuteScalar().ToDateTime();
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "SelectLastDateModProductFeatureUserByCompanyAndFeature", false, null, idPortal);
                        return DateTime.MinValue;
                    }
                }
            }
        }

        public DateTime InitDemo(int idCompanyProduct, int idCompany, short portalId)
        {
            using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
            {
                using (var command = new MySqlCommand("compuadvisor_start_demo", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_company_id", idCompany));
                    command.Parameters.Add(new MySqlParameter("_company_product_id", idCompanyProduct));
                    command.Parameters.Add(new MySqlParameter("_portal_id", portalId));

                    try
                    {
                        connection.Open();

                        return command.ExecuteScalar().ToDateTime();
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "InitDemo", false, null, portalId);
                        return DateTime.MinValue;
                    }
                }
            }
        }

        public Tuple<int, DateTime> GetOnlyAllUnitsShareWithUsers(int idCompany, int ambitId, short portalId)
        {
            using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
            {
                using (var command = new MySqlCommand("getonlyallunitssharewithusers", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_idcompany", idCompany));
                    command.Parameters.Add(new MySqlParameter("_idambit", ambitId));

                    try
                    {
                        connection.Open();

                        using (var reader = command.ExecuteReader())
                        {
                            if (reader == null)
                            {
                                return new Tuple<int, DateTime>(0, DateTime.MinValue);
                            }

                            reader.Read();

                            return new Tuple<int, DateTime>(reader.GetAsInt("counterunits"),
                                                     reader.GetAsDateTime("maxdatemod"));

                        }
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "GetOnlyAllUnitsShareWithUsers", false, null, portalId);
                        return new Tuple<int, DateTime>(0, DateTime.MinValue);
                    }
                }
            }
        }

        public List<CompanyProductEntity> SelectOldProducts(int idCompany, int portalId)
        {
            using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, (short)portalId)))
            {
                using (var command = new MySqlCommand("ae_company_products_select_previous_product", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_company_id", idCompany));
                    command.Parameters.Add(new MySqlParameter("_portal_id", portalId));

                    try
                    {
                        connection.Open();
                        var products = new List<CompanyProductEntity>();

                        using (var reader = command.ExecuteReader())
                        {

                            if (reader != null)
                            {
                                while (reader.Read())
                                {
                                    products.Add(
                                                 new CompanyProductEntity
                                                 {
                                                     Id = reader.GetAsInt("id"),
                                                     ProductId = reader.GetAsInt("product_id"),
                                                     IdCompany = reader.GetAsInt("company_id"),
                                                     PortalId = reader.GetAsShort("portal_id"),
                                                     DateAdd = reader.GetAsDateTime("date_add"),
                                                     DateMod = reader.GetAsDateTime("date_mod"),
                                                     IdUser = reader.GetAsInt("user_id"),
                                                     Price = reader.GetAsFloat("price"),
                                                     ServiceTypeId = reader.GetAsShort("service_type_id"),
                                                     SourceId = reader.GetAsShort("source_id"),
                                                     PaymentOriginId = reader.GetAsShort("payment_origin_id"),
                                                     DateActivation = reader.GetAsDateTime("date_activation"),
                                                     DateExpiration = reader.GetAsDateTime("date_expiration"),
                                                     ExpirationDays = reader.GetAsInt("expiration_days"),
                                                     StatusId = reader.GetAsShort("status_id"),
                                                     GroupId = reader.GetAsShort("group_id"),
                                                     ClientIP = reader.GetAsString("client_ip")
                                                 });
                                }
                            }
                        }
                        return products;
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "SelectOldProduct", false, null, (short)portalId);
                        return new List<CompanyProductEntity>();
                    }
                }
            }
        }

        public bool Consume(CompanyProductEntity companyProduct, int ambit)
        {
            using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, companyProduct.PortalId)))
            {
                using (var command = new MySqlCommand("company_products_features_consume", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_idCompany", companyProduct.IdCompany));
                    command.Parameters.Add(new MySqlParameter("_ambit", ambit));
                    command.Parameters.Add(new MySqlParameter("_idportal", companyProduct.PortalId));

                    try
                    {
                        connection.Open();
                        command.ExecuteNonQuery();

                        return true;
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "Consume", false, null, companyProduct.PortalId);
                        return false;
                    }
                }
            }
        }

        public bool UpdateDateMod(int idCompanyProduct, short portalId)
        {
            using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
            {
                using (var command = new MySqlCommand("sp_company_products_update_datemod", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_id", idCompanyProduct));
                    command.Parameters.Add(new MySqlParameter("_idportal", portalId));

                    try
                    {
                        connection.Open();
                        command.ExecuteNonQuery();
                        return true;
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "UpdateDateMod", false, null, portalId);
                    }
                }
            }

            return false;
        }

        public List<ProductInitialOffersEntity> GetProductInitialOffers(short portalId)
        {
            try
            {
                using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
                {
                    using (var command = new MySqlCommand("ae_get_IdProducts_Sorted_By_NumOffers", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.Add(new MySqlParameter("_idportal", portalId));

                        connection.Open();

                        var listProductsSortedByNumOffers = new List<ProductInitialOffersEntity>();
                        using (var datareader = command.ExecuteReader())
                        {
                            if (datareader != null)
                            {
                                while (datareader.Read())
                                {
                                    listProductsSortedByNumOffers.Add(new ProductInitialOffersEntity
                                    {
                                        ProductId = datareader.GetAsInt("product_id"),
                                        NumOffers = datareader.GetAsInt("initial_units")
                                    });
                                }
                            }
                        }

                        return listProductsSortedByNumOffers;
                    }
                }
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "GetProductInitialOffers", false, null, portalId);
                return new List<ProductInitialOffersEntity>();
            }
        }

        public List<int> GetSuperiorProducts(short idPortal, int idProduct)
        {
            try
            {
                using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.MasterAux)))
                {
                    using (var command = new MySqlCommand("ae_pro_product_superior_get", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.Add(new MySqlParameter("_portal_id", idPortal));
                        command.Parameters.Add(new MySqlParameter("_product_id", idProduct));

                        connection.Open();

                        var returnList = new List<int>();
                        using (var datareader = command.ExecuteReader())
                        {
                            if (datareader != null)
                            {
                                while (datareader.Read())
                                {
                                    returnList.Add(datareader.GetInt32("superior_id"));
                                }
                            }
                        }

                        return returnList;
                    }
                }
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "GetSuperiorProducts");
                return new List<int>();
            }
        }

        public int GetDaysToExpire(int companyId, int productId, short portalId)
        {
            var daysToExpire = 0;
            DateTime expirationDate = DateTime.MinValue;
            using (MySqlConnection conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
            {
                using (MySqlCommand command = new MySqlCommand("sp_company_product_get_days_to_expire", conn))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    try
                    {
                        command.Parameters.Add(new MySqlParameter("_company_id", companyId));
                        command.Parameters.Add(new MySqlParameter("_product_id", productId));
                        command.Parameters.Add(new MySqlParameter("_portal_id", portalId));

                        conn.Open();


                        expirationDate = command.ExecuteScalar().ToDateTime();

                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "GetDaysToExpire");
                        conn.Close();
                    }
                }

                if (expirationDate != null && expirationDate != DateTime.MinValue)
                    daysToExpire = expirationDate.Subtract(DateTime.Now).Days;

                return daysToExpire;
            }
        }

        public int AddProduct(int companyId, long userId, short portalId, int productId, int paymentOriginId, int productSourceId, float price, DateTime activationDate, DateTime expirationDate, string clientIp, int temporalityId = (int)TemporalityEnum.Year)
        {
            var id = 0;
            using (MySqlConnection conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
            {
                using (MySqlCommand command = new MySqlCommand("ae_company_product_productfeature_insert", conn))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    try
                    {
                        command.Parameters.Add(new MySqlParameter("_company_id", companyId));
                        command.Parameters.Add(new MySqlParameter("_user_id", userId));
                        command.Parameters.Add(new MySqlParameter("_portal_id", portalId));
                        command.Parameters.Add(new MySqlParameter("_product_id", productId));
                        command.Parameters.Add(new MySqlParameter("_payment_origin_id", paymentOriginId));
                        command.Parameters.Add(new MySqlParameter("_source_id", productSourceId));
                        command.Parameters.Add(new MySqlParameter("_price", price));
                        command.Parameters.Add(new MySqlParameter("_activation_date", activationDate));
                        command.Parameters.Add(new MySqlParameter("_expiration_date", expirationDate));
                        command.Parameters.Add(new MySqlParameter("_client_ip", clientIp));
                        command.Parameters.Add(new MySqlParameter("_idtemporality", temporalityId));

                        conn.Open();

                        var result = command.ExecuteScalar();

                        if (result != null)
                        {
                            Int32.TryParse(result.ToString(), out id);
                        }
                    }
                    catch (Exception ex)
                    {
                        Trace.TraceError($"CompanyProductRecoverAndPersist - AddProduct: {ex.ToString()}");
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "AddProduct");
                        conn.Close();
                    }

                }
                return id;
            }

        }

        public bool UpdateIsPayment(int companyId, short portalId)
        {
            using (MySqlConnection conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
            {
                using (var command = new MySqlCommand("sp_dtcompany_SetIsPayment_Portal", conn))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    try
                    {
                        command.Parameters.Add(new MySqlParameter("_idcompany", companyId));
                        command.Parameters.Add(new MySqlParameter("_idportal", portalId));

                        conn.Open();
                        command.ExecuteNonQuery();
                    }
                    catch (Exception ex)
                    {
                        Trace.TraceError($"CompanyProductRecoverAndPersist - UpdateIsPayment: {ex.ToString()}");
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "UpdateIsPayment");
                        conn.Close();
                        return false;
                    }
                }
            }
            return true;
        }

        public bool UpdateOfferCompanyProductId(short portalId, int productId, int companyId, int companyProductId, short idIntegrator, short idGroup, short productClass)
        {
            using (var conn = new MySqlConnection(_productConfiguration.RepoOfferConnectionString))
            {
                using (var command = new MySqlCommand("OfferIntegratorsUpdateProductCompanyByCompany_V2", conn))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    try
                    {
                        command.Parameters.Add(new MySqlParameter("_idportal", portalId));
                        command.Parameters.Add(new MySqlParameter("_idProduct", productId));
                        command.Parameters.Add(new MySqlParameter("_idIntegrator", idIntegrator));
                        command.Parameters.Add(new MySqlParameter("_idCompanyProduct", companyProductId));
                        command.Parameters.Add(new MySqlParameter("_isPayment", 1));
                        command.Parameters.Add(new MySqlParameter("_idcompany", companyId));
                        command.Parameters.Add(new MySqlParameter("_idGroup", idGroup));
                        command.Parameters.Add(new MySqlParameter("_productClass", productClass));
                        conn.Open();
                        command.ExecuteNonQuery();
                    }
                    catch (Exception ex)
                    {
                        Trace.TraceError($"CompanyProductRecoverAndPersist - UpdateOfferCompanyProductId: {ex}");
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "UpdateOfferCompanyProductId");
                        return false;
                    }
                }
            }

            return true;
        }

        public List<int> UpdatePaymentAndHighlightedCompanyOffersAndGetIdOffersToStack(int companyId, short portalId, bool ishighlighted, short idIntegrator)
        {
            var offerIds = new List<int>();

            using (var conn = new MySqlConnection(_productConfiguration.RepoOfferConnectionString))
            {
                using (var command = new MySqlCommand("OfferGetOfferIdsByCompanyIdAndUpdateIsPaymentHighlighted", conn))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    try
                    {
                        command.Parameters.Add(new MySqlParameter("_companyid", companyId));
                        command.Parameters.Add(new MySqlParameter("_portalid", portalId));
                        command.Parameters.Add(new MySqlParameter("_ishighlighted", ishighlighted));
                        command.Parameters.Add(new MySqlParameter("_idIntegrator", idIntegrator));

                        conn.Open();

                        using (var dataReader = command.ExecuteReader())
                        {
                            while (dataReader.Read())
                            {
                                offerIds.Add(dataReader.GetAsInt("Id"));
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Trace.TraceError($"CompanyProductRecoverAndPersist - UpdatePaymentAndHighlightedCompanyOffersAndGetIdOffersToStack: {ex}");
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "UpdatePaymentAndHighlightedCompanyOffersAndGetIdOffersToStack", false, null, portalId);
                    }
                }
            }

            return offerIds;
        }

        public CompanyProductEntity GetByCompanyProductId(int companyProductId, int idCompany, short idportal)
        {
            try
            {
                using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, idportal)))
                {
                    using (var command = new MySqlCommand("sp_company_product_get_by_company_prod_id_v2", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.Add(new MySqlParameter("_company_product_id", companyProductId));
                        command.Parameters.Add(new MySqlParameter("_company_id", idCompany));
                        command.Parameters.Add(new MySqlParameter("_portal_id", idportal));

                        connection.Open();

                        var companyProduct = new CompanyProductEntity();
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                companyProduct = new CompanyProductEntity
                                {
                                    Id = reader.GetAsInt("id"),
                                    ProductId = reader.GetAsInt("product_id"),
                                    IdCompany = reader.GetAsInt("company_id"),
                                    PortalId = reader.GetAsShort("portal_id"),
                                    DateAdd = reader.GetAsDateTime("date_add"),
                                    DateMod = reader.GetAsDateTime("date_mod"),
                                    IdUser = reader.GetAsInt("user_id"),
                                    Price = reader.GetAsFloat("price"),
                                    ServiceTypeId = reader.GetAsShort("service_type_id"),
                                    SourceId = reader.GetAsShort("source_id"),
                                    PaymentOriginId = reader.GetAsShort("payment_origin_id"),
                                    DateActivation = reader.GetAsDateTime("date_activation"),
                                    DateExpiration = reader.GetAsDateTime("date_expiration"),
                                    ExpirationDays = reader.GetAsInt("expiration_days"),
                                    StatusId = reader.GetAsShort("status_id"),
                                    GroupId = reader.GetAsShort("group_id"),
                                    ClientIP = reader.GetAsString("client_ip"),
                                    SubGroupId = reader.GetAsShort("subgroup_id"),
                                    LimitationMultiplier = reader.GetAsShort("limitation_multiplier")
                                };
                            }
                        }

                        return companyProduct;
                    }
                }
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "GetProductByCompanyProductId", false, null, idportal);
                return new CompanyProductEntity();
            }
        }

        public bool ConsumeByUser(CompanyProductEntity companyProduct, int ambit, long idUser)
        {
            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, companyProduct.PortalId)))
            {
                using (MySqlCommand command = new MySqlCommand("company_products_features_user_consume", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_idcompany", companyProduct.IdCompany));
                    command.Parameters.Add(new MySqlParameter("_idambit", ambit));
                    command.Parameters.Add(new MySqlParameter("_idportal", companyProduct.PortalId));
                    command.Parameters.Add(new MySqlParameter("_iduser", idUser));

                    try
                    {
                        connection.Open();

                        command.ExecuteNonQuery();

                        return true;
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "ConsumeByUser", false, null, companyProduct.PortalId);

                        return false;
                    }
                }
            }
        }

        public DateTime SelectCompanyOfferLastModifiedByIdUser(int idCompany, long idUser, short idPortal)
        {
            using (MySqlConnection connection = new MySqlConnection(_productConfiguration.RepoOfferConnectionString))
            {
                using (MySqlCommand command = new MySqlCommand("OfferGetLastUpdateByIdCompanyIdUser", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_idcompany", idCompany));
                    command.Parameters.Add(new MySqlParameter("_iduser", idUser));
                    command.Parameters.Add(new MySqlParameter("_idportal", idPortal));

                    try
                    {
                        connection.Open();

                        return command.ExecuteScalar().ToDateTime();
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "SelectCompanyOfferLastModifiedByIdUser", false, null, idPortal);

                        return DateTime.MinValue;
                    }
                }
            }
        }

        public DateTime SelectCompanyOfferLastModified(int idCompany, short idPortal, OfferIntegratorEnum offerIntegratorEnum)
        {
            using (MySqlConnection connection = new MySqlConnection(_productConfiguration.RepoOfferConnectionString))
            {
                using (MySqlCommand command = new MySqlCommand("OfferGetLastUpdateByIdCompanyIntegrator", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_idcompany", idCompany));
                    command.Parameters.Add(new MySqlParameter("_idportal", idPortal));
                    command.Parameters.Add(new MySqlParameter("_idintegrator", (short)offerIntegratorEnum));

                    try
                    {
                        connection.Open();

                        return command.ExecuteScalar().ToDateTime();
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "SelectCompanyOfferLastModified", false, null, idPortal);

                        return DateTime.MinValue;
                    }
                }
            }
        }

        public bool InsertCvViewedByCompany(CompanyCvViewedEntity companyCvViewedEntity)
        {
            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.ReadMaster, companyCvViewedEntity.IdPortal)))
            {
                using (MySqlCommand command = new MySqlCommand("ae_company_product_cv_insert_v4", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    try
                    {
                        command.Parameters.Add(new MySqlParameter("_company_id", companyCvViewedEntity.IdCompany));
                        command.Parameters.Add(new MySqlParameter("_cv_id", companyCvViewedEntity.IdCv));
                        command.Parameters.Add(new MySqlParameter("_candidate_id", companyCvViewedEntity.IdCandidate));
                        command.Parameters.Add(new MySqlParameter("_user_id", companyCvViewedEntity.IdUser));
                        command.Parameters.Add(new MySqlParameter("_offer_id", companyCvViewedEntity.IdOffer));
                        command.Parameters.Add(new MySqlParameter("_portal_id", companyCvViewedEntity.IdPortal));
                        command.Parameters.Add(new MySqlParameter("_product_group_id", companyCvViewedEntity.IdProductGroup));
                        command.Parameters.Add(new MySqlParameter("_company_product_id", companyCvViewedEntity.IdCompanyProduct));

                        connection.Open();

                        command.ExecuteNonQuery();
                    }
                    catch (Exception ex)
                    {
                        connection.Close();
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "TryInsertCvViewedByCompany", false, null, companyCvViewedEntity.IdPortal);
                        return false;
                    }
                }
            }
            return true;
        }

        public List<CompanyProductEntity> GetPackByCompanySubGroup(int idcompany, short portalId, short subGroup)
        {
            var result = new List<CompanyProductEntity>();

            try
            {
                using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
                {
                    using (var command = new MySqlCommand("companyProductsGetPacksBySubGroup", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.Add(new MySqlParameter("_subGroup", subGroup));
                        command.Parameters.Add(new MySqlParameter("_companyId", idcompany));
                        command.Parameters.Add(new MySqlParameter("_portalId", portalId));

                        connection.Open();


                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                result.Add(new CompanyProductEntity
                                {
                                    Id = reader.GetAsInt("id"),
                                    ProductId = reader.GetAsInt("product_id"),
                                    IdCompany = reader.GetAsInt("company_id"),
                                    PortalId = reader.GetAsShort("portal_id"),
                                    DateAdd = reader.GetAsDateTime("date_add"),
                                    DateMod = reader.GetAsDateTime("date_mod"),
                                    IdUser = reader.GetAsInt("user_id"),
                                    Price = reader.GetAsFloat("price"),
                                    ServiceTypeId = reader.GetAsShort("service_type_id"),
                                    SourceId = reader.GetAsShort("source_id"),
                                    PaymentOriginId = reader.GetAsShort("payment_origin_id"),
                                    DateActivation = reader.GetAsDateTime("date_activation"),
                                    DateExpiration = reader.GetAsDateTime("date_expiration"),
                                    ExpirationDays = reader.GetAsInt("expiration_days"),
                                    StatusId = reader.GetAsShort("status_id"),
                                    GroupId = reader.GetAsShort("group_id"),
                                    ClientIP = reader.GetAsString("client_ip"),
                                    SubGroupId = reader.GetAsShort("subgroup_id")
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "GetPackByCompanySubGroup", false, null, portalId);
            }

            return result;
        }

        public List<CompanyProductEntity> GetPackByCompany(int idcompany, short portalId)
        {
            var result = new List<CompanyProductEntity>();

            try
            {
                using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
                {
                    using (var command = new MySqlCommand("companyProductsGetPacks", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.Add(new MySqlParameter("_companyId", idcompany));
                        command.Parameters.Add(new MySqlParameter("_portalId", portalId));

                        connection.Open();


                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                result.Add(new CompanyProductEntity
                                {
                                    Id = reader.GetAsInt("id"),
                                    ProductId = reader.GetAsInt("product_id"),
                                    IdCompany = reader.GetAsInt("company_id"),
                                    PortalId = reader.GetAsShort("portal_id"),
                                    DateAdd = reader.GetAsDateTime("date_add"),
                                    DateMod = reader.GetAsDateTime("date_mod"),
                                    IdUser = reader.GetAsInt("user_id"),
                                    Price = reader.GetAsFloat("price"),
                                    ServiceTypeId = reader.GetAsShort("service_type_id"),
                                    SourceId = reader.GetAsShort("source_id"),
                                    PaymentOriginId = reader.GetAsShort("payment_origin_id"),
                                    DateActivation = reader.GetAsDateTime("date_activation"),
                                    DateExpiration = reader.GetAsDateTime("date_expiration"),
                                    ExpirationDays = reader.GetAsInt("expiration_days"),
                                    StatusId = reader.GetAsShort("status_id"),
                                    GroupId = reader.GetAsShort("group_id"),
                                    ClientIP = reader.GetAsString("client_ip"),
                                    SubGroupId = reader.GetAsShort("subgroup_id")
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "GetPackByCompany", false, null, portalId);
            }

            return result;
        }

        public bool SetConsumedByCompanyProductId(int companyProductId, int ambitId, short idPortal, int consumedUnits)
        {
            using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, idPortal)))
            {
                using (var command = new MySqlCommand("company_products_features_set_consumed_by_companyProductId", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_idcompanyProduct", companyProductId));
                    command.Parameters.Add(new MySqlParameter("_ambit", ambitId));
                    command.Parameters.Add(new MySqlParameter("_idportal", idPortal));
                    command.Parameters.Add(new MySqlParameter("_consumed_units", consumedUnits));

                    try
                    {
                        connection.Open();
                        command.ExecuteNonQuery();

                        return true;
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "SetConsumedByCompanyProductId", false, null, idPortal);
                        return false;
                    }
                }
            }
        }


        public bool ConsumeByCompanyProductId(int companyProductId, int ambitId, short idPortal)
        {
            using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, idPortal)))
            {
                using (var command = new MySqlCommand("company_products_features_consume_by_companyProductId", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_id", companyProductId));
                    command.Parameters.Add(new MySqlParameter("_ambit", ambitId));
                    command.Parameters.Add(new MySqlParameter("_idportal", idPortal));

                    try
                    {
                        connection.Open();
                        command.ExecuteNonQuery();

                        return true;
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "ConsumeByCompanyProductId", false, null, idPortal);
                        return false;
                    }
                }
            }
        }

        public bool ConsumeCVsDownloadLimitUnitsByCompanyProductId(CompanyProductEntity companyProduct, int ambitId, int units)
        {
            using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, companyProduct.PortalId)))
            {
                using (var command = new MySqlCommand("ae_companyProductsFeaturesConsumeCvsdownloadUnits_v2", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_id", companyProduct.Id));
                    command.Parameters.Add(new MySqlParameter("_ambit", ambitId));
                    command.Parameters.Add(new MySqlParameter("_idportal", companyProduct.PortalId));
                    command.Parameters.Add(new MySqlParameter("_units", units));

                    try
                    {
                        connection.Open();
                        command.ExecuteNonQuery();

                        return true;
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "ConsumeCVsDownloadLimitUnitsByCompanyProductId", false, null, companyProduct.PortalId);
                        return false;
                    }
                }
            }
        }

        public DateTime SelectCompanyProductDateModByIdCompanyProduct(int companyId, int companyProductId, short portalId)
        {
            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
            {
                using (MySqlCommand command = new MySqlCommand("CompanyProductGetLastDateModByIdCompanyProduct_v2", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_idcompany", companyId));
                    command.Parameters.Add(new MySqlParameter("_idportal", portalId));
                    command.Parameters.Add(new MySqlParameter("_idcompanyproduct", companyProductId));

                    try
                    {
                        connection.Open();

                        return command.ExecuteScalar().ToDateTime();
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "SelectCompanyProductDateModByIdCompanyProduct", false, null, portalId);
                    }
                }
            }

            return DateTime.MinValue;
        }

        public bool UpdateProductExpirationDate(int companyProductId, int idCompany, short idPortal, int addDays)
        {
            using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, idPortal)))
            {
                using (var command = new MySqlCommand("ae_UpdateProductExpirationDate", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_companyproductid", companyProductId));
                    command.Parameters.Add(new MySqlParameter("_idcompany", idCompany));
                    command.Parameters.Add(new MySqlParameter("_idportal", idPortal));
                    command.Parameters.Add(new MySqlParameter("_add_days", addDays));

                    try
                    {
                        connection.Open();
                        command.ExecuteNonQuery();

                        return true;
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "UpdateProductExpirationDate", false, null, idPortal);
                        return false;
                    }
                }
            }
        }

        public DateTime GetPackByCompanyMaxDateMod(int idCompany, int type)
        {
            using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
            {
                using (var command = new MySqlCommand("ae_GetPackByCompanyMaxDateMod", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_idcompany", idCompany));
                    command.Parameters.Add(new MySqlParameter("_typeId", type));
                    command.Parameters.Add(new MySqlParameter("_idportal", type));

                    try
                    {
                        connection.Open();

                        return command.ExecuteScalar().ToDateTime();
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "GetPackByCompanyMaxDateMod");
                        return DateTime.MinValue;
                    }
                }
            }
        }

        public List<string> GetActiveProductsName(int companyId, short portalId)
        {
            var result = new List<string>();

            try
            {
                using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
                {
                    using (var command = new MySqlCommand("ae_getAllActiveProductsName", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.Add(new MySqlParameter("_companyId", companyId));
                        command.Parameters.Add(new MySqlParameter("_portalId", portalId));

                        connection.Open();


                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                result.Add(reader.GetAsString("comercialName"));
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "GetActiveProductsName", false, null, portalId);
            }

            return result;
        }

        public List<CompanyProductEntity> GetAllCompanyActiveProducts(int idcompany, short portalId)
        {
            var result = new List<CompanyProductEntity>();

            try
            {
                using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
                {
                    using (var command = new MySqlCommand("ae_getAllCompanyActiveProducts_v2", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.Add(new MySqlParameter("_companyId", idcompany));
                        command.Parameters.Add(new MySqlParameter("_portalId", portalId));

                        connection.Open();


                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                result.Add(new CompanyProductEntity
                                {
                                    Id = reader.GetAsInt("id"),
                                    ProductId = reader.GetAsInt("product_id"),
                                    IdCompany = reader.GetAsInt("company_id"),
                                    PortalId = reader.GetAsShort("portal_id"),
                                    DateAdd = reader.GetAsDateTime("date_add"),
                                    DateMod = reader.GetAsDateTime("date_mod"),
                                    IdUser = reader.GetAsInt("user_id"),
                                    Price = reader.GetAsFloat("price"),
                                    ServiceTypeId = reader.GetAsShort("service_type_id"),
                                    SourceId = reader.GetAsShort("source_id"),
                                    PaymentOriginId = reader.GetAsShort("payment_origin_id"),
                                    DateActivation = reader.GetAsDateTime("date_activation"),
                                    DateExpiration = reader.GetAsDateTime("date_expiration"),
                                    ExpirationDays = reader.GetAsInt("expiration_days"),
                                    StatusId = reader.GetAsShort("status_id"),
                                    GroupId = reader.GetAsShort("group_id"),
                                    ClientIP = reader.GetAsString("client_ip"),
                                    SubGroupId = reader.GetAsShort("subgroup_id"),
                                    ProductTypeId = reader.GetAsShort("product_type")
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "GetAllCompanyActiveProducts", false, null, portalId);
            }

            return result;
        }

        public List<CompanyProductEntity> GetAllCompanyProducts(int idcompany, short portalId)
        {
            var result = new List<CompanyProductEntity>();

            try
            {
                using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
                {
                    using (var command = new MySqlCommand("ae_getAllCompanyProducts_v2", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.Add(new MySqlParameter("_companyId", idcompany));
                        command.Parameters.Add(new MySqlParameter("_portalId", portalId));

                        connection.Open();


                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                result.Add(new CompanyProductEntity
                                {
                                    Id = reader.GetAsInt("id"),
                                    ProductId = reader.GetAsInt("product_id"),
                                    IdCompany = reader.GetAsInt("company_id"),
                                    PortalId = reader.GetAsShort("portal_id"),
                                    DateAdd = reader.GetAsDateTime("date_add"),
                                    DateMod = reader.GetAsDateTime("date_mod"),
                                    IdUser = reader.GetAsInt("user_id"),
                                    Price = reader.GetAsFloat("price"),
                                    ServiceTypeId = reader.GetAsShort("service_type_id"),
                                    ProductTypeId = reader.GetAsShort("product_type"),
                                    SourceId = reader.GetAsShort("source_id"),
                                    PaymentOriginId = reader.GetAsShort("payment_origin_id"),
                                    DateActivation = reader.GetAsDateTime("date_activation"),
                                    DateExpiration = reader.GetAsDateTime("date_expiration"),
                                    ExpirationDays = reader.GetAsInt("expiration_days"),
                                    StatusId = reader.GetAsShort("status_id"),
                                    GroupId = reader.GetAsShort("group_id"),
                                    ClientIP = reader.GetAsString("client_ip"),
                                    SubGroupId = reader.GetAsShort("subgroup_id")
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "GetAllCompanyProducts", false, null, portalId);
            }

            return result;
        }

        public DateTime GetAllCompanyActiveProductsMaxDateMod(int idCompany, short idPortal)
        {
            using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, idPortal)))
            {
                using (var command = new MySqlCommand("ae_getAllCompanyActiveProductsMaxDateMod", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_idcompany", idCompany));
                    command.Parameters.Add(new MySqlParameter("_idportal", idPortal));

                    try
                    {
                        connection.Open();

                        return command.ExecuteScalar().ToDateTime();
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "GetAllCompanyActiveProductsMaxDateMod", false, null, idPortal);
                        return DateTime.MinValue;
                    }
                }
            }
        }

        public DateTime GetAllCompanyProductsMaxDateMod(int idCompany, short idPortal)
        {
            using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, idPortal)))
            {
                using (var command = new MySqlCommand("ae_getAllCompanyProductsMaxDateMod", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_idcompany", idCompany));
                    command.Parameters.Add(new MySqlParameter("_idportal", idPortal));

                    try
                    {
                        connection.Open();

                        return command.ExecuteScalar().ToDateTime();
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "GetAllCompanyProductsMaxDateMod", false, null, idPortal);
                        return DateTime.MinValue;
                    }
                }
            }
        }

        private bool InsertCancelledProducts(int companyProductId, short idPortal, int idPurchaseOperation, int companyid, int productid)
        {
            using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.MasterAux, idPortal)))
            {
                using (var command = new MySqlCommand("ae_cancelled_products_insert", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_companyproductid", companyProductId));
                    command.Parameters.Add(new MySqlParameter("_idportal", idPortal));
                    command.Parameters.Add(new MySqlParameter("_idpurchaseoperation", idPurchaseOperation));
                    command.Parameters.Add(new MySqlParameter("_companyid", companyid));
                    command.Parameters.Add(new MySqlParameter("_productid", productid));

                    try
                    {
                        connection.Open();
                        command.ExecuteNonQuery();

                        return true;
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "DesactivateCompanyProduct");
                        return false;
                    }
                }
            }
        }
        private bool DeleteCompanyProduct(int companyProductId, short idPortal)
        {
            using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, idPortal)))
            {
                using (var command = new MySqlCommand("ae_companyproduct_update_status_id_to_cancel", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_companyproductid", companyProductId));
                    command.Parameters.Add(new MySqlParameter("_idportal", idPortal));

                    try
                    {
                        connection.Open();
                        command.ExecuteNonQuery();

                        return true;
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "DesactivateCompanyProduct", false, null, idPortal);
                        return false;
                    }
                }
            }
        }
        public bool DesactivateCompanyProduct(int companyProductId, short idPortal, int idPurchaseOperation, int companyid, int productid)
        {
            var result = DeleteCompanyProduct(companyProductId, idPortal);
            if (result) return InsertCancelledProducts(companyProductId, idPortal, idPurchaseOperation, companyid, productid);

            return result;
        }

        public bool SaveOfferAndProductFeatureRelationship(int idOffer, int idCompanyProduct, short idAmbit, short idPortal)
        {
            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, idPortal)))
            {
                using (MySqlCommand command = new MySqlCommand("ae_offer_productsfeatures_insert", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    try
                    {
                        command.Parameters.Add(new MySqlParameter("_idoffer", idOffer));
                        command.Parameters.Add(new MySqlParameter("_idcompanyproduct", idCompanyProduct));
                        command.Parameters.Add(new MySqlParameter("_idambit", idAmbit));
                        command.Parameters.Add(new MySqlParameter("_idportal", idPortal));

                        connection.Open();

                        command.ExecuteNonQuery();
                    }
                    catch (Exception ex)
                    {
                        connection.Close();
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "SaveOfferAndProductFeatureRelationship", false, null, idPortal);
                        return false;
                    }
                }
            }
            return true;
        }

        public bool CompanyHasMoreThanOneContractedProduct(int companyId, short portalId)
        {
            bool result = false;

            using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
            {
                using (var command = new MySqlCommand("ae_companyHasMoreThanOneContractedProduct", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    command.Parameters.Add(new MySqlParameter("_companyId", companyId));
                    command.Parameters.Add(new MySqlParameter("_portalId", portalId));

                    try
                    {
                        connection.Open();
                        return command.ExecuteScalar().ToInt() > 1;
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "CompanyHasMoreThanOneContractedProduct", false, null, portalId);
                    }
                }
            }

            return result;
        }

        public int ReinitProductUnits(int companyProductId, int ambitId)
        {
            using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
            {
                using (var command = new MySqlCommand("ae_reinit_product_units", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    command.Parameters.Add(new MySqlParameter("_companyProductId", companyProductId));
                    command.Parameters.Add(new MySqlParameter("_ambitId", ambitId));

                    try
                    {
                        connection.Open();
                        return command.ExecuteScalar().ToInt();
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "ReinitProductUnits");
                    }
                }
            }
            return 0;
        }

        public bool SetLastDateConsumedByCompanyProductId(int companyProductId, int ambitId, short idPortal)
        {
            using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, idPortal)))
            {
                using (var command = new MySqlCommand("company_products_features_set_date_last_consumed", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_idcompanyProduct", companyProductId));
                    command.Parameters.Add(new MySqlParameter("_ambit", ambitId));
                    command.Parameters.Add(new MySqlParameter("_idportal", idPortal));

                    try
                    {
                        connection.Open();
                        command.ExecuteNonQuery();

                        return true;
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "SetLastDateConsumedByCompanyProductId", false, null, idPortal);
                        return false;
                    }
                }
            }
        }

        public async Task<int> AddCourtesyProductAsync(AddCourtesyProductPersistentLayerDTO dto)
        {
            var result = 0;

            using (MySqlConnection l_conex = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, dto.PortalId)))
            {
                using (MySqlCommand l_command = new MySqlCommand("bwp_company_product_add_courtesy", l_conex))
                {
                    l_command.CommandType = CommandType.StoredProcedure;
                    try
                    {

                        l_command.Parameters.Add(new MySqlParameter("_portalid", dto.PortalId));
                        l_command.Parameters.Add(new MySqlParameter("_companyid", dto.CompanyId));
                        l_command.Parameters.Add(new MySqlParameter("_productid", dto.ProductId));

                        l_conex.Open();
                        var resultQuery = await l_command.ExecuteScalarAsync();

                        if (resultQuery != null)
                        {
                            int.TryParse(resultQuery.ToString(), out result);
                        }
                    }

                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "AddCourtesyProductAsync", false, null, dto.PortalId);
                    }
                }
            }

            return result;
        }

        public async Task<bool> AddCourtesyProductFeaturesAsync(AddCourtesyProductFeaturePersistentLayerDTO dto)
        {
            try
            {
                using (MySqlConnection l_conex = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, dto.PortalId)))
                {
                    using (MySqlCommand l_command = new MySqlCommand("bwp_company_product_features_add_by_courtesy", l_conex))
                    {

                        l_conex.Open();
                        l_command.CommandType = CommandType.StoredProcedure;
                        l_command.Parameters.Add(new MySqlParameter("_companyid", dto.CompanyId));
                        l_command.Parameters.Add(new MySqlParameter("_productid", dto.ProductId));
                        l_command.Parameters.Add(new MySqlParameter("_companyproductid", dto.CompanyProductId));
                        l_command.Parameters.Add(new MySqlParameter("_portalid", dto.PortalId));

                        await l_command.ExecuteNonQueryAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "AddCourtesyProductFeaturesAsync", false, null, dto.PortalId);
                return false;
            }
            return true;
        }

        public async Task UpdateDateModActiveProductsAsync(int companyId, short portalId)
        {
            using (MySqlConnection conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
            {
                using (MySqlCommand l_command = new MySqlCommand("bwp_update_company_products_active_datemod", conn))
                {
                    try
                    {
                        conn.Open();
                        l_command.CommandType = CommandType.StoredProcedure;
                        l_command.Parameters.Add(new MySqlParameter("_idcompany", companyId));
                        l_command.Parameters.Add(new MySqlParameter("_idportal", portalId));

                        await l_command.ExecuteNonQueryAsync();
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "UpdateDateModActiveProducts", false, null, portalId);
                    }
                }
            }
        }

        public async Task UpdateDateModActiveProductsRepoProductsAsync(int companyId, short portalId)
        {
            using (MySqlConnection conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.RepoProduct, portalId)))
            {
                using (MySqlCommand l_command = new MySqlCommand("BwpUpdateCompanyProductsActiveDatemod", conn))
                {
                    try
                    {
                        conn.Open();
                        l_command.CommandType = CommandType.StoredProcedure;
                        l_command.Parameters.Add(new MySqlParameter("_idCompany", companyId));
                        l_command.Parameters.Add(new MySqlParameter("_idPortal", portalId));

                        await l_command.ExecuteNonQueryAsync();
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "UpdateDateModActiveProductsRepoProductAsync", false, null, portalId);
                    }
                }
            }
        }

        public async Task<CompanyProductProProductPortalPersistentLayerDTO> GetCompanyProductByIdAsync(SearchByCompanyProductPersistentLayerDTO dto)
        {
            var result = new CompanyProductProProductPortalPersistentLayerDTO();

            using (MySqlConnection conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, dto.PortalId)))
            {
                using (MySqlCommand comm = new MySqlCommand("bw_company_products_select_by_company_and_product", conn))
                {
                    comm.CommandType = CommandType.StoredProcedure;

                    try
                    {
                        comm.Parameters.Add(new MySqlParameter("_portalId", dto.PortalId));
                        comm.Parameters.Add(new MySqlParameter("_companyProductId", dto.CompanyProductId));
                        comm.Parameters.Add(new MySqlParameter("_companyId", dto.CompanyId));

                        conn.Open();
                        using (var dtr = await comm.ExecuteReaderAsync())
                        {
                            if (dtr != null)
                            {
                                if (dtr.Read())
                                {

                                    result = new CompanyProductProProductPortalPersistentLayerDTO()
                                    {
                                        Id = dtr.GetAsInt("id"),
                                        ComercialName = dtr.GetAsString("comercial_name"),
                                        GroupId = dtr.GetAsShort("group_id"),
                                        SubgroupId = dtr.GetAsShort("subgroup_id"),
                                        ServiceTypeId = dtr.GetAsShort("service_type_id"),
                                        ProductTypeId = dtr.GetAsShort("product_type"),
                                        LimitationMultiplier = dtr.GetAsShort("limitation_multiplier"),
                                        PortalId = dto.PortalId,
                                        DateActivation = dtr.GetAsDateTime("date_activation"),
                                        DateExpiration = dtr.GetAsDateTime("date_expiration"),
                                        SelfRenewing = dtr.GetAsBoolean("self_renewing"),
                                    };
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "GetCompanyProductByIdAsync", false, null, dto.PortalId);
                        result = null;
                    }
                }
            }

            return result;
        }

        public async Task<bool> SetOfferMultiplierAsync(SaveCompanyProductPersistentLayerDTO dto)
        {
            using (MySqlConnection conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, dto.PortalId)))
            {
                using (MySqlCommand comm = new MySqlCommand("bwp_company_product_definition_set_limitation_multiplier", conn))
                {
                    comm.CommandType = CommandType.StoredProcedure;

                    try
                    {
                        conn.Open();
                        comm.Parameters.Add(new MySqlParameter("_id", dto.Id));
                        comm.Parameters.Add(new MySqlParameter("_limitationMultiplier", dto.LimitationMultiplier));

                        await comm.ExecuteNonQueryAsync();

                        return true;
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "SetOfferMultiplierAsync");
                    }
                }
            }
            return false;
        }

        public async Task<bool> UpdateCompanyProductAsync(SaveCompanyProductPersistentLayerDTO dto)
        {
            using (MySqlConnection conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, dto.PortalId)))
            {
                using (MySqlCommand comm = new MySqlCommand("bwp_update_company_product", conn))
                {
                    comm.CommandType = CommandType.StoredProcedure;

                    try
                    {
                        conn.Open();
                        comm.Parameters.Add(new MySqlParameter("_id", dto.Id));
                        comm.Parameters.Add(new MySqlParameter("_date_activation", dto.DateActivation));
                        comm.Parameters.Add(new MySqlParameter("_date_expiration", dto.DateExpiration));
                        comm.Parameters.Add(new MySqlParameter("_idportal", dto.PortalId));

                        await comm.ExecuteNonQueryAsync();

                        return true;
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "UpdateCompanyProductAsync");
                    }
                }
            }

            return false;
        }

        public async Task<List<CompanyProductAndProProductShorterPersistentLayerDTO>> GetAllCompanyProductsAndProProductShorterAsync(short portalId, int companyId)
        {
            var result = new List<CompanyProductAndProProductShorterPersistentLayerDTO>();

            using (MySqlConnection conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
            {
                using (MySqlCommand comm = new MySqlCommand("bw_getAll_company_products_select_by_company_v2", conn))
                {
                    comm.CommandType = CommandType.StoredProcedure;

                    try
                    {
                        comm.Parameters.Add(new MySqlParameter("_portal_id", portalId));
                        comm.Parameters.Add(new MySqlParameter("_company_id", companyId));

                        conn.Open();

                        using (var dtr = await comm.ExecuteReaderAsync())
                        {
                            if (dtr != null)
                            {
                                while (dtr.Read())
                                {
                                    result.Add(
                                            new CompanyProductAndProProductShorterPersistentLayerDTO
                                            {
                                                Id = dtr.GetAsLong("id"),
                                                ProductId = dtr.GetAsInt("product_id"),
                                                ProductName = dtr.GetAsString("comercial_name"),
                                                DateActivation = dtr.GetAsDateTime("date_activation"),
                                                DateExpiration = dtr.GetAsDateTime("date_expiration"),
                                                DescStatus = dtr.GetAsString("idstatus"),
                                                StatusId = dtr.GetAsShort("status_id"),
                                                GroupId = dtr.GetAsShort("group_id")
                                            }
                                        );
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "GetAllCompanyProductsAndProProductShorterAsync");
                    }
                }
            }

            return result;
        }

        public async Task<List<CompanyProductAndProProductShorterPersistentLayerDTO>> GetCompanyProductsAndProProductShorterByIdsAsync(short portalId, string ids)
        {
            var result = new List<CompanyProductAndProProductShorterPersistentLayerDTO>();

            using (MySqlConnection conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
            {
                using (MySqlCommand comm = new MySqlCommand("bwp_company_products_select_by_ids", conn))
                {
                    comm.CommandType = CommandType.StoredProcedure;

                    try
                    {
                        comm.Parameters.Add(new MySqlParameter("_company_products_ids", ids));

                        conn.Open();

                        using (var dtr = await comm.ExecuteReaderAsync())
                        {
                            if (dtr != null)
                            {
                                while (dtr.Read())
                                {
                                    var statusId = dtr.GetAsInt("status_id");

                                    result.Add(
                                            new CompanyProductAndProProductShorterPersistentLayerDTO
                                            {
                                                Id = dtr.GetAsLong("id"),
                                                ProductId = dtr.GetAsInt("product_id"),
                                                DateActivation = dtr.GetAsDateTime("date_activation"),
                                                DateExpiration = dtr.GetAsDateTime("date_expiration"),
                                                DescStatus = System.Enum.IsDefined(typeof(StatusEnum), statusId) ? ((StatusEnum)statusId).AnotationDescription() : "",
                                                StatusId = statusId,
                                                GroupId = dtr.GetAsShort("group_id")
                                            }
                                        );
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "GetCompanyProductsAndProProductShorterByIdsAsync");
                    }
                }
            }

            return result;
        }

        public async Task<List<CompanyProductAndProProductShorterPersistentLayerDTO>> GetCompanyProductsAndProProductShorterByIdsByRepoProductsAsync(short portalId, string ids)
        {
            var result = new List<CompanyProductAndProProductShorterPersistentLayerDTO>();

            using (MySqlConnection conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.RepoProduct, portalId)))
            {
                using (MySqlCommand comm = new MySqlCommand("BwpCompanyProductsSelectByIds", conn))
                {
                    comm.CommandType = CommandType.StoredProcedure;

                    try
                    {
                        comm.Parameters.Add(new MySqlParameter("_companyProductsIds", ids));

                        conn.Open();

                        using (var dtr = await comm.ExecuteReaderAsync())
                        {
                            if (dtr != null)
                            {
                                while (dtr.Read())
                                {
                                    var statusId = dtr.GetAsInt("StatusId");

                                    result.Add(
                                            new CompanyProductAndProProductShorterPersistentLayerDTO
                                            {
                                                Id = dtr.GetAsLong("Id"),
                                                ProductId = dtr.GetAsInt("ProductId"),
                                                DateActivation = dtr.GetAsDateTime("DateActivation"),
                                                DateExpiration = dtr.GetAsDateTime("DateExpiration"),
                                                DescStatus = System.Enum.IsDefined(typeof(StatusEnum), statusId) ? ((StatusEnum)statusId).AnotationDescription() : "",
                                                StatusId = statusId,
                                                GroupId = dtr.GetAsShort("GroupId")
                                            }
                                        );
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "GetCompanyProductsAndProProductShorterByIdsByRepoProductsAsync");
                    }
                }
            }

            return result;
        }

        public async Task<DateTime> SelectCompanyProductDateModByFreemium(int companyId, short portalId)
        {
            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
            {
                using (MySqlCommand command = new MySqlCommand("CompanyProductGetLastDateModByIdCompanyProductFreemium", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_idcompany", companyId));
                    command.Parameters.Add(new MySqlParameter("_idportal", portalId));

                    try
                    {
                        connection.Open();

                        return (await command.ExecuteScalarAsync()).ToDateTime();
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "SelectCompanyProductDateModByFreemium", false, null, portalId);
                    }
                }
            }

            return DateTime.MinValue;
        }

        public async Task<CompanyProductEntity> GetFreemiumCompanyProduct(int companyId, short portalId)
        {
            try
            {
                using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
                {
                    using (var command = new MySqlCommand("company_product_get_company_prod_freemium", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.Add(new MySqlParameter("_company_id", companyId));
                        command.Parameters.Add(new MySqlParameter("_portal_id", portalId));

                        connection.Open();

                        var companyProduct = new CompanyProductEntity();
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            while (reader.Read())
                            {
                                companyProduct = new CompanyProductEntity
                                {
                                    Id = reader.GetAsInt("id"),
                                    ProductId = reader.GetAsInt("product_id"),
                                    IdCompany = reader.GetAsInt("company_id"),
                                    PortalId = reader.GetAsShort("portal_id"),
                                    DateAdd = reader.GetAsDateTime("date_add"),
                                    DateMod = reader.GetAsDateTime("date_mod"),
                                    IdUser = reader.GetAsInt("user_id"),
                                    Price = reader.GetAsFloat("price"),
                                    ServiceTypeId = reader.GetAsShort("service_type_id"),
                                    SourceId = reader.GetAsShort("source_id"),
                                    PaymentOriginId = reader.GetAsShort("payment_origin_id"),
                                    DateActivation = reader.GetAsDateTime("date_activation"),
                                    DateExpiration = reader.GetAsDateTime("date_expiration"),
                                    ExpirationDays = reader.GetAsInt("expiration_days"),
                                    StatusId = reader.GetAsShort("status_id"),
                                    GroupId = reader.GetAsShort("group_id"),
                                    ClientIP = reader.GetAsString("client_ip"),
                                    SubGroupId = reader.GetAsShort("subgroup_id"),
                                    LimitationMultiplier = reader.GetAsShort("limitation_multiplier")
                                };
                            }
                        }

                        return companyProduct;
                    }
                }
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "GetFreemiumCompanyProduct", false, null, portalId);
                return new CompanyProductEntity();
            }
        }

        public async Task<DateTime> SelectCompanyProductDateModAsync(ParamsMainCompanyProductPersistentLayerDTO dto)
        {
            using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, dto.PortalId)))
            {
                using (var command = new MySqlCommand("ae_company_product_get_date_last_update_v3", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_idcompany", dto.CompanyId));
                    command.Parameters.Add(new MySqlParameter("_typeId", dto.TypeId));

                    try
                    {
                        connection.Open();

                        return (await command.ExecuteScalarAsync()).ToDateTime();
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "SelectCompanyProductDateModAsync", false, null, dto.PortalId);
                        return DateTime.MinValue;
                    }
                }
            }
        }

        public async Task<List<CompanyProductEntity>> SelectByCompanyAsync(ParamsMainCompanyProductPersistentLayerDTO dto)
        {
            using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, (short)dto.PortalId)))
            {
                using (var command = new MySqlCommand("gl_company_products_select_by_company_v6", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_company_id", dto.CompanyId));
                    command.Parameters.Add(new MySqlParameter("_portal_id", dto.PortalId));
                    command.Parameters.Add(new MySqlParameter("_typeId", dto.TypeId));

                    try
                    {
                        var products = new List<CompanyProductEntity>();

                        connection.Open();

                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            if (reader != null)
                            {
                                while (reader.Read())
                                {
                                    products.Add(new CompanyProductEntity
                                    {
                                        Id = reader.GetAsInt("id"),
                                        ProductId = reader.GetAsInt("product_id"),
                                        IdCompany = reader.GetAsInt("company_id"),
                                        PortalId = reader.GetAsShort("portal_id"),
                                        DateAdd = reader.GetAsDateTime("date_add"),
                                        DateMod = reader.GetAsDateTime("date_mod"),
                                        IdUser = reader.GetAsInt("user_id"),
                                        Price = reader.GetAsFloat("price"),
                                        ServiceTypeId = reader.GetAsShort("service_type_id"),
                                        SourceId = reader.GetAsShort("source_id"),
                                        PaymentOriginId = reader.GetAsShort("payment_origin_id"),
                                        DateExpiration = reader.GetAsDateTime("date_expiration"),
                                        DateActivation = reader.GetAsDateTime("date_activation"),
                                        ExpirationDays = reader.GetAsInt("expiration_days"),
                                        StatusId = reader.GetAsShort("status_id"),
                                        GroupId = reader.GetAsShort("group_id"),
                                        ClientIP = reader.GetAsString("client_ip"),
                                        LimitationMultiplier = reader.GetAsShort("limitation_multiplier"),
                                        SubGroupId = reader.GetAsShort("subgroup_id")
                                    });
                                }
                            }
                        }
                        return products;
                    }
                    catch (Exception ex)
                    {
                        connection.Close();
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "SelectByCompanyAsync", false, null, dto.PortalId);
                        return null;
                    }
                }
            }
        }

        public async Task<DateTime> SelectCompanyProductDateModByIdCompanyProductAsync(int companyProductId, short portalId)
        {
            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
            {
                using (MySqlCommand command = new MySqlCommand("CProductGetLastDateModByIdCompanyProductAndPortalId", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_idportal", portalId));
                    command.Parameters.Add(new MySqlParameter("_idcompanyproduct", companyProductId));

                    try
                    {
                        connection.Open();
                        return (await command.ExecuteScalarAsync()).ToDateTime();
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "SelectCompanyProductDateModByIdCompanyProductAsync", false, null, portalId);
                    }
                }
            }

            return DateTime.MinValue;
        }

        public async Task<CompanyProductEntity> GetByCompanyProductIdAndPortalIdAsync(int companyProductId, short portalId)
        {
            try
            {
                using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
                {
                    using (var command = new MySqlCommand("company_product_get_by_company_prod_id_and_portal_id", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.Add(new MySqlParameter("_company_product_id", companyProductId));
                        command.Parameters.Add(new MySqlParameter("_portal_id", portalId));

                        connection.Open();

                        var companyProduct = new CompanyProductEntity();
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            while (reader.Read())
                            {
                                companyProduct = new CompanyProductEntity
                                {
                                    Id = reader.GetAsInt("id"),
                                    ProductId = reader.GetAsInt("product_id"),
                                    IdCompany = reader.GetAsInt("company_id"),
                                    PortalId = reader.GetAsShort("portal_id"),
                                    DateAdd = reader.GetAsDateTime("date_add"),
                                    DateMod = reader.GetAsDateTime("date_mod"),
                                    IdUser = reader.GetAsInt("user_id"),
                                    Price = reader.GetAsFloat("price"),
                                    ServiceTypeId = reader.GetAsShort("service_type_id"),
                                    SourceId = reader.GetAsShort("source_id"),
                                    PaymentOriginId = reader.GetAsShort("payment_origin_id"),
                                    DateActivation = reader.GetAsDateTime("date_activation"),
                                    DateExpiration = reader.GetAsDateTime("date_expiration"),
                                    ExpirationDays = reader.GetAsInt("expiration_days"),
                                    StatusId = reader.GetAsShort("status_id"),
                                    GroupId = reader.GetAsShort("group_id"),
                                    ClientIP = reader.GetAsString("client_ip"),
                                    SubGroupId = reader.GetAsShort("subgroup_id"),
                                    LimitationMultiplier = reader.GetAsShort("limitation_multiplier")
                                };
                            }
                        }

                        return companyProduct;
                    }
                }
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "GetByCompanyProductIdAndPortalIdAsync", false, null, portalId);
                return null;
            }
        }

        public async Task<List<CompanyProductAndProProductShorterPersistentLayerDTO>> GetAllCompanyProductsByRepoProductsAsync(short portalId, int companyId)
        {
            var result = new List<CompanyProductAndProProductShorterPersistentLayerDTO>();

            using (MySqlConnection conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.RepoProduct, portalId)))
            {
                using (MySqlCommand comm = new MySqlCommand("GetAllCompanyProductsSelectByCompany", conn))
                {
                    comm.CommandType = CommandType.StoredProcedure;

                    try
                    {
                        comm.Parameters.Add(new MySqlParameter("_portal_id", portalId));
                        comm.Parameters.Add(new MySqlParameter("_company_id", companyId));

                        conn.Open();

                        using (var dtr = await comm.ExecuteReaderAsync())
                        {
                            if (dtr != null)
                            {
                                while (dtr.Read())
                                {
                                    var statusId = dtr.GetAsInt("StatusId");

                                    result.Add(
                                            new CompanyProductAndProProductShorterPersistentLayerDTO
                                            {
                                                Id = dtr.GetAsLong("Id"),
                                                ProductId = dtr.GetAsInt("ProductId"),
                                                DateActivation = dtr.GetAsDateTime("DateActivation"),
                                                DateExpiration = dtr.GetAsDateTime("DateExpiration"),
                                                DescStatus = System.Enum.IsDefined(typeof(StatusEnum), statusId) ? ((StatusEnum)statusId).AnotationDescription() : "",
                                                StatusId = statusId,
                                                GroupId = dtr.GetAsShort("GroupId")
                                            }
                                        );
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "GetAllCompanyProductsByRepoProducts");
                    }
                }
            }

            return result;
        }

        public async Task<CompanyProductProProductPortalPersistentLayerDTO> GetCompanyProductByIdByRepoProductAsync(SearchByCompanyProductPersistentLayerDTO dto)
        {
            var result = new CompanyProductProProductPortalPersistentLayerDTO();

            using (MySqlConnection conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.RepoProduct, dto.PortalId)))
            {
                using (MySqlCommand comm = new MySqlCommand("GetCompanyProductSelect", conn))
                {
                    comm.CommandType = CommandType.StoredProcedure;

                    try
                    {
                        comm.Parameters.Add(new MySqlParameter("_portalId", dto.PortalId));
                        comm.Parameters.Add(new MySqlParameter("_companyProductId", dto.CompanyProductId));
                        comm.Parameters.Add(new MySqlParameter("_companyId", dto.CompanyId));

                        conn.Open();
                        using (var dtr = await comm.ExecuteReaderAsync())
                        {
                            if (dtr != null)
                            {
                                if (dtr.Read())
                                {

                                    result = new CompanyProductProProductPortalPersistentLayerDTO()
                                    {
                                        Id = dtr.GetAsInt("Id"),
                                        GroupId = dtr.GetAsShort("GroupId"),
                                        SubgroupId = dtr.GetAsShort("SubgroupId"),
                                        ServiceTypeId = dtr.GetAsShort("ServiceTypeId"),
                                        ProductTypeId = dtr.GetAsShort("ProductType"),
                                        LimitationMultiplier = dtr.GetAsShort("LimitationMultiplier"),
                                        PortalId = dto.PortalId,
                                        DateActivation = dtr.GetAsDateTime("DateActivation"),
                                        DateExpiration = dtr.GetAsDateTime("DateExpiration"),
                                        ProductId = dtr.GetAsInt("ProductId"),
                                        SelfRenewing = dtr.GetAsBoolean("SelfRenewing"),
                                        CompanyId = dtr.GetAsInt("CompanyId")
                                    };
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "GetCompanyProductByIdByRepoProductAsync", false, null, dto.PortalId);
                        result = null;
                    }
                }
            }

            return result;
        }

        public async Task<List<CompanyProductAndProProductShorterPersistentLayerDTO>> GetAllCompanyProductsAndProProductShorterByRepoProductAsync(short portalId, int companyId)
        {
            var result = new List<CompanyProductAndProProductShorterPersistentLayerDTO>();

            using (MySqlConnection conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
            {
                using (MySqlCommand comm = new MySqlCommand("bw_getAll_company_products_select_by_company_v2", conn))
                {
                    comm.CommandType = CommandType.StoredProcedure;

                    try
                    {
                        comm.Parameters.Add(new MySqlParameter("_portal_id", portalId));
                        comm.Parameters.Add(new MySqlParameter("_company_id", companyId));

                        conn.Open();

                        using (var dtr = await comm.ExecuteReaderAsync())
                        {
                            if (dtr != null)
                            {
                                while (dtr.Read())
                                {
                                    result.Add(
                                            new CompanyProductAndProProductShorterPersistentLayerDTO
                                            {
                                                Id = dtr.GetAsLong("id"),
                                                ProductId = dtr.GetAsInt("product_id"),
                                                ProductName = dtr.GetAsString("comercial_name"),
                                                DateActivation = dtr.GetAsDateTime("date_activation"),
                                                DateExpiration = dtr.GetAsDateTime("date_expiration"),
                                                DescStatus = dtr.GetAsString("idstatus"),
                                                StatusId = dtr.GetAsShort("status_id"),
                                                GroupId = dtr.GetAsShort("group_id")
                                            }
                                        );
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "GetAllCompanyProductsAndProProductShorterByRepoProductAsync");
                    }
                }
            }

            return result;
        }

        public async Task<bool> SetOfferMultiplierByRepoProductAsync(SaveCompanyProductPersistentLayerDTO dto)
        {
            using (MySqlConnection conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.RepoProduct, dto.PortalId)))
            {
                using (MySqlCommand comm = new MySqlCommand("CompanyProductDefinitionSetLimitationMultiplier", conn))
                {
                    comm.CommandType = CommandType.StoredProcedure;

                    try
                    {
                        conn.Open();
                        comm.Parameters.Add(new MySqlParameter("_id", dto.Id));
                        comm.Parameters.Add(new MySqlParameter("_limitationMultiplier", dto.LimitationMultiplier));

                        await comm.ExecuteNonQueryAsync();

                        return true;
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "SetOfferMultiplierByRepoProductAsync");
                    }
                }
            }
            return false;
        }

        public async Task<bool> UpdateCompanyProductByRepoProductAsync(SaveCompanyProductPersistentLayerDTO dto)
        {
            using (MySqlConnection conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.RepoProduct, dto.PortalId)))
            {
                using (MySqlCommand comm = new MySqlCommand("UpdateCompanyProduct", conn))
                {
                    comm.CommandType = CommandType.StoredProcedure;

                    try
                    {
                        conn.Open();
                        comm.Parameters.Add(new MySqlParameter("_id", dto.Id));
                        comm.Parameters.Add(new MySqlParameter("_date_activation", dto.DateActivation));
                        comm.Parameters.Add(new MySqlParameter("_date_expiration", dto.DateExpiration));
                        comm.Parameters.Add(new MySqlParameter("_idportal", dto.PortalId));

                        await comm.ExecuteNonQueryAsync();

                        return true;
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "UpdateCompanyProductByRepoProductAsync");
                    }
                }
            }

            return false;
        }

        public DateTime GetAllCompanyProductsMaxDateModByRepoProduct(int companyId, short portalId)
        {
            using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.RepoProduct, portalId)))
            {
                using (var command = new MySqlCommand("GetAllCompanyProductsMaxDateMod", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_idcompany", companyId));
                    command.Parameters.Add(new MySqlParameter("_idportal", portalId));

                    try
                    {
                        connection.Open();

                        return command.ExecuteScalar().ToDateTime();
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "GetAllCompanyProductsMaxDateModByRepoProduct", false, null, portalId);
                        return DateTime.MinValue;
                    }
                }
            }
        }

        public List<CompanyProductEntity> GetAllCompanyProductsByRepoProduct(int companyId, short portalId)
        {
            var result = new List<CompanyProductEntity>();

            try
            {
                using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.RepoProduct, portalId)))
                {
                    using (var command = new MySqlCommand("GetAllCompanyProducts", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.Add(new MySqlParameter("_companyId", companyId));
                        command.Parameters.Add(new MySqlParameter("_portalId", portalId));

                        connection.Open();


                        using (var reader = command.ExecuteReader())
                        {
                            result = AddListCompanyProductEntity(reader);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "GetAllCompanyProductsByRepoProduct", false, null, portalId);
            }

            return result;
        }

        public DateTime GetAllCompanyActiveProductsMaxDateModByRepoProduct(int companyId, short portalId)
        {
            using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.RepoProduct, portalId)))
            {
                using (var command = new MySqlCommand("GetAllCompanyActiveProductsMaxDateMod", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_idcompany", companyId));
                    command.Parameters.Add(new MySqlParameter("_idportal", portalId));

                    try
                    {
                        connection.Open();

                        return command.ExecuteScalar().ToDateTime();
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "GetAllCompanyActiveProductsMaxDateModByRepoProduct", false, null, portalId);
                        return DateTime.MinValue;
                    }
                }
            }
        }

        public List<CompanyProductEntity> GetAllCompanyActiveProductsByRepoProduct(int companyId, short portalId)
        {
            var result = new List<CompanyProductEntity>();

            try
            {
                using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.RepoProduct, portalId)))
                {
                    using (var command = new MySqlCommand("GetAllCompanyActiveProducts", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.Add(new MySqlParameter("_companyId", companyId));
                        command.Parameters.Add(new MySqlParameter("_portalId", portalId));

                        connection.Open();

                        using (var reader = command.ExecuteReader())
                        {
                            result = AddListCompanyProductEntity(reader);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "GetAllCompanyActiveProductsByRepoProduct", false, null, portalId);
            }

            return result;
        }

        public DateTime GetAllCustomerActiveProductsMaxDateModByRepoProduct(long customerId, short portalId)
        {
            using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.RepoProduct, portalId)))
            {
                using (var command = new MySqlCommand("GetAllCustomerActiveProductsMaxDateMod", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_idCustomer", customerId));
                    command.Parameters.Add(new MySqlParameter("_idPortal", portalId));

                    try
                    {
                        connection.Open();

                        return command.ExecuteScalar().ToDateTime();
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "GetAllCustomerActiveProductsMaxDateModByRepoProduct", false, null, portalId);
                        return DateTime.MinValue;
                    }
                }
            }
        }

        public List<CompanyProductEntity> GetAllCustomerActiveProductsByRepoProduct(long customerId, short portalId)
        {
            var result = new List<CompanyProductEntity>();

            try
            {
                using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.RepoProduct, portalId)))
                {
                    using (var command = new MySqlCommand("GetAllCustomerActiveProducts", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.Add(new MySqlParameter("_customerId", customerId));
                        command.Parameters.Add(new MySqlParameter("_portalId", portalId));

                        connection.Open();

                        using (var reader = command.ExecuteReader())
                        {
                            result = AddListCompanyProductEntity(reader);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "GetAllCustomerActiveProductsByRepoProduct", false, null, portalId);
            }

            return result;
        }

        public DateTime GetAllCustomerProductsMaxDateModByRepoProduct(long customerId, short portalId)
        {
            using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.RepoProduct, portalId)))
            {
                using (var command = new MySqlCommand("GetAllCustomerProductsMaxDateMod", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_idCustomer", customerId));
                    command.Parameters.Add(new MySqlParameter("_idPortal", portalId));

                    try
                    {
                        connection.Open();

                        return command.ExecuteScalar().ToDateTime();
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "GetAllCustomerProductsMaxDateModByRepoProduct", false, null, portalId);
                        return DateTime.MinValue;
                    }
                }
            }
        }

        public List<CompanyProductEntity> GetAllCustomerProductsByRepoProduct(long customerId, short portalId)
        {
            var result = new List<CompanyProductEntity>();

            try
            {
                using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.RepoProduct, portalId)))
                {
                    using (var command = new MySqlCommand("GetAllCustomerProducts", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.Add(new MySqlParameter("_customerId", customerId));
                        command.Parameters.Add(new MySqlParameter("_portalId", portalId));

                        connection.Open();


                        using (var reader = command.ExecuteReader())
                        {
                            result = AddListCompanyProductEntity(reader);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "GetAllCustomerProductsByRepoProduct", false, null, portalId);
            }

            return result;
        }

        public async Task<int> CompanyProductDiscontinueAsync(long companyProductId, short portalId, ConnectionStringType connectionType)
        {
            int result = 0;
            string spName = string.Empty;
            string paramCompanyProductId = string.Empty;
            string paramPortalId = string.Empty;
            try
            {
                using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(connectionType, portalId)))
                {
                    switch (connectionType)
                    {
                        case ConnectionStringType.Master:
                            spName = "hub_CompanyProductDiscontinue_V2";
                            paramCompanyProductId = "_company_product_id";
                            paramPortalId = "_portal_id";
                            break;
                        case ConnectionStringType.RepoProduct:
                            spName = "HubCompanyProductDiscontinue";
                            paramCompanyProductId = "_CompanyProductId";
                            paramPortalId = "_PortalId";
                            break;
                    }
                    using (var command = new MySqlCommand(spName, connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.Add(new MySqlParameter(paramCompanyProductId, companyProductId));
                        command.Parameters.Add(new MySqlParameter(paramPortalId, portalId));

                        connection.Open();
                        result = await command.ExecuteNonQueryAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", $"CompanyProductDiscontinueAsync - {spName}", false, null, portalId);
            }
            return result;
        }

        public async Task<int> CompanyProductSetLostAsync(long companyProductId, short portalId, ConnectionStringType connectionType)
        {
            int result = 0;
            string spName = string.Empty;
            string paramCompanyProductId = string.Empty;
            string paramPortalId = string.Empty;
            try
            {
                using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(connectionType, portalId)))
                {
                    switch (connectionType)
                    {
                        case ConnectionStringType.Master:
                            spName = "hub_CompanyProductSetLost_V2";
                            paramCompanyProductId = "_idCompanyProduct";
                            paramPortalId = "_idPortal";
                            break;
                        case ConnectionStringType.RepoProduct:
                            spName = "HubCompanyProductSetLost";
                            paramCompanyProductId = "_CompanyProductId";
                            paramPortalId = "_PortalId";
                            break;
                    }
                    using (var command = new MySqlCommand(spName, connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.Add(new MySqlParameter(paramCompanyProductId, companyProductId));
                        command.Parameters.Add(new MySqlParameter(paramPortalId, portalId));

                        connection.Open();
                        result = await command.ExecuteNonQueryAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", $"CompanyProductSetLostAsync - {spName}", false, null, portalId);
            }
            return result;
        }

        public async Task<int> CompanyProductFeaturesSetLostAsync(long companyProductId, short portalId, ConnectionStringType connectionType)
        {
            int result = 0;
            string spName = string.Empty;
            string paramCompanyProductId = string.Empty;
            string paramPortalId = string.Empty;
            try
            {
                using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(connectionType, portalId)))
                {
                    switch (connectionType)
                    {
                        case ConnectionStringType.Master:
                            spName = "hub_CompanyProductFeatureSetLost_V2";
                            paramCompanyProductId = "_idCompanyProduct";
                            paramPortalId = "_idPortal";
                            break;
                        case ConnectionStringType.RepoProduct:
                            spName = "HubCompanyProductFeatureSetLost";
                            paramCompanyProductId = "_CompanyProductId";
                            paramPortalId = "_PortalId";
                            break;
                    }
                    using (var command = new MySqlCommand(spName, connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.Add(new MySqlParameter(paramCompanyProductId, companyProductId));
                        command.Parameters.Add(new MySqlParameter(paramPortalId, portalId));

                        connection.Open();
                        result = await command.ExecuteNonQueryAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", $"CompanyProductFeaturesSetLostAsync - {spName}", false, null, portalId);
            }
            return result;
        }

        public async Task<int> GetInitialUnitsFeatureByCompanyProductIdAsync(long companyProductId, short portalId, short ambitId, ConnectionStringType connectionType)
        {
            int result = 0;
            string spName = string.Empty;
            string paramCompanyProductId = string.Empty;
            string paramPortalId = string.Empty;
            string paramAmbitId = string.Empty;
            try
            {
                using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(connectionType, portalId)))
                {
                    switch (connectionType)
                    {
                        case ConnectionStringType.Master:
                            spName = "hub_GetInitialUnitsFeatureByCompanyProductId";
                            paramCompanyProductId = "_idCompanyProduct";
                            paramAmbitId = "_idAmbit";
                            paramPortalId = "_idPortal";
                            break;
                        case ConnectionStringType.RepoProduct:
                            spName = "HubGetInitialUnitsFeatureByCompanyProductId";
                            paramCompanyProductId = "_IdCompanyProduct";
                            paramAmbitId = "_IdAmbit";
                            paramPortalId = "_IdPortal";
                            break;
                    }
                    using (var command = new MySqlCommand(spName, connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.Add(new MySqlParameter(paramCompanyProductId, companyProductId));
                        command.Parameters.Add(new MySqlParameter(paramAmbitId, ambitId));
                        command.Parameters.Add(new MySqlParameter(paramPortalId, portalId));

                        connection.Open();
                        var scalarResult = await command.ExecuteScalarAsync();

                        if (scalarResult != null && int.TryParse(scalarResult.ToString(), out int parsedResult))
                        {
                            result = parsedResult;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", $"GetInitialUnitsFeatureByCompanyProductIdAsync - {spName}", false, null, portalId);
            }
            return result;
        }

        public async Task<DateTime> GetProductsExpirationDateFromActivation(short portalId, long productId, int temporalityId, DateTime activationDate, ConnectionStringType connectionType)
        {
            DateTime result = DateTime.MinValue;

            string spName = string.Empty;
            string paramPortalId = string.Empty;
            string paramProductId = string.Empty;
            string paramTemporalityId = string.Empty;
            string paramActivationDate = string.Empty;

            try
            {
                using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(connectionType, portalId)))
                {
                    switch (connectionType)
                    {
                        case ConnectionStringType.Master:
                            spName = "hub_GetProductsExpirationDateFromActivation";
                            paramPortalId = "_idPortal";
                            paramProductId = "_idProduct";
                            paramTemporalityId = "_idTemporality";
                            paramActivationDate = "_activationDate";
                            break;
                        case ConnectionStringType.RepoProduct:
                            spName = "HubGetProductsExpirationDateFromActivation";
                            paramPortalId = "_IdPortal";
                            paramProductId = "_IdProduct";
                            paramTemporalityId = "_IdTemporality";
                            paramActivationDate = "_ActivationDate";
                            break;
                    }
                    using (var command = new MySqlCommand(spName, connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.Add(new MySqlParameter(paramPortalId, portalId));
                        command.Parameters.Add(new MySqlParameter(paramProductId, productId));
                        command.Parameters.Add(new MySqlParameter(paramTemporalityId, temporalityId));
                        command.Parameters.Add(new MySqlParameter(paramActivationDate, activationDate));

                        connection.Open();
                        var scalarResult = await command.ExecuteScalarAsync();

                        if (scalarResult != null && DateTime.TryParse(scalarResult.ToString(), out DateTime parsedResult))
                        {
                            result = parsedResult;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", $"GetProductsExpirationDateFromActivation - {spName}", false, null, portalId);
            }
            return result;
        }

        public async Task<ProProductPortalDTO> GetProProductPortalByRepoProductsAsync(short portalId, long productId, int temporalityId)
        {
            ProProductPortalDTO productPortalDTO = new ProProductPortalDTO();

            try
            {
                using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.RepoProduct, portalId)))
                {
                    await connection.OpenAsync();

                    using (var command = new MySqlCommand("GetProProductPortalForAddingCompanyProduct_v1", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.Add(new MySqlParameter("_portalId", portalId));
                        command.Parameters.Add(new MySqlParameter("_productId", productId));
                        command.Parameters.Add(new MySqlParameter("_idTemporality", temporalityId));

                        using (var dtr = await command.ExecuteReaderAsync())
                        {
                            if (dtr != null)
                            {
                                if (dtr.Read())
                                {
                                    productPortalDTO = new ProProductPortalDTO()
                                    {
                                        ProductId = dtr.GetAsInt("ProductId"),
                                        PortalId = dtr.GetAsShort("PortalId"),
                                        ServiceTypeId = dtr.GetAsShort("ServiceTypeId"),
                                        GroupId = dtr.GetAsShort("GroupId"),
                                        ExpirationDays = dtr.GetAsInt("ExpirationDays"),
                                        LimitationMultiplier = dtr.GetAsShort("LimitationMultiplier"),
                                        SubGroupId = dtr.GetAsShort("SubgroupId"),
                                        TemporalityId = dtr.GetAsInt("IdTemporality"),
                                        IsActiveOCC = dtr.GetAsBoolean("IsActiveOCC"),
                                        ComercialName = dtr.GetAsString("ComercialName"),
                                        ProductTypeId = dtr.GetAsShort("ProductType"),
                                        IsObsolete = dtr.GetAsShort("IsObsolete"),
                                        InternalName = dtr.GetAsString("InternalName")                                    
                                    };
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "GetProProductPortalByRepoProductsAsync", false, null, portalId);
                return null;
            }

            return productPortalDTO;
        }


        public async Task<ProProductPortalDTO> GetProProductPortalWithoutTemporalityByMasterAsync(short portalId, long productId)
        {
            ProProductPortalDTO productPortalDTO = new ProProductPortalDTO();

            try
            {
                using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
                {
                    await connection.OpenAsync();

                    using (var command = new MySqlCommand("GetProProductPortalForAddingCompanyProductCourtesy_v1", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.Add(new MySqlParameter("_portalId", portalId));
                        command.Parameters.Add(new MySqlParameter("_productId", productId));


                        using (var dtr = await command.ExecuteReaderAsync())
                        {
                            if (dtr != null)
                            {
                                if (dtr.Read())
                                {
                                    productPortalDTO = new ProProductPortalDTO()
                                    {
                                        ProductId = dtr.GetAsInt("product_id"),
                                        PortalId = dtr.GetAsShort("portal_id"),
                                        ServiceTypeId = dtr.GetAsShort("service_type_id"),
                                        GroupId = dtr.GetAsShort("group_id"),
                                        ExpirationDays = dtr.GetAsInt("expiration_days"),
                                        LimitationMultiplier = dtr.GetAsShort("limitation_multiplier"),
                                        SubGroupId = dtr.GetAsShort("subgroup_id"),
                                        TemporalityId = dtr.GetAsInt("temporality_id"),
                                        IsActiveOCC = dtr.GetAsBoolean("is_active_occ"),
                                        ComercialName = dtr.GetAsString("comercial_name"),
                                        ProductTypeId = dtr.GetAsShort("product_type"),
                                        IsObsolete = dtr.GetAsShort("is_obsolete"),
                                        InternalName = dtr.GetAsString("internal_name")
                                    };
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "GetProProductPortalByMasterAsync", false, null, portalId);
                return null;
            }

            return productPortalDTO;
        }

        public async Task<ProProductPortalDTO> GetProProductPortalByMasterAsync(short portalId, long productId, int temporalityId)
        {
            ProProductPortalDTO productPortalDTO = new ProProductPortalDTO();

            try
            {
                using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
                {
                    await connection.OpenAsync();

                    using (var command = new MySqlCommand("GetProProductPortalForAddingCompanyProduct_v1", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.Add(new MySqlParameter("_portal_id", portalId));
                        command.Parameters.Add(new MySqlParameter("_product_id", productId));
                        command.Parameters.Add(new MySqlParameter("_idtemporality", temporalityId));

                        using (var dtr = await command.ExecuteReaderAsync())
                        {
                            if (dtr != null)
                            {
                                if (dtr.Read())
                                {
                                    productPortalDTO = new ProProductPortalDTO()
                                    {
                                        ProductId = dtr.GetAsInt("product_id"),
                                        PortalId = dtr.GetAsShort("portal_id"),
                                        ServiceTypeId = dtr.GetAsShort("service_type_id"),
                                        GroupId = dtr.GetAsShort("group_id"),
                                        ExpirationDays = dtr.GetAsInt("expiration_days"),
                                        LimitationMultiplier = dtr.GetAsShort("limitation_multiplier"),
                                        SubGroupId = dtr.GetAsShort("subgroup_id"),
                                        TemporalityId = dtr.GetAsInt("temporality_id"),
                                        IsActiveOCC = dtr.GetAsBoolean("is_active_occ"),
                                        ComercialName = dtr.GetAsString("comercial_name"),
                                        ProductTypeId = dtr.GetAsShort("product_type"),
                                        IsObsolete = dtr.GetAsShort("is_obsolete"),
                                        InternalName = dtr.GetAsString("internal_name")
                                    };

                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "GetProProductPortalByMasterAsync", false, null, portalId);
                return null;
            }

            return productPortalDTO;
        }

        public async Task<List<ProProductPortalFeatureDTO>> GetProproductPortalFeatureByRepoProductsAsync(short portalId, long productId)
        {
            List<ProProductPortalFeatureDTO> productPortalFeatureDTO = new List<ProProductPortalFeatureDTO>();
            try
            {
                using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.RepoProduct, portalId)))
                {
                    await connection.OpenAsync();

                    using (var command = new MySqlCommand("GetProProductPortalForAddingCompanyProductFeature", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.Add(new MySqlParameter("_portalId", portalId));
                        command.Parameters.Add(new MySqlParameter("_productId", productId));

                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            if (reader != null)
                            {
                                while (reader.Read())
                                {
                                    productPortalFeatureDTO.Add(new ProProductPortalFeatureDTO
                                    {
                                        ProductId = reader.GetAsInt("ProductId"),
                                        PortalId = reader.GetAsShort("PortalId"),
                                        AmbitId = reader.GetAsShort("AmbitId"),
                                        Id = reader.GetAsInt("Id"),
                                        AvailableUnits = reader.GetAsInt("AvailableUnits"),
                                        InitialUnits = reader.GetAsInt("InitialUnits"),
                                        IsUnlimited = reader.GetAsShort("IsUnlimited"),
                                        IsSimultaneous = reader.GetAsShort("IsSimultaneous"),
                                        IsRecurrent = reader.GetAsShort("IsRecurrent"),
                                        FrequencyRenewDays = reader.GetAsInt("FrequencyRenewDays"),
                                        TypeId = reader.GetAsShort("FeatureTypeId"),
                                        GroupId = reader.GetAsShort("GroupId"),
                                    });
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "GetProproductPortalFeatureByRepoProductsAsync", false, null, portalId);
                return null;
            }

            return productPortalFeatureDTO;
        }

        public async Task<List<ProProductPortalFeatureDTO>> GetProproductPortalFeatureByMasterAsync(short portalId, long productId)
        {
            List<ProProductPortalFeatureDTO> productPortalFeatureDTO = new List<ProProductPortalFeatureDTO>();
            try
            {
                using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
                {
                    await connection.OpenAsync();

                    using (var command = new MySqlCommand("GetProProductPortalForAddingCompanyProductFeature", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.Add(new MySqlParameter("_portal_id", portalId));
                        command.Parameters.Add(new MySqlParameter("_product_id", productId));

                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            if (reader != null)
                            {
                                while (reader.Read())
                                {
                                    productPortalFeatureDTO.Add(new ProProductPortalFeatureDTO
                                    {
                                        ProductId = reader.GetAsInt("product_id"),
                                        PortalId = reader.GetAsShort("portal_id"),
                                        AmbitId = reader.GetAsShort("ambit_id"),
                                        Id = reader.GetAsInt("id"),
                                        AvailableUnits = reader.GetAsInt("available_units"),
                                        InitialUnits = reader.GetAsInt("initial_units"),
                                        IsUnlimited = reader.GetAsShort("is_unlimited"),
                                        IsSimultaneous = reader.GetAsShort("is_simultaneous"),
                                        IsRecurrent = reader.GetAsShort("is_recurrent"),
                                        FrequencyRenewDays = reader.GetAsInt("frequency_renew_days"),
                                        TypeId = reader.GetAsShort("feature_type_id"),
                                        GroupId = reader.GetAsShort("group_id"),
                                    });
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "GetProproductPortalFeatureByMasterAsync", false, null, portalId);
                return null;
            }

            return productPortalFeatureDTO;
        }

        public async Task<long?> AddCompanyProductByRepoProductsAsync(AddCompanyProductDTO addCompanyProductDTO)
        {
            long id = 0;

            if (addCompanyProductDTO == null) return null;

            try
            {
                using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.RepoProduct, addCompanyProductDTO.PortalId)))
                {
                    await connection.OpenAsync();

                    using (var command = new MySqlCommand("AddCompanyProduct", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.Add(new MySqlParameter("_productId", addCompanyProductDTO.ProductId));
                        command.Parameters.Add(new MySqlParameter("_companyId", addCompanyProductDTO.CompanyId));
                        command.Parameters.Add(new MySqlParameter("_customerId", addCompanyProductDTO.CustomerId));
                        command.Parameters.Add(new MySqlParameter("_portalId", addCompanyProductDTO.PortalId));
                        command.Parameters.Add(new MySqlParameter("_userId", addCompanyProductDTO.UserId));
                        command.Parameters.Add(new MySqlParameter("_price", addCompanyProductDTO.Price));
                        command.Parameters.Add(new MySqlParameter("_serviceTypeId", addCompanyProductDTO.ServiceTypeId));
                        command.Parameters.Add(new MySqlParameter("_groupId", addCompanyProductDTO.GroupId));
                        command.Parameters.Add(new MySqlParameter("_sourceId", addCompanyProductDTO.SourceId));
                        command.Parameters.Add(new MySqlParameter("_paymentOriginId", addCompanyProductDTO.PaymentOriginId));
                        command.Parameters.Add(new MySqlParameter("_activationDate", addCompanyProductDTO.ActivationDate));
                        command.Parameters.Add(new MySqlParameter("_expirationDate", addCompanyProductDTO.ExpirationDate));
                        command.Parameters.Add(new MySqlParameter("_expirationDays", addCompanyProductDTO.ExpirationDays));
                        command.Parameters.Add(new MySqlParameter("_clientIp", addCompanyProductDTO.ClientIp));
                        command.Parameters.Add(new MySqlParameter("_productType", addCompanyProductDTO.ProductType));
                        command.Parameters.Add(new MySqlParameter("_limitationMultiplier", addCompanyProductDTO.LimitationMultiplier));
                        command.Parameters.Add(new MySqlParameter("_subgroupId", addCompanyProductDTO.SubgroupId));
                        command.Parameters.Add(new MySqlParameter("_idTemporality", addCompanyProductDTO.IdTemporality));
                        command.Parameters.Add(new MySqlParameter("_isConfigProductInMaster", addCompanyProductDTO.IsConfigProductInMaster));

                        var value = await command.ExecuteScalarAsync();

                        id = Convert.ToInt64(value ?? 0);
                    }
                }
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "AddCompanyProductByRepoProductsAsync", false, null, addCompanyProductDTO.PortalId);
                return null;
            }

            return id;
        }

        public async Task<long> AddCompanyProductFeatureByRepoProductsAsync(AddCompanyProductFeatureDTO addCompanyProductFeatureDTO)
        {
            long id = 0;

            if (addCompanyProductFeatureDTO == null) return 0;

            try
            {
                using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.RepoProduct, addCompanyProductFeatureDTO.PortalId)))
                {
                    await connection.OpenAsync();

                    using (var command = new MySqlCommand("AddCompanyProductFeature", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.Add(new MySqlParameter("_companyId", addCompanyProductFeatureDTO.CompanyId));
                        command.Parameters.Add(new MySqlParameter("_customerId", addCompanyProductFeatureDTO.CustomerId));
                        command.Parameters.Add(new MySqlParameter("_productId", addCompanyProductFeatureDTO.ProductId));
                        command.Parameters.Add(new MySqlParameter("_companyProductId", addCompanyProductFeatureDTO.CompanyProductId));
                        command.Parameters.Add(new MySqlParameter("_portalId", addCompanyProductFeatureDTO.PortalId));
                        command.Parameters.Add(new MySqlParameter("_ambitId", addCompanyProductFeatureDTO.AmbitId));
                        command.Parameters.Add(new MySqlParameter("_userId", addCompanyProductFeatureDTO.UserId));
                        command.Parameters.Add(new MySqlParameter("_portalFeatureId", addCompanyProductFeatureDTO.PortalFeatureId));
                        command.Parameters.Add(new MySqlParameter("_availableUnits", addCompanyProductFeatureDTO.AvailableUnits));
                        command.Parameters.Add(new MySqlParameter("_initialUnits", addCompanyProductFeatureDTO.InitialUnits));
                        command.Parameters.Add(new MySqlParameter("_isUnlimited", addCompanyProductFeatureDTO.IsUnlimited));
                        command.Parameters.Add(new MySqlParameter("_isSimultaneous", addCompanyProductFeatureDTO.IsSimultaneous));
                        command.Parameters.Add(new MySqlParameter("_isRecurrent", addCompanyProductFeatureDTO.IsRecurrent));
                        command.Parameters.Add(new MySqlParameter("_frequencyRenewDays", addCompanyProductFeatureDTO.FrequencyRenewDays));
                        command.Parameters.Add(new MySqlParameter("_featureTypeId", addCompanyProductFeatureDTO.FeatureTypeId));
                        command.Parameters.Add(new MySqlParameter("_groupId", addCompanyProductFeatureDTO.GroupId));

                        var value = await command.ExecuteScalarAsync();

                        id = Convert.ToInt64(value ?? 0);
                    }
                }
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "AddCompanyProductFeatureByRepoProductsAsync", false, null, addCompanyProductFeatureDTO.PortalId);
                return 0;
            }

            return id;
        }

        public async Task<int?> AddCompanyProductByMasterAsync(AddCompanyProductDTO addCompanyProductDTO)
        {
            int id = 0;

            if (addCompanyProductDTO == null) return id;

            try
            {
                using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, addCompanyProductDTO.PortalId)))
                {
                    await connection.OpenAsync();

                    using (var command = new MySqlCommand("ae_company_product_productfeature_insert_v2", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.Add(new MySqlParameter("_company_id", addCompanyProductDTO.CompanyId));
                        command.Parameters.Add(new MySqlParameter("_user_id", addCompanyProductDTO.UserId));
                        command.Parameters.Add(new MySqlParameter("_portal_id", addCompanyProductDTO.PortalId));
                        command.Parameters.Add(new MySqlParameter("_product_id", addCompanyProductDTO.ProductId));
                        command.Parameters.Add(new MySqlParameter("_payment_origin_id", addCompanyProductDTO.PaymentOriginId));
                        command.Parameters.Add(new MySqlParameter("_source_id", addCompanyProductDTO.SourceId));
                        command.Parameters.Add(new MySqlParameter("_price", addCompanyProductDTO.Price));
                        command.Parameters.Add(new MySqlParameter("_activation_date", addCompanyProductDTO.ActivationDate));
                        command.Parameters.Add(new MySqlParameter("_expiration_date", addCompanyProductDTO.ExpirationDate));
                        command.Parameters.Add(new MySqlParameter("_client_ip", addCompanyProductDTO.ClientIp));
                        command.Parameters.Add(new MySqlParameter("_idtemporality", addCompanyProductDTO.IdTemporality));
                        command.Parameters.Add(new MySqlParameter("_product_type", addCompanyProductDTO.ProductType));

                        var value = await command.ExecuteScalarAsync();

                        id = Convert.ToInt32(value ?? 0);
                    }
                }
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "AddCompanyProductByMasterAsync", false, null, addCompanyProductDTO.PortalId);
                return 0;
            }

            return id;
        }

        private List<CompanyProductEntity> AddListCompanyProductEntity(MySqlDataReader reader)
        {
            List<CompanyProductEntity> result = new List<CompanyProductEntity>();
            while (reader.Read())
            {
                result.Add(new CompanyProductEntity
                {
                    Id = reader.GetAsInt("Id"),
                    ProductId = reader.GetAsInt("ProductId"),
                    IdCompany = reader.GetAsInt("CompanyId"),
                    IdCustomer = reader.GetAsInt("CustomerId"),
                    PortalId = reader.GetAsShort("PortalId"),
                    DateAdd = reader.GetAsDateTime("DateAdd"),
                    DateMod = reader.GetAsDateTime("DateMod"),
                    IdUser = reader.GetAsInt("UserId"),
                    Price = reader.GetAsFloat("Price"),
                    ServiceTypeId = reader.GetAsShort("ServiceTypeId"),
                    SourceId = reader.GetAsShort("SourceId"),
                    PaymentOriginId = reader.GetAsShort("PaymentOriginId"),
                    DateActivation = reader.GetAsDateTime("DateActivation"),
                    DateExpiration = reader.GetAsDateTime("DateExpiration"),
                    ExpirationDays = reader.GetAsInt("ExpirationDays"),
                    StatusId = reader.GetAsShort("StatusId"),
                    GroupId = reader.GetAsShort("GroupId"),
                    ClientIP = reader.GetAsString("ClientIp"),
                    SubGroupId = reader.GetAsShort("SubgroupId"),
                    ProductTypeId = reader.GetAsShort("ProductType")
                });
            }
            return result;
        }

        public async Task<int?> AddCompanyProductCourtesyByMasterAsync(AddCompanyProductDTO addCompanyProductDTO)
        {
            int id = 0;

            if (addCompanyProductDTO == null) return null;

            try
            {
                //TODO MQC api_company_products_with_features_courtesy especial integrado para el caso de productos que sean cortesía y que se encuentren en la plantilla
                using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, addCompanyProductDTO.PortalId)))
                {
                    await connection.OpenAsync();

                    using (var command = new MySqlCommand("api_company_products_with_features_courtesy", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.Add(new MySqlParameter("_companyid", addCompanyProductDTO.CompanyId));
                        command.Parameters.Add(new MySqlParameter("_productid", addCompanyProductDTO.ProductId));
                        command.Parameters.Add(new MySqlParameter("_portalid", addCompanyProductDTO.PortalId));
                        command.Parameters.Add(new MySqlParameter("_userid", addCompanyProductDTO.UserId));
                        command.Parameters.Add(new MySqlParameter("_sourceid", addCompanyProductDTO.SourceId));
                        command.Parameters.Add(new MySqlParameter("_client_ip", addCompanyProductDTO.ClientIp));
                        command.Parameters.Add(new MySqlParameter("_date_activation", addCompanyProductDTO.ActivationDate));
                        command.Parameters.Add(new MySqlParameter("_date_expiration", addCompanyProductDTO.ExpirationDate));
                        command.Parameters.Add(new MySqlParameter("_expiration_days", addCompanyProductDTO.ExpirationDays));

                        var value = await command.ExecuteScalarAsync();

                        id = Convert.ToInt32(value ?? 0);
                    }
                }
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "AddCompanyProductCourtesyByMasterAsync", false, null, addCompanyProductDTO.PortalId);
                return null;
            }

            return id;
        }

        public async Task<ProProductPortalDTO> GetProProductPortalWithoutTemporalityByRepoProductsAsync(short portalId, long productId)
        {
            ProProductPortalDTO productPortalDTO = new ProProductPortalDTO();

            try
            {
                using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.RepoProduct, portalId)))
                {
                    await connection.OpenAsync();

                    using (var command = new MySqlCommand("GetProProductPortalForAddingCompanyProductCourtesy_v1", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.Add(new MySqlParameter("_portalId", portalId));
                        command.Parameters.Add(new MySqlParameter("_productId", productId));

                        using (var dtr = await command.ExecuteReaderAsync())
                        {
                            if (dtr != null)
                            {
                                if (dtr.Read())
                                {
                                    productPortalDTO = new ProProductPortalDTO()
                                    {
                                        ProductId = dtr.GetAsInt("ProductId"),
                                        PortalId = dtr.GetAsShort("PortalId"),
                                        ServiceTypeId = dtr.GetAsShort("ServiceTypeId"),
                                        GroupId = dtr.GetAsShort("GroupId"),
                                        ExpirationDays = dtr.GetAsInt("ExpirationDays"),
                                        LimitationMultiplier = dtr.GetAsShort("LimitationMultiplier"),
                                        SubGroupId = dtr.GetAsShort("SubgroupId"),
                                        TemporalityId = dtr.GetAsInt("IdTemporality"),
                                        IsActiveOCC = dtr.GetAsBoolean("IsActiveOCC"),
                                        ComercialName = dtr.GetAsString("ComercialName"),
                                        ProductTypeId = dtr.GetAsShort("ProductType"),
                                        IsObsolete = dtr.GetAsShort("IsObsolete"),
                                        InternalName = dtr.GetAsString("InternalName")
                                    };
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "GetProProductPortalWithoutTemporalityByRepoProductsAsync", false, null, portalId);
            }

            return productPortalDTO;
        }

        public async Task<int> UpdateStatusCompanyProductByRepoProductsAsync(long companyproductId, short portalId, StatusEnum status)
        {
            int result = 0;
            try
            {
                using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.RepoProduct, portalId)))
                {
                    await connection.OpenAsync();

                    using (var command = new MySqlCommand("UpdateStatusCompanyProduct", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.Add(new MySqlParameter("_companyProductId", companyproductId));
                        command.Parameters.Add(new MySqlParameter("_portalId", portalId));
                        command.Parameters.Add(new MySqlParameter("_statusId", (short)status));

                        result = await command.ExecuteNonQueryAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "UpdateStatusCompanyProductByRepoProductsAsync", false, null, portalId);
            }

            return result;
        }

        public async Task<int> UpdateStatusCompanyProductByMasterAsync(long companyproductId, short portalId, StatusEnum status)
        {
            int result = 0;
            try
            {
                using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
                {
                    await connection.OpenAsync();

                    using (var command = new MySqlCommand("UpdateStatusCompanyProduct", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.Add(new MySqlParameter("_companyProductId", companyproductId));
                        command.Parameters.Add(new MySqlParameter("_portalId", portalId));
                        command.Parameters.Add(new MySqlParameter("_statusId", (short)status));

                        result = await command.ExecuteNonQueryAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "UpdateStatusCompanyProductByMasterAsync", false, null, portalId);
            }

            return result;
        }

        public async Task<DateTime> SelectCompanyProductDateModByIdCompanyProductByRepoProductsAsync(long companyProductId, short portalId)
        {
            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.RepoProduct, portalId)))
            {
                using (MySqlCommand command = new MySqlCommand("CProductGetLastDateModByIdCompanyProductAndPortalId", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_IdPortal", portalId));
                    command.Parameters.Add(new MySqlParameter("_IdCompanyProduct", companyProductId));

                    try
                    {
                        connection.Open();
                        return (await command.ExecuteScalarAsync()).ToDateTime();
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "SelectCompanyProductDateModByIdCompanyProductByRepoProductsAsync", false, null, portalId);
                    }
                }
            }

            return DateTime.MinValue;
        }

        public async Task<CompanyProductEntity> GetByCompanyProductIdAndPortalIdByRepoProductsAsync(long companyProductId, short portalId)
        {
            try
            {
                using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.RepoProduct, portalId)))
                {
                    using (var command = new MySqlCommand("CompanyProductGetByCompanyProdIdAndPortalId", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.Add(new MySqlParameter("_CompanyProductId", companyProductId));
                        command.Parameters.Add(new MySqlParameter("_IdPortal", portalId));

                        connection.Open();

                        var companyProduct = new CompanyProductEntity();
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            while (reader.Read())
                            {
                                companyProduct = new CompanyProductEntity
                                {
                                    Id = reader.GetAsInt("Id"),
                                    ProductId = reader.GetAsInt("ProductId"),
                                    IdCompany = reader.GetAsInt("CompanyId"),
                                    IdCustomer = reader.GetAsInt("CustomerId"),
                                    PortalId = reader.GetAsShort("PortalId"),
                                    DateAdd = reader.GetAsDateTime("DateAdd"),
                                    DateMod = reader.GetAsDateTime("DateMod"),
                                    IdUser = reader.GetAsInt("UserId"),
                                    Price = reader.GetAsFloat("Price"),
                                    ServiceTypeId = reader.GetAsShort("ServiceTypeId"),
                                    SourceId = reader.GetAsShort("SourceId"),
                                    PaymentOriginId = reader.GetAsShort("PaymentOriginId"),
                                    DateActivation = reader.GetAsDateTime("DateActivation"),
                                    DateExpiration = reader.GetAsDateTime("DateExpiration"),
                                    ExpirationDays = reader.GetAsInt("ExpirationDays"),
                                    StatusId = reader.GetAsShort("StatusId"),
                                    ClientIP = reader.GetAsString("ClientIp"),
                                    GroupId = reader.GetAsShort("GroupId"),
                                    SubGroupId = reader.GetAsShort("SubgroupId"),
                                    LimitationMultiplier = reader.GetAsShort("LimitationMultiplier")
                                };
                            }
                        }

                        return companyProduct;
                    }
                }
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "GetByCompanyProductIdAndPortalIdByRepoProductsAsync", false, null, portalId);
                return null;
            }
        }

        public async Task<int> GetCompanyIdByCompanyProductIdByRepoProductsAsync(short portalId, long companyProductId)
        {
            int idCompany = 0;
            try
            {
                using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.RepoProduct, portalId)))
                {
                    using (var command = new MySqlCommand("GetCompanyIdByCompanyProductId", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.Add(new MySqlParameter("_portalId", portalId));
                        command.Parameters.Add(new MySqlParameter("_companyProductId", companyProductId));

                        connection.Open();
                        return (await command.ExecuteScalarAsync()).ToInt();
                    }
                }
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "GetCompanyIdByCompanyProductIdByRepoProductsAsync", false, null, portalId);
            }

            return idCompany;
        }

        public async Task<int> GetCompanyIdByCompanyProductIdByMasterAsync(short portalId, long companyProductId)
        {
            int idCompany = 0;
            try
            {
                using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
                {
                    using (var command = new MySqlCommand("GetCompanyIdByCompanyProductId", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.Add(new MySqlParameter("_portalId", portalId));
                        command.Parameters.Add(new MySqlParameter("_companyProductId", companyProductId));

                        connection.Open();
                        return (await command.ExecuteScalarAsync()).ToInt();
                    }
                }
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "GetCompanyIdByCompanyProductIdByMasterAsync", false, null, portalId);
            }

            return idCompany;
        }

        public async Task<DateTime> GetProductExpirationDate(GetProductExpirationDatePersistentLayerDTO getProductExpirationDateDTO)
        {
            var newExpirationDate = new DateTime();
            using (MySqlConnection conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, getProductExpirationDateDTO.portalId)))
            {
                using (MySqlCommand comm = new MySqlCommand("ae_GetProductExpirationDate", conn))
                {
                    comm.CommandType = CommandType.StoredProcedure;

                    try
                    {
                        conn.Open();

                        comm.Parameters.Add(new MySqlParameter("_idCompany", getProductExpirationDateDTO.idCompany));
                        comm.Parameters.Add(new MySqlParameter("_companyProductId", getProductExpirationDateDTO.companyProductId));
                        comm.Parameters.Add(new MySqlParameter("_portalId", getProductExpirationDateDTO.portalId));

                        var value = await comm.ExecuteScalarAsync();

                        newExpirationDate = Convert.ToDateTime(value ?? DateTime.MinValue);
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "GetProductExpirationDate");
                    }
                }
            }

            return newExpirationDate;
        }

        public async Task<bool?> SetSelfRenewingAsync(CompanyProductEntity cProduct, bool selfRenewing)
        {
            using (MySqlConnection conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, cProduct.PortalId)))
            {
                using (MySqlCommand comm = new MySqlCommand("update_self_renewing_from_company_products", conn))
                {
                    comm.CommandType = CommandType.StoredProcedure;

                    try
                    {
                        conn.Open();
                        comm.Parameters.Add(new MySqlParameter("_id", cProduct.Id));
                        comm.Parameters.Add(new MySqlParameter("_idcompany", cProduct.IdCompany));
                        comm.Parameters.Add(new MySqlParameter("_self_renewing", selfRenewing ? 1 : 0));
                        comm.Parameters.Add(new MySqlParameter("_idportal", cProduct.PortalId));

                        return await comm.ExecuteNonQueryAsync() > 0;
                    }
                    catch (Exception ex)
                    {
                        _exceptionPublisherService.Publish(ex, "CompanyProductRecoverAndPersist", "SetSelfRenewingAsync", false, null, cProduct.PortalId);
                        return null;
                    }
                }
            }
        }        
    }
}
