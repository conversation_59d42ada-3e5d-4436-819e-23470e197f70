using MySql.Data.MySqlClient;
using Org.BouncyCastle.Bcpg.Sig;
using Redarbor.Common.Entities.Configuration;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Core.Resolver.Contracts.ServiceLibrary;
using Redarbor.Core.Resolver.Contracts.ServiceLibrary.Enums;
using Redarbor.Extensions.Library.Extensions;
using Redarbor.Master.Entities.Enums;
using Redarbor.Master.Entities.Offers;
using Redarbor.Master.Entities.Product;
using Redarbor.Master.Entities.Searchs;
using Redarbor.Products.Library.DomainServiceContracts;
using Redarbor.Products.Library.DTOs;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Threading.Tasks;

namespace Redarbor.Products.Library.DomainServicesImplementations
{
    public class ProductRecoverAndPersist : IProductRecoverAndPersist
    {
        private readonly IConnectionStringResolverService _connectionStringResolverService;
        private readonly IExceptionPublisherService _exceptionPublisherService;

        public ProductRecoverAndPersist(IConnectionStringResolverService connectionStringResolverService,
            IExceptionPublisherService exceptionPublisherService)
        {
            _connectionStringResolverService = connectionStringResolverService;
            _exceptionPublisherService = exceptionPublisherService;
        }

        public ProductEntity Select(ProductSearchSpecifications productSpecifications)
        {
            var product = new ProductEntity();
            if (productSpecifications.Id != 0)
            {
                product = TrySelect(productSpecifications);
            }

            return product;
        }

        public List<PromotionEntity> SelectAllPromotions(PromotionSearchSpecifications specifications)
        {
            return (specifications == null) ? new List<PromotionEntity>() : GetAllPromotions(specifications);
        }

        public SegmentCompanyEntity GetSegmentByCompanyId(PromotionSearchSpecifications specifications)
        {
            if (specifications == null || specifications.PortalId == 0 || specifications.CompanyId == 0)
                return new SegmentCompanyEntity();

            return TryGetCompanySegment(specifications);
        }

        public void Insert(PromotionEntity promotion)
        {
            if (promotion == null)
                return;

            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
            {
                using (MySqlCommand command = new MySqlCommand("insert_promotion_v4", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_date_start", promotion.DateStart));
                    command.Parameters.Add(new MySqlParameter("_date_end", promotion.DateEnd));
                    command.Parameters.Add(new MySqlParameter("_promotion_name", promotion.PromotionName));
                    command.Parameters.Add(new MySqlParameter("_idportal", promotion.PortalId));
                    command.Parameters.Add(new MySqlParameter("_lit_total_price", promotion.LiteralTotalPrice));
                    command.Parameters.Add(new MySqlParameter("_lit_percent_discount", promotion.LiteralPercentDiscount));
                    command.Parameters.Add(new MySqlParameter("_lit_old_price", promotion.LiteralOldPrice));
                    command.Parameters.Add(new MySqlParameter("_status_id", promotion.StatusId));
                    command.Parameters.Add(new MySqlParameter("_product_id", promotion.ProductId));
                    command.Parameters.Add(new MySqlParameter("_url", promotion.Url));
                    command.Parameters.Add(new MySqlParameter("_promotional_code", promotion.PromotionalCode));
                    command.Parameters.Add(new MySqlParameter("_apply_to_crm_companies", promotion.ApplyToCrmCompanies));
                    command.Parameters.Add(new MySqlParameter("_preferent_promotion", promotion.Preferent));
                    command.Parameters.Add(new MySqlParameter("_total_save_up_price", promotion.LiteralTotalSaveUpPrice));
                    command.Parameters.Add(new MySqlParameter("_price", promotion.NumericPrice));
                    command.Parameters.Add(new MySqlParameter("_lit_price_unity", promotion.LiteralPriceUnity));
                    command.Parameters.Add(new MySqlParameter("_temporalityId", promotion.TemporalityId));
                    command.Parameters.Add(new MySqlParameter("_temporalityUnitId", promotion.TemporalityUnitId));
                    command.Parameters.Add(new MySqlParameter("_typePromotion", promotion.TypePromotion));
                    command.Parameters.Add(new MySqlParameter("_comercialNotImportant", promotion.ComercialNotImportant));

                    try
                    {
                        connection.Open();

                        int.TryParse(command.ExecuteScalar().ToStringSafely(), out int id);
                        promotion.Id = id;
                    }
                    catch (Exception ex)
                    {
                        connection.Close();
                        Trace.TraceError($"ProductRecoverAndPersist -- Insert ex: {ex.ToString()}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "Insert");
                        promotion.Id = 0;
                    }
                }
            }
        }

        public void InsertSegment(PromotionEntity promotion)
        {
            if (promotion != null)
            {
                TryInsertSegment(promotion.Segments, promotion.Id, promotion.TypePromotion);
            }
        }

        public DateTime GetLastMaxDateTimePromotion(int portalId, short typePromotion, int idCompany = 0)
        {
            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, (short)portalId)))
            {
                using (MySqlCommand command = new MySqlCommand("ae_promotion_getmaxdate", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_id_portal", portalId));
                    command.Parameters.Add(new MySqlParameter("_typePromotion", typePromotion));
                    command.Parameters.Add(new MySqlParameter("_idCompany", idCompany));

                    try
                    {
                        connection.Open();
                        var maxDateScalar = command.ExecuteScalar();

                        var maxDate = DateTime.MinValue;

                        if (maxDateScalar != null)
                            DateTime.TryParse(maxDateScalar.ToString(), out maxDate);

                        return maxDate;
                    }
                    catch (Exception ex)
                    {
                        connection.Close();
                        Trace.TraceError($"ProductRecoverAndPersist -- GetLastMaxDateTimePromotion ex: {ex.ToString()}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "GetLastMaxDateTimePromotion");
                        return DateTime.MinValue;
                    }
                }
            }
        }

        public List<ProductEntity> GetProductsByPage(ProductSearchSpecifications productSpecifications)
        {
            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, productSpecifications.PortalId)))
            {
                using (MySqlCommand command = new MySqlCommand("ae_getProductsByPageAndSubGroup_v1", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_idportal", productSpecifications.PortalId));
                    command.Parameters.Add(new MySqlParameter("_idpage", productSpecifications.PageId));
                    command.Parameters.Add(new MySqlParameter("_subgroup", productSpecifications.SubGroup));

                    var products = new List<ProductEntity>();

                    try
                    {
                        connection.Open();
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader != null)
                            {
                                while (reader.Read())
                                {
                                    var product = new ProductEntity()
                                    {
                                        Id = reader.GetAsInt("product_id"),
                                        TemporalityId = reader.GetAsInt("temporality_id"),
                                        ComercialName = reader.GetAsString("comercial_name"),
                                        LiteralShowPrice = reader.GetAsString("literal_show_price"),
                                        Discount = reader.GetAsInt("discount"),
                                        Price = reader.GetAsInt("price"),
                                        MonthPrice = reader.GetAsInt("month_price"),
                                        LiteralShowTotal = reader.GetAsString("literal_show_total"),
                                        LiteralShowUnit = reader.GetAsString("literal_show_unit"),
                                        GroupId = reader.GetAsInt("group_id"),
                                        Priority = reader.GetAsInt("priority"),
                                        PortalId = productSpecifications.PortalId,
                                        Recommended = reader.GetAsBoolean("recommended"),
                                        SubGroupId = reader.GetAsShort("subgroup_id"),
                                        Saving = reader.GetAsDecimal("saving"),
                                        InitialUnits = reader.GetAsInt("initial_units"),
                                        Default = reader.GetBoolean("is_default")
                                    };

                                    products.Add(product);
                                }
                            }
                        }
                        return products;
                    }
                    catch (Exception ex)
                    {
                        connection.Close();
                        Trace.TraceError($"ProductRecoverAndPersist -- SearchByPage ex: {ex.ToString()}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "SearchByPage");

                        return new List<ProductEntity>();
                    }
                }
            }
        }

        public List<ProductEntity> GetProducts(ProductSearchSpecifications specifications)
        {
            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
            {
                using (MySqlCommand command = new MySqlCommand("get_products_V3", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_portalId", specifications.PortalId));

                    var listProducts = new List<ProductEntity>();

                    try
                    {
                        connection.Open();

                        using (var reader = command.ExecuteReader())
                        {
                            if (reader != null)
                            {
                                while (reader.Read())
                                {
                                    listProducts.Add(
                                        new ProductEntity
                                        {
                                            PortalId = reader.GetAsInt("portal_id"),
                                            Id = reader.GetAsInt("product_id"),
                                            ComercialName = reader.GetAsString("comercial_name"),
                                            SetterAvailableInCrm = reader.GetAsShort("available_in_crm"),
                                            SetterUniqueLoginActive = reader.GetAsShort("unique_login_active"),
                                            ServiceTypeID = reader.GetAsShort("service_type_id"),
                                            AssignUserTypeCrm = reader.GetAsShort("assign_user_type_crm"),
                                            GroupId = reader.GetAsShort("group_id"),
                                            StatusId = reader.GetAsShort("status_id"),
                                            TemporalityId = reader.GetAsInt("temporality_id"),
                                            ExpirationDays = reader.GetAsInt("expiration_days"),
                                            Price = reader.GetAsFloat("price"),
                                            LiteralShowPrice = reader.GetAsString("literal_show_price"),
                                            MonthPrice = reader.GetAsInt("month_price"),
                                            LiteralShowTotal = reader.GetAsString("literal_show_total"),
                                            LiteralShowUnit = reader.GetAsString("literal_show_unit"),
                                            Saving = reader.GetAsDecimal("saving"),
                                            SubGroupId = reader.GetAsShort("subgroup_id")
                                        }
                                        );
                                }
                            }
                        }
                        return listProducts;
                    }
                    catch (Exception ex)
                    {
                        connection.Close();
                        Trace.TraceError($"ProductRecoverAndPersist -- GetProducts ex: {ex.ToString()}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "GetProducts");
                        return new List<ProductEntity>();
                    }
                }
            }
        }

        public int GetInitialUnitsByAmbit(int productId, short portalId, int ambitId)
        {
            int initialUnits = 0;

            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
            {
                using (MySqlCommand command = new MySqlCommand("sp_pro_product_portal_features_get_initial_by_ambit", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_idproduct", productId));
                    command.Parameters.Add(new MySqlParameter("_idportal", portalId));
                    command.Parameters.Add(new MySqlParameter("_idambit", ambitId));

                    try
                    {
                        connection.Open();

                        var result = command.ExecuteScalar();

                        if (result != null)
                            int.TryParse(result.ToString(), out initialUnits);
                    }
                    catch (Exception ex)
                    {
                        connection.Close();
                        Trace.TraceError($"ProductRecoverAndPersist -- TryGetInitialUnitsByAmbit ex: {ex.ToString()}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "TryGetInitialUnitsByAmbit");
                    }
                }
            }

            return initialUnits;
        }

        public List<ProductEntity> GetByIdsProducts(short portalId, string idsProducts)
        {

            using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
            {
                using (var command = new MySqlCommand("sp_pro_product_portal_get_by_ids_products_v2", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    command.Parameters.Add(new MySqlParameter("_idportal", portalId));
                    command.Parameters.Add(new MySqlParameter("_idsproducts", idsProducts));
                    try
                    {
                        connection.Open();

                        var products = new List<ProductEntity>();
                        using (var datareader = command.ExecuteReader())
                        {
                            if (datareader != null)
                            {
                                while (datareader.Read())
                                {
                                    products.Add(new ProductEntity
                                    {
                                        Id = datareader.GetAsInt("product_id"),
                                        TemporalityId = datareader.GetAsInt("temporality_id"),
                                        ComercialName = datareader.GetAsString("comercial_name"),
                                        LiteralShowPrice = datareader.GetAsString("literal_show_price"),
                                        Discount = datareader.GetAsInt("discount"),
                                        Price = datareader.GetAsInt("price"),
                                        MonthPrice = datareader.GetAsInt("month_price"),
                                        LiteralShowTotal = datareader.GetAsString("literal_show_total"),
                                        LiteralShowUnit = datareader.GetAsString("literal_show_unit"),
                                        GroupId = datareader.GetAsShort("group_id"),
                                        SubGroupId = datareader.GetAsShort("subgroup_id"),
                                        Saving = datareader.GetAsInt("saving")
                                    });
                                }
                            }

                            return products;
                        }
                    }
                    catch (Exception ex)
                    {
                        connection.Close();
                        Trace.TraceError($"ProductRecoverAndPersist -- GetByIdsProducts ex: {ex.ToString()}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "GetByIdsProducts");
                        return new List<ProductEntity>();
                    }
                }
            }

        }

        public List<ProductFeatureEntity> GetProductFeatures(ProductSearchSpecifications productSpecifications)
        {

            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
            {
                using (MySqlCommand command = new MySqlCommand("sp_pro_product_portal_features_get_by_product", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_idportal", productSpecifications.PortalId));
                    command.Parameters.Add(new MySqlParameter("_idproduct", productSpecifications.Id));
                    try
                    {
                        var productFeatures = new List<ProductFeatureEntity>();
                        connection.Open();

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var productFeature = new ProductFeatureEntity()
                                {
                                    Id = reader.GetAsInt("id"),
                                    ProductId = reader.GetAsInt("product_id"),
                                    PortalId = reader.GetAsShort("portal_id"),
                                    AmbitId = reader.GetAsShort("ambit_id"),
                                    AvailableUnits = reader.GetAsInt("available_units"),
                                    InitialUnits = reader.GetAsInt("initial_units"),
                                    IsUnlimited = reader.GetAsShort("is_unlimited") == 1,
                                    IsSimultaneous = reader.GetAsShort("is_simultaneous") == 1,
                                    IsRecurrent = reader.GetAsShort("is_recurrent") > 0,
                                    FrequencyRenewDays = reader.GetAsInt("frequency_renew_days"),
                                    TypeId = reader.GetAsShort("feature_type_id")
                                };

                                productFeatures.Add(productFeature);
                            }
                        }

                        return productFeatures;
                    }
                    catch (Exception ex)
                    {
                        connection.Close();
                        Trace.TraceError($"ProductRecoverAndPersist -- GetProductFeatures ex: {ex.ToString()}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "GetProductFeatures");
                        return new List<ProductFeatureEntity>();
                    }
                }
            }

        }

        public List<ProductFeatureDescriptionEntity> GetSubGroupFeaturesDescriptions(short idSubGroup, short idPortal)
        {

            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
            {
                using (MySqlCommand command = new MySqlCommand("ae_GetSubGroupFeaturesDescriptions_v2", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_idportal", idPortal));
                    command.Parameters.Add(new MySqlParameter("_idsubgroup", idSubGroup));
                    try
                    {
                        var productFeaturesDescriptions = new List<ProductFeatureDescriptionEntity>();
                        connection.Open();

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var productFeature = new ProductFeatureDescriptionEntity()
                                {
                                    Literal = reader.GetAsString("literal"),
                                    Place = reader.GetAsShort("place"),
                                    PortalId = reader.GetAsShort("portal_id"),
                                    Priority = reader.GetAsShort("priority"),
                                    ProductId = reader.GetAsInt("idproduct"),
                                    Available = reader.GetAsShort("available"),
                                    LandingId = reader.GetAsShort("idlanding"),
                                    SubGroupId = reader.GetAsShort("idsubgroup")
                                };

                                productFeaturesDescriptions.Add(productFeature);
                            }
                        }

                        return productFeaturesDescriptions;
                    }
                    catch (Exception ex)
                    {
                        connection.Close();
                        Trace.TraceError($"ProductRecoverAndPersist -- GetSubGroupFeaturesDescriptions ex: {ex.ToString()}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "GetSubGroupFeaturesDescriptions");
                        return new List<ProductFeatureDescriptionEntity>();
                    }
                }
            }
        }
        public CurrencyEntity GetCurrency(short portalId)
        {

            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
            {
                using (MySqlCommand command = new MySqlCommand("sp_ms_currency_SelectByIdPortal", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    command.Parameters.Add(new MySqlParameter("_idportal", portalId));
                    try
                    {
                        connection.Open();

                        var currency = new CurrencyEntity();
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                currency.Description = reader.GetAsString("description");
                                currency.Currency = reader.GetAsString("currency");
                                currency.Pais = reader.GetAsString("pais");
                            }
                        }

                        return currency;
                    }
                    catch (Exception ex)
                    {
                        connection.Close();
                        Trace.TraceError($"ProductRecoverAndPersist -- GetCurrency ex: {ex.ToString()}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "GetCurrency");
                        return new CurrencyEntity();
                    }
                }
            }

        }

        

        public List<ProductEntity> GetWelcomePacks(short portalId)
        {
            var productEntities = new List<ProductEntity>();
            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
            {
                using (MySqlCommand command = new MySqlCommand("pro_product_portal_new_company_get_temporality_v2", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    command.Parameters.Add(new MySqlParameter("_portal_id", portalId));
                    try
                    {
                        connection.Open();
                        using (var dataReader = command.ExecuteReader())
                        {
                            if (dataReader != null)
                            {
                                while (dataReader.Read())
                                {
                                    productEntities.Add(new ProductEntity()
                                    {
                                        Id = dataReader.GetInt32("product_id"),
                                        Price = dataReader.GetInt32("price"),
                                        PortalId = dataReader.GetInt32("portal_id"),
                                        ExpirationDays = dataReader.GetInt32("expiration_days"),
                                        CurrencyId = dataReader.GetInt16("currency_id"),
                                        Discount = dataReader.GetInt32("discount"),
                                        ComercialName = dataReader.GetString("comercial_name"),
                                        GroupId = dataReader.GetInt32("group_id"),
                                        TemporalityId = dataReader.GetInt32("temporality_id")
                                    });
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        connection.Close();
                        Trace.TraceError($"ProductRecoverAndPersist -- GetWelcomePacks ex: {ex.ToString()}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "GetWelcomePacks");
                    }
                }
            }
            return productEntities;
        }

        #region Private methods

        private void TryInsertSegment(SegmentPromotionEntity segment, int promotionId, short typePromotion)
        {
            if (typePromotion == (short)PromotionTypeEnum.AutomaticPacks || typePromotion == (short)PromotionTypeEnum.VoucherPacks)
            {
                foreach (var item in segment.CompanyIds)
                {
                    TryInsertSegmentCompanyId(promotionId, item);
                }
            }
        }

        private void TryInsertSegmentCompanyId(int promotionId, int companyId)
        {
            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
            {
                using (MySqlCommand command = new MySqlCommand("insert_promotion_segment_companies", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_idPromotion", promotionId));
                    command.Parameters.Add(new MySqlParameter("_idCompany", companyId));

                    try
                    {
                        connection.Open();
                        command.ExecuteNonQuery();
                    }
                    catch (Exception ex)
                    {
                        connection.Close();
                        Trace.TraceError($"ProductRecoverAndPersist -- InsertSegmentCompanyId ex: {ex.ToString()}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "InsertSegmentCompanyId");
                    }
                }
            }
        }

        private SegmentCompanyEntity TryGetCompanySegment(PromotionSearchSpecifications specifications)
        {
            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master)))
            {
                using (MySqlCommand command = new MySqlCommand("get_segment_company_V2", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_company_id", specifications.CompanyId));
                    command.Parameters.Add(new MySqlParameter("_portal_id", specifications.PortalId));

                    try
                    {
                        connection.Open();

                        using (var reader = command.ExecuteReader())
                        {
                            if (reader != null && reader.Read())
                            {
                                return new SegmentCompanyEntity()
                                {
                                    CompanyId = specifications.CompanyId,
                                    EmploymentNumberId = reader.GetAsInt("idemploymentnumber"),
                                    LocationId = reader.GetAsInt("idlocalization"),
                                    SectorId = reader.GetAsInt("idindustry"),
                                    CretedonCompany = reader.GetAsDateTime("createdon"),
                                    LastLogin = reader.GetAsDateTime("date_last_login"),
                                    LastDateCompanyProduct = reader.GetAsDateTime("last_date_company_product"),
                                    SegmentationId = reader.GetAsInt("id_segmentation")
                                };
                            }
                        }
                        return new SegmentCompanyEntity();
                    }
                    catch (Exception ex)
                    {
                        connection.Close();
                        Trace.TraceError($"ProductRecoverAndPersist -- GetSegmentByCompanyId ex: {ex.ToString()}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "GetSegmentByCompanyId");
                        return new SegmentCompanyEntity();
                    }
                }
            }
        }

        private void AddSegment(List<int> segment, int element)
        {
            if (element > 0 && !segment.Exists(el => el == element))
                segment.Add(element);
        }

        public PromotionEntity GetPromotionById(short portalId, int promotionId)
        {
            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
            {
                using (MySqlCommand command = new MySqlCommand("ae_promotion_get_byId_v2", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_idportal", portalId));
                    command.Parameters.Add(new MySqlParameter("_idpromotion", promotionId));

                    try
                    {
                        connection.Open();

                        using (var reader = command.ExecuteReader())
                        {
                            if (reader != null && reader.Read())
                            {
                                return new PromotionEntity
                                {
                                    Id = reader.GetAsInt("id"),
                                    DateAdd = reader.GetAsDateTime("date_add"),
                                    DateMod = reader.GetAsDateTime("date_mod"),
                                    DateStart = reader.GetAsDateTime("date_start"),
                                    DateEnd = reader.GetAsDateTime("date_end"),
                                    PromotionName = reader.GetAsString("promotion_name"),
                                    PortalId = reader.GetAsShort("idportal"),
                                    LiteralTotalPrice = reader.GetAsString("lit_total_price"),
                                    LiteralPercentDiscount = reader.GetAsString("lit_percent_discount"),
                                    LiteralOldPrice = reader.GetAsString("lit_old_price"),
                                    NumericPrice = reader.GetAsFloat("price"),
                                    StatusId = reader.GetAsInt("status_id"),
                                    ProductId = reader.GetAsInt("product_id"),
                                    Url = reader.GetAsString("url"),
                                    PromotionalCode = reader.GetAsString("promotional_code"),
                                    ApplyToCrmCompanies = reader.GetAsBoolean("apply_to_crm_companies"),
                                    Preferent = reader.GetAsInt("preferent_promotion"),
                                    LiteralTotalSaveUpPrice = reader.GetAsString("total_save_up_price"),
                                    LiteralPriceUnity = reader.GetAsString("lit_price_unity"),
                                    TemporalityId = reader.GetAsShort("temporalityId"),
                                    TemporalityUnitId = reader.GetAsShort("temporalityUnitId"),
                                    TypePromotion = reader.GetAsShort("typePromotion"),
                                    ComercialNotImportant = reader.GetAsBoolean("comercialNotImportant")
                                };
                            }
                        }
                        return new PromotionEntity();
                    }
                    catch (Exception ex)
                    {
                        connection.Close();
                        Trace.TraceError($"ProductRecoverAndPersist -- GetPromotionById ex: {ex.ToString()}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "GetPromotionById");
                        return new PromotionEntity();
                    }
                }
            }
        }

        private List<PromotionEntity> GetAllPromotions(PromotionSearchSpecifications specifications)
        {
            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, specifications.PortalId)))
            {
                using (MySqlCommand command = new MySqlCommand("ae_promotions_get_v4", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_id_portal", specifications.PortalId));
                    command.Parameters.Add(new MySqlParameter("_productId", specifications.ProductId));
                    command.Parameters.Add(new MySqlParameter("_statusId", specifications.StatusId));
                    command.Parameters.Add(new MySqlParameter("_typePromotion", specifications.TypePromotion));
                    command.Parameters.Add(new MySqlParameter("_idCompany", specifications.CompanyId));

                    var promotions = new List<PromotionEntity>();
                    try
                    {
                        connection.Open();

                        using (var reader = command.ExecuteReader())
                        {
                            if (reader != null)
                            {
                                while (reader.Read())
                                {
                                    if (promotions.Find(p => p.Id == reader.GetAsInt("id")) == null)
                                    {
                                        promotions.Add(
                                            new PromotionEntity()
                                            {
                                                Id = reader.GetAsInt("id"),
                                                DateAdd = reader.GetAsDateTime("date_add"),
                                                DateMod = reader.GetAsDateTime("date_mod"),
                                                DateStart = reader.GetAsDateTime("date_start"),
                                                DateEnd = reader.GetAsDateTime("date_end"),
                                                PromotionName = reader.GetAsString("promotion_name"),
                                                PortalId = reader.GetAsShort("idportal"),
                                                LiteralTotalPrice = reader.GetAsString("lit_total_price"),
                                                LiteralPercentDiscount = reader.GetAsString("lit_percent_discount"),
                                                LiteralOldPrice = reader.GetAsString("lit_old_price"),
                                                NumericPrice = reader.GetAsFloat("price"),
                                                StatusId = reader.GetAsInt("status_id"),
                                                ProductId = reader.GetAsInt("product_id"),
                                                Url = reader.GetAsString("url"),
                                                PromotionalCode = reader.GetAsString("promotional_code"),
                                                ApplyToCrmCompanies = reader.GetAsBoolean("apply_to_crm_companies"),
                                                Preferent = reader.GetAsInt("preferent_promotion"),
                                                LiteralTotalSaveUpPrice = reader.GetAsString("total_save_up_price"),
                                                LiteralPriceUnity = reader.GetAsString("lit_price_unity"),
                                                TemporalityId = reader.GetAsShort("temporalityId"),
                                                TemporalityUnitId = reader.GetAsShort("temporalityUnitId"),
                                                TypePromotion = reader.GetAsShort("typePromotion"),
                                                ComercialNotImportant = reader.GetAsBoolean("comercialNotImportant")
                                            }
                                            );
                                    }

                                    if (promotions.Find(p => p.Id == reader.GetAsInt("id")) != null)
                                    {
                                        var indexPromotion = promotions.FindIndex(p => p.Id == reader.GetAsInt("id"));
                                        var promotion = promotions[indexPromotion];

                                        var segments = promotion.Segments;

                                        AddSegment(segments.SectorIds, reader.GetAsInt("idindustry"));
                                        AddSegment(segments.EmploymentNumberIds, reader.GetAsInt("idemploymentnumber"));
                                        AddSegment(segments.LocationIds, reader.GetAsInt("idlocalization"));
                                        AddSegment(segments.CompanyIds, reader.GetAsInt("idcompany"));

                                        var comercialAsigned = reader.GetAsShort("commercial_assigned");
                                        if (comercialAsigned == 1)
                                            segments.SetterComercialAsigned = true;

                                        var lastDateActivityCrm = reader.GetAsDateTime("last_activity_crm_date");
                                        if (lastDateActivityCrm != DateTime.MinValue)
                                            segments.LastDateActivityCrm = lastDateActivityCrm;

                                        var lastDateCompanyProduct = reader.GetAsDateTime("last_buy_product_from_date");
                                        if (lastDateCompanyProduct != DateTime.MinValue)
                                            segments.LastDateCompanyProductFrom = lastDateCompanyProduct;

                                        var lastDateCompanyProductTo = reader.GetAsDateTime("last_buy_product_to_date");
                                        if (lastDateCompanyProductTo != DateTime.MinValue)
                                            segments.LastDateCompanyProductTo = lastDateCompanyProductTo;

                                        var createdonCompany = reader.GetAsDateTime("register_company_from_date");
                                        if (createdonCompany != DateTime.MinValue)
                                            segments.CretedonCompanyFrom = createdonCompany;

                                        var createdonCompanyTo = reader.GetAsDateTime("register_company_to_date");
                                        if (createdonCompanyTo != DateTime.MinValue)
                                            segments.CretedonCompanyTo = createdonCompanyTo;

                                        var lastLogin = reader.GetAsDateTime("last_login_from_date");
                                        if (lastLogin != DateTime.MinValue)
                                            segments.LastLoginFrom = lastLogin;

                                        var lastLoginTo = reader.GetAsDateTime("last_login_to_date");
                                        if (lastLoginTo != DateTime.MinValue)
                                            segments.LastLoginTo = lastLoginTo;

                                        var lastLoginCompany = reader.GetAsDateTime("lastlogin");
                                        if (lastLoginCompany != DateTime.MinValue)
                                            segments.LastLogin = lastLoginCompany;

                                        var createdOnCompany = reader.GetAsDateTime("createdon");
                                        if (createdOnCompany != DateTime.MinValue)
                                            segments.CreatedOn = createdOnCompany;

                                        var lastBuyDate = reader.GetAsDateTime("last_buy_date");
                                        if (lastBuyDate != DateTime.MinValue)
                                            segments.LastBuy = lastBuyDate;
                                    }
                                }
                            }

                        }
                        return promotions;
                    }
                    catch (Exception ex)
                    {
                        connection.Close();
                        Trace.TraceError($"ProductRecoverAndPersist -- SelectAllPromotions ex: {ex.ToString()}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "SelectAllPromotions");
                        return new List<PromotionEntity>();
                    }
                }
            }
        }

        private ProductEntity TrySelect(ProductSearchSpecifications productSpecifications)
        {
            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, productSpecifications.PortalId)))
            {
                using (MySqlCommand command = new MySqlCommand("product_get", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_idportal", productSpecifications.PortalId));
                    command.Parameters.Add(new MySqlParameter("_idproduct", productSpecifications.Id));
                    command.Parameters.Add(new MySqlParameter("_idtemporality", productSpecifications.TemporalityId));

                    try
                    {
                        connection.Open();

                        using (var reader = command.ExecuteReader())
                        {
                            if (reader != null && reader.Read())
                            {
                                return new ProductEntity
                                {
                                    Id = reader.GetAsInt("product_id"),
                                    ComercialName = reader.GetAsString("comercial_name"),
                                    Price = reader.GetAsFloat("price"),
                                    GroupId = reader.GetAsInt("group_id"),
                                    TemporalityId = reader.GetAsInt("temporality_id"),
                                    ExpirationDays = reader.GetAsInt("expiration_days"),
                                    PortalId = reader.GetAsInt("portal_id"),
                                    SubGroupId = reader.GetAsShort("subgroup_id")
                                };
                            }
                        }
                        return new ProductEntity();
                    }
                    catch (Exception ex)
                    {
                        connection.Close();
                        Trace.TraceError($"ProductRecoverAndPersist -- Select ex: {ex.ToString()}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "Select");
                        return new ProductEntity();
                    }
                }
            }
        }

        public List<ProductVoucherConfigEntity> GetProductVoucherConfig(short portalId, short statusId)
        {
            try
            {
                var productVoucherConfigList = new List<ProductVoucherConfigEntity>();

                using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
                {
                    using (var command = new MySqlCommand("ae_productvoucherconfig_get", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.Add(new MySqlParameter("_portalId", portalId));
                        command.Parameters.Add(new MySqlParameter("_statusId", statusId));

                        connection.Open();

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                productVoucherConfigList.Add(
                                    new ProductVoucherConfigEntity()
                                    {
                                        Id = reader.GetAsInt("id"),
                                        ProductId = reader.GetAsInt("idProduct"),
                                        PortalId = reader.GetAsShort("idPortal"),
                                        Discount = reader.GetAsInt("discount"),
                                        StatusId = reader.GetAsShort("idStatus"),
                                        DateAdd = reader.GetAsDateTime("dateAdd"),
                                        DateMod = reader.GetAsDateTime("dateMod"),
                                    });
                            }
                        }
                    }
                }
                return productVoucherConfigList;
            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "GetProductVoucherConfig");
            }

            return new List<ProductVoucherConfigEntity>();
        }

        public string GetProductDescriptionByProductId(short idPortal, int idProduct)
        {
            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, idPortal)))
            {
                using (MySqlCommand command = new MySqlCommand("getproductdescriptionbyproductid", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_idproduct", idProduct));
                    command.Parameters.Add(new MySqlParameter("_idportal", idPortal));

                    try
                    {
                        connection.Open();
                        return command.ExecuteScalar().ToStringSafely();
                    }
                    catch (Exception ex)
                    {
                        connection.Close();
                        Trace.TraceError($"ProductRecoverAndPersist -- GetProductDescriptionByProductId ex: {ex.ToString()}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "GetProductDescriptionByProductId", false, new Dictionary<string, string>() { { "productId", idProduct.ToString() }, { "portalId", idPortal.ToString() } });
                    }
                }
            }
            return string.Empty;

        }


        #endregion

        public List<ConfigurationAutomaticPromotionEntity> GetConfigurationByProductId(int productId, short portalId)
        {
            var result = new List<ConfigurationAutomaticPromotionEntity>();
            using (var conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
            {
                using (var cmd = new MySqlCommand("ae_get_configuration_promotions", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    try
                    {
                        cmd.Parameters.Add(new MySqlParameter("_idportal", portalId));
                        cmd.Parameters.Add(new MySqlParameter("_idproduct", productId));
                        conn.Open();
                        using (var reader = cmd.ExecuteReader())
                        {
                            if (reader != null)
                            {
                                while (reader.Read())
                                {
                                    result.Add(
                                        new ConfigurationAutomaticPromotionEntity
                                        {
                                            Id = reader.GetAsInt("id"),
                                            DaysPromo = reader.GetAsShort("daysByPromo"),
                                            Discount = reader.GetAsShort("discount"),
                                            IdPortal = reader.GetAsShort("idPortal"),
                                            IdProduct = reader.GetAsInt("idProduct"),
                                            IdProductNew = reader.GetAsInt("newIdProduct"),
                                            IdStatus = reader.GetAsShort("idStatus"),
                                            CreatedOn = reader.GetAsDateTime("dateAdd"),
                                            DateMod = reader.GetAsDateTime("dateMod"),
                                            CanalWebNotImportant = reader.GetAsBoolean("canalWebNotImportant"),
                                            ComercialNotImportant = reader.GetAsBoolean("comercialNotImportant"),
                                        });
                                };
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        conn.Close();
                        Trace.TraceError($"ProductRecoverAndPersist -- GetConfigurationByProductId ex: {ex.ToString()}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "GetConfigurationByProductId");
                    }
                }
            }

            return result;
        }

        public DateTime GetMaxDateModConfigurationPromotionByProductId(int productId, short portalId)
        {
            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
            {
                using (MySqlCommand command = new MySqlCommand("get_max_date_configuration_automatic_promotion", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_idportal", portalId));
                    command.Parameters.Add(new MySqlParameter("_idproduct", productId));

                    try
                    {
                        connection.Open();
                        var maxDateScalar = command.ExecuteScalar();

                        var maxDate = DateTime.MinValue;

                        if (maxDateScalar != null)
                            DateTime.TryParse(maxDateScalar.ToString(), out maxDate);

                        return maxDate;
                    }
                    catch (Exception ex)
                    {
                        connection.Close();
                        Trace.TraceError($"ProductRecoverAndPersist -- GetMaxDateModConfigurationPromotionByProductId ex: {ex.ToString()}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "GetMaxDateModConfigurationPromotionByProductId");
                        return DateTime.MinValue;
                    }
                }
            }
        }

        public bool ExpiredAutomaticPromotions(short idPortal, int idCompany)
        {
            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, idPortal)))
            {
                using (MySqlCommand command = new MySqlCommand("ae_expired_automatic_promotion", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_idCompany", idCompany));
                    command.Parameters.Add(new MySqlParameter("_id_portal", idPortal));

                    try
                    {
                        connection.Open();

                        command.ExecuteNonQuery();
                        return true;
                    }
                    catch (Exception ex)
                    {
                        connection.Close();
                        Trace.TraceError($"ProductRecoverAndPersist -- UpdateRangeDatesPromotion ex: {ex.ToString()}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "ExpiredAutomaticPromotion");
                    }
                }
            }
            return false;
        }

        public string GetProductDescriptionByGroupAndSubGroup(short groupId, short subGroupId, short portalId)
        {
            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
            {
                using (MySqlCommand command = new MySqlCommand("Product_SubGroupsGetNameBySubGroup", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_subGroupId", subGroupId));
                    command.Parameters.Add(new MySqlParameter("_idportal", portalId));

                    try
                    {
                        connection.Open();
                        return command.ExecuteScalar().ToStringSafely();
                    }
                    catch (Exception ex)
                    {
                        connection.Close();
                        Trace.TraceError($"ProductRecoverAndPersist -- GetProductDescriptionByGroupAndSubGroup ex: {ex.ToString()}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "GetProductDescriptionByGroupAndSubGroup");
                    }
                }
            }
            return string.Empty;
        }

        public bool HasBasicService(short portalId)
        {
            bool result = false;

            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
            {
                using (MySqlCommand command = new MySqlCommand("ProductGetOfferAvailable", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    command.Parameters.Add(new MySqlParameter("_idportal", portalId));

                    try
                    {
                        connection.Open();
                        var response = command.ExecuteScalar();

                        if (response != null)
                        {
                            int.TryParse(response.ToStringSafely(), out var responseInt);
                            result = responseInt > 0;
                        }
                    }
                    catch (Exception ex)
                    {
                        connection.Close();
                        Trace.TraceError($"ProductRecoverAndPersist -- HasServiceBasic ex: {ex.ToString()}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "HasServiceBasic");
                    }
                }
            }
            return result;
        }

        public List<LimitationProductPurchaseEntity> GetLimitationProductsByIdProduct(int productId, short portalId)
        {
            var result = new List<LimitationProductPurchaseEntity>();

            using (var conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
            {
                using (var cmd = new MySqlCommand("ae_get_limitation_purchase_products", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    try
                    {
                        cmd.Parameters.Add(new MySqlParameter("_idportal", portalId));
                        cmd.Parameters.Add(new MySqlParameter("_idproduct", productId));
                        conn.Open();
                        using (var reader = cmd.ExecuteReader())
                        {
                            if (reader != null)
                            {
                                while (reader.Read())
                                {
                                    result.Add(
                                        new LimitationProductPurchaseEntity
                                        {
                                            IdActiveProduct = reader.GetAsInt("id_activeproduct"),
                                            IdForbiddenProducts = reader.GetAsString("idforbidden_products"),
                                            IdPortal = reader.GetAsShort("idportal")
                                        });
                                };
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        conn.Close();
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "GetLimitationProductsByIdProduct");
                    }
                }
            }

            return result;
        }

        public int GetProductGroupIdByProductId(short idPortal, int idProduct)
        {
            int result = 0;

            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, idPortal)))
            {
                using (MySqlCommand command = new MySqlCommand("get_group_by_pro_product_portal", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_idproduct", idProduct));
                    command.Parameters.Add(new MySqlParameter("_idportal", idPortal));

                    try
                    {
                        connection.Open();

                        var resultSql = command.ExecuteScalar();

                        if (resultSql != null)
                            int.TryParse(resultSql.ToString(), out result);
                    }
                    catch (Exception ex)
                    {
                        connection.Close();
                        Trace.TraceError($"ProductRecoverAndPersist -- GetProductGroupIdByProductId ex: {ex.ToString()}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "GetProductGroupIdByProductId", false, null, idPortal);
                    }
                }
            }

            return result;
        }

        public int GetProductGroupIdByProductIdByRepoProduct(short idPortal, int idProduct)
        {
            int result = 0;

            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.RepoProduct, idPortal)))
            {
                using (MySqlCommand command = new MySqlCommand("GetGroupByProProductPortal", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_idPortal", idPortal));
                    command.Parameters.Add(new MySqlParameter("_idProduct", idProduct));

                    try
                    {
                        connection.Open();

                        var resultSql = command.ExecuteScalar();

                        if (resultSql != null)
                            int.TryParse(resultSql.ToString(), out result);
                    }
                    catch (Exception ex)
                    {
                        connection.Close();
                        Trace.TraceError($"ProductRecoverAndPersist -- GetProductGroupIdByProductIdByRepoProduct ex: {ex.ToString()}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "GetProductGroupIdByProductIdByRepoProduct", false, null, idPortal);
                    }
                }
            }

            return result;
        }

        public List<ProductFeatureDescriptionEntity> GetProductFeaturesDescriptionsByIdProduct(int idProduct, short idPortal)
        {
            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, idPortal)))
            {
                using (MySqlCommand command = new MySqlCommand("ae_GetProductFeaturesDescriptions_v3", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_idportal", idPortal));
                    command.Parameters.Add(new MySqlParameter("_idproduct", idProduct));
                    try
                    {
                        var productFeaturesDescriptions = new List<ProductFeatureDescriptionEntity>();
                        connection.Open();

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var productFeature = new ProductFeatureDescriptionEntity()
                                {
                                    Literal = reader.GetAsString("literal"),
                                    Place = reader.GetAsShort("place"),
                                    PortalId = reader.GetAsShort("portal_id"),
                                    Priority = reader.GetAsShort("priority"),
                                    ProductId = reader.GetAsInt("idproduct"),
                                    Available = reader.GetAsShort("available"),
                                    LandingId = reader.GetAsShort("idlanding"),
                                    SubGroupId = reader.GetAsShort("idsubgroup")
                                };

                                productFeaturesDescriptions.Add(productFeature);
                            }
                        }

                        return productFeaturesDescriptions;
                    }
                    catch (Exception ex)
                    {
                        connection.Close();
                        Trace.TraceError($"ProductRecoverAndPersist -- GetProductFeaturesDescriptionsByIdProduct ex: {ex.ToString()}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "GetProductFeaturesDescriptionsByIdProduct");
                        return new List<ProductFeatureDescriptionEntity>();
                    }
                }
            }
        }

        public List<ProductEntity> GetAllProductsByPage(ProductSearchSpecifications productSpecifications)
        {
            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, productSpecifications.PortalId)))
            {
                using (MySqlCommand command = new MySqlCommand("ae_getAllProductsByPage", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_idportal", productSpecifications.PortalId));
                    command.Parameters.Add(new MySqlParameter("_idpage", productSpecifications.PageId));

                    var products = new List<ProductEntity>();
                    var idProduct = 0;
                    try
                    {
                        connection.Open();
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader != null)
                            {
                                while (reader.Read())
                                {
                                    var product = new ProductEntity()
                                    {
                                        Id = reader.GetAsInt("product_id"),
                                        TemporalityId = reader.GetAsInt("temporality_id"),
                                        ComercialName = reader.GetAsString("comercial_name"),
                                        LiteralShowPrice = reader.GetAsString("literal_show_price"),
                                        Discount = reader.GetAsInt("discount"),
                                        Price = reader.GetAsInt("price"),
                                        MonthPrice = reader.GetAsInt("month_price"),
                                        LiteralShowTotal = reader.GetAsString("literal_show_total"),
                                        LiteralShowUnit = reader.GetAsString("literal_show_unit"),
                                        GroupId = reader.GetAsInt("group_id"),
                                        Priority = reader.GetAsInt("priority"),
                                        PortalId = productSpecifications.PortalId,
                                        Recommended = reader.GetAsBoolean("recommended"),
                                        SubGroupId = reader.GetAsShort("subgroup_id"),
                                        Saving = reader.GetAsDecimal("saving"),
                                        InitialUnits = reader.GetAsInt("initial_units"),
                                        Default = reader.GetBoolean("is_default"),
                                        FirstAmbitId = reader.GetAsInt("ambit_id")
                                    };

                                    if (product.Id != idProduct)
                                    {
                                        products.Add(product);
                                        idProduct = product.Id;
                                    }
                                }
                            }
                        }
                        return products;
                    }
                    catch (Exception ex)
                    {
                        connection.Close();
                        Trace.TraceError($"ProductRecoverAndPersist -- GetAllProductsByPage ex: {ex.ToString()}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "GetAllProductsByPage");

                        return new List<ProductEntity>();
                    }
                }
            }
        }

        public ProductEntity GetCustomOfferProduct(short idPortal)
        {
            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, idPortal)))
            {
                using (MySqlCommand command = new MySqlCommand("ae_GetCustomOfferProduct", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_idportal", idPortal));

                    try
                    {
                        connection.Open();

                        using (var reader = command.ExecuteReader())
                        {
                            if (reader != null && reader.Read())
                            {
                                return new ProductEntity
                                {
                                    Id = reader.GetAsInt("product_id"),
                                    ComercialName = reader.GetAsString("comercial_name"),
                                    Price = reader.GetAsFloat("price"),
                                    GroupId = reader.GetAsInt("group_id"),
                                    TemporalityId = reader.GetAsInt("temporality_id"),
                                    ExpirationDays = reader.GetAsInt("expiration_days"),
                                    PortalId = reader.GetAsInt("portal_id"),
                                    SubGroupId = reader.GetAsShort("subgroup_id")
                                };
                            }
                        }
                        return new ProductEntity();
                    }
                    catch (Exception ex)
                    {
                        connection.Close();
                        Trace.TraceError($"ProductRecoverAndPersist -- GetCustomOfferProduct ex: {ex.ToString()}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "GetCustomOfferProduct");
                        return new ProductEntity();
                    }
                }
            }
        }

        public List<CustomOfferConditionsEntity> GetPromoOfferConditions(short idPortal)
        {
            List<CustomOfferConditionsEntity> customOfferConditionsEntity = new List<CustomOfferConditionsEntity>();

            try
            {
                using (MySqlConnection conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, idPortal)))
                {
                    using (var command = new MySqlCommand("ae_GetPromoOfferConditions", conn))
                    {
                        conn.Open();
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.Add(new MySqlParameter("_idportal", idPortal));

                        using (var dataReader = command.ExecuteReader())
                        {
                            while (dataReader.Read())
                            {
                                customOfferConditionsEntity.Add(new CustomOfferConditionsEntity
                                {
                                    Id = dataReader.GetInt32("Id"),
                                    IdLocalization = dataReader.GetInt32("IdLocalization"),
                                    IdPostalCode = dataReader.GetInt32("IdPostalCode"),
                                    IdCity = dataReader.GetInt32("IdCity"),
                                    IdCategory = dataReader.GetInt32("IdCategory"),
                                    IdSubCategory = dataReader.GetInt32("IdSubCategory"),
                                    Remote = dataReader.GetInt32("Remote"),
                                    Price = dataReader.GetDecimal("Price")
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"ProductRecoverAndPersist - GetPromoOfferConditions: {ex}");
                _exceptionPublisherService.PublishInCommon(ex, "ProductRecoverAndPersist", "GetPromoOfferConditions");
                throw;
            }

            return customOfferConditionsEntity;
        }

        public bool ProductIfExists(int idProduct, short idPortal)
        {
            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, idPortal)))
            {
                using (MySqlCommand command = new MySqlCommand("sp_exists_pro_product_portal_get_by_id", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_idPortal", idPortal));
                    command.Parameters.Add(new MySqlParameter("_idProduct", idProduct));

                    try
                    {
                        connection.Open();
                        var response = command.ExecuteScalar();

                        if (response != null)
                        {
                            int.TryParse(response.ToStringSafely(), out var responseInt);
                            return responseInt > 0;
                        }
                    }
                    catch (Exception ex)
                    {
                        connection.Close();
                        Trace.TraceError($"ProductRecoverAndPersist -- ProductIfExists ex: {ex.ToString()}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "ProductIfExists");
                    }
                }
            }
            return false;
        }

        public List<ProductEntity> GetCourtesyProducts(short idPortal, int idAmbit, int idSubGroup)
        {
            using (MySqlConnection connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, idPortal)))
            {
                using (MySqlCommand command = new MySqlCommand("sp_get_courtesy_products", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.Add(new MySqlParameter("_idportal", idPortal));
                    command.Parameters.Add(new MySqlParameter("_idambit", idAmbit));
                    command.Parameters.Add(new MySqlParameter("_idsubGroup", idSubGroup));

                    var products = new List<ProductEntity>();
                    try
                    {
                        connection.Open();
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader != null)
                            {
                                while (reader.Read())
                                {
                                    products.Add(new ProductEntity
                                    {
                                        Id = reader.GetAsInt("product_id"),
                                        ComercialName = reader.GetAsString("comercial_name"),
                                        TemporalityId = reader.GetAsInt("temporality_id"),
                                        GroupId = reader.GetAsInt("group_id"),
                                        ExpirationDays = reader.GetAsInt("expiration_days"),
                                        PortalId = reader.GetAsInt("portal_id"),
                                        SubGroupId = reader.GetAsShort("subgroup_id")
                                    });
                                }
                            }
                        }
                        return products;
                    }
                    catch (Exception ex)
                    {
                        connection.Close();
                        Trace.TraceError($"ProductRecoverAndPersist -- GetCourtesyProductsByGroupId ex: {ex}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "GetCourtesyProductsByGroupId");

                        return new List<ProductEntity>();
                    }
                }
            }
        }

        public async Task<List<ProProductPortalPersistentLayerDTO>> GetProProductPortalNoObsoleteMainInfoByPortalId(short portalId)
        {
            var result = new List<ProProductPortalPersistentLayerDTO>();

            using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
            {
                using (var command = new MySqlCommand("bwp_product_list_by_portal_id_hide_obsoletes_v2", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    try
                    {
                        command.Parameters.Add(new MySqlParameter("p_portal_id", portalId));

                        connection.Open();

                        using (var dtr = await command.ExecuteReaderAsync())
                        {
                            if (dtr != null)
                            {
                                while (dtr.Read())
                                {
                                    result.Add(
                                            new ProProductPortalPersistentLayerDTO
                                            {
                                                ProductId = dtr.GetAsInt("product_id"),
                                                ComercialName = dtr.GetAsString("comercial_name"),
                                                GroupId = dtr.GetAsShort("group_id"),
                                                PortalId = portalId,
                                                SubGroupId = dtr.GetAsShort("subgroup_id"),
                                                IsObsolete = dtr.GetAsShort("is_obsolete"),
                                                TemporalityId = dtr.GetAsInt("temporality_id"),
                                                ProductTypeId = dtr.GetAsInt("product_type"),
                                                IsActiveOCC = dtr.GetAsBoolean("is_active_occ")
                                            }
                                        );
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        connection.Close();
                        Trace.TraceError($"ProductRecoverAndPersist -- GetProProductPortalNoObsoleteMainInfoByPortalId ex: {ex}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "GetProProductPortalNoObsoleteMainInfoByPortalId");
                        return null;
                    }
                }
            }

            return result;
        }

        public async Task<List<ProProductPortalFeaturePersistentLayerDTO>> GetProProductPortalFeatureMainInfoByProductIdAndPortalIdAsync(int productId, short portalId)
        {
            var result = new List<ProProductPortalFeaturePersistentLayerDTO>();

            using (MySqlConnection conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
            {
                using (MySqlCommand command = new MySqlCommand("bwp_product_list_features_by_portal_id", conn))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    try
                    {
                        command.Parameters.Add(new MySqlParameter("p_product_id", productId));
                        command.Parameters.Add(new MySqlParameter("p_portal_id", portalId));

                        conn.Open();
                        using (var dtr = await command.ExecuteReaderAsync())
                        {
                            if (dtr != null)
                            {
                                while (dtr.Read())
                                {
                                    result.Add(
                                            new ProProductPortalFeaturePersistentLayerDTO
                                            {
                                                Id = dtr.GetAsInt("id"),
                                                ProductId = dtr.GetAsInt("product_id"),
                                                GroupId = dtr.GetAsShort("group_id"),
                                                FrequencyRenewDays = dtr.GetAsInt("frequency_renew_days"),
                                                InitialUnits = dtr.GetAsInt("initial_units"),
                                                IsUnlimited = dtr.GetAsShort("is_unlimited"),
                                                IsSimultaneous = dtr.GetAsShort("is_simultaneous"),
                                                AmbitId = dtr.GetAsShort("ambit_id"),
                                                IsRecurrent = !dtr.IsDBNull(dtr.GetOrdinal("is_recurrent")) ? dtr.GetAsShort("is_recurrent") : (short)0,
                                                PortalId = portalId
                                            }
                                        );
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        conn.Close();
                        Trace.TraceError($"ProductRecoverAndPersist -- GetProProductPortalFeatureMainInfoByProductIdAndPortalIdAsync ex: {ex}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "GetProProductPortalFeatureMainInfoByProductIdAndPortalIdAsync");
                        return null;
                    }
                }
            }

            return result;
        }

        public async Task<ProProductPortalPersistentLayerDTO> GetProductByIdProductAsync(int productId, short portalId)
        {
            var result = new ProProductPortalPersistentLayerDTO();

            using (MySqlConnection conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
            {
                using (MySqlCommand command = new MySqlCommand("bwp_product_get_by_portal_id_v2", conn))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    try
                    {
                        command.Parameters.Add(new MySqlParameter("p_portal_id", portalId));
                        command.Parameters.Add(new MySqlParameter("p_product_id", productId));

                        conn.Open();

                        using (var dtr = await command.ExecuteReaderAsync())
                        {
                            if (dtr != null
                                && dtr.Read())
                            {
                                result.ProductId = dtr.GetAsInt("product_id");
                                result.ComercialName = dtr.GetAsString("comercial_name");
                                result.GroupId = dtr.GetAsShort("group_id");
                                result.SubGroupId = dtr.GetAsShort("subgroup_id");
                                result.ServiceTypeId = dtr.GetAsShort("service_type_id");
                                result.ProductTypeId = dtr.GetAsShort("product_type");
                                result.LimitationMultiplier = dtr.GetAsShort("limitation_multiplier");
                                result.IsObsolete = dtr.GetAsShort("is_obsolete");
                                result.InternalName = dtr.GetAsString("internal_name");
                                result.PortalId = portalId;
                                result.IsActiveOCC = dtr.GetAsBoolean("is_active_occ");
                                result.ExpirationDays = dtr.GetAsInt("expiration_days");
                                result.TemporalityId = dtr.GetAsInt("temporality_id");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        conn.Close();
                        Trace.TraceError($"ProductRecoverAndPersist -- GetProductByIdProduct ex: {ex}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "GetProductByIdProduct");
                        return null;
                    }
                }
            }

            return result;
        }

        public async Task<List<ProProductPortalPersistentLayerDTO>> GetProProductPortalListByPortalIncludeObsolete(short portalId)
        {
            var result = new List<ProProductPortalPersistentLayerDTO>();

            using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
            {
                using (var command = new MySqlCommand("bwp_product_list_by_portal_id_v2", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    try
                    {
                        command.Parameters.Add(new MySqlParameter("p_portal_id", portalId));

                        connection.Open();

                        using (var dtr = await command.ExecuteReaderAsync())
                        {
                            if (dtr != null)
                            {
                                while (dtr.Read())
                                {
                                    result.Add(
                                            new ProProductPortalPersistentLayerDTO
                                            {
                                                ProductId = dtr.GetAsInt("product_id"),
                                                ComercialName = dtr.GetAsString("comercial_name"),
                                                GroupId = dtr.GetAsShort("group_id"),
                                                PortalId = portalId,
                                                SubGroupId = dtr.GetAsShort("subgroup_id"),
                                                IsObsolete = dtr.GetAsShort("is_obsolete"),
                                                TemporalityId = dtr.GetAsInt("temporality_id"),
                                                ProductTypeId = dtr.GetAsInt("product_type"),
                                                IsActiveOCC = dtr.GetAsBoolean("is_active_occ")
                                            }
                                        );
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        connection.Close();
                        Trace.TraceError($"ProductRecoverAndPersist -- GetProProductPortalListByPortalIncludeObsolete ex: {ex}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "GetProProductPortalListByPortalIncludeObsolete");
                        return null;
                    }
                }
            }

            return result;
        }

        public async Task<Dictionary<int, string>> GetCommercialProductNamesByProductIdsAndPortalIdAsync(short portalId, string productIds)
        {
            var result = new Dictionary<int, string>();

            using (var connection = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
            {
                using (var command = new MySqlCommand("ae_comercial_product_names_by_productids_and_portalid", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    try
                    {
                        command.Parameters.Add(new MySqlParameter("_portalid", portalId));
                        command.Parameters.Add(new MySqlParameter("_productids", productIds));

                        connection.Open();

                        using (var dtr = await command.ExecuteReaderAsync())
                        {
                            if (dtr != null)
                            {
                                while (dtr.Read())
                                {
                                    var productId = dtr.GetAsInt("product_id");

                                    if (!result.ContainsKey(productId))
                                    {
                                        result.Add(productId, dtr.GetAsString("comercial_name"));
                                    }
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        connection.Close();
                        Trace.TraceError($"ProductRecoverAndPersist -- GetComercialProductNamesByProductIdsAndPortalId ex: {ex}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "GetComercialProductNamesByProductIdsAndPortalId");
                        return null;
                    }
                }
            }

            return result;
        }

        public async Task<List<ProProductPortalPricePersistentLayerDTO>> GetProProductPricesByProductIdAndPortalIdAsync(short portalId, int productId)
        {
            var result = new List<ProProductPortalPricePersistentLayerDTO>();

            using (MySqlConnection conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
            {
                using (MySqlCommand comm = new MySqlCommand("bwp_product_price_list_v2", conn))
                {
                    comm.CommandType = CommandType.StoredProcedure;

                    try
                    {
                        comm.Parameters.Add(new MySqlParameter("_portal_id", portalId));
                        comm.Parameters.Add(new MySqlParameter("_product_id", productId));

                        conn.Open();

                        using (var dtr = await comm.ExecuteReaderAsync())
                        {
                            if ((dtr != null))
                            {
                                while (dtr.Read())
                                {
                                    result.Add(
                                    new ProProductPortalPricePersistentLayerDTO
                                    {
                                        TemporalityId = dtr.GetAsInt("temporality_id"),
                                        PortalId = dtr.GetAsShort("portal_id"),
                                        ProductId = dtr.GetAsInt("product_id"),
                                        ExpirationDays = dtr.GetAsInt("expiration_days"),
                                        Price = dtr.GetAsDouble("price"),
                                        Discount = dtr.GetAsInt("discount"),
                                        LiteralShowPrice = dtr.GetAsString("literal_show_price"),
                                        MonthPrice = dtr.GetAsDouble("month_price"),
                                        LiteralShowTotal = dtr.GetAsString("literal_show_total"),
                                        LiteralShowUnit = dtr.GetAsString("literal_show_unit"),
                                        StatusId = dtr.GetAsShort("status_id"),
                                        RenewalPrice = dtr.GetAsDouble("renewal_price"),
                                        Saving = dtr.GetAsInt("saving"),
                                        SelfRenewing = dtr.GetAsBoolean("self_renewing")
                                    });
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        conn.Close();
                        Trace.TraceError($"ProductRecoverAndPersist -- GetProProductPricesByProductIdAndPortalIdAsync ex: {ex}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "GetProProductPricesByProductIdAndPortalIdAsync");
                        return null;
                    }
                }
            }
            return result;
        }

        public async Task<List<ProProductPortalPagesPersistentLayerDTO>> GetProProductPagesByProductIdAndPortalIdAsync(short portalId, int productId)
        {
            var result = new List<ProProductPortalPagesPersistentLayerDTO>();

            using (MySqlConnection conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
            {
                using (MySqlCommand comm = new MySqlCommand("bwp_product_portal_pages_list_by_product_id", conn))
                {
                    comm.CommandType = CommandType.StoredProcedure;

                    try
                    {
                        comm.Parameters.Add(new MySqlParameter("_product_id", productId));
                        comm.Parameters.Add(new MySqlParameter("_portal_id", portalId));
                        conn.Open();

                        using (var dtr = await comm.ExecuteReaderAsync())
                        {
                            if (dtr != null)
                            {
                                while (dtr.Read())
                                {
                                    result.Add(
                                            new ProProductPortalPagesPersistentLayerDTO
                                            {
                                                PageId = dtr.GetAsInt("page_id"),
                                                PortalId = dtr.GetAsShort("portal_id"),
                                                ProductId = dtr.GetAsInt("product_id"),
                                                Priority = dtr.GetAsShort("priority"),
                                                TemporalityId = dtr.GetAsInt("temporality_id"),
                                                IdSegment = dtr.GetAsInt("id_segment"),
                                                Name = dtr.GetAsString("pagename"),
                                                AppId = dtr.GetAsShort("idapp")
                                            }
                                        );
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        conn.Close();
                        Trace.TraceError($"ProductRecoverAndPersist -- GetProProductPagesByProductIdAndPortalIdAsync ex: {ex}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "GetProProductPagesByProductIdAndPortalIdAsync");
                        return null;
                    }
                }
            }

            return result;
        }

        public async Task<bool> RemoveProProductPricesByProductIdAndPortalIdAsync(RemoveProProductPortalPricePersitentLayerDTO model)
        {
            using (MySqlConnection conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, model.PortalId)))
            {
                using (MySqlCommand comm = new MySqlCommand("bwp_product_price_del", conn))
                {
                    comm.CommandType = CommandType.StoredProcedure;
                    try
                    {
                        comm.Parameters.Add(new MySqlParameter("_portal_id", model.PortalId));
                        comm.Parameters.Add(new MySqlParameter("_product_id", model.ProductId));
                        comm.Parameters.Add(new MySqlParameter("_temporality_id", model.TemporalityId));

                        conn.Open();
                        await comm.ExecuteNonQueryAsync();
                    }

                    catch (Exception ex)
                    {
                        conn.Close();
                        Trace.TraceError($"ProductRecoverAndPersist -- RemoveProProductPricesByProductIdAndPortalIdAsync ex: {ex}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "RemoveProProductPricesByProductIdAndPortalIdAsync");
                        return false;
                    }

                }
            }
            return true;
        }

        public async Task<ProProductPortalPricePersistentLayerDTO> GetProProductPricesByTemporalityIdAsync(GetProProductPortalPricePersistentLayerDTO model)
        {
            var result = new ProProductPortalPricePersistentLayerDTO();

            using (MySqlConnection conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, model.PortalId)))
            {
                using (MySqlCommand comm = new MySqlCommand("bwp_product_price_get", conn))
                {
                    comm.CommandType = CommandType.StoredProcedure;

                    try
                    {
                        comm.Parameters.Add(new MySqlParameter("_portal_id", model.PortalId));
                        comm.Parameters.Add(new MySqlParameter("_product_id", model.ProductId));
                        comm.Parameters.Add(new MySqlParameter("_temporality_id", model.TemporalityId));

                        conn.Open();

                        using (var dtr = await comm.ExecuteReaderAsync())
                        {
                            if ((dtr != null))
                                if (dtr.Read())
                                {
                                    result.TemporalityId = dtr.GetAsInt("temporality_id");
                                    result.PortalId = dtr.GetAsShort("portal_id");
                                    result.ProductId = dtr.GetAsInt("product_id");
                                    result.ExpirationDays = dtr.GetAsInt("expiration_days");
                                    result.Price = dtr.GetAsDouble("price");
                                    result.Discount = dtr.GetAsInt("discount");
                                    result.LiteralShowPrice = dtr.GetAsString("literal_show_price");
                                    result.MonthPrice = dtr.GetAsDouble("month_price");
                                    result.LiteralShowTotal = dtr.GetAsString("literal_show_total");
                                    result.LiteralShowUnit = dtr.GetAsString("literal_show_unit");
                                    result.StatusId = dtr.GetAsShort("status_id");
                                    result.RenewalPrice = dtr.GetAsDouble("renewal_price");
                                    result.SelfRenewing = dtr.GetAsBoolean("self_renewing");
                                };
                        }
                    }
                    catch (Exception ex)
                    {
                        conn.Close();
                        Trace.TraceError($"ProductRecoverAndPersist -- GetProProductPricesByTemporalityIdAsync ex: {ex}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "GetProProductPricesByTemporalityIdAsync", false, null, model.PortalId);
                        return null;
                    }
                }
            }

            return result;
        }

        public async Task<bool> SaveProProductPriceAsync(ProProductPortalPricePersistentLayerDTO dto)
        {
            var result = true;

            using (MySqlConnection conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, dto.PortalId)))
            {
                using (MySqlCommand comm = new MySqlCommand("bwp_product_price_add_v2", conn))
                {
                    comm.CommandType = CommandType.StoredProcedure;

                    try
                    {
                        comm.Parameters.Add(new MySqlParameter("_portal_id", dto.PortalId));
                        comm.Parameters.Add(new MySqlParameter("_product_id", dto.ProductId));
                        comm.Parameters.Add(new MySqlParameter("_temporality_id", dto.TemporalityId));
                        comm.Parameters.Add(new MySqlParameter("_expiration_days", dto.ExpirationDays));
                        comm.Parameters.Add(new MySqlParameter("_price", dto.Price));
                        comm.Parameters.Add(new MySqlParameter("_literal_show_price", dto.LiteralShowPrice));
                        comm.Parameters.Add(new MySqlParameter("_month_price", dto.MonthPrice));
                        comm.Parameters.Add(new MySqlParameter("_literal_show_total", dto.LiteralShowTotal));
                        comm.Parameters.Add(new MySqlParameter("_literal_show_unit", dto.LiteralShowUnit));
                        comm.Parameters.Add(new MySqlParameter("_status_id", dto.StatusId));
                        comm.Parameters.Add(new MySqlParameter("_saving", dto.Saving));
                        comm.Parameters.Add(new MySqlParameter("_self_renewing", dto.SelfRenewing ? 1 : 0));

                        conn.Open();
                        await comm.ExecuteNonQueryAsync();
                    }

                    catch (Exception ex)
                    {
                        conn.Close();
                        Trace.TraceError($"ProductRecoverAndPersist -- SaveProProductPriceAsync ex: {ex}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "SaveProProductPriceAsync");
                        return false;
                    }

                }
            }
            return result;
        }

        public async Task<List<ProProductPortalPersistentLayerDTO>> GetAllProductsAvailablesByCRMAsync(short portalId)
        {
            var result = new List<ProProductPortalPersistentLayerDTO>();

            using (MySqlConnection conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
            {
                using (MySqlCommand l_command = new MySqlCommand("crm_get_available_products", conn))
                {
                    l_command.CommandType = CommandType.StoredProcedure;
                    try
                    {
                        l_command.Parameters.Add(new MySqlParameter("_idPortal", portalId));

                        conn.Open();

                        using (var l_dtr = await l_command.ExecuteReaderAsync())
                        {
                            if (l_dtr != null)
                            {
                                while (l_dtr.Read())
                                {
                                    result.Add(
                                            new ProProductPortalPersistentLayerDTO
                                            {
                                                ProductId = l_dtr.GetAsInt("product_id"),
                                                ComercialName = l_dtr.GetAsString("comercial_name"),
                                                GroupId = l_dtr.GetAsShort("group_id"),
                                                PortalId = portalId
                                            }
                                        );
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        conn.Close();
                        Trace.TraceError($"ProductRecoverAndPersist -- GetAllProductsAvailablesByCRMAsync ex: {ex}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "GetAllProductsAvailablesByCRMAsync");
                        return null;
                    }
                }
            }

            return result;
        }

        public async Task<List<int>> GetSuperiorProductIdsByProductIdAndPortalIdAsync(int productId, short portalId)
        {
            var result = new List<int>();

            using (MySqlConnection conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.MasterAux, portalId)))
            {
                using (MySqlCommand comm = new MySqlCommand("bwp_pro_product_superior_get", conn))
                {
                    comm.CommandType = CommandType.StoredProcedure;

                    try
                    {
                        comm.Parameters.Add(new MySqlParameter("_portal_id", portalId));
                        comm.Parameters.Add(new MySqlParameter("_product_id", productId));

                        conn.Open();
                        using (var dtr = await comm.ExecuteReaderAsync())
                        {
                            if (dtr != null)
                            {
                                while (dtr.Read())
                                {
                                    result.Add(dtr.GetAsInt("superior_id"));
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        conn.Close();
                        Trace.TraceError($"ProductRecoverAndPersist -- GetSuperiorProductIdsByProductIdAndPortalIdAsync ex: {ex}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "GetSuperiorProductIdsByProductIdAndPortalIdAsync");
                        return null;
                    }                    
                }
            }

            return result;
        }

        public async Task<bool> AddProProductSuperior(SaveProProductSuperiorPersistentLayerDTO dto)
        {
            using (MySqlConnection conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.MasterAux, dto.PortalId)))
            {
                using (MySqlCommand l_command = new MySqlCommand("bwp_pro_product_superior_add", conn))
                {
                    l_command.CommandType = CommandType.StoredProcedure;
                    try
                    {
                        l_command.Parameters.Add(new MySqlParameter("_idPortal", dto.PortalId));
                        l_command.Parameters.Add(new MySqlParameter("_idProduct", dto.ProductActiveId));
                        l_command.Parameters.Add(new MySqlParameter("_idSuperior", dto.ProductSuperiorId));

                        conn.Open();
                        await l_command.ExecuteNonQueryAsync();
                        return true;
                    }

                    catch (Exception ex)
                    {
                        conn.Close();
                        Trace.TraceError($"ProductRecoverAndPersist -- AddProProductSuperior ex: {ex}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "AddProProductSuperior");                        
                    }
                }
            }

            return false;
        }

        public async Task<bool> RemoveProProductSuperior(SaveProProductSuperiorPersistentLayerDTO dto)
        {
            using (MySqlConnection conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.MasterAux, dto.PortalId)))
            {
                using (MySqlCommand comm = new MySqlCommand("bwp_pro_product_superior_delete_by_product", conn))
                {
                    comm.CommandType = CommandType.StoredProcedure;
                    try
                    {
                        comm.Parameters.Add(new MySqlParameter("_idPortal", dto.PortalId));
                        comm.Parameters.Add(new MySqlParameter("_idProduct", dto.ProductActiveId));
                        comm.Parameters.Add(new MySqlParameter("_idSuperior", dto.ProductSuperiorId));

                        conn.Open();
                        await comm.ExecuteNonQueryAsync();
                        return true;
                    }

                    catch (Exception ex)
                    {
                        conn.Close();
                        Trace.TraceError($"ProductRecoverAndPersist -- RemoveProProductSuperior ex: {ex}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "RemoveProProductSuperior");
                    }
                }
            }

            return false;
        }

        public async Task<bool> AddFeature(ProProductPortalFeaturePersistentLayerDTO dto)
        {
            using (MySqlConnection conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, dto.PortalId)))
            {
                using (MySqlCommand comm = new MySqlCommand("bwp_product_portal_feature_add", conn))
                {
                    comm.CommandType = CommandType.StoredProcedure;
                    try
                    {
                        comm.Parameters.Add(new MySqlParameter("_ambit_id", dto.AmbitId));
                        comm.Parameters.Add(new MySqlParameter("_portal_id", dto.PortalId));
                        comm.Parameters.Add(new MySqlParameter("_product_id", dto.ProductId));
                        comm.Parameters.Add(new MySqlParameter("_units", dto.InitialUnits));
                        comm.Parameters.Add(new MySqlParameter("_unlimited", dto.IsUnlimited));
                        comm.Parameters.Add(new MySqlParameter("_simultaneous", dto.IsSimultaneous));
                        comm.Parameters.Add(new MySqlParameter("_is_recurrent", dto.IsRecurrent));
                        comm.Parameters.Add(new MySqlParameter("_frequency_renew_days", dto.FrequencyRenewDays));
                        comm.Parameters.Add(new MySqlParameter("_feature_type_id", dto.TypeId));

                        conn.Open();
                        await comm.ExecuteNonQueryAsync();
                        return true;
                    }

                    catch (Exception ex)
                    {
                        conn.Close();
                        Trace.TraceError($"ProductRecoverAndPersist -- AddFeature ex: {ex}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "AddFeature");
                    }
                }
            }

            return false;           
        }

        public async Task<bool> UpdateFeature(ProProductPortalFeaturePersistentLayerDTO dto)
        {
            using (MySqlConnection conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, dto.PortalId)))
            {
                using (MySqlCommand comm = new MySqlCommand("bwp_product_portal_feature_update", conn))
                {
                    comm.CommandType = CommandType.StoredProcedure;
                    try
                    {
                        comm.Parameters.Add(new MySqlParameter("_ambit_id", dto.AmbitId));
                        comm.Parameters.Add(new MySqlParameter("_portal_id", dto.PortalId));
                        comm.Parameters.Add(new MySqlParameter("_product_id", dto.ProductId));
                        comm.Parameters.Add(new MySqlParameter("_units", dto.InitialUnits));
                        comm.Parameters.Add(new MySqlParameter("_unlimited", dto.IsUnlimited));
                        comm.Parameters.Add(new MySqlParameter("_simultaneous", dto.IsSimultaneous));
                        comm.Parameters.Add(new MySqlParameter("_is_recurrent", dto.IsRecurrent));
                        comm.Parameters.Add(new MySqlParameter("_frequency_renew_days", dto.FrequencyRenewDays));

                        conn.Open();
                        await comm.ExecuteNonQueryAsync();
                        return true;
                    }

                    catch (Exception ex)
                    {
                        conn.Close();
                        Trace.TraceError($"ProductRecoverAndPersist -- UpdateFeature ex: {ex}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "UpdateFeature");
                    }
                }
            }

            return false;
        }

        public async Task<bool> DeleteFeature(ProProductPortalFeaturePersistentLayerDTO dto)
        {
            using (MySqlConnection conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, dto.PortalId)))
            {
                using (MySqlCommand comm = new MySqlCommand("bwp_product_portal_delete_feature", conn))
                {
                    comm.CommandType = CommandType.StoredProcedure;
                    try
                    {
                        comm.Parameters.Add(new MySqlParameter("_ambit_id", dto.AmbitId));
                        comm.Parameters.Add(new MySqlParameter("_portal_id", dto.PortalId));
                        comm.Parameters.Add(new MySqlParameter("_product_id", dto.ProductId));

                        conn.Open();
                        await comm.ExecuteNonQueryAsync();
                        return true;
                    }

                    catch (Exception ex)
                    {
                        conn.Close();
                        Trace.TraceError($"ProductRecoverAndPersist -- DeleteFeature ex: {ex}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "DeleteFeature");
                    }
                }
            }

            return false;
        }

        public async Task<bool> SetOfferMultiplier(SetOfferMultiplierPersistentLayerDTO dto)
        {
            using (MySqlConnection conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, dto.PortalId)))
            {
                using (MySqlCommand comm = new MySqlCommand("bwp_product_definition_set_limitation_multiplier", conn))
                {
                    comm.CommandType = CommandType.StoredProcedure;
                    try
                    {
                        comm.Parameters.Add(new MySqlParameter("_portal_id", dto.PortalId));
                        comm.Parameters.Add(new MySqlParameter("_product_id", dto.ProductId));
                        comm.Parameters.Add(new MySqlParameter("_limitation_multiplier", dto.LimitationMultiplier));

                        conn.Open();
                        await comm.ExecuteNonQueryAsync();
                        return true;
                    }

                    catch (Exception ex)
                    {
                        conn.Close();
                        Trace.TraceError($"ProductRecoverAndPersist -- SetOfferMultiplier ex: {ex}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "SetOfferMultiplier");
                    }
                }
            }

            return false;
        }

        public async Task<List<ProProductPortalPersistentLayerDTO>> GetDefinitionsProductsShorter(short portalId)
        {
            var result = new List<ProProductPortalPersistentLayerDTO>();

            using (MySqlConnection conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, portalId)))
            {
                using (MySqlCommand comm = new MySqlCommand("bwp_product_list_definition", conn))
                {
                    comm.CommandType = CommandType.StoredProcedure;
                    
                    try
                    {

                        conn.Open();

                        using (var l_dtr = await comm.ExecuteReaderAsync())
                        {
                            if (l_dtr != null)
                            {
                                while (l_dtr.Read())
                                {
                                    result.Add(
                                            new ProProductPortalPersistentLayerDTO
                                            {
                                                ProductId = l_dtr.GetAsInt("id"),
                                                ComercialName = l_dtr.GetAsString("comercial_name"),
                                                InternalName = l_dtr.GetAsString("internal_name"),
                                                GroupId = l_dtr.GetAsShort("group_id"),
                                                SubGroupId = l_dtr.GetAsShort("subgroup_id"),
                                                ServiceTypeId = l_dtr.GetAsShort("service_type_id"),
                                                ProductTypeId = l_dtr.GetAsInt("product_type")
                                            }
                                        );
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Trace.TraceError($"ProductRecoverAndPersist -- GetDefinitionsProductsShorter ex: {ex}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "GetDefinitionsProductsShorter");
                    }                    
                }
            }

            return result;
        }

        public async Task<int> AddProProductAsync(ProProductPortalPersistentLayerDTO dto)
        {
            var result = 0;

            using (MySqlConnection conn = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, dto.PortalId)))
            {
                using (MySqlCommand comm = new MySqlCommand("bwp_product_definition_add_v2", conn))
                {
                    comm.CommandType = CommandType.StoredProcedure;
                    try
                    {
                        comm.Parameters.Add(new MySqlParameter("_comercial_name", dto.ComercialName));
                        comm.Parameters.Add(new MySqlParameter("_service_type_id", dto.ServiceTypeId));
                        comm.Parameters.Add(new MySqlParameter("_group_id", dto.GroupId));
                        comm.Parameters.Add(new MySqlParameter("_subgroup_id", dto.SubGroupId));
                        comm.Parameters.Add(new MySqlParameter("_internal_name", dto.InternalName));
                        comm.Parameters.Add(new MySqlParameter("_id", dto.ProductId));

                        conn.Open();
                        int.TryParse((await comm.ExecuteScalarAsync()).ToString(), out result);

                    }

                    catch (Exception ex)
                    {
                        Trace.TraceError($"ProductRecoverAndPersist -- AddProProductAsync ex: {ex}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "AddProProductAsync");
                    }
                }
            }
            return result;
        }

        public async Task<bool> AddProProductPortalAsync(ProProductPortalPersistentLayerDTO dto)
        {
            using (MySqlConnection l_conex = new MySqlConnection(_connectionStringResolverService.GetConnectionString(ConnectionStringType.Master, dto.PortalId)))
            {
                using (MySqlCommand l_command = new MySqlCommand("bwp_product_portal_add_v3", l_conex))
                {
                    l_command.CommandType = CommandType.StoredProcedure;
                    try
                    {

                        l_command.Parameters.Add(new MySqlParameter("_product_id", dto.ProductId));
                        l_command.Parameters.Add(new MySqlParameter("_portal_id", dto.PortalId));
                        l_command.Parameters.Add(new MySqlParameter("_expiration_days", dto.ExpirationDays));
                        l_command.Parameters.Add(new MySqlParameter("_group_id", dto.GroupId));
                        l_command.Parameters.Add(new MySqlParameter("_subgroup_id", dto.SubGroupId));
                        l_command.Parameters.Add(new MySqlParameter("_comercial_name", dto.ComercialName));
                        l_command.Parameters.Add(new MySqlParameter("_service_type_id", dto.ServiceTypeId));
                        l_command.Parameters.Add(new MySqlParameter("_product_type_id", dto.ProductTypeId));
                        l_command.Parameters.Add(new MySqlParameter("_is_obsolete", dto.IsObsolete));
                        l_command.Parameters.Add(new MySqlParameter("_is_active_occ", dto.IsActiveOCC));

                        l_conex.Open();
                        await l_command.ExecuteNonQueryAsync();

                        return true;
                    }

                    catch (Exception ex)
                    {
                        Trace.TraceError($"ProductRecoverAndPersist -- AddProProductPortalAsync ex: {ex}");
                        _exceptionPublisherService.Publish(ex, "ProductRecoverAndPersist", "AddProProductPortalAsync");
                    }
                }
            }

            return false;
        }
    }
}
