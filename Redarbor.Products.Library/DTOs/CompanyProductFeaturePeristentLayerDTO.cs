namespace Redarbor.Products.Library.DTOs
{
    public class CompanyProductFeaturePeristentLayerDTO
    {
        public int Id { get; set; }
        public int ProductId { get; set; }
        public short GroupId { get; set; }
        public int FrequencyRenewDays { get; set; }
        public int InitialUnits { get; set; }
        public int AvailableUnits { get; set; }
        public short IsUnlimited { get; set; }
        public short IsSimultaneous { get; set; }
        public short AmbitId { get; set; }
        public short IsRecurrent { get; set; }
        public short PortalId { get; set; }
    }
}
