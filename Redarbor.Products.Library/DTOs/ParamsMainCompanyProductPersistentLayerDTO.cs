namespace Redarbor.Products.Library.DTOs
{
    public class ParamsMainCompanyProductPersistentLayerDTO
    {
        public int CompanyId { get; set; }
        public int TypeId { get; set; }
        public short PortalId { get; set; }

        public ParamsMainCompanyProductPersistentLayerDTO(int companyId, int typeId, short portalId)
        {
            CompanyId = companyId;
            TypeId = typeId;
            PortalId = portalId;
        }
    }
}
