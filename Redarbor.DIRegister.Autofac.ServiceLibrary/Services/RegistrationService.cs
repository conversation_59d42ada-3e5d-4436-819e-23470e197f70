using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Autofac;
using Autofac.Builder;
using Redarbor.DIRegister.AssemblyDiscovery.ServiceLibrary.Helpers;
using Redarbor.DIRegister.AssemblyDiscovery.ServiceLibrary.Services;
using System.Diagnostics;

namespace Redarbor.DIRegister.Autofac.ServiceLibrary.Services
{
    internal class RegistrationService
    {
        internal static void RegisterTypesInContainer(ContainerBuilder containerBuilder)
        {
            if (DataStoreSingleton.Instance.InspectedDependencies.IsAllResolvedSuccessfuly)
            {
                RegisterFoundApplicationServices(containerBuilder);

                return;
            }

            Trace.TraceError(string.Join("-", DataStoreSingleton.Instance.InspectedDependencies.FailedDependencies.Select(f => f.Description)));

            throw new Exception("Error registering dependencies. See log file for more information.");
        }

        private static void RegisterFoundApplicationServices(ContainerBuilder containerBuilder)
        {
            DataStoreSingleton.Instance.InspectedDependencies.MatchingDependenciesPairs
                .OrderBy(registrationPair => registrationPair.Value.FullName).ToList()
                .ForEach(registrationPair =>
                {
                    IRegistrationBuilder<object, ConcreteReflectionActivatorData, SingleRegistrationStyle> registration;
                    StringBuilder codeLine;

                    if (registrationPair.Key == registrationPair.Value)
                    {
                        registration = containerBuilder.RegisterType(registrationPair.Key).AsSelf();
                        codeLine = GetPartialAsSelfRegisterForTrace(registrationPair.Key);
                    }
                    else
                    {
                        registration = containerBuilder.RegisterType(registrationPair.Value).As(registrationPair.Key);
                        codeLine = GetPartialRegisterForTrace(registrationPair);
                    }

                    RegisterOnActivatedService.RegisterWithOnActivatedEvent(registration, registrationPair.Key,
                        registrationPair.Value, codeLine);

                    LifeTimeScopeService.SetInstanceLifetimeScope(registration, registrationPair, codeLine);

                    codeLine.Append(";");

                    TracerSingleton.Instance.CopyPasteForHandleRegistration.AppendLine(codeLine.ToString());
                });
        }

        private static StringBuilder GetPartialRegisterForTrace(KeyValuePair<Type, Type> interfaceImplementation)
        {
            var codeLine = new StringBuilder();
            var implementationName = interfaceImplementation.Value.ToGenericTypeString();
            var interfaceName = interfaceImplementation.Key.ToGenericTypeString();

            const string registerCodeTemplate = "builder.RegisterType<{0}>().As<{1}>()";
            codeLine.Append(String.Format(registerCodeTemplate, implementationName, interfaceName));

            return codeLine;
        }

        private static StringBuilder GetPartialAsSelfRegisterForTrace(Type implementation)
        {
            var codeLine = new StringBuilder();

            const string registerCodeTemplate = "builder.RegisterType<{0}>().AsSelf()";
            codeLine.Append(String.Format(registerCodeTemplate, implementation.ToGenericTypeString()));

            return codeLine;
        }
        
    }
}
