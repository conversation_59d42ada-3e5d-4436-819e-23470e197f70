using System;

namespace Redarbor.Products.Contracts.ServiceLibrary.DTO
{
    public class CompanyProductProProductPortalDTO
    {
        public int Id { get; set; }
        public string ComercialName { get; set; }
        public short GroupId { get; set; }
        public short SubgroupId { get; set; }
        public short ServiceTypeId { get; set; }
        public short ProductTypeId { get; set; }
        public short LimitationMultiplier { get; set; }
        public short PortalId { get; set; }
        public DateTime DateActivation { get; set; }
        public DateTime DateExpiration { get; set; }
        public int ProductId { get; set; }
        public bool SelfRenewing { get; set; }
    }
}
