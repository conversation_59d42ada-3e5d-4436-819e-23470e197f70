using System;

namespace Redarbor.Products.Contracts.ServiceLibrary.DTO
{
    public class IsAllowedToEditOfferDTO
    {
        public short IdOfferStatus { get; set; }
        public DateTime OfferExpirationTime { get; set; }
        public short CompanyProductGroupId { get; set; }
        public short CompanyProductOfferGroupId { get; set; }
        public bool CanEditPermitedByRol { get; set; } = false;
        public bool BlockOldMembership { get; set; }
    }
}
