using System;

namespace Redarbor.Products.Contracts.ServiceLibrary.DTO
{
    public class CompanyProductSmallerDTO
    {
        public int Id { get; set; }
        public int ProductId { get; set; }
        public int CompanyId { get; set; }
        public short PortalId { get; set; }
        public int ExpirationDays { get; set; }
        public short FeatureTypeId { get; set; }
        public short GroupId { get; set; }
        public short ServiceTypeId { get; set; }
        public DateTime DateAdd { get; set; }
        public DateTime DateMod { get; set; }
        public long UserId { get; set; }
        public double Price { get; set; }
        public short StatusId { get; set; }
        public short SourceId { get; set; }
        public short PaymentOriginId { get; set; }
        public DateTime DateActivation { get; set; } = DateTime.MinValue;
        public DateTime DateExpiration { get; set; } = DateTime.MinValue;
        public string ClientIp { get; set; } = string.Empty;
        public int ProductTypeId { get; set; }
    }
}
