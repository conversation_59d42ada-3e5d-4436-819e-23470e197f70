namespace Redarbor.Products.Contracts.ServiceLibrary.DTO
{
    public class ProProductPortalDTO
    {
        public int ProductId { get; set; }
        public string ComercialName { get; set; }
        public short GroupId { get; set; }
        public short SubGroupId { get; set; }
        public short IsObsolete { get; set; }
        public int TemporalityId { get; set; }
        public int ProductTypeId { get; set; }
        public short PortalId { get; set; }
        public short ServiceTypeId { get; set; }
        public short LimitationMultiplier { get; set; }
        public string InternalName { get; set; }
        public int ExpirationDays { get; set; }
        public bool IsActiveOCC { get; set; }
        public bool SelfRenewing { get; set; }
    }
}
