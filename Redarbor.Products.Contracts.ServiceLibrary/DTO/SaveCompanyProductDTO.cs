using System;

namespace Redarbor.Products.Contracts.ServiceLibrary.DTO
{
    public class SaveCompanyProductDTO
    {
        public int Id { get; set; }
        public short PortalId { get; set; }
        public short GroupId { get; set; }
        public short SubGroupId { get; set; }
        public short CurrencyId { get; set; }
        public string ComercialName { get; set; }
        public string InternalName { get; set; }
        public short ServiceTypeId { get; set; }
        public int ProductTypeId { get; set; }
        public int ExpirationDays { get; set; }
        public short LimitationMultiplier { get; set; }
        public bool HasForbidden { get; set; }
        public int TemporalityId { get; set; }
        public float Price { get; set; }
        public string LiteralShowTotal { get; set; }
        public string LiteralShowUnit { get; set; }
        public DateTime DateActivation { get; set; }
        public DateTime DateExpiration { get; set; }
        public short ProductStatus { get; set; }
        public short IsObsolete { get; set; }
        public bool IsActiveOCC { get; set; }
        public bool SelfRenewing { get; set; }
        public int CompanyId { get; set; }
    }
}
