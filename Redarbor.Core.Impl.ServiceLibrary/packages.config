<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="AWSSDK.Core" version="3.3.19" targetFramework="net452" />
  <package id="AWSSDK.S3" version="3.3.14" targetFramework="net452" />
  <package id="Microsoft.AspNetCore.Http.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Http.Features" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="8.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.DependencyInjection" version="8.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="8.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Primitives" version="2.2.0" targetFramework="net472" />
  <package id="Newtonsoft.Json" version="13.0.1" targetFramework="net472" />
  <package id="Redarbor.Cache" version="2.0.2" targetFramework="net472" />
  <package id="Redarbor.Dictionaries.Consumer" version="4.6.2" targetFramework="net472" />
  <package id="System.Buffers" version="4.4.0" targetFramework="net472" />
  <package id="System.Memory" version="4.5.1" targetFramework="net472" />
  <package id="System.Numerics.Vectors" version="4.4.0" targetFramework="net472" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="4.5.3" targetFramework="net472" />
  <package id="System.Text.Encodings.Web" version="4.5.0" targetFramework="net472" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net472" />
</packages>