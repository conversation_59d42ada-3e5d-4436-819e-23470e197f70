<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Autofac" version="3.5.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="2.1.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.ObjectPool" version="5.0.11" targetFramework="net472" />
  <package id="Newtonsoft.Json" version="7.0.1" targetFramework="net472" />
  <package id="RabbitMQ.Client" version="6.2.2" targetFramework="net472" />
  <package id="Redarbor.RabbitMQ" version="1.0.0.17" targetFramework="net472" />
  <package id="Redarbor.RabbitMQ.Abstractions" version="1.0.0.17" targetFramework="net472" />
  <package id="Redarbor.RabbitMQ.Extensions" version="1.0.0.17" targetFramework="net472" />
  <package id="Redarbor.RabbitMQ.Extensions.Autofac" version="1.0.0.16" targetFramework="net472" />
  <package id="Redarbor.RabbitMQ.Model" version="1.0.0.17" targetFramework="net472" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net472" />
  <package id="System.Configuration.ConfigurationManager" version="4.7.0" targetFramework="net472" />
  <package id="System.Memory" version="4.5.4" targetFramework="net472" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net472" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="4.5.3" targetFramework="net472" />
  <package id="System.Security.AccessControl" version="4.7.0" targetFramework="net472" />
  <package id="System.Security.Permissions" version="4.7.0" targetFramework="net472" />
  <package id="System.Security.Principal.Windows" version="4.7.0" targetFramework="net472" />
  <package id="System.Threading.Channels" version="4.7.1" targetFramework="net472" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net472" />
</packages>