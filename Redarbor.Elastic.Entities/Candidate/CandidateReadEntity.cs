using System;
using Redarbor.Master.Entities.Candidate;
using Redarbor.Master.Entities.Cv;
using Redarbor.Master.Entities.Users;

namespace Redarbor.Elastic.Entities.Candidate
{
    [Serializable]
    public class CandidateReadEntity : CandidateReadSearch
    {
        public UserEntity User { get; set; } = new UserEntity();
        public CandidateEntity Candidate { get; set; } = new CandidateEntity();
        public CvEntity Cv { get; set; } = new CvEntity();
    }
}
