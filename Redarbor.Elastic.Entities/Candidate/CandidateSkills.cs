using System;

namespace Redarbor.Elastic.Entities.Candidate
{
    [Serializable]
    public class CandidateSkills
    {
        public int IdSkillType { get; set; }

        public string Value { get; set; }

        public int IdSkill { get; set; }

        public int IdCv { get; set; }

        public CandidateSkills(int idSkillType, string value, int idSkill, int idCv)
        {
            IdSkillType = idSkillType;
            Value = value;
            IdSkill = idSkill;
            IdCv = idCv;
        }
    }
}