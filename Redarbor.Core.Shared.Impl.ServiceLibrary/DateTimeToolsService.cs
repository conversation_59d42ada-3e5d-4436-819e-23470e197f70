using System;
using Redarbor.Core.Shared.Contracts.ServiceLibrary;
using Redarbor.Core.Shared.Contracts.ServiceLibrary.DTO;
using Redarbor.Core.Shared.Contracts.ServiceLibrary.Enums;
using Redarbor.Extensions.Library.DI;

namespace Redarbor.Core.Shared.Impl.ServiceLibrary
{
    [RegisterStatefulService]
    public class DateTimeToolsService : IDateTimeToolsService
    {
        public DateTimeSpanDTO CompareDates(DateTime date1, DateTime date2)
        {
            if (date2 < date1)
            {
                DateTime sub = date1;
                date1 = date2;
                date2 = sub;
            }

            DateTime current = date1;
            int years = 0;
            int months = 0;
            int days = 0;

            var phase = PhaseEnum.Years;
            var span = new DateTimeSpanDTO();

            while (phase != PhaseEnum.Done)
            {
                switch (phase)
                {
                    case PhaseEnum.Years:
                        if (current.AddYears(years + 1) > date2)
                        {
                            phase = PhaseEnum.Months;
                            current = current.AddYears(years);
                        }
                        else
                        {
                            years++;
                        }

                        break;
                    case PhaseEnum.Months:
                        if (current.AddMonths(months + 1) > date2)
                        {
                            phase = PhaseEnum.Days;
                            current = current.AddMonths(months);
                        }
                        else
                        {
                            months++;
                        }

                        break;
                    case PhaseEnum.Days:
                        if (current.AddDays(days + 1) > date2)
                        {
                            current = current.AddDays(days);
                            TimeSpan timespan = date2 - current;
                            span = new DateTimeSpanDTO
                            {
                                   Years = years,
                                   Months = months,
                                   Days = days,
                                   Hours = timespan.Hours,
                                   Minutes = timespan.Minutes,
                                   Seconds = timespan.Seconds,
                                   Milliseconds = timespan.Milliseconds };
                            phase = PhaseEnum.Done;
                        }
                        else
                        {
                            days++;
                        }

                        break;
                }
            }

            return span;
        }

        public short GetAgeFromDateBirth(DateTime dateBirth)
        {
            if (dateBirth == DateTime.MinValue) return 0;
            var year = DateTime.Now.Year - dateBirth.Year;
            var month = DateTime.Now.Month - dateBirth.Month;
            if (month < 0) year--;
            else if (month == 0)
            {
                var day = DateTime.Now.Day - dateBirth.Day;
                if (day < 0) year--;
            }
            return Convert.ToInt16(year);
        }
    }
}
