using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Core.Shared.Contracts.ServiceLibrary;
using Redarbor.Core.Shared.Contracts.ServiceLibrary.DTO;
using Redarbor.Core.Shared.Contracts.ServiceLibrary.TypedException;
using Redarbor.Extensions.Library.DI;
using Redarbor.Extensions.Library.Helpers;
using static System.Net.WebRequestMethods;
using System.Linq;

namespace Redarbor.Core.Shared.Impl.ServiceLibrary
{
    [RegisterStatefulService]
    public class ApiService : IApiService
    {
        private readonly IExceptionPublisherService _exceptionPublisherService;

        public ApiService(IExceptionPublisherService exceptionPublisherService)
        {
            _exceptionPublisherService = exceptionPublisherService;
        }

        private const string PORTAL_HEADER_KEY = "ID_PORTAL";
        private const string BUSINESS_HEADER_KEY = "ID_BUSINESS";
        private const string BUSINESS_ID_HEADER_KEY = "businessId";

        public string Get(string url, short idBusiness = 0, short idPortal = 0)
        {
            try
            {
                var urlWithParams = new StringBuilder(url);
                using (var http = new HttpClient())
                {
                    http.DefaultRequestHeaders.Add(BUSINESS_HEADER_KEY, idBusiness.ToString());
                    http.DefaultRequestHeaders.Add(PORTAL_HEADER_KEY, idPortal.ToString());

                    var response = http.GetAsync(urlWithParams.ToString()).Result;

                    if (response.StatusCode == HttpStatusCode.NoContent)
                    {
                        return string.Empty;
                    }

                    if (response.IsSuccessStatusCode)
                    {
                        return response.Content.ReadAsStringAsync().Result;
                    }
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"ApiService, Exception on Get Method: {ex}");
            }

            return string.Empty;
        }


        public T Get<T>(string url, IDictionary<string, object> paramsToSend, short idBusiness = 0, short idPortal = 0) where T : new()
        {
            var urlWithParams = new StringBuilder(url);
            try
            {
                if (paramsToSend != null)
                {
                    foreach (var key in paramsToSend.Keys)
                    {
                        var firstChar = urlWithParams.ToString().Equals(url) ? '?' : '&';
                        urlWithParams.Append($"{firstChar}{key}={paramsToSend[key].ToString()}");
                    }
                }

                using (var http = new HttpClient())
                {
                    http.DefaultRequestHeaders.Add(BUSINESS_HEADER_KEY, idBusiness.ToString());
                    http.DefaultRequestHeaders.Add(PORTAL_HEADER_KEY, idPortal.ToString());

                    var request = http.GetAsync(urlWithParams.ToString());
                    switch (request.Result.StatusCode)
                    {
                        case HttpStatusCode.OK:
                            return JsonConvert.DeserializeObject<T>(request.Result.Content.ReadAsStringAsync().Result);
                        case HttpStatusCode.InternalServerError:
                            Trace.TraceError($"ApiService, InternalServerError on Get Method: {urlWithParams}");
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"ApiService, Exception on Get Method: {ex}");
                var extradata = new Dictionary<string, string>();
                extradata.Add("Url", url ?? string.Empty);
                extradata.Add("UrlWithParams", urlWithParams.ToString());

                _exceptionPublisherService.Publish(ex, "ApiService", "Get(paramsToSend,idBussiness,idPortal)", false, extradata);
            }

            return new T();
        }

        public T Post<T>(string url, object objectToSend = null, bool ignoreDefaultValue = true, short idBusiness = 0, short idPortal = 0) where T : new()
        {
            var result = Post(url, objectToSend, ignoreDefaultValue, idBusiness, idPortal);

            if (!string.IsNullOrEmpty(result))
                return JsonConvert.DeserializeObject<T>(result);

            return new T();
        }

        public T Post<T>(string url, short idPortal) where T : new()
        {
            var result = Post(url, null, true, 0, idPortal);

            if (!string.IsNullOrEmpty(result))
                return JsonConvert.DeserializeObject<T>(result);

            return new T();
        }
        [Obsolete("Usar PostAsync")]
        public string Post(string url, object objectToSend = null, bool ignoreDefaultValue = true, short idBusiness = 0, short idPortal = 0)
        {
            try
            {
                HttpContent stringContent = null;
                if (objectToSend != null)
                {
                    stringContent = new StringContent(JsonConvert.SerializeObject(objectToSend, Formatting.None,
                        new JsonSerializerSettings
                        {
                            NullValueHandling = NullValueHandling.Ignore,
                            DefaultValueHandling = ignoreDefaultValue ? DefaultValueHandling.Ignore : DefaultValueHandling.Include,
                            ContractResolver = new SerializeResolver()
                        }), Encoding.UTF8, "application/json");
                }

                using (var http = new HttpClient())
                {
                    http.DefaultRequestHeaders.Add(BUSINESS_HEADER_KEY, idBusiness.ToString());
                    http.DefaultRequestHeaders.Add(PORTAL_HEADER_KEY, idPortal.ToString());

                    var request = http.PostAsync(url, stringContent);
                    switch (request.Result.StatusCode)
                    {
                        case HttpStatusCode.OK:
                            var result = request.Result.Content.ReadAsStringAsync().Result;
                            if (!string.IsNullOrEmpty(result))
                                return request.Result.Content.ReadAsStringAsync().Result;
                            else
                                return string.Empty;
                        case HttpStatusCode.InternalServerError:
                            Trace.TraceError($"ApiService, InternalServerError on Post Method: {url} => {stringContent}");
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"ApiService, Exception on Post Method: {ex}");
                _exceptionPublisherService.Publish(ex, "ApiService", "Post(url,objectToSend,ignoreDefaultValue,idBusiness,idPortal)", false, null, idPortal);
            }

            return string.Empty;
        }
        public async Task<ContentPostDTO> PostAsync(string url, object objectToSend = null, bool ignoreDefaultValue = true, short idBusiness = 0, short idPortal = 0)
        {
            var result = new ContentPostDTO();
            try
            {
                HttpContent stringContent = null;
                if (objectToSend != null)
                {
                    stringContent = new StringContent(JsonConvert.SerializeObject(objectToSend, Formatting.None,
                        new JsonSerializerSettings
                        {
                            NullValueHandling = NullValueHandling.Ignore,
                            DefaultValueHandling = ignoreDefaultValue ? DefaultValueHandling.Ignore : DefaultValueHandling.Include,
                            ContractResolver = new SerializeResolver()
                        }), Encoding.UTF8, "application/json");
                }
                using (var http = new HttpClient())
                {
                    http.DefaultRequestHeaders.Add(BUSINESS_HEADER_KEY, idBusiness.ToString());
                    http.DefaultRequestHeaders.Add(PORTAL_HEADER_KEY, idPortal.ToString());
                    var request = await http.PostAsync(url, stringContent);
                    result.StatusCode = request.StatusCode;
                    result.Result = request.Content.ReadAsStringAsync().Result;
                    switch (request.StatusCode)
                    {
                        case HttpStatusCode.NotFound:
                            throw new NotFoundException(result.Result);
                        case HttpStatusCode.BadRequest:
                            throw new BadRequestException(result.Result);
                        case HttpStatusCode.OK:
                            break;
                        case HttpStatusCode.Created:
                            break;
                        default:
                            throw new Exception(result.Result);
                    }
                }
            }
            catch (NotFoundException ex)
            {
                throw new NotFoundException(ex.Message);
            }
            catch (BadRequestException ex)
            {
                throw new BadRequestException(ex.Message);
            }
            catch (Exception ex)
            {
                Trace.TraceError($"ApiService, Exception on PostAsync Method: {ex}");
                _exceptionPublisherService.Publish(ex, "ApiService", "PostAsync(url,objectToSend,ignoreDefaultValue,idBusiness,idPortal)", false, null, idPortal);
            }
            return result;
        }

        public T PostWithAuthorization<T>(string url, object objectToSend = null, string token = "", bool authorizationType = false, short idBusiness = 0, int timeoutInMinutes = 0) where T : new()
        {
            try
            {
                HttpContent stringContent = null;
                if (objectToSend != null)
                {
                    stringContent = new StringContent(JsonConvert.SerializeObject(objectToSend, Formatting.None,
                        new JsonSerializerSettings
                        {
                            NullValueHandling = NullValueHandling.Ignore,
                            DefaultValueHandling = DefaultValueHandling.Ignore
                        }),
                        UnicodeEncoding.UTF8,
                        "application/json");
                }

                List<KeyValuePair<string, string>> headers = null;
                AuthenticationHeaderValue authenticationHeaderValue = null;

                if (authorizationType)
                {
                    authenticationHeaderValue = new AuthenticationHeaderValue("Bearer", token);
                }
                
                HttpClient http = new HttpClient();

                if (timeoutInMinutes != 0)
                {
                    http.Timeout = TimeSpan.FromMinutes(timeoutInMinutes);
                }

                if (headers != null && headers.Any())
                {
                    foreach (KeyValuePair<string, string> header in headers)
                    {
                        http.DefaultRequestHeaders.Add(header.Key, header.Value);
                    }
                }

                if (authenticationHeaderValue != null)
                {
                    http.DefaultRequestHeaders.Authorization = authenticationHeaderValue;
                }
                http.DefaultRequestHeaders.Add(BUSINESS_ID_HEADER_KEY, idBusiness.ToString());
                
                Task<HttpResponseMessage> request = http.PostAsync(url, stringContent);

                if (request.Result.StatusCode == HttpStatusCode.OK || request.Result.StatusCode == HttpStatusCode.Created)
                {
                    return JsonConvert.DeserializeObject<T>(request.Result.Content.ReadAsStringAsync().Result);
                }
                else
                {
                    throw new Exception($"Error {request.Result.StatusCode} on POST method {url}");
                }
            }
            catch (Exception ex)
            {
                throw (ex);
            }
        }

      
    }
}
