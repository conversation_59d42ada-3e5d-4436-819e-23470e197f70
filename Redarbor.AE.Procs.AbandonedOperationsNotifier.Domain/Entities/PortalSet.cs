using Redarbor.AE.Procs.AbandonedOperationsNotifier.Domain.Enums;
using System.Collections.Generic;
using System.Linq;

namespace Redarbor.AE.Procs.AbandonedOperationsNotifier.Domain.Entities
{
    public class PortalSet
    {
        public DatabaseSet SetId { get; set; }

        public string MasterConnectionString { get; set; } = null!;

        public ICollection<Portal> Portals { get; set; } = null!;

        public Portal GetPortal(short portalId) => Portals.First(p => p.Id == portalId);
    }
}
