using Redarbor.Core.Cache.Contracts.ServiceLibrary;
using Redarbor.Extensions.Library.DI;
using Redarbor.Offer.Contracts.ServiceLibrary;
using Redarbor.Offer.Contracts.ServiceLibrary.DTO;
using Redarbor.Offer.Library.DomainServiceContracts;
using Redarbor.Repo.Offer.Library.DomainServiceContracts;
using Redarbor.Repo.Offer.Library.Entities;
using System;

namespace Redarbor.Offer.Impl.ServiceLibrary
{
    [RegisterStatefulService]
    public class FreemiumOfferContactsService : IFreemiumOfferContactsService
    {
        private readonly TimeSpan _timeInCache = new TimeSpan(0, 15, 0);
        private readonly ITempCache _tempCache;
        private readonly IFreemiumOfferContactsCounterRecoverAndPersist _freemiumOfferContactsCounterRecoverAndPersist;
        private readonly IMatchContactsFreemiumOfferRecoverAndPersist _matchContactsFreemiumOfferRecoverAndPersist;
        private readonly IFreemiumOfferMatchesContactsRecoverAndPersist _freemiumOfferMatchesContactsRecoverAndPersist;
        const int NO_RESULTS = -1;

        public FreemiumOfferContactsService(IFreemiumOfferContactsCounterRecoverAndPersist freemiumOfferContactsCounterRecoverAndPersist,
            IMatchContactsFreemiumOfferRecoverAndPersist matchContactsFreemiumOfferRecoverAndPersist,
            IFreemiumOfferMatchesContactsRecoverAndPersist freemiumOfferMatchesContactsRecoverAndPersist,
            ITempCache tempCache)
        {
            _freemiumOfferContactsCounterRecoverAndPersist = freemiumOfferContactsCounterRecoverAndPersist;
            _matchContactsFreemiumOfferRecoverAndPersist = matchContactsFreemiumOfferRecoverAndPersist;
            _freemiumOfferMatchesContactsRecoverAndPersist = freemiumOfferMatchesContactsRecoverAndPersist;
            _tempCache = tempCache;
        }

        public bool SetContactsByOffer(FreemiumOfferMatchesContactsDTO dto)
        {
            if (dto != null
                && dto.OfferId > 0
                && dto.PortalId > 0
                && dto.CompanyId > 0)
            {
                int counter = _freemiumOfferContactsCounterRecoverAndPersist.GetFreemiumOfferContactsCounter(dto.CompanyId);

                if (counter > NO_RESULTS)
                {
                    int chatContacts = GetContacts(counter + 1, dto.PortalId);

                    if (chatContacts > NO_RESULTS)
                    {
                        return _freemiumOfferMatchesContactsRecoverAndPersist.SetContactByOffer(new FreemiumOfferMatchesContacts() { OfferId = dto.OfferId, PortalId = dto.PortalId, ChatContacts = chatContacts })
                               && _freemiumOfferContactsCounterRecoverAndPersist.IncrementFreemiumOfferContactsCounter(dto.CompanyId);
                    }
                }
            }

            return false;
        }

        public int GetContactsByOffer(FreemiumOfferMatchesContactsDTO dto)
        {
            if (dto.OfferId > 0
                && dto.PortalId > 0)
            {
                var cacheKey = $"ContactsMatchesByFreemiumOffer_OfferId_{dto.OfferId}_portalId_{dto.PortalId}";

                var resultCache = _tempCache.Get<int?>(cacheKey, dto.PortalId);

                if (resultCache != null)
                {
                    return (int)resultCache;
                }

                int result = _freemiumOfferMatchesContactsRecoverAndPersist.GetContactsByOffer(new FreemiumOfferMatchesContacts() { PortalId = dto.PortalId, OfferId = dto.OfferId });

                _tempCache.Add(cacheKey, result, _timeInCache, dto.PortalId);

                return result;
            }

            return NO_RESULTS;
        }

        public int GetContactsByCompany(FreemiumOfferMatchesContactsDTO dto)
        {
            var result = NO_RESULTS;

            if (dto != null
                && dto.PortalId > 0
                && dto.CompanyId > 0)
            {
                int counter = _freemiumOfferContactsCounterRecoverAndPersist.GetFreemiumOfferContactsCounter(dto.CompanyId);

                if (counter > NO_RESULTS)
                {
                    result = GetContacts(counter + 1, dto.PortalId);
                }
            }

            return result;
        }

        private int GetContacts(int point, short portalId)
        {
            var result = NO_RESULTS;
            var matchContactsByNumberOffers = _matchContactsFreemiumOfferRecoverAndPersist.GetMatchContactsFreemiumOfferByPortal(portalId);

            if (matchContactsByNumberOffers != null)
            {
                int i = 0;
                bool isSelected = false;

                while (i < matchContactsByNumberOffers.Count && !isSelected)
                {
                    var item = matchContactsByNumberOffers[i];

                    if (point >= item.InitialNumber && point <= item.EndNumber)
                    {
                        result = item.ChatContacts;
                        isSelected = true;
                    }
                    else if (i == matchContactsByNumberOffers.Count - 1 && !isSelected)
                    {
                        result = item.ChatContacts;
                    }

                    i++;
                }
            }

            return result;
        }
    }
}
