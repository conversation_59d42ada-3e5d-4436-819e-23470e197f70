using Redarbor.Common.Entities.Configuration;
using Redarbor.Company.Contracts.ServiceLibrary;
using Redarbor.Core.Cache.Contracts.ServiceLibrary;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Core.Contracts.ServiceLibrary;
using Redarbor.Core.Counter.Contracts.ServiceLibrary;
using Redarbor.Core.Elastic.Library;
using Redarbor.Core.Elastic.Library.Enums;
using Redarbor.Core.Kpi.Contracts.ServiceLibrary;
using Redarbor.Core.Kpi.Contracts.ServiceLibrary.Enums;
using Redarbor.Core.Resolver.Contracts.ServiceLibrary;
using Redarbor.Core.Shared.Contracts.ServiceLibrary;
using Redarbor.Core.Stack.Contracts.ServiceLibrary;
using Redarbor.Core.Stack.Contracts.ServiceLibrary.DTO;
using Redarbor.Dictionaries.Consumer.Enums;
using Redarbor.Extensions.Library.DI;
using Redarbor.Extensions.Library.Extensions;
using Redarbor.Managers.Library;
using Redarbor.Master.Entities.Company;
using Redarbor.Master.Entities.Enums;
using Redarbor.Master.Entities.Offers;
using Redarbor.Master.Entities.Product;
using Redarbor.Master.Entities.Searchs;
using Redarbor.Master.Entities.Stack;
using Redarbor.Offer.API.Consumer.Models;
using Redarbor.Offer.Contracts.ServiceLibrary;
using Redarbor.Offer.Contracts.ServiceLibrary.DTO;
using Redarbor.Offer.Contracts.ServiceLibrary.Enums;
using Redarbor.Offer.Contracts.ServiceLibrary.OCCContractsServices;
using Redarbor.Offer.Elastic.Contracts.ServiceLibrary.DTO;
using Redarbor.Offer.Elastic.Contracts.ServiceLibrary.DTO.Filters;
using Redarbor.Offer.Impl.ServiceLibrary.Configuration;
using Redarbor.Offer.Library.DomainServiceContracts;
using Redarbor.Products.Contracts.ServiceLibrary;
using Redarbor.Repo.Offer.Library.DomainServiceContracts;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.Script.Serialization;
using CEnum = Redarbor.Common.Entities.Enums;


namespace Redarbor.Offer.Impl.ServiceLibrary
{
    [RegisterStatefulService]
    public class OfferService : IOfferService
    {
        private readonly TimeSpan _timeInCache = new TimeSpan(0, 15, 0);
        private readonly TimeSpan _timeInCacheCompaniesWithOffers = new TimeSpan(2, 0, 0);
        const int MAX_NUMBER_ITEMS_BY_BULK = 1000;

        private readonly IOfferRecoverAndPersist _offerRecoverAndPersist;
        private readonly IOfferIntegratorRecoverAndPersist _offerIntegratorRecoverAndPersist;
        private readonly IKillerQuestionsService _killerQuestionsService;
        private readonly IOfferLanguagesRecoverAndPersist _languagesRecoverAndPersist;
        private readonly IOfferDriveLicensesRecoverAndPersist _driveLicensesRecoverAndPersist;
        private readonly IMatchOfferService _matchOfferService;
        private readonly IOfferIntegratorService _offerIntegratorService;
        private readonly IEncryptionService _encryptionService;
        private readonly ITempCache _tempCache;
        private readonly IPortalConfigurationService _portalConfigurationService;
        private readonly IDictionaryService _dictionaryService;
        private readonly IStackModService _stackModService;
        private readonly IOfferModerationService _offerModerationService;
        private readonly ICompanyTrackingService _companyTrackingService;

        //TODO configuration must be on portalconfig entity
        private readonly IConfigurationService _configurationService;

        //TODO this services are CT specific, must be removed
        private readonly IOfferComputrabajoRecoverAndPersist _offerComputrabajoRecoverAndPersist;
        private readonly ICompanyProductService _companyProductService;

        private readonly IApiService _apiService;
        private readonly IOfferConfiguration _offerConfiguration;
        private readonly IExceptionPublisherService _exceptionPublisherService;
        private readonly IClientIpAddressResolverService _clientIpAddressResolverService;

        private readonly IKpiService _kpiService;
        private readonly ICompanyCountersService _companyCountersService;
        private readonly IOCCOfferService _occOfferService;


        private JavaScriptSerializer _serializer = new JavaScriptSerializer();

        public OfferService(IOfferRecoverAndPersist offerRecoverAndPersist,
            IOfferIntegratorRecoverAndPersist offerIntegratorRecoverAndPersist,
            IKillerQuestionsService killerQuestionsService,
            IOfferLanguagesRecoverAndPersist languagesRecoverAndPersist,
            IOfferDriveLicensesRecoverAndPersist driveLicensesRecoverAndPersist,
            IEncryptionService encryptionService,
            ITempCache tempCache,
            IPortalConfigurationService portalConfigurationService,
            IConfigurationService configurationService,
            IOfferIntegratorService offerIntegratorService,
            IOfferComputrabajoRecoverAndPersist offerComputrabajoRecoverAndPersist,
            ICompanyProductService companyProductService,
            IApiService apiService,
            IOfferConfiguration offerConfiguration,
            IExceptionPublisherService exceptionPublisherService,
            IClientIpAddressResolverService clientIpAddressResolverService,
            IMatchOfferService matchOfferService,
            IDictionaryService dictionaryService,
            IStackModService stackModService,
            IOfferModerationService offerModerationService,
            ICompanyTrackingService companyTrackingService,
            IKpiService kpiService,
            ICompanyCountersService companyCountersService,
            IOCCOfferService occOfferService)
        {
            _offerRecoverAndPersist = offerRecoverAndPersist;
            _offerIntegratorRecoverAndPersist = offerIntegratorRecoverAndPersist;
            _killerQuestionsService = killerQuestionsService;
            _languagesRecoverAndPersist = languagesRecoverAndPersist;
            _driveLicensesRecoverAndPersist = driveLicensesRecoverAndPersist;
            _encryptionService = encryptionService;
            _tempCache = tempCache;
            _portalConfigurationService = portalConfigurationService;
            _configurationService = configurationService;
            _offerIntegratorService = offerIntegratorService;
            _offerComputrabajoRecoverAndPersist = offerComputrabajoRecoverAndPersist;
            _companyProductService = companyProductService;
            _apiService = apiService;
            _offerConfiguration = offerConfiguration;
            _exceptionPublisherService = exceptionPublisherService;
            _clientIpAddressResolverService = clientIpAddressResolverService;
            _matchOfferService = matchOfferService;
            _dictionaryService = dictionaryService;
            _stackModService = stackModService;
            _offerModerationService = offerModerationService;
            _companyTrackingService = companyTrackingService;
            _kpiService = kpiService;
            _companyCountersService = companyCountersService;
            _occOfferService = occOfferService;
        }

        public List<OfferEntity> Search(CompanyProductEntity companyProduct, int parentCompanyId, int page, int rows,
                                        string order, int localizationId, int categoryId, int statusId, int gestorId, List<int> idsOffer, OfferIntegratorEnum offerIntegratorEnum, short portalId)
        {
            var showTabs = false;

            if (_configurationService.AppSettings["SHOW_OFFERMATCH_TABS"] != null && _configurationService.AppSettings["SHOW_OFFERMATCH_TABS"].ToLower() == "true")
            {
                var feature = companyProduct.Features.Find(feat => feat.AmbitId == (int)ProductAmbitEnum.CVFolders);
                if (feature != null)
                {
                    showTabs = true;
                }
            }
            var offers = _offerRecoverAndPersist.Search(companyProduct.IdCompany, portalId, parentCompanyId, page, rows, order, localizationId, categoryId, statusId, 0, showTabs, (short)offerIntegratorEnum);
            if (idsOffer.Count > 0)
                offers = offers.Where(offer => idsOffer.Contains(offer.idoffer)).ToList();

            SetOfferAdditionalFields(offers);

            return offers;
        }

        public List<OfferEntity> Search(int companyId, int parentCompanyId, int page, int rows,
                                        string order, List<int> localizationId, List<int> categoryId, List<int> statusId, List<int> gestorId, List<int> idsOffer,
                                        bool highlightedOffers, bool urgentOffers, bool kQOffers, bool hiddenNameOffers, bool countOnlyFolder1, OfferIntegratorEnum offerIntegratorEnum, short portalId)
        {
            var offers = _offerRecoverAndPersist.Search(companyId, portalId, parentCompanyId, page, rows, order, localizationId,
                categoryId, statusId, gestorId, idsOffer, highlightedOffers, urgentOffers, kQOffers, hiddenNameOffers, countOnlyFolder1, (short)offerIntegratorEnum);

            if (idsOffer.Any())
                offers = offers.Where(offer => idsOffer.Contains(offer.idoffer)).ToList();

            SetOfferAdditionalFields(offers);

            return offers;
        }

        private void SetOfferAdditionalFields(List<OfferEntity> offers)
        {
            if (offers.Any())
            {
                var offerIds = offers.Select(s => s.idoffer);

                if (offerIds.Any())
                {
                    var offerIntegrators = GetIntegratorsByBulkOffers(offerIds).ToList();

                    foreach (var offer in offers)
                    {
                        SetOfferLocation(offer);
                        SetOfferCreateOn(offer);
                        if (offerIntegrators.Exists(e => e.IdOffer == offer.idoffer))
                        {
                            offer.Integrations.Add(offerIntegrators.FirstOrDefault(f => f.IdOffer == offer.idoffer));
                            EncryptData(offer);
                        }
                    }
                }
            }
        }

        private void SetOfferCreateOn(OfferEntity offer)
        {
            offer.CreateOnText = offer.datelastup == DateTime.MinValue
                ? $"{offer.createdon.Day} de {_dictionaryService.GetDictionaryValue(DictionaryEnum.MONTHS, offer.createdon.Month.ToString(), offer.idportal)} de {offer.createdon.Year}"
                : $"{offer.datelastup.Day} de {_dictionaryService.GetDictionaryValue(DictionaryEnum.MONTHS, offer.datelastup.Month.ToString(), offer.idportal)} de {offer.datelastup.Year}";
        }

        private void SetOfferLocation(OfferEntity offer)
        {
            string value;
            if (offer.idcity > 0 && offer.idlocalization > 1)
            {
                value = _dictionaryService.GetDictionaryValue(DictionaryEnum.CITIES_BY_LOCALIZATION, offer.idlocalization, offer.idcity.ToString(), offer.idportal);
            }
            else if (offer.idlocalization > 1 && offer.idcountry > 0)
            {
                value = _dictionaryService.GetDictionaryValue(DictionaryEnum.LOCALIZATION_BY_COUNTRY, offer.idcountry, offer.idlocalization.ToString(), offer.idportal);
            }
            else if (offer.idcountry > 0)
            {
                value = _dictionaryService.GetDictionaryValue(DictionaryEnum.COUNTRY, offer.idcountry.ToString(), offer.idportal);
            }
            else
            {
                value = "Sin especificar";
            }

            if (string.IsNullOrEmpty(value))
            {
                var extraData = new Dictionary<string, string> {
                    { "Offer_Id", offer.idoffer.ToString() },
                    { "Offer_IdPortal", offer.idportal.ToString() },
                    { "Offer_IdCity", offer.idcity.ToString() },
                    { "Offer_IdLocalization", offer.idlocalization.ToString() },
                    { "Offer_IdCountry", offer.idcountry.ToString() }
                };
                _exceptionPublisherService.PublishWarning(new Exception($"ERROR SetOfferLocation"), "OfferService", "SetOfferLocation", extraData);
            }
            offer.Location = value;

        }



        private IEnumerable<OfferIntegratorEntity> GetIntegratorsByBulkOffers(IEnumerable<int> offerIds)
        {
            if (offerIds.Count() > MAX_NUMBER_ITEMS_BY_BULK)
            {
                var result = new List<OfferIntegratorEntity>();
                offerIds.SplitCollectionByNumber(MAX_NUMBER_ITEMS_BY_BULK).ForEach(x => result.AddRange(_offerIntegratorRecoverAndPersist.GetIntegratorsByBulkOffers(x)));
                return result;
            }

            return _offerIntegratorRecoverAndPersist.GetIntegratorsByBulkOffers(offerIds);
        }

        public List<OfferEntity> SearchByUser(int userId, int companyId, int page, int rows, string order,
                                              int localizationId, int categoryId, int statusId, List<int> idsOffer, OfferIntegratorEnum offerIntegratorEnum, short portalId)
        {
            var offers = _offerRecoverAndPersist.SearchByUser(userId, companyId, portalId, page, rows, order, localizationId, categoryId, statusId, (short)offerIntegratorEnum);

            if (idsOffer.Any())
                offers = offers.Where(offer => idsOffer.Contains(offer.idoffer)).ToList();

            SetOfferAdditionalFields(offers);

            return offers;
        }

        public OfferEntity GetByPk(string encryptedOfferId, short portalId, bool onlyHeader = true)
        {
            int.TryParse(_encryptionService.Decrypt(encryptedOfferId), out var offerId);
            return GetByPk(offerId, portalId,onlyHeader);
        }

        public OfferEntity GetByPk(int offerId, short portalId, bool onlyHeader = true)
        {
            var cacheKey = $"CACHEGETBYOFFER_{offerId}_ISONLYHEADER_{onlyHeader}";

            var result = _tempCache.Get<OfferEntity>(cacheKey, portalId);

            if (result != null)
            {
                var updateOn = _offerRecoverAndPersist.GetUpdatedOnByIdOffer(offerId, portalId);

                if (result.updatedon == updateOn
                    && updateOn != DateTime.MinValue)
                    return result;
            }

            var offer = _offerRecoverAndPersist.GetByPk(offerId, portalId,onlyHeader);

            if (offer == null || offer.idoffer <= 0) return new OfferEntity();

            SetOfferLocation(offer);
            SetOfferCreateOn(offer);
            EncryptData(offer);
            if (offer.idoffer > 0)
                _tempCache.Add(cacheKey, offer, _timeInCache, portalId);

            return offer;
        }

        public List<LanguageEntity> GetOfferLanguages(int offerId)
        {
            if (offerId != 0)
                return _languagesRecoverAndPersist.GetLanguagesByOffer(offerId);

            return new List<LanguageEntity>();
        }

        public List<DriveLicenseEntity> GetOfferDriveLicenses(int offerId)
        {
            if (offerId != 0)
                return _driveLicensesRecoverAndPersist.GetDriveLicensesByOffer(offerId);

            return new List<DriveLicenseEntity>();
        }

        public int GetTotalOffersByCompanyLastDays(int companyId, short portalId, OfferIntegratorEnum offerIntegratorEnum)
        {
            string cacheKey = $"TOTAL_OFFERS_BY_COMPANY_LAST_DAYS_{companyId}_{portalId}_{(short)offerIntegratorEnum}";

            var totalOffers = _tempCache.Get<int>(cacheKey);
            if (totalOffers > 0)
                return totalOffers;

            totalOffers = _offerRecoverAndPersist.GetTotalOffersByIntegratorCompanyLastDays(companyId, portalId, (short)offerIntegratorEnum);
            if (totalOffers > 0)
                _tempCache.Add(cacheKey, totalOffers, _timeInCache);

            return totalOffers;
        }

        public int GetTotalOffersByCompany(int companyId, int parenCompanyId, List<int> localizationId, List<int> categoryId, List<int> statusId, List<int> gestorId, List<int> idsOffer,
                                bool highlightedOffers, bool urgentOffers, bool kQOffers, bool hiddenNameOffers, OfferIntegratorEnum offerIntegratorEnum, short portalId, bool cleanCacheOffers)
        {
            var cacheKey = "TOTAL_OFFERS_BY_COMPANY_" + portalId + "_" + companyId + "_" + parenCompanyId + "_" + string.Join(",", localizationId.ToArray()) + "_" + string.Join(",", categoryId.ToArray()) + "_" + string.Join(",", statusId.ToArray()) + "_" + string.Join(",", gestorId.ToArray()) + "_" + string.Join(",", idsOffer.ToArray()) + "_" + highlightedOffers + "_" + urgentOffers + "_" + kQOffers + "_" + hiddenNameOffers + "_" + offerIntegratorEnum;

            int breackCache = !cleanCacheOffers ? _tempCache.Get<int>("BREAKCACHE_TOTAL_OFFERS_BY_COMPANY_" + companyId + "_" + portalId) : 1;

            if (breackCache == 1)
            {
                DeleteTotalOffersCache(companyId, portalId);
            }

            var totalOffers = _tempCache.Get<int>(cacheKey);
            if (totalOffers > 0 && breackCache == 0)
                return totalOffers;

            totalOffers = _offerRecoverAndPersist.GetTotalOffersByCompany(companyId, portalId, parenCompanyId, localizationId, categoryId, statusId, gestorId, idsOffer, highlightedOffers, urgentOffers, kQOffers, hiddenNameOffers, (short)offerIntegratorEnum);
            if (totalOffers > 0)
            {
                _tempCache.Add(cacheKey, totalOffers, _timeInCache);
                AddTotalOffersCache(cacheKey, companyId, portalId);
            }

            return totalOffers;
        }

        public void DeleteTotalOffersCache(int companyId, short portalId)
        {
            var totalOffersListCache = _tempCache.Get<List<string>>("totalOffersListCache_" + companyId + "_" + portalId) ?? new List<string>();

            foreach (var cacheName in totalOffersListCache)
            {
                _tempCache.Remove(cacheName);
            }

            _tempCache.Add("totalOffersListCache_" + companyId + "_" + portalId, new List<string>(), _timeInCache);
            _tempCache.Add("BREAKCACHE_TOTAL_OFFERS_BY_COMPANY_" + companyId + "_" + portalId, 0, _timeInCache);
        }
        public void AddTotalOffersCache(string cacheKeyName, int companyId, short portalId)
        {
            var totalOffersListCache = _tempCache.Get<List<string>>("totalOffersListCache_" + companyId + "_" + portalId) ?? new List<string>();
            totalOffersListCache.Add(cacheKeyName);
            _tempCache.Add("totalOffersListCache_" + companyId + "_" + portalId, totalOffersListCache, _timeInCache);
        }

        public int GetTotalOffersNotEqualCompanyProductId(int companyId, int integratorId, int companyProductId, short portalId)
        {
            var cacheKey = $@"TOTAL_OFFERS_NOT_EQUAL_COMPANY_PRODUCT_ID_{portalId}_{companyId}_{integratorId}_{companyProductId}";

            var totalOffers = _tempCache.Get<int>(cacheKey);
            if (totalOffers > 0)
                return totalOffers;

            totalOffers = _offerRecoverAndPersist.GetTotalOffersNotEqualCompanyProductId(companyId, portalId, integratorId, companyProductId);
            if (totalOffers > 0)
                _tempCache.Add(cacheKey, totalOffers, _timeInCache);

            return totalOffers;
        }

        public string ApiOffersEmpresaSuggest(OfferSuggestElasticFilter offerSuggestElasticFilter, bool isCT = false)
        {
            var suggestResult = GetApiOffersSuggest(offerSuggestElasticFilter, isCT);

            if (suggestResult.ElasticResultEnum == ElasticResultEnum.Ok)
            {
                return new JavaScriptSerializer().Serialize(suggestResult.Documents.DistinctBy(d => d.Title.ToLower().Trim()).Select(l => new
                {
                    Key = _encryptionService.Encrypt(l.IdOffer.ToString()),
                    Value = l.Title
                }));
            }

            return string.Empty;
        }

        public List<int> ApiOffersEmpresaGetIds(OfferSuggestElasticFilter offerSuggestElasticFilter, bool isCT = false)
        {
            var listOfferIds = new List<int>();

            try
            {
                return GetApiOffersSuggest(offerSuggestElasticFilter, isCT).Documents.Select(d => d.IdOffer).ToList();
            }
            catch (Exception ex)
            {
                Trace.TraceError($"OfferService - ApiOffersEmpresaGetIds ex:{ex}");
                _exceptionPublisherService.PublishInCommon(ex, "UserService", "ApiOffersEmpresaGetIds");
                return listOfferIds;
            }
        }

        public bool IsFromCompany(int companyId, int offerId, short idPortal)
        {
            var cacheKey = $"IS_FROM_COMPANY_ID_{companyId}_OFFER_ID_{offerId}";

            var hasPermission = _tempCache.Get<bool?>(cacheKey, idPortal);
            if (hasPermission != null)
                return Convert.ToBoolean(hasPermission);

            var resultIsMy = _offerRecoverAndPersist.IsFromCompany(companyId, offerId);

            if (resultIsMy > -1)
            {
                _tempCache.Add(cacheKey, resultIsMy > 0, TimeSpan.FromMinutes(1440), idPortal);
                hasPermission = resultIsMy > 0;
            }
            return Convert.ToBoolean(hasPermission);
        }

        public string SetStatus(List<string> offerIds, short newStatus, int companyId, int userId, short portalId)
        {
            var totalAffected = 0;

            Parallel.ForEach(offerIds, idOfferEncrypted =>
            {
                var offer = GetByPk(idOfferEncrypted, portalId, true);

                var integratorCT = offer?.Integrations?.FirstOrDefault(f => f.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo);

                if (integratorCT != null)
                {
                    if (_offerIntegratorService.SetStatus(new SetStatusIntegratorDTO(offer, integratorCT, newStatus, userId,
                        portalId, _dictionaryService.GetDictionaryValue(DictionaryEnum.CITY, offer.idcity.ToString(), offer.idportal))))
                    {
                        _offerRecoverAndPersist.SetStatus(offer.idoffer, offer.idcompany, newStatus, offer.idportal);
                        totalAffected = totalAffected + 1;
                        var action = newStatus == (short)OfferStatusEnum.Eliminada ? "delete" : "update";

                        Task.Factory.StartNew(() =>
                        {
                            _apiService.Post<int>(
                                $"{_offerConfiguration.OfferWebApiEndPoint}api/offerelastic/{action}/{offer.idoffer}", portalId);
                        });

                        _tempCache.Add("BREAKCACHE_TOTAL_OFFERS_BY_COMPANY_" + companyId + "_" + portalId, 1, _timeInCache);
                    }
                }
            });

            return totalAffected > 0 ? totalAffected.ToString() : GetInfoMessageError();
        }

        public bool SetStatus(OfferEntity offer, short newStatus, int userId, short portalId)
        {
            var offerIntegratorCT = offer?.Integrations?.FirstOrDefault(f => f.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo);

            if (offerIntegratorCT != null)
            {
                if (_offerIntegratorService.SetStatus(new SetStatusIntegratorDTO(offer, offerIntegratorCT, newStatus, userId,
                        portalId, _dictionaryService.GetDictionaryValue(DictionaryEnum.CITY, offer.idcity.ToString(), offer.idportal))))
                {
                    _offerRecoverAndPersist.SetStatus(offer.idoffer, offer.idcompany, newStatus, offer.idportal);

                    Task.Factory.StartNew(() =>
                    {
                        _apiService.Post<int>(
                            $"{_offerConfiguration.OfferWebApiEndPoint}api/offerelastic/update/{offer.idoffer}", portalId);
                    });

                    return true;
                }
            }

            return false;
        }

        public bool UpdateProductAndStatusOfferPostPublish(int idCompanyProduct, int idProduct, OfferEntity offer, short idOfferStatus, short groupId)
        {
            TraceExceptionIfNoFindProductUpdateProductAndStatusOfferPostPublish(idCompanyProduct, idProduct, offer, idOfferStatus, groupId);

            if (_offerIntegratorRecoverAndPersist.UpdateProductAndStatusOffer(offer.idoffer, offer.idcompany, offer.idportal, idOfferStatus, idCompanyProduct, idProduct, groupId, offer.ProductClass))
            {
                var offerIntegrator = offer.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo);

                _matchOfferService.AddMatchOffer(offer, offerIntegrator);

                Task.Factory.StartNew(() =>
                {
                    _apiService.Post<int>(
                        $"{_offerConfiguration.OfferWebApiEndPoint}api/offerelastic/update/{offer.idoffer}", offer.idportal);
                });
                return true;
            }
            return false;
        }

        public string RenewOffers(List<string> offerIds, int companyId, OfferIntegratorEnum offerIntegratorEnum, short portalId)
        {
            var totalAffected = 0;

            foreach (var encryptedOfferId in offerIds)
            {
                int.TryParse(_encryptionService.Decrypt(encryptedOfferId), out var offerId);
                if (offerId <= 0) continue;

                var offer = GetByPk(offerId, portalId);
                var offerIntegrator = offer.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)offerIntegratorEnum);
                if (offerIntegrator is null)
                    continue;

                if (_offerIntegratorService.RenewOffer(offer, offerIntegrator))
                {
                    _offerRecoverAndPersist.SetStatus(offer.idoffer, offer.idcompany, 2, offer.idportal);
                    totalAffected = totalAffected + 1;
                }

                Task.Factory.StartNew(() =>
                {
                    _apiService.Post<int>(
                        $"{_offerConfiguration.OfferWebApiEndPoint}api/offerelastic/update/{offer.idoffer}", offer.idportal);
                });
            }
            
            _companyCountersService.AddCounterCompany((int)CompanyKpiEnum.CT_AE_OFFERS_RENEW, portalId, companyId, totalAffected, false);

            return totalAffected > 0 ? totalAffected.ToString() : GetInfoMessageError();
        }

        public bool RenewCTOffer(OfferEntity offer)
        {

            if (offer?.idoffer <= 0)
                return false;

            var offerIntegrator = offer.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo);
            if (offerIntegrator is null)
                return false;

            if (!_offerIntegratorService.RenewOffer(offer, offerIntegrator))
                return false;

            _offerRecoverAndPersist.SetStatus(offer.idoffer, offer.idcompany, 2, offer.idportal);

            Task.Factory.StartNew(() =>
            {
                _apiService.Post<int>(
                    $"{_offerConfiguration.OfferWebApiEndPoint}api/offerelastic/update/{offer.idoffer}", offer.idportal);
            });


            return true;
        }

        public string UploadOffers(List<string> offerIds, int companyId, OfferIntegratorEnum offerIntegratorEnum, short portalId)
        {
            var totalAffected = 0;

            Parallel.ForEach(offerIds, lIdEncrypted =>
            {
                int.TryParse(_encryptionService.Decrypt(lIdEncrypted).ToString(), out var offerId);

                if (offerId <= 0) return;

                var offer = GetByPk(offerId, portalId, true);

                var offerIntegrator = offer.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)offerIntegratorEnum);
                if (offerIntegrator is null)
                    return;

                if (_offerIntegratorService.UploadOffer(offer, offerIntegrator, portalId))
                {
                    AddKpiOfferUpByGroupId(portalId, offer, offerIntegrator, true);

                    totalAffected = totalAffected + 1;

                    Task.Factory.StartNew(() =>
                    {
                        _apiService.Post<int>(
                            $"{_offerConfiguration.OfferWebApiEndPoint}api/offerelastic/update/{offer.idoffer}", portalId);
                    });
                }
            });
            _companyCountersService.AddCounterCompany((int)CompanyKpiEnum.CT_AE_OFFERS_UP, portalId, companyId, totalAffected, false);

            return totalAffected.ToString();
        }

        private void AddKpiOfferUpByGroupId(short portalId, OfferEntity offer, OfferIntegratorEntity offerIntegrator, bool isCT)
        {
            _kpiService.Add((short)KpiEnum.OFFER_UP, portalId);

            if (isCT)
            {
                var companyProduct = _companyProductService.GetByCompanyProductId(offerIntegrator.idcompanyproduct, offer.idportal, offer.idcompany);
                if (companyProduct.GroupId == (short)ProductGroupsEnum.Membership) _kpiService.Add((short)KpiEnum.OfferUP_Membership, portalId);
                else if (companyProduct.GroupId == (short)ProductGroupsEnum.Packs) _kpiService.Add((short)KpiEnum.OfferUP_Complete, portalId);
                else if (companyProduct.GroupId == (short)ProductGroupsEnum.Freemium) _kpiService.Add((short)KpiEnum.OfferUP_Basic, portalId);
            }
            else
            {
                _kpiService.Add((short)KpiEnum.OfferUP_ATS, portalId);
            }
        }

        public bool UploadOffer(OfferEntity offer)
        {
            var offerIntegratorCT = offer?.Integrations?.FirstOrDefault(f => f.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo);

            if (offerIntegratorCT != null)
            {
                if (_offerIntegratorService.UploadOffer(offer, offerIntegratorCT, offer.idportal))
                {
                    AddKpiOfferUpByGroupId(offer.idportal, offer, offerIntegratorCT, false);

                    Task.Factory.StartNew(() =>
                    {
                        _apiService.Post<int>(
                            $"{_offerConfiguration.OfferWebApiEndPoint}api/offerelastic/update/{offer.idoffer}", offer.idportal);
                    });

                    return true;
                }
            }

            return false;
        }

        public string ChangeOfferVisualization(OfferEntity offer, string contactName, long idUserSession, short isChecked, int ambitId)
        {
            if (idUserSession <= 0 || offer.Integrations.Count == 0 || offer.Integrations.First().IdOffer == 0)
                return "0";

            var offerIntegrator = offer.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo);
            if (offerIntegrator is null || !_offerIntegratorService.ChangeOfferVisualization(offer, isChecked, ambitId))
            {
                var companyProduct = _companyProductService.GetByIdCompany(offer.idcompany, offer.idportal, OfferIntegratorEnum.CompuTrabajo, idUserSession);
                return GetInfoMessageError(offer.createdby, contactName, idUserSession, companyProduct);
            }

            Task.Factory.StartNew(() =>
            {
                _apiService.Post<int>(
                    $"{_offerConfiguration.OfferWebApiEndPoint}api/offerelastic/update/{offer.idoffer}", offer.idportal);
            });

            if (isChecked == 1) _companyCountersService.AddCounterCompanyByAmbit(ambitId, offer.idportal, offer.idcompany, 1);

            return _serializer.Serialize(new { isValid = true });
        }

        public bool ConvertToPack(int idOffer, short subGroupId, long userId, OfferConvertTrace offerConvertTrace)
        {
            if (idOffer > 0)
            {
                var offer = GetByPk(idOffer, offerConvertTrace.IdPortal);

                if (IsFromCompany(offer.idcompany, idOffer, offer.idportal))
                {
                    if (offer.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo) == null)
                        return false;

                    return _offerIntegratorService.ConvertToPack(offer, userId, subGroupId, offerConvertTrace);
                }
            }

            return false;
        }

        public int GetOfferFrequencyRenewDays(OfferEntity offer, int idCompany, short portalId, OfferIntegratorEnum offerIntegratorEnum)
        {
            var offerIntegrator = offer.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)offerIntegratorEnum);
            if (offerIntegrator is null)
                return 0;

            return _offerIntegratorService.GetOfferFrequencyRenewDays(offer, offerIntegrator);
        }

        public string GetRenewOfferSpan(OfferEntity offer, PortalConfig portalConfig, CompanyProductEntity currentCompanyProduct, short pageId, OfferIntegratorEnum offerIntegratorEnum)
        {
            try
            {
                var offerIntegrator = offer.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)offerIntegratorEnum);
                if (offerIntegrator is null)
                    return string.Empty;

                if (!portalConfig.HasRenewOffers ||
                    currentCompanyProduct.GroupId != (int)ProductGroupsEnum.Membership ||
                    !currentCompanyProduct.Features.Exists(x => x.AmbitId == (short)ProductAmbitEnum.RenewOffer) ||
                    offerIntegrator.idstatus != (int)OfferStatusEnum.Vencida)
                {
                    if (!portalConfig.HasRenewOffers ||
                        currentCompanyProduct.GroupId != (int)ProductGroupsEnum.Membership ||
                        !currentCompanyProduct.Features.Exists(x => x.AmbitId == (short)ProductAmbitEnum.RenewOffer) ||
                        offerIntegrator.idstatus != (int)OfferStatusEnum.Vencida)
                    {
                        return $@"<a class=""icon i_renew_disabled icon_tooltip"" onclick=""ShowActionNotAllowed();""><span>" + RedarborLiteralsManager.Current.GetLiteral(pageId, "LIT_ICON_CANT_RENEW_OFFER", portalConfig) + "</span></a>";
                    }
                }

                if (offer.starttime.Date < DateTime.Now.Date && offer.starttime != DateTime.MinValue)
                    return $@"<a id='RenewStartTime' class=""icon i_renew_disabled icon_tooltip"" onclick=""ShowActionNotAllowedByMembership({(short)ProductAmbitEnum.RenewOffer});""><span>" + RedarborLiteralsManager.Current.GetLiteral(pageId, "LIT_RENEW_START_TIME", portalConfig) + "</span></a>";

                var returnError = new StringBuilder();
                var showContactButton = false;
                var isSimultaneousLimit = false;

                var offerFeature = currentCompanyProduct.Features.FirstOrDefault(i => i.AmbitId == (short)ProductAmbitEnum.Offer);
                if (offerFeature != null)
                {
                    var availableOffers = offerFeature.IsSimultaneous
                        ? offerFeature.InitialUnits
                        : offerFeature.AvailableUnits;
                    if (!offerFeature.IsUnlimited && availableOffers == 0)
                    {
                        if (offerFeature.IsSimultaneous)
                        {
                            if (offerIntegrator.idstatus != (int)OfferStatusEnum.Vencida)
                            {
                                return $@"<a class=""icon i_renew_disabled icon_tooltip"" onclick=""ShowActionNotAllowed();""><span>" + RedarborLiteralsManager.Current.GetLiteral(pageId, "LIT_ICON_CANT_RENEW_OFFER", portalConfig) + "</span></a>";
                            }
                            else
                            {
                                return $@"<a class=""icon i_renew icon_tooltip"" onclick=""javascript:renewOfferControl(5, '{RedarborLiteralsManager.Current.GetLiteral(pageId, "LIT_LIMITE_ALCANZADO_SIMULTANEAS", portalConfig)}');""><span>" + RedarborLiteralsManager.Current.GetLiteral(pageId, "LIT_RENEW_OFFER_POPUP_TITLE", portalConfig) + "</span></a>";

                            }
                        }
                        if (offerFeature.IsRecurrent)
                        {
                            returnError.Append(RedarborLiteralsManager.Current.GetLiteral(pageId, "LIT_LIMITE_ALCANZADO_MENSUALES", portalConfig));
                            showContactButton = true;
                        }
                    }
                }

                var highligthedOffersFeature = currentCompanyProduct.Features.FirstOrDefault(i => i.AmbitId == (short)ProductAmbitEnum.OfferHighlighted);
                if (highligthedOffersFeature != null)
                {
                    var availablehighligthedOffers = highligthedOffersFeature.IsSimultaneous
                        ? highligthedOffersFeature.InitialUnits
                        : highligthedOffersFeature.AvailableUnits;
                    if (!highligthedOffersFeature.IsUnlimited && availablehighligthedOffers <= 0 && offerIntegrator.ishighlighted == 1)
                    {
                        if (highligthedOffersFeature.IsSimultaneous)
                        {
                            returnError.Append(RedarborLiteralsManager.Current.GetLiteral(pageId, "P_DESTACADA", portalConfig));
                            isSimultaneousLimit = true;
                        }
                        if (highligthedOffersFeature.IsRecurrent)
                        {
                            if (returnError.Length > 0)
                                returnError.Append("<br/>");

                            returnError.Append(RedarborLiteralsManager.Current.GetLiteral(pageId, "LIT_POPUP_OFERT_DESTA", portalConfig));
                            showContactButton = true;
                        }
                    }
                }

                var urgentdOffersFeature = currentCompanyProduct.Features.FirstOrDefault(i => i.AmbitId == (short)ProductAmbitEnum.OfferUrgent);
                if (urgentdOffersFeature != null)
                {
                    var availableUrgentOffers = urgentdOffersFeature.IsSimultaneous
                        ? urgentdOffersFeature.InitialUnits
                        : urgentdOffersFeature.AvailableUnits;
                    if (!urgentdOffersFeature.IsUnlimited && availableUrgentOffers <= 0 && offerIntegrator.isurgent == 1)
                    {
                        if (urgentdOffersFeature.IsSimultaneous)
                        {
                            if (returnError.Length > 0)
                                returnError.Append(", ");

                            returnError.Append(RedarborLiteralsManager.Current.GetLiteral(pageId, "P_URGENTE", portalConfig));
                            isSimultaneousLimit = true;
                        }
                        if (urgentdOffersFeature.IsRecurrent)
                        {
                            if (returnError.Length > 0)
                                returnError.Append("<br/>");

                            returnError.Append(RedarborLiteralsManager.Current.GetLiteral(pageId, "LIT_POPUP_OFERT_URG", portalConfig));
                            showContactButton = true;
                        }
                    }
                }

                var hiddenNameOffersFeature = currentCompanyProduct.Features.FirstOrDefault(i => i.AmbitId == (short)ProductAmbitEnum.OfferHiddenName);
                if (hiddenNameOffersFeature != null)
                {
                    var availablehiddenNameOffers = hiddenNameOffersFeature.IsSimultaneous
                            ? hiddenNameOffersFeature.InitialUnits
                            : hiddenNameOffersFeature.AvailableUnits;
                    if (!hiddenNameOffersFeature.IsUnlimited && availablehiddenNameOffers <= 0 && offer.HiddenCompany == 1)
                    {
                        if (hiddenNameOffersFeature.IsSimultaneous)
                        {
                            if (returnError.Length > 0)
                                returnError.Append(", ");

                            returnError.Append(RedarborLiteralsManager.Current.GetLiteral(pageId, "P_PERFIL_CIEGO", portalConfig));
                            isSimultaneousLimit = true;
                        }
                        if (hiddenNameOffersFeature.IsRecurrent)
                        {
                            if (returnError.Length > 0)
                                returnError.Append("<br/>");

                            returnError.Append(RedarborLiteralsManager.Current.GetLiteral(pageId, "LIT_POPUP_OFERT_PERF_CIEG", portalConfig));
                            showContactButton = true;
                        }
                    }
                }

                if (returnError.Length > 0)
                {
                    if (offerIntegrator.idstatus != (int)OfferStatusEnum.Vencida)
                    {
                        return $@"<a class=""icon i_renew_disabled icon_tooltip"" onclick=""ShowActionNotAllowed();""><span>" + RedarborLiteralsManager.Current.GetLiteral(pageId, "LIT_ICON_CANT_RENEW_OFFER", portalConfig) + "</span></a>";
                    }
                    else
                    {
                        if (isSimultaneousLimit)
                        {
                            return $@"<a class=""icon i_renew icon_tooltip"" onclick=""javascript:document.getElementById('encryptedoffer').value = '{offer.idofferencrypted}'; renewOfferControl(1, '{RedarborLiteralsManager.Current.GetLiteral(pageId, "P_LIMITE_INFO_1", portalConfig)}: {returnError}. {RedarborLiteralsManager.Current.GetLiteral(pageId, "P_LIMITE_INFO_2", portalConfig)}');""><span>" + RedarborLiteralsManager.Current.GetLiteral(pageId, "LIT_RENEW_OFFER_POPUP_TITLE", portalConfig) + "</span></a>";
                        }
                        if (showContactButton)
                        {
                            return $@"<a class=""icon i_renew icon_tooltip"" onclick=""javascript:document.getElementById('encryptedoffer').value = '{offer.idofferencrypted}'; renewOfferControl(9, '{returnError}');""><span>" + RedarborLiteralsManager.Current.GetLiteral(pageId, "LIT_RENEW_OFFER_POPUP_TITLE", portalConfig) + "</span></a>";
                        }
                    }
                }

                if (offerFeature != null && (!offerFeature.IsUnlimited && !offerFeature.IsRecurrent && !offerFeature.IsSimultaneous))
                {
                    if (offerFeature.AvailableUnits > 0)
                    {
                        if (offerIntegrator.idstatus != (int)OfferStatusEnum.Vencida)
                        {
                            return $@"<a class=""icon i_renew_disabled icon_tooltip  js_popup"" onclick=""ShowActionNotAllowed();""><span>" + RedarborLiteralsManager.Current.GetLiteral(pageId, "LIT_ICON_CANT_RENEW_OFFER", portalConfig) + "</span></a>";
                        }
                        else
                        {
                            //Renovar oferta con confirmación (va a consumir un crédito)
                            return $@"<a class=""icon i_renew icon_tooltip"" onclick=""javascript:document.getElementById('encryptedoffer').value = '{offer.idofferencrypted}'; renewOfferControl(1, '{RedarborLiteralsManager.Current.GetLiteral(pageId, "LIT_RENEW_OFFER_CONSUME", portalConfig)}');""><span>" + RedarborLiteralsManager.Current.GetLiteral(pageId, "LIT_RENEW_OFFER_POPUP_TITLE", portalConfig) + "</span></a>";
                        }
                    }

                    if (offerIntegrator.idstatus != (int)OfferStatusEnum.Vencida)
                    {
                        return $@"<a class=""icon i_renew_disabled icon_tooltip"" onclick=""ShowActionNotAllowed();""><span>" + RedarborLiteralsManager.Current.GetLiteral(pageId, "LIT_ICON_CANT_RENEW_OFFER", portalConfig) + "</span></a>";
                    }
                    else
                    {
                        //no dispone de ofertas para poder renovar. (Botón comprar ofertas)
                        return $@"<a class=""icon i_renew_disabled icon_tooltip"" onclick=""ShowActionNotAllowedByMembership({(short)ProductAmbitEnum.RenewOffer});""><span>" + RedarborLiteralsManager.Current.GetLiteral(pageId, "LIT_CANT_RENEW_OFFER_EMPTY", portalConfig) + "</span></a>";
                    }
                    /*cuando se haya definido carrito de membresía, descomentar*/
                    //return $@"<span title=""{RedarborLiteralsManager.Current.GetLiteral(pageId, "LIT_RENEW_OFFER_POPUP_TITLE", portalConfig)}"" class=""icon renovar cp"" onclick=""javascript:document.getElementById('encryptedoffer').value = '{offer.idofferencrypted}'; renewOfferControl(4, '{RedarborLiteralsManager.Current.GetLiteral(pageId, "LIT_CANT_RENEW_OFFER_EMPTY", portalConfig)}');""></span>";
                }

                //renovar sin confirmación
                if (offerIntegrator.idstatus == (int)OfferStatusEnum.Vencida)
                {
                    return $@"<a class=""icon i_renew icon_tooltip"" onclick=""javascript:renewOffer('{offer.idofferencrypted}');""><span>" + RedarborLiteralsManager.Current.GetLiteral(pageId, "LIT_RENEW_OFFER_POPUP_TITLE", portalConfig) + "</span></a>";
                }
                else
                {
                    return $@"<a class=""icon i_renew_disabled icon_tooltip"" onclick=""ShowActionNotAllowed();""><span>" + RedarborLiteralsManager.Current.GetLiteral(pageId, "LIT_CANT_RENEW_OFFER_EMPTY", portalConfig) + "</span></a>";
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"OfferService - GetRenewOfferButton: ex:{ex}");
                _exceptionPublisherService.Publish(ex, "OfferService", "GetRenewOfferSpan");
                return string.Empty;
            }
        }

        public SaveOfferResult Save(OfferEntity entity, SaveOfferDTO saveOfferDTO, string defaultSelectStageName = "Seleccionados", bool IsSherlock = false, short subGroup = 0, bool isNewProduct2019 = false)
        {
            try
            {
                if (entity.Origin != OfferOriginEnum.API)
                {
                    entity.idcompany = Convert.ToInt32(_encryptionService.Decrypt(entity.idcompanyencrypted));
                    entity.KillerQuestions.Where(k => !string.IsNullOrEmpty(k.IdEncrypted)).ToList()
                    .ForEach(k => k.Id = Convert.ToInt32(_encryptionService.Decrypt(k.IdEncrypted)));

                    entity.skills.ForEach(s => s.createdby = saveOfferDTO.IdUser);
                    entity.drivelicenses.ForEach(d => d.createdby = saveOfferDTO.IdUser);
                    entity.Languages.ForEach(l => l.createdby = saveOfferDTO.IdUser);
                }

                entity.idstatus = (short)saveOfferDTO.IdStatus;

                return IsOfferInsert(entity)
                    ? Insert(entity, saveOfferDTO.IdUser, saveOfferDTO.UserName, saveOfferDTO.IsDuplicatedOffer, saveOfferDTO.AutoExcludeKillerQuestion, defaultSelectStageName, IsSherlock: IsSherlock, subGroup: subGroup, isNewProduct2019: isNewProduct2019)
                    : Update(entity, saveOfferDTO.IdUser, saveOfferDTO.UserName, saveOfferDTO.AutoExcludeKillerQuestion, saveOfferDTO.UpdateKillerQuestions, IsSherlock);
            }
            catch (Exception ex)
            {
                Trace.TraceError($"OfferService - Save: {ex}");
                _exceptionPublisherService.PublishInCommon(ex, "OfferService", "Save");
                return new SaveOfferResult()
                {
                    IdOffer = -1,
                    SaveOfferResultEnum = SaveOfferResultEnum.Ko
                };
            }
        }

        private bool IsOfferInsert(OfferEntity entity)
        {
            return (entity.Origin != OfferOriginEnum.API &&
                    string.IsNullOrEmpty(entity.idofferencrypted))
                    ||
                    (entity.Origin == OfferOriginEnum.API &&
                    entity.idoffer == 0);
        }

        public bool UpdateIdRolCreatedBy(List<int> idOffers, short idPortal, int idRole)
        {
            return _offerRecoverAndPersist.UpdateIdRolCreatedBy(idOffers, idPortal, idRole);
        }

        private SaveOfferResult Update(OfferEntity entity, int userId, string userName, bool autoExcludeKillerQuestions, bool updateKillerQuestions, bool IsSherlock = false)
        {
            if (entity.idportal == (short)CEnum.PortalEnum.OCCMexico)
            {
                return _occOfferService.Update(entity, userId, userName, autoExcludeKillerQuestions, updateKillerQuestions);
            }

            if (entity.Origin != OfferOriginEnum.API)
                entity.idoffer = Convert.ToInt32(_encryptionService.Decrypt(entity.idofferencrypted));

            var portalConfig = _portalConfigurationService.GetPortalConfiguration();
            var offerPreUpdate = GetByPk(entity.idoffer, entity.idportal, false);

            var saveOfferResult = new SaveOfferResult()
            {
                IdOffer = entity.idoffer,
                SaveOfferResultEnum = SaveOfferResultEnum.Ko
            };

            var offerIdCompanyProduct = offerPreUpdate.Integrations.FirstOrDefault(f => f.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).idcompanyproduct;
            var companyProductOffer = _companyProductService.GetByCompanyProductId(offerIdCompanyProduct, offerPreUpdate.idportal, offerPreUpdate.idcompany);

            if (!ControlUpdateOffer(entity, offerPreUpdate, offerIdCompanyProduct, companyProductOffer))
            {
                saveOfferResult.SaveOfferIntegratorResults.Add(new SaveOfferIntegratorResult()
                {
                    OfferIntegratorErrorEnum = OfferIntegratorSaveErrorEnum.ErrorControlInsertUpdate
                });

                return saveOfferResult;
            }

            PrepareOfferData(entity, offerPreUpdate);

            if (!_offerRecoverAndPersist.Update(entity, portalConfig, offerPreUpdate, updateKillerQuestions)) return saveOfferResult;

            if (entity.KillerQuestions.Exists(k => k.Type == (short)OfferKillerQuestionTypeEnum.Cerrada))
            {
                _killerQuestionsService.UpdateStatusAutoExcludedQuestions(entity.idoffer, autoExcludeKillerQuestions ? (short)1 : (short)0,entity.idportal);
            }

            _offerIntegratorService.Update(entity, offerPreUpdate, ref saveOfferResult, portalConfig, userId, userName, autoExcludeKillerQuestions, IsSherlock: IsSherlock);

            _offerRecoverAndPersist.UpdateUpdatedOn(entity.idoffer, entity.idportal);

            Task.Factory.StartNew(() =>
            {
                _apiService.Post<int>(
                    $"{_offerConfiguration.OfferWebApiEndPoint}api/offerelastic/update/{entity.idoffer}", portalConfig.PortalId);
            });

            // esa fecha de expiración se calcula en PrepareOfferData pero nunca llega a guardarse.
            // no me atrevo a tocarlo xq la fecha modificada se usa en el consumo de creditos...
            // al menos asi la api devuelve la fecha correcta
            if (entity.Origin == OfferOriginEnum.API)
            {
                entity.Integrations.First(x => x.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).expirationtime =
                    offerPreUpdate.Integrations.First(x => x.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).expirationtime;
            }

            return saveOfferResult;
        }

        public int InsertDraft(OfferEntity offer, PortalConfig portalConfig, bool autoExcludeKillerQuestion)
        {
            return _offerRecoverAndPersist.InsertDraft(offer, portalConfig, autoExcludeKillerQuestion);
        }

        public void InsertOfferInCompanyOfferElastic(OfferEntity offer)
        {
            Task.Factory.StartNew(() =>
            {
                _apiService.Post<int>(
                    $"{_offerConfiguration.OfferWebApiEndPoint}api/offerelastic/insert/{offer.idoffer}", offer.idportal);
            });
        }


        public bool PostPublishStep1OfferUpdate(OfferEntity offer, PortalConfig portalConfig, bool autoExcludeKillerQuestion, OfferEntity offerPreUpdate = null, bool updateKillerQuestions = true, OfferStatusRowEnum offerStatusDTO = OfferStatusRowEnum.Activo)
        {
            return _offerRecoverAndPersist.PostPublishStep1OfferUpdate(offer, portalConfig, autoExcludeKillerQuestion, offerPreUpdate, updateKillerQuestions, offerStatusDTO);
        }

        private bool ControlUpdateOffer(OfferEntity entity, OfferEntity offerPreUpdate, int offerIdCompanyProduct, CompanyProductEntity companyProductOffer)
        {
            if (!offerPreUpdate.Integrations.Exists(f => f.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo)) return false;
            if (!entity.Integrations.Exists(f => f.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo)) return false;

            offerIdCompanyProduct = offerPreUpdate.Integrations.FirstOrDefault(f => f.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo).idcompanyproduct;
            companyProductOffer = _companyProductService.GetByCompanyProductId(offerIdCompanyProduct, offerPreUpdate.idportal, offerPreUpdate.idcompany);

            if (companyProductOffer.GroupId == (short)ProductGroupsEnum.Packs)
            {
                var newOfferIntegrator = entity.Integrations.FirstOrDefault(f => f.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo);

                if (!companyProductOffer.Features.Exists(e => e.AmbitId == (short)ProductAmbitEnum.OfferHighlighted) && newOfferIntegrator.ishighlighted > 0) return false;
                if (!companyProductOffer.Features.Exists(e => e.AmbitId == (short)ProductAmbitEnum.OfferHiddenName) && newOfferIntegrator.Hidden > 0) return false;
                if (!companyProductOffer.Features.Exists(e => e.AmbitId == (short)ProductAmbitEnum.OfferUrgent) && newOfferIntegrator.isurgent > 0) return false;
            }
            else if (companyProductOffer.GroupId == (short)ProductGroupsEnum.Freemium)
            {
                var newOfferIntegrator = entity.Integrations.FirstOrDefault(f => f.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo);

                if (newOfferIntegrator.ishighlighted > 0
                        || entity.HiddenCompany > 0
                        || newOfferIntegrator.isurgent > 0)
                    return false;
            }

            if (offerPreUpdate.idcompany != entity.idcompany) return false;

            return true;
        }

        private SaveOfferResult Insert(OfferEntity entity, int userId, string userName, bool isDuplicatedOffer, bool autoExcludeKillerQuestions, string defaultSelectStageName = "Seleccionados", bool IsSherlock = false, short subGroup = 0, bool isNewProduct2019 = false)
        {
            if (entity.idportal == (short)CEnum.PortalEnum.OCCMexico)
            {
                return _occOfferService.Insert(entity, userId, userName, isDuplicatedOffer, autoExcludeKillerQuestions);
            }

            var portalConfig = _portalConfigurationService.GetPortalConfiguration();
            var companyProductForcePack = isNewProduct2019 && CheckMaxProductIsPack(entity.idcompany, entity.idportal)
                ? GetProductPackToConsume(entity, subGroup)
                : new CompanyProductEntity();

            var saveOfferResult = new SaveOfferResult()
            {
                IdOffer = 0,
                SaveOfferResultEnum = SaveOfferResultEnum.Ko
            };

            if (!ControlInsertOffer(entity, companyProductForcePack, isNewProduct2019))
            {
                saveOfferResult.SaveOfferIntegratorResults.Add(new SaveOfferIntegratorResult()
                {
                    OfferIntegratorErrorEnum = OfferIntegratorSaveErrorEnum.ErrorControlInsertUpdate
                });

                return saveOfferResult;
            }

            if (companyProductForcePack.Id > 0
                && entity.Integrations.Exists(e => e.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo))
            {
                var offerIntegrator = entity.Integrations.FirstOrDefault(f => f.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo);
                offerIntegrator.idproduct = companyProductForcePack.ProductId;
                offerIntegrator.idcompanyproduct = companyProductForcePack.Id;
                offerIntegrator.idproductencrypted = _encryptionService.Encrypt(companyProductForcePack.ProductId.ToString());
                offerIntegrator.idcompanyproductencrypted = _encryptionService.Encrypt(companyProductForcePack.Id.ToString());
            }

            PrepareOfferData(entity);
            if (string.IsNullOrEmpty(entity.client_ip_add))
                entity.client_ip_add = _clientIpAddressResolverService.GetIpAddress();

            var idNewOffer = _offerRecoverAndPersist.Insert(entity, portalConfig);
            saveOfferResult.IdOffer = idNewOffer;

            if (idNewOffer <= 0) return saveOfferResult;

            entity.idoffer = idNewOffer;
            entity.idofferencrypted = _encryptionService.Encrypt(entity.idoffer.ToString());
            entity.idofferCT = idNewOffer;

            if (entity.has_killer_questions == 1) _companyCountersService.AddCounterCompany((int)CompanyKpiEnum.CT_AE_OFFERS_KQ, entity.idportal, entity.idcompany, 1, false);

            if (autoExcludeKillerQuestions)
            {
                if (entity.KillerQuestions.Exists(k => k.Type == (short)OfferKillerQuestionTypeEnum.Cerrada))
                {
                    _killerQuestionsService.UpdateStatusAutoExcludedQuestions(entity.idoffer, 1,entity.idportal);
                }
            }

            _offerIntegratorService.Insert(entity, portalConfig, ref saveOfferResult, userId, userName, isDuplicatedOffer, autoExcludeKillerQuestions, defaultSelectStageName, IsSherlock: IsSherlock);
            _offerRecoverAndPersist.UpdateUpdatedOn(entity.idoffer, entity.idportal);

            InsertOfferInCompanyOfferElastic(entity);

            return saveOfferResult;
        }

        public void ExecuteActionsPostImportIntegrator(OfferEntity offer)
        {
            _offerRecoverAndPersist.UpdateUpdatedOn(offer.idoffer, offer.idportal);
            InsertOfferInCompanyOfferElastic(offer);
        }

        private bool ControlInsertOffer(OfferEntity entity, CompanyProductEntity companyProductForcePack, bool isNewPack)
        {
            if (CheckMaxProductIsPack(entity.idcompany, entity.idportal) && isNewPack)
            {
                if (!entity.Integrations.Exists(f => f.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo) || companyProductForcePack.Id == 0 || companyProductForcePack.ProductId == 0)
                    return false;

                var newOfferIntegrator = entity.Integrations.FirstOrDefault(f => f.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo);

                if (!companyProductForcePack.Features.Exists(e => e.AmbitId == (short)ProductAmbitEnum.OfferHighlighted) && newOfferIntegrator.ishighlighted > 0) return false;
                if (!companyProductForcePack.Features.Exists(e => e.AmbitId == (short)ProductAmbitEnum.OfferHiddenName) && entity.HiddenCompany > 0) return false;
                if (!companyProductForcePack.Features.Exists(e => e.AmbitId == (short)ProductAmbitEnum.OfferUrgent) && newOfferIntegrator.isurgent > 0) return false;
            }
            else
            {
                if (!entity.Integrations.Exists(f => f.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo))
                    return false;

                if (entity.idcompany <= 0 || entity.idportal <= 0)
                    return false;

                var newOfferIntegrator = entity.Integrations.FirstOrDefault(f => f.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo);
                var companyProduct = _companyProductService.GetByIdCompany(entity.idcompany, entity.idportal, OfferIntegratorEnum.CompuTrabajo);

                if (companyProduct.GroupId == (short)ProductGroupsEnum.Freemium
                    && (newOfferIntegrator.ishighlighted > 0
                        || entity.HiddenCompany > 0
                        || newOfferIntegrator.isurgent > 0))
                    return false;
            }

            return true;
        }

        private bool CheckMaxProductIsPack(int idcompany, short idportal)
        {
            return _companyProductService.GetByIdCompany(idcompany, idportal, OfferIntegratorEnum.CompuTrabajo).GroupId == (short)ProductGroupsEnum.Packs;
        }

        private CompanyProductEntity GetProductPackToConsume(OfferEntity entity, short subGroup)
        {
            if (!entity.Integrations.Exists(f => f.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo))
                return new CompanyProductEntity();

            var offerIntegrator = entity.Integrations.FirstOrDefault(f => f.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo);
            return _companyProductService.GetPackByCompanySubGroup(entity.idcompany, entity.idportal, subGroup, offerIntegrator.ishighlighted, offerIntegrator.isurgent, offerIntegrator.Hidden);
        }

        private void PrepareOfferData(OfferEntity entity, OfferEntity offerPreUpdate = null)
        {
            if (entity.Origin == OfferOriginEnum.API)
            {
                PrepareAPIOfferData(entity, offerPreUpdate);
                return;
            }

            entity.createdby = offerPreUpdate != null && offerPreUpdate.createdby > 0 ? offerPreUpdate.createdby : entity.createdby;
            entity.idofferCT = offerPreUpdate?.idofferCT ?? entity.idofferCT;

            if (offerPreUpdate != null)
            {
                entity.createdby = offerPreUpdate.createdby;
                entity.createdon = offerPreUpdate.createdon;

                entity.Integrations.ForEach(a => a.Id = offerPreUpdate.Integrations.FirstOrDefault(b => b.IdOffer == entity.idoffer && b.IdIntegrator == a.IdIntegrator)?.Id ?? 0);
                entity.Integrations.ForEach(a => a.publicationtime = offerPreUpdate.Integrations.FirstOrDefault(b => b.IdOffer == entity.idoffer && b.IdIntegrator == a.IdIntegrator)?.publicationtime ?? DateTime.MinValue);
                entity.Integrations.ForEach(a => a.urlrewrite = offerPreUpdate.Integrations.FirstOrDefault(b => b.IdOffer == entity.idoffer && b.IdIntegrator == a.IdIntegrator)?.urlrewrite ?? string.Empty);
                entity.client_ip_mod = _clientIpAddressResolverService.GetIpAddress();
            }

            entity.has_killer_questions = entity.KillerQuestions.Any() ? (short)1 : (short)0;

            entity.Integrations.ForEach(i =>
            {
                i.idcompanyproduct = _encryptionService.Decrypt(i.idcompanyproductencrypted).ToInt();
                i.idproduct = _encryptionService.Decrypt(i.idproductencrypted).ToInt();
                i.idstatus = (short)OfferStatusEnum.Pendiente;
            });
        }

        private void PrepareAPIOfferData(OfferEntity entity, OfferEntity offerPreUpdate = null)
        {
            entity.createdby = offerPreUpdate != null && offerPreUpdate.createdby > 0 ? offerPreUpdate.createdby : entity.createdby;
            entity.idofferCT = offerPreUpdate?.idofferCT ?? entity.idofferCT;
            entity.has_killer_questions = entity.KillerQuestions.Any() ? (short)1 : (short)0;

            if (offerPreUpdate != null)
            {
                entity.createdby = offerPreUpdate.createdby;
                entity.createdon = offerPreUpdate.createdon;
                entity.client_ip_add = offerPreUpdate.client_ip_add;
                entity.datelastup = offerPreUpdate.datelastup;

                entity.Integrations.ForEach(a =>
                {
                    var preOfferIntegrator = offerPreUpdate.Integrations.FirstOrDefault(b => b.IdOffer == entity.idoffer && b.IdIntegrator == a.IdIntegrator);
                    if (preOfferIntegrator != null)
                    {
                        a.Id = preOfferIntegrator.Id;
                        a.publicationtime = preOfferIntegrator.publicationtime;
                        a.urlrewrite = preOfferIntegrator.urlrewrite;
                        a.idcompanyproduct = preOfferIntegrator.idcompanyproduct;
                        a.idproduct = preOfferIntegrator.idproduct;
                    }
                });

                entity.KillerQuestions.Where(kq => kq.Id == 0).ForEach(kq =>
                {
                    var preKQ = offerPreUpdate.KillerQuestions.FirstOrDefault(pkq => pkq.Title == kq.Title);
                    if (preKQ != null)
                    {
                        if (preKQ.Type == (short)OfferKillerQuestionTypeEnum.Cerrada)
                            ValidateSameAnswers(kq, preKQ);
                        else
                            kq.Id = preKQ.Id;
                    }
                });
            }

            entity.Integrations.ForEach(i =>
            {
                i.idstatus = (short)OfferStatusEnum.Pendiente;
            });
        }

        private void ValidateSameAnswers(KillerQuestionEntity kq, KillerQuestionEntity preKQ)
        {
            var isDifferentAnswers = false;
            foreach (var item in preKQ.KillerData)
            {
                var answer = kq.KillerData.FirstOrDefault(kd => kd.Answer == item.Answer && kd.Score == item.Score);
                if (answer == null) { isDifferentAnswers = true; break; }
            }
            if (!isDifferentAnswers && preKQ.KillerData.Any() && preKQ.KillerData.Count() == kq.KillerData.Count()) kq.Id = preKQ.Id;
        }

        private void EncryptData(OfferEntity offer)
        {
            offer.idofferencrypted = _encryptionService.Encrypt(offer.idoffer.ToString());
            offer.idofferCTencrypted = _encryptionService.Encrypt(offer.idofferCT.ToString());
            offer.idcompanyencrypted = _encryptionService.Encrypt(offer.idcompany.ToString());
            offer.Integrations.ForEach(i => i.idcompanyproductencrypted = _encryptionService.Encrypt(i.idcompanyproduct.ToString()));
            offer.Integrations.ForEach(i => i.idproductencrypted = _encryptionService.Encrypt(i.idproduct.ToString()));
        }

        public void RePublishAllCompanyOffers(CompanyEntity company, OfferIntegratorEnum offerIntegratorEnum)
        {
            _offerIntegratorService.RePublishAllCompanyOffers(company, offerIntegratorEnum);
        }

        public List<OfferCandidateEntity> GetOfferCandidateHistory(int candidateId, int jobOfferId, int idCompany)
        {
            var keyCache = $"COMPANYOFFERSCANDIDATE_COMPANY_{idCompany}_CANDIDATEID_{candidateId}_OFFERID_{jobOfferId}";

            var cache = _tempCache.Get<List<OfferCandidateEntity>>(keyCache);

            if (cache != null)
            {
                var maxValue = _offerComputrabajoRecoverAndPersist.GetMaxMatchOffers(idCompany, candidateId, jobOfferId);

                if (cache.Any())
                {
                    var maxCacheMatchValue = cache.Max(c => c.MatchDate);
                    var maxCacheUpdateValue = cache.Max(c => c.CandidateProcessDate);

                    if (maxValue.Item1 == maxCacheMatchValue
                        && maxValue.Item2 == maxCacheUpdateValue
                        && maxValue.Item1 > DateTime.MinValue
                        && maxValue.Item2 > DateTime.MinValue
                        && maxCacheMatchValue > DateTime.MinValue)
                    {
                        return cache;
                    }
                }
            }

            var result = _offerComputrabajoRecoverAndPersist.GetResultOffers(idCompany, candidateId, jobOfferId);

            if (result.Any())
            {

                _tempCache.Add(keyCache, result, _timeInCache);
            }

            return result;
        }

        public void DeleteConversationByOffer(ConversationFilter conversationFilter)
        {
            _offerComputrabajoRecoverAndPersist.DeleteConversationByOffer(conversationFilter.IdOfferCT, conversationFilter.IdPortal);
        }

        public int GetIdOfferByIdOfferCT(int idOfferCt, int idPortal)
        {
            var key = $"GetIdOfferByIdOfferCT_{idOfferCt}_{idPortal}";

            var idoffer = _tempCache.Get<int>(key);

            if (idoffer != 0) return idoffer;

            idoffer = _offerRecoverAndPersist.GetByPkCt(idOfferCt, idPortal);
            _tempCache.Add(key, idoffer);

            return idoffer;
        }

        public int GetOfferCtIdByOfferId(int idOffer, int idPortal)
        {
            var key = $"GetOfferCtIdByOfferId{idOffer}_{idPortal}";

            var idofferCt = _tempCache.Get<int>(key);

            if (idofferCt != 0) return idofferCt;

            idofferCt = _offerRecoverAndPersist.GetIdOfferCtByIdOffer(idOffer, idPortal);
            _tempCache.Add(key, idofferCt);

            return idofferCt;
        }

        public string GetOfferTitle(int idOffer, short idPortal)
        {
            var key = $"GetOfferTitle{idOffer}_{idPortal}";

            var titleOffer = _tempCache.Get<string>(key, idPortal);

            if (!string.IsNullOrEmpty(titleOffer)) return titleOffer;

            titleOffer = _offerRecoverAndPersist.GetOfferTitle(idOffer, idPortal);
            _tempCache.Add(key, titleOffer, idPortal);

            return titleOffer;
        }

        public ElasticResult<OfferElasticDTO> GetApiOffers(OfferElasticFilter offerElasticFilter, PortalConfig portalConfig)
        {
            return _apiService.Post<ElasticResult<OfferElasticDTO>>($"{_offerConfiguration.OfferWebApiEndPoint}api/offerelastic/search"
                , offerElasticFilter);
        }

        public List<int> GetApiOffersIds(OfferElasticFilter offerElasticFilter, PortalConfig portalConfig)
        {
            var result = new List<int>();
            var offersElastic = GetApiOffers(offerElasticFilter, portalConfig);

            if (offersElastic.Documents != null
                && offersElastic.Documents.Any())
            {
                result = offersElastic.Documents.Select(s => s.IdOffer).ToList();
            }

            return result;
        }

        public ElasticSuggestResult<OfferSuggestElastic> GetApiOffersSuggest(OfferSuggestElasticFilter offerElasticFilter, bool isCT = false)
        {
            string url = isCT ? "api/offerelastic/suggestCT" : "api/offerelastic/suggest";
            return _apiService.Post<ElasticSuggestResult<OfferSuggestElastic>>($"{_offerConfiguration.OfferWebApiEndPoint}{url}",
                offerElasticFilter);
        }

        private string GetInfoMessageError()
        {
            return _serializer.Serialize(new { DefaultMessageError = true });
        }

        private string GetInfoMessageError(int userConsumCredits, string contactName, long idUser, CompanyProductEntity companyProduct)
        {
            if (companyProduct.Features.Exists(f => f.AmbitId == (short)ProductAmbitEnum.AssignCredits) && idUser != userConsumCredits)
                return _serializer.Serialize(new { isValid = false, ContactName = contactName, DefaultMessageError = false });
            else
            {
                return _serializer.Serialize(new { isValid = false, DefaultMessageError = true });
            }
        }

        public void RecalculateTotalApplies(List<OfferStageDeltaEntity> list)
        {
            if (list == null || !list.Any())
                return;

            _offerRecoverAndPersist.RecalculateTotalApplies(list);
        }

        public OfferEntity GetParentAndIntegratorOfferByPk(int idOfferCT, short portalId)
        {
            if (idOfferCT > 0 && portalId > 0)
                return _offerRecoverAndPersist.GetParentAndIntegratorOfferByPk(idOfferCT, portalId);

            return new OfferEntity();
        }

        public void UpdateOfferNewTotalsNotViewedByNumber(int idOfferCT, short idPortal, int value)
        {
            if (idOfferCT > 0 && idPortal > 0)
                _offerRecoverAndPersist.UpdateOfferNewTotalsNotViewedByNumber(idOfferCT, idPortal, value);
        }

        public bool UpdateOfferFeatures(OfferFeaturesEntity offerFeatures)
        {
            return _offerIntegratorRecoverAndPersist.UpdateOfferFeatures(offerFeatures);
        }

        public void OfferProductTrace(OfferEntity offer, OfferIntegratorEntity offerIntegrator, string action)
        {
            _offerIntegratorService.OfferProductTrace(offer, offerIntegrator, action);
        }

        public bool UpdatePostPublishOfferIntegrator(OfferEntity offer, bool isPayment, short offerExpirationDays, CompanyProductEntity productToConsume, PortalConfig portalConfig)
        {
            if (_offerIntegratorRecoverAndPersist.UpdatePostPublishOfferIntegrator(offer.idoffer, offer.idcompany, offer.idportal, (short)OfferStatusEnum.Pendiente, productToConsume.Id, productToConsume.ProductId, isPayment, offerExpirationDays, productToConsume.GroupId, offer.ProductClass))
            {
                var offerIntegrator = offer.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo);

                _matchOfferService.AddMatchOffer(offer, offerIntegrator);

                if (productToConsume.GroupId == (short)ProductGroupsEnum.Freemium)
                {
                    _companyTrackingService.UpdateLastFreemiumOffer(offer.idcompany, offer.idportal, DateTime.UtcNow);
                }

                _offerIntegratorService.SetPostModerateOffer(offer, portalConfig, productToConsume);

                Task.Factory.StartNew(() =>
                {
                    _apiService.Post<int>(
                        $"{_offerConfiguration.OfferWebApiEndPoint}api/offerelastic/update/{offer.idoffer}", offer.idportal);
                });

                return true;
            }
            return false;
        }

        public bool UpdateOfferPostPublish(OfferFeaturesEntity offerFeatures)
        {
            TraceExceptionIfNoFindProductUpdateOfferPostPublish(offerFeatures);

            if (_offerIntegratorRecoverAndPersist.UpdateOfferPostPublish(offerFeatures, _companyProductService.SetProductClassByIdGroup((short)offerFeatures.OfferGroupToConsume)))
            {
                _stackModService.Add(new StackModEntity
                {
                    objectid = offerFeatures.IdOffer,
                    typeid = (short)StackObjectTypeEnum.Offer,
                    idportal = offerFeatures.IdPortal
                });

                var publishOfferDTO = new PublishOfferDTO()
                {
                    IdOffer = offerFeatures.IdOffer,
                    IdPortal = offerFeatures.IdPortal,
                    IdCompany = offerFeatures.IdCompany,
                    OfferActionId = (short)OfferActionStackModEnum.Published,
                    ExperienceYears = offerFeatures.ExperienceYears,
                    UrlReWrite = offerFeatures.UrlRewrite
                };

                _offerModerationService.Publish(publishOfferDTO);

                if (offerFeatures.OfferGroupToConsume == (int)ProductGroupsEnum.Freemium)
                {
                    _companyTrackingService.UpdateLastFreemiumOffer(offerFeatures.IdCompany, offerFeatures.IdPortal, DateTime.UtcNow);
                }

                Task.Factory.StartNew(() =>
                {
                    _apiService.Post<int>(
                        $"{_offerConfiguration.OfferWebApiEndPoint}api/offerelastic/update/{offerFeatures.IdOffer}", offerFeatures.IdPortal);
                });

                return true;
            }
            return false;
        }

        public void UpdateOfferInCompanyOfferElastic(OfferEntity offer)
        {
            Task.Factory.StartNew(() =>
            {
                _apiService.Post<int>(
                    $"{_offerConfiguration.OfferWebApiEndPoint}api/offerelastic/update/{offer.idoffer}", offer.idportal);
            });
        }

        public List<OfferEntity> GetOfferToFindCvs(int companyId, short idPortal, int page, int rows, string order)
        {
            var offers = _offerRecoverAndPersist.GetOfferToFindCvs(companyId, idPortal, page, rows, order);
            SetOfferAdditionalFields(offers);
            return offers;
        }

        public List<OfferEntity> GetCompanyOffersByOfferIds(int companyId, short idPortal, ICollection<int> offerIds)
        {
            var key = $"CACHEGETCOMPANYOFFERSBYOFFERIDS_{companyId}";

            var cache = _tempCache.Get<List<OfferEntity>>(key, idPortal);
            if (cache != null && cache.Any())
                return cache;

            var result = _offerRecoverAndPersist.GetCompanyOffersByOfferIds(companyId, idPortal, offerIds);
            SetOfferAdditionalFields(result);

            if (result.Any())
                _tempCache.Add(key, result, _timeInCache, idPortal);

            return result;
        }

        public bool HasKillerQuestions(int offerId)
        {
            return offerId != 0 && _offerRecoverAndPersist.CheckIfHasKillers(offerId);
        }
        public List<SyncOfferEntity> GetCompanyOffersByCompanyIdToPandape(int idCompany, short idPortal)
        {
            var key = $"CACHEGETCOMPANYOFFERSBYCOMPANYIDTOPANDAPE_{idCompany}_{idPortal}";

            var cache = _tempCache.Get<List<SyncOfferEntity>>(key, idPortal);
            if (cache != null && cache.Any())
                return cache;

            var result = _offerRecoverAndPersist.GetCompanyOffersByCompanyId(idCompany, idPortal);

            if (result.Any())
                _tempCache.Add(key, result, _timeInCache, idPortal);

            return result;
        }


        public bool UpdatePostalCode(int idOffer, short idPortal, int idPostalCode)
        {
            var result = _offerRecoverAndPersist.UpdatePostalCode(idOffer, idPostalCode, idPortal);

            Task.Factory.StartNew(() =>
            {
                _apiService.Post<int>(
                    $"{_offerConfiguration.OfferWebApiEndPoint}api/offerelastic/update/{idOffer}", idPortal);
            });

            return result;
        }

        public int GetCompanyWithActiveOffers(PortalConfig portalConfig, OfferElasticSimpleFilter elasticFilter)
        {
            int companiesWithOffers = 0;
            string cacheName = $"ACTIVES_COMPANIES_COUNTER_PORTAL_ID_{portalConfig.PortalId}";

            try
            {
                var cache = _tempCache.Get<int?>(cacheName);
                if (cache != null && cache > 0)
                {
                    return (int)cache;
                }

                string offersCtApiUrl = _offerConfiguration.OfferWebApiEndPoint;
                if (!string.IsNullOrWhiteSpace(offersCtApiUrl))
                {
                    offersCtApiUrl = $"{offersCtApiUrl}{portalConfig.AEPortalConfig.ApiElasticGetCompaniesWithOffers}";
                    companiesWithOffers = _apiService.Post<int>(offersCtApiUrl, elasticFilter);
                }
                _tempCache.Add(cacheName, companiesWithOffers, _timeInCacheCompaniesWithOffers);

                return companiesWithOffers;
            }
            catch (Exception e)
            {
                _exceptionPublisherService.Publish(e, "OfferService", "GetCompanyWithActiveOffers");
            }

            return companiesWithOffers;
        }

        private void TraceExceptionIfNoFindProductUpdateProductAndStatusOfferPostPublish(int idCompanyProduct, int idProduct, OfferEntity offer, short idOfferStatus, short groupId)
        {
            if (idCompanyProduct <= 0 || idProduct <= 0 || groupId <= 0)
            {
                _exceptionPublisherService.Publish(new Exception($"PostPublish update offer con producto a 0, idCompanyProduct: {idCompanyProduct}," +
                    $" idProduct: {idProduct}, idoffer: {offer.idoffer}, idPortal: {offer.idportal}, idCompany:{offer.idcompany}," +
                    $" idOfferStatus: {idOfferStatus}, groupId: {groupId}, productClass: {offer.ProductClass}"), "OfferService", "TraceExceptionIfNoFindProductUpdateProductAndStatusOfferPostPublish");
            }
        }

        private void TraceExceptionIfNoFindProductUpdateOfferPostPublish(OfferFeaturesEntity offerFeatures)
        {
            if (offerFeatures != null && (offerFeatures.OfferGroupToConsume == 0 || offerFeatures.OfferIdProduct == 0 || offerFeatures.OfferIdCompanyProduct == 0))
            {
                _exceptionPublisherService.Publish(new Exception($"PostPublish update offer con producto a 0, idCompanyProduct: {offerFeatures.OfferIdCompanyProduct}," +
                    $" idProduct: {offerFeatures.OfferIdProduct}, idoffer: {offerFeatures.IdOffer}, idPortal: {offerFeatures.IdPortal}, idCompany:{offerFeatures.IdCompany}," +
                    $" idOfferStatus: {offerFeatures.IdOfferStatus}, groupId: {offerFeatures.OfferGroupToConsume}"), "OfferService", "TraceExceptionIfNoFindProductUpdateOfferPostPublish");
            }
        }

        public int ImportOffer(OfferEntity entity)
        {
            if (entity.idportal > 0
                && !string.IsNullOrEmpty(entity.cargo)
                && !string.IsNullOrEmpty(entity.title)
                && !string.IsNullOrEmpty(entity.description)
                && entity.idcountry > 0
                && entity.salary > 0
                && entity.idstudy > 0
                && entity.idcontracttype > 0
                && entity.vacancies > 0
                && entity.IdWorkPlaceType > 0)
            {
                if (string.IsNullOrEmpty(entity.client_ip_add))
                    entity.client_ip_add = _clientIpAddressResolverService.GetIpAddress();

                return _offerRecoverAndPersist.Insert(entity, _portalConfigurationService.GetPortalConfiguration(entity.idportal));
            }

            return 0;
        }

        public int GetIdOfferByIdExternalAndIdOrigin(long idExternal, short idPortal, int idOrigin)
        {
            if (idExternal > 0 && idOrigin > 0)
                return _offerRecoverAndPersist.GetIdOfferByIdExternalAndIdOrigin(idExternal, idPortal, idOrigin);

            return 0;
        }

        public bool UpdateIdExternalAndIdOrigin(int idOffer, short idPortal, long idExternal, int idOrigin)
        {
            if (idOffer > 0 && idExternal > 0 && idOrigin > 0)
                return _offerRecoverAndPersist.UpdateIdExternalAndIdOrigin(idOffer, idPortal, idExternal, idOrigin);

            return false;
        }

        public List<int> GetOfferIds(int idCompany, short idPortal, short idIntegrator)
        {
            if (idCompany > 0 && idPortal > 0 && idIntegrator > 0)
                return _offerRecoverAndPersist.GetOfferIds(idCompany, idPortal, idIntegrator);

            return new List<int>();

        }
    }
}