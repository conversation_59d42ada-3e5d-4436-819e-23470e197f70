using Redarbor.Core.Cache.Contracts.ServiceLibrary;
using Redarbor.Extensions.Library.DI;
using Redarbor.Master.Contracts.ServiceLibrary;
using Redarbor.Master.Entities.Enums;
using Redarbor.Master.Entities.Offers;
using Redarbor.Offer.Contracts.ServiceLibrary;
using Redarbor.Offer.Contracts.ServiceLibrary.DTO;
using Redarbor.User.Contracts.ServiceLibrary;
using System.Collections.Generic;
using System.Linq;

namespace Redarbor.Offer.Impl.ServiceLibrary
{
    [RegisterStatefulService]
    public class SecurityOfferService : ISecurityOfferService
    {
        private readonly ISecurityService _securityService;
        private readonly IOfferService _offerService;
        private readonly IUserService _userService;

        public SecurityOfferService(ISecurityService securityService, IOfferService offerService, IUserService userService)
        {
            _securityService = securityService;
            _offerService = offerService;
            _userService = userService;
        }     

        public bool IsMy(IsMyOfferDTO securityOfferDTO)
        {
            if (!securityOfferDTO.EnabledNewIsMy)
                return _offerService.IsFromCompany(securityOfferDTO.IdCompany, securityOfferDTO.IdOffer, securityOfferDTO.IdPortal);            

            if (CheckSecurityOfferDTO(securityOfferDTO))
            {                
                var offerEntity = _offerService.GetByPk(securityOfferDTO.IdOffer, securityOfferDTO.IdPortal, true);

                if (offerEntity.idcompany == securityOfferDTO.IdCompany)
                    return IsMyByRole(securityOfferDTO, offerEntity);
            }

            return false;
        }

        private bool IsMyByRole(IsMyOfferDTO securityOfferDTO, OfferEntity offerEntity)
        {
            return _securityService.IsActionPemitedByRole((short)securityOfferDTO.UserRole, securityOfferDTO.IdPortal, SecurityActionEnum.CompanyOffersManageByAllUsers)
               ? true : GetUsersToFilteredIsMyByRole(securityOfferDTO).Any(a => offerEntity.createdby == a);
        }
        
        private List<int> GetUsersToFilteredIsMyByRole(IsMyOfferDTO securityOfferDTO)
        {
            var usersToFilter = new List<int>();

            if (_securityService.IsActionPemitedByRole((short)securityOfferDTO.UserRole, securityOfferDTO.IdPortal, SecurityActionEnum.CompanyOffersManageLessThanYoursRol))
            {
                var usersEntity = _userService.GetManagersByDescendentRol(securityOfferDTO.IdCompany, securityOfferDTO.UserRole, securityOfferDTO.IdPortal);

                if (usersEntity != null && usersEntity.Any())
                    usersToFilter.AddRange(usersEntity.Select(s => (int)s.Id));
            }
            else
                usersToFilter.Add(securityOfferDTO.IdUser);

            return usersToFilter;
        }

        private static bool CheckSecurityOfferDTO(IsMyOfferDTO securityOfferDTO)
        {
            return securityOfferDTO.IdUser > 0
                            && securityOfferDTO.IdPortal > 0
                            && securityOfferDTO.IdCompany > 0
                            && securityOfferDTO.UserRole > 0
                            && securityOfferDTO.IdOffer > 0;
        }
    }
}
