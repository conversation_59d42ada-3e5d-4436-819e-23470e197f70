using Newtonsoft.Json;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Core.Kpi.Contracts.ServiceLibrary;
using Redarbor.Core.Kpi.Contracts.ServiceLibrary.Enums;
using Redarbor.Core.Shared.Contracts.ServiceLibrary;
using Redarbor.Offer.Contracts.ServiceLibrary;
using Redarbor.Offer.Contracts.ServiceLibrary.DTO;
using Redarbor.Offer.Contracts.ServiceLibrary.Enums;
using Redarbor.Offer.Contracts.ServiceLibrary.Mappings;
using Redarbor.Offer.Library.DomainServiceContracts;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace Redarbor.Offer.Impl.ServiceLibrary
{
    public class OfferPreRegisterService : IOfferPreRegisterService
    {
        private readonly IKpiService _kpiService;
        private readonly IExceptionPublisherService _exceptionPublisherService;
        private readonly IPortalConfigurationService _portalConfigurationService;
        private readonly IOfferPreRegisterRecoverAndPersist _offerPreRegisterRecoverAndPersist;
        private readonly IApiService _apiService;

        public OfferPreRegisterService(IKpiService kpiService, IExceptionPublisherService exceptionPublisherService, IPortalConfigurationService portalConfigurationService, IOfferPreRegisterRecoverAndPersist offerPreRegisterRecoverAndPersist, IApiService apiService)
        {
            _kpiService = kpiService;
            _exceptionPublisherService = exceptionPublisherService;
            _portalConfigurationService = portalConfigurationService;
            _offerPreRegisterRecoverAndPersist = offerPreRegisterRecoverAndPersist;
            _apiService = apiService;
        }

        public bool ClearOfferSelectedToImport(string companyNit, short idPortal)
        {
            return _offerPreRegisterRecoverAndPersist.ClearOfferSelectedToImport(companyNit, idPortal);
        }

        public OfferPreRegisterDTO GetOfferById(int id)
        {
            return OfferPreRegisterMapping.Map(_offerPreRegisterRecoverAndPersist.GetOfferById(id));
        }

        public List<OfferPreRegisterDTO> GetOffersPendingToImportByCompanyNit(string companyNit, short idPortal)
        {
            var list = new List<OfferPreRegisterDTO>();
            var listentity = _offerPreRegisterRecoverAndPersist.GetOffersByCompanyNit(companyNit, idPortal, (short)OfferPreRegisterStatusEnum.Pending);

            listentity.ForEach(o => { list.Add(OfferPreRegisterMapping.Map(o)); });

            return list;
        }

        public List<OfferPreRegisterDTO> GetOffersSelectedToImportByCompanyNit(string companyNit, short idPortal)
        {
            var list = new List<OfferPreRegisterDTO>();
            var listentity = _offerPreRegisterRecoverAndPersist.GetOffersToImportByCompanyNit(companyNit, idPortal);

            listentity.ForEach(o => { list.Add(OfferPreRegisterMapping.Map(o)); });

            return list;
        }

        public OfferImportPreRegisterResponseDTO ImportOffersList(OfferImportPreRegisterRequestDTO offersToImport)
        {          
            int applicationId = GetApplicationId();
            string baseUrl = GetBaseApiOfferUrl();
            var endpoint = $"{baseUrl}v1/ImportFreemiumOffersAsync?idOrigin={applicationId}";

            if (offersToImport == null || offersToImport.Offers?.Count == 0) return new OfferImportPreRegisterResponseDTO() { IsSuccesful = false, Error = "No hay ofertas pendientes a importar" };

            return _apiService.Post<OfferImportPreRegisterResponseDTO>(endpoint, offersToImport);            
        }
      
        public async Task ImportOffersListAsync(OfferImportPreRegisterRequestDTO offersToImport)
        {       
   
            try
            {
                if (offersToImport == null || offersToImport.Offers?.Count == 0) throw new ArgumentException("No hay ofertas para importar.", nameof(offersToImport));

                var extradata = new Dictionary<string, string>
                {
                    { "IdCompany", offersToImport.IdCompany.ToString() },
                    { "IdPortal", offersToImport.IdPortal.ToString() },
                    { "TotalOfertasAImportar", offersToImport.Offers.Count.ToString() },
                    { "StartWatch", DateTime.UtcNow.ToString() }
                };

                int applicationId = GetApplicationId();
                string baseUrl = GetBaseApiOfferUrl();
                var endpoint = $"{baseUrl}v1/ImportFreemiumOffersAsync?idOrigin={applicationId}";
              
                _exceptionPublisherService.PublishWarning(new Exception($"LLamamos a {endpoint}"), "OfferPreRegisterService", "ImportOffersListAsync", extradata, offersToImport.IdPortal);

                _ = _apiService.PostAsync(endpoint, offersToImport, true, (short)applicationId, offersToImport.IdPortal);

            }
            catch (Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "OfferPreRegisterService", "ImportOffersListAsync",false, null, offersToImport.IdPortal);
            }
        }

        public async Task<bool> SendEmailImportOffersCompleted(int idCompany, string companyNit, short idPortal)
        {           

            try
            {
                int applicationId = GetApplicationId();
                string baseUrl = GetBaseApiCompanyUrl();
                var endpoint = $"{baseUrl}offerPreRegister/sendMailPublishLastOffer";

                var totalOffersMigrated = GetTotalOffersByCompanyNit(companyNit,idPortal,OfferPreRegisterStatusEnum.Migrated);                               

                _kpiService.AddSumBlock((short)KpiEnum.CT_AE_IMPORTED_OFFERS, idPortal,totalOffersMigrated);

                var result = await _apiService.PostAsync(endpoint, new OfferPreRegisterMailRequestDTO() { IdCompany = idCompany, Nit = companyNit }, true, (short)applicationId, idPortal);

                return true;
            }
            catch (Exception ex )
            {
                var extradata = new Dictionary<string, string>
                {
                    { "IdCompany", idCompany.ToString() },
                    { "IdPortal", idPortal.ToString() }
                };
                _exceptionPublisherService.Publish(ex, "OfferPreRegisterService", "SendEmailImportOffersCompleted", false, extradata, idPortal);

                return false;
            }        
        }

        private int GetApplicationId()
        {
            if (int.TryParse(ConfigurationManager.AppSettings["APP_ID"], out int applicationId))
            {
                return applicationId;
            }
            throw new ConfigurationErrorsException("APP_ID no está configurado correctamente.");
        }

        private string GetBaseApiOfferUrl()
        {
            string baseUrl = ConfigurationManager.AppSettings["OFFERS_AE_URL_API"];
            if (string.IsNullOrEmpty(baseUrl))
            {
                throw new ConfigurationErrorsException("OFFERS_AE_URL_API no está configurado.");
            }
            return baseUrl;
        }

        private string GetBaseApiCompanyUrl()
        {
            string baseUrl = ConfigurationManager.AppSettings["COMPANY_AE_URL_API"];
            if (string.IsNullOrEmpty(baseUrl))
            {
                throw new ConfigurationErrorsException("COMPANY_AE_URL_API no está configurado.");
            }
            return baseUrl;
        }

        public bool SetOfferStatusById(int id, int idOffer, int idCompany, OfferPreRegisterStatusEnum status, short idPortal)
        {
            try
            {

                //añadimos la fecha de migración                
                if (status == OfferPreRegisterStatusEnum.Migrated)
                {
                    _offerPreRegisterRecoverAndPersist.SetOfferMigratedSuccessById(id, idOffer, idCompany, (short)status, idPortal, DateTime.UtcNow);
                }
                else
                {
                    _offerPreRegisterRecoverAndPersist.SetOfferStatusById(id, idCompany, (short)status, idPortal);
                }

                return true;
            }
            catch (System.Exception ex)
            {
                _exceptionPublisherService.Publish(ex, "OfferPreRegisterService", "SetOfferStatusById");
                return false;
            }
        }

        public bool UpdateOfferById(OfferUpdatePreRegisterDTO offerPreRegisterDTO)
        {
            return _offerPreRegisterRecoverAndPersist.UpdateCargoAndDescriptionById(offerPreRegisterDTO.Id, offerPreRegisterDTO.IdPortal, offerPreRegisterDTO.Cargo, offerPreRegisterDTO.Description);
        }

        public bool UpdateOfferSelectedToImportByIds(string companyNit, short idPortal, List<int> ids)
        {
            return _offerPreRegisterRecoverAndPersist.UpdateOfferSelectedToImportByIds(companyNit, idPortal, ids);
        }

        public bool UpdateOfferSelectedToImportById(int id, bool isChecked)
        {
            return _offerPreRegisterRecoverAndPersist.UpdateOfferSelectedToImportById(id, isChecked);
        }

        public int GetTotalOffersByCompanyNit(string companyNit, short idPortal, OfferPreRegisterStatusEnum status)
        {
            return _offerPreRegisterRecoverAndPersist.GetTotalOffersByCompanyNit(companyNit, idPortal, (short)status);
        }

        public int GetTotalOffersToImportByCompany(string companyNit, short idPortal)
        {
            return _offerPreRegisterRecoverAndPersist.GetTotalOffersToImportByCompany(companyNit, idPortal);
        }       
    }
}
