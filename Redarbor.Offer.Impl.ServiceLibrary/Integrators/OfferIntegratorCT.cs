using Redarbor.Common.Entities.Configuration;
using Redarbor.Company.Contracts.ServiceLibrary;
using Redarbor.Core.Computrabajo.Contracts.ServiceLibrary;
using Redarbor.Core.Contracts.ServiceLibrary;
using Redarbor.Core.Counter.Contracts.ServiceLibrary;
using Redarbor.Core.Kpi.Contracts.ServiceLibrary;
using Redarbor.Core.Kpi.Contracts.ServiceLibrary.Enums;
using Redarbor.Core.Resolver.Contracts.ServiceLibrary;
using Redarbor.Core.SEO.Contracts.ServiceLibrary;
using Redarbor.Core.Stack.Contracts.ServiceLibrary;
using Redarbor.Core.TrackingActions.Contracts.ServiceLibrary;
using Redarbor.Extensions.Library.Extensions;
using Redarbor.Master.Entities.Company;
using Redarbor.Master.Entities.Cv;
using Redarbor.Master.Entities.Enums;
using Redarbor.Master.Entities.Offers;
using Redarbor.Master.Entities.Product;
using Redarbor.Master.Entities.Stack;
using Redarbor.Match.Contracts.ServiceLibrary;
using Redarbor.Match.Contracts.ServiceLibrary.DTO;
using Redarbor.Offer.Contracts.ServiceLibrary;
using Redarbor.Offer.Contracts.ServiceLibrary.DTO;
using Redarbor.Offer.Contracts.ServiceLibrary.Enums;
using Redarbor.Offer.Contracts.ServiceLibrary.Integrators;
using Redarbor.Offer.Library.DomainServiceContracts;
using Redarbor.Products.Contracts.ServiceLibrary;
using Redarbor.Repo.Offer.Library.DomainServiceContracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Redarbor.Offer.Impl.ServiceLibrary.Integrators
{
    public class OfferIntegratorCT : IOfferIntegratorCT
    {
        private readonly IOfferIntegratorRecoverAndPersist _offerIntegratorRecoverAndPersist;
        private readonly IOfferRecoverAndPersist _offerRecoverAndPersist;
        private readonly IOfferComputrabajoRecoverAndPersist _offerComputrabajoRecoverAndPersist;
        private readonly IMatchOfferService _matchOfferService;
        private readonly IProductService _productService;
        private readonly ICompanyProductService _companyProductService;
        private readonly ICompanyProductFeatureService _companyProductFeatureService;
        private readonly IOfferCountersService _offerCountersService;
        private readonly ICompanyCountersService _companyCountersService;
        private readonly IMatchService _matchService;
        private readonly ICompanyService _companyService;
        private readonly ITrackingActionsService _trackingActionsService;
        private readonly IDictionaryService _dictionaryService;
        private readonly IStackService _stackService;
        private readonly IStackModService _stackModService;
        private readonly IStackMatchesManagerService _stackMatchesManagerService;
        private readonly IKpiService _kpiService;
        private readonly ISemanticUrlService _semanticUrlService;
        private readonly IClientIpAddressResolverService _clientIpAddressResolverService;
        private readonly IPortalResolverService _portalResolverService;
        private readonly IPortalConfigurationService _portalConfigurationService;
        private readonly IKillerQuestionsService _killerQuestionsService;
        private readonly IExceptionPublisherService _exceptionPublisherService;

        public OfferIntegratorCT(IOfferRecoverAndPersist offerRecoverAndPersist,
            IOfferComputrabajoRecoverAndPersist offerComputrabajoRecoverAndPersist,
            IMatchOfferService matchOfferService,
            IOfferIntegratorRecoverAndPersist offerIntegratorRecoverAndPersist,
            IProductService productService,
            ICompanyProductService companyProductService,
            ICompanyProductFeatureService companyProductFeatureService,
            ICompanyCountersService companyCountersService,
            IOfferCountersService offerCountersService,
            IMatchService matchService,
            ICompanyService companyService,
            ITrackingActionsService trackingActionsService,
            IDictionaryService dictionaryService,
            IStackService stackService,
            IStackModService stackModService,
            IStackMatchesManagerService stackMatchesManagerService,
            IKpiService kpiService,
            ISemanticUrlService semanticUrlService,
            IClientIpAddressResolverService clientIpAddressResolverService,
            IPortalResolverService portalResolverService,
            IPortalConfigurationService portalConfigurationService,
            IKillerQuestionsService killerQuestionsService,
            IExceptionPublisherService exceptionPublisherService)
        {
            _offerIntegratorRecoverAndPersist = offerIntegratorRecoverAndPersist;
            _offerRecoverAndPersist = offerRecoverAndPersist;
            _offerComputrabajoRecoverAndPersist = offerComputrabajoRecoverAndPersist;
            _matchOfferService = matchOfferService;
            _productService = productService;
            _companyProductService = companyProductService;
            _companyProductFeatureService = companyProductFeatureService;
            _companyCountersService = companyCountersService;
            _offerCountersService = offerCountersService;
            _matchService = matchService;
            _companyService = companyService;
            _trackingActionsService = trackingActionsService;
            _dictionaryService = dictionaryService;
            _stackService = stackService;
            _stackModService = stackModService;
            _stackMatchesManagerService = stackMatchesManagerService;
            _kpiService = kpiService;
            _semanticUrlService = semanticUrlService;
            _clientIpAddressResolverService = clientIpAddressResolverService;
            _portalResolverService = portalResolverService;
            _portalConfigurationService = portalConfigurationService;
            _killerQuestionsService = killerQuestionsService;
            _exceptionPublisherService = exceptionPublisherService;
        }

        public void Update(OfferEntity offer, OfferEntity offerPreUpdate, ref SaveOfferResult saveOfferResult, PortalConfig portalConfig, bool autoExcludeKillerQuestions = true, long userId = 0)
        {
            var offerIntegrator = offer.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo);
            if (offerIntegrator is null)
                return;

            var offerPreUpdateIntegrator = offerPreUpdate.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo);
            var companyProduct = _companyProductService.GetByCompanyProductId(offerIntegrator.idcompanyproduct, portalConfig.PortalId, offer.idcompany);

            TraceExceptionIfNoFindCompanyProductWhenUpdate(offer, portalConfig, offerIntegrator, companyProduct);

            SaveOfferVersioning(offer, offerPreUpdate);
            PrepareOfferData(offer, companyProduct, portalConfig, ref saveOfferResult, offerPreUpdate);

            // ------ Always previous values -------
            offerIntegrator.Isflash = offerPreUpdateIntegrator?.Isflash ?? 0;
            // -------------------------------------

            _matchOfferService.AddMatchOffer(offer, offerIntegrator);

            if (!_offerIntegratorRecoverAndPersist.Update(offerIntegrator, portalConfig.PortalId))
                return;

            if (offer.KillerQuestions.Any())
            {
                _stackMatchesManagerService.Save(new StackMatchesManagerEntity(portalConfig.PortalId)
                {
                    ObjectId = offer.idoffer,
                    TypeId = (short)StackObjectTypeEnum.Killers
                });
            }

            if (companyProduct.Features.Exists(e => e.AmbitId == (short)ProductAmbitEnum.CandidateAdequacy))
            {
                _stackMatchesManagerService.Save(new StackMatchesManagerEntity(portalConfig.PortalId)
                {
                    ObjectId = offer.idoffer,
                    TypeId = (short)StackObjectTypeEnum.Adecuation
                });
            }

            _stackModService.PublishOfferStackMod(offer.idoffer, offer.idcompany, offer.idportal, (short)OfferActionStackModEnum.Published, offer.experienceyears, offerPreUpdateIntegrator?.urlrewrite ?? string.Empty);

            if (portalConfig.offer_moderation_queues)
            {
                _stackModService.UpdateQueueStatus(portalConfig.PortalId, offer.idoffer, StackModQueueEnum.Pending);
                _stackModService.InsertHistoryQueue(offer.idoffer, (short)StackModQueueEnum.Pending, portalConfig.PortalId);
            }

            var postmodFeature = companyProduct.Features.FirstOrDefault(feat => feat.AmbitId == (int)ProductAmbitEnum.OfferPostModeracion);
            if (postmodFeature != null)
            {
                _offerIntegratorRecoverAndPersist.UpdateOfferStatus(offer.idoffer, (short)OfferStatusEnum.Activa, (short)OfferStatusRowEnum.Activo, portalConfig.PortalId, offerIntegrator.IdIntegrator);
                offerIntegrator.idstatus = (int)OfferStatusEnum.Activa;
                offer.idstatus = (short)OfferStatusRowEnum.Activo;

                _stackService.Save(new StackEntity(portalConfig.PortalId)
                {
                    ObjectId = offer.idoffer,
                    TypeId = (short)StackObjectTypeEnum.Offer
                });
            }

            _kpiService.Add((int)KpiEnum.OFFER_UPDATE, portalConfig.PortalId);
            OfferProductTrace(offer, offerIntegrator, ProductTraceActionsEnum.UPDATE_OFFER.AnotationDescription());

            saveOfferResult.SaveOfferResultEnum = SaveOfferResultEnum.Ok;
        }

        public void RePublishAllCompanyOffers(CompanyEntity company)
        {
            var offersIds = _offerRecoverAndPersist.GetOfferIds(company.Id, _portalResolverService.ResolvePortalId(), (short)OfferIntegratorEnum.CompuTrabajo);

            Task.Run(
                    () =>
                    {
                        foreach (var item in offersIds)
                        {
                            _stackService.Save(new StackEntity(company.PortalId)
                            {
                                PortalId = company.PortalId,
                                ObjectId = item,
                                TypeId = (int)StackObjectTypeEnum.Offer
                            });
                        }

                    });
        }

        public void Insert(OfferEntity offer, PortalConfig portalConfig, ref SaveOfferResult saveOfferResult, int userId, string userName, bool isDuplicatedOffer,
            bool autoExcludeKillerQuestions = true, string defaultSelectStageName = "Seleccionados", bool IsSherlock = false)
        {
            var offerIntegrator = offer.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo);
            if (offerIntegrator is null)
                return;

            var companyProduct = _companyProductService.GetByIdCompany(offer.idcompany, offer.idportal, OfferIntegratorEnum.CompuTrabajo, userId);

            if (companyProduct.GroupId == (short)ProductGroupsEnum.Packs)
                companyProduct = _companyProductService.GetByCompanyProductId(offerIntegrator.idcompanyproduct, portalConfig.PortalId, offer.idcompany);

            if (!_companyProductService.ConsumeUnits(companyProduct, (short)ProductAmbitEnum.Offer, 1, userId, offerIntegrator.expirationtime, (OfferIntegratorEnum)offerIntegrator.IdIntegrator))
            {
                saveOfferResult.SaveOfferIntegratorResults.Add(new SaveOfferIntegratorResult()
                {
                    OfferIntegratorErrorEnum = OfferIntegratorSaveErrorEnum.NotEnoughtCreditsCreateOffer,
                    OfferIntegratorEnum = OfferIntegratorEnum.CompuTrabajo
                });
                return;
            }

            PrepareOfferData(offer, companyProduct, portalConfig, ref saveOfferResult);

            if (!_offerIntegratorRecoverAndPersist.Insert(offerIntegrator, portalConfig.PortalId))
            {
                saveOfferResult.SaveOfferIntegratorResults.Add(new SaveOfferIntegratorResult()
                {
                    OfferIntegratorErrorEnum = OfferIntegratorSaveErrorEnum.ErrorInsertInOfferIntegrators,
                    OfferIntegratorEnum = OfferIntegratorEnum.CompuTrabajo
                });
                return;
            }
            _matchOfferService.AddMatchOffer(offer, offerIntegrator);

            _matchService.SetProcessMatchStatus(offer.idcompany, offer.idofferCT, (int)MatchProcessStatusCompanyEnum.Recruiting, portalConfig.PortalId);
            _stackModService.PublishOfferStackMod(offer.idoffer, offer.idcompany, offer.idportal, (short)OfferActionStackModEnum.Published, offer.experienceyears, offerIntegrator.urlrewrite);

            if (portalConfig.offer_moderation_queues)
            {
                _stackModService.AddToQueue(portalConfig.PortalId, offer.idoffer, StackModQueueEnum.Pending);
                _stackModService.InsertHistoryQueue(offer.idoffer, (short)StackModQueueEnum.Pending, portalConfig.PortalId);
            }

            SetPostModerateOffer(offer, portalConfig, companyProduct);

            if (companyProduct.GroupId == (short)ProductGroupsEnum.Packs)
            {
                _offerRecoverAndPersist.UpdateDateLastUp(offer.idoffer, offer.idcompany, portalConfig.PortalId);
                offer.datelastup = portalConfig.CurrentDateTimePortal;
                offer.updatedon = portalConfig.CurrentDateTimePortal;
            }

            _kpiService.Add((int)CompanyKpiEnum.OffersTotalPublished, portalConfig.PortalId);
            _kpiService.AddByProductGroup(companyProduct.GroupId, portalConfig.PortalId, DataToTraceKpisOfferPublish(offer, portalConfig, userId, companyProduct));
            _kpiService.AddMonthlyOffersByCompany((int)KpiEnum.MONTHLY_OFFERS_BY_COMPANY, portalConfig.PortalId, offer.idcompany);

            if (isDuplicatedOffer)
            {
                OfferProductTrace(offer, offerIntegrator,
                    offer.KillerQuestions.Any()
                        ? ProductTraceActionsEnum.DUPLICATE_OFFER_KQ.AnotationDescription()
                        : ProductTraceActionsEnum.DUPLICATE_OFFER.AnotationDescription());
                Task.Factory.StartNew(() =>
                {
                    _companyCountersService.AddCounterCompany((int)KpiEnum.COMPANY_OFFER_DUPLICATE, portalConfig.PortalId, offer.idcompany, 1, false);
                    _kpiService.AddSumBlock((int)KpiEnum.COMPANY_OFFER_DUPLICATE, portalConfig.PortalId, 1);
                });
            }
            else
            {
                OfferProductTrace(offer, offerIntegrator,
                    offer.KillerQuestions.Any()
                        ? ProductTraceActionsEnum.NEW_OFFER_KQ.AnotationDescription()
                        : ProductTraceActionsEnum.NEW_OFFER.AnotationDescription());
            }

            if (offerIntegrator.Isflash == 1)
            {
                _trackingActionsService.AddActionTracking(new TrackingActionEntity()
                {
                    Type = (short)ProductTraceActionsEnum.OFFER_FLASH,
                    IdObject = offer.idoffer,
                    IdPortal = portalConfig.PortalId,
                    IdStatus = (short)OfferStatusRowEnum.Pendiente,
                    IdCompany = offer.idcompany,
                    IdUser = userId,
                    UserName = userName,
                    DateUpdate = DateTime.MinValue
                });

                _stackService.Save(new StackEntity(portalConfig.PortalId)
                {
                    ObjectId = offer.idoffer,
                    TypeId = (short)StackObjectTypeEnum.OfferFlash
                });

                _kpiService.Add((int)KpiEnum.OFERTA_FLASH_COMPANY, portalConfig.PortalId);
            }

            _companyProductService.UpdateDateMod(companyProduct.ProductId, portalConfig.PortalId);
            if (companyProduct.ProductId == (int)ProductEnum.WelcomePack5)
            {
                var availableWelcome = _companyProductFeatureService.GetInitialUnitsByFeature(companyProduct, (int)ProductAmbitEnum.Offer);
                var totalOfferActive = _offerCountersService.GetCounters(offer.idcompany, portalConfig.PortalId, OfferIntegratorEnum.CompuTrabajo, (int)ProductAmbitEnum.Offer, false);
                if (availableWelcome - totalOfferActive <= 0)
                {
                    saveOfferResult.SaveOfferResultEnum = SaveOfferResultEnum.EndPresent;
                    return;
                }
            }

            if (_companyProductService.Has2N(companyProduct))
                _companyCountersService.IncrementCountersPublishOffersByMonth(offer.idcompany);
        }

        private static Dictionary<string, string> DataToTraceKpisOfferPublish(OfferEntity offer, PortalConfig portalConfig, int userId, CompanyProductEntity companyProduct)
        {
            return new Dictionary<string, string> {
                { "idOffer", offer.idoffer.ToString()},
                { "idCompany", offer.idcompany.ToString() },
                { "userId", userId.ToString() },
                { "companyProductId", companyProduct.Id.ToString() },
                { "companyProductGroupId", companyProduct.GroupId.ToString() },
                { "portalId", portalConfig.PortalId.ToString() }
            };
        }

        public bool SetStatus(SetStatusIntegratorDTO setStatusdto)
        {
            setStatusdto.IdPortal = setStatusdto.IdPortal > 0 ? setStatusdto.IdPortal : _portalResolverService.ResolvePortalId();
            var companyProduct = _companyProductService.GetByCompanyProductId(setStatusdto.OfferIntegrator.idcompanyproduct, setStatusdto.IdPortal, setStatusdto.Offer.idcompany);

            if (!CheckCanDoActionSetStatus(setStatusdto.Offer, setStatusdto.OfferIntegrator, setStatusdto.NewStatus, companyProduct)) return false;

            var canSet = false;
            var currentIdOfferStatus = setStatusdto.OfferIntegrator.idstatus;
            var portalConfig = _portalConfigurationService.GetPortalConfiguration(setStatusdto.IdPortal);
            int ispayment = setStatusdto.OfferIntegrator.IsPayment;

            var totalmoderatedtimes = setStatusdto.OfferIntegrator.TotalModeratedTimes;

            if (currentIdOfferStatus == setStatusdto.NewStatus) return true;
            if (currentIdOfferStatus == 0) return false;

            switch (currentIdOfferStatus)
            {
                case (int)OfferStatusEnum.Pendiente:
                    if (ispayment == 1 && totalmoderatedtimes == 0 && setStatusdto.NewStatus == (int)OfferStatusEnum.Desactivada) canSet = true;
                    if (setStatusdto.NewStatus == (int)OfferStatusEnum.Eliminada) canSet = true;
                    break;
                case (int)OfferStatusEnum.Activa:
                    if (setStatusdto.NewStatus == (int)OfferStatusEnum.Desactivada || setStatusdto.NewStatus == (int)OfferStatusEnum.Eliminada) canSet = true;
                    break;
                case (int)OfferStatusEnum.Desactivada:
                    if (ispayment == 1 && totalmoderatedtimes == 0 && setStatusdto.NewStatus == (int)OfferStatusEnum.Activa) setStatusdto.NewStatus = (int)OfferStatusEnum.Activa;
                    if (setStatusdto.NewStatus == (int)OfferStatusEnum.Pendiente || setStatusdto.NewStatus == (int)OfferStatusEnum.Activa || setStatusdto.NewStatus == (int)OfferStatusEnum.Eliminada)
                        canSet = true;
                    break;
                case (int)OfferStatusEnum.Vencida:
                    if (setStatusdto.NewStatus == (int)OfferStatusEnum.Activa || setStatusdto.NewStatus == (int)OfferStatusEnum.Eliminada) canSet = true;
                    break;
                case (int)OfferStatusEnum.Rechazada:
                case (int)OfferStatusEnum.Borrador:
                    if (setStatusdto.NewStatus == (int)OfferStatusEnum.Eliminada) canSet = true;
                    break;
                case (int)OfferStatusEnum.Eliminado_fisico:
                    if (setStatusdto.NewStatus == (int)OfferStatusEnum.Activa) canSet = true;
                    break;
            }

            if (!canSet) return false;

            if (setStatusdto.NewStatus == (short)OfferStatusEnum.Activa
                && setStatusdto.Offer.HiddenCompany == 1
                && companyProduct.Features.Exists(e => e.IsSimultaneous && e.AmbitId == (short)ProductAmbitEnum.Offer)
                && !_companyProductService.CanDoActionByFeature(setStatusdto.Offer.idcompany, setStatusdto.Offer.idportal, (short)ProductAmbitEnum.OfferHiddenName, 1, setStatusdto.Offer.createdby, OfferIntegratorEnum.CompuTrabajo))
            {
                return false;
            }

            var result = setStatusdto.NewStatus == (int)OfferStatusEnum.Activa
                ? _offerIntegratorRecoverAndPersist.ReactivateOffer(setStatusdto.Offer.idoffer, setStatusdto.Offer.idcompany, (short)OfferStatusRowEnum.Activo, setStatusdto.NewStatus, setStatusdto.IdPortal, setStatusdto.OfferIntegrator.IdIntegrator)
                : _offerIntegratorRecoverAndPersist.SetStatus(setStatusdto.Offer.idoffer, setStatusdto.Offer.idcompany, setStatusdto.NewStatus, setStatusdto.IdPortal, setStatusdto.OfferIntegrator.IdIntegrator);

            if (!result) return false;

            if (result && setStatusdto.NewStatus == (int)OfferStatusEnum.Eliminada)
            {
                OfferProductTrace(setStatusdto.Offer, setStatusdto.OfferIntegrator, ProductTraceActionsEnum.DELETE_OFFER.AnotationDescription(), setStatusdto.IdUser);
                _matchService.ChangeAllCandidatesProcessStatus(new CandidateProcessStatusDTO(setStatusdto.Offer.idofferCT, (int)MatchCandidateProcessStatusEnum.Discarded, setStatusdto.IdPortal, setStatusdto.Offer.idcompany),
                                                               new TimelineExtraParamsChangeMatchDTO(setStatusdto.Offer.title, _companyService.GetCompanyComercialName(setStatusdto.Offer.idcompany, setStatusdto.IdPortal),
                                                               portalConfig.idapp, portalConfig.CurrentIntPortal, portalConfig.PortalId, setStatusdto.CityName));
                _offerComputrabajoRecoverAndPersist.DeleteConversationByOffer(setStatusdto.Offer.idoffer, setStatusdto.IdPortal);
                _offerIntegratorRecoverAndPersist.SetDeletedOn(setStatusdto.OfferIntegrator.Id, setStatusdto.OfferIntegrator.IdOffer, setStatusdto.IdPortal);

                if (setStatusdto.OfferIntegrator.idstatus == (short)OfferStatusEnum.Borrador)
                {
                    _killerQuestionsService.DeleteKQ(setStatusdto.Offer.idofferCT, setStatusdto.IdPortal);
                }
                _stackModService.PublishOfferStackMod(setStatusdto.Offer.idoffer, setStatusdto.Offer.idcompany, setStatusdto.Offer.idportal, (short)OfferActionStackModEnum.Deleted, setStatusdto.Offer.experienceyears, setStatusdto.OfferIntegrator.urlrewrite);
            }

            if (result && setStatusdto.NewStatus == (int)OfferStatusEnum.Desactivada)
            {
                OfferProductTrace(setStatusdto.Offer, setStatusdto.OfferIntegrator, ProductTraceActionsEnum.OFFER_DEACTIVATE.AnotationDescription(), setStatusdto.IdUser);
                _stackModService.PublishOfferStackMod(setStatusdto.Offer.idoffer, setStatusdto.Offer.idcompany, setStatusdto.Offer.idportal, (short)OfferActionStackModEnum.Archived, setStatusdto.Offer.experienceyears, setStatusdto.OfferIntegrator.urlrewrite);
            }

            if (result && setStatusdto.NewStatus == (int)OfferStatusEnum.Vencida)
                _stackModService.PublishOfferStackMod(setStatusdto.Offer.idoffer, setStatusdto.Offer.idcompany, setStatusdto.Offer.idportal, (short)OfferActionStackModEnum.Expired, setStatusdto.Offer.experienceyears, setStatusdto.OfferIntegrator.urlrewrite);

            if (result && setStatusdto.NewStatus == (int)OfferStatusEnum.Activa)
            {
                OfferProductTrace(setStatusdto.Offer, setStatusdto.OfferIntegrator, ProductTraceActionsEnum.OFFER_REACTIVATE.AnotationDescription(), setStatusdto.IdUser);
                _stackModService.PublishOfferStackMod(setStatusdto.Offer.idoffer, setStatusdto.Offer.idcompany, setStatusdto.Offer.idportal, (short)OfferActionStackModEnum.Published, setStatusdto.Offer.experienceyears, setStatusdto.OfferIntegrator.urlrewrite);
            }

            _stackService.Save(new StackEntity(setStatusdto.IdPortal)
            {
                ObjectId = setStatusdto.Offer.idoffer,
                TypeId = (int)StackObjectTypeEnum.Offer
            });

            if (setStatusdto.NewStatus == (short)OfferStatusEnum.Pendiente)
            {
                _stackModService.PublishOfferStackMod(setStatusdto.Offer.idoffer, setStatusdto.Offer.idcompany, setStatusdto.Offer.idportal, (short)OfferActionStackModEnum.Published, setStatusdto.Offer.experienceyears, setStatusdto.OfferIntegrator.urlrewrite);
            }
            else
            {
                _stackModService.UpdateStatus(
                    new StackModEntity
                    {
                        objectid = setStatusdto.Offer.idoffer,
                        typeid = (short)StackObjectTypeEnum.Offer,
                        statusid = (short)OfferStatusRowEnum.Activo,
                        idportal = setStatusdto.IdPortal
                    });
            }

            if ((setStatusdto.NewStatus == (int)OfferStatusEnum.Eliminada ||
                setStatusdto.NewStatus == (int)OfferStatusEnum.Vencida ||
                setStatusdto.NewStatus == (int)OfferStatusEnum.Desactivada) &&
                setStatusdto.OfferIntegrator.idstatus != (short)OfferStatusEnum.Borrador)
            {
                Task.Factory.StartNew(() =>
                {
                    _matchService.SetProcessMatchStatus(setStatusdto.Offer.idcompany, setStatusdto.Offer.idofferCT, (int)MatchProcessStatusCompanyEnum.Ended, setStatusdto.IdPortal);
                });
            }
            else if (setStatusdto.NewStatus == (int)OfferStatusEnum.Activa && currentIdOfferStatus == (int)OfferStatusEnum.Eliminado_fisico)
            {
                _matchService.SetProcessMatchStatus(setStatusdto.Offer.idcompany, setStatusdto.Offer.idofferCT, (int)MatchProcessStatusCompanyEnum.Recruiting, setStatusdto.IdPortal);
            }

            if (companyProduct.GroupId == (int)ProductGroupsEnum.Membership)
            {
                if (Convert.ToBoolean(setStatusdto.OfferIntegrator.ishighlighted))
                    _offerIntegratorRecoverAndPersist.UpdateOfferHighlighted(setStatusdto.Offer.idoffer, 0, setStatusdto.IdPortal, setStatusdto.OfferIntegrator.IdIntegrator);

                if (Convert.ToBoolean(setStatusdto.OfferIntegrator.isurgent))
                    _offerIntegratorRecoverAndPersist.UpdateOfferUrgent(setStatusdto.Offer.idoffer, 0, setStatusdto.IdPortal, setStatusdto.OfferIntegrator.IdIntegrator);
            }

            Task.Factory.StartNew(() => { _companyService.UpdateActiveOffers(setStatusdto.Offer.idcompany, _offerRecoverAndPersist.GetTotalOffers(setStatusdto.Offer.idcompany, setStatusdto.IdPortal, setStatusdto.OfferIntegrator.IdIntegrator), setStatusdto.IdPortal); });

            return true;
        }

        public bool RenewOffer(OfferEntity offer, OfferIntegratorEntity offerIntegrator)
        {
            var companyProduct = _companyProductService.GetByCompanyProductId(offerIntegrator.idcompanyproduct, offer.idportal, offer.idcompany);
            var portalConfig = _portalConfigurationService.GetPortalConfiguration();

            var highligthedOffersFeature = new CompanyProductFeatureEntity();
            var urgentdOffersFeature = new CompanyProductFeatureEntity();
            var hiddenNameOffersFeature = new CompanyProductFeatureEntity();

            var userConsumCredits = offer.createdby;
            var updateOffer = false;

            if (offerIntegrator.ishighlighted == 1)
            {
                if (!_companyProductService.CanDoActionByFeature(offer.idcompany, offer.idportal, (short)ProductAmbitEnum.OfferHighlighted, 1, userConsumCredits, OfferIntegratorEnum.CompuTrabajo))
                {
                    offerIntegrator.ishighlighted = 0;
                    updateOffer = true;
                }
            }

            if (offerIntegrator.isurgent == 1)
            {
                if (!_companyProductService.CanDoActionByFeature(offer.idcompany, offer.idportal, (short)ProductAmbitEnum.OfferUrgent, 1, userConsumCredits, OfferIntegratorEnum.CompuTrabajo))
                {
                    offerIntegrator.isurgent = 0;
                    updateOffer = true;
                }
            }

            if (offer.HiddenCompany == 1)
            {
                if (!_companyProductService.CanDoActionByFeature(offer.idcompany, offer.idportal, (short)ProductAmbitEnum.OfferHiddenName, 1, userConsumCredits, OfferIntegratorEnum.CompuTrabajo))
                {
                    offer.HiddenCompany = 0;
                    updateOffer = true;
                }
            }

            if (updateOffer)
            {
                offer.ProductClass = _companyProductService.SetProductClassByIdGroup(companyProduct.GroupId);
                _offerRecoverAndPersist.Update(offer, portalConfig);
            }

            if (!_companyProductService.ConsumeUnits(offer.idcompany, offer.idportal, (int)ProductAmbitEnum.RenewOffer, 1, userConsumCredits, offerIntegrator.expirationtime, OfferIntegratorEnum.CompuTrabajo)) return false;

            var updateProductDateMod = false;
            if (highligthedOffersFeature.IsUnlimited &&
                offerIntegrator.ishighlighted == 1 &&
                highligthedOffersFeature.IsRecurrent)
            {
                if (_companyProductService.ConsumeUnits(offer.idcompany, offer.idportal, (int)ProductAmbitEnum.OfferHighlighted, 1, userConsumCredits, offerIntegrator.expirationtime, OfferIntegratorEnum.CompuTrabajo))
                {
                    updateProductDateMod = true;
                }
            }
            if (urgentdOffersFeature.IsUnlimited &&
                offerIntegrator.isurgent == 1 &&
                urgentdOffersFeature.IsRecurrent)
            {
                if (_companyProductService.ConsumeUnits(offer.idcompany, offer.idportal, (int)ProductAmbitEnum.OfferUrgent, 1, userConsumCredits, offerIntegrator.expirationtime, OfferIntegratorEnum.CompuTrabajo))
                {
                    updateProductDateMod = true;
                }
            }
            if (hiddenNameOffersFeature.IsUnlimited &&
                offer.HiddenCompany == 1 &&
                hiddenNameOffersFeature.IsRecurrent)
            {
                if (_companyProductService.ConsumeUnits(offer.idcompany, offer.idportal, (int)ProductAmbitEnum.OfferHiddenName, 1, userConsumCredits, offerIntegrator.expirationtime, OfferIntegratorEnum.CompuTrabajo))
                {
                    updateProductDateMod = true;
                }
            }

            if (updateProductDateMod)
                _companyProductService.UpdateDateMod(companyProduct.Id, offer.idportal);

            DisableHighlightedAndUrgentIfPack(offer, offerIntegrator, companyProduct, offer.idportal, userConsumCredits);

            offer.ProductClass = _companyProductService.SetProductClassByIdGroup(companyProduct.GroupId);

            TraceErrorIfNoProductRenewOffer(offer, companyProduct);

            if (!RenewOffer(offer.idoffer, (OfferRenewStatusEnum)offerIntegrator.RenewOfferStatus,
                (OfferStatusEnum)offerIntegrator.idstatus, offerIntegrator.expirationtime, portalConfig, offerIntegrator.IdIntegrator, companyProduct.GroupId, offer.ProductClass, companyProduct)) return false;

            if (!_offerIntegratorRecoverAndPersist
                    .UpdateProductIdAndProductCompanyId(offer.idoffer, companyProduct.ProductId, companyProduct.Id,
                        offerIntegrator.IdIntegrator, companyProduct.GroupId, offer.ProductClass))
                return false;

            if (_companyProductService.Has2N(companyProduct))
                _companyCountersService.IncrementCountersPublishOffersByMonth(offer.idcompany);

            _stackService.Save(new StackEntity(portalConfig.PortalId)
            {
                ObjectId = offer.idoffer,
                TypeId = (int)StackObjectTypeEnum.Offer
            });

            if ((OfferStatusEnum)offerIntegrator.idstatus == OfferStatusEnum.Vencida)
            {
                OfferProductTrace(offer, offerIntegrator, ProductTraceActionsEnum.RENEW_OFFER.AnotationDescription());
            }
            else if ((OfferStatusEnum)offerIntegrator.idstatus == OfferStatusEnum.Eliminada)
            {
                OfferProductTrace(offer, offerIntegrator, ProductTraceActionsEnum.RECOVER_OFFER_DELETED.AnotationDescription());
            }
            else if ((OfferStatusEnum)offerIntegrator.idstatus == OfferStatusEnum.Eliminado_fisico)
            {
                _matchService.SetProcessMatchStatus(offer.idcompany, offer.idofferCT, (int)MatchProcessStatusCompanyEnum.Recruiting, portalConfig.PortalId);
                OfferProductTrace(offer, offerIntegrator, ProductTraceActionsEnum.RECOVER_OFFER_PHYSICAL_DELETED.AnotationDescription());
            }

            return true;
        }

        public bool UploadOffer(OfferEntity offer, OfferIntegratorEntity offerIntegrator)
        {
            var companyProduct = _companyProductService.GetByCompanyProductId(offerIntegrator.idcompanyproduct, offer.idportal, offer.idcompany);
            var offerUpFeature = companyProduct.Features.Find(x => x.AmbitId == (short)ProductAmbitEnum.OfferUp);

            if (offerUpFeature != null)
            {
                if (!UpdateDateLastUp(offer, offerUpFeature.FrequencyRenewDays, offerIntegrator.IdIntegrator))
                {
                    return false;
                }
                OfferProductTrace(offer, offerIntegrator, ProductTraceActionsEnum.UP_OFFER_MANUAL.AnotationDescription());
                return true;
            }

            return false;
        }


        public int GetOfferFrequencyRenewDays(OfferEntity offer, OfferIntegratorEntity offerIntegrator)
        {
            var offerFrequencyRenewDays = 0;
            var productOfferFeatures = _companyProductService.GetByCompanyProductId(offerIntegrator.idcompanyproduct, offer.idportal, offer.idcompany);

            if (productOfferFeatures.Features.Any(x => x.AmbitId == (short)ProductAmbitEnum.OfferUp))
                offerFrequencyRenewDays = productOfferFeatures.Features.FirstOrDefault(x => x.AmbitId == (short)ProductAmbitEnum.OfferUp)?.FrequencyRenewDays ?? 0;

            return offerFrequencyRenewDays;
        }



        private void PrepareOfferData(OfferEntity entity, CompanyProductEntity companyProduct, PortalConfig portalConfig, ref SaveOfferResult saveOfferResult, OfferEntity offerPreUpdate = null)
        {
            var offerIntegrationCT =
                entity.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo);

            var offerPreUpdateIntegrationCT =
                offerPreUpdate?.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo);

            if (offerIntegrationCT is null)
                return;

            offerIntegrationCT.IdOffer = entity.idoffer;
            if (entity.Origin != OfferOriginEnum.API)
                offerIntegrationCT.client_ip_mod = _clientIpAddressResolverService.GetIpAddress();

            offerIntegrationCT.createdby = offerPreUpdate != null && offerPreUpdate.createdby > 0 ? offerPreUpdate.createdby : entity.createdby;
            if (offerPreUpdate != null)
            {
                offerIntegrationCT.createdby = offerPreUpdate.createdby;
                offerIntegrationCT.createdon = offerPreUpdate.createdon;
            }

            if (offerIntegrationCT.Isflash == 1)
                entity.HiddenCompany = 0;

            if (entity.HiddenCompany == 1)
            {
                offerIntegrationCT.show_contact_email = 0;
                offerIntegrationCT.contact_email = string.Empty;

                offerIntegrationCT.show_phone_offer = 0;
                offerIntegrationCT.contact_phone = string.Empty;

                offerIntegrationCT.show_contact_address = 0;
                offerIntegrationCT.contact_address = string.Empty;

                entity.HiddenCompanyName = entity.HiddenCompanyName;
            }
            else
            {
                entity.HiddenCompanyName = _companyService.GetCompanyComercialName(entity.idcompany, entity.idportal);
            }

            var expirationDays = GetExpirationDays(companyProduct, portalConfig.expiration_days);

            offerIntegrationCT.expirationtime = DateTime.Now.AddDays(expirationDays);
            offerIntegrationCT.urlrewrite = _semanticUrlService.GetSemanticUrlOffer(entity, portalConfig, (short)OfferIntegratorEnum.CompuTrabajo);

            if (companyProduct.GroupId == (short)ProductGroupsEnum.Membership
                && (offerIntegrationCT.ishighlighted == 1 && offerPreUpdateIntegrationCT is null ||
                offerIntegrationCT.ishighlighted == 1 && offerPreUpdateIntegrationCT?.ishighlighted == 0))
            {
                if (!_companyProductService.ConsumeUnits(entity.idcompany, entity.idportal, (short)ProductAmbitEnum.OfferHighlighted, 1, entity.createdby, offerIntegrationCT.expirationtime, OfferIntegratorEnum.CompuTrabajo))
                {
                    offerIntegrationCT.ishighlighted = 0;
                    saveOfferResult.SaveOfferIntegratorResults.Add(new SaveOfferIntegratorResult()
                    {
                        OfferIntegratorErrorEnum = OfferIntegratorSaveErrorEnum.NotEnoughtCreditsMakeOfferHighlighted,
                        OfferIntegratorEnum = OfferIntegratorEnum.CompuTrabajo
                    });
                }
                else
                    _companyCountersService.AddCounterCompany((int)CompanyKpiEnum.CT_AE_OFFERS_HIGHLIGHTED, entity.idportal, entity.idcompany, 1, false);

            }

            if (companyProduct.GroupId == (short)ProductGroupsEnum.Membership
                && (offerIntegrationCT.isurgent == 1 && offerPreUpdate is null ||
                offerIntegrationCT.isurgent == 1 && offerPreUpdateIntegrationCT?.isurgent == 0))
            {
                if (!_companyProductService.ConsumeUnits(entity.idcompany, entity.idportal, (short)ProductAmbitEnum.OfferUrgent, 1, entity.createdby, offerIntegrationCT.expirationtime, OfferIntegratorEnum.CompuTrabajo))
                {
                    offerIntegrationCT.isurgent = 0;
                    saveOfferResult.SaveOfferIntegratorResults.Add(new SaveOfferIntegratorResult()
                    {
                        OfferIntegratorErrorEnum = OfferIntegratorSaveErrorEnum.NotEnoughtCreditsMakeOfferUrgent,
                        OfferIntegratorEnum = OfferIntegratorEnum.CompuTrabajo
                    });
                }
                else
                    _companyCountersService.AddCounterCompany((int)CompanyKpiEnum.CT_AE_OFFERS_URGENT, entity.idportal, entity.idcompany, 1, false);

            }

            if (((offerIntegrationCT.Isflash == 1 && offerPreUpdate is null) ||
                (offerIntegrationCT.Isflash == 1 && offerPreUpdateIntegrationCT?.Isflash == 0)) &&
                entity.HiddenCompany == 0)
            {
                if (!_companyProductService.ConsumeUnits(entity.idcompany, entity.idportal,
                    (short)ProductAmbitEnum.OfferFlash, 1, entity.createdby, offerIntegrationCT.expirationtime,
                    OfferIntegratorEnum.CompuTrabajo))
                {
                    offerIntegrationCT.Isflash = 0;
                    saveOfferResult.SaveOfferIntegratorResults.Add(new SaveOfferIntegratorResult()
                    {
                        OfferIntegratorErrorEnum = OfferIntegratorSaveErrorEnum.NotEnoughtCreditsMakeOfferFlash,
                        OfferIntegratorEnum = OfferIntegratorEnum.CompuTrabajo
                    });
                }
                else
                    _companyCountersService.AddCounterCompany((int)CompanyKpiEnum.CT_AE_OFFERS_FLASH, entity.idportal, entity.idcompany, 1, false);

            }

            if (companyProduct.GroupId == (short)ProductGroupsEnum.Membership &&
                (entity.HiddenCompany == 1 && offerPreUpdate is null ||
                entity.HiddenCompany == 1 && offerPreUpdate?.HiddenCompany == 0))
            {
                if (!_companyProductService.ConsumeUnits(entity.idcompany, entity.idportal,
                    (short)ProductAmbitEnum.OfferHiddenName, 1, entity.createdby, offerIntegrationCT.expirationtime,
                    OfferIntegratorEnum.CompuTrabajo))
                {
                    entity.HiddenCompany = 0;
                    saveOfferResult.SaveOfferIntegratorResults.Add(new SaveOfferIntegratorResult()
                    {
                        OfferIntegratorErrorEnum = OfferIntegratorSaveErrorEnum.NotEnoughtCreditsMakeOfferConfidential,
                        OfferIntegratorEnum = OfferIntegratorEnum.CompuTrabajo
                    });
                }
                else
                    _companyCountersService.AddCounterCompany((int)CompanyKpiEnum.CT_AE_OFFERS_HIDDEN_NAME, entity.idportal, entity.idcompany, 1, false);

            }

            if (companyProduct.ProductId == (int)ProductEnum.CorporateMigration
                || companyProduct.ProductId == (int)ProductEnum.AdvancedService1Month
                || companyProduct.ProductId == (int)ProductEnum.AdvancedService3Month
                || companyProduct.ProductId == (int)ProductEnum.AdvancedService6Month
                || companyProduct.ProductId == (int)ProductEnum.AdvancedService12Month)
            {
                offerIntegrationCT.ishighlighted = 1;
            }

            if (offerPreUpdateIntegrationCT != null)
            {
                offerIntegrationCT.moderatedby = offerPreUpdateIntegrationCT.moderatedby;
                offerIntegrationCT.moderationtime = offerPreUpdateIntegrationCT.moderationtime;
                offerIntegrationCT.IsPayment = offerPreUpdateIntegrationCT.IsPayment;
            }
            else
            {
                offerIntegrationCT.IsPayment = Convert.ToInt16(_companyProductService.IsPayment(companyProduct));
                offerIntegrationCT.urlrewrite = _semanticUrlService.GetSemanticUrlOffer(entity, portalConfig, (short)OfferIntegratorEnum.CompuTrabajo);
            }

            offerIntegrationCT.publicationdays = (short)expirationDays;
            ModifyToCorrectIncorrectOfferProductValues(offerIntegrationCT, companyProduct);
        }

        public short GetExpirationDays(CompanyProductEntity companyProduct, short expirationDays)
        {
            var feature = companyProduct?.Features?.FirstOrDefault(f => f.AmbitId == (short)ProductAmbitEnum.DaysToExpireOffer) ?? null;

            if (feature != null && feature.InitialUnits > 0)
            {
                return (short)feature.InitialUnits;
            }

            return expirationDays;
        }

        private void ModifyToCorrectIncorrectOfferProductValues(OfferIntegratorEntity offerIntegrationCT, CompanyProductEntity companyProduct)
        {
            var incorrectOfferValuesCondition = offerIntegrationCT.idproduct == 0
                || offerIntegrationCT.idcompanyproduct == 0
                || offerIntegrationCT.IdGroup == 0;

            if (incorrectOfferValuesCondition
                && (companyProduct.Id == 0 || companyProduct.ProductId == 0 || companyProduct.GroupId == 0))
            {
                _exceptionPublisherService.PublishWarning(new Exception($@"Para la empresa IdCompany: {companyProduct.IdCompany}. El companyProducts tiene valores a 0
                (PortalId: {companyProduct.PortalId}), y los q dispone la oferta (IdOffer:{offerIntegrationCT.IdOffer}) también. oi.IdProduct: {offerIntegrationCT.idproduct}, 
                oi.IdCompanyProduct: {offerIntegrationCT.idcompanyproduct}, oi.IdGroup: {offerIntegrationCT.IdGroup}; cp.IdProduct: {companyProduct.ProductId},
                cp.IdCompanyProduct: {companyProduct.Id}, cp.IdGroup: {companyProduct.GroupId}"), "OfferIntegratorCT", "ToCorrectIncorrectOfferProductValues", null, companyProduct.PortalId);
            }
            else if (incorrectOfferValuesCondition)
            {
                offerIntegrationCT.idproduct = companyProduct.ProductId;
                offerIntegrationCT.idcompanyproduct = companyProduct.Id;
                offerIntegrationCT.IdGroup = companyProduct.GroupId;
            }
        }

        private void SetDateLastUpInDbAndUpdateEntityForElasticByImport(OfferEntity entity, OfferIntegratorEntity offerIntegrator, PortalConfig portalConfig)
        {
            _offerIntegratorRecoverAndPersist.SetDateLastUp(entity.idoffer, portalConfig.expiration_days, portalConfig.PortalId, offerIntegrator.IdIntegrator);

            entity.datelastup = portalConfig.CurrentDateTimePortal;
            entity.updatedon = portalConfig.CurrentDateTimePortal;

            offerIntegrator.publicationtime = portalConfig.CurrentDateTimePortal;
            offerIntegrator.expirationtime = portalConfig.CurrentDateTimePortal.AddDays(portalConfig.expiration_days);
        }

        private void SetDateLastUpInDbAndUpdateEntityForElastic(OfferEntity entity, OfferIntegratorEntity offerIntegrator, PortalConfig portalConfig, CompanyProductEntity companyProduct)
        {
            var expirationDays = GetExpirationDays(companyProduct, portalConfig.expiration_days);
            _offerIntegratorRecoverAndPersist.SetDateLastUp(entity.idoffer, (short)expirationDays, portalConfig.PortalId, offerIntegrator.IdIntegrator);

            entity.datelastup = portalConfig.CurrentDateTimePortal;
            entity.updatedon = portalConfig.CurrentDateTimePortal;

            offerIntegrator.publicationtime = portalConfig.CurrentDateTimePortal;
            offerIntegrator.expirationtime = portalConfig.CurrentDateTimePortal.AddDays(expirationDays);
        }

        public void OfferProductTrace(OfferEntity offer, OfferIntegratorEntity offerIntegrator, string action, int idUser = 0)
        {
            if (!IsValidToOfferProductTrace(offer, offerIntegrator, action)) return;

            _offerComputrabajoRecoverAndPersist.OfferProductTrace(MappingToProductTrace(offer, offerIntegrator, action, idUser));
        }

        private ProductTraceEntity MappingToProductTrace(OfferEntity offer, OfferIntegratorEntity offerIntegrator,
            string actionPerformed, int idUser)
        {
            return new ProductTraceEntity
            {
                idoffer = offer.idofferCT,
                idcompany = offer.idcompany,
                idproduct = offerIntegrator.idproduct,
                idcompanyproduct = offerIntegrator.idcompanyproduct,
                idportal = offer.idportal,
                IsHidden = offer.HiddenCompany,
                IsHighlighted = offerIntegrator.ishighlighted,
                IsUrgent = offerIntegrator.isurgent,
                IdOfferStatus = offerIntegrator.idstatus,
                action = actionPerformed,
                IpAddress = _clientIpAddressResolverService.GetIpAddress(),
                IdUser = idUser
            };
        }

        private bool IsDesactivateAction(OfferIntegratorEntity offerIntegrator, short newStatus) => (offerIntegrator.idstatus == (short)OfferStatusEnum.Activa && newStatus == (short)OfferStatusEnum.Desactivada) ||
                                                                                                    (offerIntegrator.idstatus == (short)OfferStatusEnum.Desactivada && newStatus == (short)OfferStatusEnum.Activa);

        private bool CheckCanDoActionSetStatus(OfferEntity offer, OfferIntegratorEntity offerIntegrator, short newStatus, CompanyProductEntity companyProductEntity)
        {
            //Nueva Feature para activar / desactivar oferta, si no la tenemos, hacemos lo k ya esabamos haciendo hasta ahora
            if (IsDesactivateAction(offerIntegrator, newStatus) && companyProductEntity.Features.Any(f => f.AmbitId == (short)ProductAmbitEnum.DeactivateOffer))
                return true;

            var groupId = _productService.GetProductGroupIdByProductId(offer.idportal, offerIntegrator.idproduct);
            if (offerIntegrator.idproduct == 0 && (offerIntegrator.idstatus != (short)OfferStatusEnum.Borrador && offerIntegrator.idstatus != (short)OfferStatusEnum.Vencida) ||
                groupId != (short)ProductGroupsEnum.Membership && newStatus != (short)OfferStatusEnum.Eliminada)
            {
                return false;
            }

            if (newStatus == (short)OfferStatusEnum.Activa)
            {
                var portalConfig = _portalConfigurationService.GetPortalConfiguration(offer.idportal, (short)ApplicationEnum.AreaEmpresa);

                if (offerIntegrator.expirationtime < portalConfig.CurrentDateTimePortal && offerIntegrator.idstatus != (short)OfferStatusEnum.Eliminado_fisico)
                {
                    return false;
                }

                if (_companyProductFeatureService.IsSimultaneous(companyProductEntity, (short)ProductAmbitEnum.Offer))
                {
                    var offersByUsers = new Dictionary<long, List<int>>();

                    if (offersByUsers.ContainsKey(offer.createdby))
                        offersByUsers[offer.createdby].Add(offer.idoffer);
                    else
                        offersByUsers.Add(offer.createdby, new List<int> { offer.idoffer });

                    if (companyProductEntity.GroupId == (int)ProductGroupsEnum.Membership)
                    {
                        foreach (var offersByUser in offersByUsers)
                        {
                            var userConsumCredits = offersByUser.Key;
                            var offersToCheckUnits = offersByUser.Value.Where(id => id > 0);

                            var canDoActionByFeature = offersToCheckUnits.Any()
                                ? _companyProductService.CanDoActionByFeature(offer.idcompany, offer.idportal,
                                    (short)ProductAmbitEnum.Offer, (short)offersToCheckUnits.Count(),
                                    userConsumCredits,
                                    (OfferIntegratorEnum)Enum.Parse(typeof(OfferIntegratorEnum),
                                        offerIntegrator.IdIntegrator.ToString()))
                                : false;
                            if (!canDoActionByFeature)
                                return false;
                        }
                    }
                }
            }

            return true;
        }

        private bool RenewOffer(int idOffer, OfferRenewStatusEnum currentRenewOfferStatus, OfferStatusEnum offerStatus, DateTime offerExpirationTime, PortalConfig portalConfig,
                                short idIntegrator, short idGroup, short productClass, CompanyProductEntity companyProduct)
        {
            if (ConditionsToRenewOffer(offerStatus, portalConfig, idIntegrator, idGroup, productClass)) return false;

            var newDateExpirationTime = GetNewDateExpiration(offerExpirationTime, portalConfig, companyProduct);
            var newRenewOfferStatus = GetNewRenewOfferStatus(currentRenewOfferStatus);

            return _offerIntegratorRecoverAndPersist.RenewOffer(idOffer, newDateExpirationTime, newRenewOfferStatus, portalConfig.PortalId, idIntegrator, idGroup, productClass);
        }

        private static bool ConditionsToRenewOffer(OfferStatusEnum offerStatus, PortalConfig portalConfig, short idIntegrator, short idGroup, short productClass)
        {
            return offerStatus != OfferStatusEnum.Vencida &&
                            offerStatus != OfferStatusEnum.Eliminado_fisico &&
                            offerStatus != OfferStatusEnum.Eliminada
                            || portalConfig == null
                            || idIntegrator == 0
                            || idGroup == 0
                            || productClass == 0;
        }

        private void DisableHighlightedAndUrgentIfPack(OfferEntity offer, OfferIntegratorEntity offerIntegrator, CompanyProductEntity companyProduct, short portalId, int userId)
        {
            if (companyProduct.GroupId != (int)ProductGroupsEnum.Packs) return;

            var productGroupIdByOffer = _productService.GetProductGroupIdByProductId(portalId, offerIntegrator.idproduct);
            if (productGroupIdByOffer != (int)ProductGroupsEnum.Membership) return;

            _offerIntegratorRecoverAndPersist.UpdateOfferHighlighted(offerIntegrator.IdOffer, 0, offer.idportal, offerIntegrator.IdIntegrator);
            _offerIntegratorRecoverAndPersist.UpdateOfferUrgent(offerIntegrator.IdOffer, 0, offer.idportal, offerIntegrator.IdIntegrator);
        }

        private bool UpdateDateLastUp(OfferEntity offer, int daysUpload, short idIntegrator)
        {
            if (_offerIntegratorRecoverAndPersist.GetOfferIntegratorStatus(offer.idoffer, idIntegrator, offer.idportal) !=
                (int)OfferStatusEnum.Activa) return false;
            var lastUploadDate = _offerRecoverAndPersist.GetDateLastUp(offer.idoffer, offer.idportal);
            if (lastUploadDate != DateTime.MinValue && lastUploadDate.AddDays(daysUpload) > DateTime.Now) return false;

            var upload = _offerRecoverAndPersist.UpdateDateLastUp(offer.idoffer, offer.idcompany, offer.idportal);
            if (upload)
            {
                _stackService.Save(new StackEntity(offer.idportal)
                {
                    ObjectId = offer.idoffer,
                    TypeId = (int)StackObjectTypeEnum.Offer
                });
            }

            return upload;
        }

        private static bool IsValidToOfferProductTrace(OfferEntity offer, OfferIntegratorEntity offerIntegrator, string action)
        {
            if (offer != null)
            {
                return offer.idoffer > 0 && offer.idportal > 0 && !string.IsNullOrEmpty(action);
            }

            return false;
        }

        private DateTime GetNewDateExpiration(DateTime offerExpirationTime, PortalConfig portalConfig, CompanyProductEntity companyProduct)
        {
            var expirationDays = GetExpirationDays(companyProduct, portalConfig.expiration_days);
            var dateTimeNowByPortal = portalConfig.CurrentDateTimePortal;

            return offerExpirationTime < dateTimeNowByPortal
                ? dateTimeNowByPortal.AddDays(expirationDays)
                : offerExpirationTime.AddDays(expirationDays);
        }

        private static OfferRenewStatusEnum GetNewRenewOfferStatus(OfferRenewStatusEnum currentRenewOfferStatus)
        {
            return currentRenewOfferStatus == OfferRenewStatusEnum.Automatic
                ? OfferRenewStatusEnum.Manual
                : currentRenewOfferStatus;
        }

        private void SaveOfferVersioning(OfferEntity offer, OfferEntity originalOffer)
        {

            if ((offer.cargo != originalOffer.cargo) ||
                (offer.complement != originalOffer.complement) ||
                (offer.title != originalOffer.title) ||
                (offer.idcategory != originalOffer.idcategory) ||
                (offer.description != originalOffer.description) ||
                (offer.idcountry != originalOffer.idcountry) ||
                (offer.idlocalization != originalOffer.idlocalization) ||
                (offer.idcity != originalOffer.idcity) ||
                (offer.idemploymenttype != originalOffer.idemploymenttype) ||
                (offer.IdWorkPlaceType != originalOffer.IdWorkPlaceType) ||
                (offer.ProductClass != originalOffer.ProductClass) ||
                (offer.idcontracttype != originalOffer.idcontracttype) ||
                (offer.salary != originalOffer.salary) ||
                (offer.showsalary != originalOffer.showsalary) ||
                (offer.commissions != originalOffer.commissions) ||
                (offer.starttime != originalOffer.starttime) ||
                (offer.vacancies != originalOffer.vacancies) ||
                (offer.experienceyears != originalOffer.experienceyears) ||
                (offer.minage != originalOffer.minage) ||
                (offer.maxage != originalOffer.maxage) ||
                (offer.idstudy != originalOffer.idstudy) ||
                (offer.travel != originalOffer.travel) ||
                (offer.residencechange != originalOffer.residencechange) ||
                (offer.disability != originalOffer.disability))
            {
                _offerComputrabajoRecoverAndPersist.InsertOfferVersion(originalOffer.idoffer, originalOffer.idportal);
                _offerIntegratorRecoverAndPersist.InsertOfferVersion(originalOffer.idoffer, originalOffer.idportal);
            }
        }

        public bool PostPublishStep1InsertDraft(OfferEntity offer, PortalConfig portalConfig)
        {
            var offerIntegratorCT = offer.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo);
            if (offerIntegratorCT is null)
                return false;

            offerIntegratorCT.IdOffer = offer.idoffer;
            offerIntegratorCT.client_ip_mod = _clientIpAddressResolverService.GetIpAddress();
            offerIntegratorCT.createdby = offer.createdby;
            offerIntegratorCT.expirationtime = DateTime.Now.AddDays(portalConfig.expiration_days);
            offerIntegratorCT.urlrewrite = _semanticUrlService.GetSemanticUrlOffer(offer, portalConfig, (short)OfferIntegratorEnum.CompuTrabajo);
            offerIntegratorCT.publicationdays = portalConfig.expiration_days;
            offerIntegratorCT.idstatus = (short)OfferStatusEnum.Borrador;

            if (!_offerIntegratorRecoverAndPersist.Insert(offerIntegratorCT, portalConfig.PortalId))
            {
                return false;
            }

            return true;
        }

        public bool PostPublishStep1OfferIntegratorUpdate(OfferEntity offer, OfferEntity offerPreUpdate, CompanyProductEntity offerProduct)
        {
            var offerIntegrator = offer.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo);
            if (offerIntegrator is null)
                return false;
            var offerPreUpdateIntegrator = offerPreUpdate.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo);
            if (offerPreUpdateIntegrator is null)
                return false;

            SaveOfferVersioning(offer, offerPreUpdate);

            if (offerPreUpdateIntegrator.idstatus != (short)OfferStatusEnum.Borrador)
            {
                short newIdstatus = offerProduct.Features.Exists(feat => feat.AmbitId == (int)ProductAmbitEnum.OfferPostModeracion) ? (short)OfferStatusEnum.Activa : (short)OfferStatusEnum.Pendiente;

                if (newIdstatus != offerPreUpdateIntegrator.idstatus)
                {
                    if (!_offerIntegratorRecoverAndPersist.SetStatus(offerPreUpdate.idoffer, offerPreUpdate.idcompany, newIdstatus, offerPreUpdate.idportal, (short)OfferIntegratorEnum.CompuTrabajo))
                    {
                        return false;
                    }
                }
            }

            OfferProductTrace(offer, offerIntegrator, ProductTraceActionsEnum.UPDATE_OFFER.AnotationDescription());

            return true;
        }

        private void TraceExceptionIfNoFindCompanyProductWhenUpdate(OfferEntity offer, PortalConfig portalConfig, OfferIntegratorEntity offerIntegrator, CompanyProductEntity companyProduct)
        {
            if (companyProduct.ProductId <= 0
                || companyProduct.Id <= 0)
            {
                _exceptionPublisherService.Publish(new Exception($"No se ha podida recuperar el producto, al actualizar una oferta. OfferId: {offer.idoffer}, CompanyId: {offer.idcompany}, PortalId: {portalConfig.PortalId}, IdCompanyProduct: {offerIntegrator.idcompanyproduct}"), "OfferIntegratorCT", "Update");
            }
        }

        private void TraceErrorIfNoProductRenewOffer(OfferEntity offer, CompanyProductEntity companyProduct)
        {
            if (companyProduct.ProductId <= 0 || companyProduct.Id <= 0)
            {
                _exceptionPublisherService.Publish(new Exception($"RenewOffer idCompanyProduct: {companyProduct.Id}, idProduct: {companyProduct.ProductId}, idoffer: {offer.idoffer}, idPortal: {offer.idportal}"), "OfferIntegratorCT", "RenewOffer");
            }
        }

        public async Task<bool> ImportOffer(OfferEntity offerEntity, ExtraParamsToImportOfferDTO extraDTO)
        {
            if (CanImportOfferIntegrator(offerEntity, extraDTO))
            {
                var portalConfig = _portalConfigurationService.GetPortalConfiguration(offerEntity.idportal);

                offerEntity.Integrations.Add(new OfferIntegratorEntity
                {
                    IdOffer = offerEntity.idoffer,
                    IdIntegrator = (int)OfferIntegratorEnum.CompuTrabajo,
                    publicationdays = portalConfig.expiration_days,
                    urlrewrite = await _semanticUrlService.GetSemanticUrlOfferAsync(offerEntity, portalConfig),
                    createdon = offerEntity.createdon,
                    createdby = offerEntity.createdby,
                    IsPayment = 0,
                    idstatus = (short)OfferStatusEnum.Activa,
                    client_ip_add = offerEntity.client_ip_add,
                    ishighlighted = 0,
                    isurgent = 0,
                    Isflash = 0,
                    show_contact_email = 0,
                    contact_email = string.Empty,
                    show_phone_offer = 0,
                    contact_phone = string.Empty,
                    show_contact_address = 0,
                    contact_address = string.Empty,
                    idproduct = extraDTO.ProductId,
                    idcompanyproduct = extraDTO.CompanyProductId,
                    publicationtime = portalConfig.CurrentDateTimePortal,
                    expirationtime = portalConfig.CurrentDateTimePortal.AddDays(portalConfig.expiration_days),
                    ExternalApplicationEmail = string.Empty,
                    autopublished = 0,
                    Hidden = 0,
                    IdGroup = extraDTO.GroupId
                });

                var offerIntegrator = offerEntity.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo);

                if (_offerIntegratorRecoverAndPersist.Insert(offerIntegrator, portalConfig.PortalId))
                {
                    ExecuteActionsPostImportOffer(offerEntity, extraDTO, portalConfig, offerIntegrator);
                    return true;
                }
            }

            return false;
        }

        private void ExecuteActionsPostImportOffer(OfferEntity offerEntity, ExtraParamsToImportOfferDTO extraDTO, PortalConfig portalConfig, OfferIntegratorEntity offerIntegrator)
        {
            _matchOfferService.AddMatchOffer(offerEntity, offerIntegrator);

            _matchService.SetProcessMatchStatus(offerEntity.idcompany, offerEntity.idofferCT, (int)MatchProcessStatusCompanyEnum.Recruiting, portalConfig.PortalId);
            _stackModService.PublishOfferStackMod(offerEntity.idoffer, offerEntity.idcompany, offerEntity.idportal, (short)OfferActionStackModEnum.Published, offerEntity.experienceyears, offerIntegrator.urlrewrite);

            if (portalConfig.offer_moderation_queues)
            {
                _stackModService.AddToQueue(portalConfig.PortalId, offerEntity.idoffer, StackModQueueEnum.Pending);
                _stackModService.InsertHistoryQueue(offerEntity.idoffer, (short)StackModQueueEnum.Pending, portalConfig.PortalId);
            }

            SetDateLastUpInDbAndUpdateEntityForElasticByImport(offerEntity, offerIntegrator, portalConfig);

            _stackService.Save(new StackEntity(portalConfig.PortalId)
            {
                ObjectId = offerEntity.idoffer,
                TypeId = (short)StackObjectTypeEnum.Offer
            });

            _kpiService.Add((int)CompanyKpiEnum.OffersTotalPublished, portalConfig.PortalId);
            _kpiService.AddByProductGroup(extraDTO.GroupId, portalConfig.PortalId, DataToTraceKpisOfferPublish(offerEntity, portalConfig, offerEntity.createdby,
                                            new CompanyProductEntity() { Id = extraDTO.CompanyProductId, GroupId = extraDTO.GroupId }));
            _kpiService.AddMonthlyOffersByCompany((int)KpiEnum.MONTHLY_OFFERS_BY_COMPANY, portalConfig.PortalId, offerEntity.idcompany);
            OfferProductTrace(offerEntity, offerIntegrator, ProductTraceActionsEnum.NEW_OFFER.AnotationDescription());
            _companyProductService.UpdateDateMod(extraDTO.CompanyProductId, portalConfig.PortalId);
        }

        private static bool CanImportOfferIntegrator(OfferEntity offerEntity, ExtraParamsToImportOfferDTO extraDTO)
        {
            return offerEntity != null && offerEntity.idofferCT > 0
                            && offerEntity.idoffer > 0
                            && !string.IsNullOrEmpty(offerEntity.title)
                            && offerEntity.idcountry > 0
                            && offerEntity.createdby > 0
                            && offerEntity.createdon > DateTime.MinValue
                            && extraDTO != null
                            && extraDTO.GroupId > 0
                            && extraDTO.ProductId > 0
                            && extraDTO.CompanyProductId > 0;
        }

        public void SetPostModerateOffer(OfferEntity offer, PortalConfig portalConfig, CompanyProductEntity productToConsume)
        {
            var postmodFeature = productToConsume.Features.FirstOrDefault(feat => feat.AmbitId == (int)ProductAmbitEnum.OfferPostModeracion);
            if (postmodFeature != null)
            {
                var offerIntegrator = offer.Integrations.FirstOrDefault(i => i.IdIntegrator == (short)OfferIntegratorEnum.CompuTrabajo);

                _offerIntegratorRecoverAndPersist.UpdateOfferStatus(offer.idoffer, (short)OfferStatusEnum.Activa, (short)OfferStatusRowEnum.Activo, portalConfig.PortalId, offerIntegrator.IdIntegrator);
                offerIntegrator.idstatus = (int)OfferStatusEnum.Activa;
                offer.idstatus = (short)OfferStatusRowEnum.Activo;

                SetDateLastUpInDbAndUpdateEntityForElastic(offer, offerIntegrator, portalConfig, productToConsume);

                _stackService.Save(new StackEntity(portalConfig.PortalId)
                {
                    ObjectId = offer.idoffer,
                    TypeId = (short)StackObjectTypeEnum.Offer
                });
            }
        }
    }
}
