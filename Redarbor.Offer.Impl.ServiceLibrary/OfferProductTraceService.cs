using Redarbor.Core.Cache.Contracts.ServiceLibrary;
using Redarbor.Extensions.Library.DI;
using Redarbor.Master.Entities.Offers;
using Redarbor.Master.Entities.Product;
using Redarbor.Offer.Contracts.ServiceLibrary;
using Redarbor.Offer.Library.DomainServiceContracts;
using Redarbor.Offer.Library.DomainServicesImplementations;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Caching;
using System.Windows.Input;

namespace Redarbor.Offer.Impl.ServiceLibrary
{
    [RegisterStatefulService]
    public class OfferProductTraceService : IOfferProductTraceService
    {
        private readonly TimeSpan _timeInCache = new TimeSpan(0, 15, 0);
        private readonly IOfferProductTraceRecoverAndPersist _offerProductTraceRecoverAndPersist;
        private readonly ITempCache _tempCache;

        public OfferProductTraceService(IOfferProductTraceRecoverAndPersist offerProductTraceRecoverAndPersist, ITempCache tempCache)
        {
            _offerProductTraceRecoverAndPersist = offerProductTraceRecoverAndPersist;
            _tempCache = tempCache;
        }

        public List<ProductTraceEntity> GetCompanyDeletedProductTracesByCompanyId(int companyId, short idPortal)
        {
            var key = $"CACHEGETCOMPANYPRODUCTTRACEBYCOMPANYID_{companyId}_PORTAL_{idPortal}";

            var cache = _tempCache.Get<List<ProductTraceEntity>>(key, idPortal);
            if (cache != null)
                return cache;

            var result = _offerProductTraceRecoverAndPersist.GetDeletedOfferProductTrace(companyId, idPortal);
            _tempCache.Add(key, result, _timeInCache, idPortal);

            return result;
        }
    }
}
